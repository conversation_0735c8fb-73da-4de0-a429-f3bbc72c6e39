{"description": "Semantic MediaWiki group import", "import": [{"page": "Group:Schema properties", "namespace": "SMW_NS_SCHEMA", "contents": {"importFrom": "/groups/schema.json"}, "options": {"replaceable": {"LAST_EDITOR": "IS_IMPORTER"}}}, {"page": "Group:Predefined properties", "namespace": "SMW_NS_SCHEMA", "contents": {"importFrom": "/groups/predefined.properties.json"}, "options": {"replaceable": {"LAST_EDITOR": "IS_IMPORTER"}}}], "meta": {"version": "1"}}