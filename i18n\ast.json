{"@metadata": {"authors": ["Esbard<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "YoaR"]}, "smw-desc": "Facer la to wiki más accesible - pa les máquines ''y'' pa los humanos ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentación en llinia])", "smw-semantics-not-enabled": "Nun s'activó la función de MediaWiki Semántica pa esta wiki.", "smw_viewasrdf": "Canal RDF", "smw_finallistconjunct": ", ⴷ", "smw-factbox-head": "… más sobre «$1»", "smw-factbox-facts": "Fechos", "smw-factbox-facts-help": "Amosar les declaraciones y fechos que se crearon por un usuariu", "smw-factbox-facts-derived": "Fechos deriva<PERSON>", "smw-factbox-facts-derived-help": "Amosar fechos que se derivaron de regles o cola ayuda d'otres técniques de razonamientu", "smw_isspecprop": "Esta propiedá ye una propiedá especial nesta wiki.", "smw-concept-cache-header": "<PERSON><PERSON> de la caché", "smw-concept-cache-count": "La [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count caché de conceptos] contién {{PLURAL:$1|'''una''' entidá|'''$1''' entidaes}} ($2).", "smw-concept-no-cache": "Nun hai caché disponible.", "smw_concept_description": "Descripción del conceptu \"$1\"", "smw_no_concept_namespace": "Los conceptos sólo puen definise nes páxines del espaciu de nomes Conceptu:.", "smw_multiple_concepts": "Cada páxina de conceptu sólo pue tener una definición d'un conceptu.", "smw_concept_cache_miss": "El conceptu \"$1\" nun pue usase de momentu, porque la configuración de la wiki requier que se calcule desconectáu. Si'l problema nun desapaez dempués d'un tiempu, pida al alministrador del sitiu que faiga disponible esti conceptu.", "smw_noinvannot": "<PERSON>un pueden dase valores a propiedaes inverses.", "version-semantic": "Estensiones semántiques", "smw_baduri": "Les URIs de la forma «$1» nun tan permitíes.", "smw_printername_count": "Resultaos del recuentu", "smw_printername_csv": "Esportación a CSV", "smw_printername_dsv": "Esportación a DSV", "smw_printername_debug": "Consulta de depuración (pa espertos)", "smw_printername_embedded": "Incrustar conteníu de páxina", "smw_printername_json": "Esportación a JSON", "smw_printername_list": "Llista", "smw_printername_plainlist": "Llista simple", "smw_printername_ol": "Enumeración", "smw_printername_ul": "Itemización", "smw_printername_table": "Tabla", "smw_printername_broadtable": "Tabla ancha", "smw_printername_template": "Plantía", "smw_printername_templatefile": "Ficheru de plantía", "smw_printername_rdf": "Esportación a RDF", "smw_printername_category": "Categoría", "validator-type-class-SMWParamSource": "testu", "smw-paramdesc-limit": "El númberu máximu de resultaos a devolver", "smw-paramdesc-offset": "El desplazamientu del primer resultáu", "smw-paramdesc-headers": "Ver les cabeceres/nomes de propiedá", "smw-paramdesc-mainlabel": "La etiqueta a dar al nome de la páxina principal", "smw-paramdesc-link": "Ver los valores como enllaces", "smw-paramdesc-intro": "El testu que s'amuesa antes de los resultaos de la consulta, si los hai", "smw-paramdesc-outro": "El testu que s'amuesa dempués de los resultaos de la consulta, si los hai", "smw-paramdesc-default": "El testu que s'amuesa si nun hai resultaos na consulta", "smw-paramdesc-sep": "El separador ente resultancies", "smw-paramdesc-propsep": "El separador ente les propiedaes d'una entrada de resultancia", "smw-paramdesc-valuesep": "El separador ente los valores d'una propiedá d'un resultáu", "smw-paramdesc-showsep": "Amosar el separador al principiu del ficheru CSV (\"sep=<valor>\")", "smw-paramdesc-distribution": "En llugar d'amosar tolos valores, contar les veces qu'ocurren y amosales.", "smw-paramdesc-distributionsort": "Ordenar la distribución de valores pol númberu d'ocurrencies.", "smw-paramdesc-distributionlimit": "Llendar la distribución de valores a la cuenta d'algunos valores solamente.", "smw-paramdesc-aggregation": "Especifica con qué tien de rellacionase'l conxuntu", "smw-paramdesc-template": "El nome d'una plantía cola qu'amosar los llistaos", "smw-paramdesc-columns": "El númberu de columnes p'amosar los resultaos", "smw-paramdesc-userparam": "Un valor pasáu en cada llamada de plantía,si s'usa una plantía", "smw-paramdesc-class": "Una clase CSS estra qu'establecer pa la tabla", "smw-paramdesc-introtemplate": "El nome d'una plantía que s'amuesa antes de los resultaos de la consulta, si los hai", "smw-paramdesc-outrotemplate": "El nome d'una plantía que s'amuesa dempués de los resultaos de la consulta, si los hai", "smw-paramdesc-embedformat": "La etiqueta HTML que s'usa pa definir los encabezaos", "smw-paramdesc-embedonly": "<PERSON><PERSON> amosar cabeceres", "smw-paramdesc-table-class": "Una clase CSS estra qu'establecer pa la tabla", "smw-paramdesc-table-transpose": "Ver les testeres de tabla en vertical y los resultaos n'horizontal", "smw-paramdesc-rdfsyntax": "La sintaxis RDF a usar", "smw-paramdesc-csv-sep": "Especifica un separador de columnes", "smw-paramdesc-csv-valuesep": "Especifica un separador de valores", "smw-paramdesc-csv-merge": "Mecer los valores de fileres y columnes col mesmu identificador de suxetu (aka primera columna)", "smw-paramdesc-csv-bom": "Añadir un BOM (caráuter pa indicar la triba «endian») al principiu del ficheru de salida", "smw-paramdesc-dsv-separator": "El separador a usar", "smw-paramdesc-dsv-filename": "El nome del ficheru DSV", "smw-paramdesc-filename": "El nome del ficheru de salida", "smw-smwdoc-description": "Amuesa una tabla con tolos parámetros que se pueden usar pal formatu de resultaos especificáu xunto colos valores predeterminaos y les descripciones.", "smw-smwdoc-default-no-parameter-list": "Esti formatu de resultáu nun ufre parámetros específicos de formatu.", "smw-smwdoc-par-format": "El formatu de los resultaos pal qu'amosar la documentación de parámetros.", "smw-smwdoc-par-parameters": "Qué parámetros amosar. \"specific\" pa los amestaos pol formatu, \"base\" pa los disponibles en tolos formatos y \"all\" pa ambos.", "smw-paramdesc-sort": "Propiedá pola qu'ordenar la consulta", "smw-paramdesc-order": "Orde d'ordenación de la consulta", "smw-paramdesc-searchlabel": "Testu pa siguir la busca", "smw-paramdesc-named_args": "Nome de los argumentos que se pasan a la plantía", "smw-paramdesc-template-arguments": "Configura cómo se pasen los argumentos con nome a la plantía", "smw-paramdesc-import-annotation": "Se copiaran otros datos anotaos más demientres l'análisis d'un tema", "smw-paramdesc-export": "Opción d'esportación", "smw-paramdesc-prettyprint": "Una salida con formatu axeitáu qu'amuesa más sangríes y llinies nueves", "smw-paramdesc-json-unescape": "Salida pa contener barres sin escapar y carácteres Unicode multibyte", "smw-paramdesc-json-type": "Tipu de serialización", "smw-paramdesc-source": "Fonte alternativa de consulta", "smw-paramdesc-jsonsyntax": "Sintaxis JSON a usar", "smw-printername-feed": "Canal RSS y Atom", "smw-paramdesc-feedtype": "Tipu de canal", "smw-paramdesc-feedtitle": "El testu a usar como títulu de la canal", "smw-paramdesc-feeddescription": "El testu a usar como descripción de la canal", "smw-paramdesc-feedpagecontent": "Conteníu de la páxina que s'amosará cola canal", "smw-label-feed-description": "Canal $2 $1", "smw_iq_disabled": "Les consultes semántiques tan desactivaes nesta wiki.", "smw_iq_moreresults": "… más resultaos", "smw_parseerror": "<PERSON>un s'entendió el valor dau.", "smw_notitle": "\"$1\" nun se pue usar como nome de páxina nesta wiki.", "smw_noproperty": "\"$1\" nun se pue usar como nome de propiedá nesta wiki.", "smw_wrong_namespace": "Equí sólo se permiten les páxines del espaciu de nomes \"$1\".", "smw_manytypes": "Definiose más d'un tipu pa la propiedá.", "smw_emptystring": "<PERSON>un s'aceuten les cadenes baleres.", "smw_notinenum": "«$1» nun ta na llista ($2) de [[Property:Allows value|valores posibles]] de la propiedá «$3».", "smw_noboolean": "«$1» nun ta reconocíu como valor booleanu (verdaderu/falsu).", "smw_true_words": "verdaderu,v,si,s,true,t", "smw_false_words": "falsu,false,f,non,n", "smw_nofloat": "\"$1\" nun ye un númberu.", "smw_infinite": "Nun hai sofitu pa númberos tán grandes como \"$1\".", "smw_unitnotallowed": "\"$1\" nun ta declarada como unidá de midida válida pa esta propiedá.", "smw_nounitsdeclared": "Nun se declaró nenguna unidá de midida pa esta propiedá.", "smw_novalues": "<PERSON><PERSON> s'especificó nengún valor.", "smw_nodatetime": "<PERSON>un s'entendió la data «$1».", "smw_toomanyclosing": "Paez qu'hai demasiaes apaiciones de \"$1\" na consulta.", "smw_noclosingbrackets": "Algún usu de \"<nowiki>[[</nowiki>\" na consulta nun ta zarráu coles \"]]\" correspondientes.", "smw_misplacedsymbol": "El símbolu \"$1\" usose nun llugar onde nun ye útil.", "smw_unexpectedpart": "La parte \"$1\" de la consulta nun s'entendió.\nLos resultaos podríen nun ser los esperaos.", "smw_emptysubquery": "Alguna subconsulta nun tien una condición válida.", "smw_misplacedsubquery": "Alguna subconsulta s'usó nun llugar onde nun se permiten les subconsultes.", "smw_valuesubquery": "Nun hai sofitu de subconsultes pa los valores de la propiedá \"$1\".", "smw_badqueryatom": "Una parte «<nowiki>[[…]]</nowiki>» de la consulta nun s'entendió.", "smw_propvalueproblem": "El valor de la propiedá «$1» nun s'entendió.", "smw_noqueryfeature": "Alguna carauterística de la consulta nun tien sofitu nesta wiki y parte de la consulta nun se tuvo en cuenta ($1).", "smw_noconjunctions": "Les conxunciones nes consultes nun tienen sofitu nesta wiki y parte de la consulta nun se tuvo en cuenta ($1)", "smw_nodisjunctions": "Les dixunciones nes consultes nun tienen sofitu nesta wiki y parte de la consulta nun se tuvo en cuenta ($1).", "smw_querytoolarge": "{{PLURAL:$2|La siguiente condición|Les siguientes condiciones}} de consulta nun {{PLURAL:$2|pudo|pudieron}} considerase poles torgues de les wikis nel tamañu o fondura de les consultes: <code>$1</code>", "smw_notemplategiven": "Dar un valor al parámetru \"plantía\" pa qu'esti formatu de consulta funcione.", "smw_db_sparqlqueryproblem": "Nun pudieron algamase resultaos de la consulta de la base de datos SPARQL. Esti error pue ser temporal o indicar un error nel programa de la base de datos.", "smw_db_sparqlqueryincomplete": "Resultó demasiao difícil responder la consulta y s'encaboxó. Pueden faltar algunos resultaos. Si ye posible, intente facer una consulta más cenciella.", "smw_type_header": "Propiedaes de tipu \"$1\"", "smw_typearticlecount": "Amosando $1 {{PLURAL:$1|propiedá qu'usa|propiedaes qu'usen}} esti tipu.", "smw_attribute_header": "Páxines qu'usen la propiedá \"$1\"", "smw_attributearticlecount": "Amosando $1 {{PLURAL:$1|páxina qu'usa|páxines qu'usen}} esta propiedá.", "smw-propertylist-subproperty-header": "Subpropiedaes", "smw-propertylist-redirect-header": "Sinónimos", "smw-propertylist-error-header": "Páxines con atribuciones inapropiaes", "smw-propertylist-count": "S'{{PLURAL:$1|amuesa $1 entidá rellacionada|amuesen $1 entidaes rellacionaes}}.", "smw-propertylist-count-with-restricted-note": "S'{{PLURAL:$1|amuesa $1 entidá rellacionada|amuesen $1 entidaes rellacionaes}} (hai más disponibles, pero la salida ta llendada a «$2»).", "smw-propertylist-count-more-available": "S'{{PLURAL:$1|amuesa $1 entidá rellacionada|amuesen $1 entidaes rellacionaes}} (hai más disponibles).", "exportrdf": "Esportar les páxines a RDF", "smw_exportrdf_docu": "Esta páxina permite algamar datos d'una páxina en formatu RDF.\nPa esportar les páxines, escriba los títulos nel cuadru de testu d'abaxo, un títulu por llinia.", "smw_exportrdf_recursive": "Esportar recursivamente toles páxines rellacionaes.\n¡Tenga en cuenta que'l resultáu pue ser grande!", "smw_exportrdf_backlinks": "Esportar tamién toles páxines que se refieren a les páxines esportaes.\nXenera un RDF navegable.", "smw_exportrdf_lastdate": "Nun esportar les páxines que nun camudaron dende cierta data.", "smw_exportrdf_submit": "Esportar", "uriresolver": "URIResolver", "properties": "Propiedaes", "smw-categories": "Categoríes", "smw_properties_docu": "Les siguientes propiedaes s'usen na wiki.", "smw_property_template": "$1 de tipu $2 ($3 {{PLURAL:$3|usu|usos}})", "smw_propertylackspage": "¡Toles propiedaes habríen de tar descrites por una páxina!", "smw_propertylackstype": "<PERSON>un s'especificó nengún tipu pa esta propiedá (asumiendo tipu $1 de momentu).", "smw_propertyhardlyused": "¡Esta propiedá apenes tien usu nesta wiki!", "smw-property-name-invalid": "Nun se pue usar la propiedá $1 (nome de propiedá inválidu).", "smw-property-name-reserved": "«$1» marcóse como nome acutáu y nun tendría d'utilizase como propiedá. La siguiente [https://www.semantic-mediawiki.org/wiki/Help:Property_naming páxina d'ayuda] podría tener información sobre'l motivu pa qu'esti nome tea acutáu.", "smw-sp-property-searchform": "<PERSON>ar les propiedaes que contengan:", "smw-sp-property-searchform-inputinfo": "La entrada ye sensible a mayúscules y, cuando s'usa pa filtriar, namái apaecen les propiedaes que casen cola condición.", "smw-special-property-searchform": "<PERSON>ar les propiedaes que contengan:", "smw-special-property-searchform-inputinfo": "La entrada ye sensible a mayúscules y, cuando s'usa pa filtriar, namái apaecen les propiedaes que casen cola condición.", "smw-special-property-searchform-options": "Opciones", "smw-special-wantedproperties-filter-label": "Filtru:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "<PERSON><PERSON> aprobar", "smw-special-wantedproperties-filter-unapproved-desc": "Opción de filtru usada en conexón col módulu d'autoridá.", "concepts": "Conceptos", "smw-special-concept-docu": "Un [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceptu] pue vese como una \"categoría dinámica\", esto ye, una coleición de páxines que nun tan creaes manualmente, sinón que se calculen por Semantic MediaWiki dende una descripción d'una consulta dada.", "smw-special-concept-header": "Llista de conceptos", "smw-special-concept-count": "{{PLURAL:$1|Ta llistáu el siguiente conceptu|Tan llistaos los siguientes $1 conceptos}}.", "smw-special-concept-empty": "<PERSON><PERSON> s'alcontró nengún conceptu.", "unusedproperties": "Propiedaes non usaes", "smw-unusedproperties-docu": "Nesta páxina ta la llista de [https://www.semantic-mediawiki.org/wiki/Unused_properties propiedaes sin usu] que tán declaraes anque nenguna páxina fai usu d'elles. Pa una vista distinta, visita les páxines especiales de [[Special:Properties|propiedaes]] o [[Special:WantedProperties|propiedaes solicitaes]]", "smw-unusedproperty-template": "$1 de tipu $2", "wantedproperties": "Propiedaes deseaes", "smw-wantedproperties-docu": "Nesta páxina ta la llista de [https://www.semantic-mediawiki.org/wiki/Wanted_properties propiedaes solicitaes] que tán declaraes anque nenguna páxina fai usu d'elles. Pa una vista distinta, visita les páxines especiales de [[Special:Properties|propiedaes]] o [[Special:UnusedProperties|propiedaes sin usu]]", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|usu|usos}})", "smw-special-wantedproperties-docu": "Nesta páxina ta la llista de [https://www.semantic-mediawiki.org/wiki/Wanted_properties propiedaes solicitaes] que s'usen nesta wiki anque nenguna páxina les describe. Pa una vista distinta, visita les páxines especiales de [[Special:Properties|propiedaes]] o [[Special:UnusedProperties|propiedaes sin usu]]", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|usu|usos}})", "smw_purge": "<PERSON><PERSON><PERSON><PERSON>", "smw-purge-failed": "Semantic MediaWiki tentó purgar la páxina pero falló", "types": "Tipos", "smw_types_docu": "Llista de [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tipos de datos disponibles] con cada [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipu] que representa un conxuntu únicu d'atributos pa describir un valor en términos de carauterístiques d'almacenamientu y visualización que s'herieden pa una propiedá atribuyida.", "smw-special-types-no-such-type": "«$1» ye desconocíu o nun s'especificó como tipu de datos válidu.", "smw-statistics": "Estadístiques semántiques", "smw-statistics-property-instance": "{{PLURAL:$1|Valor|Valores}} de propiedá (total)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Propiedá|Propiedaes}}]] (total)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Propiedá|Propiedaes}} (total)", "smw-statistics-property-used": "{{PLURAL:$1|Propiedá|Propiedaes}} (úsase con un valor polo menos)", "smw-statistics-property-page": "{{PLURAL:$1|Propiedá|Propiedaes}} ({{PLURAL:$1|rexistrada|rexistraes}} con una páxina)", "smw-statistics-property-type": "{{PLURAL:$1|Propiedá|Propiedaes}} ({{PLURAL:$1|asociada|asociaes}} a un tipu de datos)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Consulta|Consultes}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Consulta|Consultes}}]] (incrustaes, totales)", "smw-statistics-query-size": "<PERSON><PERSON><PERSON><PERSON> consulta", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Conceptu|Conceptos}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Conceptu|Conceptos}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Sub-oxetu|Sub-oxetos}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Suboxetu|Suboxetos}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Tipu|Tipos}} de datos]]", "smw-statistics-error-count": "{{PLURAL:$1|<PERSON>or de propiedá|Valores de propiedá}} ([[Special:ProcessingErrorList|{{PLURAL:$1|anotación incorreuta|anotaciones incorreutes}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Valor de propiedá|Valores de propiedá}} ({{PLURAL:$1|anotación incorreuta|anotaciones incorreutes}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Entidá anticuada|Entidaes anticuaes}}]", "smw_uri_doc": "El resolvedor d'URI encadarma [$1 W3C TAG finding on httpRange-14].\nS'encarga de metadatos que los humanos nun ven nos sitios web.", "ask": "<PERSON><PERSON><PERSON>", "smw-ask-help": "Esta sección contién dellos enllaces p'ayudar a esplicar cómo utilizar la sintaxis <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selección de páxines] describe cómo escoyer páxines y construir condiciones.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadores de gueta] amuesa los operadores de gueta disponibles, incluyendo los de consulta d'intervalu y de caráuteres comodín.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Amuesa d'información] espon l'usu de les declaraciones de salida y les opciones de formatu.", "smw_ask_sortby": "Ordenar por columna (opcional)", "smw_ask_ascorder": "Ascendiente", "smw_ask_descorder": "Descendiente", "smw-ask-order-rand": "Al debalu", "smw_ask_submit": "Alcontrar <PERSON>aos", "smw_ask_editquery": "Editar consulta", "smw_add_sortcondition": "[Añadir una condición d'ordenación]", "smw-ask-sort-add-action": "Añadir una condición d'ordenación", "smw_ask_hidequery": "<PERSON><PERSON><PERSON>r consulta (vista compacta)", "smw_ask_help": "<PERSON>yuda pa consultes", "smw_ask_queryhead": "Condición", "smw_ask_printhead": "Selección de datos a imprimir", "smw_ask_printdesc": "(añadir un nome de propiedá por llinia)", "smw_ask_format_as": "Dar formatu de:", "smw_ask_defaultformat": "predetermin<PERSON><PERSON>", "smw_ask_otheroptions": "Otres opciones", "smw-ask-otheroptions-info": "Esta seición contién opciones qu'alteren los formatos de salida. Les descripciones de los parámetros pueden vese pasando'l mur perriba d'ellos.", "smw-ask-otheroptions-collapsed-info": "Use l'iconu «más» pa ver toles opciones disponibles", "smw_ask_show_embed": "Amosar el códigu incrustáu", "smw_ask_hide_embed": "Anubrir el códigu incrustáu", "smw_ask_embed_instr": "Pa incrustar en llinia esta consulta nuna páxina wiki use'l códigu siguiente.", "smw-ask-delete": "Desaniciar", "smw-ask-sorting": "Ordenación", "smw-ask-options": "Opciones", "smw-ask-options-sort": "Opciones d'ordenación", "smw-ask-format-options": "Formatu y opciones", "smw-ask-parameters": "Parámetros", "smw-ask-search": "<PERSON><PERSON><PERSON>", "smw-ask-debug": "<PERSON><PERSON><PERSON>", "smw-ask-debug-desc": "Xenera información pa la depuración de consultes", "smw-ask-no-cache": "Desactivar la caxé de consultes", "smw-ask-no-cache-desc": "Resultancies ensin caché de consulta", "smw-ask-result": "Resultáu", "smw-ask-empty": "<PERSON><PERSON><PERSON> toles entraes", "smw-ask-download-link-desc": "Descargar les resultancies de la consulta en formatu $1", "smw-ask-format": "Formatu", "smw-ask-format-selection-help": "Ayuda pal formatu seleicionáu: $1", "smw-ask-condition-change-info": "Alteróse la condición, ya'l buscador precisa executar otra vegada la consulta pa producir resultancies que casen colos requisitos nuevos.", "smw-ask-input-assistance": "Asistencia d'entrada", "smw-ask-condition-input-assistance": "Bríndase [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance asistencia d'entrada] pa los campos de salida, d'ordenación y de condición. El campu de condición rique emplegar dalgunu d'estos prefixos:", "smw-ask-condition-input-assistance-property": "<code>p</code>: pa recibir suxerencies de propiedaes (p.&nbsp;ex., <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c</code>: pa recibir suxerencies de categoríes", "smw-ask-condition-input-assistance-concept": "<code>con</code>: pa recibir suxerencies de conceutos", "smw-ask-format-change-info": "Cambió'l formatu y ríquese executar nuevamente la consulta pa que correspuenda colos nuevos parámetros y opciones de vista.", "smw-ask-format-export-info": "El formatu d'esportación escoyíu ye unu que nun tien representación visual, por eso los resultaos sólo s'ufren como descarga.", "smw-ask-query-search-info": "{{PLURAL:$3|1=<code>$2</code> (de la caché) respondió|<code>$2</code> (de la caché) respondieron|<code>$2</code> respondió}} la consulta <code><nowiki>$1</nowiki></code> en $4 {{PLURAL:$4|segundu|segundos}}.", "searchbyproperty": "Buscar por propiedá", "processingerrorlist": "Procesando la llista d'errores", "propertylabelsimilarity": "Informe de semeyanza na etiqueta Propiedá", "smw-processingerrorlist-intro": "La llista siguiente ufre una güeyada de los [https://www.semantic-mediawiki.org/wiki/Processing_errors errores de procesamientu] qu'apaecieron venceyaos con [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Recoméndase vixilar esta llista de manera regular y correxir les anotaciones de valor inválidu.", "smw_sbv_docu": "Buscar toles páxines que tienen una propiedá y valor daos.", "smw_sbv_novalue": "Escribi un valor válidu pa la propiedá, o mira tolos valores de la propiedá pa «$1».", "smw_sbv_displayresultfuzzy": "Llista de toles páxines que tienen la propiedá \"$1\" con valor \"$2\".\nComo namái hebo unos pocos resultaos, ta<PERSON><PERSON> s'amuesen los valores aproximaos.", "smw_sbv_property": "Propiedá:", "smw_sbv_value": "Valor:", "smw_sbv_submit": "Alcontrar <PERSON>aos", "browse": "Esplorar la wiki", "smw_browselink": "E<PERSON><PERSON><PERSON> les propiedaes", "smw_browse_article": "Escriba'l nome de la páxina dende la qu'empezar la navegación.", "smw_browse_go": "<PERSON><PERSON>", "smw_browse_show_incoming": "Ver les propiedaes entrantes", "smw_browse_hide_incoming": "Tapecer les propiedaes entrantes", "smw_browse_no_outgoing": "<PERSON>sta páxina nun tien propiedaes.", "smw_browse_no_incoming": "<PERSON>un hai propiedaes qu'enllacen a esta páxina.", "smw-browse-from-backend": "Ta recuperándose la información del motor.", "smw-browse-intro": "Esta páxina apurre detalles sobre una instancia d'asuntu o d'entidá, escribi'l nome d'un oxetu a inspeicionar.", "smw-browse-invalid-subject": "La validación del asuntu devolvió l'error «$1».", "smw-browse-api-subject-serialization-invalid": "L'asuntu tien un formatu de serialización inválidu.", "smw-browse-js-disabled": "Albídrase que Javascript ta desactiváu o nun ta disponible, y recomendamos usar un navegador con encontu pal mesmu. Otres opciones discútense na páxina del parámetru de configuración [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Amosar grupos", "smw-browse-hide-group": "Anubrir los grupos", "smw-noscript": "Esta páxina o aición rique Javascript pa funcionar. Activa Javascript nel navegador, o utiliza un navegador con encontu, pa que pueda sirvise y proporcionase esta carauterística como se solicitó. Pa más información, consulta la páxina d'ayuda [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "Etiqueta de propiedá inversa", "pageproperty": "<PERSON><PERSON><PERSON> de propiedá de paxina", "smw_pp_docu": "Escribe una páxina y propiedá,o sólo una propiedá pa llograr tolos valores venceyaos.", "smw_pp_from": "De la páxina:", "smw_pp_type": "Propiedá:", "smw_pp_submit": "Alcontrar <PERSON>aos", "smw_result_prev": "Anterior", "smw_result_next": "Siguient<PERSON>", "smw_result_results": "Resultaos", "smw_result_noresults": "<PERSON><PERSON> hai resultaos.", "smwadmin": "Tableru (Semantic MediaWiki)", "smw-admin-statistics-job-title": "Estadístiques de trabayos", "smw-admin-statistics-job-docu": "Les estadístiques de trabayu amuesen información de los trabayos programaos de Semantic MediaWiki qu'entá nun s'executaron. El númberu de trabayos puede ser llixeramente inexautu o contener intentos fallíos, consulta esti [https://www.mediawiki.org/wiki/Manual:Job_queue manual] pa información detallada.", "smw-admin-statistics-querycache-title": "Caché de <PERSON>", "smw-admin-statistics-querycache-disabled": "La [https://www.semantic-mediawiki.org/wiki/QueryCache Caché de consultes] nun s'activó nesta wiki y, da<PERSON><PERSON><PERSON>, nun hai estadístiques disponibles.", "smw-admin-permission-missing": "Bloquióse l'accesu a esta páxina por falta de permisos; consulta la páxina d'ayuda sobro [https://www.semantic-mediawiki.org/wiki/Help:Permissions permisos] pa llograr detalles de la configuración necesaria.", "smw-admin-setupsuccess": "Configuróse'l motor d'almacenamientu.", "smw_smwadmin_return": "Tornar a $1.", "smw_smwadmin_updatestarted": "Anicióse un nuevu procesu d'actualización pa refrescar los datos semánticos.\nTolos datos almacenaos se reconstruirán o repararán onde faiga falta.\nPues siguir el procesu d'actualización nesta páxina especial.", "smw_smwadmin_updatenotstarted": "Yá ta executando un procesu d'actualización.\nNun se creará otru.", "smw_smwadmin_updatestopped": "Pararon tolos procesos d'actualización esistentes.", "smw_smwadmin_updatenotstopped": "Pa parar el procesu d'actualización n'execución, tienes d'activar el cuadru de marca pa indicar que daveres tas seguru.", "smw-admin-docu": "Esta páxina especial ayudaráte demientres la instalación, anovamientu, mantenimientu y usu de <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> y ufre tamién otres funciones y xeres alministratives, lo mesmo qu'estadístiques.\nRecuerda facer copia de seguridá de los datos importantes antes d'executar funciones alministratives.", "smw-admin-environment": "Entornu de software", "smw-admin-db": "Configuración de la base de datos", "smw-admin-db-preparation": "Ta en cursu l'aniciu de la tabla y puede tardar un tiempu ata que s'amuesen les resultancies, dependiendo del tamañu de la tabla y de posibles optimizaciones de la mesma.", "smw-admin-dbdocu": "Semantic MediaWiki rique una estructura de base de datos propia (y ye independiente de MediaWiki, polo que nun afeuta al restu de la instalación) pa poder almacenar los datos semánticos.\nEsta función de configuración pue executase múltiples veces ensin peligru, pero namái ye necesaria una vez na instalación o anovamientu.", "smw-admin-permissionswarn": "Si la operación falla con errores SQL, probablemente l'usuariu de la base de datos qu'emplega la wiki (comprueba'l ficheru LocalSettings.php) nun tien permisos bastantes.\nO bien da-y a esti usuariu más permisos pa crear y desaniciar tables, o pon temporalmente los datos de conexón del usuariu root de la base de datos nel ficheru LocalSettings.php, o usa'l script de mantenimientu <code>setupStore.php</code>, que puede usar les credenciales d'un alministrador.", "smw-admin-dbbutton": "<PERSON><PERSON><PERSON> o anovar les tables", "smw-admin-announce": "Anuncia la to wiki", "smw-admin-announce-text": "Si la to wiki ye pública, puedes rexistrala en <a href=\"https://wikiapiary.com\">WikiApiary</a>, la wiki de siguimientu wikis.", "smw-admin-deprecation-notice-title": "Avisos d'obsolescencia", "smw-admin-deprecation-notice-docu": "La siguiente sección contién axustes que se quedaron anticuaos o se desaniciaron, pero que detectóse que tán activos nesta wiki. Espérase que cualquier versión futura desanicie l'encontu d'estes configuraciones.", "smw-admin-deprecation-notice-config-notice": "[https://www.semantic-mediawiki.org/wiki/Help:$1 $1] ta anticuáu y va desaniciase na versión $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> desaniciará (o reemplazará) {{PLURAL:$2|la opción siguiente|les opciones siguientes}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> ta anticuáu y va desaniciase'n $2", "smw-admin-deprecation-notice-config-replacement": "[https://www.semantic-mediawiki.org/wiki/Help:$1 $1] sustituyóse por [https://www.semantic-mediawiki.org/wiki/Help:$2 $2]", "smw-admin-deprecation-notice-config-replacement-option": "{{PLURAL:$2|Opción|Opciones}} de <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> ta trocándose por <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "[https://www.semantic-mediawiki.org/wiki/Help:$1 $1] desanicióse na versión $2", "smw-admin-deprecation-notice-title-notice": "Próximos cambios", "smw-admin-deprecation-notice-title-notice-explanation": "Detectóse qu'esta wiki usa les siguientes configuraciones que ta previsto desaniciar o camudar nuna versión futura.", "smw-admin-deprecation-notice-title-replacement": "Opciones sustituyíes o cambiaes de nome", "smw-admin-deprecation-notice-title-replacement-explanation": "La sección siguiente contien opciones que cambiaron el nome o recibieron otros cambeos. Encaméntase anovar darréu'l so nome o formatu.", "smw-admin-deprecation-notice-title-removal": "Opciones desaniciaes", "smw-admin-deprecation-notice-title-removal-explanation": "Les opciones siguientes desaniciáronse nuna versión anterior, pero detectóse qu'entá s'utilicen nesta wiki.", "smw-smwadmin-refresh-title": "Reparación y anovamientu de los datos", "smw_smwadmin_datarefresh": "Reconstrucción de los datos", "smw_smwadmin_datarefreshdocu": "Ye posible restaurar tolos datos de Semantic MediaWiki basandose nel conteníu actual de la wiki.\nEsto pue ser afayadizo pa reparar datos rotos o pa refrescar os datos si'l formatu internu cambió por un anovamientu del software.\nEsti anovamientu executase páxina a páxina y nun se completará nel intre.\nLo siguiente amuesa si un anovamientu ta en cursu y permiti-y principialos o detenelos (esceuto si esta carauterística ta desactivada pol alministrador del sitiu).", "smw_smwadmin_datarefreshprogress": "<strong>Yá hai una anovamientu en cursu.</strong>\nYe normal que l'anovamientu avance sólo de mou lentu, porque sólo refresca los datos en pequeños trozos cada vez qu'un usuariu entra na wiki.\nP'acabar esti anovamientu más rápido, pue llamar al script de mantenimientu de MediaWiki <code>runJobs.php</code> (use la opción <code>--maxjobs 1000</code> para restrinxir el númberu d'anovamientos fechos nun llote).\nProgresu estimáu del anovamientu actual:", "smw_smwadmin_datarefreshbutton": "Programar la reconstrucción de los datos", "smw_smwadmin_datarefreshstop": "Detener esti anovamientu", "smw_smwadmin_datarefreshstopconfirm": "Sí, toi {{GENDER:$1|seguru|segura}}.", "smw-admin-job-scheduler-note": "Les xeres (que tán activaes) d'esta seición execútense usando la cola de trabayos pa evitar situaciones de bloquéu demientres la execución. La [https://www.mediawiki.org/wiki/Manual:Job_queue cola de trabayos] ye la responsable del procesamientu, y ye crítico que'l script de caltenimientu <code>runJobs.php</code> (ver tamién el parámetru de configuración $<code>wgRunJobsAsync</code>) tenga una capacidá afayadiza.", "smw-admin-outdateddisposal-title": "Desaniciu d'entidaes anticuaes", "smw-admin-outdateddisposal-intro": "Delles actividaes (un cambéu a un tipu de propiedá, el desaniciu de páxines wiki o la correición de valores d'error) van producir [https://www.semantic-mediawiki.org/wiki/Outdated_entities entidaes anticuaes] y suxerse desaniciales dacuando pa lliberar l'espaciu de tables venceyáu.", "smw-admin-outdateddisposal-active": "Programóse un trabayu de desaniciu d'entidaes anticuaes.", "smw-admin-outdateddisposal-button": "Programar un desaniciu", "smw-admin-feature-disabled": "Esta carauterística ta desactivada nesta wiki, pa más información, consulta la páxina d'ayuda de <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">configuración</a> o comunícate col alministrador del sistema.", "smw-admin-propertystatistics-title": "Reconstruir les estadístiques de la propiedá", "smw-admin-propertystatistics-intro": "Reconstruye toles estadístiques d'usu de la propiedá y actualiza y corrixe la [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count cuenta d'usu] de propiedaes.", "smw-admin-propertystatistics-active": "Programóse un trabayu de reconstrucción d'estadístiques de propiedaes.", "smw-admin-propertystatistics-button": "Programar reconstrucción d'estadístiques", "smw-admin-fulltext-title": "Reconstrucción de gueta de testu completu", "smw-admin-fulltext-intro": "Reconstruye l'índiz de gueta dende les tables de propiedá con un tipu de datu activáu [https://@www.semantic-mediawiki.org/wiki/full-text gueta de testu completu]. Los cambeos nes normes d'índices (cambios nes palabres saltaes, nuevu algoritmu de radicales etc.) y/o una tabla nueva añadida o camudada riquen repetir esta xera.", "smw-admin-fulltext-active": "Programóse un trabayu de reconstrucción de guetes de testu completu.", "smw-admin-fulltext-button": "Programar reconstrucción de testu completu", "smw-admin-support": "Algamar asistencia", "smw-admin-supportdocu": "Úfrense dellos recursos p'ayudate si tienes problemes:", "smw-admin-installfile": "Si alcuentres problemes cola instalación, principia revisando les indicaciones del <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">ficheru INSTALL</a> y la <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">páxina d'instalación</a>.", "smw-admin-smwhomepage": "La documentación d'usuariu completa de Semantic MediaWiki ta en <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Pues informar de los errores nel <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">siguimientu de problemes</a>, la páxina de <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">informe de fallos</a> ufre dellos conseyos pa escribir un informe de fallos eficaz.", "smw-admin-questions": "Si tienes más preguntes o suxerencies, xúntate a la conversación de la <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">llista de corréu d'usuarios</a> de Semantic MediaWiki.", "smw-admin-other-functions": "Otres funciones", "smw-admin-supplementary-section-title": "Funciones suplementaries", "smw-admin-supplementary-section-subtitle": "Funciones centrales sofitaes", "smw-admin-supplementary-section-intro": "Esta sección apurre otres funciones más allá del ámbitu de les actividaes de caltenimientu y ye posible que delles funciones qu'apaecen (ver la [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentación]) tean torgaes o nun tean disponibles y polo tanto sin accesu nesta wiki.", "smw-admin-supplementary-settings-title": "Axustes de configuración", "smw-admin-supplementary-settings-intro": "<u>$1</u> xenera una llista de configuraciones que s'usen na MediaWiki Semántica", "smw-admin-supplementary-operational-statistics-title": "Estadístiques operatives", "smw-admin-supplementary-operational-statistics-intro": "Amuesa un conxuntu enantáu de <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Busca y desaniciu d'entidá", "smw-admin-supplementary-idlookup-intro": "Sofita una función simple pa <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Busca d'entidaes duplicaes", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> p'alcontrar entidaes que tán na categoría de los duplicaos de la tabla d'entidaes seleicionada", "smw-admin-supplementary-duplookup-docu": "Esta páxina amuesa la llista d'entraes de la [https://www.semantic-mediawiki.org/wiki/Help:Entity_table tabla d'entidaes] que punxeron na categoría de duplicaes. Les entraes duplicaes solo tendríen d'asoceder (como muncho) en rares ocasiones potencialmente causaes por un procesu termináu mientres una actualización de la base de datos o una transaición de reversión fallida.", "smw-admin-supplementary-operational-statistics-cache-title": "Estadístiques de la caché", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> amuesa estadístiques rellacionaes cola caché", "smw-admin-supplementary-elastic-intro": "<u>$1</u> informa sobro les preferencies y les estadístiques d'índices", "smw-admin-supplementary-elastic-docu": "Esta páxina contién información sobro axustes, mapeos, salú y estadístiques d'índices rellacionaos con un cluster d'Elasticsearch que ta conectáu a Semantic MediaWiki ya'l so [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Funciones sofitaes", "smw-admin-supplementary-elastic-settings-title": "Configuración", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> usáu por Elasticsearch p'alministrar los índices de MediaWiki Semántica", "smw-admin-supplementary-elastic-mappings-title": "Asignaciones", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> pa la llista d'índices y asihnaciones de campos", "smw-admin-supplementary-elastic-mappings-docu": "Esta páxina contien detalles de la correspondencia de campos porque s'utilicen colos índices actuales. El resume de correspondencies tendría de controlase en conexón col <code>index.mapping.total_fields.limit</code> qu'especifica'l númberu máximu de campos nun índiz.", "smw-admin-supplementary-elastic-mappings-summary": "Resume", "smw-admin-supplementary-elastic-mappings-fields": "Correspondencies de campos", "smw-admin-supplementary-elastic-nodes-title": "Nuedos", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> amuesa les de estadístiques de nuedos", "smw-admin-supplementary-elastic-indices-title": "Índices", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> ufre un resume de los índices disponibles y les sos estadístiques", "smw-admin-supplementary-elastic-statistics-title": "Estadístiques", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> amuesa les estadístiques a nivel d'índiz", "smw-admin-supplementary-elastic-statistics-docu": "Esta páxina apurre una visión de les estadístiques de los índices de les distintes operaciones que tán asocediendo a nivel d'índiz, les estadístiques devueltes combínense coles primaries y les totales. La [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html páxina d'ayuda] contién una descripción detallada de les estadístiques d'índices disponibles.", "smw-admin-supplementary-elastic-status-replication": "Estáu de replicación", "smw-admin-supplementary-elastic-status-last-active-replication": "Última replicación activa: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Intervalu d'enfrescáu: $1", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replicación bloquiada: $1 (en procesu de reconstrucción)", "smw-list-count": "La llista contién $1{{PLURAL:$1|entrada|entraes}}.", "smw-property-label-uniqueness": "La etiqueta «$1» casa polo menos con otra representación de propiedá. Consulta la [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness páxina d'ayuda] pa saber cómo iguar esti problema.", "smw-property-label-similarity-title": "Informe de semeyanza na etiqueta Propiedá", "smw-property-label-similarity-intro": "<u>$1</u> calcula la semeyanza de les etiquetes de propiedá qu'esisten", "smw-property-label-similarity-threshold": "Estragal:", "smw-property-label-similarity-type": "Amosar l'identificador de tipu", "smw-property-label-similarity-noresult": "<PERSON>un s'atoparon resultancies pa les opciones escoyíes.", "smw-property-label-similarity-docu": "Compara y informa de la [https://www.semantic-mediawiki.org/wiki/Property_similarity semeyanza sintáutica] (non la semeyanza semántica) ente dos etiquetes de propiedá, lo que puede ayudar a filtriar propiedaes mal escrites o equivalientes que representen el mesmu conceutu (visita la páxina especial [[Special:Properties|Propiedaes]] p'aclarar los conceutos y los usos de les propiedaes del informe). Puede afaese l'estragal p'aumentar o menguar la distancia de semeyanza. <code>[[Property:$1|$1]]</code> utilízase pa quitar propiedaes del analís.", "smw-admin-operational-statistics": "Esta páxina contién estadístiques d'operación recoyíes en o dende funciones rellacionaes cola MediaWiki Semántica. Puedes alcontrar [[Special:Statistics|<b>equí</b>]] una llista enantada d'estadístiques específiques de la wiki.", "smw_adminlinks_datastructure": "Estructura de datos", "smw_adminlinks_displayingdata": "Visualización de los datos", "smw_adminlinks_inlinequerieshelp": "Ayuda de les consultes en llinia", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Númberu d'usos] albidráu: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Propiedá definida pol {{PLURAL:$1|usuariu|sistema}}", "smw-property-indicator-last-count-update": "Cuenta estimada d'usu\nAnovada por última vegada: $1", "smw-concept-indicator-cache-update": "Cuenta de caché\nÚltima actualización: $1", "smw-createproperty-isproperty": "Ye una propiedá de tipu $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|El valor permitíu|Los valores permitíos}} pa esta propiedá {{PLURAL:$1|ye|son}}:", "smw-paramdesc-category-delim": "El delimitador", "smw-paramdesc-category-template": "Una plantía cola que dar formatu a los elementos", "smw-paramdesc-category-userparam": "Un parámetru pa pasar a la plantía", "smw-info-par-message": "Mensaxe a amosar.", "smw-info-par-icon": "Iconu a amosar; o \"info\" o bien \"warning\".", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Opciones xenerales", "prefs-ask-options": "Opciones de gueta semántica", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] (y les estensiones rellacionaes) ufren una personalización individual pa delles funciones seleicionaes. Consulta la [https://www.semantic-mediawiki.org/wiki/Help:User_preferences páxina d'ayuda] pa ver una descripción detallada.", "smw-prefs-ask-options-tooltip-display": "Amosar el testu del parámetru como información d'ayuda", "smw-prefs-ask-options-compact-view-basic": "Activar vista compacta básica", "smw-prefs-help-ask-options-compact-view-basic": "Si s'activa, amuesa un conxuntu amenorgáu d'enllaces na vista compacta de Special:Ask.", "smw-prefs-general-options-time-correction": "Activar la correición horaria pa les páxines especiales usando la configuración llocal d'[[Special:Preferences#mw-prefsection-rendering|estaya horaria]]", "smw-prefs-general-options-jobqueue-watchlist": "Amosar la llista de siguimientu de la cola de xeres na mio barra personal", "smw-prefs-help-general-options-jobqueue-watchlist": "Si s'activa, amuesa una [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist llista de] xeres escoyíes pendientes xunto colos tamaños de cola albidraos.", "smw-prefs-general-options-disable-editpage-info": "Desactivar el testu d'introducción de la páxina d'edición", "smw-prefs-general-options-disable-search-info": "Desactivar la información d'ayuda de sintaxis na páxina estándar de busques", "smw-prefs-general-options-suggester-textinput": "Activar l'ayuda d'entrada pa les entidaes semántiques", "smw-ui-tooltip-title-property": "Propiedá", "smw-ui-tooltip-title-quantity": "Conversión d'unidaes", "smw-ui-tooltip-title-info": "Información", "smw-ui-tooltip-title-service": "Enllaces de serviciu", "smw-ui-tooltip-title-warning": "Avisu", "smw-ui-tooltip-title-error": "Error", "smw-ui-tooltip-title-parameter": "Parámetru", "smw-ui-tooltip-title-event": "Actividá", "smw-ui-tooltip-title-note": "<PERSON>a", "smw-ui-tooltip-title-legend": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-reference": "Referencia", "smw_unknowntype": "El tipu «$1» d'esta propiedá ye inválidu", "smw-concept-cache-text": "El conceutu tien un total de $1 {{PLURAL:$1|páxina|páxines}}, y l'últimu anovamientu foi el $3 a les $2.", "smw_concept_header": "Páxines del conceptu \"$1\"", "smw_conceptarticlecount": "Abaxo {{PLURAL:$1|s'amuesa $1 páxina|s'amuesen $1 páxines}}.", "smw-qp-empty-data": "Los datos solicitaos nun se pudieron amosar por unos criterios de seleición insuficientes.", "right-smw-admin": "Entrar nes xeres alministratives (Semantic MediaWiki)", "right-smw-patternedit": "Accesu d'edición pa caltener espresiones regulares y patrones permitíos (Semantic MediaWiki)", "right-smw-pageedit": "Accesu d'edición pa páxines con anotación <code>is edit protected</code> (MediaWiki Semántica)", "restriction-level-smw-pageedit": "protexida (sólo usuarios permitíos)", "action-smw-patternedit": "editar les espresiones regulares qu'usa Semantic MediaWiki", "action-smw-pageedit": "editar páxines cola anotación <code>is edit protected</code> (MediaWiki Semántica)", "group-smwadministrator": "Alministradores (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|alministrador|alministradora}} (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Alministradores (Semantic MediaWiki)", "group-smwcurator": "Conservadores (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|conservador|conservadora}} (Semantic MediaWiki)", "grouppage-smwcurator": "{{ns:project}}:Conservadores (Semantic MediaWiki)", "action-smw-admin": "entrar nes xeres alministratives de Semantic MediaWiki", "action-smw-ruleedit": "editar les páxines de regles (Semantic MediaWiki)", "smw-property-predefined-default": "«$1» ye una propiedá predefinida.", "smw-property-predefined-common": "Esta propiedá ta pre-desplegada (tamién conocida como [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propiedá especial]) y vien con otros privilexos alministrativos más, pero puede usase como cualquier otra [https://www.semantic-mediawiki.org/wiki/Property propiedá definida pol usuariu].", "smw-property-predefined-ask": "«$1» ye una propiedá predefinida que representa meta-información (en forma de [https://www.semantic-mediawiki.org/wiki/Subobject suboxetu]) sobro consultes individuales y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "«$1» ye una propiedá predefinida que recueye'l númberu de condiciones utilizaes n'una consulta y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "«$1» ye una propiedá predefinida qu'informa sobre la fondura d'una consulta, y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "Ye un valor numbéricu calculáu en base al añeramientu de subconsultes, cadenes de propiedaes, y elementos de descripción disponibles cola execución d'una consulta torgada pol parámetru de configuración <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "«$1» ye una propiedá predefinida, que describe parámetros qu'inflúen na resultancia d'una consulta, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "Ye parte d'una colección de\npropiedaes qu'especifiquen un [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler perfil de consulta].", "smw-sp-properties-docu": "Esta páxina amuesa les [https://www.semantic-mediawiki.org/wiki/Property propiedaes] y la cuenta d'usos disponible pa esta Wiki. Pa unes estadístiques actualizaes de cuentes recomiéndase executar el script de mantenimientu d'[https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics estadístiques de propiedá] de mou regular . Pa una vista diferenciada, mira les páxines especiales [[Special:UnusedProperties|propiedaes ensin usu]] o [[Special:WantedProperties|propiedaes buscaes]].", "smw-sp-properties-cache-info": "Los datos de la llista recuperaronse de la [https://www.semantic-mediawiki.org/wiki/Caching cache], y anovaronse por última vez el $1.", "smw-sp-properties-header-label": "Llist<PERSON> de propiedae<PERSON>", "smw-admin-settings-docu": "Amuesa la llista de tolos axustes predeterminaos y llocalizaos que tan rellacionaos col entornu de MediaWiki Semántica. Pa los detalles de los axustes individuales, consulta la páxina d'ayuda de la [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuración].", "smw-sp-admin-settings-button": "Xenerar la llista d'axustes", "smw-admin-idlookup-title": "<PERSON><PERSON><PERSON>", "smw-admin-idlookup-docu": "Esta seición amuesa detalles téunicos sobro una entidá individual (páxina wiki, suboxetu, propiedá, etc.) en Semantic MediaWiki. La entrada puede ser un númberu ID o un valor de cadena que case col campu de gueta relevante, anque cualquier referencia ID ta rellacionada con Semantic MediaWiki y non col ID de páxina o de revisión de MediaWiki.", "smw-admin-iddispose-title": "Desaniciu", "smw-admin-iddispose-docu": "Repara en que la operación de desaniciu nun tien torga y desaniciará la entidá del motor d'almacenamientu xunto con toles referencies en tables pendientes, si se confirma. Fai esta xera con '''procuru''' y sólo después de consultar la [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentación].", "smw-admin-iddispose-done": "Desanicióse la ID «$1» del motor d'almacenamientu.", "smw-admin-iddispose-references": "ID «$1» {{PLURAL:$2|nun tien|tien polo menos}} una referencia activa:", "smw-admin-iddispose-references-multiple": "Llista de correspondencies con polo menos un rexistru de referencia activu.", "smw-admin-iddispose-no-references": "La gueta nun atopó «$1» en nenguna entrada de tabla.", "smw-admin-idlookup-input": "Gueta:", "smw-admin-objectid": "Identificador:", "smw-admin-tab-general": "Supervisión", "smw-admin-tab-notices": "Avisos de desusu", "smw-admin-tab-supplement": "Funciones suplementaries", "smw-admin-tab-registry": "Rexistru", "smw-livepreview-loading": "Cargando...", "smw-sp-searchbyproperty-description": "Esta páxina apurre una [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces interfaz de navegación] simple p'atopar entidaes descrites por una propiedá y un valor con nome. Otres interfaces de busca disponibles incluyen la [[Special:PageProperty|busca de propiedaes  de páxina]] y el [[Special:Ask|constructor de consultes]].", "smw-sp-searchbyproperty-resultlist-header": "Llista de resultaos", "smw-sp-searchbyproperty-nonvaluequery": "Una llista de valores que tienen atribuída la propiedá «$1».", "smw-sp-searchbyproperty-valuequery": "Una llista de páxines que tienen la propiedá \"$1\" col valor \"$2\" anotáu.", "smw-datavalue-number-textnotallowed": "\"$1\" nun puede atribuise a un tipu de númberu declaráu con valor $2.", "smw-datavalue-number-nullnotallowed": "«$1» devolvió un \"NULL\" que nun se permite como númberu.", "smw-editpage-annotation-enabled": "Esta páxina almite anotaciones semántiques nel testu (p. ex. <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) pa construir conteníu estructuráu y consultable facilitáu por Semantic MediaWiki. Pa una descripción completa sobre cómo usar anotaciones o la función analizadora #ask, consulta les páxines d'ayuda [https://www.semantic-mediawiki.org/wiki/Help:Getting_started primeros pasos], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation anotaciones nel testu] o [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries consultes en llinia].", "smw-editpage-annotation-disabled": "Esta páxina nun ta activada pa les anotaciones semántiques nel testu por torgues nel espaciu de nomes. Los detalles de como activar l'espaciu de nomes pueden alcontrase na páxina d'ayuda de la [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuración].", "smw-editpage-property-annotation-enabled": "Esta propiedá puede estendese usando anotaciones semántiques pa especificar un tipu de datu (p. ex. <nowiki>\"[[Has type::Page]]\"</nowiki>) o otres declaraciones d'asistencia (p. ex. <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). Pa saber cómo aumentar esta páxina, consulta les páxines d'ayuda [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaración d'una propiedá] o la [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes llista de los tipos de datos disponibles].", "smw-editpage-property-annotation-disabled": "Esta propiedá nun puede estendese con una anotación de tipu de datu (p. ex. <nowiki>\"[[Has type::Page]]\"</nowiki>) porque yá ta predefinida (pa más información, consulta la páxina d'ayuda [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propiedaes especiales]).", "smw-editpage-concept-annotation-enabled": "Esti conceptu puede ampliase usando la función d'analís #concept. Pa una descripción de como usar #concept, consulta la páxina d'ayuda [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceptos].", "smw-search-syntax-support": "La entrada de la gueta permite usar la [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search sintaxis de consulta] semántica p'atopar resultancies al usar Semantic MediaWiki.", "smw-search-input-assistance": "El [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance asistente d'entrada] tamién ta activáu pa facilitar la pre-selección de les propiedaes y categoríes disponibles.", "smw-search-input": "Entrada y gueta", "smw-search-syntax": "Sintaxis", "smw-search-profile": "Estendida", "smw-search-profile-tooltip": "Funciones de gueta en conexón con Semantic MediaWiki", "smw-search-profile-sort-recent": "Más nuevu", "smw-search-profile-sort-title": "T<PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-query": "Utilizóse <code><nowiki>$1</nowiki></code> como consulta.", "smw-search-profile-extended-help-find-forms": "formes disponibles", "smw-search-profile-extended-section-sort": "Ordenar por", "smw-search-profile-extended-section-form": "Formes", "smw-search-profile-extended-section-namespace": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-query": "Consulta", "log-name-smw": "Rexistru de MediaWiki Semántica", "log-show-hide-smw": "$1 el rexistru de MediaWiki Semántica", "logeventslist-smw-log": "Rexistru de MediaWiki Semántica", "log-description-smw": "Actividaes de los [https://www.semantic-mediawiki.org/wiki/Help:Logging tipos d'actividá activaos] de los qu'informaron MediaWiki Semántica y los sos componentes.", "logentry-smw-maintenance": "Actividaes rellacionaes col mantenimientu emitíes por MediaWiki Semántica", "smw-datavalue-import-unknown-namespace": "L'espaciu de nomes d'importación «$1» ye desconocíu. Comprueba que los detalles d'importación de OWL tean disponibles al traviés de [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Nun pudo alcontrase la URI del espaciu de nomes «$1» na [[MediaWiki:Smw import $1|importación de $1]].", "smw-datavalue-import-missing-type": "Nun s'alcontró la definición de tipu pa «$1» na [[MediaWiki:Smw import $2|importación de $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Importación de $1]]", "smw-datavalue-import-invalid-value": "«$1» nun ye un formatu válidu, habría de tener el formatu «espaciu de nomes»:«identificador» (p.ex. «foaf:nome»).", "smw-datavalue-import-invalid-format": "Esperábase que la cadena «$1» tuviera xebrada en cuatro partes, pero nun s'entendió'l so formatu.", "smw-property-predefined-impo": "«$1» ye una propiedá predefinida que describe la rellación con un [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabulariu importáu] y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "«$1» ye una propiedá predefinida que describe'l [[Special:Types|tipu de datu]] d'una propiedá y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "«$1» ye una propiedá predefinida que representa una construcción [https://www.semantic-mediawiki.org/wiki/Help:Container contenedor] y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "El contenedor permite acumular pares propiedá-valor asemeyaes a les d'una páxina wiki normal pero dientro d'un espaciu d'entidaes distintu mentanto ta enllazáu al tema que lu incluye.", "smw-property-predefined-errp": "«$1» ye una propiedá predefinida pa siguir los errores d'entrada por anotaciones de valores irregulares, y ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "Na mayoría de los casos ta provocao porque nun casen los tipos o pola torga d'un [[Property:Allows value|valor]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value «$1»] ye una propiedá predefinida que puede definir una llista de valores posibles pa torgar designaciones de valores pa una propiedá y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list «$1»] ye una propiedá predefinida que puede especificar una referencia a una llista que contién valores permitíos pa torgar designaciones de valores para una propiedá, y que ta proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-datavalue-property-restricted-annotation-use": "La propiedá «$1» tien un campu d'aplicación llindáu y nun puede utilizase como propiedá d'anotación polos usuarios.", "smw-datavalue-property-restricted-declarative-use": "La propiedá «$1» ye declarativa y solo puede utilizase nes páxines de propiedá o de categoría.", "smw-datavalue-property-create-restriction": "Nun esiste la propiedá «$1» y l'usuariu nun tien el permisu «$2» (consulta [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode mou d'autoridá]) pa crear o anotar valores con una propiedá nun aprobada.", "smw-datavalue-property-invalid-character": "«$1» contién el caráuter «$2» d'una llista como parte de la etiqueta de propiedá y, poro, clasificóse como inválidu.", "smw-datavalue-property-invalid-chain": "Nun se permite usar «$1» como cadena de propiedaes mientres el procesu d'anotación.", "smw-datavalue-restricted-use": "Esti valor de datos marcóse pa usu acutáu. $1", "smw-datavalue-invalid-number": "<PERSON>un puede interpretase «$1» como númberu.", "smw-query-condition-circular": "Detectóse una posible condición circular en «$1».", "smw-types-list": "Llista de tipos de datos", "smw-types-default": "«$1» ye un tipu de datos integráu.", "smw-types-help": "Hai más información y exemplos na [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 páxina d'ayuda].", "smw-type-anu": "«$1» ye una variante del tipu de datos [[Special:Types/URL|URL]] que s'usa principalmente pa la declaración d'esportación de ''owl:AnnotationProperty''.", "smw-type-boo": "«$1» ye un tipu de datu primitivu pa describir un valor verdadero/falso.", "smw-type-cod": "«$1» ye una variante del tipu de datu  [[Special:Types/Text|Testu]] pa usar en testos técnicos de llonxitú variable, como llistaos de códigu fonte.", "smw-type-geo": "«$1» ye un tipu de datu que describe llugares xeográficos y rique la [https://www.semantic-mediawiki.org/wiki/Extension:Maps estensión «Mapes»].", "smw-type-tel": "«$1» ye un tipu de datos especial pa describir númberos de teléfonu internacionales acordies con RFC 3966.", "smw-type-txt": "«$1» ye un tipu de datos primitivu pa describir cadenes de testu de llonxitú arbitraria.", "smw-type-dat": "«$1» ye un tipu de datos pa representar puntos nel tiempu nun formatu unificáu.", "smw-property-predefined-errc": "«$1» ye una propiedá predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] que representa errores qu'apaecieron venceyaos a anotaciones de valores o procesamientu d'entraes inapropiaos.", "smw-property-predefined-long-errc": "Los errores recuéyense nun [https://www.semantic-mediawiki.org/wiki/Help:Container contenedor] que puede incluir una referencia a la propiedá que causó'l desacuerdu.", "smw-property-predefined-errt": "«$1» ye una propiedá predefinida que contién el testu de descripción d'un error y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Un suboxetu definíu pol usuariu tenía un esquema de nomes inválidu. Un nome con un puntu ($1) usáu nos cinco primeros caráuteres ta acutáu pa les estensiones. Puedes definir un [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier identificador con nome].", "smw-datavalue-record-invalid-property-declaration": "La definición del rexistru contién la propiedá «$1» declarada ella mesma como de tipu rexistru, lo que nun ta permitío.", "smw-property-predefined-mdat": "«$1» ye una propiedá predefinida que correspuende cola data del últimu cambiu d'un tema y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "«$1» ye una propiedá predefinida que correspuende cola data de la primera revisión d'un tema y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "«$1» ye una propiedá predefinida qu'indica si un tema ye nuevu o non y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "«$1» ye una propiedá predefinida que contién el nome de páxina del usuariu que creó la última revisión y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "«$1» ye una propiedá predefinida que describe'l tipu MIME d'un ficheru xubíu y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "«$1» ye una propiedá predefinida que describe'l tipu de multimedia d'un ficheru xubíu y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "«$1» ye una propiedá predefinida que contién el nome del formatu del resultáu usáu n'una consulta y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "«$1» ye una propiedá predefinida que describe les condiciones d'una consulta como cadena y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "«$1» ye una propiedá predefinida que contién un valor de tiempu (en segundos) que la consulta necesitó pa completar la execución y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "«$1» ye una propiedá predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], qu'identifica fontes alternatives de consultes (por casu, fontes remotes o federaes).", "smw-property-predefined-prec": "\"$1\" ye una propiedá predefinida que describe una [https://www.semantic-mediawiki.org/wiki/Help:Display_precision precisión de representación] (en cifres decimales) pa tipos de datos numbéricos.", "smw-types-extra-geo-not-available": "<PERSON>un se <PERSON>ó la [https://www.semantic-mediawiki.org/wiki/Extension:Maps estensión «Mapes»] y, darr<PERSON>u, «$1» tien torgada la capacidá d'operar.", "smw-datavalue-monolingual-dataitem-missing": "Falta un elementu esperáu pa construir un valor compuestu monollingüe.", "smw-datavalue-languagecode-missing": "L'analizador nun pudo determinar el códigu de llingua (p.ex. \"fulanu@ast\") pa l'anotación «$1».", "smw-datavalue-languagecode-invalid": "Nun se reconoció «$1» como códigu de llingua válidu.", "smw-property-predefined-lcode": "«$1» ye una propiedá predefinida que representa un códigu de llingua en formatu BCP47 y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "«$1» ye un tipu de datos [https://@www.semantic-mediawiki.org/wiki/Help:Container contenedor] qu'asocia un valor de testu con un [[Property:Language code|códigu de llingua]] determináu.", "smw-types-extra-mlt-lcode": "El tipu de datos {{PLURAL:$2|rique|nun rique}} un códigu de llingua (por casu, {{PLURAL:$2|nun s'acepta|s'acepta}} una anotación de valor sin un códigu de llingua).", "smw-property-predefined-text": "«$1» ye una propiedá predefinida que representa testu de cualquier llargor y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "\"$1\" ye una propiedá predefinida que permite describir una propiedá nel contestu d'una llingua, y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "«$1» ye una propiedá predefinida pa definir una llista de propiedaes que s'usen con una propiedá de tipu [[Special:Types/Record|rexistru]] y que ta proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Tiempu d'analís d'anotaciones nel testu", "smw-limitreport-intext-postproctime": "[SMW] duración del pos-procesamientu", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|segundu|segundos}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|segundu|segundos}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Tiempu d'actualización del almacenamientu (na purga de páxines)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|segundu|segundos}}", "smw_allows_pattern": "Espérase qu'esta páxina contenga una llista de referencies (siguía por [https://en.wikipedia.org/wiki/Regular_expression espresiones regulares]) pa que s'ufra pola propiedá [[Property:Allows pattern|Permite patrón]]. Pa editar esta páxina, ríquese'l permisu <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "La espresión regular «$2» clasificó a «$1» como inválida.", "smw-datavalue-allows-pattern-reference-unknown": "La referencia del patrón «$1» nun correspuende a nenguna entrada de [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "La referencia de llista «$1» nun correspuende con una páxina en [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "Nel conteníu de la llista «$1» falten elementos con un marcador de llista *.", "smw-datavalue-feature-not-supported": "La función «$1» nun tien encontu o se desactivó nesta wiki.", "smw-datavalue-external-formatter-invalid-uri": "«$1» ye una URL inválida.", "smw-datavalue-external-identifier-formatter-missing": "Falta atribuir una [[Property:External formatter uri|\"URI de formateador esternu\"]] a la propiedá.", "smw-datavalue-parse-error": "<PERSON>un s'entendió el valor dau «$1».", "smw-datavalue-propertylist-invalid-property-key": "La llista de propiedaes «$1» contenía una clave de propiedá non válida, «$2».", "smw-parser-invalid-json-format": "L'analizador JSON devolvió «$1».", "smw-property-preferred-label-language-combination-exists": "Nun puede utilizase «$1» como etiqueta preferida porque la llingua «$2» yá se venceyó a la etiqueta «$3».", "smw-clipboard-copy-link": "Copiar l'enllaz al cartafueyu", "smw-data-lookup": "Recuperando los datos...", "smw-data-lookup-with-wait": "La solicitú ta procesándose y puede tardar un momentu.", "smw-no-data-available": "<PERSON>un hai datos disponibles.", "smw-property-req-violation-missing-fields": "A la propiedá «$1» fálten-y los detalles de declaración pal tipu «$2» anotáu al nun definir la propiedá <code>Tien campos</code>.", "smw-edit-protection-enabled": "Protexíu contra ediciones (Semantic MediaWiki)", "smw-patternedit-protection": "Esta páxina ta protexida y sólo pueden editala los usuarios que tengan los  [https://www.semantic-mediawiki.org/wiki/Help:Permissions permisos] <code>smw-patternedit</code> afayadizos.", "smw-query-reference-link-label": "Referencia de consulta", "smw-format-datatable-emptytable": "<PERSON>un hai datos disponibles na tabla", "smw-format-datatable-info": "<PERSON>ando de la _START_ a la _END_ de _TOTAL_ entraes", "smw-format-datatable-infoempty": "Amosando de 0 a 0 de 0 entraes", "smw-format-datatable-infofiltered": "(filtriaes d'un total de _MAX_ entraes)", "smw-format-datatable-infothousands": ".", "smw-format-datatable-lengthmenu": "Amosar _MENU_ entraes", "smw-format-datatable-loadingrecords": "Cargando...", "smw-format-datatable-processing": "Procesando...", "smw-format-datatable-search": "Buscar:", "smw-format-datatable-zerorecords": "<PERSON>un s'<PERSON>aron rexistros que concasen", "smw-format-datatable-first": "Primer", "smw-format-datatable-last": "<PERSON><PERSON>", "smw-format-datatable-next": "Siguient<PERSON>", "smw-format-datatable-previous": "Anterior", "smw-format-datatable-sortascending": ": activar pa ordenar la columna de mou ascendiente", "smw-format-datatable-sortdescending": ": activar pa ordenar la columna de mou descendiente", "smw-format-datatable-toolbar-export": "Esportar", "smw-category-invalid-redirect-target": "La categoría «$1» contién un destín de redireición inválidu a un espaciu de nomes que nun ye de categoría.", "apihelp-ask-summary": "Módulu de la API pa consultar Semantic MediaWiki usando'l llinguaxe Ask.", "apihelp-askargs-summary": "Módulu de la API pa consultar Semantic MediaWiki usando'l llinguaxe Ask como llista de condiciones, visualizaciones y parámetros.", "apihelp-browsebyproperty-summary": "Módulu de la API pa recuperar información sobre dalguna propiedá o llista de propiedaes.", "apihelp-browsebysubject-summary": "Módulu de la API pa recuperar información sobre dalguna tema.", "apihelp-smwtask-summary": "Módulu de la API pa executar xeres rellacionaes con Semantic MediaWiki.", "apihelp-smwbrowse-summary": "Módulu de la API pa encontu de les actividaes de navegación pa distintos tipos d'entidaes de Semantic MediaWiki.", "smw-property-page-list-count": "Amosando $1 {{PLURAL:$1|páxina qu'usa|páxines qu'usen}} esta propiedá.", "smw-property-page-list-search-count": "Amosando $1 {{PLURAL:$1|páxina qu'usa|páxines qu'usen}} esta propiedá con una correspondencia de valor «$2».", "smw-property-reserved-category": "Categoría", "smw-category": "Categoría", "smw-datavalue-uri-invalid-scheme": "<PERSON>un s'incluyó «$1» nos esquemes d'URI válidos.", "smw-browse-property-group-title": "Grupu de propiedae<PERSON>", "smw-browse-property-group-label": "Etiqueta de grupu de propiedaes", "smw-browse-property-group-description": "Descripción de grupu de propiedaes", "smw-section-expand": "Espander la sección", "smw-section-collapse": "Contraer la sección", "smw-ask-format-help-link": "Formatu [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Chuleta", "smw-personal-jobqueue-watchlist": "Llista de siguimientu de cola de xeres", "smw-property-predefined-label-skey": "Clave d'ordenación", "smw-processing": "Procesando...", "smw-redirect-target-unresolvable": "El destín nun puede resolvese pol motivu «$1»", "smw-types-title": "Tipu: $1", "smw-ask-title-keyword-type": "Busca de palabres clave", "smw-ask-message-keyword-type": "Esta busca casa cola condición <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Nun pudo coneutase col destín remotu «$1».", "smw-remote-source-disabled": "L'orixe '''$1''' desactivó l'encontu de solicitúes remotes.", "smw-pendingtasks-setup-tasks": "Xeres", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "Abaxo s'{{PLURAL:$1|amuesa hasta <strong>un</strong> resultáu|amuesen <strong>$1</strong> resultaos}}, principando por #<strong>$2</strong>."}