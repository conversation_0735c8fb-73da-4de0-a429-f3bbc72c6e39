{"@metadata": {"authors": ["<PERSON>es", "McDut<PERSON><PERSON>", "<PERSON><PERSON>", "Soued031", "Volvox"]}, "smw-desc": "Är Wiki méi accessibel machen - fir Maschinnen ''a'' <PERSON><PERSON> ([https://www.semantic-mediawiki.org/wiki/Help:User_manual online documentation])", "smw-error": "Feeler", "smw-upgrade-progress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-error-title": "Feeler »Semantic MediaWiki", "smw-upgrade-error-why-title": "<PERSON><PERSON><PERSON> gesinn ech dë<PERSON>?", "smw-upgrade-error-how-title": "Wéi kann ech dëse <PERSON> fl<PERSON>cken?", "smw-extensionload-error-why-title": "<PERSON><PERSON><PERSON> gesinn ech dë<PERSON>?", "smw-extensionload-error-how-title": "Wéi kann ech dëse <PERSON> fl<PERSON>cken?", "smw-upgrade-maintenance-why-title": "<PERSON><PERSON><PERSON> gesinn ech dë<PERSON>?", "smw-semantics-not-enabled": "D'Semantic MediaWiki Funktioun gouf op dëser Wiki net ageschalt.", "smw_viewasrdf": "RDF-Feed", "smw_finallistconjunct": ", an", "smw-factbox-head": "... méi iwwer \"$1\"", "smw-factbox-facts": "<PERSON>ak<PERSON>", "smw-factbox-attachments-is-local": "Ass lokal", "smw_isspecprop": "Dës Eegenschaft ass eng Spezial-Eegenschaft an dëser Wiki.", "smw-concept-cache-header": "Benotze vum Tëschespäicher", "smw-concept-no-cache": "<PERSON>en Tëschespäicher disponibel.", "smw_concept_description": "Beschreiwung vum Konzept \"$1\"", "smw_multiple_concepts": "Op jiddwer Konzeptsäit ka just eng Definitioun vun engem Konzept stoen.", "version-semantic": "Softwareerweiderungen (Semantic MediaWiki)", "smw_baduri": "URIë vun der Form \"$1\" sinn net erlaabt.", "smw_printername_count": "Resultater zielen", "smw_printername_csv": "Export als CSV", "smw_printername_dsv": "DSV-Export", "smw_printername_debug": "Debug-Ufro (fir Experten)", "smw_printername_embedded": "Säiteninhalter abannen", "smw_printername_json": "Export als JSON", "smw_printername_list": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_plainlist": "Lësch<PERSON> (einfach)", "smw_printername_ol": "Nummer<PERSON><PERSON><PERSON>", "smw_printername_ul": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "Breed <PERSON>", "smw_printername_template": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_rdf": "RDF-Export", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "Text", "smw-paramdesc-limit": "D'maximal <PERSON>uel vu Resultater, déi gewise ginn", "smw-paramdesc-mainlabel": "D'Etikett déi der Haaptsäit den Numm gëtt", "smw-paramdesc-link": "D'Werter als Linke weisen", "smw-paramdesc-intro": "<PERSON>, dee virun de Resultater vun der Ufro gewise g<PERSON>tt, wann et der gëtt", "smw-paramdesc-outro": "Den Text deen no de Resultater vun der Ufro gewise gëtt, wann et der gëtt", "smw-paramdesc-default": "Den Text den ugewise gëtt wann et keng Resultater beim <PERSON> gëtt", "smw-paramdesc-sep": "D'Trennzeeche tëscht Resultater", "smw-paramdesc-template": "Den Numm vun enger <PERSON> mat där d'Drockversioune gewise ginn", "smw-paramdesc-columns": "D?Zuel vu Kolonnen an deenen d'Resultater vun der Si<PERSON> gewise ginn", "smw-paramdesc-userparam": "<PERSON> <PERSON><PERSON><PERSON> deen an all Opruff vun enger <PERSON>loun dragesat gëtt, wann eng <PERSON>habloun benotzt gëtt", "smw-paramdesc-introtemplate": "Den Numm vun en<PERSON>, déi virun de Resultater vun der Ufro gewise g<PERSON>tt, wann et der gëtt", "smw-paramdesc-outrotemplate": "Den Numm vun en<PERSON>, déi no de Resultater vun der Ufro gewise g<PERSON>tt, wann et der g<PERSON>tt", "smw-paramdesc-embedformat": "Den HTML-Tag dee benotzt gëtt fir d'Iwwerschrëften ze definéieren", "smw-paramdesc-embedonly": "<PERSON><PERSON>ë<PERSON>e weisen", "smw-paramdesc-table-class": "<PERSON><PERSON> zousätzlech CSS-Klass fir Tabellen", "smw-paramdesc-table-transpose": "D'Iwwerschrëfte vun Tabelle vertikal an d'Resultater horizontal weisen", "smw-paramdesc-rdfsyntax": "RDF-Syntax déi benotzt gi muss", "smw-paramdesc-csv-sep": "Spezifizéiert d'Trennsymbol fir Kolonnen", "smw-paramdesc-dsv-separator": "D'Trennsymbol dat benotzt gi muss", "smw-paramdesc-dsv-filename": "Den Numm fir den DSV-Fichier", "smw-paramdesc-searchlabel": "Den Text fir mam Siche virunzefueren", "smw-paramdesc-export": "Export-Optiounen", "smw-paramdesc-jsonsyntax": "JSON Syntax muss benotzt ginn", "smw-printername-feed": "Feed (RSS/Atom)", "smw-paramdesc-feedtype": "Feed-<PERSON><PERSON>", "smw-label-feed-description": "$2-Feed: $1", "smw_iq_moreresults": "… weider Resultater", "smw_parseerror": "<PERSON>, deen <PERSON> a<PERSON>n hutt, gouf net verstanen.", "smw_notitle": "\"$1\" kann net als Numm vun enger Säit op dëser Wiki benotzt ginn.", "smw_wrong_namespace": "Nëmme Säiten aus dem Nummraum \"$1\" sinn hei erlaabt.", "smw_emptystring": "Eidel Zeeche ginn net akzeptéiert.", "smw_notinenum": "\"$1\" ass net an der Lëscht ($2) vun den [[Property:Allows value|erlaabte Wäerter]] fir d'Eegeschaft \"$3\".", "smw_true_words": "wouer,w,jo,j", "smw_false_words": "falsch,f,nee,n", "smw_nofloat": "\"$1\" ass keng Zuel.", "smw_infinite": "Zuele sou grouss wéi \"$1\" ginn net ënnerstëtzt.", "smw_novalues": "<PERSON><PERSON> spezifizé<PERSON>.", "smw_nodatetime": "Den Datum \"$1\" gouf net verstan.", "smw_noclosingbrackets": "Eng oder méi \"<nowiki>[[</nowiki>\" an Ärer Ufro war net zou duerch eng entspriechent \"]]\".", "smw_misplacedsymbol": "D'Symbol „$1“ gouf op ener Plaz <PERSON>, wou et net nëtzlech ass.", "smw_badqueryatom": "<PERSON><PERSON> \"<nowiki>[[...]]</nowiki>\" vun der Ufro gouf net verstanen.", "smw_propvalueproblem": "De Wäert vun der Eegenschaft \"$1\" gouf net verstane.", "smw_type_header": "Eegenschafte vum Typ \"$1\"", "smw_attribute_header": "Säiten, déi d'Eegenschaft „$1“ benotzen", "smw_attributearticlecount": "Et {{PLURAL:$1|gëtt eng Säit|gi(nn) $1 Säite}} gew<PERSON>n, déi dës Eegenschaft {{PLURAL:$1|benotzt|benotzen}}:", "smw-propertylist-subproperty-header": "Ënnereegenschaften", "smw-propertylist-redirect-header": "Synonymmen", "specialpages-group-smw_group-maintenance": "Maintenance", "exportrdf": "Säiten als RDF exportéieren", "smw_exportrdf_backlinks": "Och all Säiten déi op déi exportéiert Säite referéieren exportéieren.\nEt gët en RDF ugeluecht dee liicht duerchsicht ka ginn.", "smw_exportrdf_lastdate": "Keng Säiten exportéieren déi zënter dem Zäitpunkt deen uginn ass net geännert goufen.", "smw_exportrdf_submit": "Exportéieren", "properties": "Eegenschaften", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "Dës Eegenschafte ginn op dëser Wiki benotzt.", "smw_property_template": "$1 vum Typ $2 ({{PLURAL:$3|eemol benotzt|$3 benotzt}})", "smw_propertylackspage": "All Eegenschafte sollen op enger Säit beschriwwe sinn!", "smw_propertyhardlyused": "Dës Eegenschaft gëtt an dëser Wiki kaum benotzt!", "smw-special-property-searchform-options": "<PERSON><PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Net confirméiert", "concepts": "<PERSON><PERSON><PERSON><PERSON>", "smw-special-concept-header": "Lësch<PERSON> vun de Ko<PERSON>epter", "smw-special-concept-empty": "Et gouf kee <PERSON>ept fonnt.", "unusedproperties": "Onbenotzt Eegeschaften", "smw-unusedproperties-docu": "OP dëser Säit stinn déi [https://www.semantic-mediawiki.org/wiki/Unused_properties net benotzt Eegenschaften] déi dekla<PERSON>iert sinn och wa keng aner Säite se benotzen. Fir eng aner Vue kuckt d'Spezialsäite mat  [[Special:Properties|allen]] oder de [[Special:WantedProperties|gewënschten Eegenschaften]].", "smw-unusedproperty-template": "$1 vum Typ $2", "wantedproperties": "Gewënschten Eegeschaften", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|mol benotzt|mol benotzt}})", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|mol benotzt}})", "smw_purge": "Aktualiséieren", "smw-purge-failed": "Semantic MediWiki huet probéiert fir d'Säit eidel ze maachen awer dat huet net funktionéiert", "types": "Typpen", "smw-special-types-no-such-type": "<PERSON> deen ugi gouf g<PERSON>tt et net", "smw-statistics": "<PERSON><PERSON><PERSON><PERSON> Statistiken", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Eegenschaft|Eegenschaften}} (gesamt)", "smw-statistics-property-used": "{{PLURAL:$1|Eegenschaft|Eegenschaften}} (mat mindestens engem W<PERSON><PERSON>)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Ufro|Ufroen}}", "smw-statistics-query-format": "<code>$1</code> Format", "smw-statistics-query-size": "Gréisst vun der Ufro", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Konzept|Konzepter}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|<PERSON><PERSON><PERSON>|Ko<PERSON>epter}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Ënnerobjet|Ënnerobjeten}}", "ask": "<PERSON><PERSON><PERSON><PERSON> sichen", "smw_ask_sortby": "No der Kolonn sortéieren (fakultativ)", "smw_ask_ascorder": "<PERSON>u kleng op grouss (croissant)", "smw_ask_descorder": "vu grouss op kleng (décroissant)", "smw-ask-order-rand": "<PERSON><PERSON><PERSON>", "smw_ask_submit": "Resultater sichen", "smw_ask_editquery": "<PERSON><PERSON><PERSON>", "smw_add_sortcondition": "[Kondi<PERSON>oun fir d'Zortéieren derbäisetzen]", "smw_ask_hidequery": "Ufro verstoppen (kompakt Vue)", "smw_ask_help": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_queryhead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_printhead": "Eraussiche fir ze drécken", "smw_ask_printdesc": "(eng Eegenschaft pro Linn derbäisetzen)", "smw_ask_format_as": "Formatéieren als:", "smw_ask_defaultformat": "Standard", "smw_ask_otheroptions": "<PERSON><PERSON>", "smw-ask-otheroptions-collapsed-info": "Benotzt wgl. de 'Plus-Icon' fir all disponibel Optiounen ze gesinn", "smw_ask_show_embed": "Agebonnene Code weisen", "smw_ask_hide_embed": "Agebonnene Code verstoppen", "smw-ask-delete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-sorting": "Sortéierung", "smw-ask-options": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-options-sort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-format-options": "Format an Optiounen", "smw-ask-parameters": "Parameteren", "smw-ask-search": "<PERSON><PERSON>", "smw-ask-debug": "<PERSON>er analys<PERSON><PERSON>en", "smw-ask-no-cache": "<PERSON><PERSON>", "smw-ask-no-cache-desc": "Resultater ouni Tëschespäicher vun den Ufroen", "smw-ask-result": "Resultat", "smw-ask-empty": "Ko<PERSON>tt eidelmaachen", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Hëllef fir den erausgesichte Format: $1", "smw-ask-condition-input-assistance-property": "<code>p:</code> Fir 'D'Sichen no enger Eegenschaft z'aktivéieren  (z. Bsp. <code>[[p:Huet ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> fir Propose fir <PERSON><PERSON><PERSON>'aktivéieren", "smw-ask-condition-input-assistance-concept": "<code>con:</code> fir d'Konzeptsichen z'aktivéieren", "searchbyproperty": "No Eegenschafte sichen", "processingerrorlist": "Lëscht vun Veraarbechtungsfehler", "smw_sbv_docu": "Sichen no alle Säiten déi eng bestëmmt Eegenschaft mat engem bestëmmte Wä<PERSON> hunn.", "smw_sbv_property": "Eegenschaft:", "smw_sbv_value": "<PERSON><PERSON><PERSON>:", "smw_sbv_submit": "Resultater sichen", "browse": "Duerch d'Wiki goen", "smw_browselink": "Proprietéite weisen", "smw_browse_go": "<PERSON><PERSON>", "smw_browse_show_incoming": "Eegenschafte weisen, déi heihi linken", "smw_browse_hide_incoming": "Eegeschaften verstoppen déi hei hi linken", "smw_browse_no_outgoing": "<PERSON><PERSON><PERSON> huet keng Eegeschaften.", "smw_browse_no_incoming": "Et linke keng Eegeschaften op dës Säit.", "smw-browse-from-backend": "Informatioune ginn elo vum Backend ofgeruff.", "smw-browse-show-group": "Gruppe weisen", "smw-browse-hide-group": "Gruppe verstoppen", "smw_inverse_label_default": "$1 vu(n)", "pageproperty": "An den Eegenschafte vun der Säit sichen", "smw_pp_from": "<PERSON><PERSON> der <PERSON>äit:", "smw_pp_type": "Eegenschaft:", "smw_pp_submit": "Resultater sichen", "smw-prev": "vireg {{PLURAL:$1|$1}}", "smw-next": "nächst {{PLURAL:$1|$1}}", "smw_result_prev": "<PERSON><PERSON><PERSON>", "smw_result_next": "Nächst", "smw_result_results": "Resultater", "smw_result_noresults": "<PERSON><PERSON>, et gouf näischt fonnt.", "smwadmin": "Dashboard (Semantic MediaWiki)", "smw-admin-statistics-job-title": "<PERSON><PERSON><PERSON>ati<PERSON><PERSON><PERSON>", "smw-admin-statistics-querycache-title": "Tëschespäicher vun den Ufroen", "smw-admin-statistics-semanticdata-overview": "Iwwersiicht", "smw_smwadmin_return": "Zréck op $1", "smw_smwadmin_updatenotstarted": "Et ass schonn een Aktualiséiereungs-Prozess amgaang.\nEt ka keen neien ugefaang ginn.", "smw_smwadmin_updatestopped": "All Aktualisatiouns-Prozesser goufe gestoppt.", "smw-admin-docu": "Dës Spezialsäit hëlleft Iech wärend der Installatioun, der Aktualiséierung, der Maintenance an dem Gebrauch vu <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> an erméiglecht Iech weider administrativ Funktiounen an Aufgaben, geneesou wéi Statistiken.\nDenkt drun fir wichteg Donnéeën ze späicheren ier Dir administrativ Funktiounen ausféiert.", "smw-admin-db": "Datebank-installatioun an -aktualiséierung", "smw-admin-dbbutton": "Tabellen initialiséieren oder aktualiséieren", "smw-admin-announce": "Är Wiki ukënnegen", "smw-admin-announce-text": "Wann Är Wiki ëffentlech ass kënnt Dir se op der Wiki-Tracking-Wiki <a href=\"https://wikiapiary.com\">WikiApiary</a> registréieren.", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> ass vereelzt a gëtt a(n) $2 ewechgeholl", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> gouf schonn duerch <code>$2</code> ersat", "smw-admin-deprecation-notice-title-notice": "Vereelzt Astellungen", "smw-admin-deprecation-notice-title-replacement": "Ersat oder ëmbenannt Astellungen", "smw-admin-deprecation-notice-title-removal": "Astellungen déi ewechgeholl goufen", "smw-admin-deprecation-notice-section-legend": "Erklärung", "smw_smwadmin_datarefresh": "<PERSON><PERSON><PERSON><PERSON> goufe restauré<PERSON>t", "smw_smwadmin_datarefreshprogress": "<strong>Eng Aktualiséierung ass schonn am <PERSON>aang.</strong>\nEt ass normal datt d'Aktualiséierung nëmme lues virugeet well Donnéeë nëmmen a klenge Päck aktualiséiert ginn an zwar ëmmer da wann ee Benotzer op dës Wiki zougräift.\nFir dës Aktualiséierung méi séier fäerdeg ze maache, kann de MediaWiki-Skript <code>runJobs.php</code> (benotzt d'Optioun <code>--maxjobs 1000</code> fir d'Zuel vun den Aktualiséierungen déi beienee gemaach ginn ze limitéieren).\nGeschate Fortschrëtt vun der aktueller Aktualiséierung:", "smw_smwadmin_datarefreshbutton": "Aktualiséiere vun den Date plangen", "smw_smwadmin_datarefreshstop": "Dësn Update stoppen", "smw_smwadmin_datarefreshstopconfirm": "<PERSON>, ech si {{GENDER:$1|sécher}}.", "smw-admin-support": "Ënnerstëtzung kréien", "smw-admin-supportdocu": "Verschidde Quelle kënnen Iech bei Problemer hëllefen:", "smw-admin-installfile": "<PERSON>n Dir Problemer bei der Installatioun hutt, da fänkt un andeem <PERSON>Direktiven am  <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">INSTALL Fichier</a>  an d'a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">Installatiouns-Säit</a> nokuckt.", "smw-admin-smwhomepage": "Déi komplett Benotzerdokumentatioun vu Semantic MediaWiki fannt Dir op <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "<PERSON>er kënnen op dem <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">'Issue tracker'</a>, op <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\"><PERSON>er mellen</a> fannt Dir Informatioune wéi dir dat effikass maache kënnt.", "smw-admin-questions": "<PERSON>n Dir nach Froen oder Propositiounen relativ zu Semantic MediaWiki hutt, da bedeelegt Iech un der Diskussioun op der <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">Semantic MediaWiki Benotzer-Mailinglëscht</a> oder am <a href=\"https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_chatroom\">Chatroom</a>.", "smw-admin-other-functions": "<PERSON><PERSON>", "smw-admin-statistics-extra": "Zousätzlech Statistiken", "smw-admin-statistics": "Statistiken", "smw-admin-supplementary-section-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-section-subtitle": "Ënnerstëtzt <PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-section-intro": "Verschidde Funktiounen déi an dësem Abschnitt opgezielt gi sinn eventuell limitéiert an dofir an dëser Wiki net disponibel.", "smw-admin-supplementary-settings-title": "Astellungen a Konfiguratioun", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "<PERSON>ell Statistiken", "smw-admin-supplementary-operational-statistics-intro": "<u>$1</u> weist eng erweidert Sammlung vu Statistiken", "smw-admin-supplementary-operational-statistics-cache-title": "<PERSON>ache-Statist<PERSON><PERSON>", "smw-admin-supplementary-operational-table-statistics-title": "Tabelle-Statistiken", "smw-admin-supplementary-operational-table-statistics-short-title": "Tabelle-Statistiken", "smw-admin-supplementary-elastic-version-info": "Versioun", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-functions": "Ënnerstëtzt <PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-settings-title": "Astellungen (indexen)", "smw-admin-supplementary-elastic-mappings-summary": "Resumé", "smw-admin-supplementary-elastic-nodes-title": "Kniet", "smw-admin-supplementary-elastic-indices-title": "Hiweiser", "smw-admin-supplementary-elastic-statistics-title": "Statistiken", "smw-admin-supplementary-elastic-status-refresh-interval": "Aktualisatiounsintervall: $1", "smw-admin-supplementary-elastic-replication-function-title": "Replikatioun", "smw-admin-supplementary-elastic-replication-files": "Fichieren", "smw-admin-supplementary-elastic-replication-pages": "Säiten", "smw-admin-supplementary-elastic-config": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_adminlinks_datastructure": "Datestruktur", "smw_adminlinks_displayingdata": "<PERSON><PERSON><PERSON><PERSON> weisen", "smw_adminlinks_inlinequerieshelp": "<PERSON><PERSON><PERSON><PERSON> fir intern <PERSON><PERSON><PERSON><PERSON>", "smw-property-indicator-type-info": "{{PLURAL:$1|Benotzer|System}} definéiert Eegenschaft", "smw-createproperty-isproperty": "Et ass eng Eegenschaft vum Typ $1.", "smw-paramdesc-category-template": "En<PERSON> fir d'Objeten ze formatéieren", "smw-paramdesc-category-userparam": "E Parameter deen der Schabloun iwwergi gëtt", "smw-info-par-message": "Message fir ze weisen.", "smw-info-par-icon": "Symbol fir entweder \"Info\" oder \"Warnung\" ze weisen.", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Allgemeng <PERSON>", "prefs-extended-search-options": "Erweidert Sich", "prefs-ask-options": "<PERSON><PERSON><PERSON><PERSON> sichen", "smw-prefs-general-options-disable-editpage-info": "Den Aféierungstext op der Ännerungssäit ausschalten", "smw-ui-tooltip-title-property": "Eegenschaft", "smw-ui-tooltip-title-quantity": "Ëmwandlung vun der <PERSON>heet", "smw-ui-tooltip-title-info": "Informatioun", "smw-ui-tooltip-title-service": "Service-Linken", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-error": "Feeler", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "Evenement", "smw-ui-tooltip-title-note": "Notiz", "smw-ui-tooltip-title-legend": "Erklärung", "smw-ui-tooltip-title-reference": "<PERSON><PERSON><PERSON><PERSON>", "smw-concept-cache-text": "D'Konzept huet am Ganzen {{PLURAL:$1|eng <PERSON>|$1 Säiten}} a gouf fir d'lescht den $2 ëm $3 aktualiséiert.", "smw_concept_header": "Säite vum Konzept \"$1\"", "smw_conceptarticlecount": "Déi $1 {{PLURAL:$1|Säit|Säite}} ginn hei drënner gewisen.", "restriction-level-smw-pageedit": "gespaart (nëmme berechtegt Benotzer)", "group-smwadministrator": "Administrateuren (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Administrateuren (Semantic MediaWiki)", "grouppage-smweditor": "{{ns:project}}:<PERSON><PERSON><PERSON> (Semantic MediaWiki)", "smw-property-predefined-default": "„$1“ ass eng virdefinéiert Eegenschaft vum Typ $2.", "smw-sp-properties-header-label": "Lëscht vun Eegenschaften", "smw-sp-admin-settings-button": "Lëscht vun den Astellunge generéieren", "smw-admin-idlookup-title": "<PERSON><PERSON><PERSON>", "smw-admin-idlookup-input": "<PERSON><PERSON>:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Iwwersiicht", "smw-admin-tab-maintenance": "Maintenance", "smw-admin-tab-alerts": "Warnungen", "smw-admin-alerts-tab-maintenancealerts": "Maintenance-Warnungen", "smw-admin-maintenancealerts-invalidentities-alert-title": "Ongülteg Entité<PERSON>n", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Astellungen", "smw-admin-configutation-tab-namespaces": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-maintenance-tab-tasks": "Aufgaben", "smw-admin-maintenance-no-description": "<PERSON><PERSON>.", "smw-livepreview-loading": "Lueden...", "smw-sp-searchbyproperty-resultlist-header": "Lë<PERSON><PERSON> vun de Resultater", "smw-editpage-annotation-disabled": "<PERSON><PERSON><PERSON> ass net fir semantesch Notifikatiounen am Text konfiguréiert duerch d'Limitatiounen an dësem Nummraum. Detailer wéi dat fir den Nummraum ageschalt ka gi fannt Dir op der Hëllefssäit fir d'[https://www.semantic-mediawiki.org/wiki/Help:Configuration Astellungen].", "smw-search-syntax": "Syntax", "smw-search-profile": "Erweidert", "smw-search-profile-sort-title": "Titel", "smw-search-profile-extended-help-query": "Link op:$1", "smw-search-profile-extended-help-query-link": "(fir méi <PERSON> $1).", "smw-search-profile-extended-section-sort": "Sortéieren no", "smw-search-profile-extended-section-namespace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-show": "<PERSON><PERSON>", "smw-search-hide": "Verstoppen", "log-name-smw": "Semantic-MediaWiki-Logbuch", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1-Import]]", "smw-datavalue-invalid-number": "\"$1\" kann net als Zuel interpretéiert ginn.", "smw-types-list": "Lëscht vun Datentypen", "smw-types-default": "\"$1\" ass en agebauten Datentyp.", "smw-types-help": "Méi Informatiounen a Beispiller fannt Dir op dëser [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 Hëllef-Säit].", "smw-type-tab-properties": "Eegenschaften", "smw-type-tab-types": "Typpen", "smw-type-tab-type-ids": "Typ-IDen", "smw-type-tab-errors": "Feeler", "smw-type-primitive": "<PERSON><PERSON><PERSON>", "smw-type-contextual": "Kontextuel", "smw-type-compound": "Zesummegesat", "smw-type-no-group": "Net klassifizéiert", "smw-limitreport-intext-parsertime-value": "$1  {{PLURAL:$1|Sekonn|Sekonnen}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|Sekonn|Sekonnen}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|Sekonn|Sekonnen}}", "smw-datavalue-time-invalid-date-components-common": "\"$1\" huet eng Informatioun déi net kann interpretéiert ginn.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" huet e puer eidel Komponenten", "smw-datavalue-time-invalid-date-components-three": "\"$1\" huet méi wéi dr<PERSON><PERSON>, déi fir d'Interpretatioun vun engem Datum obligatoresch sinn.", "smw-datavalue-time-invalid-ampm": "\"$1\" huet \"$2\" als Stonnenelement, dat ass fir eng 12-Stonne-Konventioun awer net valabel.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" ass keng valabel URL", "smw-parser-invalid-json-format": "Den JSON-<PERSON><PERSON><PERSON> huet mat engem \"$1\" geäntwert.", "smw-clipboard-copy-link": "Link an den Tëschespäicher kopéieren", "smw-data-lookup": "<PERSON><PERSON><PERSON><PERSON><PERSON> ofruffen...", "smw-data-lookup-with-wait": "D'Ufro g<PERSON>tt verschafft an et kann een Ament daueren.", "smw-no-data-available": "<PERSON><PERSON> disponibel.", "smw-edit-protection-enabled": "Gespaart fir z'änneren (Semantic MediaWiki)", "smw-format-datatable-emptytable": "An der Tabell si keng Donnéeën disponibel", "smw-format-datatable-lengthmenu": "<PERSON><PERSON> _MENU_ Elementer", "smw-format-datatable-loadingrecords": "Lueden...", "smw-format-datatable-processing": "Verschaffen...", "smw-format-datatable-search": "<PERSON><PERSON>:", "smw-format-datatable-zerorecords": "Et gouf keen esou en Enregistrement fonnt", "smw-format-datatable-first": "<PERSON><PERSON><PERSON>", "smw-format-datatable-last": "<PERSON><PERSON>", "smw-format-datatable-next": "Nächst", "smw-format-datatable-previous": "<PERSON><PERSON><PERSON>", "smw-format-datatable-toolbar-export": "Exportéieren", "smw-format-list-other-fields-open": "(", "smw-api-invalid-parameters": "<PERSON><PERSON><PERSON><PERSON><PERSON>, „$1“", "smw-property-reserved-category": "<PERSON><PERSON><PERSON>", "smw-category": "<PERSON><PERSON><PERSON>", "smw-filter": "Filter", "smw-section-expand": "Den Abschnitt opklappen", "smw-section-collapse": "Den Abschnitt zesummeklappen", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1] Format", "smw-help": "<PERSON><PERSON><PERSON><PERSON>", "smw-property-predefined-label-skey": "Sort<PERSON>iers<PERSON><PERSON><PERSON>", "smw-processing": "Verschaffen...", "smw-loading": "Lueden...", "smw-preparing": "Virbereeden...", "smw-expand": "Opklappen", "smw-collapse": "Zesummeklappen", "smw-copy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-jsonview-search-label": "<PERSON><PERSON>:", "smw-types-title": "Typ: $1", "smw-schema-error-title": "Validatioun {{PLURAL:$1|Feeler}}", "smw-schema-error-miscellaneous": "Onbestë<PERSON><PERSON> ($1)", "smw-schema-error-json": "JSON-Feeler: \"$1\"", "smw-schema-validation-schema-title": "JSON Schema", "smw-schema-summary-title": "Resumé", "smw-schema-title": "<PERSON><PERSON><PERSON>", "smw-schema-type": "Typ vu Schema", "smw-schema-type-description": "Typbesch<PERSON><PERSON>wung", "smw-schema-description": "Sc<PERSON>mabes<PERSON><PERSON><PERSON><PERSON>", "smw-schema-tag": "{{PLURAL:$1|Markéierung|Markéierungen}}", "smw-ask-title-keyword-type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sic<PERSON>", "smw-parameter-missing": "De Parameter \"$1\" feelt.", "smw-property-tab-usage": "Benotzung", "smw-property-tab-redirects": "Synonymmen", "smw-property-tab-subproperties": "Ënnereegenschaften", "smw-property-tab-specification": "... méi", "smw-concept-tab-list": "<PERSON><PERSON><PERSON><PERSON>", "smw-concept-tab-errors": "Feeler", "smw-ask-tab-result": "Resultat", "smw-ask-tab-code": "Code", "smw-pendingtasks-tab-setup": "Astellung", "smw-pendingtasks-setup-tasks": "Aufgaben", "smw-report": "Rapport", "smw-legend": "Erklärung", "smw-entity-examiner-deferred-elastic-replication": "Elastesch", "smw-entity-examiner-deferred-constraint-error": "Lim<PERSON><PERSON>ou<PERSON>", "smw-entity-examiner-associated-revision-mismatch": "Versioun", "smw-entity-examiner-deferred-fake": "Fälschung", "smw-indicator-revision-mismatch": "Rev<PERSON><PERSON><PERSON>", "smw-facetedsearch-intro-tab-search": "<PERSON><PERSON>", "smw-facetedsearch-input-filter-placeholder": "Filteren...", "smw-facetedsearch-no-filters": "<PERSON><PERSON>.", "smw-facetedsearch-clear-filters": "{{PLURAL:$1|Filter|Filteren}} zrécksetzen", "smw-search-placeholder": "Sichen...", "smw-listingcontinuesabbrev": "(Fortsetzung)", "smw-showingresults": "<PERSON>i g<PERSON>tt der  {{PLURAL:$1| '''1''' Resultat|'''$1''' Resultater}}, ugefaange mat #'''$2'''."}