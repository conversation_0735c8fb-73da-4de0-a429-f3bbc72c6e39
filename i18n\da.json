{"@metadata": {"authors": ["Cgtdk", "Christian List", "<PERSON><PERSON><PERSON>en", "<PERSON><PERSON><PERSON>", "<PERSON>", "Palnatoke", "<PERSON><PERSON><PERSON>", "Saederup92"]}, "smw-error": "<PERSON><PERSON><PERSON>", "smw-upgrade-progress": "Fremskridt", "smw-upgrade-error-title": "<PERSON><PERSON><PERSON>", "smw-upgrade-error-why-title": "Hvorfor ser jeg denne side?", "smw-upgrade-error-how-title": "<PERSON>vordan fikser jeg denne fejl?", "smw-extensionload-error-why-title": "Hvorfor ser jeg denne side?", "smw-extensionload-error-how-title": "<PERSON><PERSON><PERSON> retter jeg denne fejl?", "smw_viewasrdf": "RDF-nyhedskilde", "smw_finallistconjunct": ", og", "smw-factbox-head": "... mere om \"$1\"", "smw-factbox-facts": "<PERSON><PERSON><PERSON>", "smw-concept-cache-header": "Brug af cache", "smw-concept-no-cache": "Ingen tilgængelige cache.", "smw_concept_description": "Beskrivelse af konceptet \"$1\"", "smw_printername_csv": "CSV-eksport", "smw_printername_dsv": "DSV-eksport", "smw_printername_json": "JSON eksportering", "smw_printername_list": "Liste", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_template": "Skabelon", "smw_printername_templatefile": "Skabelon fil", "smw_printername_rdf": "RDF-eksport", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-limit": "Maksimalt antal resultater som skal returneres", "smw-paramdesc-offset": "Forskydning for det første resultat", "smw-paramdesc-dsv-filename": "Navnet på DSV filen", "smw-printername-feed": "RSS- og Atom-feed", "smw-paramdesc-feedtype": "Feedtype", "smw-label-feed-description": "$1 $2 feed", "smw_iq_moreresults": "… flere resultater", "smw_parseerror": "Opgivet værdi blev ikke forstået.", "smw_emptystring": "<PERSON><PERSON> strenge godtages ikke.", "smw_notinenum": "\"$1\" er ikke i listen ($2) over [[Property:Allows value|tilladte værdier]] for egenskaben \"$3\".", "smw_true_words": "sandt,s,ja,j", "smw_false_words": "<PERSON><PERSON>,f,nej,n", "smw_nofloat": "\"$1\" er ikke et tal.", "smw_infinite": "Tal der er lige så store som \"$1\" understøttes ikke.", "smw_novalues": "Ingen værdier angivet.", "smw_nodatetime": "<PERSON><PERSON><PERSON> \"$1\" blev ikke forstået.", "smw_type_header": "Egenskaber af typen \"$1\"", "smw_attribute_header": "Sider der bruger egenskaben \"$1\"", "smw-propertylist-subproperty-header": "Underegenskaver", "smw-propertylist-redirect-header": "Synonymer", "specialpages-group-smw_group-search": "Gennemse og søg", "exportrdf": "Eksporter sider til RDF", "smw_exportrdf_submit": "Eksportér", "uriresolver": "URI-løser", "properties": "Egenskaber", "smw-categories": "<PERSON><PERSON><PERSON>", "smw-sp-property-searchform": "Vis egenskaber der indeholder:", "smw-special-property-searchform": "Vis egenskaber der indeholder:", "smw-special-property-searchform-options": "<PERSON><PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "Ingen", "smw-special-wantedproperties-filter-unapproved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(e)", "concepts": "Koncepter", "smw-special-concept-header": "Liste over koncepter", "unusedproperties": "Ubrugte egenskaber", "smw-unusedproperty-template": "$1 af typen $2", "wantedproperties": "Efterspurgte egenskaber", "smw_purge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-purge-failed": "Genindlæsning mislykkedes", "types": "Typer", "smw-statistics-query-size": "St<PERSON>rrelse på efterspørgsel", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Koncept|Koncepter}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Koncept|Koncepter}}]]", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Datatype|Datatyper}}]]", "smw-statistics-delete-count-info": "<PERSON><PERSON><PERSON>, der er blevet markeret til fjernelse, skal bortskaffes regelmæssigt ved hjælp af de medfølgende vedligeholdelsesscripts.", "smw-ask-order-rand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Find resultater", "smw_ask_editquery": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "smw_ask_format_as": "Formater som:", "smw_ask_defaultformat": "standard", "smw-ask-delete": "<PERSON><PERSON><PERSON>", "smw-ask-format-options": "Format og indstillinger", "smw-ask-parameters": "Parametre", "smw-ask-search": "<PERSON><PERSON><PERSON>", "smw-ask-no-cache": "<PERSON><PERSON><PERSON><PERSON> kø <PERSON>", "smw-ask-result": "Resultat", "smw-ask-format": "Format", "smw-ask-format-change-info": "Formatet blev ændret og det er krævet for at køre forespørgslen igen for at matche nye parametre og visualiseringsindstillinger.", "searchbyproperty": "<PERSON><PERSON>g efter e<PERSON>b", "smw_sbv_property": "Egenskab:", "smw_sbv_value": "<PERSON><PERSON><PERSON>:", "smw_sbv_submit": "Find resultater", "browse": "Gennemse wiki", "smw_browselink": "Gennemgå egenskaber", "smw_browse_show_incoming": "Vis indkommende egenskaber", "smw_browse_hide_incoming": "Skjul indkommende egenskaber", "smw_browse_no_outgoing": "Denne side har ingen egenskaber.", "smw-browse-js-disabled": "Det er mistanke om, at JavaScript er deaktiveret eller ikke er tilgængelig, og vi kan anbefale at bruge en browser, hvor dette understøttes. <PERSON> muligheder findes måske på [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi $smwgBrowseByApi] indstillingssiden.", "smw-browse-show-group": "<PERSON>is grupper", "smw-browse-hide-group": "Skjul grupper", "smw_inverse_label_default": "$1 af", "smw_pp_from": "Fra side:", "smw_pp_type": "Egenskab:", "smw_pp_submit": "Find resultater", "smw-prev": "forrige {{PLURAL:$1|$1}}", "smw-next": "næste {{PLURAL:$1|$1}}", "smw_result_prev": "<PERSON><PERSON><PERSON>", "smw_result_next": "<PERSON><PERSON><PERSON>", "smw_result_results": "Resultater", "smw_result_noresults": "Ingen resultater.", "smw-admin-statistics-semanticdata-overview": "Oversigt", "smw_smwadmin_return": "Vend tilbage til $1", "smw-admin-environment": "Softwaremiljø", "smw-admin-announce": "Bekendtgør din wiki", "smw-admin-deprecation-notice-title-notice": "Kommende ændringer", "smw-admin-deprecation-notice-title-replacement": "Erstattede eller omdøbte indstillinger", "smw-admin-deprecation-notice-title-removal": "Fjernede indstillinger", "smw_smwadmin_datarefreshstop": "Stop denne opdatering", "smw_smwadmin_datarefreshstopconfirm": "Ja, jeg er {{GENDER:$1|sikker}}", "smw-admin-other-functions": "<PERSON>", "smw-admin-statistics": "Statistik", "smw-admin-supplementary-section-subtitle": "Understøttede kernefunktioner", "smw-admin-supplementary-operational-table-statistics-short-title": "tabel-statistikker", "smw-admin-supplementary-elastic-functions": "<PERSON><PERSON><PERSON><PERSON><PERSON> funktioner", "smw-admin-supplementary-elastic-settings-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-nodes-title": "<PERSON><PERSON>", "smw-admin-supplementary-elastic-statistics-title": "Statistik", "smw-admin-supplementary-elastic-replication-files": "Filer", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON>", "smw-list-count": "Listen indeholder $1 {{PLURAL:$1|element|elementer}}.", "smw_adminlinks_datastructure": "Datastruktur", "smw_adminlinks_displayingdata": "Datavisning", "smw-createproperty-isproperty": "Det er en egenskab af typen $1.", "smw-info-par-message": "Besked at vise.", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-property": "Egenskab", "smw-ui-tooltip-title-info": "Information", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-error": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "H<PERSON><PERSON>lse", "smw-ui-tooltip-title-note": "Bemærkning", "smw-ui-tooltip-title-legend": "Forklaring", "smw-ui-tooltip-title-reference": "<PERSON><PERSON><PERSON><PERSON>", "smw-sp-properties-header-label": "Liste over egenskaber", "smw-admin-idlookup-input": "Søg:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Oversigt", "smw-admin-configutation-tab-settings": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-configutation-tab-namespaces": "Navnerum", "smw-admin-maintenance-tab-tasks": "Opgaver", "smw-admin-maintenance-no-description": "Ingen beskrivelse.", "smw-livepreview-loading": "<PERSON><PERSON><PERSON><PERSON> …", "smw-sp-searchbyproperty-resultlist-header": "Liste over resultater", "smw-search-syntax": "Syntaks", "smw-search-profile": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-sort-title": "Titel", "smw-search-profile-extended-help-query": "Link til: $1", "smw-search-profile-extended-section-sort": "Sorter efter", "smw-search-profile-extended-section-form": "Formularer", "smw-search-profile-extended-section-namespace": "Navnerum", "smw-search-profile-extended-section-query": "Fores<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-link-caption-query": "se", "smw-search-show": "Vis", "smw-search-hide": "Skjul", "smw-types-list": "Liste over datatyper", "smw-types-default": "\"$1\" er en indbygget datatype.", "smw-type-tab-properties": "Egenskaber", "smw-type-tab-types": "Typer", "smw-type-tab-errors": "<PERSON><PERSON><PERSON>", "smw-type-primitive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" indeholder nogle tomme komponenter.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" er et ugyldigt link.", "smw-datavalue-parse-error": "Den opgivet værdi \"$1\" blev ikke forstået.", "smw-clipboard-copy-link": "Kopier link til udklipsholder", "smw-data-lookup": "Henter data ...", "smw-no-data-available": "Ingen tilgængelige data.", "smw-format-datatable-emptytable": "Ingen data tilgængelig i tabel", "smw-format-datatable-info": "Viser _START_ til _END_ af _TOTAL_ opslag", "smw-format-datatable-infoempty": "Viser 0 til 0 af 0 opslag", "smw-format-datatable-lengthmenu": "Vis _MENU_ opslag", "smw-format-datatable-loadingrecords": "Indslæser...", "smw-format-datatable-processing": "Bearbejder...", "smw-format-datatable-search": "Søg:", "smw-format-datatable-first": "<PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-last": "Sidste", "smw-format-datatable-next": "<PERSON><PERSON><PERSON>", "smw-format-datatable-previous": "<PERSON><PERSON><PERSON>", "smw-format-datatable-toolbar-export": "Eksportér", "smw-format-list-other-fields-open": "(", "smw-api-invalid-parameters": "Ugyldige parametre, \"$1\"", "smw-property-reserved-category": "<PERSON><PERSON><PERSON>", "smw-category": "<PERSON><PERSON><PERSON>", "smw-filter": "Filter", "smw-section-expand": "<PERSON><PERSON><PERSON>", "smw-section-collapse": "<PERSON><PERSON>", "smw-help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Snyde ark", "smw-personal-jobqueue-watchlist": "Jobkø-overvågningsliste", "smw-processing": "Bearbejder ...", "smw-loading": "<PERSON><PERSON><PERSON><PERSON> …", "smw-fetching": "Henter ...", "smw-preparing": "Forbereder ...", "smw-expand": "Fold ud", "smw-collapse": "<PERSON>old sammen", "smw-copy": "<PERSON><PERSON><PERSON>", "smw-types-title": "Type: $1", "smw-schema-error-title": "Valideringsfejl", "smw-schema-error-json": "JSON-fejl: \"$1\"", "smw-schema-validation-schema-title": "JSON-skema", "smw-schema-type": "Type", "smw-schema-tag": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON>}}", "smw-parameter-missing": "Parameteren \"$1\" mangler.", "smw-property-tab-redirects": "Synonymer", "smw-property-tab-subproperties": "Underegenskaber", "smw-property-tab-specification": "... mere", "smw-concept-tab-list": "Liste", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-ask-tab-result": "Resultat", "smw-ask-tab-extra": "Ekstra", "smw-ask-tab-code": "<PERSON><PERSON>", "smw-listingcontinuesabbrev": "forts.", "smw-showingresults": "Nedenfor vises <b>$1</b> {{PLURAL:$1|resultat|resultater}} startende med nummer <b>$2</b>."}