{"@metadata": {"authors": ["Cedric31", "ChrisPtDe"]}, "smw_viewasrdf": "Flux RDF", "smw_finallistconjunct": " et", "smw_concept_description": "Dèscripcion du concèpte « $1 »", "version-semantic": "Èxtensions sèmantiques", "smw_printername_count": "Comptar los rèsultats", "smw_printername_csv": "èxportacion en CSV", "smw_printername_dsv": "Èxportacion en DSV", "smw_printername_debug": "Requéta d’èliminacion de les cofieries (por los èxpèrts)", "smw_printername_embedded": "Entrebetar lo contegnu de les pâges", "smw_printername_json": "èxportacion en JSON", "smw_printername_list": "Lista", "smw_printername_ol": "Ènumèracion", "smw_printername_ul": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_table": "Grelye", "smw_printername_broadtable": "Trâbla lârge", "smw_printername_template": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_rdf": "Èxportacion RDF", "smw_printername_category": "Catè<PERSON><PERSON>", "smw-paramdesc-link": "<PERSON>rar les valors coment lims", "smw-paramdesc-sep": "Lo <PERSON>è<PERSON>ator de les valors", "smw-paramdesc-embedonly": "Pas fâre vêre les en<PERSON>t<PERSON>", "smw-paramdesc-rdfsyntax": "La sintaxa RDF a utilisar", "smw-paramdesc-csv-sep": "Lo sèparator a utilisar", "smw-paramdesc-dsv-separator": "Lo sèparator a utilisar", "smw-paramdesc-dsv-filename": "Lo nom du fichiér DSV", "smw-paramdesc-searchlabel": "Lo tèxto du lim de vers los rèsultats", "smw_iq_moreresults": "&hellip; <PERSON><PERSON><PERSON>", "smw_true_words": "veré,v,ouè,o", "smw_false_words": "fôx,f,nan,n", "smw_nofloat": "« $1 » est pas un nombro.", "smw_novalues": "Gins de valor spècefiâ.", "smw_type_header": "Propriètâts de tipo « $1 »", "smw_typearticlecount": "<PERSON><PERSON><PERSON> vêre l{{PLURAL:$1|a propriètât qu’utilise|es $1 propriètâts qu’utilisont}} ceti tipo.", "smw_attribute_header": "Pâges qu’utilisont la propriètât « $1 »", "smw_attributearticlecount": "<PERSON><PERSON><PERSON> vêre l{{PLURAL:$1|a pâge qu’utilise|es $1 pâges qu’utilisont}} ceta propriètât.", "exportrdf": "Èxportar des pâges en RDF", "smw_exportrdf_submit": "Èxportar", "uriresolver": "Rèsolvior d’URI", "properties": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-categories": "Catègories", "smw_property_template": "$1 du tipo $2 ($3)", "unusedproperties": "Propriètâts inutilisâs", "smw-unusedproperty-template": "$1 de tipo $2", "wantedproperties": "Propri<PERSON><PERSON><PERSON><PERSON>", "smw-wantedproperty-template": "$1 ($2 usâjo{{PLURAL:$2||s}})", "smw_purge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "types": "Tipos", "ask": "Rechèrche <PERSON>", "smw_ask_sortby": "<PERSON>y<PERSON>r per colones (u chouèx)", "smw_ask_ascorder": "<PERSON>rès<PERSON>", "smw_ask_descorder": "Dècrèssent", "smw_ask_submit": "Trovar des rèsultats", "smw_ask_editquery": "<PERSON><PERSON><PERSON><PERSON> la requéta", "smw_add_sortcondition": "[Apondre les condicions de tri]", "smw_ask_hidequery": "Cachiér la requéta", "smw_ask_help": "Éde a la requéta", "smw_ask_queryhead": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_printhead": "<PERSON><PERSON><PERSON>s de ples a fâre vêre", "smw_ask_printdesc": "(apondre yon nom de propriètât per legne)", "smw_ask_format_as": "Formatar en :", "smw_ask_defaultformat": "per d<PERSON><PERSON><PERSON><PERSON>", "smw_ask_otheroptions": "Ôtros chouèx", "smw_ask_show_embed": "<PERSON><PERSON><PERSON> vêre lo code entrebetâ", "smw_ask_hide_embed": "Cachiér lo code entrebetâ", "smw-ask-delete": "[Suprimar]", "searchbyproperty": "Rechèrchiér per propri<PERSON>", "smw_sbv_property": "Propriè<PERSON><PERSON><PERSON> :", "smw_sbv_value": "Valor :", "smw_sbv_submit": "Trovar des rèsultats", "browse": "Navegar lo vouiqui", "smw_browselink": "Navegar les propriètâts", "smw_browse_go": "Emmodar", "smw_browse_show_incoming": "fâre vêre les propriètâts que pouentont ique", "smw_browse_hide_incoming": "cachiér les propriètâts que pouentont ique", "smw_browse_no_outgoing": "<PERSON><PERSON> pâge at gins de propriètât.", "smw_browse_no_incoming": "<PERSON><PERSON> proprièt<PERSON>t pouente vers ceta pâge.", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "Lambél de la propriètât a l’envèrsa", "pageproperty": "Rechèrche dens les propriètâts de la pâge", "smw_pp_from": "De la pâge", "smw_pp_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_pp_submit": "Trovar des rèsultats", "smw_result_prev": "<PERSON>ant", "smw_result_next": "Aprés", "smw_result_results": "Rèsultats", "smw_result_noresults": "<PERSON><PERSON><PERSON><PERSON>, gins de rèsultat.", "smw_smwadmin_return": "Tornar a $1", "smw-admin-dbbutton": "Inicialisar ou ben betar a nivél les trâbles", "smw-admin-announce": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> vouiqui", "smw_smwadmin_datarefresh": "Rèparacion et misa a nivél de les balyês", "smw_smwadmin_datarefreshbutton": "Comenci<PERSON><PERSON> la misa a jorn de les balyês", "smw_smwadmin_datarefreshstop": "<PERSON><PERSON><PERSON><PERSON> ceta misa a jorn", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON><PERSON>, je nen su de sûr.", "smw-admin-support": "Avê<PERSON> d<PERSON>assistance", "smw_adminlinks_datastructure": "Structura de les balyês", "smw_adminlinks_displayingdata": "Visualisacion de les balyês", "smw_adminlinks_inlinequerieshelp": "Éde sur les requétes entrebetâs", "smw-createproperty-isproperty": "Ceta propriètât est de tipo « $1 ».", "smw-info-par-message": "Mèssâjo a fâre vêre.", "smw_concept_header": "Pâges du concèpte « $1 »", "smw_conceptarticlecount": "<PERSON><PERSON><PERSON> vêre l{{PLURAL:$1|a pâge que repôse|es $1 pâges que repôsont}} sur cél concèpte.", "smw-livepreview-loading": "Chargement...", "smw-listingcontinuesabbrev": "(suita)", "smw-showingresults": "Afichâjo ce-desot de tant qu’a <strong>$1</strong> rèsultat{{PLURAL:$1||s}} dês lo nº <strong>$2</strong>."}