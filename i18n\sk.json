{"@metadata": {"authors": ["Helix84", "Kghbln", "Luky001", "TomášPolonec", "Yardom78", "아라"]}, "smw-desc": "Sprístupnenie vašej wiki pre stroje ''aj'' ľudí ([https://www.semantic-mediawiki.org/wiki/Help:User_manual dokumentácia])", "smw_viewasrdf": "RDF kanál", "smw_finallistconjunct": " a", "smw_isspecprop": "T<PERSON>to vlastnosť je špeciálna vlastnosť na tejto wiki.", "smw_concept_description": "Popis pojmu „$1”", "smw_no_concept_namespace": "Pojmy je možné definovať iba na stránkach v mennom priestore Concept:", "smw_multiple_concepts": "Každý pojem môže mať iba jednu definíciu pojmu.", "smw_concept_cache_miss": "Pojem „$1” nemožno momentálne použiť, pretože konfigurácia wiki vyžaduje, aby sa vypočítal, keď wiki nebude pripojená. Ak problém po určitej dobe nezmizne, pož<PERSON>dajte správcu, aby tento pojem sprístupnil.", "smw_noinvannot": "Hodnoty nemožno priradiť inverzným vlastnostiam.", "smw_baduri": "<PERSON><PERSON><PERSON><PERSON><PERSON>, URI z rozsahu \"$1\" na tomto mieste nie sú dostupné.", "smw_printername_count": "Výsledky počítania", "smw_printername_csv": "export CSV", "smw_printername_debug": "Ladiaca <PERSON> (pre expertov)", "smw_printername_embedded": "Vložiť obsah stránky", "smw_printername_json": "export JSON", "smw_printername_list": "Zoznam", "smw_printername_ol": "Vymenovaný typ", "smw_printername_ul": "Rozpoložkovanie", "smw_printername_table": "Tabuľka", "smw_printername_broadtable": "Široká tabuľka", "smw_printername_template": "Šablóna", "smw-paramdesc-limit": "Maximálny počet vrátených výsledov", "smw-paramdesc-headers": "Zobrazovať hlavičky/názvy vlastností", "smw-paramdesc-mainlabel": "Označenie názvu hlavnej stránky", "smw-paramdesc-link": "Zobrazovať hodnoty ako odkazy", "smw-paramdesc-intro": "Text, ktorý sa má zobrazovať pred výsledkami požiadavky (nepovinnné)", "smw-paramdesc-outro": "Text, ktorý sa má zobrazovať po výsledkoch požiadavky (nepovinnné)", "smw-paramdesc-default": "Text, ktorý sa má zobrazovať ak požiadavka nevráti žiadne výsledky", "smw-paramdesc-sep": "Oddeľovač medzi výsledkami", "smw-paramdesc-template": "Názov šablóny pomocou ktorej zobrazovať výtlačky", "smw-paramdesc-columns": "Počet stĺpcov, v ktorých zobrazovať výsledky", "smw-paramdesc-embedformat": "Značka HTML na definíciu nadpisov", "smw-paramdesc-embedonly": "Nezobrazovať nadpisy", "smw-paramdesc-searchlabel": "Text pre pokračovanie vo vyhľadávaní", "smw_iq_disabled": "Prepáčte. Inline queries have been disabled for this wiki.", "smw_iq_moreresults": "&hellip; <PERSON><PERSON><PERSON><PERSON>ky", "smw_parseerror": "Zadaná hodnota nebola pochopená.", "smw_notitle": "„$1“ nie je možné na tejto wiki použiť ako názov stránky.", "smw_wrong_namespace": "Sú tu povolené iba stránky v mennom priestore „$1”.", "smw_manytypes": "Pre atribút bol definovaný viac ako jeden typ.", "smw_emptystring": "Prázdne reťazcie nie sú akceptované.", "smw_notinenum": "„$1“ nie je v zozname [[Property:Allows value|možných hodnôt]] ($2) pre vlastnosť „$3“.", "smw_noboolean": "\"$1\" nebolo rozpoznané ako platná hodnota typy boolean (áno/nie).", "smw_true_words": "<PERSON><PERSON>,true", "smw_false_words": "nie,false", "smw_nofloat": "\"$1\" nie je číslo s plávajúcou desatinnou č<PERSON>u.", "smw_infinite": "Čísla také veľké ako „$1“ nie sú podporované.", "smw_nodatetime": "Nevedel som interpretovať dátum \"$1\".", "smw_toomanyclosing": "Zdá sa, že požiadavka obsahuje pr<PERSON><PERSON>š mnoho výskytov „$1“.", "smw_noclosingbrackets": "Niektoré použitie „<nowiki>[[</nowiki>” vo vašej požiadavke nebolo ukončené zodpovedajúcim „]]”.", "smw_misplacedsymbol": "Symbol „$1“ bol použitý na mieste, kde nemá význam.", "smw_unexpectedpart": "Časť požiadavky „$1“ nebola pochopená.\nVýsledky nemusia byť podľa očakávaní.", "smw_emptysubquery": "Niektorá subpožiadavka nemá platný stav.", "smw_misplacedsubquery": "Niektorá subpožiadavka bola použitá na mieste, kde nie sú povolené subpožiadavky.", "smw_valuesubquery": "Podpožiadavky nie sú podporované pre hodnoty vlastnosti „$1“.", "smw_badqueryatom": "Niektorá časť „<nowiki>[[…]]</nowiki>“ nebola pochopená.", "smw_propvalueproblem": "Hodnota vlastnosti „$1“ nebola pochopená.", "smw_noqueryfeature": "Táto wiki nepodporuje istú časť požiadavky a jej časť bola ignorovaná ($1).", "smw_noconjunctions": "Táto wiki nepodporuje konjunkcie v požiadavkách a časť požiadavky bola ignorovaná ($1).", "smw_nodisjunctions": "Disjunkcie nie sú v požiadavkách na tejto wiki podporované a časť požiadavky bola ignorovaná ($1).", "smw_querytoolarge": "{{PLURAL:$2|Nasledovná podmienka|Nasledovné $2 podmienky|Nasledovných $2 podmienok}} požiadavky nebudú zohľadnené z dôvodu obmedzení tejto wiki na dĺžku alebo hĺbku požiadavky: <code>$1</code>.", "smw_notemplategiven": "Aby tento formát požiadavky fungoval, poskytnite prosím hodnotu parametra „template”.", "smw_type_header": "Vlastnosti typu „$1“", "smw_typearticlecount": "{{PLURAL:$1|Zobrazuje sa $1 vlastnosť|Zobrazujú sa $1 vlastnosti|Zobrazuje sa $1 vlastností}} tohto typu.", "smw_attribute_header": "Stránky používajúce vlastnosť „$1“", "smw_attributearticlecount": "Zobrazuje sa $1 {{PLURAL:$1|stránka|str<PERSON><PERSON>|str<PERSON>ok}}, ktor<PERSON> p<PERSON> túto vlastnosť.", "specialpages-group-smw_group-search": "Prehliadať a hľadať", "exportrdf": "Exportovať stránky do RDF", "smw_exportrdf_docu": "<PERSON><PERSON>to stránka vám umožňuje exportovať časti stránok do formátu RDF. Po zadaní názvov stránok do spodného textového poľa, jeden názov na riadok, môžete exportovať stránky.", "smw_exportrdf_recursive": "Rekurzívne exportovať všetky súvisiace stránky. <PERSON><PERSON>, výsledok môže byť veľmi veľký!", "smw_exportrdf_backlinks": "Tieť exportovať v<PERSON><PERSON><PERSON> str<PERSON>, ktoré odkazujú na exportované stránky. Vytvorí prehliadateľné RDF.", "smw_exportrdf_lastdate": "Neexportova<PERSON>, k<PERSON><PERSON> neboli zmenené od zadaného času.", "smw_exportrdf_submit": "Export", "uriresolver": "Prekladač URI", "properties": "V<PERSON><PERSON>ti", "smw-categories": "Kategórie", "smw_properties_docu": "Na tejto wiki sa používajú nasledovné vlastnosti.", "smw_property_template": "$1 typu $2 ($3 {{PLURAL:$3|použitie|použitia|použití}})", "smw_propertylackspage": "<PERSON><PERSON><PERSON><PERSON> vlastnosti by mali ma<PERSON> popisnú stránku!", "smw_propertylackstype": "Pre túto vlastnosť nebol uvedený žiadny typ (nateraz sa predpokladá typ $1).", "smw_propertyhardlyused": "Táto vlastnosť sa na wiki takmer nepoužíva!", "unusedproperties": "Nepoužité vlastnosti", "smw-unusedproperties-docu": "<PERSON><PERSON><PERSON> strán<PERSON> obsahuje [https://www.semantic-mediawiki.org/wiki/Unused_properties nepoužívané vlastnosti], ktoré sú deklarované napriek tomu, že ich žiadna iná stránka nepoužíva. Pre porovnanie si pozrite špeciálne stránky obsahujúce [[Special:Properties|všetky]] alebo [[Special:WantedProperties|žiadané vlastnosti]].", "smw-unusedproperty-template": "$1 typu $2", "wantedproperties": "Ž<PERSON><PERSON><PERSON>", "smw-wantedproperties-docu": "<PERSON><PERSON><PERSON> strán<PERSON> obsahuje [https://www.semantic-mediawiki.org/wiki/Unused_properties žiadané vlastnosti], ktoré sú na wiki používané, ale nemajú stránku, kde by boli popísané. Pre porovnanie si pozrite špeciálne stránky obsahujúce [[Special:Properties|všetky]] alebo [[Special:WantedProperties|nepoužívané vlastnosti]].", "smw-wantedproperty-template": "$1 (použité {{PLURAL:$2|raz|$2-krát}})", "smw_purge": "Obnoviť", "types": "<PERSON><PERSON>", "smw_types_docu": "Zoznam [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes dostupných údajových typov], kde ka<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Datatype typ] reprezentuje unikátnu sadu atribútov na opis hodnoty z hľadiska uloženia a vlastností zobrazenia, ktoré sú pre pridelenú vlastnosť dedičné.", "smw_uri_doc": "URI resolver sa stará o implementáciu [$1 W3C TAG hľadanie na httpRange-14].\nStará sa o to, aby sa ľudia nestali webstránkami.", "ask": "<PERSON><PERSON><PERSON><PERSON>é v<PERSON>ľadávanie", "smw_ask_sortby": "Zoradiť podľa stĺpca", "smw_ask_ascorder": "Vzostupne", "smw_ask_descorder": "Zostupne", "smw_ask_submit": "Nájdi v<PERSON>dky", "smw_ask_editquery": "Upraviť požiadavku", "smw_add_sortcondition": "[Pridať podmienku na zoradenie]", "smw_ask_hidequery": "Skryť požiadavku (kompaktné zobrazenie)", "smw_ask_help": "Pomocník pre požiadavky", "smw_ask_queryhead": "Podmienka", "smw_ask_printhead": "Výber výpisu", "smw_ask_printdesc": "(pridajte jednu vlastnosť na riadok)", "smw_ask_format_as": "Formátovať ako:", "smw_ask_defaultformat": "štandardne", "smw_ask_otheroptions": "Ďalšie mo<PERSON><PERSON>ti", "smw_ask_show_embed": "Zobraziť kód embed", "smw_ask_hide_embed": "Skry<PERSON> kód embed", "smw_ask_embed_instr": "Ak chcete vložiť túto požiadavku do wiki stránky, použite dolu uvedený kód.", "searchbyproperty": "Hľadať podľa hodnoty atribútu", "smw_sbv_docu": "Hľadať na wiki článok, ktorý má atribút s istou hodnotou.", "smw_sbv_novalue": "Nebola poskytnutá hodnota. Prosím, poskytnite ju vo formulári alebo zobrazte všetky atribúty typu $1", "smw_sbv_displayresultfuzzy": "Zoznam v<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> ma<PERSON> „$1” s hodnotou „$2”.\nKeď<PERSON><PERSON> v<PERSON>dkov bolo len ni<PERSON>, zobrazujú sa aj blízke hodnoty.", "smw_sbv_property": "Atribút:", "smw_sbv_value": "Hodnota:", "smw_sbv_submit": "Hľadať výsledky", "browse": "Prehliadať wiki", "smw_browselink": "Prehliadať vlastnosti", "smw_browse_article": "Zadaj<PERSON> ná<PERSON>v <PERSON>, od k<PERSON>j chcete začať prehliadať.", "smw_browse_go": "Vykonať", "smw_browse_show_incoming": "Zobraziť prichádzajúce vlastnosti", "smw_browse_hide_incoming": "Skryť prichádzajúce vlastnosti", "smw_browse_no_outgoing": "<PERSON><PERSON><PERSON> s<PERSON>.", "smw_browse_no_incoming": "Žiadne vlastnosti neodkazujú na túto stránku.", "smw_inverse_label_default": "$1 z", "smw_inverse_label_property": "Označenie inverznej vlastnosti", "pageproperty": "Hľadanie vlastností stránky", "smw_pp_docu": "Buď zadajte stránku a vlastnosť, alebo iba vlastnosť pre načítanie všetkých priradených hodnôt.", "smw_pp_from": "<PERSON><PERSON>:", "smw_pp_type": "Vlastnosť:", "smw_pp_submit": "Výsledky hľadania", "smw_result_prev": "Späť", "smw_result_next": "Ďalej", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "Pre<PERSON><PERSON>č<PERSON>, žiadne výsledky.", "smwadmin": "Panel (Sémantická MediaWiki)", "smw-admin-setupsuccess": "Úložisko bolo nastavené.", "smw_smwadmin_return": "Späť na $1", "smw_smwadmin_updatestarted": "Začal sa nový proces aktualizácie sémantických údajov.\nVšetky uložené údaje budú znova zostavené alebo opravené tam, kde je to potrebné.\nNa tejto špeciálnej stránke môžete sledovať priebeh aktualizácie.", "smw_smwadmin_updatenotstarted": "Proces aktualizácie už beží.\nĎalší sa nespustí.", "smw_smwadmin_updatestopped": "Všetky bežiace procesy aktualizácie boli zastavené.", "smw_smwadmin_updatenotstopped": "Bežiaci proces aktualizácie môžete zastaviť označením zaškrtávacieho poľa, <PERSON><PERSON><PERSON> potvrdí<PERSON>, že ste si naozaj istý.", "smw-admin-docu": "T<PERSON>to <PERSON>iálna stránka vám pomôže počas inštalácie, aktualizácie, údr<PERSON><PERSON> a používania <a href=\"http://semantic-mediawiki.org\">Sémantickej MediaWiki</a>. Nájdete v nej tiež ďalšie správcovské funkcie, úlohy a štatistiky. Pred spustením správcovských funkcií nezabudnite zálohovať dôležité dáta.", "smw-admin-db": "Nastavenie databázy", "smw-admin-dbdocu": "Semantic MediaWiki vyžaduje svoju vlastnú databázovú štruktúru (a je nezávislá na MediaWiki, čiže neovplyvňuje zvyšok inštalácie MediaWiki), aby molo ukladať sémantické údaje.\nFunkcia nižšie zabezpečuje správne nastavenie databázy.\nTúto funkcia nastavenia možno vykonať viacnásobne bez akejkoľvek ujmy, ale je potrebná iba raz počas inštalácie či aktualizácie.", "smw-admin-permissionswarn": "Ak táto operácia zlyhá na chybách SQL, používateľ databázy, ktorého využíva vaša wiki (pozrite sa do LocalSettings.php) pravdepodobne nemá dostatočné privilégiá.\nBuď udeľte tomuto používateľovi dodatočné privilégiá na vytváranie a mazanie tabuliek, dočasne zadajte prihlasovacie údaje používateľa root databázy do LocalSettings.php alebo použite skript na údržbu <code>setupStore.php</code>, ktorý dokáže využiť administrátorské oprávnenie.", "smw-admin-dbbutton": "Inicializovať alebo aktualizovať tabuľky", "smw-admin-announce": "Oznámiť vašu wiki", "smw_smwadmin_datarefresh": "Nové vytvorenie d<PERSON>", "smw_smwadmin_datarefreshdocu": "Je možné obnoviť všetky údaje Semantic MediaWiki na základe aktuálneho obsahu wiki.\nTo sa hodí na opravu poškodených údajov alebo obnovu údajov ak sa pri aktualizácii softvéru zmenil vnútorný formát ukladania údajov.\nAktualizáciu je možné spustiť na špeciálnej stránke a nebude dokočená okamžite.\nTu sa zobrazuje priebeh aktualizácie a môžete tu spustiť alebo zastaviť aktualizácie (ak túto funkciu správca nevypol).", "smw_smwadmin_datarefreshprogress": "<strong>Aktualizácia už prebieha.</strong>\n<PERSON>, že aktualizácie prebieha pomaly, pretože obnovuje údaje iba po malých kúskoch naraz, keď používateľ zobrazí stránku wiki.\nRýchlejšie dokončenie tejto aktualizácie môžete dosiahnuť vyvolaním údržbového skriptu MediaWiki <code>runJobs.php</code> (použite voľbu <code>--maxjobs 1000</code> na obmedzenie počtu aktualizácií v jednej dávke).\nOdhadovaný priebeh aktualizácie:", "smw_smwadmin_datarefreshbutton": "Naplánovať znovunastavenie dát", "smw_smwadmin_datarefreshstop": "Zastaviť túto aktualizáciu", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, som si {{GENDER:$1|istý|istá}}.", "smw-admin-support": "Získanie podpory", "smw-admin-supportdocu": "V prípade problémov vám môžu pomôcť rozličné zdroje:", "smw-admin-installfile": "Ak budete mať s inštal<PERSON><PERSON>u problém, prečítajte si návod k inštalácii v <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">s<PERSON>bor<PERSON> INSTALL </a> a na <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">inštalačnej stránke</a>.", "smw-admin-smwhomepage": "Úplná používateľská dokumentácia Sémantickej MediaWiki sa nachádza na <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Chyby môžete oznamovať v <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">zápisn<PERSON><PERSON> chýb</a>, str<PERSON><PERSON> <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">Hlásenie chýb</a> poskytuje návod ako efektívne napísať hlásenie chyby.", "smw-admin-questions": "Pokiaľ máte ďalšie otázky alebo návrhy, pridajte sa do diskusie v <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">mailing liste používateľov</a> Sémantickej MediaWiki.", "smw_adminlinks_datastructure": "Štrukt<PERSON><PERSON>", "smw_adminlinks_displayingdata": "Zob<PERSON><PERSON>", "smw_adminlinks_inlinequerieshelp": "Pomoc k inline požiadavkám", "smw-createproperty-isproperty": "Je to vlastnosť typu $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Povolená hodnota|Povolené hodnoty}} tejto vlastnosti {{PLURAL:$1|je|sú}}:", "smw_unknowntype": "Typ „$1“ tejto vlastnosti je neplatný", "smw_concept_header": "Návrh „$1”", "smw_conceptarticlecount": "Nižšie {{PLURAL:$1|zobrazená jedna stránka|zobrazené $1 stránky|zobrazených $1 stránkok}}.", "smw-property-predefined-askpa": "„$1“ je predefinovaná vlastnosť popisujúca parametre, ktor<PERSON> ovplyvňuj<PERSON> výsledok dopytu, a poskytuje ju [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-livepreview-loading": "Načítava sa…", "smw-listingcontinuesabbrev": "<PERSON><PERSON><PERSON><PERSON>.", "smw-showingresults": "Nižšie {{PLURAL:$1|je zobrazený jeden výsledok|sú zobrazené '''1''' výsledky|je zobrazených '''$1''' vý<PERSON><PERSON><PERSON>}}, počnúc od  #<b>$2</b>."}