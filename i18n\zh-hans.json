{"@metadata": {"authors": ["A Chinese Wikipedian", "A Retired User", "A2093064", "Anakmalaysia", "Angrydog001", "CXuesong", "Deathkon", "<PERSON>", "Eduardoad<PERSON>", "FakeGreenHand", "Func", "Great Brightstar", "GuoPC", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hanyanbo98", "Hehua", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Lakejason0", "Li3939108", "<PERSON><PERSON>", "Linforest", "LittlePaw365", "Liuxinyu970226", "<PERSON><PERSON><PERSON>", "LowensteinYang", "McDut<PERSON><PERSON>", "NekoCharm", "Nemo bis", "NeverBehave", "Onecountry", "<PERSON><PERSON><PERSON>", "PhiLiP", "<PERSON>", "SaoMikoto", "<PERSON><PERSON><PERSON>", "SolidBlock", "<PERSON><PERSON>", "StarHeart", "Stieizc", "Tiger", "<PERSON>r jason", "Wxyveronica", "<PERSON><PERSON><PERSON>", "Xiplus", "Xzonn", "Yanmiao liu", "Yfdyh000", "Zazzzz", "<PERSON><PERSON><PERSON>", "予弦", "列维劳德", "夢蝶葬花", "淮南皓月", "神樂坂秀吉", "科劳", "落花有意12138", "铁桶", "阿pp", "아라"]}, "smw-desc": "让机器与人类都能更轻松地访问wiki（[https://www.semantic-mediawiki.org/wiki/Help:User_manual/zh-hans 在线文档]）", "smw-error": "错误", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ 语义MediaWiki]已安装并启用，但缺少适当的[https://www.semantic-mediawiki.org/wiki/Help:Upgrade 升级密钥]。", "smw-upgrade-release": "发行", "smw-upgrade-progress": "进度", "smw-upgrade-progress-explain": "升级何时完成较难预测，因为这取决于数据存储库的大小和可用的硬件，对于较大的wiki，可能需要一定时间才能完成。\n\n请联系您的本地管理员获取更多有关进度的信息。", "smw-upgrade-progress-create-tables": "正在创建（或更新）表格和索引...", "smw-upgrade-progress-post-creation": "正在运行创建后任务...", "smw-upgrade-progress-table-optimization": "正在运行表格优化...", "smw-upgrade-progress-supplement-jobs": "正在添加补充工作...", "smw-upgrade-error-title": "错误 » 语义MediaWiki", "smw-upgrade-error-why-title": "我为何会看到这个页面？", "smw-upgrade-error-why-explain": "语义MediaWiki的内部数据库结构发生更改，需要进行一些调整才能完全正常运行。原因可能有以下几种：\n* 添加了额外的固定属性（需要额外的表格设置）\n* 升级含有对表格或索引的某些更改，因此在访问数据之前必须进行拦截\n* 存储或查询引擎发生更改", "smw-upgrade-error-how-title": "我该如何修复这个错误？", "smw-upgrade-error-how-explain-admin": "管理员（或任何具有管理员权限的人）必须运行MediaWiki的 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php]或语义MediaWiki的[https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php]维护脚本。", "smw-upgrade-error-how-explain-links": "您还可以查阅以下页面获取更多帮助：\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation 安装]说明\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting 故障排除]帮助页面", "smw-extensionload-error-why-title": "我为何会看到这个页面？", "smw-extensionload-error-why-explain": "本扩展<b>不是</b>使用<code>enableSemantics</code>加载的，而是通过其他方式启用的，例如直接使用<code>wfLoadExtension( 'SemanticMediaWiki' )</code>。", "smw-extensionload-error-how-title": "我该如何修复这个错误？", "smw-extensionload-error-how-explain": "要在启用扩展时避免命名空间声明和待处理配置的相关问题，必须使用<code>enableSemantics</code>来确保在通过<code>ExtensionRegistry</code>加载扩展之前设置好必需变量。\n\n更多帮助请查阅[https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics]帮助页面。", "smw-upgrade-maintenance-title": "维护 » 语义MediaWiki", "smw-upgrade-maintenance-why-title": "我为何会看到这个页面？", "smw-upgrade-maintenance-note": "系统目前正在进行[https://www.semantic-mediawiki.org/ 语义MediaWiki]扩展及其数据存储库的[https://www.semantic-mediawiki.org/wiki/Help:Upgrade 升级]。我们恳请您耐心等待维护完成，之后即可再次访问本wiki。", "smw-upgrade-maintenance-explain": "扩展通过把大部分维护任务推迟到<code>update.php</code>之后来尽量减少影响和停机时间，但某些数据库相关更改必需先行完成，以避免数据不一致。这可能包括：\n* 更改表格结构，如添加新字段或修改现有字段\n* 更改或添加表格索引\n* 运行表格优化（当启用时）", "smw-semantics-not-enabled": "此wiki未启用语义MediaWiki功能。", "smw_viewasrdf": "RDF信息源", "smw_finallistconjunct": "和", "smw-factbox-head": "...“$1”的更多信息", "smw-factbox-facts": "事实", "smw-factbox-facts-help": "展示用户创建的陈述和事实", "smw-factbox-attachments": "附件", "smw-factbox-attachments-value-unknown": "不适用", "smw-factbox-attachments-is-local": "本地", "smw-factbox-attachments-help": "展示可用的附件", "smw-factbox-facts-derived": "衍生事实", "smw-factbox-facts-derived-help": "展示根据规则或借助其他推理技术得出的事实", "smw_isspecprop": "该属性是此wiki内的特殊属性。", "smw-concept-cache-header": "缓存使用情况", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count 概念缓存]含有{{PLURAL:$1|'''$1'''个实体}}（$2）。", "smw-concept-no-cache": "没有可用的缓存。", "smw_concept_description": "概念“$1”的描述", "smw_no_concept_namespace": "概念只能在“概念:”（Concept:）命名空间内的页面上定义。", "smw_multiple_concepts": "每个概念页面只能有一个概念定义。", "smw_concept_cache_miss": "因为wiki配置要求概念“$1”离线计算，所以该概念目前无法使用。如果一段时间后问题仍未解决，请要求网站管理员让该概念可用。", "smw_noinvannot": "值不能分配给反向属性。", "version-semantic": "语义扩展", "smw_baduri": "不允许“$1”形式的URI。", "smw_printername_count": "结果计数", "smw_printername_csv": "CSV导出", "smw_printername_dsv": "DSV导出", "smw_printername_debug": "调试查询（供专家使用）", "smw_printername_embedded": "嵌入式页面内容", "smw_printername_json": "JSON导出", "smw_printername_list": "列表", "smw_printername_plainlist": "普通列表", "smw_printername_ol": "有序列表", "smw_printername_ul": "无序列表", "smw_printername_table": "表格", "smw_printername_broadtable": "宽表格", "smw_printername_template": "模板", "smw_printername_templatefile": "模板文件", "smw_printername_rdf": "RDF导出", "smw_printername_category": "分类", "validator-type-class-SMWParamSource": "文本", "smw-paramdesc-limit": "最大返回结果数", "smw-paramdesc-offset": "第一个结果的偏移量", "smw-paramdesc-headers": "显示表头/属性名称", "smw-paramdesc-mainlabel": "首列（主页面名称）的表头标签", "smw-paramdesc-link": "把值显示成链接", "smw-paramdesc-intro": "当有查询结果时显示在结果前的文字", "smw-paramdesc-outro": "当有查询结果时显示在结果后的文字", "smw-paramdesc-default": "当没有查询结果时显示的文字", "smw-paramdesc-sep": "结果之间的分隔符", "smw-paramdesc-propsep": "结果条目内属性之间的分隔符", "smw-paramdesc-valuesep": "结果属性的值之间的分隔符", "smw-paramdesc-showsep": "在CSV文件顶部显示分隔符（“sep=<value>”）", "smw-paramdesc-distribution": "显示值的出现次数，而不是显示所有值。", "smw-paramdesc-distributionsort": "按出现次数排序值分布。", "smw-paramdesc-distributionlimit": "限制值分布内值的数量。", "smw-paramdesc-aggregation": "指定合计涉及的内容", "smw-paramdesc-template": "用于展示打印输出的模板的名称", "smw-paramdesc-columns": "显示结果的列数", "smw-paramdesc-userparam": "当使用模板时传递给每个模板调用的值", "smw-paramdesc-class": "给列表设置的额外CSS类", "smw-paramdesc-introtemplate": "当有查询结果时显示在结果前的模板的名称", "smw-paramdesc-outrotemplate": "当有查询结果时显示在结果后的模板的名称", "smw-paramdesc-embedformat": "用于定义标题的HTML标记", "smw-paramdesc-embedonly": "不显示标题", "smw-paramdesc-table-class": "给表格设置的额外CSS类", "smw-paramdesc-table-transpose": "垂直显示表头，水平显示结果", "smw-paramdesc-prefix": "控制打印输出内命名空间的显示", "smw-paramdesc-rdfsyntax": "要使用的RDF语法", "smw-paramdesc-csv-sep": "指定列的分隔符", "smw-paramdesc-csv-valuesep": "指定值的分隔符", "smw-paramdesc-csv-merge": "合并有相同主体标识符（也称首列）的行及其每列的值", "smw-paramdesc-csv-bom": "在输出文件的顶部添加BOM（标示字节顺序的字符）", "smw-paramdesc-dsv-separator": "要使用的分隔符", "smw-paramdesc-dsv-filename": "DSV文件的名称", "smw-paramdesc-filename": "输出文件的名称", "smw-smwdoc-description": "展示可用于特定结果格式的所有参数及其默认值和描述的表格。", "smw-smwdoc-default-no-parameter-list": "该结果格式未提供格式特有参数。", "smw-smwdoc-par-format": "显示参数文档的结果格式。", "smw-smwdoc-par-parameters": "要显示的参数种类。“specific”显示格式特有的参数，“base”显示所有格式均有的参数，“all”两种参数都显示。", "smw-paramdesc-sort": "排序查询所依据的属性", "smw-paramdesc-order": "查询排序的顺序", "smw-paramdesc-searchlabel": "提示继续搜索的文字", "smw-paramdesc-named_args": "命名传递给模板的参数", "smw-paramdesc-template-arguments": "设置命名的参数传递给模板的方式", "smw-paramdesc-import-annotation": "解析主体期间复制的额外注解的数据", "smw-paramdesc-export": "导出选项", "smw-paramdesc-prettyprint": "显示额外缩进和换行的美化打印（pretty-print）输出", "smw-paramdesc-json-unescape": "含有未转义斜杠和多字节Unicode字符的输出", "smw-paramdesc-json-type": "序列化类型", "smw-paramdesc-source": "备选查询来源", "smw-paramdesc-jsonsyntax": "要使用的JSON语法", "smw-printername-feed": "RSS和Atom信息源", "smw-paramdesc-feedtype": "信息源类型", "smw-paramdesc-feedtitle": "用作信息源标题的文字", "smw-paramdesc-feeddescription": "用作信息源描述的文字", "smw-paramdesc-feedpagecontent": "用信息源显示的页面内容", "smw-label-feed-description": "$1的$2信息源", "smw-paramdesc-mimetype": "输出文件的媒体类型（MIME类型）", "smw_iq_disabled": "此wiki已停用语义查询。", "smw_iq_moreresults": "...更多结果", "smw_parseerror": "无法理解给定的值。", "smw_decseparator": ".", "smw_kiloseparator": ",", "smw_notitle": "“$1”在此wiki内不能用作页面名称。", "smw_noproperty": "“$1”在此wiki内不能用作属性名称。", "smw_wrong_namespace": "这里只允许命名空间“$1”内的页面。", "smw_manytypes": "给属性定义了不止一种类型。", "smw_emptystring": "不接受空字符串。", "smw_notinenum": "“$1”不在“$3”属性的[[Property:Allows value|允许值]]列表（$2）内。", "smw-datavalue-constraint-error-allows-value-list": "“$1”不在“$3”属性的[[Property:Allows value|允许值]]列表（$2）内。", "smw-datavalue-constraint-error-allows-value-range": "“$1”不在“$3”属性的[[Property:Allows value|允许值]]约束指定的“$2”范围内。", "smw-constraint-error": "约束问题", "smw-constraint-error-suggestions": "请检查列出的违反约束情况和属性以及它们的注解值，以确保满足所有约束要求。", "smw-constraint-error-limit": "该列表最多含有$1个违反约束情况。", "smw_noboolean": "“$1”不能被识别为布尔（真/假）值。", "smw_true_words": "true,t,yes,y,真,对,是", "smw_false_words": "false,f,no,n,假,错,否", "smw_nofloat": "“$1”不是数字。", "smw_infinite": "不支持“$1”这么大的数字。", "smw_unitnotallowed": "“$1”未被声明为该属性的有效计量单位。", "smw_nounitsdeclared": "该属性没有声明任何计量单位。", "smw_novalues": "没有指定值。", "smw_nodatetime": "无法理解日期“$1”。", "smw_toomanyclosing": "查询内“$1”出现次数过多。", "smw_noclosingbrackets": "您的查询内使用的某些“<nowiki>[[</nowiki>”未用对应的“]]”封闭。", "smw_misplacedsymbol": "符号“$1”用在了没有用处的位置。", "smw_unexpectedpart": "无法理解查询的“$1”部分。结果可能与预期不同。", "smw_emptysubquery": "某些子查询有无效条件。", "smw_misplacedsubquery": "某些子查询用在了不允许使用的位置。", "smw_valuesubquery": "属性“$1”的值不支持子查询。", "smw_badqueryatom": "无法理解查询的某些“<nowiki>[[...]]</nowiki>”部分。", "smw_propvalueproblem": "无法理解属性“$1”的值。", "smw_noqueryfeature": "此wiki不支持某些查询功能，部分查询被丢弃（$1）。", "smw_noconjunctions": "此wiki不支持查询内合取，部分查询被丢弃（$1）。", "smw_nodisjunctions": "此wiki不支持查询内析取，部分查询被丢弃（$1）。", "smw_querytoolarge": "由于此wiki对查询大小或深度的限制，无法考虑以下{{PLURAL:$2|查询条件}}：<code>$1</code>。", "smw_notemplategiven": "请提供参数“template”（模板）的值以便此查询格式正常工作。", "smw_db_sparqlqueryproblem": "无法从SPARQL数据库获得查询结果。该错误可能是暂时的，也可能表明数据库软件存在错误。", "smw_db_sparqlqueryincomplete": "回答查询过于困难，因此被中止。某些结果可能会丢失。如果可能，请尝试使用更简单的查询。", "smw_type_header": "“$1”类型的属性", "smw_typearticlecount": "正在显示$1个使用此类型的{{PLURAL:$1|属性}}。", "smw_attribute_header": "使用属性“$1”的页面", "smw_attributearticlecount": "正在显示$1个使用此属性的{{PLURAL:$1|页面}}。", "smw-propertylist-subproperty-header": "子属性", "smw-propertylist-redirect-header": "同义词", "smw-propertylist-error-header": "有不当分配的页面", "smw-propertylist-count": "正在显示$1个相关的{{PLURAL:$1|实体}}。", "smw-propertylist-count-with-restricted-note": "正在显示$1个相关的{{PLURAL:$1|实体}}（可用实体更多但显示数量限制为“$2”）。", "smw-propertylist-count-more-available": "正在显示$1个相关的{{PLURAL:$1|实体}}（可用实体更多）。", "specialpages-group-smw_group": "语义MediaWiki", "specialpages-group-smw_group-maintenance": "维护", "specialpages-group-smw_group-properties-concepts-types": "属性、概念、类型", "specialpages-group-smw_group-search": "浏览与搜索", "exportrdf": "导出页面为RDF", "smw_exportrdf_docu": "此页面允许您以RDF格式获取页面上的数据。要导出页面，请在下方文本框内输入页面标题，每行一个。", "smw_exportrdf_recursive": "递归导出所有相关页面。注意，结果可能过大！", "smw_exportrdf_backlinks": "也导出引用导出页面的所有页面。生成可浏览的RDF。", "smw_exportrdf_lastdate": "不导出自给定时刻之后无更改的页面。", "smw_exportrdf_submit": "导出", "uriresolver": "URI解析器", "properties": "属性", "smw-categories": "分类", "smw_properties_docu": "以下属性在本wiki内使用。", "smw_property_template": "$1，$2类型（$3次{{PLURAL:$3|使用}}）", "smw_property_template_notype": "$1（$2）", "smw_propertylackspage": "所有属性都应通过页面进行描述！", "smw_propertylackstype": "没有给该属性指定类型（暂时假设为$1类型）。", "smw_propertyhardlyused": "该属性在本wiki内几乎没有使用！", "smw-property-name-invalid": "属性$1无法使用（无效属性名称）。", "smw-property-name-reserved": "“$1”被列为保留名称，不应用作属性。[https://www.semantic-mediawiki.org/wiki/Help:Property_naming 帮助页面]可能含有保留该名称原因的有关信息。", "smw-sp-property-searchform": "显示名称含有以下文字的属性：", "smw-sp-property-searchform-inputinfo": "输入区分大小写，用于筛选时，只显示匹配条件的属性。", "smw-special-property-searchform": "显示名称含有以下文字的属性：", "smw-special-property-searchform-inputinfo": "输入区分大小写，用于筛选时，只显示匹配条件的属性。", "smw-special-property-searchform-options": "选项", "smw-special-wantedproperties-filter-label": "筛选：", "smw-special-wantedproperties-filter-none": "无", "smw-special-wantedproperties-filter-unapproved": "未核准", "smw-special-wantedproperties-filter-unapproved-desc": "与核准模式配合使用的筛选选项。", "concepts": "概念", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Concepts 概念]可视作“动态分类”，即不是手工创建，而是语义MediaWiki根据给定查询的描述计算得到的页面合集。", "smw-special-concept-header": "概念列表", "smw-special-concept-count": "以下{{PLURAL:$1|概念|$1个概念}}正在被列举。", "smw-special-concept-empty": "没有找到概念。", "unusedproperties": "未使用的属性", "smw-unusedproperties-docu": "此页面列出已声明但未被其他页面使用的[https://www.semantic-mediawiki.org/wiki/Unused_properties 未使用的属性]。要获取其他类型属性列表，请查看[[Special:Properties|属性]]或[[Special:WantedProperties|需要的属性]]特殊页面。", "smw-unusedproperty-template": "$1，$2类型", "wantedproperties": "需要的属性", "smw-wantedproperties-docu": "此页面列出在本wiki内使用但没有页面描述的[https://www.semantic-mediawiki.org/wiki/Wanted_properties 需要的属性]。要获取其他类型属性列表，请查看[[Special:Properties|属性]]或[[Special:UnusedProperties|未使用的属性]]特殊页面。", "smw-wantedproperty-template": "$1（$2次{{PLURAL:$2|使用}}）", "smw-special-wantedproperties-docu": "此页面列出在本wiki内使用但没有页面描述的[https://www.semantic-mediawiki.org/wiki/Wanted_properties 需要的属性]。要获取其他类型属性列表，请查看[[Special:Properties|属性]]或[[Special:UnusedProperties|未使用的属性]]特殊页面。", "smw-special-wantedproperties-template": "$1（$2次{{PLURAL:$2|使用}}）", "smw_purge": "刷新", "smw-purge-update-dependencies": "语义MediaWiki正在清除当前页面缓存，因为它检测到一些需要更新的过时依赖。", "smw-purge-failed": "语义MediaWiki尝试清除页面缓存失败", "types": "类型", "smw_types_docu": "[https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes 可用数据类型]列表，其中每个[https://www.semantic-mediawiki.org/wiki/Help:Datatype 类型]代表一组从存储特征角度和从会遗传给分配的属性的显示特征角度描述值的唯一特性。", "smw-special-types-no-such-type": "“$1”未知或未指定为有效数据类型。", "smw-statistics": "语义统计", "smw-statistics-cached": "语义统计（缓存）", "smw-statistics-entities-total": "实体（总计）", "smw-statistics-entities-total-info": "估算的实体的行数。包括属性、概念或任何其他需要分配ID的注册对象表示形式。", "smw-statistics-property-instance": "属性{{PLURAL:$1|值}}（总计）", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|属性}}]]（总计）", "smw-statistics-property-total-info": "注册属性的总数。", "smw-statistics-property-total-legacy": "{{PLURAL:$1|属性}}（总计）", "smw-statistics-property-used": "{{PLURAL:$1|属性}}（与至少一个值一起使用）", "smw-statistics-property-page": "{{PLURAL:$1|属性}}（用页面注册）", "smw-statistics-property-page-info": "统计有专门页面和描述的属性。", "smw-statistics-property-type": "{{PLURAL:$1|属性}}（已分配数据类型）", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|查询}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|查询}}]]（嵌入式，总计）", "smw-statistics-query-format": "<code>$1</code>格式", "smw-statistics-query-size": "查询大小", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|概念}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|概念}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|子对象}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|子对象}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|数据类型}}]]", "smw-statistics-error-count": "{{PLURAL:$1|属性值}}（[[Special:ProcessingErrorList|{{PLURAL:$1|不正确的注解}}]]）", "smw-statistics-error-count-legacy": "{{PLURAL:$1|属性值}}（{{PLURAL:$1|不正确的注解}}）", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities 过时{{PLURAL:$1|实体}}]", "smw-statistics-delete-count-info": "已标记为需删除的实体，应使用提供的维护脚本定期处理。", "smw_uri_doc": "URI解析器实现[$1 W3C关于httpRange-14的TAG发现报告]。它确保人类不会被转化为网站。它可确保RDF表示（针对机器）或维基页面（针对人类）是有根据请求交付。", "ask": "语义搜索", "smw-ask-help": "此部分含有一些解释<code>#ask</code>语法使用方法的链接。\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages 选择页面]介绍选择页面和构建条件的方法\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators 搜索运算符]列出可用的搜索运算符，包括范围查询和通配符查询的运算符\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information 显示信息]概述打印输出语句和格式选项的用法", "smw_ask_sortby": "按列排序（可选）", "smw_ask_ascorder": "升序", "smw_ask_descorder": "降序", "smw-ask-order-rand": "随机", "smw_ask_submit": "查找结果", "smw_ask_editquery": "编辑查询", "smw_add_sortcondition": "[添加排序条件]", "smw-ask-sort-add-action": "添加排序条件", "smw_ask_hidequery": "隐藏查询（紧凑视图）", "smw_ask_help": "查询帮助", "smw_ask_queryhead": "条件", "smw_ask_printhead": "打印输出选择", "smw_ask_printdesc": "（每行添加一个属性名称）", "smw_ask_format_as": "格式为：", "smw_ask_defaultformat": "默认", "smw_ask_otheroptions": "其他选项", "smw-ask-otheroptions-info": "此部分含有更改打印输出语句的选项。把鼠标悬停在参数上可查看参数说明。", "smw-ask-otheroptions-collapsed-info": "请使用加号图标查看所有可用选项", "smw_ask_show_embed": "显示嵌入代码", "smw_ask_hide_embed": "隐藏嵌入代码", "smw_ask_embed_instr": "要把该查询嵌入wiki页面，请使用下方代码。", "smw-ask-delete": "删除", "smw-ask-sorting": "排序", "smw-ask-options": "选项", "smw-ask-options-sort": "排序选项", "smw-ask-format-options": "格式和选项", "smw-ask-parameters": "参数", "smw-ask-search": "搜索", "smw-ask-debug": "调试", "smw-ask-debug-desc": "生成查询调试信息", "smw-ask-no-cache": "停用查询缓存", "smw-ask-no-cache-desc": "没有查询缓存的结果", "smw-ask-result": "结果", "smw-ask-empty": "清除所有条目", "smw-ask-download-link-desc": "以$1格式下载查询结果", "smw-ask-format": "格式", "smw-ask-format-selection-help": "所选格式的帮助：$1", "smw-ask-condition-change-info": "条件已更改，搜索引擎需要重新运行查询以生成匹配新要求的结果。", "smw-ask-input-assistance": "输入辅助", "smw-ask-condition-input-assistance": "给打印输出、排序和条件字段提供[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 输入辅助]。条件字段需要使用任一以下前缀：", "smw-ask-condition-input-assistance-property": "<code>p:</code>可获取属性建议（如<code>[[p:Has ...</code>）", "smw-ask-condition-input-assistance-category": "<code>c:</code>可获取分类建议", "smw-ask-condition-input-assistance-concept": "<code>con:</code>可获取概念建议", "smw-ask-format-change-info": "格式已修改，需要再次执行查询以匹配新参数和可视化选项。", "smw-ask-format-export-info": "所选格式为没有可视化表示形式的导出格式，因此结果只能以下载形式提供。", "smw-ask-query-search-info": "查询<code><nowiki>$1</nowiki></code>在$4{{PLURAL:$4|秒}}内被{{PLURAL:$3|1=<code>$2</code>（来自缓存）|<code>$2</code>（来自缓存）|<code>$2</code>}}回答。", "smw-ask-extra-query-log": "查询日志", "smw-ask-extra-other": "其他", "searchbyproperty": "按属性搜索", "processingerrorlist": "处理错误列表", "constrainterrorlist": "约束错误列表", "propertylabelsimilarity": "属性标签相似性报告", "missingredirectannotations": "丢失的重定向注解", "smw-processingerrorlist-intro": "以下列表提供与[https://www.semantic-mediawiki.org/ 语义MediaWiki]相关的[https://www.semantic-mediawiki.org/wiki/Processing_errors 处理错误]的概览。建议定期监控此列表并修正无效的值注解。", "smw-constrainterrorlist-intro": "以下列表提供与[https://www.semantic-mediawiki.org/ 语义MediaWiki]相关的[https://www.semantic-mediawiki.org/wiki/Constraint_errors 约束错误]的概览。建议定期监控此列表并修正无效的值注解。", "smw-missingredirects-intro": "以下段落会列出语义MediaWiki内缺少[https://www.semantic-mediawiki.org/wiki/Redirects 重定向]注解的页面（通过与MediaWiki内存储的信息对比得到），可通过手动[https://www.semantic-mediawiki.org/wiki/Help:Purge 清除页面缓存]或运行<code>rebuildData.php</code>维护脚本（使用选项<code>--redirects</code>）来重建这些注解。", "smw-missingredirects-list": "缺少注解的页面", "smw-missingredirects-list-intro": "显示$1个缺少重定向注解的{{PLURAL:$1|页面}}。", "smw-missingredirects-noresult": "没有找到缺少的重定向注解。", "smw_sbv_docu": "搜索有给定属性和值的所有页面。", "smw_sbv_novalue": "请输入该属性的有效值，或查看“$1”的所有属性值。", "smw_sbv_displayresultfuzzy": "有值为“$2”的“$1”属性的所有页面的列表。由于结果过少，也显示邻近值的结果。", "smw_sbv_property": "属性：", "smw_sbv_value": "值：", "smw_sbv_submit": "查找结果", "browse": "浏览wiki", "smw_browselink": "浏览属性", "smw_browse_article": "请输入开始浏览的页面名称。", "smw_browse_go": "提交", "smw_browse_show_incoming": "显示传入属性", "smw_browse_hide_incoming": "隐藏传入属性", "smw_browse_no_outgoing": "该页面没有属性。", "smw_browse_no_incoming": "没有属性链接到该页面。", "smw-browse-from-backend": "当前正在从后端检索信息。", "smw-browse-intro": "此页面提供主体或实体实例的有关详细信息，请输入要检查的对象名称。", "smw-browse-invalid-subject": "主体验证返回“$1”错误。", "smw-browse-api-subject-serialization-invalid": "主体的序列化格式无效。", "smw-browse-js-disabled": "JavaScript可能停用或不支持。我们建议使用支持JavaScript的浏览器。其他选项请查阅[https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>]配置参数页面。", "smw-browse-show-group": "显示组合", "smw-browse-hide-group": "隐藏组合", "smw-noscript": "该页面或操作需要JavaScript才能工作。请在您的浏览器中启用JavaScript或使用支持Javascript的浏览器，以便功能按需提供。更多信息请查阅[https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript]帮助页面。", "smw_inverse_label_default": "作为$1", "smw_inverse_label_property": "反向属性标签", "pageproperty": "页面属性搜索", "pendingtasklist": "待处理任务列表", "facetedsearch": "多面搜索", "smw_pp_docu": "输入页面和属性，或只输入属性以检索所有分配值。", "smw_pp_from": "页面：", "smw_pp_type": "属性：", "smw_pp_submit": "查找结果", "smw-prev": "上{{PLURAL:$1|$1}}条", "smw-next": "下{{PLURAL:$1|$1}}条", "smw_result_prev": "上一页", "smw_result_next": "下一页", "smw_result_results": "结果", "smw_result_noresults": "没有结果。", "smwadmin": "语义MediaWiki控制面板", "smw-admin-statistics-job-title": "工作统计", "smw-admin-statistics-job-docu": "工作统计显示尚未执行的预定语义MediaWiki工作的有关信息。工作数量可能略有误差或含有失败的尝试。更多信息请查阅[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 手册]。", "smw-admin-statistics-querycache-title": "查询缓存", "smw-admin-statistics-querycache-disabled": "[https://www.semantic-mediawiki.org/wiki/QueryCache 查询缓存]未在此wiki启用，因此没有可用的统计信息。", "smw-admin-statistics-querycache-legend": "缓存统计含有以下临时累积数据及其衍生数据：\n* “misses”是从缓存检索数据无法获得响应，而被迫检索直接存储库（数据库、三重存储等）的尝试次数\n* “deletes”是缓存驱逐操作（通过清除缓存或查询依赖）的总数\n* “hits”含有从嵌入式（wiki页面内调用的查询）或非嵌入式（通过诸如Special:Ask等页面或API请求）来源检索缓存的次数\n* “medianRetrievalResponseTime”是收集过程中的缓存和非缓存检索请求的中位响应时间（秒）的方向值\n* “noCache”表示从缓存检索结果的无尝试请求（limit=0的查询、“no-cache”选项等）的总数", "smw-admin-statistics-section-explain": "该部分给管理员提供额外的统计信息。", "smw-admin-statistics-semanticdata-overview": "概览", "smw-admin-permission-missing": "对此页面的访问因缺少权限而被阻止，请查阅[https://www.semantic-mediawiki.org/wiki/Help:Permissions 权限]帮助页面获取必要设置的有关详细信息。", "smw-admin-setupsuccess": "存储引擎已设置完成。", "smw_smwadmin_return": "返回$1", "smw_smwadmin_updatestarted": "刷新语义数据的新更新流程已启动。所有储存的数据会在需要时重建或修复。您可以在此特殊页面上追踪更新进度。", "smw_smwadmin_updatenotstarted": "已经有正在运行的更新流程。不会再创建另一个。", "smw_smwadmin_updatestopped": "所有现有的更新流程均已停止。", "smw_smwadmin_updatenotstopped": "要停止正在运行的更新流程，您必须选中复选框以确认您的要求。", "smw-admin-docu": "此特殊页面会在您安装、升级、维护和使用<a href=\"https://www.semantic-mediawiki.org\">语义MediaWiki</a>时提供帮助，还提供进一步的管理功能、任务，以及统计。请记得在执行管理功能前备份重要数据。", "smw-admin-environment": "软件环境", "smw-admin-db": "数据库设置", "smw-admin-db-preparation": "表格初始化正在进行，可能需要一些时间才能显示结果，这取决于表格的大小和可能需要的表格优化。", "smw-admin-dbdocu": "语义MediaWiki需要自己的数据库结构（独立于MediaWiki，因此不影响MediaWiki安装的其余部分）来存储语义数据。虽然此设置功能可以多次执行而不造成任何损害，但是只需在安装或升级时执行一次即可。", "smw-admin-permissionswarn": "如果操作因SQL错误而失败，则可能是wiki任用的数据库用户（检查“LocalSettings.php”文件设置）没有足够的权限。要么授予该用户创建和删除表格的额外权限，要么临时在“LocalSettings.php”文件输入数据库根目录的登录信息，要么使用可读取管理员凭据的维护脚本<code>setupStore.php</code>。", "smw-admin-dbbutton": "初始化或升级表格", "smw-admin-announce": "发布您的wiki", "smw-admin-announce-text": "如果您的wiki是公开的，您可以在<a href=\"https://wikiapiary.com\">WikiApiary</a>（追踪wiki的wiki）上注册它。", "smw-admin-deprecation-notice-title": "弃用通知", "smw-admin-deprecation-notice-docu": "以下部分含有已弃用或删除，但在此wiki上仍处于活跃状态的设置。预计任何未来发行版都会删除对这些配置的支持。", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>已弃用，会在$2版本删除", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>会删除（或替换）以下{{PLURAL:$2|选项}}：", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code>已弃用，会在$2版本删除", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>已替换为<code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>已替换为<code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>{{PLURAL:$2|选项}}：", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code>即将替换为<code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>已在$2版本删除", "smw-admin-deprecation-notice-title-notice": "已弃用的设置", "smw-admin-deprecation-notice-title-notice-explanation": "<b>已弃用的设置</b>展示在此wiki检测到使用并计划在未来发行版中删除或更改的设置。", "smw-admin-deprecation-notice-title-replacement": "已替换或重命名的设置", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>已替换或重命名的设置</b>含有已重命名或有其他形式修改的设置，建议立刻更新其名称或格式。", "smw-admin-deprecation-notice-title-removal": "已删除的设置", "smw-admin-deprecation-notice-title-removal-explanation": "<b>已删除的设置</b>识别在之前发行版中删除但在此wiki检测到使用的设置。", "smw-admin-deprecation-notice-section-legend": "说明", "smw-smwadmin-refresh-title": "数据修复与更新", "smw_smwadmin_datarefresh": "数据重建", "smw_smwadmin_datarefreshdocu": "所有语义MediaWki数据可以根据wiki当前内容还原。此功能可以用于，当内部格式因软件升级而更改时，修复受损数据或刷新数据。更新逐页面执行，不会立即完成。下面会显示更新是否正在进行，并允许您启动或停止更新（除非网站管理员停用该功能）。", "smw_smwadmin_datarefreshprogress": "<strong>已有更新正在进行。</strong>更新进展缓慢是正常的，因为只会在每当有用户访问wiki时刷新一小块数据。要更快完成更新，您可以调用MediaWiki维护脚本<code>runJobs.php</code>（使用选项<code>--maxjobs 1000</code>限制每批次更新的数量）。当前更新估计进度：", "smw_smwadmin_datarefreshbutton": "安排数据重建", "smw_smwadmin_datarefreshstop": "停止此次更新", "smw_smwadmin_datarefreshstopconfirm": "是的，我{{GENDER:$1|确认}}。", "smw-admin-job-scheduler-note": "此部分内（已启用的）任务都通过工作队列执行，以避免执行过程中出现冲突死锁情况。[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue job queue 工作队列]对处理过程负责，因此维护脚本<code>runJobs.php</code>必需拥有适当的容量（请检查配置参数<code>$wgRunJobsAsync</code>）。", "smw-admin-outdateddisposal-title": "过时实体废弃", "smw-admin-outdateddisposal-intro": "某些活动（更改属性类型、删除wiki页面、纠正错误值）会产生[https://www.semantic-mediawiki.org/wiki/Outdated_entities 过时实体]，建议定期删除这些实体以释放关联的表格空间。", "smw-admin-outdateddisposal-active": "已安排过时实体废弃工作。", "smw-admin-outdateddisposal-button": "安排废弃", "smw-admin-feature-disabled": "此功能已在此wiki停用，请查阅<a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">设置</a>帮助页面或联系系统管理员。", "smw-admin-propertystatistics-title": "属性统计重建", "smw-admin-propertystatistics-intro": "重建整个属性使用情况统计，借此更新并更正属性的[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count 使用次数]。", "smw-admin-propertystatistics-active": "已安排属性统计重建工作。", "smw-admin-propertystatistics-button": "安排统计重建", "smw-admin-fulltext-title": "全文搜索重建", "smw-admin-fulltext-intro": "用启用的[https://www.semantic-mediawiki.org/wiki/Full-text 全文搜索]数据类型从属性表格重建搜索索引。更改索引规则（修改停止词、新增词干分析器等）和/或新增或修改表格后，必需再次运行此工作。", "smw-admin-fulltext-active": "已安排全文搜索重建工作。", "smw-admin-fulltext-button": "安排全文重建", "smw-admin-support": "获取支持", "smw-admin-supportdocu": "我们提供多种资源帮助您处理以下问题：", "smw-admin-installfile": "如果您在安装时遇到问题，请首先检查<a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">安装指导文件</a>和<a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">安装页面</a>内的指南。", "smw-admin-smwhomepage": "语义MediaWiki的完整用户文档位于<b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>。", "smw-admin-bugsreport": "漏洞错误可在<a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">问题追踪器</a>报告，<a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">报告错误</a>页面提供了一些关于撰写有效问题报告的方法指导。", "smw-admin-questions": "如果您有更多疑问或建议，请加入语义MediaWiki<a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">用户邮件列表</a>上的讨论。", "smw-admin-other-functions": "其他功能", "smw-admin-statistics-extra": "统计功能", "smw-admin-statistics": "统计", "smw-admin-supplementary-section-title": "补充功能", "smw-admin-supplementary-section-subtitle": "支持的核心功能", "smw-admin-supplementary-section-intro": "此部分提供维护活动范围外的额外功能，列出的某些功能（请查阅[https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions 文档]）可能受限或不可用，因此在此wiki不可访问。", "smw-admin-supplementary-settings-title": "配置与设置", "smw-admin-supplementary-settings-intro": "<u>$1</u>展示定义语义MediaWiki行为的参数", "smw-admin-main-title": "语义MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "运行统计", "smw-admin-supplementary-operational-statistics-short-title": "运行统计", "smw-admin-supplementary-operational-statistics-intro": "展示一组扩展的<u>$1</u>", "smw-admin-supplementary-idlookup-title": "实体查找与废弃", "smw-admin-supplementary-idlookup-short-title": "实体查找与废弃", "smw-admin-supplementary-idlookup-intro": "支持简单的<u>$1</u>功能", "smw-admin-supplementary-duplookup-title": "重复实体查找", "smw-admin-supplementary-duplookup-intro": "<u>$1</u>可查找归类为所选表格矩阵的重复项的实体", "smw-admin-supplementary-duplookup-docu": "此页面列出所选表格中归类为[https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities 重复]的条目。重复条目应该只会在更新终止或回滚事务不成功等极少数情况下产生。", "smw-admin-supplementary-operational-statistics-cache-title": "缓存统计", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u>展示一组精选的与缓存相关的统计信息", "smw-admin-supplementary-operational-table-statistics-title": "表格统计", "smw-admin-supplementary-operational-table-statistics-short-title": "表格统计", "smw-admin-supplementary-operational-table-statistics-intro": "给一组选定的表格生成<u>$1</u>", "smw-admin-supplementary-operational-table-statistics-explain": "此部分含有帮助管理员和数据管理员就后端和存储引擎的状态做出明智决策的选定表格的统计信息。", "smw-admin-supplementary-operational-table-statistics-legend": "以下是表格统计使用的一些键的说明：", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> 表格内的总行数", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> 当前使用的最后一个ID\n* <code>duplicate_count</code> id_table内找到的重复项数（另见[[Special:SemanticMediaWiki/duplicate-lookup|重复实体查找]]）\n* <code>rows.rev_count</code> 分配有表示直接wiki页面链接的revision_id的行数\n* <code>rows.smw_namespace_group_by_count</code> 表格内使用的命名空间合计的行数\n* <code>rows.smw_proptable_hash.query_match_count</code> 有对应表格引用的查询子对象数\n* <code>rows.smw_proptable_hash.query_null_count</code> 没有表格引用（未链接、浮动引用）的查询子对象数", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> 唯一词语的百分比（百分比低表明重复词语占据了表格内容和索引）\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> 只出现一次的词语数\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> 出现多次的词语数", "smw-admin-supplementary-elastic-version-info": "版本", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u>展示设置和索引统计的有关详细信息", "smw-admin-supplementary-elastic-docu": "此页面含有连接至语义MediaWiki的Elasticsearch集群及其[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>]的设置、映射、健康状况和索引统计的有关信息。", "smw-admin-supplementary-elastic-functions": "支持的功能", "smw-admin-supplementary-elastic-settings-title": "设置（索引）", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u>被Elasticsearch用于管理语义MediaWiki索引", "smw-admin-supplementary-elastic-mappings-title": "映射", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u>列出索引与字段的映射", "smw-admin-supplementary-elastic-mappings-docu": "此页面含有当前索引使用的字段映射的详细信息。建议监控与<code> index.mapping.total_fields.limit</code>（指定索引内允许的最大字段数）相关的映射。", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code>是有索引的核心字段数，而<code>nested_fields</code>是分配给核心字段以支持特定结构化搜索样板的额外字段的累积数。", "smw-admin-supplementary-elastic-mappings-summary": "摘要", "smw-admin-supplementary-elastic-mappings-fields": "字段映射", "smw-admin-supplementary-elastic-nodes-title": "节点", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u>展示节点统计信息", "smw-admin-supplementary-elastic-indices-title": "索引", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u>提供可用索引及其统计的概览", "smw-admin-supplementary-elastic-statistics-title": "统计", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u>展示索引等级的统计信息", "smw-admin-supplementary-elastic-statistics-docu": "此页面提供在索引等级上进行的不同操作的索引统计洞察信息，返回的统计整合了初级数据和总合计。[https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html 帮助页面]含有可用索引统计的详细描述。", "smw-admin-supplementary-elastic-status-replication": "复制状态", "smw-admin-supplementary-elastic-status-last-active-replication": "最后一次活跃复制：$1", "smw-admin-supplementary-elastic-status-refresh-interval": "刷新间隔：$1", "smw-admin-supplementary-elastic-status-recovery-job-count": "还原工作积压：$1（估计）", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "摄入（文件）工作积压：$1（估计）", "smw-admin-supplementary-elastic-status-rebuild-lock": "锁定的复制：$1（正在进行重建）", "smw-admin-supplementary-elastic-status-replication-monitoring": "复制监控（活跃）：$1", "smw-admin-supplementary-elastic-replication-header-title": "复制状态", "smw-admin-supplementary-elastic-replication-function-title": "复制", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u>展示失败复制的有关信息", "smw-admin-supplementary-elastic-replication-docu": "此页面提供报告有Elasticsearch集群问题的实体的[https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring 复制状态]的有关信息。建议检查列出的实体并清除内容，以确认这是临时问题。", "smw-admin-supplementary-elastic-replication-files-docu": "需要注意，对于文件列表，必需首先执行[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion 文件摄入]工作并等待其处理完成。", "smw-admin-supplementary-elastic-replication-files": "文件", "smw-admin-supplementary-elastic-replication-pages": "页面", "smw-admin-supplementary-elastic-endpoints": "端点", "smw-admin-supplementary-elastic-config": "配置", "smw-admin-supplementary-elastic-no-connection": "本wiki目前'''无法'''建立与Elasticsearch集群的连接，请联系wiki管理员调查该问题，因为它会导致系统的索引和查询能力失效。", "smw-list-count": "该列表含有$1个{{PLURAL:$1|实体}}。", "smw-property-label-uniqueness": "“$1”标签匹配至少一个其他属性表示。请查阅[https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness 帮助页面]了解解决该问题的方法。", "smw-property-label-similarity-title": "属性标签相似性报告", "smw-property-label-similarity-intro": "<u>$1</u>计算现有属性标签的相似性", "smw-property-label-similarity-threshold": "阈值：", "smw-property-label-similarity-type": "显示类型ID", "smw-property-label-similarity-noresult": "没有找到符合所选选项的结果。", "smw-property-label-similarity-docu": "此页面比较属性标签间的[https://www.semantic-mediawiki.org/wiki/Property_similarity 相似性距离]（不要与语义或词汇相似性混淆），并报告超出阈值的标签。报告可帮助筛选错误拼写或代表相同概念的等效属性（请查看[[Special:Properties|属性]]特殊页面了解报告的属性的概念和使用情况）。可以调整阈值来扩大或缩小用于近似匹配的距离。<code>[[Property:$1|$1]]</code>用于从分析中排除属性。", "smw-admin-operational-statistics": "此页面含有从语义MediaWiki相关功能收集到的运行统计。wiki特有统计的扩展列表请查看[[Special:Statistics|<b>这里</b>]]。", "smw_adminlinks_datastructure": "数据结构", "smw_adminlinks_displayingdata": "数据显示", "smw_adminlinks_inlinequerieshelp": "行内查询帮助", "smw-page-indicator-usage-count": "估算的[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count 使用计数]：{{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|用户|系统}}定义的属性", "smw-property-indicator-last-count-update": "估算的使用计数 最后更新时间：$1", "smw-concept-indicator-cache-update": "缓存计数 最后更新时间：$1", "smw-createproperty-isproperty": "这是$1类型的属性。", "smw-createproperty-allowedvals": "此属性的{{PLURAL:$1|允许值有}}：", "smw-paramdesc-category-delim": "分隔符", "smw-paramdesc-category-template": "用于格式化项目的模板", "smw-paramdesc-category-userparam": "传递给模板的参数", "smw-info-par-message": "要显示的消息。", "smw-info-par-icon": "要显示的图标，“info”（信息）或“warning”（警告）。", "prefs-smw": "语义MediaWiki", "prefs-general-options": "常规选项", "prefs-extended-search-options": "扩展搜索", "prefs-ask-options": "语义搜索", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ 语义MediaWiki]及关联扩展给一组精选的特色功能和一般功能提供个人参数设置。各个设置及其描述和特征的列表请查阅[https://www.semantic-mediawiki.org/wiki/Help:User_preferences 帮助页面]。", "smw-prefs-ask-options-tooltip-display": "在#ask[[Special:Ask|查询生成器]]特殊页面上把参数文本显示为信息工具提示。", "smw-prefs-ask-options-compact-view-basic": "启用基本紧凑视图", "smw-prefs-help-ask-options-compact-view-basic": "若启用，会在Special:Ask紧凑视图上显示一组精简的链接。", "smw-prefs-general-options-time-correction": "使用本地[[Special:Preferences#mw-prefsection-rendering|时差]]参数设置给特殊页面启用时间校正", "smw-prefs-general-options-jobqueue-watchlist": "在我的个人栏显示工作队列监视列表", "smw-prefs-help-general-options-jobqueue-watchlist": "若启用，会显示待处理的精选工作及其估计队列大小的[https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist 列表]。", "smw-prefs-general-options-disable-editpage-info": "停用编辑页面上的介绍性文字", "smw-prefs-general-options-disable-search-info": "停用标准搜索页面上的语法支持信息", "smw-prefs-general-options-suggester-textinput": "启用语义实体的输入辅助", "smw-prefs-help-general-options-suggester-textinput": "若启用，即允许使用[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 输入辅助]查找来自输入上下文的属性、概念、分类。", "smw-prefs-general-options-show-entity-issue-panel": "显示实体问题面板", "smw-prefs-help-general-options-show-entity-issue-panel": "若启用，会在每个页面上运行完整性检查并显示[https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel 实体问题面板]。", "smw-prefs-factedsearch-profile": "选择[[Special:FacetedSearch|多面搜索]]默认配置文件：", "smw-ui-tooltip-title-property": "属性", "smw-ui-tooltip-title-quantity": "单位换算", "smw-ui-tooltip-title-info": "信息", "smw-ui-tooltip-title-service": "服务链接", "smw-ui-tooltip-title-warning": "警告", "smw-ui-tooltip-title-error": "错误", "smw-ui-tooltip-title-parameter": "参数", "smw-ui-tooltip-title-event": "事件", "smw-ui-tooltip-title-note": "注释", "smw-ui-tooltip-title-legend": "说明", "smw-ui-tooltip-title-reference": "引用", "smw_unknowntype": "该属性的类型“$1”无效", "smw-concept-cache-text": "该概念共有$1个{{PLURAL:$1|页面}}，最后更新于$2 $3。", "smw_concept_header": "概念“$1”的页面", "smw_conceptarticlecount": "下面正在显示$1个{{PLURAL:$1|页面}}。", "smw-qp-empty-data": "由于选择标准不充分，无法显示请求的数据。", "right-smw-admin": "访问管理任务（语义MediaWiki）", "right-smw-patternedit": "维护允许的正则表达式和样板的编辑权限（语义MediaWiki）", "right-smw-pageedit": "编辑“受编辑保护”（<code>Is edit protected</code>）的注释页面权限（语义MediaWiki）", "right-smw-schemaedit": "编辑[https://www.semantic-mediawiki.org/wiki/Help:Schema 模式页面]（语义MediaWiki）", "right-smw-viewjobqueuewatchlist": "访问工作队列[https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist 监视列表]功能（语义MediaWiki）", "right-smw-viewentityassociatedrevisionmismatch": "访问关联有修订误配的实体的有关信息（语义MediaWiki）", "right-smw-vieweditpageinfo": "查看[https://www.semantic-mediawiki.org/wiki/Help:Edit_help 编辑帮助]（语义MediaWiki）", "restriction-level-smw-pageedit": "受保护（仅限符合资格的用户）", "action-smw-patternedit": "编辑语义MediaWiki使用的正则表达式", "action-smw-pageedit": "编辑有“受编辑保护”（<code>Is edit protected</code>）注释的页面（语义MediaWiki）", "group-smwadministrator": "管理员（语义MediaWiki）", "group-smwadministrator-member": "{{GENDER:$1|管理员（语义MediaWiki）}}", "grouppage-smwadministrator": "{{ns:project}}:管理员（语义MediaWiki）", "group-smwcurator": "负责人（语义MediaWiki）", "group-smwcurator-member": "{{GENDER:$1|负责人（语义MediaWiki）}}", "grouppage-smwcurator": "{{ns:project}}:负责人（语义MediaWiki）", "group-smweditor": "编辑（语义MediaWiki）", "group-smweditor-member": "{{GENDER:$1|编辑（语义MediaWiki）}}", "grouppage-smweditor": "{{ns:project}}:编辑（语义MediaWiki）", "action-smw-admin": "访问语义MediaWiki管理任务", "action-smw-ruleedit": "编辑规则页面（语义MediaWiki）", "smw-property-namespace-disabled": "属性[https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks 命名空间]已停用，因此无法给该属性声明类型或其他属性特有特征。", "smw-property-predefined-default": "“$1”是$2类型的预定义属性。", "smw-property-predefined-common": "该属性是预部署属性（也称[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 特殊属性]），带有额外管理权限，但是可以像任何其他[https://www.semantic-mediawiki.org/wiki/Property 用户定义属性]一样使用。", "smw-property-predefined-ask": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示有关单个查询的元信息（[https://www.semantic-mediawiki.org/wiki/Subobject 子对象]形式）的预定义属性。", "smw-property-predefined-asksi": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的收集查询内使用的条件数的预定义属性。", "smw-property-predefined-askde": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的告知查询深度的预定义属性。", "smw-property-predefined-long-askde": "它是根据，子查询嵌套、属性链和执行<code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>配置参数限制的查询得到的描述元素，计算得出的数字值。", "smw-property-predefined-askpa": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的描述影响查询结果的参数的预定义属性。", "smw-property-predefined-long-askpa": "它属于指定[https://www.semantic-mediawiki.org/wiki/Help:Query_profiler 查询配置文件]的属性集合。", "smw-sp-properties-docu": "此页面列出此wiki可用的[https://www.semantic-mediawiki.org/wiki/Property 属性]及其使用次数。要获取最新的次数统计，我们建议定期运行[https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics 属性统计]维护脚本。要获取其他类型属性列表，请查看[[Special:UnusedProperties|未使用的属性]]或[[Special:WantedProperties|需要的属性]]特殊页面。", "smw-sp-properties-cache-info": "列出的数据检索自[https://www.semantic-mediawiki.org/wiki/Caching 缓存]，最后更新于$1。", "smw-sp-properties-header-label": "属性列表", "smw-admin-settings-docu": "显示所有与语义MediaWiki环境相关的默认和本地设置的列表。要获取各个设置的详细信息，请查阅[https://www.semantic-mediawiki.org/wiki/Help:Configuration 配置]帮助页面。", "smw-sp-admin-settings-button": "生成设置列表", "smw-admin-idlookup-title": "查找", "smw-admin-idlookup-docu": "此部分展示语义MediaWiki中单个实体（wiki页面、子对象、属性等）的技术详细信息。输入可以是数字ID或匹配相关搜索字段的字符串值，任何ID都应该是语义MediaWiki内的ID，而与MediaWiki的页面或修订ID无关。", "smw-admin-iddispose-title": "废弃", "smw-admin-iddispose-docu": "请注意，废弃操作不受限制，一旦确认，会删除存储引擎内的该实体及其在待处理表格内的所有引用。请只在查阅[https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal 文档]后'''谨慎'''执行此任务。", "smw-admin-iddispose-done": "ID“$1”已从存储后端删除。", "smw-admin-iddispose-references": "ID“$1”{{PLURAL:$2|没有|有至少一个}}活跃引用：", "smw-admin-iddispose-references-multiple": "有至少一个活跃引用记录的匹配项列表。", "smw-admin-iddispose-no-references": "搜索无法把“$1”与表格条目匹配。", "smw-admin-idlookup-input": "搜索：", "smw-admin-objectid": "ID：", "smw-admin-tab-general": "概览", "smw-admin-tab-notices": "弃用通知", "smw-admin-tab-maintenance": "维护", "smw-admin-tab-supplement": "补充功能", "smw-admin-tab-registry": "注册", "smw-admin-tab-alerts": "警报", "smw-admin-alerts-tab-deprecationnotices": "弃用通知", "smw-admin-alerts-tab-maintenancealerts": "维护警报", "smw-admin-alerts-section-intro": "此部分展示归类为需要管理员或有适当权限用户注意的设置、操作和其他活动相关的警报和通知。", "smw-admin-maintenancealerts-section-intro": "以下警报和通知应予以解决，虽然不是必需的，但预计有助于提高系统和运行的维护性。", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "表格优化", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "系统发现最后一次[https://www.semantic-mediawiki.org/wiki/Table_optimization 表格优化]运行于$2天前（来自$1的记录），超出了$3天的维护阈值。如文档所述，运行优化会使查询规划器能够对查询做出更好的决策，因此建议定期运行表格优化。", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "过时实体", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "系统统计到$1个[https://www.semantic-mediawiki.org/wiki/Outdated_entities 过时实体]，达到超过阈值$2的无人值守维护的临界水平。建议运行[https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>]维护脚本。", "smw-admin-maintenancealerts-invalidentities-alert-title": "无效实体", "smw-admin-maintenancealerts-invalidentities-alert": "系统匹配$1个[https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|实体}}]到[https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace 未维护命名空间]，建议运行[https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>]或[https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>]维护脚本。", "smw-admin-deprecation-notice-section": "语义MediaWiki", "smw-admin-configutation-tab-settings": "设置", "smw-admin-configutation-tab-namespaces": "命名空间", "smw-admin-configutation-tab-schematypes": "模式类型", "smw-admin-maintenance-tab-tasks": "任务", "smw-admin-maintenance-tab-scripts": "维护脚本", "smw-admin-maintenance-no-description": "没有描述。", "smw-admin-maintenance-script-section-title": "可用维护脚本列表", "smw-admin-maintenance-script-section-intro": "执行以下列出的维护脚本需要管理员和命令行访问权限。", "smw-admin-maintenance-script-description-dumprdf": "现有三元组的RDF导出。", "smw-admin-maintenance-script-description-rebuildconceptcache": "此脚本用于管理语义MediaWiki的概念缓存，可以创建、删除和更新选定的缓存。", "smw-admin-maintenance-script-description-rebuilddata": "通过循环遍历所有可能拥有语义数据的页面，重新创建数据库内的所有语义数据。", "smw-admin-maintenance-script-description-rebuildelasticindex": "通过循环遍历所有拥有语义数据的实体，重建Elasticsearch索引（仅适用于使用<code>ElasticStore</code>的安装）。", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "查找Elasticsearch内丢失的实体（仅适用于使用<code>ElasticStore</code>的安装）并安排适当的更新工作。", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "重建<code>SQLStore</code>全文搜索索引（适用于启用该设置的安装）。", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "重建所有属性实体的使用统计。", "smw-admin-maintenance-script-description-removeduplicateentities": "删除所选表格内找到的没有活跃引用的重复实体。", "smw-admin-maintenance-script-description-setupstore": "设置<code>LocalSettings.php</code>内定义的存储和查询后端。", "smw-admin-maintenance-script-description-updateentitycollation": "更新<code>SQLStore</code>内的<code>smw_sort</code>字段（根据[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]设置）。", "smw-admin-maintenance-script-description-populatehashfield": "填充缺少值的行的<code>smw_hash</code>字段。", "smw-admin-maintenance-script-description-purgeentitycache": "清除已知实体及其关联数据的缓存条目。", "smw-admin-maintenance-script-description-updatequerydependencies": "更新查询和查询依赖（请查看[https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]设置）。", "smw-admin-maintenance-script-description-disposeoutdatedentities": "废弃过时实体和查询链接。", "smw-admin-maintenance-script-description-runimport": "填充和导入从[https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs]自动发现的内容。", "smw-admin-maintenance-script-section-update": "更新脚本", "smw-admin-maintenance-script-section-rebuild": "重建脚本", "smw-livepreview-loading": "正在加载...", "smw-sp-searchbyproperty-description": "此页面提供用于查找以属性和命名值描述的实体的简单[https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces 浏览界面]。其他可用搜索界面包括[[Special:PageProperty|页面属性搜索]]和[[Special:Ask|询问查询生成器]]。", "smw-sp-searchbyproperty-resultlist-header": "结果列表", "smw-sp-searchbyproperty-nonvaluequery": "分配有属性“$1”的值的列表。", "smw-sp-searchbyproperty-valuequery": "具有属性“$1”且注解值为“$2”的页面列表。", "smw-datavalue-number-textnotallowed": "“$1”不能分配给已声明的值为$2的数字类型。", "smw-datavalue-number-nullnotallowed": "“$1”返回了“NULL”，它不能当作数字。", "smw-editpage-annotation-enabled": "此页面支持用语义文内注解（如<nowiki>“[[Is specified as::World Heritage Site]]”</nowiki>）来构建语义MediaWiki提供的结构化和可查询内容。要了解注解或#ask解析器函数使用方法的详细说明，请查阅[https://www.semantic-mediawiki.org/wiki/Help:Getting_started 入门]、[https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation 文内注解]或[https://www.semantic-mediawiki.org/wiki/Help:Inline_queries 行内查询]帮助页面。", "smw-editpage-annotation-disabled": "由于命名空间限制，此页面未启用语义文内注解。有关启用命名空间方法的详细信息请查阅[https://www.semantic-mediawiki.org/wiki/Help:Configuration 配置]帮助页面。", "smw-editpage-property-annotation-enabled": "此属性可以使用语义注解扩展，以指定数据类型（如<nowiki>“[[Has type::Page]]”</nowiki>）或其他支持的声明（如<nowiki>“[[Subproperty of::dc:date]]”</nowiki>）。要了解扩展此页面的方法说明，请查阅[https://www.semantic-mediawiki.org/wiki/Help:Property_declaration 属性声明]或[https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes 可用数据类型列表]帮助页面。", "smw-editpage-property-annotation-disabled": "此属性由于已经预定义，无法使用数据类型注解（如<nowiki>“[[Has type::Page]]”</nowiki>）扩展（更多信息请查阅[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 特殊属性]帮助页面）。", "smw-editpage-concept-annotation-enabled": "此概念可以使用#concept解析器函数扩展。要了解#concept的使用方法说明，请查阅[https://www.semantic-mediawiki.org/wiki/Help:Concepts 概念]帮助页面。", "smw-search-syntax-support": "搜索输入支持使用语义[https://www.semantic-mediawiki.org/wiki/Help:Semantic_search 查询语法]，借助语义MediaWiki来匹配结果。", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 输入辅助]也已启用，以方便预选可用的属性和分类。", "smw-search-help-intro": "<code><nowiki>[[ ... ]]</nowiki></code>输入会示意输入处理器使用语义MediaWiki搜索后端。需要注意，不支持组合<code><nowiki>[[ ... ]]</nowiki></code>与非结构化文本搜索，如<code><nowiki>[[ ... ]] OR 任意搜索文本</nowiki></code>不受支持。", "smw-search-help-structured": "结构化搜索：\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>、<code><nowiki>[[Has number::123]]</nowiki></code>（作为[https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context 筛选上下文]）\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code>（使用[https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context 查询上下文]）", "smw-search-help-proximity": "邻近搜索（未知的属性，'''仅'''适用于提供全文搜索集成的后端）：\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code>（在所有文档内搜索已索引的“lorem”和“ipsum”）\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code>（把“lorem ipsum”作为词组来匹配）", "smw-search-help-ask": "以下链接会解释<code>#ask</code>语法的使用方法。\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages 选择页面]描述选择页面和构建条件的方法\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators 搜索运算符]列出可用的搜索运算符，包括用于范围和通配符查询的运算符", "smw-search-input": "输入和搜索", "smw-search-help-input-assistance": "给输入字段提供[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 输入辅助]，需要使用任一以下前缀：\n\n*<code>p:</code>可启用属性建议（如<code><nowiki>[[p:Has ...</nowiki></code>）\n\n*<code>c:</code>可启用分类建议\n\n*<code>con:</code>可启用概念建议", "smw-search-syntax": "语法", "smw-search-profile": "扩展", "smw-search-profile-tooltip": "与语义MediaWiki相关的搜索功能", "smw-search-profile-sort-best": "最佳匹配", "smw-search-profile-sort-recent": "最近", "smw-search-profile-sort-title": "标题", "smw-search-profile-extended-help-intro": "Special:Search[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile 扩展配置文件]提供对语义MediaWiki及其支持的查询后端特有的搜索功能的访问。", "smw-search-profile-extended-help-sort": "指定结果显示的排序参数设置：", "smw-search-profile-extended-help-sort-title": "* “标题”使用页面标题（或显示标题）作为排序标准", "smw-search-profile-extended-help-sort-recent": "* “最近”会首先展示最近修改的实体（子对象实体会被忽略，因为这些实体没有[[Property:Modification date|修改日期]]注解）", "smw-search-profile-extended-help-sort-best": "* “最佳匹配”会根据后端提供的[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy 相关性]评分来排序实体", "smw-search-profile-extended-help-form": "提供表单（若有维护）来匹配特定使用案例，其通过暴露不同的属性和值字段来缩减输入过程，方便用户进行搜索请求。（请查阅$1）", "smw-search-profile-extended-help-namespace": "选择表单后，命名空间选择框会被隐藏，但可以通过“显示/隐藏”按钮使其可见。", "smw-search-profile-extended-help-search-syntax": "搜索输入字段支持使用<code>#ask</code>语法定义语义MediaWiki特有的搜索上下文。有用的表达式包括：", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code>可查找任何含有其后文的内容，当涉及的搜索上下文或属性未知时特别有用（例如<code>in:(lorem && ipsum)</code>等效于<code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>）。", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code>可查找任何含有跟其后文词序完全相同的词组的内容", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code>可匹配任何有其后输入的属性的实体（例如<code>has:(Foo && Bar)</code>等效于<code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>）", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code>可不匹配任何包含其后文的实体", "smw-search-profile-extended-help-search-syntax-prefix": "* 可使用和定义额外的自定义前缀，如：$1", "smw-search-profile-extended-help-search-syntax-reserved": "* 有些表达式被保留，如：<nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''某些列出的操作仅在启用全文索引或ElasticStore时才有用。''", "smw-search-profile-extended-help-query": "已使用<code><nowiki>$1</nowiki></code>作为查询。", "smw-search-profile-extended-help-query-link": "要了解更多详细信息，请使用$1。", "smw-search-profile-extended-help-find-forms": "可用表单", "smw-search-profile-extended-section-sort": "排序方式", "smw-search-profile-extended-section-form": "表单", "smw-search-profile-extended-section-search-syntax": "搜索输入", "smw-search-profile-extended-section-namespace": "命名空间", "smw-search-profile-extended-section-query": "查询", "smw-search-profile-link-caption-query": "查询生成器", "smw-search-show": "显示", "smw-search-hide": "隐藏", "log-name-smw": "语义MediaWiki日志", "log-show-hide-smw": "$1语义MediaWiki日志", "logeventslist-smw-log": "语义MediaWiki日志", "log-description-smw": "语义MediaWiki及其组件报告的[https://www.semantic-mediawiki.org/wiki/Help:Logging 已启用事件类型]的活动。", "logentry-smw-maintenance": "语义MediaWiki发出的维护相关事件", "smw-datavalue-import-unknown-namespace": "导入命名空间“$1”未知。请确保OWL导入详细信息可通过[[MediaWiki:Smw import $1]]获取", "smw-datavalue-import-missing-namespace-uri": "无法在[[MediaWiki:Smw import $1|$1导入]]内找到“$1”命名空间的URI。", "smw-datavalue-import-missing-type": "没有在[[MediaWiki:Smw import $2|$2导入]]内找到“$1”的类型定义。", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1导入]]", "smw-datavalue-import-invalid-value": "“$1”不是有效的格式，预期结构为“命名空间:标识符”（例如“foaf:name”）。", "smw-datavalue-import-invalid-format": "无法理解字符串“$1”的格式，预期应分成四部分。", "smw-property-predefined-impo": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的描述与[https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary 导入词汇]的关系的预定义属性。", "smw-property-predefined-type": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的描述属性的[[Special:Types|数据类型]]的预定义属性。", "smw-property-predefined-sobj": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示[https://www.semantic-mediawiki.org/wiki/Help:Container 容器]构造的预定义属性。", "smw-property-predefined-long-sobj": "该容器允许积累，与普通wiki页面类似但与链接的嵌入主体分属不同实体空间的，属性值分配。", "smw-property-predefined-errp": "“$1”是由[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供，用来追踪不规则值注解的输入错误的预定义属性。", "smw-property-predefined-long-errp": "大多数情况下，这是由于类型误配或[[Property:Allows value|值]]限制造成的。", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value “$1”]是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的定义限制属性值分配的允许值列表的预定义属性。", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list “$1”]是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的指定指向保存限制属性值分配的允许值列表的引用的预定义属性。", "smw-datavalue-property-restricted-annotation-use": "属性“$1”的应用范围受限，用户不能将其用作注解属性。", "smw-datavalue-property-restricted-declarative-use": "属性“$1”是声明性属性，只能在属性或分类页面上使用。", "smw-datavalue-property-create-restriction": "属性“$1”不存在，用户缺少“$2”权限，无法创建未核准的属性或用未核准的属性注解值（请查阅[https://www.semantic-mediawiki.org/wiki/Help:Authority_mode 核准模式]）。", "smw-datavalue-property-invalid-character": "“$1”含有用作属性标签部件的“$2”字符，因此被归类为无效。", "smw-datavalue-property-invalid-chain": "不允许在注解过程中使用“$1”作为属性链。", "smw-datavalue-restricted-use": "数据值“$1”被标记为限制使用。", "smw-datavalue-invalid-number": "“$1”无法理解为数字。", "smw-query-condition-circular": "“$1”内检测到可能存在的循环条件。", "smw-query-condition-empty": "查询描述有空条件。", "smw-types-list": "数据类型列表", "smw-types-default": "“$1”是内置数据类型。", "smw-types-help": "更多信息和示例请查阅[https://www.semantic-mediawiki.org/wiki/Help:Type_$1 帮助页面]。", "smw-type-anu": "“$1”是[[Special:Types/URL|URL]]数据类型的变体，主要用于''owl:AnnotationProperty''导出声明。", "smw-type-boo": "“$1”是描述值的真/假的基本数据类型。", "smw-type-cod": "“$1”是[[Special:Types/Text|文本]]数据类型的变体，用于任意长度的技术文本，如源代码列表。", "smw-type-geo": "“$1”是描述地理位置的数据类型，需要[https://www.semantic-mediawiki.org/wiki/Extension:Maps “Maps”]（地图）扩展提供扩展功能。", "smw-type-tel": "“$1”是根据RFC 3966所述方式描述国际电话号码的特殊数据类型。", "smw-type-txt": "“$1”是描述任意长度字符串的基本数据类型。", "smw-type-dat": "“$1”是以统一格式表示时刻的基本数据类型。", "smw-type-ema": "“$1”是表示电子邮件的特殊数据类型。", "smw-type-tem": "“$1”是表示温度的特殊数字数据类型。", "smw-type-qty": "“$1”是用数字表示与计量单位一起描述数量的数据类型。", "smw-type-rec": "“$1”是以固定顺序指定有类型属性的列表的容器数据类型。", "smw-type-extra-tem": "换算模式包括开尔文、摄氏度、华氏度和兰氏度等支持的单位。", "smw-type-tab-properties": "属性", "smw-type-tab-types": "类型", "smw-type-tab-type-ids": "类型ID", "smw-type-tab-errors": "错误", "smw-type-primitive": "基本", "smw-type-contextual": "上下文", "smw-type-compound": "复合词", "smw-type-container": "容器", "smw-type-no-group": "未归类", "smw-special-pageproperty-description": "此页面提供用于查找属性和给定页面的所有值的浏览界面。其他可用搜索界面包括[[Special:SearchByProperty|属性搜索]]和[[Special:Ask|询问查询生成器]]。", "smw-property-predefined-errc": "“$1”是由[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供，表示与不当值注解或输入处理相关的错误的预定义属性。", "smw-property-predefined-long-errc": "错误会收集到[https://www.semantic-mediawiki.org/wiki/Help:Container 容器]内，其中可能包含指向造成差异的属性的引用。", "smw-property-predefined-errt": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的容纳错误的文字描述的预定义属性。", "smw-subobject-parser-invalid-naming-scheme": "用户定义的子对象含有无效的命名模式。前5个字符内的点符号（$1）是给扩展保留的。您可以设置[https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier 命名标识符]。", "smw-datavalue-record-invalid-property-declaration": "记录定义包含自身声明为记录类型的“$1”属性，这不被允许。", "smw-property-predefined-mdat": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的记录主体的最后修改日期的预定义属性。", "smw-property-predefined-cdat": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的记录主题的首次修订日期的预定义属性。", "smw-property-predefined-newp": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表明主体是否为新增的预定义属性。", "smw-property-predefined-ledt": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的容纳创建最后修订的用户的页面名称的预定义属性。", "smw-property-predefined-mime": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的描述上传文件的MIME类型的预定义属性。", "smw-property-predefined-media": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的描述上传文件的媒体类型的预定义属性。", "smw-property-predefined-askfo": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的保存查询内使用的结果格式名称的预定义属性。", "smw-property-predefined-askst": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的以字符串描述查询条件的预定义属性。", "smw-property-predefined-askdu": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的容纳完成查询执行所需时间值（秒）的预定义属性。", "smw-property-predefined-asksc": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的标识替代（例如远程、联合）查询来源的预定义属性。", "smw-property-predefined-askco": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的描述查询或其组件的状态的预定义属性。", "smw-property-predefined-long-askco": "对分配的表示内部编码状态的数字或编号的说明请查阅[https://www.semantic-mediawiki.org/wiki/Help:Query_profiler 帮助页面]。", "smw-property-predefined-prec": "“$1”是描述数字数据类型的[https://www.semantic-mediawiki.org/wiki/Help:Display_precision 显示精度]（小数位数）的预定义属性。", "smw-property-predefined-attch-link": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的收集页面内嵌入文件和图像的链接的预定义属性。", "smw-property-predefined-inst": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存独立于MediaWiki的分类信息的内部预定义属性。", "smw-property-predefined-unit": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的定义数字类型属性的显示单位的声明性预定义属性。", "smw-property-predefined-long-unit": "可使用半角逗号分隔的列表描述用于显示的单位或格式。", "smw-property-predefined-conv": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的定义物理量单位的换算因数的声明性预定义属性。", "smw-property-predefined-serv": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的添加属性的服务链接的声明性预定义属性。", "smw-property-predefined-redi": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的记录重定向的内部预定义属性。", "smw-property-predefined-subp": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的定义属性的[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of 父属性]的声明性预定义属性。", "smw-property-predefined-subc": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的定义分类的[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of 父分类]的预定义属性。", "smw-property-predefined-conc": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的定义相关概念的内部预定义属性。", "smw-property-predefined-err-type": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的标识一组或一类[https://www.semantic-mediawiki.org/wiki/Help:Processing_errors 处理错误]的预定义属性。", "smw-property-predefined-skey": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的保存排序引用的内部预定义属性。", "smw-property-predefined-pplb": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的指定[https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label 首选属性标签]的声明性预定义属性。", "smw-property-predefined-chgpro": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的保存[https://www.semantic-mediawiki.org/wiki/Help:Change_propagation 更改传播]信息的预定义属性。", "smw-property-predefined-schema-link": "，由[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供。", "smw-property-predefined-format-schema": "，由[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供。", "smw-property-predefined-profile-schema": "，由[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供。", "smw-property-predefined-trans": "，由[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供。", "smw-property-predefined-trans-source": "，由[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供。", "smw-property-predefined-trans-group": "，由[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供。", "smw-property-predefined-cont-len": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存长度信息的预定义属性。", "smw-property-predefined-long-cont-len": "它与[https://www.semantic-mediawiki.org/ElasticStore ElasticStore]（和[https://www.semantic-mediawiki.org/Attachment_processor 附件处理器]）配合使用可收集和储存从摄入文件（若提供）内检索到的长度信息。", "smw-property-predefined-cont-lang": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存语言信息的预定义属性。", "smw-property-predefined-long-cont-lang": "它与[https://www.semantic-mediawiki.org/ElasticStore ElasticStore]（和[https://www.semantic-mediawiki.org/Attachment_processor 附件处理器]）配合使用可收集和储存从摄入文件（若提供）内检索到的语言信息。", "smw-property-predefined-cont-title": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存标题信息的预定义属性。", "smw-property-predefined-long-cont-title": "它与[https://www.semantic-mediawiki.org/ElasticStore ElasticStore]（和[https://www.semantic-mediawiki.org/Attachment_processor 附件处理器]）配合使用可收集和储存从摄入文件（若提供）内检索到的标题信息。", "smw-property-predefined-cont-author": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存作者信息的预定义属性。", "smw-property-predefined-long-cont-author": "它与[https://www.semantic-mediawiki.org/ElasticStore ElasticStore]（和[https://www.semantic-mediawiki.org/Attachment_processor 附件处理器]）配合使用可收集和储存从摄入文件（若提供）内检索到的作者信息。", "smw-property-predefined-cont-date": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存日期信息的预定义属性。", "smw-property-predefined-long-cont-date": "它与[https://www.semantic-mediawiki.org/ElasticStore ElasticStore]（和[https://www.semantic-mediawiki.org/Attachment_processor 附件处理器]）配合使用可收集和储存从摄入文件（若提供）内检索到的日期信息。", "smw-property-predefined-cont-type": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存文件类型信息的预定义属性。", "smw-property-predefined-long-cont-type": "它与[https://www.semantic-mediawiki.org/ElasticStore ElasticStore]（和[https://www.semantic-mediawiki.org/Attachment_processor 附件处理器]）配合使用可收集和储存从摄入文件（若提供）内检索到的类型信息。", "smw-property-predefined-cont-keyw": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示关键词的预定义属性。", "smw-property-predefined-long-cont-keyw": "它与[https://www.semantic-mediawiki.org/ElasticStore ElasticStore]（和[https://www.semantic-mediawiki.org/Attachment_processor 附件处理器]）配合使用可收集和储存从摄入文件（若提供）内检索到的关键词。", "smw-property-predefined-file-attch": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示储存附件信息的容器的预定义属性。", "smw-property-predefined-long-file-attch": "它与[https://www.semantic-mediawiki.org/ElasticStore ElasticStore]（和[https://www.semantic-mediawiki.org/Attachment_processor 附件处理器]）配合使用可收集所有可从摄入文件（若提供）内检索到的内容特有信息。", "smw-types-extra-geo-not-available": "未检测到[https://www.semantic-mediawiki.org/wiki/Extension:Maps “Maps”（地图）扩展]，因此“$1”的运行能力受到限制。", "smw-datavalue-monolingual-dataitem-missing": "预期用于构建单语言复合词的项目丢失。", "smw-datavalue-monolingual-lcode-parenthesis": "（$1）", "smw-datavalue-languagecode-missing": "对于“$1”注解，解析器无法确定语言代码（即“甲@zh”）。", "smw-datavalue-languagecode-invalid": "“$1”不被识别为支持的语言代码。", "smw-property-predefined-lcode": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示BCP47格式语言代码的预定义属性。", "smw-type-mlt-rec": "“$1”是给文本值关联特定[[Property:Language code|语言代码]]的[https://www.semantic-mediawiki.org/wiki/Help:Container 容器]数据类型。", "smw-types-extra-mlt-lcode": "该数据类型{{PLURAL:$2|需要|不需要}}语言代码（即{{PLURAL:$2|不接受没有语言代码的值注解|接受没有语言代码的值注解}}）。", "smw-property-predefined-text": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示任意长度的文本的预定义属性。", "smw-property-predefined-pdesc": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的在语言上下文内描述属性的预定义属性。", "smw-property-predefined-list": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的定义配合[[Special:Types/Record|记录]]类型属性使用的属性的列表的预定义属性。", "smw-limitreport-intext-parsertime": "[SMW]文内注解解析器时间", "smw-limitreport-intext-postproctime": "[SMW]后处理时间", "smw-limitreport-intext-parsertime-value": "$1{{PLURAL:$1|秒}}", "smw-limitreport-intext-postproctime-value": "$1{{PLURAL:$1|秒}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW]存储更新（页面缓存清除）时间", "smw-limitreport-pagepurge-storeupdatetime-value": "$1{{PLURAL:$1|秒}}", "smw_allows_pattern": "此页面应该含有根据[[Property:Allows pattern|允许样板]]属性提供的引用（后跟[https://zh.wikipedia.org/wiki/%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F 正则表达式]）的列表。要编辑此页面，需要<code>smw-patternedit</code>权限。", "smw-datavalue-allows-pattern-mismatch": "“$1”被“$2”正则表达式归类为无效。", "smw-datavalue-allows-pattern-reference-unknown": "“$1”样板引用无法匹配[[MediaWiki:Smw allows pattern]]内的条目。", "smw-datavalue-allows-value-list-unknown": "“$1”列表引用无法匹配[[MediaWiki:Smw allows list $1]]页面。", "smw-datavalue-allows-value-list-missing-marker": "“$1”列表内容缺少有星号（*）列表标记的项目。", "smw-datavalue-feature-not-supported": "此wiki不支持或停用“$1”功能。", "smw-property-predefined-pvap": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的指定应用[https://zh.wikipedia.org/wiki/%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F 正则表达式]匹配的[[MediaWiki:Smw allows pattern|样板引用]]的预定义属性。", "smw-property-predefined-dtitle": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的给实体分配独特显示标题的预定义属性。", "smw-property-predefined-pvuc": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的限制每个实体的值分配必须唯一（或最多有一个值）的预定义属性。", "smw-property-predefined-long-pvuc": "唯一性在两个值的字面表示不相等时建立，任何违反该约束的情形都会被归类为错误。", "smw-datavalue-constraint-uniqueness-violation": "属性“$1”只允许唯一值分配，而''$2''已注解给主体“$3”。", "smw-datavalue-constraint-uniqueness-violation-isknown": "属性“$1”只允许唯一值注解，''$2''已含有分配值。“$3”违反唯一性约束。", "smw-datavalue-constraint-violation-non-negative-integer": "属性“$1”有“非负整数”约束，而值''$2''违反该要求。", "smw-datavalue-constraint-violation-must-exists": "属性“$1”有<code>must_exists</code>（必须存在）约束，而值''$2''违反该要求。", "smw-datavalue-constraint-violation-single-value": "“[[Property:$1|$1]]”属性有<code>single_value</code>（单一值）约束，而值“$2”违反该要求。", "smw-constraint-violation-uniqueness": "“[[Property:$1|$1]]”属性分配有<code>unique_value_constraint</code>（唯一值约束）约束，该约束只允许唯一值分配，而''$2''值注释已注解给“$3”主体。", "smw-constraint-violation-uniqueness-isknown": "“[[Property:$1|$1]]”属性分配有<code>unique_value_constraint</code>（唯一值约束）约束，因此只允许唯一值注释。''$2''已含有注解值“$3”，违反当前主体的唯一性约束。", "smw-constraint-violation-non-negative-integer": "“[[Property:$1|$1]]”属性分配有<code>non_negative_integer</code>（非负整数）约束，而''$2''值注解违反约束要求。", "smw-constraint-violation-must-exists": "“[[Property:$1|$1]]”属性分配有<code>must_exists</code>（必须存在）约束，而''$2''值注解违反约束要求。", "smw-constraint-violation-single-value": "“[[Property:$1|$1]]”属性分配有<code>single_value</code>（单一值）约束，而“$2”值注解违反约束要求。", "smw-constraint-violation-class-shape-constraint-missing-property": "“[[:$1]]”分类分配有键为<code>property</code>（属性）的<code>shape_constraint</code>（形式约束），但缺少必需的“$2”属性。", "smw-constraint-violation-class-shape-constraint-wrong-type": "“[[:$1]]”分类分配有键为<code>property_type</code>（属性类型）的<code>shape_constraint</code>（形式约束），但“$2”属性不匹配“$3”类型。", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "“[[:$1]]”分类分配有键为<code>max_cardinality</code>（最大基数）的<code>shape_constraint</code>（形式约束），但“$2”属性不匹配基数“$3”。", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "“[[:$1]]”分类分配有键为<code>min_textlength</code>（最小文本长度）的<code>shape_constraint</code>（形式约束），但“$2”属性不匹配长度要求“$3”。", "smw-constraint-violation-class-mandatory-properties-constraint": "“[[:$1]]”分类分配有<code>mandatory_properties</code>（强制属性）约束，必需有以下强制属性：$2", "smw-constraint-violation-allowed-namespace-no-match": "“[[Property:$1|$1]]”属性分配有<code>allowed_namespaces</code>（允许命名空间）约束，而“$2”违反命名空间要求，只允许“$3”命名空间。", "smw-constraint-violation-allowed-namespaces-requires-page-type": "<code>allowed_namespaces</code>（允许命名空间）约束必需为页面类型。", "smw-constraint-schema-category-invalid-type": "注解的“$1”模式对于分类无效，必须为“$2”类型。", "smw-constraint-schema-property-invalid-type": "注解的“$1”模式对于属性无效，必须为“$2”类型。", "smw-constraint-error-allows-value-list": "“$1”不在“$3”属性的[[Property:Allows value|允许值]]列表（$2）内。", "smw-constraint-error-allows-value-range": "“$1”不在“$3”属性的[[Property:Allows value|允许值]]约束指定的范围“$2”内。", "smw-property-predefined-boo": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示布尔值的[[Special:Types/Boolean|类型]]和预定义属性。", "smw-property-predefined-num": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示数字值的[[Special:Types/Number|类型]]和预定义属性。", "smw-property-predefined-dat": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示日期值的[[Special:Types/Date|类型]]和预定义属性。", "smw-property-predefined-uri": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示URI/URL值的[[Special:Types/URL|类型]]和预定义属性。", "smw-property-predefined-qty": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示数量值的[[Special:Types/Quantity|类型]]和预定义属性。", "smw-datavalue-time-invalid-offset-zone-usage": "“$1”含有不支持的时差和时区标识符。", "smw-datavalue-time-invalid-values": "值“$1”含有“$2”形式的无法解读的信息。", "smw-datavalue-time-invalid-date-components-common": "“$1”含有一些无法解读的信息。", "smw-datavalue-time-invalid-date-components-dash": "“$1”含有外部破折号或其他对日期解读无效的字符。", "smw-datavalue-time-invalid-date-components-empty": "“$1”含有一些空组件。", "smw-datavalue-time-invalid-date-components-three": "“$1”含有多于3个日期解读所必需的组件。", "smw-datavalue-time-invalid-date-components-sequence": "“$1”含有无法根据日期组件的可用匹配矩阵进行解读的序列。", "smw-datavalue-time-invalid-ampm": "“$1”含有作为小时元素的“$2”，它在12小时制里是无效的。", "smw-datavalue-time-invalid-jd": "无法把输入值“$1”解读为有效的JD（儒略日）数字，报告的结果为“$2”。", "smw-datavalue-time-invalid-prehistoric": "无法解读过早的输入值“$1”。例如，在过早的时间上下文下指定年或日历模型以外的组件可能返回预期外结果。", "smw-datavalue-time-invalid": "无法把输入值“$1”解读为有效的日期或时间组件，报告的结果为“$2”。", "smw-datavalue-external-formatter-uri-missing-placeholder": "格式化URI缺少''$1''占位符。", "smw-datavalue-external-formatter-invalid-uri": "“$1”是无效的URL。", "smw-datavalue-external-identifier-formatter-missing": "属性缺少[[Property:External formatter uri|“外部格式化URI”]]（External formatter URI）分配。", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "“$1”外部标识符应该有多字段替换，但当前值“$2”缺少至少一个匹配要求的值参数。", "smw-datavalue-keyword-maximum-length": "关键词超出$1个{{PLURAL:$1|字符}}的最大长度。", "smw-property-predefined-eid": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表示外部标识符的[[Special:Types/External identifier|类型]]和预定义属性。", "smw-property-predefined-peid": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的指定外部标识符的预定义属性。", "smw-property-predefined-pefu": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的指定有占位符的外部资源的预定义属性。", "smw-property-predefined-long-pefu": "URI应该含有可调整为[[Special:Types/External identifier|外部标识符]]值的占位符，以组成有效的资源引用。", "smw-type-eid": "“$1”是[[Special:Types/Text|文本]]数据类型的变体，描述外部资源（基于URL）并要求被分配的属性声明[[Property:External formatter uri|外部格式化URI]]。", "smw-property-predefined-keyw": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的规范文本并拥有受限字符长度的预定义属性和[[Special:Types/Keyword|类型]]。", "smw-type-keyw": "“$1”是[[Special:Types/Text|文本]]数据类型的变体，拥有受限字符长度和规范化的内容表示形式。", "smw-datavalue-stripmarker-parse-error": "给定值“$1”含有[https://en.wikipedia.org/wiki/Help:Strip_markers 剥除标记]，因此无法充分解析。", "smw-datavalue-parse-error": "无法理解给定的值“$1”。", "smw-datavalue-propertylist-invalid-property-key": "属性列表“$1”含有无效的属性键“$2”。", "smw-datavalue-type-invalid-typeuri": "“$1”类型无法转换为有效的URI表示形式。", "smw-datavalue-wikipage-missing-fragment-context": "wiki页面输入值“$1”不能在没有上下文页面的情况下使用。", "smw-datavalue-wikipage-invalid-title": "页面类型输入值“$1”含有无效字符或不完整，因此会在查询或注解过程中导致预期外结果。", "smw-datavalue-wikipage-property-invalid-title": "输入值为“$2”的属性“$1”（作为页面类型）含有无效字符或不完整，因此会在查询或注解过程中导致预期外结果。", "smw-datavalue-wikipage-empty": "wiki页面输入值为空（如<code>[[SomeProperty::]]、[[]]</code>），因此不能用作名称或查询条件的一部分。", "smw-type-ref-rec": "“$1”是记录有关值分配的额外信息（如起源数据）的[https://www.semantic-mediawiki.org/wiki/Container 容器]类型。", "smw-datavalue-reference-outputformat": "$1：$2", "smw-datavalue-reference-invalid-fields-definition": "[[Special:Types/Reference|引用]]类型应该为使用[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields 有字段]（Has fields）属性声明的属性列表。", "smw-parser-invalid-json-format": "JSON解析器返回了“$1”。", "smw-property-preferred-title-format": "$1（$2）", "smw-property-preferred-label-language-combination-exists": "“$1”不能用作首选标签，因为语言“$2”已分配给“$3”标签。", "smw-clipboard-copy-link": "复制链接到剪贴板", "smw-property-userdefined-fixedtable": "“$1”被配置为[https://www.semantic-mediawiki.org/wiki/Fixed_properties 固定属性]，对其[https://www.semantic-mediawiki.org/wiki/Type_declaration 类型声明]的任何修改都需要运行<code>setupStore.php</code>或完成特殊的[[Special:SemanticMediaWiki|“数据库安装与升级”]]任务。", "smw-data-lookup": "正在获取数据...", "smw-data-lookup-with-wait": "正在处理该请求，可能需要一些时间。", "smw-no-data-available": "没有可用数据。", "smw-property-req-violation-missing-fields": "属性“$1”缺少其“$2”类型所必需的[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>有字段</code>]（<code>Has fields</code>）声明。", "smw-property-req-violation-multiple-fields": "属性“$1”含有多个（冲突的）[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>有字段</code>]（<code>Has fields</code>）声明，其“$2”类型只应有一个该声明。", "smw-property-req-violation-missing-formatter-uri": "由于未能定义<code>外部格式化URI</code>（<code>External formatter URI</code>）属性，属性“$1”缺少注解的类型的声明的详细信息。", "smw-property-req-violation-predefined-type": "作为预定义属性，属性“$1”含有与其默认类型不兼容的“$2”类型声明。", "smw-property-req-violation-import-type": "检测到与导入的“$1”词汇的预定义类型不兼容的类型声明。通常无需声明类型，因为会从导入定义中检索类型信息。", "smw-property-req-violation-change-propagation-locked-error": "属性“$1”已更改，需要使用[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改传播]过程重新评估分配的实体。该属性页面已锁定，直到主要规格更新完成，以防止中途中断或规格相互矛盾。该页面能解锁前，该过程可能持续一段时间，因为其取决于[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 工作队列]调度器的大小和频率。", "smw-property-req-violation-change-propagation-locked-warning": "属性“$1”已更改，需要使用[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改传播]过程重新评估分配的实体。更新可能需要一段时间，因为其取决于[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 工作队列]调度器的大小和频率，建议推迟对该属性的更改，以防止中途中断或规格相互矛盾。", "smw-property-req-violation-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改传播]更新正在等待处理（估计有$1个[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|工作}}]），建议等待该过程完成后再修改属性，以防止中途中断或规格相互矛盾。", "smw-property-req-violation-missing-maps-extension": "语义MediaWiki无法检测到作为先决条件的[https://www.semantic-mediawiki.org/wiki/Extension:Maps “Maps”]（地图）扩展，因此该属性功能受限（即无法储存或处理地理数据）。", "smw-property-req-violation-type": "该属性含有冲突的类型规格，可能导致无效的值注解，因此需要用户分配一个适当的类型。", "smw-property-req-error-list": "该属性含有以下错误或警告：", "smw-property-req-violation-parent-type": "属性“$1”和分配的父属性“$2”有不同的类型注解。", "smw-property-req-violation-forced-removal-annotated-type": "[https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance 强制父类型继承]执行已启用，“$1”属性的注解类型与其父属性的“$2”类型不匹配，并已更改以符合该要求。建议调整页面内的类型定义，以便删除该属性的错误消息和强制执行。", "smw-change-propagation-protection": "此页面已锁定，以防止在[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改传播]更新运行时意外修改数据。该页面解锁前，该过程可能持续一段时间，因为其取决于[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 工作队列]调度器的大小和频率。", "smw-category-change-propagation-locked-error": "分类“$1”已更改，需要使用[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改传播]过程重新评估分配的实体。该分类页面已锁定，直到主要规格更新完成，以防止中途中断或规格相互矛盾。该页面能解锁前，该过程可能持续一段时间，因为其取决于[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 工作队列]调度器的大小和频率。", "smw-category-change-propagation-locked-warning": "分类“$1”已更改，需要使用[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改传播]过程重新评估分配的实体。更新可能需要一段时间，因为其取决于[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 工作队列]调度器的大小和频率，建议推迟对该分类的更改，以防止中途中断或规格相互矛盾。", "smw-category-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改传播]更新正在等待处理（估计有$1个[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|工作}}]），建议等待该过程完成后再修改分类，以防止中途中断或规格相互矛盾。", "smw-category-invalid-value-assignment": "“$1”未被识别为有效的分类或值注解。", "protect-level-smw-pageedit": "仅允许有页面编辑权限的用户（语义MediaWiki）", "smw-create-protection": "[https://www.semantic-mediawiki.org/wiki/Help:Authority_mode 核准模式]启用时，“$1”属性的创建仅限于有适当的“$2”权限（或[https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups 用户组]）的用户。", "smw-create-protection-exists": "[https://www.semantic-mediawiki.org/wiki/Help:Authority_mode 核准模式]启用时，“$1”属性的更改仅限于有适当的“$2”权限（或[https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups 用户组]）的用户。", "smw-edit-protection": "此页面受[[Property:Is edit protected|保护]]以防止意外数据修改，只能被有适当的编辑权限（“$1”）或[https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups 用户组]的用户编辑。", "smw-edit-protection-disabled": "编辑保护已停用，因此“︁$1”︁不能用来保护实体页面免受未经授权的编辑。", "smw-edit-protection-auto-update": "语义MediaWiki已根据“受编辑保护”（Is edit protected）属性更新保护状态。", "smw-edit-protection-enabled": "受编辑保护（语义MediaWiki）", "smw-patternedit-protection": "此页面受保护，只能被有适当的<code>smw-patternedit</code>[https://www.semantic-mediawiki.org/wiki/Help:Permissions 权限]的用户编辑。", "smw-property-predefined-edip": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的表明编辑是否受保护的预定义属性。", "smw-property-predefined-long-edip": "虽然任何用户都有资格给主体添加该属性，但是只有拥有专门权限的用户才能在给实体添加保护后编辑或撤销保护。", "smw-query-reference-link-label": "查询引用", "smw-format-datatable-emptytable": "表格内没有可用数据", "smw-format-datatable-info": "正在显示第_START_至_END_条记录，共_TOTAL_条", "smw-format-datatable-infoempty": "正在显示第0至0条记录，共0条", "smw-format-datatable-infofiltered": "（筛选自全部_MAX_条记录）", "smw-format-datatable-infothousands": ",", "smw-format-datatable-lengthmenu": "显示_MENU_条记录", "smw-format-datatable-loadingrecords": "正在加载...", "smw-format-datatable-processing": "正在处理...", "smw-format-datatable-search": "搜索：", "smw-format-datatable-zerorecords": "没有找到匹配的记录", "smw-format-datatable-first": "首页", "smw-format-datatable-last": "末页", "smw-format-datatable-next": "下页", "smw-format-datatable-previous": "上页", "smw-format-datatable-sortascending": "：激活以按升序排序此列", "smw-format-datatable-sortdescending": "：激活以按降序排序此列", "smw-format-datatable-toolbar-export": "导出", "smw-format-list-other-fields-open": "（", "smw-format-list-other-fields-close": "）", "smw-category-invalid-redirect-target": "分类“$1”含有指向非分类命名空间的无效重定向目标。", "smw-parser-function-expensive-execution-limit": "解析器函数已达到高开销执行的限制（请检查配置参数[https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]）。", "smw-postproc-queryref": "语义MediaWiki正在根据某些必需的查询后处理来刷新当前页面。", "apihelp-smwinfo-summary": "检索语义MediaWiki有关统计信息和其他元信息的API模块。", "apihelp-ask-summary": "使用询问语法查询语义MediaWiki的API模块。", "apihelp-askargs-summary": "使用条件、打印输出和参数列表形式的询问语法查询语义MediaWiki的API模块。", "apihelp-browsebyproperty-summary": "检索属性或属性列表有关信息的API模块。", "apihelp-browsebysubject-summary": "检索主体有关信息的API模块。", "apihelp-smwtask-summary": "执行语义MediaWiki相关任务的API模块（仅供内部使用，不供公开使用）。", "apihelp-smwbrowse-summary": "支持浏览语义MediaWiki内不同实体类型的活动的API模块。", "apihelp-ask-parameter-api-version": "输出格式：\n;2:向后兼容格式，结果列表使用{}。\n;3:实验格式，结果列表使用[]。", "apihelp-smwtask-param-task": "定义任务类型", "apihelp-smwtask-param-params": "匹配所选任务类型要求的JSON编码参数", "smw-apihelp-smwtask-example-update": "给特定主体运行更新任务的示例：", "smw-api-invalid-parameters": "无效参数，“$1”", "smw-parser-recursion-level-exceeded": "解析过程中超出了$1层的递归深度。建议验证模板结构，或在必要时调整配置参数<code>$maxRecursionDepth</code>。", "smw-property-page-list-count": "正在显示$1个使用此属性的{{PLURAL:$1|页面}}。", "smw-property-page-list-search-count": "正在显示$1个使用有“$2”值匹配的此属性的{{PLURAL:$1|页面}}。", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter 搜索筛选]允许包含[https://www.semantic-mediawiki.org/wiki/Help:Query_expressions 查询表达式]，如<code>~</code>或<code>!</code>。选用的[https://www.semantic-mediawiki.org/wiki/Query_engine 查询引擎]可能还支持不区分大小写匹配或其他简短表达式，像是：\n\n* <code>in:</code>的结果会包含查询词，如“<code>in:甲</code>”\n\n* <code>not:</code>的结果不会包含查询词，如“<code>not:乙</code>”", "smw-property-reserved-category": "分类", "smw-category": "分类", "smw-datavalue-uri-invalid-scheme": "“$1”未被列为有效的URI模式。", "smw-datavalue-uri-invalid-authority-path-component": "“$1”被识别为含有无效的“$2”权限或路径组件。", "smw-browse-property-group-title": "属性组合", "smw-browse-property-group-label": "属性组合标签", "smw-browse-property-group-description": "属性组合描述", "smw-property-predefined-ppgr": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的标识用作属性组合实例的实体（主要是分类）的预定义属性。", "smw-filter": "筛选", "smw-section-expand": "展开该部分", "smw-section-collapse": "折叠该部分", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]格式", "smw-help": "帮助", "smw-cheat-sheet": "速查表", "smw-personal-jobqueue-watchlist": "工作队列监视列表", "smw-personal-jobqueue-watchlist-explain": "数字表示等待执行的工作队列条目的估计数。", "smw-property-predefined-label-skey": "排序键", "smw-processing": "正在处理...", "smw-loading": "正在加载...", "smw-fetching": "正在获取...", "smw-preparing": "正在准备...", "smw-expand": "展开", "smw-collapse": "折叠", "smw-copy": "复制", "smw-copy-clipboard-title": "复制内容到剪贴板", "smw-jsonview-expand-title": "展开JSON视图", "smw-jsonview-collapse-title": "折叠JSON视图", "smw-jsonview-search-label": "搜索：", "smw-redirect-target-unresolvable": "目标无法解析，原因是“$1”", "smw-types-title": "类型：$1", "smw-schema-namespace-editcontentmodel-disallowed": "不允许更改[https://www.semantic-mediawiki.org/wiki/Help:Schema 模式页面]的内容模型。", "smw-schema-namespace-edit-protection": "此页面受保护，只能被有适当的<code>smw-schemaedit</code>[https://www.semantic-mediawiki.org/wiki/Help:Permissions 权限]的用户编辑。", "smw-schema-namespace-edit-protection-by-import-performer": "此页面由列出的[https://www.semantic-mediawiki.org/wiki/Import_performer 导入执行者]导入。这意味着只有列出的用户才能更改此页面的内容。", "smw-schema-error-title": "验证{{PLURAL:$1|错误}}", "smw-schema-error-schema": "验证模式'''$1'''找到以下不一致：", "smw-schema-error-miscellaneous": "其他错误（$1）", "smw-schema-error-validation-json-validator-inaccessible": "JSON验证器“<b>$1</b>”无法访问（或未安装），因此无法检查“$2”文件，这会阻止当前页面被保存或更改。", "smw-schema-error-validation-file-inaccessible": "无法访问验证文件“$1”。", "smw-schema-error-violation": "[“$1”，“$2”]", "smw-schema-error-type-missing": "内容缺少在[https://www.semantic-mediawiki.org/wiki/Help:Schema 模式命名空间]内识别和使用它所需的类型。", "smw-schema-error-type-unknown": "“$1”类型未注册，不能用于[https://www.semantic-mediawiki.org/wiki/Help:Schema SMW/模式]命名空间内的内容。", "smw-schema-error-json": "JSON错误：“$1”", "smw-schema-error-input": "输入验证找到以下问题，需要在保存内容前解决这些问题。[https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling 模式帮助]页面可能会提供一些消除不一致或解决模式输入问题方法的建议。", "smw-schema-error-input-schema": "验证模式'''$1'''找到以下不一致，需要在保存内容前解决它们。[https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling 模式帮助]页面可能会提供一些解决这类问题方法的建议。", "smw-schema-error-title-prefix": "此模式类型要求模式标题以“$1”前缀开头。", "smw-schema-validation-error": "“$1”类型未注册，不能用于[https://www.semantic-mediawiki.org/wiki/Help:Schema SMW/模式]命名空间内的内容。", "smw-schema-validation-schema-title": "JSON模式", "smw-schema-summary-title": "摘要", "smw-schema-title": "模式", "smw-schema-usage": "使用情况", "smw-schema-type": "模式类型", "smw-schema-type-description": "类型描述", "smw-schema-description": "模式描述", "smw-schema-description-link-format-schema": "此模式类型支持定义用于创建与[[Property:Formatter schema|格式化模式]]分配的属性相关的上下文敏感链接的特征。", "smw-schema-description-search-form-schema": "此模式类型支持定义[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch 扩展搜索]配置文件的输入形式和特征，其中含有生成输入字段、定义默认命名空间、声明搜索请求的前缀表达式的方法说明。", "smw-schema-description-property-profile-schema": "此模式类型支持定义配置文件，可声明分配的属性及其注解值的特征。", "smw-schema-description-facetedsearch-profile-schema": "此模式类型支持定义用于组成[[Special:FacetedSearch|多面搜索]]环境的配置文件。", "smw-schema-description-property-group-schema": "此模式类型支持定义[https://www.semantic-mediawiki.org/wiki/Help:Property_group 属性组合]，可帮助组织[https://www.semantic-mediawiki.org/wiki/Help:Special:Browse 浏览]界面结构。", "smw-schema-description-property-constraint-schema": "这支持定义属性实例以及分配给它的值的约束规则。", "smw-schema-description-class-constraint-schema": "此模式类型支持定义类实例（也称为分类）的约束规则。", "smw-schema-tag": "{{PLURAL:$1|标记}}", "smw-property-predefined-constraint-schema": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的定义约束模式的预定义属性。", "smw-property-predefined-schema-desc": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存模式描述的预定义属性。", "smw-property-predefined-schema-def": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的储存模式内容的预定义属性。", "smw-property-predefined-schema-tag": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的标识模式集合的预定义属性。", "smw-property-predefined-long-schema-tag": "即标识有相似内容或特征的模式的标签。", "smw-property-predefined-schema-type": "“$1”是[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 语义MediaWiki]提供的描述用于区分模式组合的类型的预定义属性。", "smw-property-predefined-long-schema-type": "每个[https://www.semantic-mediawiki.org/wiki/Help:Schema/Type 类型]都提供自己的语法元素和应用规则的表达方式，并可借助[https://www.semantic-mediawiki.org/wiki/Help:Schema#validation 验证模式]来表达。", "smw-ask-title-keyword-type": "关键词搜索", "smw-ask-message-keyword-type": "此搜索匹配<code><nowiki>$1</nowiki></code>条件。", "smw-remote-source-unavailable": "无法连接远程“$1”目标。", "smw-remote-source-disabled": "'''$1'''来源已禁用远程请求支持！", "smw-remote-source-unmatched-id": "'''$1'''来源不匹配支持远程请求的语义MediaWiki版本。", "smw-remote-request-note": "结果获取自'''$1'''远程来源，生成的内容很可能含有当前wiki内无法提供的信息。", "smw-remote-request-note-cached": "结果获取自'''$1'''远程来源'''缓存'''，生成的内容很可能含有当前wiki内无法提供的信息。", "smw-parameter-missing": "缺少参数“$1”。", "smw-property-tab-usage": "使用情况", "smw-property-tab-profile-schema": "配置文件模式", "smw-property-tab-redirects": "同义词", "smw-property-tab-subproperties": "子属性", "smw-property-tab-errors": "不当分配", "smw-property-tab-constraint-schema": "约束模式", "smw-property-tab-constraint-schema-title": "编译的约束模式", "smw-property-tab-specification": "...更多", "smw-concept-tab-list": "列表", "smw-concept-tab-errors": "错误", "smw-ask-tab-result": "结果", "smw-ask-tab-extra": "额外", "smw-ask-tab-debug": "调试", "smw-ask-tab-code": "代码", "smw-install-incomplete-tasks-title": "未完成的管理任务", "smw-install-incomplete-intro": "要完成[https://www.semantic-mediawiki.org 语义MediaWiki]的{{PLURAL:$1|安装|升级}}，还有$2个未完成或[[Special:PendingTaskList|待处理]]的{{PLURAL:$2|任务}}。管理员或有足够权限的用户可以完成{{PLURAL:$2|它|它们}}。这应该在添加新数据之前完成，以避免数据不一致。", "smw-install-incomplete-intro-note": "此消息会在所有相关任务解决后消失。", "smw-pendingtasks-intro-empty": "没有与语义MediaWiki相关的归类为待处理、未完成或未解决的任务。", "smw-pendingtasks-intro": "此页面提供与语义MediaWiki相关的归类为待处理、未完成或未解决的任务的有关信息。", "smw-pendingtasks-setup-no-tasks-intro": "安装（或升级）已完成，目前没有待处理或未解决的任务。", "smw-pendingtasks-tab-setup": "设置", "smw-updateentitycollation-incomplete": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code>设置最近被更改，必需执行<code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code>脚本，以便更新实体并使其含有正确的排序字段值。", "smw-updateentitycountmap-incomplete": "<code>smw_countmap</code>字段在最近发行版中添加，必需执行<code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code>脚本，以便功能能够访问该字段的内容。", "smw-populatehashfield-incomplete": "<code>smw_hash</code>字段填充在设置过程中跳过。必需执行<code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>脚本。", "smw-install-incomplete-populate-hash-field": "<code>smw_hash</code>字段填充在设置过程中跳过。必需执行<code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>脚本。", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code>已被选为[https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore 默认存储]，但是扩展无法找到任何执行<code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code>脚本的记录，请按说明运行该脚本。", "smw-elastic-rebuildelasticindex-run-incomplete": "<code>ElasticStore</code>已被选为[https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore 默认存储]，但是扩展无法找到任何执行<code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code>脚本的记录，请按说明运行该脚本。", "smw-pendingtasks-setup-intro": "<b>语义MediaWiki</b>的{{PLURAL:$1|安装|升级}}已把以下任务归类为[https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade 未完成]，管理员（或有足够权限的用户）应该在用户继续创建或更改内容之前解决这些任务。", "smw-pendingtasks-setup-tasks": "任务", "smw-filter-count": "筛选数", "smw-es-replication-check": "复制检查（Elasticsearch）", "smw-es-replication-error": "Elasticsearch复制问题", "smw-es-replication-file-ingest-error": "文件摄入问题", "smw-es-replication-maintenance-mode": "Elasticsearch维护", "smw-es-replication-error-missing-id": "复制监控发现Elasticsearch后端缺少条目“$1”（ID：$2）。", "smw-es-replication-error-divergent-date": "复制监控发现条目“$1”（ID：$2）的<b>修改日期</b>存在差异。", "smw-es-replication-error-divergent-date-short": "以下是用于对比的日期信息：", "smw-es-replication-error-divergent-date-detail": "引用的修改日期：\n*Elasticsearch：$1\n*数据库：$2", "smw-es-replication-error-divergent-revision": "复制监控发现条目“$1”（ID：$2）的<b>关联修订</b>存在差异。", "smw-es-replication-error-divergent-revision-short": "以下是用于对比的关联修订数据：", "smw-es-replication-error-divergent-revision-detail": "引用的关联修订：\n*Elasticsearch：$1\n*数据库：$2", "smw-es-replication-error-maintenance-mode": "Elasticsearch复制目前受到限制，因为它在[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>维护模式</b>]下运行，对实体和页面的更改<b>不会</b>立即可见，查询结果可能含有过时信息。", "smw-es-replication-error-no-connection": "复制监控无法执行任何检查，因为它无法与Elasticsearch集群建立连接。", "smw-es-replication-error-bad-request-exception": "Elasticsearch连接处理程序抛出了错误请求异常（“400 Bad Request错误”），表明复制和搜索请求期间存在持续问题。", "smw-es-replication-error-other-exception": "Elasticsearch连接处理程序抛出了异常：“$1”。", "smw-es-replication-error-suggestions": "建议编辑页面或清除页面缓存以消除差异。如果问题仍然存在，请检查Elasticsearch集群本身（分配器、异常、磁盘空间等）。", "smw-es-replication-error-suggestions-maintenance-mode": "建议联系wiki管理员检查[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild 索引重建]当前是否正在进行或<code>refresh_interval</code>是否没有设置为预期的默认值。", "smw-es-replication-error-suggestions-no-connection": "建议联系wiki管理员并报告“无连接”问题。", "smw-es-replication-error-suggestions-exception": "请检查日志获取Elasticsearch状态、索引，以及可能存在的错误配置问题的有关信息。", "smw-es-replication-error-file-ingest-missing-file-attachment": "复制监控发现“$1”缺少[[Property:File attachment|文件附件]]（File attachment）注解，表明文件摄入处理器尚未启动或尚未完成处理。", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "请确保[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion 文件摄入]工作在注解和文件索引可用之前安排并执行。", "smw-report": "报告", "smw-legend": "说明", "smw-datavalue-constraint-schema-category-invalid-type": "注解的“$1”模式对于分类无效，必须为“$2”类型。", "smw-datavalue-constraint-schema-property-invalid-type": "注解的“$1”模式对于属性无效，必须为“$2”类型。", "smw-entity-examiner-check": "在后台运行{{PLURAL:$1|检查器}}", "smw-entity-examiner-indicator": "实体问题面板", "smw-entity-examiner-deferred-check-awaiting-response": "“$1”检查器目前正在等待后端的响应。", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "约束", "smw-entity-examiner-associated-revision-mismatch": "修订", "smw-entity-examiner-deferred-fake": "虚假", "smw-entity-examiner-indicator-suggestions": "作为实体检查的一部分，发现了以下{{PLURAL:$1|问题}}，建议仔细检查{{PLURAL:$1|该问题|这些问题}}并采取适当的{{PLURAL:$1|操作}}。", "smw-indicator-constraint-violation": "{{PLURAL:$1|约束}}", "smw-indicator-revision-mismatch": "修订", "smw-indicator-revision-mismatch-error": "[https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner 关联修订]检查发现，MediaWiki内引用的修订与语义MediaWiki内给该实体关联的修订误配。", "smw-indicator-revision-mismatch-comment": "误配通常表明某些进程中断了语义MediaWiki内的存储操作。建议检查服务器日志并查找异常或其他故障。", "smw-facetedsearch-intro-text": "语义MediaWiki的[https://www.semantic-mediawiki.org/wiki/Faceted_search <b>多面搜索</b>]给用户提供简单界面，可借助创建自从属属性和分类的多面视图快速缩小特定查询条件的结果范围。", "smw-facetedsearch-intro-tips": "* 使用<code>category:?</code>、<code>property:?</code>、<code>concept:?</code>可分别查找可用的构建条件集合的分类、属性、概念\n* 使用#ask语法可描述条件（例如<code><nowiki>[[Category:Foo]]</nowiki></code>）\n* 使用“OR”“AND”或其他查询表达式可创建复杂条件\n* 当选用的[https://www.semantic-mediawiki.org/wiki/Query_engine 查询引擎]支持时，<code>in:</code>、<code>phrase:</code>等表达式可用于全文匹配或非结构化搜索", "smw-facetedsearch-profile-label-default": "默认配置文件", "smw-facetedsearch-intro-tab-explore": "探索", "smw-facetedsearch-intro-tab-search": "搜索", "smw-facetedsearch-explore-intro": "选择集合，开始浏览。", "smw-facetedsearch-profile-options": "配置文件选项", "smw-facetedsearch-size-options": "分页选项", "smw-facetedsearch-order-options": "顺序选项", "smw-facetedsearch-format-options": "显示选项", "smw-facetedsearch-format-table": "表格", "smw-facetedsearch-input-filter-placeholder": "筛选...", "smw-facetedsearch-no-filters": "没有筛选。", "smw-facetedsearch-no-filter-range": "没有筛选范围。", "smw-facetedsearch-no-output": "对于所选的“$1”格式，没有可用的输出。", "smw-facetedsearch-clear-filters": "清除{{PLURAL:$1|筛选}}", "smw-search-placeholder": "搜索...", "smw-listingcontinuesabbrev": "续", "smw-showingresults": "下面正在显示从第<strong>$2</strong>条结果开始的{{PLURAL:$1|<strong>1</strong>|<strong>$1</strong>}}条结果。"}