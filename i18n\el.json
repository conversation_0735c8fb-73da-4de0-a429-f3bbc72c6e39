{"@metadata": {"authors": ["Consta", "Crazymadlover", "FocalPoint", "<PERSON><PERSON><PERSON>", "Glavkos", "KATRINE1992", "Kghbln", "<PERSON>", "McDut<PERSON><PERSON>", "Nemo bis", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Omnipaedista", "Protnet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ZaD<PERSON>k", "Ανώνυ<PERSON><PERSON>παιδιστής"]}, "smw-desc": "Κάνοντας το wiki σας πιο προσιτό - σε μηχανές ''και'' ανθρώπους ([https://www.semantic-mediawiki.org/wiki/Help:User_manual ηλεκτρονική τεκμηρίωση])", "smw-error": "Σφάλμα", "smw-upgrade-release": "Κυκλοφορία", "smw-upgrade-progress": "Πρ<PERSON><PERSON><PERSON>ος", "smw-upgrade-progress-explain": "Η εκτίμηση ως προς το πότε θα ολοκληρωθεί η αναβάθμιση είναι δύσκολο να προβλεφθεί, κα<PERSON><PERSON><PERSON> εξαρτάται από το μέγεθος του αποθετηρίου δεδομένων και τη διαθέσιμη υλική υποδομή και μπορεί να πάρει λίγο χρόνο για να ολοκληρωθούν μεγαλύτερα wiki.\n\nΕπικοινωνήστε με τον τοπικό σας διαχειριστή για να λάβετε περισσότερες πληροφορίες σχετικά με την πρόοδο.", "smw-upgrade-progress-create-tables": "Γίνεται δημιουργία (ή ενημέρωση) πινάκων και ευρετηρίων ...", "smw-upgrade-progress-post-creation": "Γίνεται εκτέλεση εργασιών που έπονται της δημιουργίας ...", "smw-upgrade-progress-table-optimization": "Γίνεται εκτέλεση βελτιστοποίησης πινάκων ...", "smw-upgrade-progress-supplement-jobs": "Γίνεται προσθήκη συμπληρωματικών εργασιών ...", "smw-upgrade-error-title": "Σφάλμα » Σημασιολογικό MediaWiki", "smw-upgrade-error-why-title": "Για<PERSON><PERSON> βλέπω αυτήν τη σελίδα;", "smw-upgrade-error-how-title": "<PERSON><PERSON><PERSON> διορθώνω αυτό το σφάλμα;", "smw-extensionload-error-why-title": "Για<PERSON><PERSON> βλέπω αυτήν τη σελίδα;", "smw-extensionload-error-how-title": "<PERSON><PERSON><PERSON> διορθώνω αυτό το σφάλμα;", "smw-upgrade-maintenance-title": "Συντήρηση » Σημασιολογικό MediaWiki", "smw-upgrade-maintenance-why-title": "Για<PERSON><PERSON> βλέπω αυτήν τη σελίδα;", "smw-semantics-not-enabled": "Η λειτουργικότητα Σημασιολογικού MediaWiki δεν είναι ενεργοποιημένη για αυτό το wiki.", "smw_viewasrdf": "Ροή RDF", "smw_finallistconjunct": " και", "smw-factbox-head": "... περισσότερα σχετικά με το «$1»", "smw-factbox-facts": "Γεγονότα", "smw_isspecprop": "Αυτή η ιδιότητα είναι μια ειδική ιδιότητα σε αυτό το wiki.", "smw-concept-cache-header": "Χρήση προσωρινής μνήμης", "smw-concept-cache-count": "Η [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count προσωρινή μνήμη έννοιας] περιέχει {{PLURAL:$1|'''μία''' οντότητα|'''$1''' οντότητες}} ($2).", "smw-concept-no-cache": "Δεν υπάρχει προσωρινή μνήμη διαθέσιμη.", "smw_concept_description": "Περιγραφή της έννοιας «$1»", "smw_no_concept_namespace": "Έννοιες μπορούν να οριστούν μόνο σε σελίδες στον ονοματοχώρο «Έννοια:».", "smw_multiple_concepts": "Κάθε έννοια μπορεί να έχει μόνο έναν ορισμό έννοιας.", "smw_concept_cache_miss": "Η έννοια «$1» δεν μπορεί να χρησιμοποιηθεί τη στιγμή, επειδή η ρύθμιση παραμέτρων του wiki απαιτεί να υπολογιστεί εκτός σύνδεσης.\nΕάν μετά την πάροδο κάποιου χρόνου το πρόβλημα δεν έχει λυθεί, ζητήστε από το διαχειριστή του ιστοχώρου να κάνει αυτήν την έννοια διαθέσιμη.", "smw_noinvannot": "Δεν μπορούν να δοθούν τιμές σε αντίστροφες ιδιότητες.", "version-semantic": "Σημασιολογι<PERSON><PERSON>ς επεκτάσεις", "smw_baduri": "Δεν επιτρέπονται URI της μορφής «$1».", "smw_printername_count": "Καταμέτρηση", "smw_printername_csv": "Εξαγωγή σε CSV", "smw_printername_dsv": "Εξαγωγή σε DSV", "smw_printername_debug": "Ερώτημα αποσφαλμάτωσης (για ειδικούς)", "smw_printername_embedded": "Ενσωμάτωση περιεχομένων σελίδων", "smw_printername_json": "Εξαγωγή σε JSON", "smw_printername_list": "Λίστα", "smw_printername_ol": "Αριθμημένη λίστα", "smw_printername_ul": "Λίστα με κουκκίδες", "smw_printername_table": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "smw_printername_broadtable": "Πί<PERSON><PERSON><PERSON><PERSON>ς πλήρους εύρους", "smw_printername_template": "Πρότυπο", "smw_printername_templatefile": "Αρχ<PERSON><PERSON><PERSON> προτύπου", "smw_printername_rdf": "Εξαγωγή σε RDF", "smw_printername_category": "Κατηγορία", "validator-type-class-SMWParamSource": "κείμενο", "smw-paramdesc-limit": "Ο μέγιστος αριθμός αποτελεσμάτων που θα επιστραφούν", "smw-paramdesc-offset": "Η μετατόπιση του πρώτου αποτελέσματος", "smw-paramdesc-headers": "Προβολή των κεφαλίδων/ονομ<PERSON>των ιδιοτήτων", "smw-paramdesc-mainlabel": "Η ετικέτα που θα δοθεί στο όνομα της κύριας σελίδας", "smw-paramdesc-link": "Εμφάνιση τιμών ως σύνδεσμοι", "smw-paramdesc-intro": "Κείμενο που θα εμφανίζεται πριν από τα αποτελέσματα του ερωτήματος, αν υπάρχουν", "smw-paramdesc-outro": "Κείμενο που θα εμφανίζεται μετά από τα αποτελέσματα του ερωτήματος, αν υπάρχουν", "smw-paramdesc-default": "Κείμενο που θα εμφανίζ<PERSON>τ<PERSON><PERSON> αν δεν υπάρχουν αποτελέσματα ερωτήματος", "smw-paramdesc-sep": "Διαχωριστικ<PERSON> μεταξύ αποτελεσμάτων", "smw-paramdesc-showsep": "Εμφάνιση διαχωριστικού στην αρχή του αρχείου CSV (\"sep=<value>\")", "smw-paramdesc-distribution": "Αντί για προβολή όλων των τιμών, καταμέτρηση των εμφανίσεών τους και προβολή αυτών.", "smw-paramdesc-distributionsort": "Ταξινόμηση της κατανομής τιμών βάσει πλήθους εμφανίσεων.", "smw-paramdesc-distributionlimit": "Περιορισ<PERSON><PERSON>ς της κατανομής τιμών σε πλήθος μόνο κάποιων τιμών.", "smw-paramdesc-template": "Το όνομα προτύπου με το οποίο να εμφανίζονται οι εκτυπώσεις", "smw-paramdesc-columns": "Ο αριθμός των στηλών κειμένου που θα έχει το αποτέλεσμα", "smw-paramdesc-userparam": "Τιμή που θα περνιέται σε κάθε κλήση πρoτύπου, αν χρησιμοποιείται πρότυπο", "smw-paramdesc-class": "Μια πρόσθετη κλάση CSS που θα οριστεί για τη λίστα", "smw-paramdesc-introtemplate": "Όνομα προτύπου που θα εμφανίζεται πριν από τα αποτελέσματα του ερωτήματος, αν υπάρχουν", "smw-paramdesc-outrotemplate": "Όνομα προτύπου που θα εμφανίζεται μετά από τα αποτελέσματα του ερωτήματος, αν υπάρχουν", "smw-paramdesc-embedformat": "Ετικέτα HTML που χρησιμοποιείται για τον ορισμό επικεφαλίδων", "smw-paramdesc-embedonly": "Μη εμφάνιση επικεφαλίδων", "smw-paramdesc-table-class": "Μια πρόσθετη κλάση CSS για αυτόν τον πίνακα", "smw-paramdesc-table-transpose": "Παρουσ<PERSON>α<PERSON>η των κεφαλίδων του πίνακα κατακόρυφα και των αποτελεσμάτων οριζόντια", "smw-paramdesc-rdfsyntax": "Σύνταξη RDF που θα χρησιμοποιηθεί", "smw-paramdesc-csv-sep": "Διαχωριστικ<PERSON> που θα χρησιμοποιηθεί", "smw-paramdesc-csv-valuesep": "Διαχωρίζει τις τιμές", "smw-paramdesc-csv-merge": "Σύμπτυξη σειρών και τιμών στηλών με έναν παρόμοιο δείκτη αναγνώρισης θέματος (aka πρώτη στήλη)", "smw-paramdesc-csv-bom": "Προσθέστε ένα <PERSON> (χαρακτήρα σήματος που στάλθηκε) στο πάνω μέρος του φακέλλου παραγωγής", "smw-paramdesc-dsv-separator": "Διαχωριστικ<PERSON> που θα χρησιμοποιηθεί", "smw-paramdesc-dsv-filename": "Όνομα αρχείου DSV", "smw-paramdesc-filename": "Το όνομα για το αρχείο εξόδου", "smw-smwdoc-description": "Εμφανίζει έναν πίνακα με όλες τις παραμέτρους που μπορούν να χρησιμοποιηθούν για τη μορφή αποτελέσματος που έχει οριστεί μαζί με προεπιλεγμένες τιμές και περιγραφές.", "smw-smwdoc-par-format": "Μορφή αποτελέσματος για την οποία να προβληθεί τεκμηρίωση παραμέτρων.", "smw-smwdoc-par-parameters": "Ποιες παράμετροι να εμφανίζονται. «ειδικές» για όσες προστίθενται από τη μορφή, «βασικές» για όσες είναι διαθέσιμες σε όλες τις μορφές και «όλες» και για τα δύο.", "smw-paramdesc-sort": "Ιδιότητα βάσει της οποίας να ταξινομηθεί το ερώτημα", "smw-paramdesc-order": "Σειρά ταξινόμησης του ερωτήματος", "smw-paramdesc-searchlabel": "Κείμενο για συνέχιση της αναζήτησης", "smw-paramdesc-named_args": "Ονοματο<PERSON><PERSON><PERSON><PERSON><PERSON> στα ορίσματα που πασάρονται στο πρότυπο", "smw-paramdesc-template-arguments": "Ορίζει τον τρόπο με τον οποίο θα μεταβιβάζονται στο πρότυπο τα επώνυμα ορίσματα.", "smw-paramdesc-export": "Επιλογή εξαγωγής", "smw-paramdesc-prettyprint": "Όμορφα μορφοποιη<PERSON><PERSON><PERSON>ος κώδικας που εμφανίζει πρόσθετες εσοχές και αλλαγές γραμμής", "smw-paramdesc-source": "Εναλλακτική προέλευση ερωτήματος", "smw-paramdesc-jsonsyntax": "Σύνταξη JSON που θα χρησιμοποιηθεί", "smw-printername-feed": "Ροές RSS και Atom", "smw-paramdesc-feedtype": "Τ<PERSON><PERSON><PERSON> ροής", "smw-paramdesc-feedtitle": "Κείμενο που θα χρησιμοποιείται ως τίτλος της ροής", "smw-paramdesc-feeddescription": "Κείμενο που θα χρησιμοποιείται ως περιγραφή της ροής", "smw-paramdesc-feedpagecontent": "Περιεχόμεν<PERSON> σελίδων που θα εμφανίζεται με τη ροή", "smw-label-feed-description": "ροή $2 για $1", "smw-paramdesc-mimetype": "Ο τύπος πολυμέσων (τύπος MIME) για το αρχείο εξόδου", "smw_iq_disabled": "Τα σημασιολογι<PERSON>ά ερωτήματα έχουν απενεργοποιηθεί για αυτό το wiki.", "smw_iq_moreresults": "... περισσότερα αποτελέσματα", "smw_parseerror": "Η τιμή που δόθηκε δεν έγινε κατανοητή.", "smw_notitle": "Το «$1» δεν μπορεί να χρησιμοποιηθεί σαν όνομα σελίδας σε αυτό το wiki.", "smw_noproperty": "Το «$1» δεν μπορεί να χρησιμοποιηθεί σαν όνομα ιδιότητας σε αυτό το wiki.", "smw_wrong_namespace": "Μόνο οι σελίδες που ανήκουν στον ονοματοχώρο «$1» επιτρέπονται εδώ.", "smw_manytypes": "Έχουν οριστεί περισσότεροι από έναν τύποι για την ιδιότητα.", "smw_emptystring": "<PERSON><PERSON><PERSON><PERSON><PERSON> συμβολοσειρές δεν γίνονται δεκτές.", "smw_notinenum": "Το «$1» δεν είναι στη λίστα ($2) [[Property:Allows value|επιτρεπτών τιμών]] για την ιδιότητα «$3».", "smw-datavalue-constraint-error-allows-value-list": "Το «$1» δεν περιλαμβάνεται στη λίστα ($2) με τις [[Property:Allows value|επιτρεπόμενες τιμές]] για την ιδιότητα «$3».", "smw-datavalue-constraint-error-allows-value-range": "Το «$1» δεν βρίσκεται εντός του εύρους «$2» που καθορίζεται από τον περιορισμό [[Property:Allows value|επιτρεπτών τιμών]] για την ιδιότητα «$3».", "smw_noboolean": "Το «$1» δεν αναγνωρίζεται ως Μπουλιανή (αληθές/ψευδές) τιμή.", "smw_true_words": "αληθές,α,ναι,ν", "smw_false_words": "ψευδές,ψ,όχι,ο", "smw_nofloat": "Το «$1» δεν είναι αριθμός.", "smw_infinite": "Δεν υποστηρίζονται τόσο μεγάλοι αριθμοί σαν τον «$1».", "smw_unitnotallowed": "Το «$1» δεν έχει δηλωθεί ως έγκυρη μονάδα μέτρησης για αυτήν την ιδιότητα.", "smw_nounitsdeclared": "Δεν έχουν δηλωθεί μονάδες μέτρησης για αυτήν την ιδιότητα.", "smw_novalues": "Δεν καθορίστηκαν τιμές.", "smw_nodatetime": "Η ημερομηνία «$1» δεν έγινε κατανοητή.", "smw_toomanyclosing": "Φαίνε<PERSON><PERSON><PERSON> ότι το «$1» εμφανίζεται πάρα πολλές φορές μέσα στο ερώτημα.", "smw_noclosingbrackets": "Κάποιες αγκύλες «<nowiki>[[</nowiki>» στο ερώτημά σας δεν έχουν κλείσει με αντίστοιχο «]]».", "smw_misplacedsymbol": "Το σύμβολο «$1» χρησιμοποιήθηκε σε σημείο όπου δεν έχει κάποια χρησιμότητα.", "smw_unexpectedpart": "Το τμήμα «$1» του ερωτήματος δεν έγινε κατανοητό.\nΤα αποτελέσματα ενδέχεται να μην είναι τα αναμενόμενα.", "smw_emptysubquery": "Κάποιο υποερώτημα δεν έχει έγκυρη συνθήκη.", "smw_misplacedsubquery": "Κάποιο υποερώτημα χρησιμοποιήθηκε σε σημείο όπου δεν επιτρέπονται υποερωτήματα.", "smw_valuesubquery": "Δεν υποστηρίζονται υποερωτήματα για τιμές της ιδιότητας «$1».", "smw_badqueryatom": "Κάποιο τμήμα «<nowiki>[[…]]</nowiki>» του ερωτήματος δεν έγινε κατανοητό.", "smw_propvalueproblem": "Η τιμή της ιδιότητας «$1» δεν έγινε κατανοητή.", "smw_noqueryfeature": "Κάποια δυνατότητα των ερωτημάτων δεν υποστηρίζεται σε αυτό το wiki και ένα μέρος του ερωτήματος έχει κοπεί ($1).", "smw_noconjunctions": "Η λογική σύζευξη ερωτημάτων δεν υποστηρίζεται σε αυτό το wiki και ένα μέρος του ερωτήματος έχει κοπεί ($1).", "smw_nodisjunctions": "Η λογική διάζευξη ερωτημάτων δεν υποστηρίζεται σε αυτό το wiki και ένα μέρος του ερωτήματος έχει κοπεί ($1).", "smw_querytoolarge": "{{PLURAL:$2|Η ακόλουθη συνθήκη του ερωτήματος δεν μπόρεσε να ληφθεί|Οι ακόλουθες συνθήκες του ερωτήματος δεν μπόρεσαν να ληφθούν}} υπόψιν λόγω των περιορισμών μεγέθους ή βάθους ερωτήματος αυτού του wiki: <code>$1</code>.", "smw_notemplategiven": "Δώστε μια τιμή για την παράμετρο «πρότυπο» για αυτήν τη μορφή ερωτήματος για να λειτουργήσει.", "smw_db_sparqlqueryproblem": "Το αποτέλεσμα του ερωτήματος δεν ήταν δυνατόν να ληφθεί από τη βάση δεδομένων SPARQL. Αυτό το σφάλμα μπορεί να είναι προσωρινό ή να δηλώνει την ύπαρξη προγραμματιστικού σφάλματος στο λογισμικό της βάσης δεδομένων.", "smw_db_sparqlqueryincomplete": "Το να απαντηθεί το ερώτημα αποδείχτηκε πολύ δύσκολο και η εκτέλεση δεν ολοκληρώθηκε. Με<PERSON>ι<PERSON><PERSON> αποτελέσματα μπορεί να είναι ελλειπή. Εάν είναι δυνατόν, προσπαθήστε να χρησιμοποιήσετε απλούστερο ερώτημα.", "smw_type_header": "Ιδιότητες τύπου «$1»", "smw_typearticlecount": "Εμφάνιση $1 {{PLURAL:$1|ιδιότητας|ιδιοτήτων}} που χρησιμοποιούν αυτόν τον τύπο.", "smw_attribute_header": "Σελίδες που χρησιμοποιούν την ιδιότητα «$1»", "smw_attributearticlecount": "Εμφάνιση $1 {{PLURAL:$1|σελίδας που χρησιμοποιεί|σελίδων που χρησιμοποιούν}} αυτήν την ιδιότητα.", "smw-propertylist-subproperty-header": "Υποϊδιότητες", "smw-propertylist-redirect-header": "Συνώνυμα", "smw-propertylist-error-header": "Σελίδες με ακατάλληλες αναθέσεις", "smw-propertylist-count": "Εμφάνιση $1 {{PLURAL:$1|συσχετισμένης οντότητας|συσχετισμένων οντοτήτων}}.", "smw-propertylist-count-with-restricted-note": "Εμφάνιση $1 {{PLURAL:$1|συσχετισμένης οντότητας|συσχετισμένων οντοτήτων}} (περισσότερες είναι διαθέσιμες, αλλ<PERSON> η παρουσίαση περιορίζεται σε «$2»).", "smw-propertylist-count-more-available": "Εμφάνιση $1 {{PLURAL:$1|συσχετισμένης οντότητας|συσχετισμένων οντοτήτων}} (περισσότερες είναι διαθέσιμες).", "specialpages-group-smw_group": "Σημασιολογικό MediaWiki", "specialpages-group-smw_group-maintenance": "Συντήρηση", "specialpages-group-smw_group-properties-concepts-types": "Ιδιότητες, έννοιες και τύποι", "exportrdf": "Εξαγωγή σελίδων σε RDF", "smw_exportrdf_docu": "Αυτή η σελίδα σας επιτρέπει τη λήψη δεδομένων από μια σελίδα σε μορφή RDF.\nΓια να εξαγάγετε σελίδες, πληκτρολογήστε τους τίτλους τους στο πλαίσιο κειμένου παρακάτω, ένας τίτλος ανά γραμμή.", "smw_exportrdf_recursive": "Εξαγωγ<PERSON> όλων των σχετικών σελίδων αναδρομικά.\nΝα σημειωθεί ότι το αποτέλεσμα μπορεί να είναι μεγάλο!", "smw_exportrdf_backlinks": "Εξαγωγή επίσης όλων των σελίδων που αναφέρονται στις εξαγόμενες σελίδες.\nΠαράγει RDF με δυνατότητα περιήγησης (δηλαδή με συνδέσμους επιστροφής).", "smw_exportrdf_lastdate": "Μην εξάγετε σελίδες που δεν έχουν αλλάξει από τη δεδομένη χρονική στιγμή.", "smw_exportrdf_submit": "Εξαγωγή", "uriresolver": "Επιλύτης URI", "properties": "Ιδιότητες", "smw-categories": "Κατηγορίες", "smw_properties_docu": "Σε αυτό το wiki χρησιμοποιούνται οι ακόλουθες ιδιότητες.", "smw_property_template": "$1 τύπου $2 ($3 {{PLURAL:$3|χρήση|χρήσεις}})", "smw_propertylackspage": "Όλες οι ιδιότητες πρέπει να περιγράφονται από κάποια σελίδα!", "smw_propertylackstype": "Δεν έχει καθοριστεί τύπος για την ιδιότητα αυτή (προς το παρόν θεωρείται τύπος $1).", "smw_propertyhardlyused": "Αυτή η ιδιότητα δεν χρησιμοποιείται σχεδόν καθόλου εντός του wiki!", "smw-property-name-invalid": "Η ιδιότητα  $1  δεν μπορεί να χρησιμοποιηθεί (μη έγκυρο όνομα ιδιότητας).", "smw-property-name-reserved": " Το\"$1\" ταξινομήθηκε ως όνομα καταχώρησης από αποθήκευση και δεν πρέπει να χρησιμοποιείται για ιδία χρήση. Η ακόλουθη ιστοσελίδα [https://www.semantic-mediawiki.org/wiki/Help:Property_naming help page] μπορεί να περιλαμβάνει πληροφορίες όπως το γιατί αυτό το όνομα αποθηκεύτηκε (καταχωρήθηκε).", "smw-sp-property-searchform": "Προβο<PERSON><PERSON> ιδιοτήτων που περιέχουν:", "smw-sp-property-searchform-inputinfo": "Το πλαίσιο εισαγωγής δεδομένων κάνει διάκριση πεζών-κεφαλ<PERSON><PERSON>ων και όταν χρησιμοποιείται για φιλτράρισμα, εμ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν<PERSON><PERSON><PERSON> μόνο ιδιότητες που ικανοποιούν τη συνθήκη.", "smw-special-property-searchform": "Παρουσ<PERSON><PERSON><PERSON><PERSON> ιδιοτήτων που περιέχουν:", "smw-special-property-searchform-inputinfo": "Το πλαίσιο εισαγωγής δεδομένων κάνει διάκριση πεζών-κεφαλ<PERSON><PERSON>ων και όταν χρησιμοποιείται για φιλτράρισμα, εμ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν<PERSON><PERSON><PERSON> μόνο ιδιότητες που ικανοποιούν τη συνθήκη.", "smw-special-property-searchform-options": "Επιλογές", "smw-special-wantedproperties-filter-label": "Φίλτρο:", "smw-special-wantedproperties-filter-none": "Κανένα", "smw-special-wantedproperties-filter-unapproved": "Μη εγκεκριμένο", "concepts": "Έννοιες", "smw-special-concept-docu": "Μια [https://www.semantic-mediawiki.org/wiki/Help:Concepts έννοια] μπορεί να θεωρηθεί ως «δυναμική κατηγορία», δηλαδή ως μία συλλογή σελίδων που δεν δημιουργήθηκαν με το χέρι αλλά μέσω υπολογισμών από το Σημασιολογικό MediaWiki από την περιγραφή δεδομένου ερωτήματος.", "smw-special-concept-header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εννοιών", "smw-special-concept-count": "{{PLURAL:$1|Παρατίθεται η ακόλουθη έννοια|Παρατίθενται οι ακόλουθες $1 έννοιες}}.", "smw-special-concept-empty": "Δεν βρέθηκε κάποια έννοια.", "unusedproperties": "Αχρησιμοποίητες ιδιότητες", "smw-unusedproperties-docu": "Αυτή η σελίδα παρουσιάζει μια λίστα με [https://www.semantic-mediawiki.org/wiki/Unused_properties αχρησιμοποίητες ιδιότητες] που ενώ έχουν οριστεί, δεν χρησιμοποιούνται σε κάποια σελίδα. Μπορείτε σε αντιπαραβολή να δείτε την ειδική σελίδα με [[Special:Properties|όλες τις ιδιότητες]] ή αυτήν με τις [[Special:WantedProperties|ζητούμενες ιδιότητες]].", "smw-unusedproperty-template": "$1 τύπου $2", "wantedproperties": "Επιθυμητές ιδιότητες", "smw-wantedproperties-docu": "Αυτή η σελίδα παρουσιάζει μια λίστα με [https://www.semantic-mediawiki.org/wiki/Wanted_properties ζητούμενες ιδιότητες] που ενώ χρησιμοποιούνται στο wiki, δεν έχουν σελίδα που να τις ορίζει. Μπορείτε σε αντιπαραβολή να δείτε την ειδική σελίδα με [[Special:Properties|όλες τις ιδιότητες]] ή αυτήν με τις [[Special:UnusedProperties|αχρησιμοποίητες ιδιότητες]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|χρήση|χρήσεις}})", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|χρήση|χρήσεις}})", "smw_purge": "Ανανέωση", "smw-purge-failed": "Το Σημασιολογικό MediaWiki προσπάθησε να ανανεώσει τη σελίδα αλλά απέτυχε", "types": "Τύποι", "smw_types_docu": "Λίστα [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes διαθέσιμων τύπων δεδομένων] με κάθε [https://www.semantic-mediawiki.org/wiki/Help:Datatype τύπο] να αντιπροσωπεύει ένα μοναδικό σύνολο χαρακτηριστικών για να περιγράψει μια τιμή από πλευράς χαρακτηριστικών αποθήκευσης και εμφάνισης που είναι κληρονομικά ως προς κάποια ανατεθειμένη ιδιότητα.", "smw-special-types-no-such-type": "Το «$1» είν<PERSON><PERSON> άγνωστο και δεν έχει καθοριστεί ως έγκυρος τύπος δεδομένων.", "smw-statistics": "Σημασιολ<PERSON><PERSON><PERSON><PERSON><PERSON> στατιστικά", "smw-statistics-cached": "Σημασι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> στατιστικά (αποθηκευμένα στην προσωρινή μνήμη)", "smw-statistics-entities-total": "Οντότητες (σύνολο)", "smw-statistics-property-instance": "{{PLURAL:$1|Τιμή ιδιότητας|Τιμές ιδιοτήτων}} (συνολικά)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Ιδιότητα|Ιδιότητες}}]] (συνολικά)", "smw-statistics-property-total-info": "Το σύνολο των καταχωρισμένων ιδιοτήτων.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Ιδιότητα|Ιδιότητες}} (συνολικά)", "smw-statistics-property-used": "{{PLURAL:$1|Ιδιότητα (που χρησιμοποιείται με τουλάχιστον μία τιμή)|Ιδιότητες (που χρησιμοποιούνται με τουλάχιστον μία τιμή)}}", "smw-statistics-property-page": "{{PLURAL:$1|Ιδιότητα (καταχωρισμένη σε σελίδα)|Ιδιότητες (καταχωρισμένες σε σελίδες)}}", "smw-statistics-property-page-info": "Αριθ<PERSON><PERSON>ς ιδιοτήτων που έχουν δική τους σελίδα και περιγραφή.", "smw-statistics-property-type": "{{PLURAL:$1|Ιδιότητα (εκχωρισμένη σε τύπο δεδομένων)|Ιδιότητες (εκχωρισμένες σε τύπους δεδομένων)}}", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Ερώτημα|Ερωτήματα}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Ερώτημα (ενσωματωμένο, σύνολο)|Ερωτήματα}} (ενσωματωμένα, σύνολο)]]", "smw-statistics-query-format": "μορφή <code>$1</code>", "smw-statistics-query-size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ερωτήματος", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Έννοια|Έννοιες}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Έννοια|Έννοιες}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Υποαντικείμενο|Υποαντικείμενα}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Υποαντικείμενο|Υποαντικείμενα}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Τύπος δεδομένων|Τύποι δεδομένων}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Τιμή ιδιότητας|Τιμές ιδιοτήτων}} ([[Special:ProcessingErrorList|{{PLURAL:$1|ακατάλληλη σημειογραφία|ακατάλληλες σημειογραφίες}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Τιμή ιδιότητας|Τιμές ιδιοτήτων}} ({{PLURAL:$1|ακατάλληλη σημειογραφία|ακατάλληλες σημειογραφίες}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Παρωχημένη οντότητα|Παρωχημένες οντότητες}}]", "smw-statistics-delete-count-info": "Οντότητες που έχουν επισημανθεί για κατάργηση και πρέπει να απορρίπτονται τακτικά χρησιμοποιώντας τις παρεχόμενες δέσμες ενεργειών συντήρησης.", "smw_uri_doc": "Ο αναλυτής URI υλοποιεί το [$1 εύρημα της Ομάδας Τεχνικής Αρχιτεκτονικής του W3C σχετικά με το θέμα «httpRange-14»].\nΟυ<PERSON>ιαστικά φροντίζει οι άνθρωποι να μην μεταμορφωθούν σε ιστοσελίδες.", "ask": "Σημασιολογι<PERSON><PERSON> αναζήτηση", "smw-ask-help": "Αυτή η ενότητα περιέχει ορισμένους συνδέσμους για να βοηθήσουν στην κατανόηση του πώς να χρησιμοποιήσετε τη σύνταξη <code>#ask</code>.\n\n* Το [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Επιλέγοντας σελίδες] περιγράφει πώς να επιλέγετε σελίδες και να κατασκευάζετε συνθήκες.\n\n*Το [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Τελεστές αναζήτησης] παραθέτει τους διαθέσιμους τελεστές αναζήτησης, συμπεριλαμβανομένων αυτών για ερωτήματα εύρους και ερωτήματα με μπαλαντέρ\n\n\n*Το [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Εμφανίζοντας τις πληροφορίες] περιγράφει τη χρήση των τρόπων εξαγωγής δηλώσεων και τις επιλογές μορφοποίησης", "smw_ask_sortby": "Ταξινόμηση κατά στήλη (προαιρετικό)", "smw_ask_ascorder": "Αύξουσα", "smw_ask_descorder": "Φθίνουσα", "smw-ask-order-rand": "Τυχαία", "smw_ask_submit": "Εύρεση αποτελεσμάτων", "smw_ask_editquery": "Επεξεργα<PERSON>ία ερωτήματος", "smw_add_sortcondition": "[Προσθήκη συνθήκης ταξινόμησης]", "smw-ask-sort-add-action": "Προσθήκη συνθήκης ταξινόμησης", "smw_ask_hidequery": "Απόκρυψη ερωτήματος (συμπαγής προβολή)", "smw_ask_help": "Βοήθεια για τα ερωτήματα", "smw_ask_queryhead": "Συνθήκη", "smw_ask_printhead": "Επιλογ<PERSON> ιδιοτήτων προς εμφάνιση", "smw_ask_printdesc": "(βάλτε μια ιδιότητα ανά γραμμή)", "smw_ask_format_as": "Μορφοποίηση ως:", "smw_ask_defaultformat": "προεπιλογή", "smw_ask_otheroptions": "Άλλες επιλογές", "smw-ask-otheroptions-info": "Αυτή η ενότητα περιέχει επιλογές που μεταβάλλουν καταστάσεις εκτυπώσεων. Μπορείτε να δείτε τις περιγραφές παραμέτρων περνώντας το ποντίκι από πάνω τους.", "smw-ask-otheroptions-collapsed-info": "Παρακαλούμε χρησιμοποιήστε το εικονίδιο με το σύμβολο «συν» για να δείτε όλες τις διαθέσιμες επιλογές", "smw_ask_show_embed": "Εμφάνιση ενσωματωμένου κώδικα", "smw_ask_hide_embed": "Απόκρυψη ενσωματωμένου κώδικα", "smw_ask_embed_instr": "Για την ενσωμάτωση αυτού του ερωτήματος σε μια σελίδα wiki, χρησιμοποιήστε τον παρακάτω κώδικα.", "smw-ask-delete": "Διαγραφή", "smw-ask-sorting": "Ταξινόμηση", "smw-ask-options": "Επιλογές", "smw-ask-options-sort": "Επιλογ<PERSON>ς ταξινόμησης", "smw-ask-format-options": "Μορφή και επιλογές", "smw-ask-parameters": "Παράμετροι", "smw-ask-search": "Αναζήτηση", "smw-ask-debug": "Εκσφαλμάτωση", "smw-ask-no-cache": "Απενεργοποίηση προσωρινής μνήμης ερωτήματος", "smw-ask-result": "Αποτέλεσμα", "smw-ask-empty": "Διαγρα<PERSON><PERSON> όλων των καταχωρίσεων", "smw-ask-format": "Μορφοποίηση", "smw-ask-format-selection-help": "Βοήθεια για την επιλεγμένη μορφή: $1", "smw-ask-condition-change-info": "Η συνθήκη άλλαξε και η μηχανή αναζήτησης απαιτεί να εκτελέσει ξανά το ερώτημα για να παραγάγει αποτελέσματα που ταιριάζουν με τις νέες απαιτήσεις.", "smw-ask-input-assistance": "Βοήθεια κατά την εισαγωγή", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Βοήθεια εισαγωγής] παρέχεται για τα πεδία εκτύπωσης, ταξινόμησης και συνθήκης. Το πεδίο συνθήκης απαιτεί τη χρήση ενός από τα ακόλουθα προθέματα:", "smw-ask-condition-input-assistance-property": "<code>p:</code> για λήψη υποδείξεων για ιδιότητες  (π.χ. <code>[[p:Έχει ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code>  για λήψη υποδείξεων για κατηγορίες", "smw-ask-condition-input-assistance-concept": "<code>con:</code>  για λήψη υποδείξεων για έννοιες", "smw-ask-format-change-info": "Η μορφή τροποποιήθηκε και απαιτείται να εκτελεστεί ξανά το ερώτημα για να ταιριάζει με τις νέες παραμέτρους και επιλογές οπτικοποίησης.", "smw-ask-format-export-info": "Η επιλεγμένη μορφή είναι μια εξαγώγιμη μορφή που δεν έχει οπτική αναπαράσταση επομένως τα αποτελέσματα παρέχονται μόνο ως κατέβασμα.", "smw-ask-query-search-info": "Το ερώτημα <code><nowiki>$1</nowiki></code> απαντήθηκε από το {{PLURAL:$3|1=<code>$2</code> (από προσωρινή μνήμη)|<code>$2</code> (από προσωρινή μνήμη)|<code>$2</code>}} σε $4 {{PLURAL:$4|δευτερόλεπτο|δευτερόλεπτα}}.", "searchbyproperty": "Αναζήτηση κατά ιδιότητα", "processingerrorlist": "<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σφαλμάτων επεξεργασίας", "propertylabelsimilarity": "Αναφορ<PERSON> ομοιότητας μεταξύ ονομάτων ιδιοτήτων", "smw-processingerrorlist-intro": "Η ακόλουθη λίστα παρέχει μια επισκόπηση σχετικά με τα [https://www.semantic-mediawiki.org/wiki/Processing_errors σφάλματα επεξεργασίας] που εμφανίστηκαν σε σχέση με το [https://www.semantic-mediawiki.org/ Σημασιολογικό MediaWiki]. Συνιστάται η παρακολούθηση αυτής της λίστας σε τακτική βάση και η διόρθωση μη έγκυρης σημειογραφίας τιμών.", "smw-missingredirects-list": "Σελίδες στις οποίες απουσιάζουν σημειογραφίες", "smw_sbv_docu": "Αναζήτηση για όλες τις σελίδες που έχουν μια δεδομένη ιδιότητα και τιμή.", "smw_sbv_novalue": "Εισαγάγετε μια έγκυρη τιμή για την ιδιότητα, ή προβάλετε όλες τις τιμές ιδιοτήτων για «$1».", "smw_sbv_displayresultfuzzy": "Λίστα με όλες τις σελίδες που έχουν ιδιότητα «$1» με τιμή «$2».\nΔεδομένου ότι υπήρξαν μόνο λίγα αποτελέσματα, εμφανίζονται επίσης κάποιες γειτονικές τιμές.", "smw_sbv_property": "Ιδιότητα:", "smw_sbv_value": "Τιμή:", "smw_sbv_submit": "Εύρεση αποτελεσμάτων", "browse": "Περιήγηση στο wiki", "smw_browselink": "Περιήγηση στις ιδιότητες", "smw_browse_article": "Εισαγάγετε το όνομα της σελίδας από την οποία θέλετε να ξεκινήσετε την περιήγηση.", "smw_browse_go": "Μετάβαση", "smw_browse_show_incoming": "Εμφάνιση ιδιοτήτων που συνδέουν εδώ", "smw_browse_hide_incoming": "Απόκρυψη ιδιοτήτων που συνδέουν εδώ", "smw_browse_no_outgoing": "Αυτή η σελίδα δεν έχει ιδιότητες.", "smw_browse_no_incoming": "Δεν υπάρχει ιδιότητα που να έχει σύνδεσμο προς αυτήν τη σελίδα.", "smw-browse-from-backend": "Οι πληροφορ<PERSON>ες ανακτώνται αυτήν τη στιγμή από τη βάση δεδομένων.", "smw-browse-js-disabled": "Υπάρχει η υποψία ότι το Javascript είναι απενεργοποιημένο ή μη διαθέσιμο και σας συνιστούμε να χρησιμοποιήσετε ένα πρόγραμμα περιήγησης που να υποστηρίζεται. Άλλες επιλογές αναλύονται στη σελίδα ρυθμίσεων της παραμέτρου [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi $smwgBrowseByApi].", "smw-browse-show-group": "Εμφάνιση ομάδων", "smw-browse-hide-group": "Απόκρυψη ομάδων", "smw_inverse_label_default": "$1 από", "smw_inverse_label_property": "Ετικέτα αντίστροφης ιδιότητας", "pageproperty": "Αναζήτη<PERSON>η ιδιότητας σελίδας", "pendingtasklist": "Λίστα εκκρεμών εργασιών", "smw_pp_docu": "Εισαγάγετε ή μια σελίδα και ιδιότητα, ή απλά μια ιδιότητα για να ανακτήσετε όλες τις ανατεθειμένες τιμές.", "smw_pp_from": "Από τη σελίδα:", "smw_pp_type": "Ιδιότητα:", "smw_pp_submit": "Εύρεση αποτελεσμάτων", "smw_result_prev": "Προηγούμενο", "smw_result_next": "Επόμενο", "smw_result_results": "Αποτελέσματα", "smw_result_noresults": "Λυπούμαστε, δεν υπάρχουν αποτελέσματα.", "smwadmin": "Πίνα<PERSON><PERSON>ς ελέγχου Σημασιολογικού MediaWiki", "smw-admin-statistics-job-title": "Στατιστικ<PERSON> εργασιών", "smw-admin-statistics-job-docu": "Τα στατιστικά εργασιών παρουσιάζουν πληροφορίες σχετικά με προγραμματισμένες εργασίες του Σημασιολογικού MediaWiki που δεν έχουν ακόμη εκτελεστεί. Ο αριθμός των εργασιών μπορεί να είναι ελαφρώς ανακριβής ή να περιέχει αποτυχημένες προσπάθειες, παρακαλείσθε να συμβουλευτείτε το [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue εγχειρίδιο χρήσης] για περαιτέρω πληροφορίες.", "smw-admin-statistics-querycache-title": "Προσωρινή μνήμη ερωτημάτων", "smw-admin-statistics-querycache-disabled": "Η [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] δεν έχει ενεργοποιηθεί σε αυτό το wiki και δεν προφανώς δεν υπάρχουν διαθέσιμα στατιστικά στοιχεία.", "smw-admin-statistics-section-explain": "Η ενότητα παρέχει πρόσθετα στατιστικά στοιχεία για τους διαχειριστές.", "smw-admin-statistics-semanticdata-overview": "Επισκόπηση", "smw-admin-permission-missing": "Η πρόσβαση σε αυτήν τη σελίδα έχει αποκλειστεί λόγω έλλειψης αδειών. Για λεπτομέρειες σχετικά με τις απαραίτητες ρυθμίσεις, παρακαλούμε συμβουλευτείτε τη σελίδα βοήθειας για τις [https://www.semantic-mediawiki.org/wiki/Help:Permissions άδειες].", "smw-admin-setupsuccess": "Η μηχανή αποθήκευσης αναδομήθηκε.", "smw_smwadmin_return": "Επιστροφή στην $1", "smw_smwadmin_updatestarted": "Ξεκίνησε μια νέα διαδικασία ενημέρωσης για την ανανέωση των σημασιολογικών δεδομένων.\nΌλα τα αποθηκευμένα δεδομένα θα αναδομηθούν ή θα επιδιορθωθούν όπου χρειάζεται.\nΜπορείτε να παρακολουθείτε την πρόοδο της ενημέρωσης από αυτήν την ειδική σελίδα.", "smw_smwadmin_updatenotstarted": "Υπάρχει ήδη μια διαδικασία ενημέρωσης σε εκτέλεση.\nΔεν πρόκειται να δημιουργηθεί άλλη.", "smw_smwadmin_updatestopped": "Όλες οι υπάρχουσες διαδικασίες ενημέρωσης έχουν σταματήσει.", "smw_smwadmin_updatenotstopped": "Για να διακόψετε τη εκτελούμενη διεργασία ενημέρωσης, πρέπει να ενεργοποιήσετε το πλαίσιο ελέγχου για να δηλώσετε ότι είστε πραγματικά βέβαιοι.", "smw-admin-docu": "Αυτή η ειδική σελίδα σας βοηθά κατά τη διάρκεια της εγκατάστασης, αναβάθμισης, συντήρησης και χρήσης του <a href=\"https://www.semantic-mediawiki.org\">Σημασιολογικού MediaWiki</a> και επίσης παρέχει περαιτέρω διαχειριστικές λειτουργίες και εργασίες καθώς επίσης και στατιστικά στοιχεία.\nΝα θυμάστε να παίρνετε αντίγραφα ασφαλείας όλων των σημαντικών δεδομένων πριν από την εκτέλεση διαχειριστικών λειτουργιών.", "smw-admin-environment": "Περιβάλλον λογισμικού", "smw-admin-db": "Σετάρισμα βάσεως δεδομένων", "smw-admin-db-preparation": "Η αρχικοποίηση του πίνακα βρίσκεται σε εξέλιξη και μπορεί να πάρει λίγο χρόνο πριν εμφανιστούν τα αποτελέσματα εν αναμονή του μεγέθους και των πιθανών βελτιστοποιήσεων του πίνακα.", "smw-admin-dbdocu": "Το Σημασιολογικό MediaWiki απαιτεί τη δική του δομή βάσης δεδομένων (και είναι ανεξάρτητη από το MediaWiki συνεπώς δεν επηρεάζει την υπόλοιπη εγκατάσταση του MediaWiki) για να αποθηκεύσει τα σημασιολογικά δεδομένα.\nΑυτή η λειτουργία μπορεί να εκτελεσθεί πολλές φορές χωρίς να προκαλεί ζημιά, αλλ<PERSON> είναι αναγκαία μόνο μία φορά κατά την εγκατάσταση ή αναβάθμιση.", "smw-admin-permissionswarn": "Εάν η επέμβαση αποτυγχάνει αναφέροντας σφάλματα S<PERSON>, ο χρήστης της βάσης δεδομένων τον οποίο χρησιμοποιεί το wiki σας (ελέγξτε το αρχείο σας «LocalSettings.php») μάλλον δεν έχει επαρκή δικαιώματα.\nΉ χορηγήστε σε αυτόν το χρήστη επιπλέον δικαιώματα να δημιουργεί και να διαγράφει πίνακες, ή εισαγάγετε προσωρινά τα στοιχεία του διαχειριστή της βάσης δεδομένων σας στο αρχείο «LocalSettings.php», ή χρησιμοποιήστε τη δέσμη ενεργειών συντήρησης <code>setupStore.php</code>, η οποία μπορεί να χρησιμοποιήσει διαπιστευτήρια διαχειριστή.", "smw-admin-dbbutton": "Αρχικοποίηση ή αναβάθμιση πινάκων", "smw-admin-announce": "Αναγγελία του wiki σας", "smw-admin-announce-text": "Αν το wiki σας είναι δημόσιο, μπορείτε να το καταχωρίσετε στο <a href=\"https://wikiapiary.com\">WikiApiary</a>, το wiki που παρακολουθεί άλλα wiki.", "smw-admin-deprecation-notice-title": "Ειδοποιήσεις παρωχημένων", "smw-admin-deprecation-notice-docu": "Η ακόλουθη ενότητα περιέχει ρυθμίσεις που είναι παρωχημένες ή έχουν καταργηθεί αλλά εντοπίστηκαν να είναι ενεργές σε αυτό το wiki. Αναμένεται ότι σε μελλοντικές εκδόσεις θα πάψει η υποστήριξη για αυτές τις ρυθμίσεις.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> is deprecated and will be removed in $2", "smw-admin-deprecation-notice-config-notice-option": "Το <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> θα καταργήσει (ή θα αντικαταστήσει) {{PLURAL:$2|την ακόλουθη επιλογή|τις ακόλουθες επιλογές}}:", "smw-admin-deprecation-notice-config-notice-option-list": "Το <code>$1</code> είναι παρωχημένο και θα καταργηθεί στην έκδοση $2", "smw-admin-deprecation-notice-config-replacement": "Το <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> αντικαταστάθηκε από το <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-option-list": "Το <code>$1</code> πρόκειται να αντικατασταθεί από το <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> was removed in $2", "smw-admin-deprecation-notice-title-notice": "Κατηργημένες ρυθμίσεις", "smw-admin-deprecation-notice-title-notice-explanation": "Οι <b>παρωχημένες ρυθμίσεις</b> εμφανίζουν ρυθμίσεις που έχουν ανιχνευθεί για χρήση σε αυτό το wiki και σχεδιάζεται να καταργηθούν ή να αλλάξουν σε μελλοντική έκδοση.", "smw-admin-deprecation-notice-title-replacement": "Αντικατασταθείσες ή μετονομασμένες ρυθμίσεις", "smw-admin-deprecation-notice-title-replacement-explanation": "Οι <b>αντ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>θείσες ή μετονομασθείσες ρυθμίσεις</b> περιέχουν ρυθμίσεις που μετονομάστηκαν ή τροποποιήθηκαν με κάποιον άλλον τρόπο και συνιστάται να ενημερώσετε αμέσως το όνομα ή τη μορφή τους.", "smw-admin-deprecation-notice-title-removal": "Κατηργημένες ρυθμίσεις", "smw-admin-deprecation-notice-section-legend": "Υπόμνημα", "smw-smwadmin-refresh-title": "Επισκευή και ενημέρωση δεδομένων", "smw_smwadmin_datarefresh": "Αναδόμηση δεδομένων", "smw_smwadmin_datarefreshdocu": "Είναι δυνατό να επαναφέρετε όλα τα δεδομένα του Σημασιολογικού MediaWiki βασιζόμενοι στα τρέχοντα περιεχόμενα του wiki.\nΑυτό μπορεί να είναι χρήσιμο στην περίπτωση της επιδιόρθωσης κατεστραμμένων δεδομένων ή για την ανανέωση των δεδομένων εάν έχει αλλάξει η εσωτερική τους μορφή λόγω κάποιας αναβάθμισης λογισμικού.\nΗ ενημέρωση εκτελείται σελίδα σελίδα και δεν θα ολοκληρωθεί άμεσα.\nΤο ακόλουθο δείχνει εάν μια ενημέρωση βρίσκεται σε εξέλιξη και σας επιτρέπει να ξεκινήσετε ή να διακόψετε ενημερώσεις (εκτός εάν αυτή η δυνατότητα έχει απενεργοποιηθεί από το διαχειριστή του ιστοχώρου).", "smw_smwadmin_datarefreshprogress": "<strong>Η ενημέρωση είναι ήδη σε εξέλιξη.</strong>\nΕίν<PERSON><PERSON> φυσικό η ενημέρωση να εξελίσσεται αργά δεδομένου ότι ανανεώνει τα δεδομένα σε μικρά κομμάτια και μόνο κάθε φορά που ένας χρήστης έχει πρόσβαση σε μια οποιαδήποτε σελίδα του wiki.\nΓια να ολοκληρώσετε αυτήν την ενημέρωση πιο γρήγορα, μπορείτε να εκτελέσετε τη δέσμη ενεργειών συντήρησης <code>runJobs.php</code> του MediaWiki (χρησιμοποιήστε την επιλογή <code>--maxjobs 1000</code> για να περιορίσετε τον αριθμό των ενημερώσεων που θα γίνουν σε μία δέσμη ενεργειών).\nΕκτιμώμενη πρόοδος της τρέχουσας ενημέρωσης:", "smw_smwadmin_datarefreshbutton": "Προγραμμα<PERSON>ι<PERSON><PERSON><PERSON>ς αναδόμησης δεδομένων", "smw_smwadmin_datarefreshstop": "Σταμάτημα αυτής της ενημέρωσης", "smw_smwadmin_datarefreshstopconfirm": "Ναι, είμαι {{GENDER:$1|σίγουρος|σίγουρη}}.", "smw-admin-job-scheduler-note": "Οι εργασίες (αυτές που είναι ενεργοποιημένες) σε αυτήν την ενότητα εκτελούνται μέσω της ουράς εργασιών για την αποφυγή καταστάσεων αδιεξόδου κατά την εκτέλεση τους. Η [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue ουρά εργασιών] είναι υπεύθυνη για την επεξεργασία και είναι καίριας σημασίας η δέσμη ενεργειών συντήρησης <code>runJobs.php</code> να έχει κατάλληλη χωρητικότητα (βλ. και παράμετρο διαμόρφωσης <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Απόρριψη παρωχημένων οντοτήτων", "smw-admin-outdateddisposal-intro": "Ορισμένες δραστηριότητες (κάποια αλλαγή στον τύπο μιας ιδιότητας, διαγραφή σελίδων wiki ή διόρθωση τιμών σφάλματος) θα έχουν ως αποτέλεσμα [https://www.semantic-mediawiki.org/wiki/Outdated_entities παρωχημένες οντότητες] και προτείνεται η περιοδική διαγραφή τους για να απελευθερώνεται ο χώρος που καταλαμβάνουν στους σχετικούς πίνακες.", "smw-admin-outdateddisposal-active": "Η εργασία απόρριψης παρωχημένων οντοτήτων έχει προγραμματιστεί.", "smw-admin-outdateddisposal-button": "Έναρξη προγραμματισμένης απόρριψης", "smw-admin-feature-disabled": "Αυτή η δυνατότητα έχει απενεργοποιηθεί σε αυτό το wiki, παρακαλούμε να συμβουλευτείτε τη σελίδα βοήθειας περί <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">ρυθμίσεων</a> ή επικοινωνήστε με το διαχειριστή του συστήματος.", "smw-admin-propertystatistics-title": "Αναδόμηση στατιστικ<PERSON>ν στοιχείων ιδιοτήτων", "smw-admin-propertystatistics-intro": "Αναδομεί το σύνολο των στατιστικ<PERSON>ν στοιχείων χρήσης των ιδιοτήτων και επ' αυτού ενημερώνει και διορθώνει το [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count πλήθος χρήσεων] κάθε ιδιότητας.", "smw-admin-propertystatistics-active": "Η εργασία αναδόμησης των στατιστικών έχει προγραμματιστεί.", "smw-admin-propertystatistics-button": "Έναρξης προγραμματισμένης αναδόμησης στατιστικών στοιχείων", "smw-admin-fulltext-title": "Αναδόμηση αναζήτησης πλήρους κειμένου", "smw-admin-fulltext-intro": "Αναδομεί το ευρετήριο αναζήτησης από πίνακες ιδιοτήτων με ενεργοποιημένο τύπο δεδομένων [https://www.semantic-mediawiki.org/wiki/Full-text αναζήτησης πλήρους κειμένου]. Αλλαγ<PERSON>ς στους κανόνες του ευρετηρίου (τροποποιημένες προειδοποιητικές λέξεις κλειδιά, νέο στέλεχος κλπ.) ή/και σε έναν πρόσφατα προστεθέντα ή τροποποιημένο πίνακα απαιτούν εκ νέου την εκτέλεση αυτής της εργασίας.", "smw-admin-fulltext-active": "Η εργασία αναδόμησης της αναζήτησης πλήρους κειμένου έχει προγραμματιστεί.", "smw-admin-fulltext-button": "Έναρξης προγραμματισμένης αναδόμησης πλήρους κειμένου", "smw-admin-support": "Παροχή υποστήριξης", "smw-admin-supportdocu": "Διάφοροι πόροι παρέχονται για να σας βοηθήσουν σε περίπτωση προβλημάτων:", "smw-admin-installfile": "Εάν αντιμετωπίζετε προβλήματα με την εγκατάσταση, ξεκινήστε ρίχνοντας μια ματιά στις κατευθυντήριες γραμμές στο <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">αρχείο με τίτλο INSTALL</a> και στη <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">σελίδα εγκατάστασης</a>.", "smw-admin-smwhomepage": "Η πλήρης τεκμηρίωση χρήσης του Σημασιολογικού MediaWiki βρίσκεται στο <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Προγραμματιστικά σφάλματα μπορούν να αναφερθούν στη <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">σελίδα παρακολούθησης προβλημάτων</a>, η σελίδα <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">αναφοράς προγραμματιστικών σφαλμάτων</a> παρέχει κάποιες οδηγίες για το πώς να γράψετε μια αποτελεσματική αναφορά για κάποιο πρόβλημα.", "smw-admin-questions": "Εάν έχετε περαιτέρω ερωτήσεις ή προτάσεις, λάβετε μέρος στη συζήτηση στη <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">λίστα αλληλογραφίας χρηστών</a> του Σημασιολογικού MediaWiki.", "smw-admin-other-functions": "Άλλες λειτουργίες", "smw-admin-statistics-extra": "Στατιστικές λειτουργίες", "smw-admin-statistics": "Στατιστικά", "smw-admin-supplementary-section-title": "Συμπληρωματικές λειτουργίες", "smw-admin-supplementary-section-subtitle": "Υποστηριζόμενες βασικές λειτουργίες", "smw-admin-supplementary-section-intro": "Αυτή η ενότητα παρέχει πρόσθετες λειτουργίες εκτός του πεδίου των δραστηριοτήτων συντήρησης και είναι πιθανόν ορισμένες λειτουργίες που παρατίθενται (βλ. [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions τεκμηρίωση]) να είναι περιορισμένες ή μη διαθέσιμες και συνεπώς απροσπέλαστες σε αυτό το wiki.", "smw-admin-supplementary-settings-title": "Διαμόρφωση και ρυθμίσεις", "smw-admin-supplementary-settings-intro": "Οι <u>$1</u> εμφανίζουν παραμέτρους που καθορίζουν τη συμπεριφορά του Σημασιολογικού MediaWiki", "smw-admin-main-title": "Σημασιολογικό MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Στατιστικ<PERSON> λειτουργίας", "smw-admin-supplementary-operational-statistics-short-title": "στατιστικ<PERSON> λειτουργίας", "smw-admin-supplementary-operational-statistics-intro": "Παρουσιά<PERSON><PERSON>ι ένα εκτεταμένο σύνολο από <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Αναζήτηση και απόρριψη οντότητας", "smw-admin-supplementary-idlookup-short-title": "Αναζήτηση και απόρριψη οντότητας", "smw-admin-supplementary-idlookup-intro": "Υποστηρίζει μια απλή λειτουργία <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Αναζήτηση πληροφοριών για διπλότυπες οντότητες", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> για να βρείτε οντότητες που είναι κατηγοριοποιημένες ως διπλότυπες για τον επιλεγμένο πίνακα", "smw-admin-supplementary-operational-statistics-cache-title": "Στατιστι<PERSON><PERSON> προσωρινής έκδοσης", "smw-admin-supplementary-operational-table-statistics-title": "Στατιστικά πίνακα", "smw-admin-supplementary-operational-table-statistics-short-title": "στατιστικ<PERSON> πινάκων", "smw-admin-supplementary-operational-table-statistics-intro": "Παράγει <u>$1</u> για επιλεγμένο σύνολο πινάκων", "smw-admin-supplementary-elastic-version-info": "Έκδοση", "smw-admin-supplementary-elastic-functions": "Υποστηριζόμενες λειτουργίες", "smw-admin-supplementary-elastic-settings-title": "Ρυθμίσεις (ευρετήρια)", "smw-admin-supplementary-elastic-mappings-summary": "Περίλη<PERSON>η", "smw-admin-supplementary-elastic-mappings-fields": "Χ<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς πεδίου", "smw-admin-supplementary-elastic-nodes-title": "Κόμβοι", "smw-admin-supplementary-elastic-indices-title": "Ευρετήρια", "smw-admin-supplementary-elastic-statistics-title": "Στατιστικά", "smw-admin-supplementary-elastic-status-refresh-interval": "Διάστημα ανανέωσης: $1", "smw-admin-supplementary-elastic-replication-files": "Αρχεία", "smw-admin-supplementary-elastic-replication-pages": "Σελίδες", "smw-admin-supplementary-elastic-config": "Διαμορφώσεις", "smw-list-count": "Η λίστα περιέχει $1 {{PLURAL:$1|καταχώριση|καταχωρίσεις}}.", "smw-property-label-uniqueness": "Η ετικέτα «$1» βρέθηκε όμοια με τουλάχιστον μία άλλη λεκτική αναπαράσταση ιδιότητας. Παρακαλούμε συμβουλευτείτε τη [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness σελίδα βοήθειας] σχετικά με τον τρόπο επίλυσης αυτού του ζητήματος.", "smw-property-label-similarity-title": "Αναφορ<PERSON> ομοιότητας μεταξύ ονομάτων ιδιοτήτων", "smw-property-label-similarity-intro": "Το <u>$1</u> υπολογίζει ομοιότητες μεταξύ των υπαρχόντων ονομάτων ιδιοτήτων", "smw-property-label-similarity-threshold": "Κατώφλιο:", "smw-property-label-similarity-noresult": "Δεν βρέθηκαν αποτελέσματα για τις συγκεκριμένες επιλογές.", "smw-admin-operational-statistics": "Αυτή η σελίδα περιέχει στατιστικά στοιχεία λειτουργίας που συλλέγονται σε ή από συναφείς λειτουργίες του Σημασιολογικού MediaWiki. Μια εκτεταμένη λίστα στατιστικών στοιχείων σχετικά με το wiki μπορεί να βρεθεί [[Special:Statistics|<b>εδώ</b>]].", "smw_adminlinks_datastructure": "Δομή δεδομένων", "smw_adminlinks_displayingdata": "Παρουσίαση δεδομένων", "smw_adminlinks_inlinequerieshelp": "Βοήθεια σχετικά με τα ερωτήματα εντός κειμένου", "smw-page-indicator-usage-count": "Εκτιμώμενο [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count πλήθος χρήσεων]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Ιδιότητα ορισμένη από {{PLURAL:$1|χρήστη|το σύστημα}}", "smw-createproperty-isproperty": "Αυτή είναι ιδιότητα τύπου $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Η επιτρεπτή τιμή|Οι επιτρεπτές τιμές}} για αυτήν την ιδιότητα είναι:", "smw-paramdesc-category-delim": "Το διαχωριστικό", "smw-paramdesc-category-template": "Ένα πρότυπο για να μορφοποιήσετε με αυτό τα στοιχεία", "smw-paramdesc-category-userparam": "Παρά<PERSON><PERSON><PERSON><PERSON>ος για να την περάσετε στο πρότυπο", "smw-info-par-message": "Μήνυμα προς εμφάνιση.", "smw-info-par-icon": "Εικονίδιο για εμφάνιση είτε «πληροφοριών» είτε «προειδοποίησης».", "prefs-smw": "Σημασιολογικό MediaWiki", "prefs-general-options": "Γενικ<PERSON>ς επιλογές", "prefs-extended-search-options": "Εκτεταμένη αναζήτηση", "prefs-ask-options": "Σημασιολογι<PERSON><PERSON> αναζήτηση", "smw-prefs-intro-text": "Το [https://www.semantic-mediawiki.org/ Σημασιολογικό MediaWiki] και οι συναφείς επεκτάσεις παρέχουν εξατομικευμένες προτιμήσεις για μια ομάδα επιλεγμένων χαρακτηριστικών και λειτουργιών. Μια λίστα μεμονωμένων ρυθμίσεων με την περιγραφή και τα χαρακτηριστικά τους είναι διαθέσιμη στην ακόλουθη [https://www.semantic-mediawiki.org/wiki/Help:User_preferences σελίδα βοηθείας].", "smw-prefs-ask-options-tooltip-display": "Εμφάνιση κειμένου παραμέτρου ως πληροφορίες στο δείκτη του ποντικιού στην ειδική σελίδα [[Special:Ask|εργαλείο δημιουργίας ερωτημάτων]] με το #ask.", "smw-prefs-general-options-jobqueue-watchlist": "Εμφάνιση της λίστας παρακολούθησης ουράς εργασιών στην προσωπική μου μπάρα", "smw-prefs-general-options-disable-editpage-info": "Απενεργοποίηση του εισαγωγικού κειμένου στη σελίδα επεξεργασίας", "smw-prefs-general-options-disable-search-info": "Απενεργοποίηση των πληροφοριών υποστήριξης σύνταξης στην τυπική σελίδα αναζήτησης", "smw-prefs-general-options-suggester-textinput": "Ενεργοποίηση της βοήθειας κατά την εισαγωγή για σημασιολογικές οντότητες", "smw-prefs-help-general-options-suggester-textinput": "Εάν είναι ενεργοποιημένη, επιτρέπει τη χρήση [[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance βοήθειας κατά την εισαγωγή]] για την εύρεση ιδιοτήτων, εννοιών και κατηγοριών από συμφραζόμενα της εισόδου.", "smw-ui-tooltip-title-property": "Ιδιότητα", "smw-ui-tooltip-title-quantity": "Μετατρο<PERSON><PERSON> μονάδας", "smw-ui-tooltip-title-info": "Πληροφορίες", "smw-ui-tooltip-title-service": "Σύνδεσμοι υπηρεσίας", "smw-ui-tooltip-title-warning": "Προειδοποίηση", "smw-ui-tooltip-title-error": "Σφάλμα", "smw-ui-tooltip-title-parameter": "Παρ<PERSON><PERSON>ετρος", "smw-ui-tooltip-title-event": "Συμβάν", "smw-ui-tooltip-title-note": "Σημείωση", "smw-ui-tooltip-title-legend": "Υπόμνημα", "smw-ui-tooltip-title-reference": "Αναφορά", "smw_unknowntype": "Ο τύπος «$1» αυτής της ιδιότητας δεν είναι έγκυρος", "smw-concept-cache-text": "Η έννοια διαθέτει συνολικά $1 {{PLURAL:$1|σελίδα|σελίδες}} και ενημερώθηκε τελευταία φορά στις $2, στις $3.", "smw_concept_header": "Σελίδες της έννοιας «$1»", "smw_conceptarticlecount": "Παρα<PERSON><PERSON>τω {{PLURAL:$1|εμφανίζεται 1 σελίδα|εμφανίζονται $1 σελίδες}}.", "smw-qp-empty-data": "Τα ζητούμενα δεδομένα δεν μπορούν να προβληθούν λόγω ανεπαρκών κριτηρίων επιλογής.", "right-smw-admin": "Πρόσβαση σε διαχειριστικές εργασίες (Σημασιολογικό MediaWiki)", "right-smw-patternedit": "Πρόσβαση επεξεργασίας για τη συντήρηση των επιτρεπτών κανονικών εκφράσεων και μοτίβων (Σημασιολογικό MediaWiki)", "action-smw-patternedit": "επεξερ<PERSON>αστείτε κανονικ<PERSON>ς εκφράσεις που χρησιμοποιούνται από το Σημασιολογικό MediaWiki", "group-smwadministrator": "Διαχε<PERSON><PERSON>ιστέ<PERSON> (Σημασιολογικού MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|διαχειριστής|διαχειρίστρια}} (Σημασιολογικού MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Δι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τ<PERSON><PERSON> (Σημασιολογικού MediaWiki)", "group-smwcurator": "Επιμελητές (Σημασιολογικό MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|επιμελητής|επιμελήτρια}} (Σημασιολογικό MediaWiki)", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τ<PERSON><PERSON> (Σημασιολογικό MediaWiki)", "group-smweditor": "Συντ<PERSON>κτε<PERSON> (Σημασιολογικό MediaWiki)", "group-smweditor-member": "{{GENDER:$1|συντάκτης|συντάκτρια}} (Σημασιολογικό MediaWiki)", "grouppage-smweditor": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Σημασιολογικού MediaWiki)", "action-smw-admin": "μεταβείτε στις διαχειριστικές εργασίες του Σημασιολογικού MediaWiki", "action-smw-ruleedit": "επεξεργαστείτε σελίδες κανόνων (Σημασιολογικό MediaWiki)", "smw-property-predefined-default": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα του $2.", "smw-property-predefined-common": "Αυτή η ιδιότητα είναι μια εκ των προτέρων ορισμένη ιδιότητα (γνωστή επίσης ως [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ειδική ιδιότητα]) και συνοδεύεται από επιπλέον διαχειριστικά προνόμια αλλά μπορεί να χρησιμοποιηθεί ακριβώς όπως οποιαδήποτε άλλη [https://www.semantic-mediawiki.org/wiki/Property ιδιότητα που ορίζεται από χρήστη].", "smw-property-predefined-ask": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που αναπαριστά μεταπληροφορία (με τη μορφή [https://www.semantic-mediawiki.org/wiki/Subobject υποαντικειμένου]) σχετικά με μεμονωμένα ερωτήματα και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-asksi": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία συλλέγει τον αριθμό των συνθηκών που χρησιμοποιούνται σε ένα ερώτημα και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-askde": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία παρέχει πληροφορίες σχετικά με το βάθος ενός ερωτήματος και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-long-askde": "Είναι μια αριθμητική τιμή που υπολογίζεται με βάση την εμφώλευση υποερωτημάτων, τις αλυσίδες ιδιοτήτων και τα διαθέσιμα στοιχεία περιγραφής με την εκτέλεση ερωτήματος που περιορίζεται από τη ρύθμιση <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-sp-properties-docu": "Αυτή η ειδική σελίδα παρέχει μια λίστα με τις διαθέσιμες [https://www.semantic-mediawiki.org/wiki/Property ιδιότητες] και το πλήθος χρήσεών τους για αυτό το wiki. Για πιο επικαιροποιημένα στατιστικά μετρήσεων συνιστάται η εκτέλεση της δέσμης ενεργειών συντήρησης των [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics στατιστικών στοιχείων των ιδιοτήτων] σε τακτική βάση. Για μια διαφοροποιημένη προβολή, δείτε τις ειδικές σελίδες με τις [[Special:UnusedProperties|μη χρησιμοποιούμενες]] ή τις [[Special:WantedProperties|ζητούμενες ιδιότητες]].", "smw-sp-properties-cache-info": "Τα δεδομένα που παρατίθενται έχουν ανακτηθεί από την [https://www.semantic-mediawiki.org/wiki/Caching προσωρινή μνήμη] και ενημερώθηκαν τελευταία φορά στις $1.", "smw-sp-properties-header-label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ιδιοτήτων", "smw-admin-settings-docu": "Παρουσιάζει μια λίστα όλων των προεπιλεγμένων και τοπικοποιημένων ρυθμίσεων που αφορούν στο περιβάλλον του Σημασιολογικού MediaWiki. Για λεπτομέρειες σχετικά με συγκεκριμένες ρυθμίσεις, συμβουλευθείτε τη σελίδα βοήθειας περί [https://www.semantic-mediawiki.org/wiki/Help:Configuration διαμόρφωσης].", "smw-sp-admin-settings-button": "Δημιουργ<PERSON>α λίστας ρυθμίσεων", "smw-admin-idlookup-title": "Αναζήτηση", "smw-admin-idlookup-docu": "Αυτή η ενότητα εμφανίζει τεχνικές λεπτομέρειες σχετικά με ένα αναγνωριστικό εσωτερικού αντικειμένου που αντιπροσωπεύει μια μεμονωμένη οντότητα (σελίδα wiki, υποαντικείμενο, ιδιότητα κλπ.) στο Σημασιολογικό MediaWiki. Η είσοδος μπορεί να είναι ένα αναγνωριστικό ή μια τιμή συμβολοσειράς που να ταιριάζει με το σχετικό πεδίο αναζήτησης, ωστόσο οποιαδήποτε αναφορά σε αναγνωριστικό σχετίζεται με το Σημασιολογικό MediaWiki και όχι με σελίδα του MediaWiki ή αναγνωριστικό αναθεώρησης.", "smw-admin-iddispose-title": "Απόρριψη", "smw-admin-iddispose-done": "Το αναγνωριστικό «$1» έχει αφαιρεθεί από τη βάση δεδομένων.", "smw-admin-idlookup-input": "Αναζήτηση:", "smw-admin-objectid": "Αναγνωριστικό:", "smw-admin-tab-general": "Επισκόπηση", "smw-admin-tab-notices": "Ειδοποιήσεις παρωχημένων", "smw-admin-tab-maintenance": "Συντήρηση", "smw-admin-tab-supplement": "Συμπληρωματικές λειτουργίες", "smw-admin-tab-registry": "Μητρ<PERSON>ο", "smw-admin-tab-alerts": "Ειδοποιήσεις", "smw-admin-alerts-tab-maintenancealerts": "Ειδοποιή<PERSON><PERSON>ις συντήρησης", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Βελτιστοποίηση πίνακα", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Παρωχημένες οντότητες", "smw-admin-maintenancealerts-invalidentities-alert-title": "Μη έγκυρες οντότητες", "smw-admin-deprecation-notice-section": "Σημασιολογικό MediaWiki", "smw-admin-configutation-tab-settings": "Ρυθμίσεις", "smw-admin-configutation-tab-namespaces": "Ονοματοχώροι", "smw-admin-configutation-tab-schematypes": "Τύποι σημασιολογικού σχήματος", "smw-admin-maintenance-tab-tasks": "Εργασίες", "smw-admin-maintenance-tab-scripts": "Δέσμες ενεργειών συντήρησης", "smw-admin-maintenance-no-description": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγραφή.", "smw-admin-maintenance-script-section-title": "Κα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> διαθέσιμων δεσμών ενεργειών συντήρησης", "smw-admin-maintenance-script-section-intro": "Οι ακόλουθες δέσμες ενεργειών συντήρησης απαιτούν κάποιον διαχειριστή και πρόσβαση στη γραμμή εντολών για να είναι σε θέση να εκτελέσει τις αναφερόμενες δέσμες ενεργειών.", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Επανακ<PERSON><PERSON><PERSON><PERSON>κευάζει τα στατιστικά χρήσης για όλες τις οντότητες ιδιότητας.", "smw-livepreview-loading": "Φόρτωση σε εξέλιξη...", "smw-sp-searchbyproperty-description": "Αυτή η σελίδα παρέχει μια απλή [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces διεπαφή περιήγησης] για την εύρεση οντοτήτων που περιγράφονται από κάποια ιδιότητα και κάποια ονομαστική τιμή. Άλλες διαθέσιμες διεπαφές αναζήτησης περιλαμβάνουν την [[Special:PageProperty|αναζήτηση ιδιοτήτων σελίδας]] και το [[Special:Ask|εργαλείο κατασκευής ερωτημάτων]].", "smw-sp-searchbyproperty-resultlist-header": "Λίστα αποτελεσμάτων", "smw-sp-searchbyproperty-nonvaluequery": "Μια λίστα τιμών που έχουν ανατεθειμένη την ιδιότητα «$1».", "smw-sp-searchbyproperty-valuequery": "Κα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σελίδων που έχουν την ιδιότητα «$1» με σημειωμένη τιμή «$2».", "smw-datavalue-number-textnotallowed": "Το «$1» δεν μπορεί να εκχωρηθεί σε τύπο που έχει δηλωθεί ως αριθμητικός με τιμή $2.", "smw-datavalue-number-nullnotallowed": "Το «$1» επέστρεψε «NULL», το οποίο δεν επιτρέπεται ως αριθμός.", "smw-editpage-annotation-enabled": "Αυτή η σελίδα υποστηρίζει ενδοκειμενική σημασιολογική σημειογραφία (π.χ. <nowiki>«[[Ορίζεται ως::Μνημείο παγκόσμιας πολιτιστικής κληρονομιάς]]»</nowiki>) για την κατασκευή δομημένου και ανακτήσιμου μέσω ερωτημάτων περιεχομένου που παρέχεται από το Σημασιολογικό MediaWiki. Για μια ολοκληρωμένη περιγραφή για το πώς να χρησιμοποιείτε αυτή τη σημειογραφία ή τη συνάρτηση του συντακτικού αναλυτή #ask, παρακαλούμε ρίξτε μια ματιά στις σελίδες βοήθειας [https://www.semantic-mediawiki.org/wiki/Help:Getting_started ξεκινώντας], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation ενδοκειμενική σημειογραφία], ή [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries ενδοκειμενικά ερωτήματα].<!--[[Is specified as::World Heritage Site]]-->", "smw-editpage-annotation-disabled": "Σε αυτήν τη σελίδα δεν παρέχεται η δυνατότητα ενδοκειμενικής σημασιολογικής σημειογραφίας λόγω περιορισμών του ονοματοχώρου. Λεπτομέρειες σχετικά με το πώς να ενεργοποιήσετε αυτήν τη δυνατότητα για τον ονοματοχώρο μπορούν να βρεθούν στη σελίδα βοήθειας για τη [https://semantic-mediawiki.org/wiki/Help:Configuration ρύθμιση παραμέτρων].", "smw-editpage-property-annotation-enabled": "Αυτή η ιδιότητα μπορεί να επεκταθεί χρησιμοποιώντας σημασιολογική σημειογραφία για καθορισμό τύπου δεδομένων (π.χ. <nowiki>«[[Has type::Page]]»</nowiki>) ή άλλες υποστηρικτικές δηλώσεις (π.χ. <nowiki>«[[Subproperty of::dc:date]]»</nowiki>). Για περιγραφή σχετικά με το πώς να εμπλουτίσετε αυτή τη σελίδα, δείτε τη [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration δήλωση μιας ιδιότητας] ή τη σελίδα βοήθειας με τη [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes λίστα διαθέσιμων τύπων δεδομένων].", "smw-editpage-property-annotation-disabled": "Αυτή η ιδιότητα δεν μπορεί να επεκταθεί με σημειογραφία τύπου δεδομένων (π.χ. <nowiki>«[[Has type::Page]]»</nowiki>) καθώς είναι ήδη εκ των προτέρων ορισμένη (βλ. σελίδα βοήθειας σχετικά με [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ειδικές ιδιότητες] για περισσότερες πληροφορίες).", "smw-editpage-concept-annotation-enabled": "Αυτή η έννοια μπορεί να επεκταθεί χρησιμοποιώντας τη συνάρτηση του συντακτικού αναλυτή #concept. Για περιγραφή σχετικά με το πώς να χρησιμοποιήσετε την #concept, δείτε τη σελίδα βοήθειας για τις [https://www.semantic-mediawiki.org/wiki/Help:Concepts έννοιες].", "smw-search-input": "Εισαγω<PERSON><PERSON> και αναζήτηση", "smw-search-syntax": "Επισήμανση", "smw-search-profile-tooltip": "Λειτου<PERSON><PERSON><PERSON><PERSON>ς αναζήτησης σε σχέση με το Σημασιολογικό MediaWiki", "smw-search-profile-sort-best": "Καλύτερη αντιστοιχία", "smw-search-profile-sort-recent": "Πιο πρόσφατο", "smw-search-profile-sort-title": "Τίτλος", "smw-search-profile-extended-help-query": "Χρησιμοποιήθηκε το <code><nowiki>$1</nowiki></code> ως ερώτημα.", "smw-search-profile-extended-help-query-link": "(Για περισσότερες πληροφορίες παρακαλούμε χρησιμοποιήστε το $1).", "smw-search-profile-extended-help-find-forms": "διαθέσιμες φόρμες", "smw-search-profile-extended-section-sort": "Ταξινόμηση κατά", "smw-search-profile-extended-section-form": "Φόρμες", "smw-search-profile-extended-section-namespace": "Ονοματ<PERSON><PERSON><PERSON><PERSON>ος", "smw-search-profile-extended-section-query": "Ερώτημα", "smw-search-profile-link-caption-query": "κατασ<PERSON><PERSON><PERSON><PERSON><PERSON>τ<PERSON>ς ερωτημάτων", "smw-search-show": "Εμφάνιση", "smw-search-hide": "Απόκρυψη", "log-name-smw": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Σημασιολογικού MediaWiki", "log-show-hide-smw": "$1 μητρώου Σημασιολογικού MediaWiki", "logeventslist-smw-log": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Σημασιολογικού MediaWiki", "log-description-smw": "Δραστηριότητες για [https://www.semantic-mediawiki.org/wiki/Help:Logging ενεργοποιημένους τύπους συμβάντων] που έχουν αναφερθεί από το Σημασιολογικό MediaWiki και τα συστατικά του.", "logentry-smw-maintenance": "Γεγον<PERSON><PERSON><PERSON> σχετικά με συντήρηση που προέρχονται από το Σημασιολογικό MediaWiki", "smw-datavalue-import-unknown-namespace": "Ο ονοματοχώρος εισαγωγής «$1» είν<PERSON>ι άγνωστος. Παρακαλούμε βεβαιωθείτε ότι οι πληροφορίες εισαγωγής OWL είναι διαθέσιμες μέσω του ονοματοχώρου [[MediaWiki:Smw import $1]].", "smw-datavalue-import-missing-namespace-uri": "Δεν ήταν δυνατόν να βρεθεί URI του ονοματοχώρου «$1» στην [[MediaWiki:Smw import $1|εισαγωγή $1]].", "smw-datavalue-import-missing-type": "Δεν βρέθηκε ορισμός τύπου του «$1» στην [[MediaWiki:Smw import $2|εισαγωγή $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|εισαγωγή $1]]", "smw-datavalue-import-invalid-value": "Το «$1» δεν είναι έγκυρη μορφή και αναμένεται να αποτελείται από «ονοματοχώρο»:«αναγνωριστικό» (π.χ. «foaf:name»).", "smw-datavalue-import-invalid-format": "Η συμβολοσειρά «$1» αναμένετο να ήταν χωρισμένη σε τέσσερα μέρη, αλλ<PERSON> η μορφή δεν έγινε κατανοητή.", "smw-property-predefined-impo": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που περιγράφει σχέση με [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary εισηγμένο λεξιλόγιο] και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-type": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που περιγράφει τον [[Special:Types|τύπο δεδομένων]] μιας ιδιότητας και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-sobj": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που αντιπροσωπεύει ένα κατασκεύασμα σαν [https://www.semantic-mediawiki.org/wiki/Help:Container δοχείο] και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-errp": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα για την παρακολούθηση σφαλμάτων εισόδου από παράτυπη σημειογραφία τιμών και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-long-errp": "Στις περισσότερες περιπτώσεις προξενείται από έναν μη προσαρμοσμένο τύπο ή περιορισμό [[Property:Allows value|τιμής]].", "smw-property-predefined-pval": "Η [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value «$1»] είναι μια εκ των προτέρων ορισμένη ιδιότητα που ορίζει μια λίστα από επιτρεπτές τιμές για τον περιορισμό των τιμών που μπορεί να ανατίθενται σε μια ιδιότητα και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "Η ιδιότητα ''$1'' έχει περιορισμένη περιοχή εφαρμογής και δεν μπορεί να χρησιμοποιηθεί ως ιδιότητα επισήμανσης από τον χρήστη", "smw-datavalue-property-restricted-declarative-use": "Η ιδιότητα ''$1'' είν<PERSON><PERSON> ιδιότητα που πρέπει να δηλωθεί και μπορεί να χρησιμοποιηθεί μόνο σε μια σελίδα ιδιοτήτων ή κατηγορίας", "smw-datavalue-restricted-use": "Η τιμή δεδομένων «$1» έχει σημανθεί για χρήση με περιορισμούς.", "smw-datavalue-invalid-number": "Το «$1» δεν μπορεί να ερμηνευτεί ως αριθμός.", "smw-query-condition-circular": "Ανιχνεύθηκε πιθανό πρόβλημα κυκλικότητας στο «$1»", "smw-types-list": "Λίστα τύπων δεδομένων", "smw-types-default": "Το «$1» είναι ενσωματωμένος τύπος δεδομένων.", "smw-types-help": "Περισσότερες πληροφορίες και παραδείγματα μπορούν να βρεθούν σε αυτήν τη [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 σελίδα βοήθειας].", "smw-type-anu": "Το «$1» είναι μια παραλλαγή του τύπου δεδομένων [[Special:Types/URL|URL]] και χρησιμοποιείται κυρίως ως δηλωτικό εξαγωγής ενός «owl:AnnotationProperty».", "smw-type-boo": "Το «$1» είν<PERSON>ι πρωτογενής τύπος δεδομένων που περιγράφει τιμή αληθές/ψευδές.", "smw-type-cod": "Το «$1» είναι μια παραλλαγή του τύπου δεδομένων [[Special:Types/Text|Text]] για χρήση σε τεχνικά κείμενα  αυθαίρετου μήκους, όπως κείμενα πηγαίου κώδικα προγραμμάτων.", "smw-type-geo": "Το «$1» είναι τύπος δεδομένων που περιγράφει γεωγραφικές τοποθεσίες και απαιτεί την επέκταση [https://www.semantic-mediawiki.org/wiki/Semantic_Maps «Χάρτες»] για την παροχή εκτεταμένης λειτουργικότητας.", "smw-type-tel": "Το «$1» είναι ειδικός τύπος δεδομένων που περιγράφει διεθνείς τηλεφωνικούς αριθμούς σύμφωνα με το RFC 3966.", "smw-type-txt": "Το «$1» είν<PERSON><PERSON> βασικός τύπος δεδομένων που περιγράφει συμβολοσειρές αυθαίρετου μήκους.", "smw-type-dat": "Το «$1» είναι πρωτογενής τύπος δεδομένων που αντιπροσωπεύει σημεία στο χρόνο σε μια ενιαία μορφή.", "smw-type-ema": "Το «$1» είναι ειδικός τύπος δεδομένων που αναπαριστά διεύθυνση ηλεκτρονικού ταχυδρομείου.", "smw-type-tem": "Το «$1» είναι ειδικός αριθμητικός τύπος δεδομένων που αναπαριστά θερμοκρασία.", "smw-type-qty": "Το «$1» είναι τύπος δεδομένων που περιγράφει ποσότητες με αριθμητική αναπαράσταση και μονάδα μέτρησης.", "smw-type-rec": "Το «$1» είναι τύπος δεδομένων σαν δοχείο που καθορίζει λίστα πληκτρολογούμενων ιδιοτήτων σε σταθερή σειρά.", "smw-type-extra-tem": "Η διάταξη μετατροπής περιλαμβάνει υποστηριζόμενες μονάδες όπως <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> και Ράνκιν.", "smw-type-tab-properties": "Ιδιότητες", "smw-type-tab-types": "Τύποι", "smw-type-tab-type-ids": "Αναγνωριστικά τύπου", "smw-type-tab-errors": "Σφάλματα", "smw-type-primitive": "Βασικοί", "smw-type-compound": "Σύνθετοι", "smw-property-predefined-errc": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki] και αντιπροσωπεύει σφάλματα που εμφανίστηκαν σε σχέση με σημειογραφίες ακατάλληλης τιμής ή επεξεργασία κάποιας εισόδου.", "smw-property-predefined-errt": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία περιέχει κάποια λεκτική περιγραφή σφάλματος και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-mdat": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία αντιστοιχεί στην ημερομηνία της τελευταίας τροποποίησης μιας σελίδας και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-cdat": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία αντιστοιχεί στην ημερομηνία της πρώτης έκδοσης μιας σελίδας και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-newp": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία υποδηλώνει εάν μία σελίδα είναι νέα ή όχι και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-ledt": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία περιέχει το όνομα σελίδας του χρήστη που δημιούργησε την τελευταία αναθεώρηση και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-mime": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία περιγράφει τον τύπο MIME κάποιου ανεβασμένου αρχείου και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-media": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία περιγράφει τον τύπο μέσου κάποιου ανεβασμένου αρχείου και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-askfo": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία αποθηκεύει το όνομα της μορφής αποτελεσμάτων που χρησιμοποιείται σε κάποιο ερώτημα και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-askst": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία περιγράφει τις συνθήκες του ερωτήματος ως συμβολοσειρά και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-askdu": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία περιέχει τη χρονική τιμή (σε δευτερόλεπτα) που απαιτήθηκε για την ολοκλήρωση της εκτέλεσης του ερωτήματος και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-asksc": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki] η οποία αναγνωρίζει εναλλακτικές (δηλαδή απομακρυσμένες, συνενωμένες) πηγές ερωτημάτων.", "smw-property-predefined-prec": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που περιγράφει την [https://www.semantic-mediawiki.org/wiki/Help:Display_precision απεικονιζόμενη ακρίβεια] (σε δεκαδικά ψηφία) για αριθμητικούς τύπους δεδομένων.", "smw-types-extra-geo-not-available": "Η [https://www.semantic-mediawiki.org/wiki/Extension:Maps επέκταση «Χάρτες»] δεν εντοπίστηκε οπότε η ιδιότητα «$1» έχει περιορισμένες δυνατότητες λειτουργίας.", "smw-datavalue-monolingual-dataitem-missing": "Απουσιά<PERSON><PERSON>ι αναμενόμενο στοιχείο για τη δόμηση μονόγλωσσης σύνθετης τιμής.", "smw-datavalue-languagecode-missing": "Για τη σημειογρα<PERSON><PERSON><PERSON> «$1», ο συντακτικ<PERSON>ς αναλυτής δεν ήταν σε θέση να προσδιορίσει κωδικό γλώσσας (π.χ. «foo@en»).", "smw-datavalue-languagecode-invalid": "Το «$1» δεν αναγνωρίστηκε ως κάποιος από τους υποστηριζόμενους κωδικούς γλώσσας.", "smw-property-predefined-lcode": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία αντιπροσωπεύει κωδικό γλώσσας μορφοποιημένο κατά BCP47 και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-types-extra-mlt-lcode": "Ο τύπος δεδομένων {{PLURAL:$2|απαιτεί|δεν απαιτεί}} κωδικό γλώσσας (δηλαδή μια σημειογραφία τιμής χωρίς κωδικό γλώσσας {{PLURAL:$2|δεν|}} είναι αποδεκτή).", "smw-property-predefined-text": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία αντιπροσωπεύει κείμενο αυθαίρετου μήκους και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-pdesc": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία επιτρέπει τη λεκτική περιγραφή μιας ιδιότητας σε κάποια γλώσσα και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-list": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που ορίζει μια λίστα ιδιοτήτων που χρησιμοποιούνται με ιδιότητα τύπου [[Special:Types/Record|record]] και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] <PERSON><PERSON><PERSON><PERSON><PERSON> συντακτικού αναλυτή για ενδοκειμενική σημειογραφία", "smw-limitreport-intext-postproctime": "[SMW] χρόνος μετα-επεξεργασίας", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|δευτερόλεπτο|δευτερόλεπτα}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|δευτερόλεπτο|δευτερόλεπτα}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|δευτερόλεπτο|δευτερόλεπτα}}", "smw_allows_pattern": "Αυτή η σελίδα αναμένεται να περιλαμβάνει μια λίστα από παραπομπές (ακολουθούμενες από [https://en.wikipedia.org/wiki/Regular_expression κανονικές εκφράσεις]) και να γίνουν διαθέσιμες από την ιδιότητα [[Property:Allows pattern|Allows pattern]]. Για να επεξεργαστείτε αυτή τη σελίδα, απαιτείται το δικαίωμα <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "Η τιμή «$1» χαρακτηρίστηκε ως μη έγκυρη από την κανονική έκφραση «$2».", "smw-datavalue-allows-pattern-reference-unknown": "Για την ανα<PERSON><PERSON><PERSON><PERSON> μοτίβου «$1» δεν βρέθηκε ταίριασμα με κάποια καταχώριση στο [[MediaWiki:Smw allows pattern]].", "smw-datavalue-feature-not-supported": "Η λειτουργία «$1» δεν υποστηρίζεται ή έχει απενεργοποιηθεί σε αυτό το wiki.", "smw-datavalue-constraint-uniqueness-violation": "Η ιδιότητα «$1» επιτρέπει μόνο μοναδικές αναθέσεις τιμών και η τιμή ''$2'' έχει ήδη σημειογραφηθεί στο θέμα «$3».", "smw-datavalue-constraint-uniqueness-violation-isknown": "Η ιδιότητα «$1» επιτρέπει μόνο μοναδικές σημειογραφήσεις τιμών, το ''$2'' περιέχει ήδη μια ανατεθειμένη τιμή. Το «$3» παραβιάζει τον περιορισμό μοναδικότητας.", "smw-datavalue-time-invalid-values": "Η τιμή «$1» περιέχει μη ερμηνεύσιμη πληροφορία της μορφής «$2».", "smw-datavalue-time-invalid-date-components-common": "Η τιμή «$1» περιέχει μη ερμηνεύσιμη πληροφορία.", "smw-datavalue-time-invalid-date-components-dash": "Η τιμή «$1» περιέχει άλλου τύπου διαχωριστικ<PERSON> ή άλλους χαρακτήρες που δεν είναι έγκυροι για την αναγνώρισή της ως ημερομηνία.", "smw-datavalue-time-invalid-date-components-empty": "Η τιμή «$1» περιέχει κάποια κενά στοιχεία.", "smw-datavalue-time-invalid-date-components-three": "Η τιμή «$1» περιέχει περισσότερα από τα τρία στοιχεία που απαιτούνται για την αναγνώρισή της ως ημερομηνία.", "smw-datavalue-time-invalid-date-components-sequence": "Το «$1» περιέχει μια ακολουθία που δεν ήταν δυνατόν να ερμηνευθεί βάσει του διαθέσιμου πίνακα με τα στοιχεία μορφής ημερομηνιών.", "smw-datavalue-time-invalid-ampm": "Η τιμή «$1» περιέχει ως ώρα το «$2» το οποίο δεν είναι έγκυρο σε 12-ωρη μορφή.", "smw-datavalue-external-formatter-invalid-uri": "Το «$1» είναι μη έγκυρη διεύθυνση URL.", "smw-datavalue-keyword-maximum-length": "Η λέξη-κλειδί υπερέβη το μέγιστο μήκος {{PLURAL:$1|του|των}} $1 {{PLURAL:$1|χαρακτήρα|χαρακτήρων}}.", "smw-property-predefined-peid": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα η οποία καθορίζει ένα εξωτερικό αναγνωριστικό και παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki].", "smw-property-predefined-pefu": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki] για να καθορίσει μια εξωτερική διεύθυνση URI με μεταβλητό τμήμα κειμένου.", "smw-property-predefined-long-pefu": "Το URI αναμένεται να περιέχει μια μεταβλητή κειμένου που θα λάβει μια τιμή [[Special:Types/External identifier|εξωτερικού αναγνωριστικού]] για να σχηματίσει μια έγκυρη διεύθυνση πόρου.", "smw-datavalue-parse-error": "Η τιμή «$1» που δόθηκε δεν ήταν κατανοητή.", "smw-datavalue-propertylist-invalid-property-key": "Η λίστα ιδιοτήτων «$1» περιείχε μη έγκυρο κλειδί ιδιότητας «$2».", "smw-datavalue-type-invalid-typeuri": "Ο τύπος «$1» δεν θα μπορούσε να μετατραπεί σε έγκυρη αναπαράσταση URI.", "smw-datavalue-wikipage-invalid-title": "The page type input value \"$1\" contains invalid characters or is incomplete and therefore can cause unexpected results during a query or annotation process.", "smw-datavalue-wikipage-property-invalid-title": "Property \"$1\" (as page type) with input value \"$2\" contains invalid characters or is incomplete and therefore can cause unexpected results during a query or annotation process.", "smw-parser-invalid-json-format": "Ο συντακτικός αναλυτής JSON επέστρεψε το σφάλμα «$1».", "smw-clipboard-copy-link": "Αντιγραφ<PERSON> συνδέσμου στο πρόχειρο", "smw-property-userdefined-fixedtable": "Η «$1» διαμορφώθηκε ως [https://www.semantic-mediawiki.org/wiki/Fixed_properties σταθερή ιδιότητα] και οποιαδήποτε τροποποίηση στη [https://www.semantic-mediawiki.org/wiki/Type_declaration δήλωση τύπου] της απαιτεί είτε να εκτελέσετε το <code>setupStore.php</code> είτε να ολοκληρώσετε την εργασία [[Special:SemanticMediaWiki|«Εγκατάσταση βάσεως δεδομένων και αναβάθμιση»]].", "smw-data-lookup": "Γίνεται λήψη των δεδομένων...", "smw-no-data-available": "Δεν υπάρχουν διαθέσιμα δεδομένα.", "smw-property-req-violation-change-propagation-locked-error": "Η ιδιότητα \"$1\" τροποποιήθηκε και απαιτεί την επαναξιολόγηση εκχωρισμένων οντοτήτων χρησιμοποιώντας μια διαδικασία [https://www.semantic-mediawiki.org/wiki/Change_propagation διάδοσης αλλαγών]. Η σελίδα της ιδιότητας έχει κλειδωθεί έως ότου ολοκληρωθεί η ενημέρωση της κύριας προδιαγραφής για να αποφευχθούν ενδιάμεσες διακοπές ή αντιφατικές προδιαγραφές. Η διαδικασία μπορεί να πάρει κάποιο χρόνο πριν να ξεκλειδωθεί η σελίδα, καθώς εξαρτάται από το μέγεθος και τη συχνότητα του χρονοπρογραμματιστή [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue ουράς εργασιών].", "smw-property-req-violation-change-propagation-locked-warning": "Η ιδιότητα «$1» τροποποιήθηκε και απαιτεί την επαναξιολόγηση εκχωρισμένων οντοτήτων χρησιμοποιώντας μια διαδικασία [https://www.semantic-mediawiki.org/wiki/Change_propagation διάδοσης αλλαγών]. Η ενημέρωση μπορεί να πάρει κάποιο χρόνο, καθώς εξαρτάται από το μέγεθος και τη συχνότητα του χρονοπρογραμματιστή [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue ουράς εργασιών] και προτείνεται να αναβληθούν αλλαγές στην ιδιότητα για να αποφευχθούν ενδιάμεσες διακοπές ή αντιφατικές προδιαγραφές.", "smw-property-req-violation-change-propagation-pending": "Οι ενημερώσεις της [https://www.semantic-mediawiki.org/wiki/Change_propagation διάδοσης αλλαγών] εκκρεμούν ($1 {{PLURAL:$1|εκτιμώμενη|εκτιμώμενες}} [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|εργασία|εργασίες}}]) και συνιστάται να περιμένετε με τις τροποποιήσεις κάποιας ιδιότητας έως ότου ολοκληρωθεί η διαδικασία για να αποφευχθούν ενδιάμεσες διακοπές ή αντιφατικές προδιαγραφές.", "smw-property-req-error-list": "Η ιδιότητα περιέχει τα ακόλουθα σφάλματα ή προειδοποιήσεις:", "smw-category-change-propagation-locked-error": "Η κατηγορία «$1» τροποποιήθηκε και απαιτεί την επαναξιολόγηση εκχωρισμένων οντοτήτων χρησιμοποιώντας μια διαδικασία [https://www.semantic-mediawiki.org/wiki/Change_propagation διάδοσης αλλαγών]. Στο μεταξύ, η σελίδα της κατηγορίας έχει κλειδωθεί έως ότου ολοκληρωθεί η ενημέρωση της κύριας προδιαγραφής για να αποφευχθούν ενδιάμεσες διακοπές ή αντιφατικές προδιαγραφές. Η διαδικασία μπορεί να πάρει κάποιο χρόνο πριν να ξεκλειδωθεί η σελίδα, καθώς εξαρτάται από το μέγεθος και τη συχνότητα του χρονοπρογραμματιστή [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue ουράς εργασιών].", "smw-category-change-propagation-locked-warning": "Η κατηγορία «$1» τροποποιήθηκε και απαιτεί την επαναξιολόγηση εκχωρισμένων οντοτήτων χρησιμοποιώντας μια διαδικασία [https://www.semantic-mediawiki.org/wiki/Change_propagation διάδοσης αλλαγών]. Η ενημέρωση μπορεί να πάρει κάποιο χρόνο, καθώς εξαρτάται από το μέγεθος και τη συχνότητα του χρονοπρογραμματιστή [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue ουράς εργασιών] και προτείνεται να αναβληθούν αλλαγές στην κατηγορία για να αποφευχθούν ενδιάμεσες διακοπές ή αντιφατικές προδιαγραφές.", "smw-category-change-propagation-pending": "Οι ενημερώσεις της [https://www.semantic-mediawiki.org/wiki/Change_propagation διάδοσης αλλαγών] εκκρεμούν ($1 {{PLURAL:$1|εκτιμώμενη|εκτιμώμενες}} [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|εργασία|εργασίες}}]) και συνιστάται να περιμένετε με τις τροποποιήσεις κάποιας κατηγορίας έως ότου ολοκληρωθεί η διαδικασία για να αποφευχθούν ενδιάμεσες διακοπές ή αντιφατικές προδιαγραφές.", "smw-category-invalid-value-assignment": "Το «$1» δεν αναγνωρίζεται ως έγκυρη κατηγορία ή σημειογραφία τιμής.", "smw-edit-protection-enabled": "Προστατευμένη από επεξεργασίες (Σημασιολογικό MediaWiki)", "smw-patternedit-protection": "Αυτή η σελίδα προστατεύεται και μπορούν να την επεξεργαστούν μόνο χρήστες με την κατάλληλη [https://www.semantic-mediawiki.org/wiki/Help:Permissions άδεια] <code>smw-patternedit</code> .", "smw-property-predefined-edip": "Η «$1» είναι μια εκ των προτέρων ορισμένη ιδιότητα που παρέχεται από το [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Σημασιολογικό MediaWiki] για να δηλώσει εάν η επεξεργασία είναι προστατευμένη ή όχι.", "smw-format-datatable-emptytable": "Δεν υπάρχουν δεδομένα στον διαθέσιμο πίνακα", "smw-format-datatable-info": "Showing _START_ to _END_ of _TOTAL_ entries", "smw-format-datatable-infoempty": "Showing 0 to 0 of 0 entries", "smw-format-datatable-infofiltered": "(filtered from _MAX_ total entries)", "smw-format-datatable-infothousands": ".", "smw-format-datatable-lengthmenu": "Show _MENU_ entries", "smw-format-datatable-loadingrecords": "Φόρτωση", "smw-format-datatable-processing": "Γίνεται επεξεργασία...", "smw-format-datatable-search": "Αναζήτηση:", "smw-format-datatable-zerorecords": "Δεν βρέθηκαν εγγραφές που να ταιριάζουν", "smw-format-datatable-first": "Πρώτ<PERSON>", "smw-format-datatable-last": "Τελευταίο", "smw-format-datatable-next": "Επόμενο", "smw-format-datatable-previous": "Προηγούμενο", "smw-format-datatable-sortascending": ":Ενεργοποιήστε για να ταξινομήσετε αύξουσα στήλη", "smw-format-datatable-sortdescending": ": Ενεργοποιήστε για να ταξινομήσετε φθίνουσα στήλη", "smw-format-datatable-toolbar-export": "Εξαγωγή", "smw-format-list-other-fields-open": "(", "smw-parser-function-expensive-execution-limit": "Η συνάρτηση του συντακτικού αναλυτή έχει φτάσει στο όριό της για ακριβές εκτελέσεις (βλ. παράμετρο διαμόρφωσης [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit $smwgQExpensiveExecutionLimit]).", "smw-postproc-queryref": "Το Σημασιολογικό MediaWiki ανανεώνει την τρέχουσα σελίδα με την προϋπόθεση ότι απαιτείται κάποια μετα-επεξεργασία ερωτημάτων.", "apihelp-smwtask-param-task": "Ορίζει τον τύπο εργασίας", "smw-api-invalid-parameters": "Μη έγκυρες παράμετροι, «$1»", "smw-property-page-list-count": "Εμφάνιση $1 {{PLURAL:$1|σελίδας που χρησιμοποιεί|σελίδων που χρησιμοποιούν}} αυτήν την ιδιότητα.", "smw-property-reserved-category": "Κατηγορία", "smw-category": "Κατηγορία", "smw-datavalue-uri-invalid-scheme": "Το \"$1\" δεν έχει καταχωρηθεί ως έγκυρο σχήμα  URI .", "smw-browse-property-group-title": "Ομάδα ιδιοτήτων", "smw-browse-property-group-label": "Ταμπέλα ομάδας ιδιοτήτων", "smw-browse-property-group-description": "Περιγρα<PERSON><PERSON> ομάδας ιδιοτήτων", "smw-filter": "Φιλτράρισμα", "smw-section-expand": "Επέκταση της ενότητας", "smw-section-collapse": "Σύμπτυξη της ενότητας", "smw-help": "Βοήθεια", "smw-cheat-sheet": "Σκονάκι", "smw-personal-jobqueue-watchlist": "Λίστα παρακολούθησης ουράς εργασιών", "smw-expand": "Ανάπτυξη", "smw-collapse": "Σύμπτυξη", "smw-copy": "Αντιγραφή", "smw-copy-clipboard-title": "Αντιγράφει περιεχόμενο στο πρόχειρο", "smw-jsonview-expand-title": "Επεκτείνει την προβολή JSON", "smw-jsonview-collapse-title": "Συμπτύσσει την προβολή JSON", "smw-jsonview-search-label": "Αναζήτηση:", "smw-types-title": "Τύπος: $1", "smw-schema-error-title": "{{PLURAL:$1|Σφάλμα|Σφάλματα}} επικύρωσης", "smw-schema-error-json": "Σφάλμα JSON: «$1»", "smw-schema-validation-schema-title": "Σημασιολογικό σχήμα JSON", "smw-schema-summary-title": "Σύνοψη", "smw-schema-title": "Σημασιολογικό σχήμα", "smw-schema-usage": "<PERSON>ρ<PERSON><PERSON><PERSON>", "smw-schema-type": "Τύ<PERSON><PERSON> σημασιολογικού σχήματος", "smw-schema-type-description": "Περιγραφή τύπου", "smw-schema-description": "Περιγρα<PERSON><PERSON> σημασιολογικού σχήματος", "smw-schema-tag": "{{PLURAL:$1|Ετικέτα|Ετικέτες}}", "smw-ask-title-keyword-type": "Αναζήτηση με λέξεις κλειδιά", "smw-parameter-missing": "Η παράμετρος «$1» απουσιάζει.", "smw-property-tab-usage": "<PERSON>ρ<PERSON><PERSON><PERSON>", "smw-property-tab-redirects": "Συνώνυμα", "smw-property-tab-subproperties": "Υποϊδιότητες", "smw-property-tab-errors": "Ακατάλληλες αναθέσεις", "smw-concept-tab-list": "Κατ<PERSON><PERSON><PERSON><PERSON>ος", "smw-concept-tab-errors": "Σφάλματα", "smw-ask-tab-result": "Αποτέλεσμα", "smw-pendingtasks-setup-tasks": "Εργασίες", "smw-report": "Αναφορά", "smw-legend": "Υπόμνημα", "smw-entity-examiner-associated-revision-mismatch": "Αναθεώρηση", "smw-listingcontinuesabbrev": "συνεχίζεται", "smw-showingresults": "Παρακ<PERSON>τω {{PLURAL:$1|εμφανίζεται μέχρι <strong>1</strong> αποτέλεσμα|εμφανίζονται μέχρι <strong>$1</strong> αποτελέσματα}} ξεκινώντας από το Νο <strong>$2</strong>."}