{"@metadata": {"authors": ["A Chinese Wikipedian", "A Retired User", "A2093064", "Anakmalaysia", "Assoc", "<PERSON><PERSON><PERSON>", "Cwlin0416", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "H78c67c", "Hello903hello", "<PERSON><PERSON><PERSON><PERSON>", "Justincheng12345", "<PERSON><PERSON>", "LNDDYL", "Laundry Machine", "Littletung", "Liuxinyu970226", "Mark85296341", "McDut<PERSON><PERSON>", "Oapbtommy", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Steph<PERSON>", "SunAfterRain", "Sunny00217", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Willy1018", "<PERSON>", "Wrightbus", "<PERSON><PERSON><PERSON>uk", "Xiplus", "予弦", "列维劳德", "捍粵者", "机智的小鱼君", "神樂坂秀吉", "아라"]}, "smw-desc": "讓您的 Wiki 更容易使用 - 不論是對於機器''或''人類（[https://www.semantic-mediawiki.org/wiki/Help:User_manual 線上說明文件]）", "smw-error": "錯誤", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] 已安裝完並且已啟用，但缺少符合的[https://www.semantic-mediawiki.org/wiki/Help:Upgrade 更新鍵]。", "smw-upgrade-release": "釋出", "smw-upgrade-progress": "進度", "smw-upgrade-progress-explain": "關於升級會何時完成的估計是難以預測的，因為這取決於資料存放大小以及可用的硬體，而規模較大的 wiki 會需要一段時間才能完成。\n\n請聯絡您本地的管理員來獲得更多有關進度的資訊。", "smw-upgrade-progress-create-tables": "正在建立（或更新）表格與索引…", "smw-upgrade-progress-post-creation": "正在執行建立後工作…", "smw-upgrade-progress-table-optimization": "正在將表格最佳化…", "smw-upgrade-progress-supplement-jobs": "正在新增補充作業…", "smw-upgrade-error-title": "錯誤 » Semantic MediaWiki", "smw-upgrade-error-why-title": "為什麼我會看到此頁面？", "smw-upgrade-error-why-explain": "Semantic MediaWiki 的內部資料庫架構已更改，並且需要做出一些調整來讓功能齊全，這可能有包含以下數種原因：\n* 已添加額外固定屬性（需要額外表格設定）\n* 更新包含一些對於表格的變動，或是在存取資料之前索引有必要產生中斷\n* 對於存儲或查詢引擎的更改", "smw-upgrade-error-how-title": "我要如何修正此錯誤？", "smw-upgrade-error-how-explain-admin": "管理員（或任何具有管理權限的人）需運行 MediaWiki 的 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] 或 Semantic MediaWiki 的 [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] 維護手稿。", "smw-upgrade-error-how-explain-links": "您可以參考以下頁面來獲得進一步協助：\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation 安裝]指引\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting 問題排除]說明頁面", "smw-extensionload-error-why-title": "為什麼我會看到此頁面？", "smw-extensionload-error-why-explain": "使用 <code>enableSemantics</code> 時<b>不能</b>載入擴充，改由透過像是直接使用 <code>wfLoadExtension( 'SemanticMediaWiki' )</code> 來啟用。", "smw-extensionload-error-how-title": "我要如何修正此錯誤？", "smw-extensionload-error-how-explain": "要啟用擴充套件以及防止命名空間聲明問題和待定設置，需要使用 <code>enableSemantics</code>，這會確保在透過 <code>ExtensionRegistry</code> 載入擴充套件之前所需的變數已被設定。\n\n請查看 [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] 說明頁面來獲得更多幫助。", "smw-upgrade-maintenance-title": "維護 » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "為什麼我會看到此頁面？", "smw-upgrade-maintenance-note": "系統目前正擴充[https://www.semantic-mediawiki.org/ Semantic MediaWiki]與[https://www.semantic-mediawiki.org/wiki/Help:Upgrade 更新]該資料儲存庫，在wiki可存取前，希望您能耐心等候讓保養得以繼續。", "smw-upgrade-maintenance-explain": "該擴充嘗試靠著推遲多數維護任務到 <code>update.php</code> 之後，來減輕影響與停機時間 ，但一些資料庫相關的變更需要先完成以避免資料內容不一致。包含有：\n* 更改表格的結構，例如添加新的或變動現有欄位\n* 更改或添加表格的索引\n* 運行表格最佳化（當有啟用時）", "smw-semantics-not-enabled": "Semantic MediaWiki 功能沒有為此 wiki 啟用。", "smw_viewasrdf": "訂閱 RDF feed", "smw_finallistconjunct": "和", "smw-factbox-head": "...更多有關「$1」", "smw-factbox-facts": "事實", "smw-factbox-facts-help": "顯示由某一使用者所創建的聲明和事實", "smw-factbox-attachments": "附件", "smw-factbox-attachments-value-unknown": "N/A", "smw-factbox-attachments-is-local": "本地", "smw-factbox-attachments-help": "顯示所有可用附件", "smw-factbox-facts-derived": "衍生的事實", "smw-factbox-facts-derived-help": "顯示衍生自規則的實情，或是在其它推理技術幫助下所生成的實情", "smw_isspecprop": "此屬性為此 Wiki 的特殊屬性。", "smw-concept-cache-header": "暫存使用", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count 概念快取]包含 {{PLURAL:$1|'''1'''|'''$1'''}} 個實體（$2）。", "smw-concept-no-cache": "沒有可用快取。", "smw_concept_description": "概念 \"$1\" 的描述", "smw_no_concept_namespace": "概念只能定義於命名空間 Concept: 中。", "smw_multiple_concepts": "每頁概念頁面只能擁有一個概念定義。", "smw_concept_cache_miss": "因為維基設定需要概念 \"$1\" 在離線時計算，此概念現在無法使用。\n如果這個問題在一段時間後仍未解決，請向網站管理員請求令該概念可用。", "smw_noinvannot": "不能為逆屬性指定值。", "version-semantic": "語意擴充功能", "smw_baduri": "不允許 \"$1\" 形式的 URI。", "smw_printername_count": "計算結果", "smw_printername_csv": "匯出為 CSV", "smw_printername_dsv": "匯出為 DSV", "smw_printername_debug": "偵錯查詢 (用於專業人士)", "smw_printername_embedded": "嵌入頁面內容", "smw_printername_json": "匯出為 JSON", "smw_printername_list": "清單", "smw_printername_plainlist": "純清單", "smw_printername_ol": "編號清單", "smw_printername_ul": "符號清單", "smw_printername_table": "表格", "smw_printername_broadtable": "寬版表格", "smw_printername_template": "模板", "smw_printername_templatefile": "模板檔案", "smw_printername_rdf": "匯出為 RDF", "smw_printername_category": "類別", "validator-type-class-SMWParamSource": "內容", "smw-paramdesc-limit": "查詢結果數量上限", "smw-paramdesc-offset": "第一個結果的偏差", "smw-paramdesc-headers": "顯示標頭/屬性名稱", "smw-paramdesc-mainlabel": "主要頁面的標籤名稱", "smw-paramdesc-link": "以連結顯示數值", "smw-paramdesc-intro": "查詢結果前顯示的文字", "smw-paramdesc-outro": "查詢結果後顯示的文字", "smw-paramdesc-default": "查詢結果為空時顯示的文字", "smw-paramdesc-sep": "結果之間的分隔符號", "smw-paramdesc-propsep": "結果記錄的内容之間的分隔符號", "smw-paramdesc-valuesep": "用於結果裡屬性值之間的分隔符號", "smw-paramdesc-showsep": "顯示分隔符號於 CSV 檔案的頂端 (\"sep=<value>\")", "smw-paramdesc-distribution": "顯示出現的次數，不顯示所有數值。", "smw-paramdesc-distributionsort": "依出現次數排序數值分布。", "smw-paramdesc-distributionlimit": "限制數值分佈只計算特殊數值。", "smw-paramdesc-aggregation": "指定聚集應與什麼相關", "smw-paramdesc-template": "用來顯示列印頁面的模板名稱", "smw-paramdesc-columns": "所要顯示結果的欄數", "smw-paramdesc-userparam": "使用模板時，傳遞給模板的參數值", "smw-paramdesc-class": "為清單設定的額外 CSS 類別", "smw-paramdesc-introtemplate": "查詢結果前顯示的模板名稱", "smw-paramdesc-outrotemplate": "查詢結果後顯示的模板名稱", "smw-paramdesc-embedformat": "定義標題的 HTML 標籤", "smw-paramdesc-embedonly": "不顯示標題", "smw-paramdesc-table-class": "額外套用到表格上的 CSS 類別", "smw-paramdesc-table-transpose": "垂直顯示表格標題，水平顯示結果", "smw-paramdesc-prefix": "控制在列印輸出裡命名空間的顯示", "smw-paramdesc-rdfsyntax": "使用 RDF 語法", "smw-paramdesc-csv-sep": "指定的欄位分隔符號", "smw-paramdesc-csv-valuesep": "指定一個值的分隔符號", "smw-paramdesc-csv-merge": "與相同主題識別字（或稱首欄）來合併列和欄的值", "smw-paramdesc-csv-bom": "在輸出檔案的頂端添加 BOM（訊號位元組順序字元）", "smw-paramdesc-dsv-separator": "使用的分隔符號", "smw-paramdesc-dsv-filename": "DSV 檔案名稱", "smw-paramdesc-filename": "輸出的檔案名稱", "smw-smwdoc-description": "顯示所有參數的表，它們可以與預設值和描述一起用於指定的結果格式。", "smw-smwdoc-default-no-parameter-list": "此結果格式未提供格式特定參數。", "smw-smwdoc-par-format": "顯示參數文件的結果格式。", "smw-smwdoc-par-parameters": "要顯示哪些參數。\"specific\" (專用) 表示該格式所新增那些的，\"base\" (基礎) 表示所有格式當中可用的那些，而\"all\" (全部) 表示兩種情況具有。", "smw-paramdesc-sort": "排序查詢的屬性", "smw-paramdesc-order": "查詢排序的順序", "smw-paramdesc-searchlabel": "繼續搜尋文字", "smw-paramdesc-named_args": "請指定傳遞給該模板的變數", "smw-paramdesc-template-arguments": "設定命名參數如何傳遞到模板", "smw-paramdesc-import-annotation": "額外的帶註釋資料在解析主題期間都會被複製", "smw-paramdesc-export": "匯出選項", "smw-paramdesc-prettyprint": "美化輸出顯示額外縮排與換行", "smw-paramdesc-json-unescape": "包含未跳脫斜線以及多位元組萬國碼字元的輸出", "smw-paramdesc-json-type": "序列化類型", "smw-paramdesc-source": "替代的查詢來源", "smw-paramdesc-jsonsyntax": "使用 JSON 語法", "smw-printername-feed": "訂閱 RSS 與 Atom feed", "smw-paramdesc-feedtype": "訂閱類型", "smw-paramdesc-feedtitle": "訂閱來源的標題文字", "smw-paramdesc-feeddescription": "訂閱來源的描述文字", "smw-paramdesc-feedpagecontent": "訂閱來源顯示的頁面內容", "smw-label-feed-description": "訂閱 $1 $2 feed", "smw-paramdesc-mimetype": "用於輸出檔案的媒體類型（MIME 類型）", "smw_iq_disabled": "此 wiki 的語義查詢已被停用", "smw_iq_moreresults": "...更多結果", "smw_parseerror": "輸入的值無法理解。", "smw_notitle": " \"$1\" 不得被用做此 wiki 的頁面名稱。", "smw_noproperty": "\"$1\"不能用作此 wiki 中的一個屬性名稱。", "smw_wrong_namespace": "此處只允許命名空間 \"$1\" 中的頁面。", "smw_manytypes": "此屬性定義了一種以上類型。", "smw_emptystring": "不接受空字元串。", "smw_notinenum": "「$1」不在用於「$3」屬性[[Property:Allows value|所允許值]]的清單（$2）中。", "smw-datavalue-constraint-error-allows-value-list": "「$1」不在用於「$3」屬性[[Property:Allows value|所允許值]]的清單（$2）中。", "smw-datavalue-constraint-error-allows-value-range": "「$1」沒有符合用於限制屬性「$3」的[[Property:Allows value|允許值]]所指定的「$2」範圍。", "smw-constraint-error": "限制問題", "smw-constraint-error-suggestions": "請檢查所列出違反內容與帶有其註釋值的屬性，來確認符合所有限制的需求。", "smw-constraint-error-limit": "清單最多包含 $1 個違反內容。", "smw_noboolean": "\"$1\" 不是布林 (是非) 值", "smw_true_words": "true,t,yes,y,是,真,對", "smw_false_words": "false,f,no,n,否,假,錯", "smw_nofloat": "「$1」不是數字。", "smw_infinite": " \"$1\" 數值過大，無法支援。", "smw_unitnotallowed": "\"$1\"未被聲明為此屬性的一個有效的度量單位。", "smw_nounitsdeclared": "此屬性沒有聲明任何度量單位。", "smw_novalues": "未設定數值。", "smw_nodatetime": "無法理解日期 \"$1\"", "smw_toomanyclosing": "查詢中 \"$1\" 過多。", "smw_noclosingbrackets": "你的查詢中有未符合\"]]\"的\"<nowiki>[[</nowiki>\"。", "smw_misplacedsymbol": "符號 \"$1\" 的位置無效。", "smw_unexpectedpart": "查詢的 \"$1\" 部分無法理解。\n結果可能與預料的不同。", "smw_emptysubquery": "部份子查詢的條件無效。", "smw_misplacedsubquery": "部份子查詢使用在不允許子查詢的地方。", "smw_valuesubquery": "子查詢不支援屬性 \"$1\" 的值", "smw_badqueryatom": "部份查詢 \"<nowiki>[[...]]</nowiki>\" 無法理解。", "smw_propvalueproblem": "屬性 \"$1\" 的值無法理解。", "smw_noqueryfeature": "此 wiki 不支援一些查詢功能，查詢被部分停止 ($1) 。", "smw_noconjunctions": "此 wiki 不支援查詢中的合取，查詢被部分停止 ($1) 。", "smw_nodisjunctions": "此 wiki 不支援查詢中的析取，查詢被部分停止 ($1) 。", "smw_querytoolarge": "由于此 wiki 在査詢大小或深度上的限制，下列{{PLURAL:$2|查詢準則}}無法列入考慮：<code>$1</code>。", "smw_notemplategiven": "為使查詢格式正常工作，請為參數 \"模板\" 賦值。", "smw_db_sparqlqueryproblem": "無法從 SPARQL 資料庫獲得查詢結果。這個錯誤可能是暫時的，也可能是資料庫軟體的錯誤造成的。", "smw_db_sparqlqueryincomplete": "本查詢太過複雜因而被中止，可能會因此遺失部分結果。如果可能，請嘗試改用一個較簡單的查詢。", "smw_type_header": "類型 \"$1\" 的屬性", "smw_typearticlecount": "使用此類型顯示 $1 {{PLURAL:$1|property|屬性}}", "smw_attribute_header": "使用屬性 \"$1\" 的頁面", "smw_attributearticlecount": "使用此屬性顯示 $1 {{PLURAL:$1|page|頁面}}", "smw-propertylist-subproperty-header": "子屬性", "smw-propertylist-redirect-header": "同義詞", "smw-propertylist-error-header": "不適當分配的頁面", "smw-propertylist-count": "顯示 $1 個相關{{PLURAL:$1|實體|實體}}。", "smw-propertylist-count-with-restricted-note": "顯示 $1 個相關{{PLURAL:$1|實體|實體}}（有更多實體可用，但被限制僅顯示 \"$2\" 個實體）。", "smw-propertylist-count-more-available": "顯示 $1 個相關{{PLURAL:$1|實體|實體}}（有更多實體可用）。", "specialpages-group-smw_group": "語意MediaWiki", "specialpages-group-smw_group-maintenance": "維護", "specialpages-group-smw_group-properties-concepts-types": "屬性、概念、與類型", "specialpages-group-smw_group-search": "瀏覽與搜尋", "exportrdf": "輸出頁面到 RDF", "smw_exportrdf_docu": "這個頁面用於以 RDF 格式獲得頁面資料。\n在下方文字框中輸入標題以匯出頁面，每行一個標題。", "smw_exportrdf_recursive": "遞歸匯出所有相關頁面。\n注意：結果檔案會很大。", "smw_exportrdf_backlinks": "同時匯出與匯出頁面相關的所有頁面。\n產生可瀏覽的 RDF。", "smw_exportrdf_lastdate": "不要匯出自指定時間後無變更的頁面。", "smw_exportrdf_submit": "匯出", "uriresolver": "URI 解析器", "properties": "屬性", "smw-categories": "分類", "smw_properties_docu": "本 wiki 使用以下屬性。", "smw_property_template": "類型為 $2 的屬性 $1 (出現 $3 次)", "smw_propertylackspage": "所有屬性均應有一個頁面描述！", "smw_propertylackstype": "此屬性未指定類型 (目前預設為類型 $1)", "smw_propertyhardlyused": "此屬性在本 wiki 中不常使用。", "smw-property-name-invalid": "無法使用屬性 $1 (無效的屬性名稱)。", "smw-property-name-reserved": "「$1」被列舉為保留名稱，且不應用作屬性。以下[https://www.semantic-mediawiki.org/wiki/Help:Property_naming 幫助頁面]可能包含有關為何該名稱是保留名稱的資訊。", "smw-sp-property-searchform": "顯示包含以下內容的屬性：", "smw-sp-property-searchform-inputinfo": "輸入區分大小寫且當用來搜尋時只會顯示符合條件的屬性。", "smw-special-property-searchform": "顯示包含以下內容的屬性：", "smw-special-property-searchform-inputinfo": "輸入區分大小寫且當用來搜尋時只會顯示符合條件的屬性。", "smw-special-property-searchform-options": "選項", "smw-special-wantedproperties-filter-label": "篩選：", "smw-special-wantedproperties-filter-none": "無", "smw-special-wantedproperties-filter-unapproved": "未批准", "smw-special-wantedproperties-filter-unapproved-desc": "篩選用於權威模式時連接的選項。", "concepts": "概念", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Concepts 概念]可被看做是 \"動態分類\"，即非手動建立的頁面集合，是由 Semantic MediaWiki 從指定查詢中的描述所找出。", "smw-special-concept-header": "概念清單", "smw-special-concept-count": "以下列出{{PLURAL:$1|1 個概念|$1 個概念}}。", "smw-special-concept-empty": "查無概念。", "unusedproperties": "未使用的屬性", "smw-unusedproperties-docu": "此頁面列出已聲明，沒有被其它頁面使用的[https://www.semantic-mediawiki.org/wiki/Unused_properties 未使用屬性]。差別內容請參見[[Special:Properties|全部屬性]]或[[Special:WantedProperties|所需屬性]]特殊頁面。", "smw-unusedproperty-template": "類型為 $2 的屬性 $1", "wantedproperties": "需要的屬性", "smw-wantedproperties-docu": "此頁面列出在 wiki 中使用，但沒有頁面描述的[https://www.semantic-mediawiki.org/wiki/Wanted_properties 所需屬性]。差別內容請參見[[Special:Properties|全部屬性]]或[[Special:UnusedProperties|未使用屬性]]特殊頁面。", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|use|使用}})", "smw-special-wantedproperties-docu": "此頁面列出在 wiki 中使用，但沒有頁面描述的[https://www.semantic-mediawiki.org/wiki/Wanted_properties 所需屬性]。差別內容請參見[[Special:Properties|全部屬性]]或[[Special:UnusedProperties|未使用屬性]]特殊頁面。", "smw-special-wantedproperties-template": "$1（$2 {{PLURAL:$2|次使用}}）", "smw_purge": "重新整理", "smw-purge-update-dependencies": "由於一些需要更新的過時相依內容被檢測出，Semantic MediaWiki 正清除目前的頁面。", "smw-purge-failed": "Semantic MediaWiki 嘗試清除頁面但不成功", "types": "類型", "smw_types_docu": "[https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes 可用資料類型]的列表，每種[https://www.semantic-mediawiki.org/wiki/Help:Datatype 類型]代表唯一的屬性集，以描述儲存方面的值，並顯示遺傳至分配内容的特徵。", "smw-special-types-no-such-type": "「$1」為未知，或是尚未指定成有效的資料型態。", "smw-statistics": "Semantic 語義統計", "smw-statistics-cached": "語意統計（快取）", "smw-statistics-entities-total": "實體（總計）", "smw-statistics-entities-total-info": "估計的實體列次數。此包含屬性、概念，或是任何其它需要分配 ID 的已註冊物件表示。", "smw-statistics-property-instance": "屬性{{PLURAL:$1|值|值}} (總計)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|屬性|屬性}}]] (總計)", "smw-statistics-property-total-info": "已註冊屬性總數。", "smw-statistics-property-total-legacy": "{{PLURAL:$1|屬性|屬性}} (總計)", "smw-statistics-property-used": "{{PLURAL:$1|屬性|屬性}}（使用了至少一個值）", "smw-statistics-property-page": "{{PLURAL:$1|Property|屬性}} (使用頁面註冊)", "smw-statistics-property-page-info": "具有專用頁面與描述之屬性的次數。", "smw-statistics-property-type": "{{PLURAL:$1|Property|屬性}} (指派給資料型態)", "smw-statistics-query-inline-legacy": "$1 條查詢", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|查詢|查詢}}]]（嵌入、總計）", "smw-statistics-query-format": "<code>$1</code>格式", "smw-statistics-query-size": "隊列長度", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|概念|概念}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|概念}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|子物件}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|子物件|子物件}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|資料型態|資料型態}}]]", "smw-statistics-error-count": "{{PLURAL:$1|屬性值}}（[[Special:ProcessingErrorList|{{PLURAL:$1|不正確注釋}}]]）", "smw-statistics-error-count-legacy": "{{PLURAL:$1|屬性值|屬性值}}（{{PLURAL:$1|不正確注釋|不正確注釋}}）", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities 過時的{{PLURAL:$1|實體|實體}}]", "smw-statistics-delete-count-info": "被標記為應被定期所使用的提供維護手稿給移除處置的實體。", "smw_uri_doc": "URI 分析器實現[$1 W3C httpRange-14 標記尋找]。這確保一個 RDF 表示（對於機器）或是 wiki 頁面（對於人類）是個有根據請求的交遞。", "ask": "語意搜尋", "smw-ask-help": "此章節包含一些幫助解釋如何使用 <code>#ask</code> 語法的連結。\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages 選擇頁面]介紹如何選擇頁面，並構造條件\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators 搜索操作]列舉可用的蒐索操作，包含範圍査詢和萬用字元査詢\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Displaying_information 顯示資訊]概述列印輸出說明的用法及格式選項", "smw_ask_sortby": "按列排序 (選填)", "smw_ask_ascorder": "升冪", "smw_ask_descorder": "降冪", "smw-ask-order-rand": "隨機", "smw_ask_submit": "搜尋結果", "smw_ask_editquery": "編輯查詢", "smw_add_sortcondition": "[增加排序條件]", "smw-ask-sort-add-action": "增加排序條件", "smw_ask_hidequery": "隱藏查詢（密集檢視）", "smw_ask_help": "查詢說明", "smw_ask_queryhead": "條件", "smw_ask_printhead": "列印輸出選擇", "smw_ask_printdesc": "（每行一個查詢名）", "smw_ask_format_as": "格式為：", "smw_ask_defaultformat": "預設", "smw_ask_otheroptions": "其他選項", "smw-ask-otheroptions-info": "本節包含改變列印輸出語句的選項。可透過在它們上面懸停滑鼠來查看參數描述。", "smw-ask-otheroptions-collapsed-info": "請使用加號圖示來檢視所有可用的選項", "smw_ask_show_embed": "顯示嵌入代碼", "smw_ask_hide_embed": "隱藏嵌入的代碼", "smw_ask_embed_instr": "使用以下代碼將查詢嵌入維基頁面。", "smw-ask-delete": "移除", "smw-ask-sorting": "排序", "smw-ask-options": "選項", "smw-ask-options-sort": "排序選項", "smw-ask-format-options": "格式和選項", "smw-ask-parameters": "參數", "smw-ask-search": "搜尋", "smw-ask-debug": "除錯", "smw-ask-debug-desc": "產生查詢除錯訊息", "smw-ask-no-cache": "停用查詢快取", "smw-ask-no-cache-desc": "沒有査詢快取的結果", "smw-ask-result": "結果", "smw-ask-empty": "清空所有項目", "smw-ask-download-link-desc": "以 $1 格式來下載查詢結果", "smw-ask-format": "格式", "smw-ask-format-selection-help": "有關選定格式的幫助：$1", "smw-ask-condition-change-info": "條件已改變，搜尋引擎需要重新運行査詢來產生匹配新要求的結果。", "smw-ask-input-assistance": "輸入說明", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 輸入幫助]提供於列印輸出、排序、條件欄位方面。條件欄位需要以下字首之一：", "smw-ask-condition-input-assistance-property": "<code>p:</code> 檢索屬性建議（例如：<code>[[p:Has ...</code>）", "smw-ask-condition-input-assistance-category": "<code>c:</code> 來索取分類建議", "smw-ask-condition-input-assistance-concept": "<code>con:</code> 來索取概念建議", "smw-ask-format-change-info": "格式已變動，並且需要再次執行查詢來比對新參數和視覺化選項。", "smw-ask-format-export-info": "所選格式為不具有視覺化呈現的匯出格式，因此內容結果僅能提供下載。", "smw-ask-query-search-info": "查詢 <code><nowiki>$1</nowiki></code> 由 {{PLURAL:$3|1=<code>$2</code>（來自快取）|<code>$2</code>（來自快取）|<code>$2</code>}}在 $4 {{PLURAL:$4|秒|秒}}內回應。", "smw-ask-extra-query-log": "查詢日誌", "smw-ask-extra-other": "其他", "searchbyproperty": "按屬性查詢", "processingerrorlist": "處理錯誤清單", "constrainterrorlist": "限制錯誤清單", "propertylabelsimilarity": "屬性標籤相似度報告", "missingredirectannotations": "缺少重新導向註解", "smw-processingerrorlist-intro": "以下清單提供有關出現在連接 [https://www.semantic-mediawiki.org/ Semantic MediaWiki] 時的[https://www.semantic-mediawiki.org/wiki/Processing_errors 處理錯誤]方面概要。建議定期查看此清單，並修正無效的值註解。", "smw-constrainterrorlist-intro": "以下清單提供有關出現在連接 [https://www.semantic-mediawiki.org/ Semantic MediaWiki] 時的[https://www.semantic-mediawiki.org/wiki/Constraint_errors 限制錯誤]方面概要。建議定期查看此清單，並修正無效的值註解。", "smw-missingredirects-intro": "以下段落會列出在 Semantic MediaWiki 裡缺少[https://www.semantic-mediawiki.org/wiki/Redirects 重新導向]通知的頁面（透過比較儲存在 MediaWiki 的資訊），並以手動[https://www.semantic-mediawiki.org/wiki/Help:Purge 清除]頁面或是運行 <code>rebuildData.php</code> 維護手稿（帶選項 <code>--redirects</code>）來重新儲存這些註解。", "smw-missingredirects-list": "缺少註解的頁面", "smw-missingredirects-list-intro": "顯示 $1 個缺少重新導向註解的{{PLURAL:$1|頁面}}。", "smw-missingredirects-noresult": "沒有找出缺少重新導向註解。", "smw_sbv_docu": "搜尋所有已設定屬性和值的頁面。", "smw_sbv_novalue": "為此屬性輸入一個有效值，或者顯示所有 \"$1\" 的屬性值。", "smw_sbv_displayresultfuzzy": "有值為 \"$2\" 的屬性 \"$1\" 的所有頁面清單。\n由於精確相符的頁面不多，同時列出了部分有相似值的頁面。", "smw_sbv_property": "屬性：", "smw_sbv_value": "值：", "smw_sbv_submit": "搜尋結果", "browse": "瀏覽 wiki", "smw_browselink": "瀏覽屬性", "smw_browse_article": "輸入瀏覽起始頁面名稱。", "smw_browse_go": "進入", "smw_browse_show_incoming": "顯示連入屬性", "smw_browse_hide_incoming": "隱藏連入屬性", "smw_browse_no_outgoing": "這個頁面沒有設定屬性。", "smw_browse_no_incoming": "沒有屬性連結到此頁。", "smw-browse-from-backend": "訊息目前正從後端取得。", "smw-browse-intro": "此頁面提供有關主題或實體實例的詳情，請輸入物件名稱來檢查。", "smw-browse-invalid-subject": "主題驗證返回“$1”錯誤。", "smw-browse-api-subject-serialization-invalid": "主題有無效的序列化格式。", "smw-browse-js-disabled": "似乎是 JavaScript 被停用或無法使用，我們建議使用支援 Javascript 的瀏覽器。其它選項有在 [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>] 設置參數頁面裡被提及到。", "smw-browse-show-group": "顯示群組", "smw-browse-hide-group": "隱藏群組", "smw-noscript": "此頁面或操作需要 JavaScript 才能執行。請在您的瀏覽器上啟用 JavaScript，或是使用支援 JavaScript 的瀏覽器，以讓依據要求所提供的功能可被提供。進一步的詳細內容，請查看 [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript] 說明頁面。", "smw_inverse_label_default": "的$1", "smw_inverse_label_property": "逆屬性標籤", "pageproperty": "頁面屬性搜尋", "pendingtasklist": "待辦任務清單", "facetedsearch": "多面搜尋", "smw_pp_docu": "輸入頁面和屬性，或是僅輸入屬性來索取所有分配的值。", "smw_pp_from": "從頁面：", "smw_pp_type": "屬性：", "smw_pp_submit": "搜尋結果", "smw-prev": "前{{PLURAL:$1|$1}}", "smw-next": "後{{PLURAL:$1|$1}}", "smw_result_prev": "上一個", "smw_result_next": "下一個", "smw_result_results": "結果", "smw_result_noresults": "沒有結果。", "smwadmin": "Semantic MediaWiki 功能板", "smw-admin-statistics-job-title": "任務統計", "smw-admin-statistics-job-docu": "任務統計顯示出有關已安排但尚未執行的 Semantic MediaWiki 任務。任務數目可能會有些不精準或是包含到失敗的嘗試，請查閱[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 手冊]來獲得進一步資訊。", "smw-admin-statistics-querycache-title": "查詢快取", "smw-admin-statistics-querycache-disabled": "[https://www.semantic-mediawiki.org/wiki/QueryCache 查詢快取]在此 wiki 未啟用，因此沒有可用的統計內容。", "smw-admin-statistics-querycache-legend": "快取統計為含有臨時累計內容和衍生資料，包括： \n* \"misses\" 是嘗試從快取索取資料但帶有無效回應，而必須直接做儲存（資料庫、三重儲存或其它）檢索的總數\n* \"deletes\" 代表回收快取操作（透過清除或是依靠查詢）的總數量\n* \"hits\" 含有來自內嵌（來自以 wiki 頁面的查詢呼叫）或非內嵌（如有啟用，由像是 Special:Ask 頁面或是 API 的請求） 來源索取的快取數量\n* \"medianRetrievalResponseTime\" 是用於超過收集過程時間段落的快取、非快取檢索請求回應時間（以秒為單位）中位數的取向值\n* \"noCache\" 表示沒有嘗試從快取來索取結果的次數（limit=0 的查詢，或是 'no-cache' 等其它選項）", "smw-admin-statistics-section-explain": "提供給管理員額外統計的段落。", "smw-admin-statistics-semanticdata-overview": "概要", "smw-admin-permission-missing": "出於缺少權限緣故，被阻止存取此頁面，請參閱[https://www.semantic-mediawiki.org/wiki/Help:Permissions 權限]說明頁面來獲得更多有關所需設定的詳情。", "smw-admin-setupsuccess": "儲存引擎已設定。", "smw_smwadmin_return": "返回「$1」", "smw_smwadmin_updatestarted": "更新語意資料過程開始。\n所有已儲存資料將被按需重建或修復。\n你可以透過本特殊頁面追蹤更新程序。", "smw_smwadmin_updatenotstarted": "已有更新程序正在執行，請勿建立另一個。", "smw_smwadmin_updatestopped": "已停止所有更新程序。", "smw_smwadmin_updatenotstopped": "若要停止更新程序，您必須勾選核取方塊來表示您確實如此要求。", "smw-admin-docu": "這個特殊頁面為您安裝、升級、維護和使用 <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> 時提供說明，並提供進一步的管理功能和任務，以及統計。\n請記得在執行管理功能前備份好資料。", "smw-admin-environment": "軟體環境", "smw-admin-db": "資料庫設定", "smw-admin-db-preparation": "表格正在初始化，依大小及可能的表格最佳化來顯示結果前可能會花一段時間。", "smw-admin-dbdocu": "Semantic MediaWiki 需要本身所持有的資料庫架構來儲存語意資料（這獨立於 MediaWiki，不會對 MediaWiki 的安裝部份造成影響）\n這個設定功能可多次執行，不會造成任何危害。但是只需在安裝或升級時執行一次即可。", "smw-admin-permissionswarn": "如果操作因 SQL 錯誤而失敗，可能是您 wiki 所用的資料庫使用者（可在您的 LocalSettings.php 檔案上檢查）沒有相應權限。您可以向該使用者賦予建立和刪除表格的權限，或臨時將資料庫 root 使用者的登入資訊輸入在 LocalSettings.php　檔案，或是使用維護手稿 <code>setupStore.php</code>，該手稿可以使用 LocalSettings.php 中的資訊。", "smw-admin-dbbutton": "初始化或升級表格", "smw-admin-announce": "發佈 wiki", "smw-admin-announce-text": "若您的 wiki 是公開的，您可以在 <a href=\"https://wikiapiary.com\">WikiApiary</a> 註冊它，這是用來追蹤 wiki 的 wiki。", "smw-admin-deprecation-notice-title": "棄用通知", "smw-admin-deprecation-notice-docu": "下面段落包含已被棄用或移除；但在此 wiki 上發現有被啟動的設定。今起往後任何發行版本都將移除對這些配置的支援。", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> 已棄用，並將在 $2 版本裡移除", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> 將移除（或替換）以下{{PLURAL:$2|選項}}：", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code>已棄用，並將在$2中移除", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> 已被替換為 <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> 已被 <code>$2</code> 取代", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|選項}}：", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> 正在替換為 <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>已在 $2 版本中被移除", "smw-admin-deprecation-notice-title-notice": "棄用的設定", "smw-admin-deprecation-notice-title-notice-explanation": "<b>棄用設定</b>顯示出在此 wiki 上所偵測到使用的設定會計畫在之後釋出的版本裡移除或更改。", "smw-admin-deprecation-notice-title-replacement": "已替換或重新命名的設定", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>取代或重新命名設定</b>包含已重新命名或或是其它變動的設定，建議立即更新其名稱或格式。", "smw-admin-deprecation-notice-title-removal": "已移除的設定", "smw-admin-deprecation-notice-title-removal-explanation": "<b>移除設定</b>標識的設定已在上一個發佈版本中移除，但在此 wiki 上被檢測到有使用著。", "smw-admin-deprecation-notice-section-legend": "圖例", "smw-smwadmin-refresh-title": "資料修復與更新", "smw_smwadmin_datarefresh": "資料重建", "smw_smwadmin_datarefreshdocu": "可以基於維基先有內容對所有 Semantic MediaWki 資料進行重置。\n這個功能對修復損壞的資料，或者在軟體升級，內部格式改變時更新資料非常有用。\n更新時逐頁面執行的，需要一段時間。\n下方將顯示更新是否正在進行，並允許你開始或終止更新。 (此功能可以被網站管理員停用)", "smw_smwadmin_datarefreshprogress": "<strong>有一個更新正在進行。</strong>\n更新程序可能會很慢，因為該程序僅在使用者訪問維基時小塊地更新資料。\n可以使用 MediaWiki 維護腳本<code>runJobs.php</code> (使用選項 <code>--maxjobs 1000</code> 限制每批更新數目) 來加快更新完成速度。\n目前更新的估計進度：", "smw_smwadmin_datarefreshbutton": "安排資料重建", "smw_smwadmin_datarefreshstop": "停止更新", "smw_smwadmin_datarefreshstopconfirm": "是的，我{{GENDER:$1|確認}}。", "smw-admin-job-scheduler-note": "在此段落的任務（已啟用）會透過任務佇列進行，以避免在執行期間出現死結狀況。[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 任務佇列]負責程序，在緊要時可採用 <code>runJobs.php</code> 維護用手稿檔案（另參見設置參數 <code>$wgRunJobsAsync</code>）。", "smw-admin-outdateddisposal-title": "過時實體處置", "smw-admin-outdateddisposal-intro": "部分行動（對於屬性類型的更改、wiki 頁面的移除、或錯誤值的糾正）的結果會在[https://www.semantic-mediawiki.org/wiki/Outdated_entities 過時實體]上，並建議定期移除它們以釋放相關表格空間。", "smw-admin-outdateddisposal-active": "過時實體處置任務已加入排程。", "smw-admin-outdateddisposal-button": "安排處置", "smw-admin-feature-disabled": "該功能已在此 wiki 上禁用。請查閱<a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">設定</a>幫助頁面或聯絡系統管理員。", "smw-admin-propertystatistics-title": "屬性統計重建", "smw-admin-propertystatistics-intro": "重建整個屬性使用統計以及其中的更新，並更正[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count usage 使用計數]屬性。", "smw-admin-propertystatistics-active": "屬性統計重建任務已加入排程。", "smw-admin-propertystatistics-button": "安排統計重建", "smw-admin-fulltext-title": "重建全文搜尋", "smw-admin-fulltext-intro": "從啟用[https://www.semantic-mediawiki.org/wiki/Full-text 全文搜索]資料類型的屬性表格來重建搜尋索引。對索引規則的更改（改變的停用詞、新的詞幹等）以及/或是新的已添加或已變動表格，需要重新運行此任務。", "smw-admin-fulltext-active": "全文搜尋重建任務已加入排程。", "smw-admin-fulltext-button": "排程重建全文", "smw-admin-support": "獲得協助", "smw-admin-supportdocu": "已提供各種資源來幫助您解决以下問題：", "smw-admin-installfile": "如果您安裝時遇到問題，請先檢查<a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">安裝文件</a>及<a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">安裝頁面</a>中的指引。", "smw-admin-smwhomepage": "Semantic MediaWiki 的完整使用者文件請查閱 <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>。", "smw-admin-bugsreport": "程式問題可在<a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">問題追蹤</a>報告，<a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">報告錯誤</a>頁面提供一些如何書寫有效的問題報告方面指導。", "smw-admin-questions": "如果有問題或建議，可加入 Semantic MediaWiki <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">使用者郵件清單</a>。", "smw-admin-other-functions": "其它功能", "smw-admin-statistics-extra": "統計功能", "smw-admin-statistics": "統計", "smw-admin-supplementary-section-title": "補充功能", "smw-admin-supplementary-section-subtitle": "支援核心功能", "smw-admin-supplementary-section-intro": "此段落所提供額外功能不在維護活動的範圍之內，並且有些列在這裡的函式（參見[https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions 文檔]）為受到限制或是不可用，因此在此維基上不可取用。", "smw-admin-supplementary-settings-title": "設置與設定", "smw-admin-supplementary-settings-intro": "<u>$1</u>列出定義 Semantic MediaWiki 行為的參數", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "操作統計", "smw-admin-supplementary-operational-statistics-short-title": "操作統計", "smw-admin-supplementary-operational-statistics-intro": "顯示<u>$1</u>的擴充集合", "smw-admin-supplementary-idlookup-title": "實體查找與處置", "smw-admin-supplementary-idlookup-short-title": "實體查找與處置", "smw-admin-supplementary-idlookup-intro": "支援簡單的<u>$1</u>功能", "smw-admin-supplementary-duplookup-title": "重複實體查找", "smw-admin-supplementary-duplookup-intro": "<u>$1</u>用來替所選的表格矩陣找出被歸類為重複內容的項", "smw-admin-supplementary-duplookup-docu": "此頁面列舉來自所選表格的項目，它們被歸類為[https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities 重複項]。重複項（如有）應僅在少數情況下會被終止更新或是未成功的回退事項所導致出。", "smw-admin-supplementary-operational-statistics-cache-title": "快取統計", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u>顯示已選擇的快取相關統計", "smw-admin-supplementary-operational-table-statistics-title": "表格統計", "smw-admin-supplementary-operational-table-statistics-short-title": "表格統計", "smw-admin-supplementary-operational-table-statistics-intro": "為所選的表格集產生<u>$1</u>", "smw-admin-supplementary-operational-table-statistics-explain": "此段落含有關於後端與儲存引擎狀態的已選表格統計，可用來協助管理員與資料策展人做出正確決策。", "smw-admin-supplementary-operational-table-statistics-legend": "圖例描述了一些用於表格統計的鍵且包含有：", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> 為在表格裡的列總數", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> 目前使用的最新 ID\n* <code>duplicate_count</code> 在 the id_table 找到的重複項數目（可另外參考[[Special:SemanticMediaWiki/duplicate-lookup|重複單元查找]]）\n* <code>rows.rev_count</code> 指示出直接的 wiki 頁面連結，有分配到 revision_id 的列數\n* <code>rows.smw_namespace_group_by_count</code> 在表格裡有使用到的命名空間合計數目\n* <code>rows.smw_proptable_hash.query_match_count</code> 帶有對應表格參照的子物件查詢數目\n* <code>rows.smw_proptable_hash.query_null_count</code> 不帶有表格參照（未連結、浮動參考）的子物件查詢數目", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> 唯一項目的百分率（較低的百分率代表著重複的項目佔據著表格內容與索引）\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> 僅出現一次項目的數目\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> 出現一次以上項目的數目", "smw-admin-supplementary-elastic-version-info": "版本", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u>顯示詳細設定與索引統計", "smw-admin-supplementary-elastic-docu": "此頁面包含有關設定的資訊、對應、狀態，以及相關連結到 Semantic MediaWiki 以及其 [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>] 之 Elasticsearch 叢集的索引統計。", "smw-admin-supplementary-elastic-functions": "支援功能", "smw-admin-supplementary-elastic-settings-title": "設定（索引）", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> 由 Elasticsearch 所使用來管理 Semantic MediaWiki 索引", "smw-admin-supplementary-elastic-mappings-title": "對應", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> 用來列出索引與欄位對應", "smw-admin-supplementary-elastic-mappings-docu": "此頁面包含由目前索引所使用的欄位對應詳情。建議監測與 <code>index.mapping.total_fields.limit</code> 關聯的對應。（指定在所允許索引裡的最大欄位數）", "smw-admin-supplementary-elastic-mappings-docu-extra": "當 <code>nested_fields</code> 參照分配給核心欄位、用來支援特定結構搜尋模式的額外欄位之累積次數時，<code>property_fields</code> 參照索引核心欄位次數。", "smw-admin-supplementary-elastic-mappings-summary": "摘要", "smw-admin-supplementary-elastic-mappings-fields": "欄位對應", "smw-admin-supplementary-elastic-nodes-title": "節點", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u>顯示節點統計", "smw-admin-supplementary-elastic-indices-title": "索引", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> 提供可用索引以及其統計內容的概要", "smw-admin-supplementary-elastic-statistics-title": "統計", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> 顯示索引層級統計", "smw-admin-supplementary-elastic-statistics-docu": "此頁面提供發生在索引層級上不同操作的索引統計方面洞察，所回傳統計是以初級內容與總計集成的聚合。[https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html 說明頁面]包含可用索引統計的詳情描述。", "smw-admin-supplementary-elastic-status-replication": "複製狀態", "smw-admin-supplementary-elastic-status-last-active-replication": "上一次有效的複製：$1", "smw-admin-supplementary-elastic-status-refresh-interval": "重整間隔：$1", "smw-admin-supplementary-elastic-status-recovery-job-count": "積壓修復任務：$1（估計值）", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "收取（檔案）任務的積壓：$1（估計值）", "smw-admin-supplementary-elastic-status-rebuild-lock": "複製已鎖定：$1（重建中）", "smw-admin-supplementary-elastic-status-replication-monitoring": "複製監測（有效）：$1", "smw-admin-supplementary-elastic-replication-header-title": "複製狀態", "smw-admin-supplementary-elastic-replication-function-title": "複製", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u>顯示有關複製失敗的資訊", "smw-admin-supplementary-elastic-replication-docu": "此頁面提供關於被回報有 Elasticsearch 叢集問題的實體[https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring 複製狀態]資訊。建議檢閱所列項目，並清除內容以確認是臨時問題。", "smw-admin-supplementary-elastic-replication-files-docu": "對於檔案清單，應注意需先執行[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion 檔案接納]任務，並需完成該處理。", "smw-admin-supplementary-elastic-replication-files": "檔案", "smw-admin-supplementary-elastic-replication-pages": "頁面", "smw-admin-supplementary-elastic-endpoints": "端點", "smw-admin-supplementary-elastic-config": "設置", "smw-admin-supplementary-elastic-no-connection": "Wiki 目前'''無法'''建立對 Elasticsearch 叢集的連結，請聯絡 wiki 管理員來調查此問題，因為這會導致系統的索引與查詢功能失效。", "smw-list-count": "清單包含 $1 個{{PLURAL:$1|項目|項目}}。", "smw-property-label-uniqueness": "「$1」標籤符合了至少一個其它屬性表示內容。請查閱[https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness 幫助頁面]瞭解如何解决該問題。", "smw-property-label-similarity-title": "屬性標籤相似度報告", "smw-property-label-similarity-intro": "<u>$1</u>可計算現有屬性標籤的相似度", "smw-property-label-similarity-threshold": "臨界：", "smw-property-label-similarity-type": "顯示類型 ID", "smw-property-label-similarity-noresult": "找不到符合所選項目的结果。", "smw-property-label-similarity-docu": "此頁面比較兩個屬性標籤間的[https://www.semantic-mediawiki.org/wiki/Property_similarity 相似性差距]（不要被語意或詞彙相似性給混淆），並在超過臨界時做出報告。該報告可幫助篩選掉拼寫錯誤或提供相同概念的等效屬性（請參見[[Special:Properties|屬性]]特殊頁面來協助闡明報告屬性的概念和用法）。用於近似相比的臨界值可被調整增加或減少相似差距。<code>[[Property:$1|$1]]</code>　用於從分析中排除屬性。", "smw-admin-operational-statistics": "此頁面包含存在於或來自 Semantic MediaWiki 相關功能的所收集運作統計。Wiki 特定統計的擴充清單可[[Special:Statistics|<b>在此</b>]]找到。", "smw_adminlinks_datastructure": "資料結構", "smw_adminlinks_displayingdata": "資料顯示", "smw_adminlinks_inlinequerieshelp": "直接插入式查詢幫助", "smw-page-indicator-usage-count": "已估計[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count 使用次數]：{{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|使用者|系統}}定義的屬性", "smw-property-indicator-last-count-update": "估計使用數\n上一次更新於：$1", "smw-concept-indicator-cache-update": "快取數\n上一次更新：$1", "smw-createproperty-isproperty": "它是一個類型為$1的屬性。", "smw-createproperty-allowedvals": "允許的{{PLURAL:$1|屬性值為|屬性值為}}：", "smw-paramdesc-category-delim": "分隔符", "smw-paramdesc-category-template": "格式化這些條目的模板", "smw-paramdesc-category-userparam": "傳遞模板的參數", "smw-info-par-message": "顯示的訊息。", "smw-info-par-icon": "顯示的圖示，\"訊息\"或\"警告\"。", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "一般選項", "prefs-extended-search-options": "延伸搜尋", "prefs-ask-options": "Semantic 搜尋", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] 與分配的擴充套件，提供用於所選特性與功能群組的各別偏好設定。以下[https://www.semantic-mediawiki.org/wiki/Help:User_preferences 說明頁面]有著各別設定與其描述與特徵的清單。", "smw-prefs-ask-options-tooltip-display": "在 #ask [[Special:Ask|查詢建置器]]特殊頁面顯示參數文字作為資訊提示。", "smw-prefs-ask-options-compact-view-basic": "啟用基本密集檢視", "smw-prefs-help-ask-options-compact-view-basic": "若有啟用，顯示在 Special:Ask 密集檢視的連結簡化集合。", "smw-prefs-general-options-time-correction": "使用本地[[Special:Preferences#mw-prefsection-rendering|時間偏移]]偏好設定來啟用替特殊頁面做時間調正。", "smw-prefs-general-options-jobqueue-watchlist": "在我的個人工具列顯示任務佇列", "smw-prefs-help-general-options-jobqueue-watchlist": "若有啟用，將待定所選任務[https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist 清單]與所估計佇列大小一同顯示。", "smw-prefs-general-options-disable-editpage-info": "在編輯頁面停用前言文字", "smw-prefs-general-options-disable-search-info": "在標準搜尋頁面上停用語法支援資訊", "smw-prefs-general-options-suggester-textinput": "啟用語意實體的輸入幫助", "smw-prefs-help-general-options-suggester-textinput": "若有啟用，允許使用[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 輸入幫助]來查找來自輸入上下文的屬性、概念、以及分類。", "smw-prefs-general-options-show-entity-issue-panel": "顯示實體問題面板", "smw-prefs-help-general-options-show-entity-issue-panel": "如有啟用，會在每個頁面上運行完整檢查並顯示[https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel 實體問題面板]。", "smw-prefs-factedsearch-profile": "選擇[[Special:FacetedSearch|多面搜尋]]的預設個人檔案：", "smw-ui-tooltip-title-property": "屬性", "smw-ui-tooltip-title-quantity": "單位轉換", "smw-ui-tooltip-title-info": "資訊", "smw-ui-tooltip-title-service": "服務連結", "smw-ui-tooltip-title-warning": "警告", "smw-ui-tooltip-title-error": "錯誤", "smw-ui-tooltip-title-parameter": "參數", "smw-ui-tooltip-title-event": "事件", "smw-ui-tooltip-title-note": "註釋", "smw-ui-tooltip-title-legend": "圖例", "smw-ui-tooltip-title-reference": "參考文獻", "smw_unknowntype": "此屬性類型「$1」無效", "smw-concept-cache-text": "此概念總共有 $1 {{PLURAL:$1|個頁面|個頁面}}，最後更新於 $2 $3。", "smw_concept_header": "概念 \"$1\" 的頁面", "smw_conceptarticlecount": "顯示於以下 $1 {{PLURAL:$1|個頁面|個頁面}}。", "smw-qp-empty-data": "出於一些選擇標準不足，請求資料無法顯示。", "right-smw-admin": "存取管理工作項目（Semantic MediaWiki）", "right-smw-patternedit": "編輯維護所允許正規表達式與模式方面的取用（Semantic MediaWiki）", "right-smw-pageedit": "編輯有關 <code>Is edit protected</code> 註釋頁面的取用（Semantic MediaWiki）", "right-smw-schemaedit": "編輯[https://www.semantic-mediawiki.org/wiki/Help:Schema 架構頁面]（Semantic MediaWiki）", "right-smw-viewjobqueuewatchlist": "存取任務佇列[https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist 監視清單]功能（Semantic MediaWiki）", "right-smw-viewentityassociatedrevisionmismatch": "存取相關修訂不符合的實體資訊（Semantic MediaWiki）", "right-smw-vieweditpageinfo": "檢視[https://www.semantic-mediawiki.org/wiki/Help:Edit_help 編輯說明]（Semantic MediaWiki）", "restriction-level-smw-pageedit": "已保護（僅限有資格的使用者）", "action-smw-patternedit": "編輯由語義MediaWiki使用的規則運算式", "action-smw-pageedit": "編輯帶有註釋 <code>Is edit protected</code> 的頁面（Semantic MediaWiki）", "group-smwadministrator": "管理員（Semantic MediaWiki）", "group-smwadministrator-member": "{{GENDER:$1|管理員（Semantic MediaWiki）}}", "grouppage-smwadministrator": "{{ns:project}}:管理員（Semantic MediaWiki）", "group-smwcurator": "策展人（Semantic MediaWiki）", "group-smwcurator-member": "{{GENDER:$1|策展人（Semantic MediaWiki）}}", "grouppage-smwcurator": "{{ns:project}}:策展人（Semantic MediaWiki）", "group-smweditor": "編輯者（Semantic MediaWiki）", "group-smweditor-member": "{{GENDER:$1|編輯者（Semantic MediaWiki）}}", "grouppage-smweditor": "{{ns:project}}:編輯者（Semantic MediaWiki）", "action-smw-admin": "存取 Semantic MediaWiki 的管理工作項目", "action-smw-ruleedit": "編輯規則頁面（Semantic MediaWiki）", "smw-property-namespace-disabled": "屬性[https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks 命名空間]已停用，無法嘗試對此屬性聲明類型或是其它屬性指定特徵。", "smw-property-predefined-default": "「$1」是$2類型的預先定義屬性。", "smw-property-predefined-common": "此屬性為預先部署（即為[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 特殊屬性]）且帶有額外管理權限，但可以像其它[https://www.semantic-mediawiki.org/wiki/Property 使用者定義屬性]般地使用。", "smw-property-predefined-ask": "「$1」是代表出有關各查詢的詮釋資訊（在[https://www.semantic-mediawiki.org/wiki/Subobject 子物件]的形式），且由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供的預先定義屬性。", "smw-property-predefined-asksi": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，收集使用在查詢的條件數量之預先定義屬性。", "smw-property-predefined-askde": "「$1」是用由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，告知有關查詢深度的預先定義屬性。", "smw-property-predefined-long-askde": "這是基於子查詢巢狀、屬性鏈，和可用的描述元素，根據 <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code> 設置參數所限制的執行查詢計算出來的數值。", "smw-property-predefined-askpa": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，描述影響查詢結果的參數之預先定義屬性。", "smw-property-predefined-long-askpa": "這是屬性集合的一部分，可指定[https://www.semantic-mediawiki.org/wiki/Help:Query_profiler 查詢偏好設定]。", "smw-sp-properties-docu": "此頁面列出此 wiki 上的可用[https://www.semantic-mediawiki.org/wiki/Property 屬性]以及其使用數。為了能有最新的數量統計，建議您定期運作[https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics 屬性統計]維護手稿程式。觀點上的差異，請查看[[Special:UnusedProperties|未使用]]或[[Special:WantedProperties|有所需要]]的屬性特殊頁面。", "smw-sp-properties-cache-info": "所列出資料已從[https://www.semantic-mediawiki.org/wiki/Caching 快取]檢索，並更新於 $1。", "smw-sp-properties-header-label": "屬性清單", "smw-admin-settings-docu": "顯示所有相關 Semantic MediaWiki 環境之預設及本地化設定的清單。有關個別設定詳情，請參閱[https://www.semantic-mediawiki.org/wiki/Help:Configuration 設置]說明頁面。", "smw-sp-admin-settings-button": "產生設定清單", "smw-admin-idlookup-title": "查找", "smw-admin-idlookup-docu": "此段落顯示出有關在 Semantic MediaWiki 上個別實體（wiki 頁面、子物件、屬性、其它） 的技術詳情。輸入內容可以是數字 ID 或是字串值來比對相關搜尋欄位，不過任何 ID 的引用是相關 Semantic MediaWiki；而不是 MediaWiki 的頁面或修訂 ID。", "smw-admin-iddispose-title": "擴散", "smw-admin-iddispose-docu": "請注意，處理操作不受限制，一旦確認，存儲引擎中的所有數據和掛起的請求將全部被删除。請'''慎重'''執行此任務，並只在查閱[https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal 檔案]後進行。", "smw-admin-iddispose-done": "ID“$1”已從存儲後端中移除。", "smw-admin-iddispose-references": "ID「$1」{{PLURAL:$2|沒有|至少有一個}}有效的參考文獻。", "smw-admin-iddispose-references-multiple": "至少具有一個有效參考文獻紀錄的符合清單。", "smw-admin-iddispose-no-references": "搜尋無法找出符合「$1」的表格項目。", "smw-admin-idlookup-input": "搜尋：", "smw-admin-objectid": "ID：", "smw-admin-tab-general": "預覽", "smw-admin-tab-notices": "棄用通知", "smw-admin-tab-maintenance": "維護", "smw-admin-tab-supplement": "補充功能", "smw-admin-tab-registry": "註冊", "smw-admin-tab-alerts": "警告", "smw-admin-alerts-tab-deprecationnotices": "棄用通知", "smw-admin-alerts-tab-maintenancealerts": "維護警告", "smw-admin-alerts-section-intro": "此段落顯示出與設定、操作、其它行動關聯的警告與通知，這些已被歸類為需要具有適當權限的管理員或使用者關注。", "smw-admin-maintenancealerts-section-intro": "儘管非必須，以下警告與通知應被解決，因為這預期能幫助增進系統與操作的維護性。", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "表格最佳化", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "系統找出上一次[https://www.semantic-mediawiki.org/wiki/Table_optimization 表格最佳化]是在 $2 天前執行（來自$1的紀錄），這已超出 $3 天維護的臨界值。如文件所述，執行最佳化能使得查詢規劃對於查詢作出更好的決策，因此建議定期執行表格最佳化。", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "過時實體", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "系統計算出有 $1 個[https://www.semantic-mediawiki.org/wiki/Outdated_entities 過時實體]，且超出$2臨界值達到未被注意維護的危急等級。建議執行 [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] 維護手稿。", "smw-admin-maintenancealerts-invalidentities-alert-title": "無效實體", "smw-admin-maintenancealerts-invalidentities-alert": "系統相配了 $1 個[https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|實體}}]到一個[https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace 未維護命名空間]，並建議執行 [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] 或是 [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>] 維護手稿。", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "設定", "smw-admin-configutation-tab-namespaces": "命名空間", "smw-admin-configutation-tab-schematypes": "綱要類型", "smw-admin-maintenance-tab-tasks": "任務", "smw-admin-maintenance-tab-scripts": "維護用手稿", "smw-admin-maintenance-no-description": "沒有描述。", "smw-admin-maintenance-script-section-title": "可用維護手稿清單", "smw-admin-maintenance-script-section-intro": "以下維護手稿需要為管理員，並且得使用命令列才能執行所列出的手稿。", "smw-admin-maintenance-script-description-dumprdf": "現有的三重 RDF 匯出。", "smw-admin-maintenance-script-description-rebuildconceptcache": "此手稿用於管理 Semantic MediaWiki 的概念快取，這可創建、移除、更新所選快取。", "smw-admin-maintenance-script-description-rebuilddata": "以循環透過所有可能含有語意資料的頁面來重新創建在資料庫的語意資料。", "smw-admin-maintenance-script-description-rebuildelasticindex": "以循環透過所有含有語意資料的實體來重新建置 Elasticsearch 索引（僅用於使用 <code>ElasticStore</code> 的安裝）。", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "在 Elasticsearch 尋找缺少的實體（僅適用於使用 <code>ElasticStore</code> 安裝），以及安排合適的更新作業。", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "重新建置 <code>SQLStore</code> 全文搜尋索引（用於設定已啟用的安裝）。", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "重新建置所有屬性實體的使用統計內容。", "smw-admin-maintenance-script-description-removeduplicateentities": "移除在所選且沒有有效參考文獻的表格上找出的重複實體。", "smw-admin-maintenance-script-description-setupstore": "依照定義設定在 <code>LocalSettings.php</code> 的儲存與查詢後端。", "smw-admin-maintenance-script-description-updateentitycollation": "更新在 <code>SQLStore</code> 的 <code>smw_sort</code> 欄位（依據 [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation] 設定）。", "smw-admin-maintenance-script-description-populatehashfield": "為缺少值的列填充 <code>smw_hash</code> 欄位。", "smw-admin-maintenance-script-description-purgeentitycache": "清除已知項目與它們所關聯資料的快取項目。", "smw-admin-maintenance-script-description-updatequerydependencies": "更新查詢和查詢相依關係（請查看 [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore] 設定）。", "smw-admin-maintenance-script-description-disposeoutdatedentities": "處置過時實體與查詢連結。", "smw-admin-maintenance-script-description-runimport": "從[https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs]填入與匯入自動檢測的內容。", "smw-admin-maintenance-script-section-update": "更新手稿", "smw-admin-maintenance-script-section-rebuild": "重建手稿", "smw-livepreview-loading": "讀取中...", "smw-sp-searchbyproperty-description": "此頁面提供用於找尋由屬性實體與命名值所描述實體的簡易[https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces 瀏覽介面]。其它可用的搜尋介面，包含有[[Special:PageProperty|頁面屬性搜尋]]以及[[Special:Ask|詢問查詢建置器]]。", "smw-sp-searchbyproperty-resultlist-header": "結果清單", "smw-sp-searchbyproperty-nonvaluequery": "已分配屬性「$1」的值清單。", "smw-sp-searchbyproperty-valuequery": "已註解屬性「$1」帶有值「$2」的頁面清單", "smw-datavalue-number-textnotallowed": "「$1」不能分配給值為 $2 的聲明數字類型。", "smw-datavalue-number-nullnotallowed": "「$1」回傳了「NULL」，這不允許作為數字。", "smw-editpage-annotation-enabled": "此頁面支援語義文內註釋 (例如 <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) 來建立由 Semantic MediaWiki 提供的結構化、可查詢的內容。 如何使用標示法或 #ask 分析器功能的詳細說明，請查看 [https://www.semantic-mediawiki.org/wiki/Help:Getting_started 入門指引]、[https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation 文內註釋]或[https://www.semantic-mediawiki.org/wiki/Help:Inline_queries 行內查詢]說明頁面。", "smw-editpage-annotation-disabled": "由於命名空間限制，此頁面在語義文內註釋中不會開啟。有關如何開啟命名空間的詳細資料可在[https://www.semantic-mediawiki.org/wiki/Help:Configuration 設定]說明頁面找到。", "smw-editpage-property-annotation-enabled": "此屬性可以擴充使用語意註解來指定資料類型（例如：<nowiki>\"[[Has type::Page]]\"</nowiki>）或支援的宣告（例如：<nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>）。有關如何增加此頁面的描述，請查看[https://www.semantic-mediawiki.org/wiki/Help:Property_declaration 屬性宣告]或是[https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes 可用資料類型清單]說明頁面。", "smw-editpage-property-annotation-disabled": "此屬性已被預先定義，因此不能以資料類型註解（例如：<nowiki>\"[[Has type::Page]]\"</nowiki>）來擴充（請查看[https://www.semantic-mediawiki.org/wiki/Help:Special_properties 特殊屬性]說明頁面來獲得更多資訊）。", "smw-editpage-concept-annotation-enabled": "此概念可使用 #concept 解析功能來擴充。有關如何使用 #concept 的敘述內容，請參見 [https://www.semantic-mediawiki.org/wiki/Help:Concepts 概念]說明頁面。", "smw-search-syntax-support": "搜尋輸入支援語意[https://www.semantic-mediawiki.org/wiki/Help:Semantic_search 查詢語法]的使用，來協助使用 Semantic MediaWiki 比對結果。", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 輸入幫助]也已啟用，來減少可用屬性和分類的預先選擇。", "smw-search-help-intro": "<code><nowiki>[[ ... ]]</nowiki></code> 輸入會指示至輸入處理器來使用 Semantic MediaWiki 後端搜尋。另外需注意不支援 <code><nowiki>[[ ... ]]</nowiki></code> 結合像是 <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> 這樣的非結構化文字搜尋。", "smw-search-help-structured": "結構化搜尋：\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>、<code><nowiki>[[Has number::123]]</nowiki></code>（作為 [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context 已篩選語境]）\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code>（帶有[https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context 查詢語境]）", "smw-search-help-proximity": "接近搜尋（未知的屬性，'''只'''對於提供全文搜尋整合的後端可用）：\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code>（在所有文件裡搜尋已索引的 \"lorem\" 和 \"ipsum\"）\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code>（比對 \"lorem ipsum\" 為詞組）", "smw-search-help-ask": "以下連結會解釋如何使用 <code>#ask</code> 語法。\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages 選擇頁面]描述來選擇頁面如何並建置條件\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators 搜尋運算]列出包括用於範圍和萬用查詢的可用搜尋運算", "smw-search-input": "輸入與搜尋", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 輸入幫助]用於輸入欄位時提供，這需要使用以下字首之一：\n\n*<code>p:</code> 用來啟用屬性建議（例如：<code><nowiki>[[p:Has ...</nowiki></code>）\n\n*<code>c:</code> 用來啟用分類建議\n\n*<code>con:</code> 用來啟用概念建議", "smw-search-syntax": "語法", "smw-search-profile": "擴充", "smw-search-profile-tooltip": "搜尋與 Semantic MediaWiki 關聯的功能", "smw-search-profile-sort-best": "最佳符合", "smw-search-profile-sort-recent": "最新", "smw-search-profile-sort-title": "標題", "smw-search-profile-extended-help-intro": "Special:Search [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile 擴充偏好設定]提供取用在特定於 Semantic MediaWiki 的搜尋功能，並且支援後端查詢。", "smw-search-profile-extended-help-sort": "指定用於結果顯示的排序偏好：", "smw-search-profile-extended-help-sort-title": "* \"Title\" 使用頁面標題（或顯示標題）來作為排序標準", "smw-search-profile-extended-help-sort-recent": "* \"Most recent\" 會優先顯示出最多近期變動的實體（子物件實體會抑制成這些實體，並不會以[[Property:Modification date|修改日期]]來註解）", "smw-search-profile-extended-help-sort-best": "* \"Best match\" 會基於由後端所提供的[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy 關聯度]分數來排序實體", "smw-search-profile-extended-help-form": "所提供會根據所見的不同屬性和值欄位來比對特定使用情況的表單（如有保持著），會減少輸入過程讓要持續搜尋請求的使用者能更容易利用。（請查看$1）", "smw-search-profile-extended-help-namespace": "當表單選擇時，命名空間選擇框會被隱藏起來，但可借助「顯示/隱藏」按鈕來將其可見。", "smw-search-profile-extended-help-search-syntax": "搜尋輸入欄位支援用來定義 Semantic MediaWiki 指定搜尋語境的 <code>#ask</code> 語法使用。有效的表達式包含：", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> 用於查詢包含「...」的任何內容，這在所涉及的搜尋上下文或屬性未知時很有用（例如：<code>in:(lorem && ipsum)</code> 等同 <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>）。", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> 為在完全相同排序裡查找任何含有「...」的項目", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> 為符合任何具有屬性 \"...\" 的項目（例如：<code>has:(Foo && Bar)</code> 等同於 <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>）", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> 為沒有符合任何包含「...」的項目", "smw-search-profile-extended-help-search-syntax-prefix": "* 額外可用且已定義的自定義字首，例如像是：$1", "smw-search-profile-extended-help-search-syntax-reserved": "* 一些表達式為保留的，例如像是：<nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''一些所列出的操作，僅適用於啟用的全文索引或 ElasticStore。''", "smw-search-profile-extended-help-query": "已使用 <code><nowiki>$1</nowiki></code> 作為查詢。", "smw-search-profile-extended-help-query-link": "有關更多詳情，請使用$1。", "smw-search-profile-extended-help-find-forms": "可用表單", "smw-search-profile-extended-section-sort": "排序依", "smw-search-profile-extended-section-form": "表單", "smw-search-profile-extended-section-search-syntax": "搜尋輸入", "smw-search-profile-extended-section-namespace": "命名空間", "smw-search-profile-extended-section-query": "查詢", "smw-search-profile-link-caption-query": "查詢建置器", "smw-search-show": "顯示", "smw-search-hide": "隱藏", "log-name-smw": "Semantic MediaWiki 日誌", "log-show-hide-smw": "$1 Semantic MediaWiki 日誌", "logeventslist-smw-log": "Semantic MediaWiki 日誌", "log-description-smw": "有關[https://www.semantic-mediawiki.org/wiki/Help:Logging 已啟用事件類型]的行動，該已由 Semantic MediaWiki 及其元件所回報。", "logentry-smw-maintenance": "由 Semantic MediaWiki 發佈出的維護相關事件", "smw-datavalue-import-unknown-namespace": "匯入的命名空間 \"$1\" 不明，請確認 OWL 匯入詳細資訊，可至 [[MediaWiki:Smw import $1]] 取得。", "smw-datavalue-import-missing-namespace-uri": "無法在[[MediaWiki:Smw import $1|$1 匯入]]資料中找到 \"$1\" 命名空間的 URI。", "smw-datavalue-import-missing-type": "在 [[MediaWiki:Smw import $2|$2]] 個匯入裡，找不到用於「$1」的類型定義。", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1 匯入]]", "smw-datavalue-import-invalid-value": "「$1」不是個有效格式，且預期要含有\"命名空間\":\"識別碼\"（例如：\"foaf:name\"）。", "smw-datavalue-import-invalid-format": "字串「$1」預期應分割成四個部分，但格式未能理解。", "smw-property-predefined-impo": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來描述與[https://semantic-mediawiki.org/wiki/Help:Import_vocabulary 已匯入詞彙]間的關係的預先定義屬性。", "smw-property-predefined-type": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來描述屬性[[Special:Types|資料類型]]的預先定義屬性。", "smw-property-predefined-sobj": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來表示出[https://www.semantic-mediawiki.org/wiki/Help:Container 容器]結構的預先定義屬性。", "smw-property-predefined-long-sobj": "容器允許與普通 wiki 頁面相似，但當連結至嵌入主題時，是以不同實體空間的累積屬性-值分配。", "smw-property-predefined-errp": "「$1」是用由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，用來追蹤異常值通知方面輸入錯誤的預先定義屬性。", "smw-property-predefined-long-errp": "在多數情況下，這是由於類型不符合或是[[Property:Allows value|值]]方面的限制所造成。", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] 是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，可定義出允許值清單，來對屬性做值分配限制的預先定義屬性。", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list \"$1\"] 是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，可指定參考文獻至允許值清單，來對屬性做值分配限制的預先定義屬性。", "smw-datavalue-property-restricted-annotation-use": "屬性「$1」有限制應用區域，且不能由使用者來註解屬性。", "smw-datavalue-property-restricted-declarative-use": "屬性「$1」是宣告式屬性，且僅能在屬性和分類頁面上使用。", "smw-datavalue-property-create-restriction": "屬性「$1」不存在，且使用者缺少建立或是以未核准屬性來註解值的「$2」權限（查看[https://www.semantic-mediawiki.org/wiki/Help:Authority_mode 權威模式]）。", "smw-datavalue-property-invalid-character": "「$1」包含作為屬性標籤一部份的列舉字元「$2」，因此被歸類為無效。", "smw-datavalue-property-invalid-chain": "不允許在註解過程中使用「$1」作為屬性鏈。", "smw-datavalue-restricted-use": "資料值「$1」被標記為限制使用。", "smw-datavalue-invalid-number": "\"$1\" 無法作為數字解讀。", "smw-query-condition-circular": "於 \"$1\" 偵測到可能會發生循環的情況。", "smw-query-condition-empty": "查詢描述含有空的條件。", "smw-types-list": "資料型態清單", "smw-types-default": "\"$1\" 是內建的資料型態。", "smw-types-help": "更多的資訊與範例可在此[https://www.semantic-mediawiki.org/wiki/Help:Type_$1 說明頁面]找到。", "smw-type-anu": "\"$1\" 是 [[Special:Types/URL|URL]] 資料型態的變體，大多用在 ''owl:AnnotationProperty'' 匯出宣告。", "smw-type-boo": "「$1」是基本資料型態，用來描述真/假值。", "smw-type-cod": "\"$1\" 是[[Special:Types/Text|文字]]資料型態的變體，使用在不定長度的技術文字，如原始碼清單。", "smw-type-geo": "「$1」是資料型態，用來描述地理位置，需要搭配 [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] 擴充來提供延伸功能。", "smw-type-tel": "\"$1\" 是特殊的資料型態，用來描述依 RFC 3966 規範的國際電話號碼。", "smw-type-txt": "「$1」是基本資料型態，用來描述任意長度的字串。", "smw-type-dat": "「$1」是基本資料型態，使用統一格式來代表時間點。", "smw-type-ema": "「$1」是表現出電子郵件的特殊資料類型。", "smw-type-tem": "「$1」是代表溫度的特殊數字資料類型。", "smw-type-qty": "「$1」是以數字表示以及計量單位來描述數量的資料類型。", "smw-type-rec": "「$1」是指定出固定排序類型化的屬性清單之容器資料類型。", "smw-type-extra-tem": "轉換架構含有像是克耳文、攝氏、華氏，以及蘭金溫標這類的支援單位。", "smw-type-tab-properties": "屬性", "smw-type-tab-types": "類型", "smw-type-tab-type-ids": "類型 ID", "smw-type-tab-errors": "錯誤", "smw-type-primitive": "基本", "smw-type-contextual": "上下文", "smw-type-compound": "合成詞", "smw-type-container": "容器", "smw-type-no-group": "未分類", "smw-special-pageproperty-description": "此頁面提供瀏覽用於找尋屬性全部值與指定頁面的介面。其它可用搜尋介面包括[[Special:SearchByProperty|屬性搜尋]]與[[Special:Ask|請求查詢建置器]]。", "smw-property-predefined-errc": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，表示出現在連接到不恰當值註解或輸入處理時的錯誤之預先定義屬性。", "smw-property-predefined-long-errc": "錯誤會收集於[https://www.semantic-mediawiki.org/wiki/Help:Container 容器]裡，可能會包含導致內容不符的屬性參考文獻。", "smw-property-predefined-errt": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，含有錯誤方面文字描述的預先定義屬性。", "smw-subobject-parser-invalid-naming-scheme": "使用者定義的子物件包含無效的命名架構。使用在前五個字元的點號（$1）已被保留用於擴充方面。您可以來設定[https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier 命名的識別碼]。", "smw-datavalue-record-invalid-property-declaration": "紀錄定義包含著本身已被聲明為紀錄類型屬性「$1」，因此不被許可。", "smw-property-predefined-mdat": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，對應主題最後一次修改的日期之預先定義屬性。", "smw-property-predefined-cdat": "「$1」是用由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，對應主題首次修訂的日期之預先定義屬性。", "smw-property-predefined-newp": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，表示出是否為新主題的預先定義屬性。", "smw-property-predefined-ledt": "「$1」是用由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，含有使用者所建立出最新一次修訂的頁面名稱之預先定義屬性。", "smw-property-predefined-mime": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來描述已上傳檔案的 MIME 類型之預先定義屬性。", "smw-property-predefined-media": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來描述已上傳檔案的多媒體類型之預先定義屬性。", "smw-property-predefined-askfo": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，持有使用在查詢裡結果格式名稱的預先定義屬性。", "smw-property-predefined-askst": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，以字串來描述查詢條件的預先定義屬性。", "smw-property-predefined-askdu": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，含有查詢執行完畢所需時間值（以秒為單位）的預先定義屬性。", "smw-property-predefined-asksc": "「$1」是用由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，確認替代（例如：遠端、聯合）查詢來源的預先定義屬性。", "smw-property-predefined-askco": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，描述查詢狀態或構成要素的預先定義屬性。", "smw-property-predefined-long-askco": "數字或分配數字代表內部編碼狀態，在[https://www.semantic-mediawiki.org/wiki/Help:Query_profiler 說明頁面]裡含有進一步解釋。", "smw-property-predefined-prec": "「$1」是用於數字資料類型裡[https://www.semantic-mediawiki.org/wiki/Help:Display_precision 顯示精確度]（小數位數）的預先定義屬性。", "smw-property-predefined-attch-link": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，收集在頁面裡所找出的內嵌檔案和圖片連結之預先定義屬性。", "smw-property-predefined-inst": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，用來儲存獨立於 MediaWiki 之分類資訊的內部預先定義屬性。", "smw-property-predefined-unit": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，定義數字類型屬性所顯示單位的宣告式預先定義屬性。", "smw-property-predefined-long-unit": "以逗號區分的清單允許描述用來顯示的單位或格式。", "smw-property-predefined-conv": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，定義某個物理量單位之轉換因數的宣告式預先定義屬性。", "smw-property-predefined-serv": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，添加服務連結到屬性裡的宣告式預先定義屬性。", "smw-property-predefined-redi": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，用來記錄重新導向的內部預先定義屬性。", "smw-property-predefined-subp": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，指定一個屬性為另一個[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of 子屬性]的宣告式預先定義屬性。", "smw-property-predefined-subc": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，定義一個分類為另個[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of 子分類]的預先定義屬性。", "smw-property-predefined-conc": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，定義相關概念的內部預先定義屬性。", "smw-property-predefined-err-type": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，識別出一個[https://www.semantic-mediawiki.org/wiki/Help:Processing_errors 處理錯誤]群組或類別的預先定義屬性。", "smw-property-predefined-skey": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，持有排序參照的內部預先定義屬性。", "smw-property-predefined-pplb": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，指定一個[https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label 首選屬性標籤]的預先定義屬性。", "smw-property-predefined-chgpro": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，持有[https://www.semantic-mediawiki.org/wiki/Help:Change_propagation 更改傳播]資訊的預先定義屬性。", "smw-property-predefined-schema-link": "並且是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供。", "smw-property-predefined-format-schema": "並且是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供。", "smw-property-predefined-profile-schema": "並且是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供。", "smw-property-predefined-trans": "並且是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供。", "smw-property-predefined-trans-source": "並且是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供。", "smw-property-predefined-trans-group": "並且是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供。", "smw-property-predefined-cont-len": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，儲存長度資訊的預先定義屬性。", "smw-property-predefined-long-cont-len": "這與 [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] 連結使用（以及[https://www.semantic-mediawiki.org/Attachment_processor 附件處理器]）來收集並儲存從接納檔案索取的長度資訊（若有提供的話）。", "smw-property-predefined-cont-lang": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，儲存語言資訊的預先定義屬性。", "smw-property-predefined-long-cont-lang": "這與 [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] 連結使用（以及[https://www.semantic-mediawiki.org/Attachment_processor 附件處理器]）來收集並儲存從接納檔案索取的語言資訊（若有提供的話）。", "smw-property-predefined-cont-title": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，儲存標題資訊的預先定義屬性。", "smw-property-predefined-long-cont-title": "這與 [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] 連結使用（以及[https://www.semantic-mediawiki.org/Attachment_processor 附件處理器]）來收集並儲存從接納檔案索取的標題資訊（若有提供的話）。", "smw-property-predefined-cont-author": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，儲存作者資訊的預先定義屬性。", "smw-property-predefined-long-cont-author": "這與 [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] 連結使用（以及[https://www.semantic-mediawiki.org/Attachment_processor 附件處理器]）來收集並儲存從接納檔案索取的作者資訊（若有提供的話）。", "smw-property-predefined-cont-date": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，儲存日期資訊的預先定義屬性。", "smw-property-predefined-long-cont-date": "這與 [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] 連結使用（以及[https://www.semantic-mediawiki.org/Attachment_processor 附件處理器]）來收集並儲存從接納檔案索取的日期資訊（若有提供的話）。", "smw-property-predefined-cont-type": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，儲存檔案類型資訊的預先定義屬性。", "smw-property-predefined-long-cont-type": "這與 [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] 連結使用（以及[https://www.semantic-mediawiki.org/Attachment_processor 附件處理器]）來收集並儲存從接納檔案索取的類型資訊（若有提供的話）。", "smw-property-predefined-cont-keyw": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，代表關鍵字的預先定義屬性。", "smw-property-predefined-long-cont-keyw": "這與 [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] 連結使用（以及[https://www.semantic-mediawiki.org/Attachment_processor 附件處理器]）來收集並儲存從接納檔案索取的關鍵字（若有提供的話）。", "smw-property-predefined-file-attch": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，表示出一個有儲存附件資訊之容器的預先定義屬性。", "smw-property-predefined-long-file-attch": "這與 [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] 連結使用（以及[https://www.semantic-mediawiki.org/Attachment_processor 附件處理器]）來收集從接納檔案索取的所有特定資訊內容（若有提供的話）。", "smw-types-extra-geo-not-available": "未偵測到[https://www.semantic-mediawiki.org/wiki/Extension:Maps 擴充「Maps」] ，因此「$1」的運作能力被受限。", "smw-datavalue-monolingual-dataitem-missing": "缺少用來建置單語合成詞值的預期項目。", "smw-datavalue-languagecode-missing": "對於「$1」註解，解析器無法判斷語言代碼（例如像：\"foo@en\"）。", "smw-datavalue-languagecode-invalid": "「$1」不被認為是支援的語言代碼。", "smw-property-predefined-lcode": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，表示出 BCP47 格式語言代碼的預先定義屬性。", "smw-type-mlt-rec": "「$1」是以特定[[Property:Language code|語言代碼]]來關聯文字值的[https://www.semantic-mediawiki.org/wiki/Help:Container 容器]資料類型。", "smw-types-extra-mlt-lcode": "資料類型{{PLURAL:$2|需要|不需}}語言代碼（註：{{PLURAL:$2|不接受沒有語言代碼的值註解|可接受沒有語言代碼的值註解}}）。", "smw-property-predefined-text": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，表示出任意長度文字的預先定義屬性。", "smw-property-predefined-pdesc": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，允許描述在語境裡屬性的預先定義屬性。", "smw-property-predefined-list": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來定義與[[Special:Types/Record|紀錄]]類型屬性一同使用的屬性清單之預先定義屬性。", "smw-limitreport-intext-parsertime": "[SMW]文內註解解析器時間", "smw-limitreport-intext-postproctime": "[SMW]發佈處理時間", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|秒|秒}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|秒|秒}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW]存儲更新時間（在頁面清除時）", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|秒|秒}}", "smw_allows_pattern": "此頁面預期包含參考文獻清單（依據[https://zh.wikipedia.org/wiki/%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F 正規表示式]），透過[[Property:Allows pattern|允許模式]]參數可用。要編輯此頁面，需要 <code>smw-patternedit</code> 權限。", "smw-datavalue-allows-pattern-mismatch": "「$1」被正規表達式「$2」歸類為無效。", "smw-datavalue-allows-pattern-reference-unknown": "「$1」模式參考文獻不符合在 [[MediaWiki:Smw allows pattern]] 裡的項目。", "smw-datavalue-allows-value-list-unknown": "「$1」清單參考文獻不符合[[MediaWiki:Smw allows list $1]]頁面。", "smw-datavalue-allows-value-list-missing-marker": "「$1」清單內容缺少帶有 * 清單標記的項目。", "smw-datavalue-feature-not-supported": "「$1」功能在此 wiki 不支援或是被停用。", "smw-property-predefined-pvap": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，可指定[[MediaWiki:Smw allows pattern|模式參照]]來套用到[https://zh.wikipedia.org/wiki/%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F 正規表達式]比對的預先定義屬性。", "smw-property-predefined-dtitle": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，可為實體分配不同顯示標題的預先定義屬性。", "smw-property-predefined-pvuc": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，替各實例限制值分配以達到內容不重複（或最多一個）的預先定義屬性。", "smw-property-predefined-long-pvuc": "唯一性會在兩個值在字面涵義上不相等時建立，且違反任何限制會被歸類為錯誤。", "smw-datavalue-constraint-uniqueness-violation": "屬性「$1」僅允許唯一值分配，而「$2」已註解在主題「$3」裡。", "smw-datavalue-constraint-uniqueness-violation-isknown": "屬性「$1」僅允許唯一值註解，「$2」已包含所分配值。「$3」違反了唯一性限制。", "smw-datavalue-constraint-violation-non-negative-integer": "屬性「$1」有「非負整數」限制，且內容值「$2」違反需求。", "smw-datavalue-constraint-violation-must-exists": "屬性「$1」有 <code>must_exists</code> 限制，且值「$2」不合乎需求。", "smw-datavalue-constraint-violation-single-value": "屬性「[[Property:$1|$1]]」有 <code>single_value</code> 限制，且內容值「$2」違反需求。", "smw-constraint-violation-uniqueness": "<code>unique_value_constraint</code> 限制被分配到僅允許唯一值分配的「[[Property:$1|$1]]」屬性，另外在「$3」主題裡找出已註解了值註解''$2''。", "smw-constraint-violation-uniqueness-isknown": "<code>unique_value_constraint</code> 限制被分配到「[[Property:$1|$1]]」屬性，因此僅允許唯一值註解，「$2」已包含帶有「$3」的值註釋，違反了目前主題的唯一性限制。", "smw-constraint-violation-non-negative-integer": "<code>non_negative_integer</code> 限制被分配到「[[Property:$1|$1]]」屬性，且「$2」值註解違反了限制需求。", "smw-constraint-violation-must-exists": "<code>must_exists</code> 限制被分配到「[[Property:$1|$1]]」屬性，且「$2」值註解違反了限制需求。", "smw-constraint-violation-single-value": "<code>single_value</code> 限制被分配到「[[Property:$1|$1]]」屬性，且「$2」值註解違反了限制需求。", "smw-constraint-violation-class-shape-constraint-missing-property": "<code>shape_constraint</code> 被分配到帶有 <code>property</code> 關鍵字的「[[:$1]]」分類，缺少所需要的「$2」屬性。", "smw-constraint-violation-class-shape-constraint-wrong-type": "<code>shape_constraint</code> 被分配到帶有 <code>property_type</code> 關鍵字的「[[:$1]]」分類，「$2」屬性不符合「$3」的類型。", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "<code>shape_constraint</code> 被分配到帶有 <code>max_cardinality</code> 關鍵字的「[[:$1]]」分類，「$2」屬性不符合「$3」的基數。", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "<code>shape_constraint</code> 被分配到帶有 <code>min_textlength</code> 關鍵字的「[[:$1]]」分類，「$2」屬性不符合「$3」的長度需求。", "smw-constraint-violation-class-mandatory-properties-constraint": "<code>mandatory_properties</code> 限制被分配到分類「[[:$1]]」，並需要以下強制屬性：$2", "smw-constraint-violation-allowed-namespace-no-match": "<code>allowed_namespaces</code> 限制分配到「[[Property:$1|$1]]」屬性，而「$2」違反了命名空間所需，僅允許以下「$3」命名空間。", "smw-constraint-violation-allowed-namespaces-requires-page-type": "<code>allowed_namespaces</code> 限制需要頁面類型。", "smw-constraint-schema-category-invalid-type": "註解「$1」架構對於分類無效，需要是「$2」類型。", "smw-constraint-schema-property-invalid-type": "註解「$1」架構對於分類無效，需要是「$2」類型。", "smw-constraint-error-allows-value-list": "「$1」不在用於「$3」屬性[[Property:Allows value|所允許值]]的清單（$2）中。", "smw-constraint-error-allows-value-range": "「$1」沒有符合用於限制屬性「$3」的[[Property:Allows value|允許值]]所指定的「$2」範圍。", "smw-property-predefined-boo": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來代表布林值的[[Special:Types/Boolean|類型]]及預先定義屬性。", "smw-property-predefined-num": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來代表數字值的[[Special:Types/Number|類型]]及預先定義屬性。", "smw-property-predefined-dat": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來代表日期值的[[Special:Types/Date|類型]]及預先定義屬性。", "smw-property-predefined-uri": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來代表 URI/URL 值的[[Special:Types/URL|類型]]及預先定義屬性。", "smw-property-predefined-qty": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來代表數量的[[Special:Types/Quantity|類型]]及預先定義屬性。", "smw-datavalue-time-invalid-offset-zone-usage": "「$1」含有不支援的偏差和區域識別碼。", "smw-datavalue-time-invalid-values": "值「$1」包含在「$2」形式下無法解釋的資訊。", "smw-datavalue-time-invalid-date-components-common": "「$1」包含一些無法解釋的資訊。", "smw-datavalue-time-invalid-date-components-dash": "「$1」含有外部連接號或是其它無效的闡明日期字元。", "smw-datavalue-time-invalid-date-components-empty": "「$1」包含一些空的成份。", "smw-datavalue-time-invalid-date-components-three": "「$1」包含超過闡明日期所需的三個要素。", "smw-datavalue-time-invalid-date-components-sequence": "「$1」含有無法解讀，違反用於日期組件之可用符合模型的序列。", "smw-datavalue-time-invalid-ampm": "「$1」包含作為小時元素的「$2」，這在 12 小時制裡是無效的。", "smw-datavalue-time-invalid-jd": "無法將輸入值「$1」解讀為有效的儒略日，以「$2」內容回報。", "smw-datavalue-time-invalid-prehistoric": "無法解讀過時的輸入值「$1」。例如在舊式上下文裡指定了超過一年或是日曆模組，可能會回傳非預期的結果。", "smw-datavalue-time-invalid": "無法將輸入值「$1」解讀為有效的日期或時間成份，以「$2」內容回報。", "smw-datavalue-external-formatter-uri-missing-placeholder": "格式化 URI 缺少「$1」佔位符。", "smw-datavalue-external-formatter-invalid-uri": "「$1」是無效的 URL。", "smw-datavalue-external-identifier-formatter-missing": "屬性缺少[[Property:External formatter uri|\"外部格式化 URI\"]] 分配。", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "外部識別碼\"$1\"需要多個欄位代換，但目前在最少需一個值的要求上缺少了值\"$2\"。", "smw-datavalue-keyword-maximum-length": "關鍵詞超出最大長度 $1 個{{PLURAL:$1|字元|字元}}。", "smw-property-predefined-eid": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來代表外部識別碼的[[Special:Types/External identifier|類型]]及預先定義屬性。", "smw-property-predefined-peid": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，指定外部識別碼的預先定義屬性。", "smw-property-predefined-pefu": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，以佔位符來指定外部資源的預先定義屬性。", "smw-property-predefined-long-pefu": "URI 預期包含將由[[Special:Types/External identifier|外部識別碼]]值所調整的佔位符，來形成有效的資源參照。", "smw-type-eid": "\"$1\" 是描述外部資源（基於 URI）[[Special:Types/Text|文字]]的資料型態變體，且需要分配屬性來聲明[[Property:External formatter uri|外部格式化 URI]]。", "smw-property-predefined-keyw": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，可標準化文字且含有限制字元長度的預先定義屬性與[[Special:Types/Keyword|類型]]。", "smw-type-keyw": "\"$1\" 是[[Special:Types/Text|文字]]資料型態的變體，為具標準化內容呈現的有限字元長度。", "smw-datavalue-stripmarker-parse-error": "提供的值「$1」含有 [https://en.wikipedia.org/wiki/Help:Strip_markers strip markers] 內容，因此不能被充分解析。", "smw-datavalue-parse-error": "提供的值「$1」無法理解。", "smw-datavalue-propertylist-invalid-property-key": "屬性清單「$1」包含無效的屬性鍵「$2」。", "smw-datavalue-type-invalid-typeuri": "類型「$1」不可轉換成有效的 URI 表述。", "smw-datavalue-wikipage-missing-fragment-context": "Wiki 頁面輸入值「$1」不可在不帶脈絡頁面情況下使用。", "smw-datavalue-wikipage-invalid-title": "頁面類型輸入值「$1」包含無效字元或是內容不完整，導致在查詢或註釋過程裡發生意外結果。", "smw-datavalue-wikipage-property-invalid-title": "屬性「$1」（作為頁面類型）所帶的輸入值「$2」包含無效字元或是內容不完整，導致在查詢或註釋過程裡發生意外結果。", "smw-datavalue-wikipage-empty": "Wiki 頁面輸入值為空（例如像：<code>[[SomeProperty::]], [[]]</code>），因此不能用作為查詢條件的名稱或一部份。", "smw-type-ref-rec": "「$1」是個允許記錄有關值分配之額外資訊（例如：出處資料）的[https://www.semantic-mediawiki.org/wiki/Container 容器]類型。", "smw-datavalue-reference-outputformat": "$1：$2", "smw-datavalue-reference-invalid-fields-definition": "[[Special:Types/Reference|參考文獻]]類型應為聲明使用[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields 含有欄位]屬性的屬性清單。", "smw-parser-invalid-json-format": "JSON 解析器回傳了「$1」。", "smw-property-preferred-title-format": "$1（$2）", "smw-property-preferred-label-language-combination-exists": "「$1」不能用於首選標籤，因為語言「$2」已分配給標籤「$3」。", "smw-clipboard-copy-link": "複製連結到剪貼簿", "smw-property-userdefined-fixedtable": "「$1」被設置為[https://www.semantic-mediawiki.org/wiki/Fixed_properties 固定屬性]，並且任何對其[https://www.semantic-mediawiki.org/wiki/Type_declaration 類型宣告]的變更需要運作 <code>setupStore.php</code> 或是完成特殊[[Special:SemanticMediaWiki|「資料庫與安裝與更新」]]任務。", "smw-data-lookup": "正在抓取資料…", "smw-data-lookup-with-wait": "正在處理請求，可能需花費一些時間。", "smw-no-data-available": "沒有可用資料。", "smw-property-req-violation-missing-fields": "屬性「$1」缺少用於此「$2」類型所需的 [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] 宣告。", "smw-property-req-violation-multiple-fields": "屬性「$1」含有多個 [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] 宣告（因此衝突），但「$2」類型僅能用一個。", "smw-property-req-violation-missing-formatter-uri": "因定義 <code>External formatter URI</code> 屬性失敗，屬性「$1」缺少用於註釋類型的定義詳情。", "smw-property-req-violation-predefined-type": "作為預先定義屬性的「$1」屬性包含「$2」類型宣告，因此與此屬性的預設類型不相容。", "smw-property-req-violation-import-type": "偵測到與所匯入詞彙「$1」預先定義類型不相容的類型宣告。在一般情況下，因為有從匯入定義裡檢索資訊，所以不需要宣告類型。", "smw-property-req-violation-change-propagation-locked-error": "屬性「$1」已調整，並要求使用[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改傳播]過程來重新評估分配的實體。此期間屬性頁面會在主要規格更新完成之前被鎖定，以防止中途打斷或是規格衝突。在頁面取消鎖定之前，過程花費的時間會依據[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 任務佇列]排程的大小與頻率。", "smw-property-req-violation-change-propagation-locked-warning": "屬性「$1」已調整，並要求使用[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改傳播]過程來重新評估分配的實體。更新花費的時間會依據[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 任務佇列]排程的大小與頻率，並建議延後對屬性進行更改，以防止中途打斷或是規格衝突。", "smw-property-req-violation-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改傳播]更新正在等待中（估計有 $1 個[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|任務|任務}}]），建議在處理完成前暫停屬性方面的變動，以防止中途打斷或是規格衝突。", "smw-property-req-violation-missing-maps-extension": "Semantic MediaWiki 無法檢測出[https://www.semantic-mediawiki.org/wiki/Extension:Maps 「Maps」]擴充，該為所需項目因此限制此屬性的功能（例如：無法儲存或處理地理資料）。", "smw-property-req-violation-type": "屬性包含衝突的類型規格，可能會導致值註解無效，因此應讓使用者分配一個合適類型。", "smw-property-req-error-list": "屬性包含以下錯誤或警告：", "smw-property-req-violation-parent-type": "屬性「$1」與分配的父屬性「$2」擁有不同的類型註解。", "smw-property-req-violation-forced-removal-annotated-type": "[https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance mandatory 父類型繼承]執行已啟用，「$1」屬性的註解類型不符合父屬性「$2」類型，並且為了反應請求而被修改。建議調整頁內類型定義，來去除此屬性的錯誤訊息和強制執行。", "smw-change-propagation-protection": "此頁面已被鎖定，以防止當[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改傳播]更新在運行時意外的資料變動。在頁面解開鎖定前，會依據[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 任務佇列]排程的大小與頻率花費一段時間。", "smw-category-change-propagation-locked-error": "分類「$1」已調整，並要求使用[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改傳播]過程來重新評估分配的實體。此期間分類頁面會在主要規格更新完成之前被鎖定，以防止中途打斷或是規格衝突。在頁面取消鎖定之前，過程花費的時間會依據[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 任務佇列]排程的大小與頻率。", "smw-category-change-propagation-locked-warning": "分類「$1」已調整，並要求使用[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改傳播]過程來重新評估分配的實體。更新花費的時間會依據[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue 任務佇列]排程的大小與頻率，並建議延後對分類進行更改，以防止中途打斷或是規格衝突。", "smw-category-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation 更改傳播]更新正在等待中（估計有 $1 個[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|任務|任務}}]），建議在處理完成前暫停分類方面的變動，以防止中途打斷或是規格衝突。", "smw-category-invalid-value-assignment": "「$1」未被視為有效分類或值註解。", "protect-level-smw-pageedit": "僅允許俱有頁面編輯權限的使用者（Semantic MediaWiki）", "smw-create-protection": "當[https://www.semantic-mediawiki.org/wiki/Help:Authority_mode 權威模式]啟用後，具有符合「$2」權限的使用者（或是[https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups 使用者群組]）會在建立屬性「$1」時受限", "smw-create-protection-exists": "當[https://www.semantic-mediawiki.org/wiki/Help:Authority_mode 權威模式]啟用後，具有符合「$2」權限的使用者（或是[https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups 使用者群組]）會在更改屬性「$1」時受限", "smw-edit-protection": "此頁面已被[[Property:Is edit protected|保護]]以防止意外的資料變動，並僅可由擁有編輯權限（\"$1\"）的使用者或[https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups 使用者群組]來編輯。", "smw-edit-protection-disabled": "編輯保護已被停用，因此「$1」不能用於保護實體頁面來免於未經授權的編輯。", "smw-edit-protection-auto-update": "Semantic MediaWiki 已根據「Is edit protected」屬性來更新保護狀態。", "smw-edit-protection-enabled": "編輯已保護內容（Semantic MediaWiki）", "smw-patternedit-protection": "此頁面已被保護，並僅能由具有適當 <code>smw-patternedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions 權限]的使用者編輯。", "smw-property-predefined-edip": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，指示編輯是否受到保護的預先定義屬性。", "smw-property-predefined-long-edip": "儘管任何使用者都能添加此屬性至主題，但僅有專用權限的使用者可以對已被添加的實體做出編輯或解除保護。", "smw-query-reference-link-label": "查詢參考文獻", "smw-format-datatable-emptytable": "表格裡沒有可用資料", "smw-format-datatable-info": "顯示 _TOTAL_ 個項目裡從 _START_ 到 _END_ 的內容", "smw-format-datatable-infoempty": "顯示 0 個項目從 0 到 0 的內容", "smw-format-datatable-infofiltered": "（從 _MAX_ total 項目篩選）", "smw-format-datatable-lengthmenu": "顯示 _MENU_ 項目", "smw-format-datatable-loadingrecords": "載入中...", "smw-format-datatable-processing": "處理中...", "smw-format-datatable-search": "搜尋：", "smw-format-datatable-zerorecords": "找不到相符的記錄", "smw-format-datatable-first": "第一", "smw-format-datatable-last": "最後", "smw-format-datatable-next": "下一個", "smw-format-datatable-previous": "上一個", "smw-format-datatable-sortascending": ":啟用升冪排序欄位", "smw-format-datatable-sortdescending": ":啟用降冪排序欄位", "smw-format-datatable-toolbar-export": "匯出", "smw-category-invalid-redirect-target": "分類「$1」包含指向到非分類命名空間的無效重新導向目標。", "smw-parser-function-expensive-execution-limit": "解析功能已達到耗量執行的限制（請參閱 [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>] 設置參數）。", "smw-postproc-queryref": "Semantic MediaWiki 會在一些必要查詢後期處理條件下，重新整理頁面。", "apihelp-smwinfo-summary": "檢索有關 Semantic MediaWiki 統計資訊與其它詮釋資訊的 API 模組。", "apihelp-ask-summary": "使用請求語言來查詢 Semantic MediaWiki 的 API 模組。", "apihelp-askargs-summary": "使用請求語言作為條件、輸出內容、參數的清單，來查詢 Semantic MediaWiki 的 API 模組。", "apihelp-browsebyproperty-summary": "檢索有關屬性或是屬性清單資訊的 API 模組。", "apihelp-browsebysubject-summary": "檢索有關主題資訊的 API 模組。", "apihelp-smwtask-summary": "執行 Semantic MediaWiki 相關任務的 API 模組（僅用於內部，不適用公共）。", "apihelp-smwbrowse-summary": "支援在 Semantic MediaWiki 瀏覽不同實體類型行動的 API 模組。", "apihelp-ask-parameter-api-version": "輸出格式：\n;2：用於結果清單，使用 {} 的後端相容格式。\n;3：使用 [] 來作為結果清單的實驗格式。", "apihelp-smwtask-param-task": "定義任務類型", "apihelp-smwtask-param-params": "符合所選任務類型需求的 JSON 編碼參數", "smw-apihelp-smwtask-example-update": "針對特定主題之運作更新任務的範例：", "smw-api-invalid-parameters": "無效參數「$1」", "smw-parser-recursion-level-exceeded": "$1 個遞迴的層次在解析期間溢出。建議您驗證模板結構，或是如有需要可設置參數 <code>$maxRecursionDepth</code>。", "smw-property-page-list-count": "顯示使用到此屬性的 $1 個{{PLURAL:$1|頁面|頁面}}。", "smw-property-page-list-search-count": "顯示使用到符合值「$2」之屬性的 $1 個{{PLURAL:$1|頁面|頁面}}。", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter 搜尋篩選]允許包含查詢表達式，例如像是 <code>~</code> 或 <code>!</code>。所選的[https://www.semantic-mediawiki.org/wiki/Help:Query_expressions 查詢引擎]可能會支援不區分大小寫，或是其它較短的表達式。例如像：\n\n* <code>in:</code> 的結果為應有包含，如：'<code>in:Foo</code>'\n\n* <code>not:</code> 的結果為應不包含，如：'<code>not:Bar</code>'", "smw-property-reserved-category": "分類", "smw-category": "分類", "smw-datavalue-uri-invalid-scheme": "「$1」未被列入在有效的 URI 架構。", "smw-datavalue-uri-invalid-authority-path-component": "「$1」被確認含有無效的權威「$2」或路徑組成。", "smw-browse-property-group-title": "屬性群組", "smw-browse-property-group-label": "屬性群組標籤", "smw-browse-property-group-description": "屬性群組描述", "smw-property-predefined-ppgr": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，可識別用來替屬性分組實例之實體（主要是分類）的預先定義屬性。", "smw-filter": "篩選", "smw-section-expand": "展開章節", "smw-section-collapse": "摺疊章節", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]格式", "smw-help": "說明", "smw-cheat-sheet": "提示", "smw-personal-jobqueue-watchlist": "任務佇列監視清單", "smw-personal-jobqueue-watchlist-explain": "等待執行的任務佇列項目估計數。", "smw-property-predefined-label-skey": "排序鍵", "smw-processing": "正在處理…", "smw-loading": "正在載入…", "smw-fetching": "正在抓取…", "smw-preparing": "正在準備…", "smw-expand": "展開", "smw-collapse": "摺疊", "smw-copy": "複製", "smw-copy-clipboard-title": "複製內容到剪貼簿", "smw-jsonview-expand-title": "展開 JSON 檢視", "smw-jsonview-collapse-title": "摺疊 JSON 檢視", "smw-jsonview-search-label": "搜尋：", "smw-redirect-target-unresolvable": "目標無法解決，出於原因「$1」", "smw-types-title": "類型：$1", "smw-schema-namespace-editcontentmodel-disallowed": "不允許更改[https://www.semantic-mediawiki.org/wiki/Help:Schema 架構頁面]的內容模組。", "smw-schema-namespace-edit-protection": "此頁面已被保護，並僅能由具有適當 <code>smw-schemaedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions 權限]的使用者編輯。", "smw-schema-namespace-edit-protection-by-import-performer": "此頁面是由列出的[https://www.semantic-mediawiki.org/wiki/Import_performer 匯入執行]所匯入，這代表著僅限於所列出使用者能改變此頁面內容。", "smw-schema-error-title": "驗證{{PLURAL:$1|錯誤}}", "smw-schema-error-schema": "驗證架構'''$1'''發現以下不一致：", "smw-schema-error-miscellaneous": "雜項錯誤（$1）", "smw-schema-error-validation-json-validator-inaccessible": "無法存取（或安裝）JSON 驗證器「<b>$1</b>」，此為無法審查檔案「$2」的原因，因為會阻止保存或變動目前的頁面。", "smw-schema-error-validation-file-inaccessible": "驗證檔案「$1」無法存取。", "smw-schema-error-violation": "[\"$1\"，\"$2\"]", "smw-schema-error-type-missing": "內容缺少可用於識別且用在[https://www.semantic-mediawiki.org/wiki/Help:Schema 架構命名空間]的類型", "smw-schema-error-type-unknown": "類型「$1」未註冊，不能用於 [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema] 命名空間內容裡。", "smw-schema-error-json": "JSON 錯誤：\"$1\"", "smw-schema-error-input": "輸入驗證發現以下問題，在內容儲存之前需要先解決。[https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling 架構說明]頁面可以提供一些如何移除架構輸入不一致，或是問題解決的建議。", "smw-schema-error-input-schema": "驗證架構'''$1'''發現以下不一致，在內容儲存之前需要先解決。[https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling 架構說明]頁面可以提供一些如何解決問題的建議。", "smw-schema-error-title-prefix": "此架構類型需要開頭帶有「$1」字首的架構標題。", "smw-schema-validation-error": "類型「$1」未註冊，不能用於 [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema] 命名空間內容裡。", "smw-schema-validation-schema-title": "JSON綱要", "smw-schema-summary-title": "摘要", "smw-schema-title": "綱要", "smw-schema-usage": "使用狀況", "smw-schema-type": "綱要類型", "smw-schema-type-description": "類型描述", "smw-schema-description": "綱要描述", "smw-schema-description-link-format-schema": "此架構類型支援的特徵定義用於創建相關[[Property:Formatter schema|格式化架構]]分配屬性的上下文連結。", "smw-schema-description-search-form-schema": "這個架構類型支援用於定義[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch 擴充搜尋]偏好設定的輸入表單與字元，其中包含如何產生輸入欄位、定義預設命名空間、或是宣告用於搜尋請求之字首表達的說明。", "smw-schema-description-property-profile-schema": "此架構類型支援用來聲明已分配屬性與其註解值之特性的偏好設定定義。", "smw-schema-description-facetedsearch-profile-schema": "此架構類型支援用於組成[[Special:FacetedSearch|多面搜尋]]環境的個人設定定義。", "smw-schema-description-property-group-schema": "此架構類型支援以定義[https://www.semantic-mediawiki.org/wiki/Help:Property_group 屬性群組]的方式來協助架構[https://www.semantic-mediawiki.org/wiki/Help:Special:Browse 瀏覽]介面。", "smw-schema-description-property-constraint-schema": "這支援定義屬性實體的限制條件和所分配的值。", "smw-schema-description-class-constraint-schema": "此架構類型支援用於類別實例的限制規則定義（即為分類）。", "smw-schema-tag": "{{PLURAL:$1|標籤|標籤}}", "smw-property-predefined-constraint-schema": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來定義限制架構的預先定義屬性。", "smw-property-predefined-schema-desc": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，儲存架構描述的預先定義屬性。", "smw-property-predefined-schema-def": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，儲存架構內容的預先定義屬性。", "smw-property-predefined-schema-tag": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 提供，識別綱要收集的預先定義屬性。", "smw-property-predefined-long-schema-tag": "識別相似內容或特徵之綱要的標籤。", "smw-property-predefined-schema-type": "「$1」是由 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] 所提供，用來描述區分架構群組類型的預先定義屬性。", "smw-property-predefined-long-schema-type": "各[https://www.semantic-mediawiki.org/wiki/Help:Schema/Type 類型]提供本身對於語法元素的闡明和應用規則，並可在[https://www.semantic-mediawiki.org/wiki/Help:Schema#validation 驗證架構]的幫助下來表示出。", "smw-ask-title-keyword-type": "關鍵字搜尋", "smw-ask-message-keyword-type": "此搜尋符合 <code><nowiki>$1</nowiki></code> 條件。", "smw-remote-source-unavailable": "無法連接至遠端「$1」目標。", "smw-remote-source-disabled": "來源'''$1'''已停用遠端請求支援！", "smw-remote-source-unmatched-id": "來源「'''$1'''」不符合可支援遠端請求的 Semantic MediaWiki 版本。", "smw-remote-request-note": "結果已從遠端來源「'''$1'''」檢索，並且可能用於產生包含目前 wiki 的不可用資訊內容。", "smw-remote-request-note-cached": "結果已從遠端來源「'''$1'''」給'''快取'''，並且可能用於產生包含目前 wiki 的不可用資訊內容。", "smw-parameter-missing": "遺失參數「$1」。", "smw-property-tab-usage": "使用量", "smw-property-tab-profile-schema": "偏好設定架構", "smw-property-tab-redirects": "同義詞", "smw-property-tab-subproperties": "子屬性", "smw-property-tab-errors": "不適當分配", "smw-property-tab-constraint-schema": "限制架構", "smw-property-tab-constraint-schema-title": "已編譯限制架構", "smw-property-tab-specification": "... 更多", "smw-concept-tab-list": "清單", "smw-concept-tab-errors": "錯誤", "smw-ask-tab-result": "結果", "smw-ask-tab-extra": "額外", "smw-ask-tab-debug": "除錯", "smw-ask-tab-code": "代碼", "smw-install-incomplete-tasks-title": "不完整的管理任務", "smw-install-incomplete-intro": "這裡有 $2 個未完成或是用於結束{{PLURAL:$1|安裝|更新}} [https://www.semantic-mediawiki.org Semantic MediaWiki] 的[[Special:PendingTaskList|待定]]{{PLURAL:$2|任務}}。擁有足夠權限的管理員或使用者可以完成{{PLURAL:$2|它|這些}}。為了避免資料不一致，應在添加新資料之前處理好。", "smw-install-incomplete-intro-note": "此訊息在所有相關任務解決後會消失。", "smw-pendingtasks-intro-empty": "沒有與 Semantic MediaWiki 關聯的任務被歸類為待辦、未完成、或是需處理。", "smw-pendingtasks-intro": "此頁面提供與 Semantic MediaWiki 關聯，被歸類在待辦、未完成、或是需處理的任務相關資訊。", "smw-pendingtasks-setup-no-tasks-intro": "安裝（或更新）已完成，目前沒有待辦或是需處理的任務。", "smw-pendingtasks-tab-setup": "設定", "smw-updateentitycollation-incomplete": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> 設定近期有更動，需要執行 <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> 手稿來讓實體更新，來包含正確的排序欄位值。", "smw-updateentitycountmap-incomplete": "<code>smw_countmap</code> 欄位是在最新釋出裡添加，且需要執行 <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> 手稿以讓功能可存取此欄位的內容。", "smw-populatehashfield-incomplete": "在設定過程中裡有略過填充 <code>smw_hash</code> 欄位，必須執行 <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> 手稿。", "smw-install-incomplete-populate-hash-field": "在設定過程中裡有略過填充 <code>smw_hash</code> 欄位，必須執行 <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> 手稿。", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code> 被選擇為[https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore 預設儲存]，但擴充無法找出 <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> 手稿執行過的紀錄, 請依照說明運行手稿。", "smw-elastic-rebuildelasticindex-run-incomplete": "<code>ElasticStore</code> 被選擇為[https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore 預設儲存]，但擴充無法找出 <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> 手稿執行過的紀錄, 請依照說明運行手稿。", "smw-pendingtasks-setup-intro": "<b>Semantic MediaWiki</b> 的{{PLURAL:$1|安裝|更新}}已歸類出以下任務為[https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade 未完成]，而管理員（或是其他擁有足夠權限的使用者）應在使用者繼續建立或是更改內容之前解決掉這些任務。", "smw-pendingtasks-setup-tasks": "任務", "smw-filter-count": "篩選次數", "smw-es-replication-check": "複製檢查（Elasticsearch）", "smw-es-replication-error": "Elasticsearch 複製問題", "smw-es-replication-file-ingest-error": "檔案接納問題", "smw-es-replication-maintenance-mode": "Elasticsearch 維護", "smw-es-replication-error-missing-id": "複製監測發現 Elasticsearch 後端缺少條目「$1」（ID：$2）。", "smw-es-replication-error-divergent-date": "複製監測發現有關條目「$1」（ID：$2）的<b>變動日期</b>出現差異。", "smw-es-replication-error-divergent-date-short": "以下日期資訊為用於比較：", "smw-es-replication-error-divergent-date-detail": "參考修改日期：\n*Elasticsearch：$1\n*資料庫：$2", "smw-es-replication-error-divergent-revision": "複製監測發現有關條目「$1」（ID：$2）的<b>關聯修訂</b>出現差異。", "smw-es-replication-error-divergent-revision-short": "以下關聯修訂資料為用於比較：", "smw-es-replication-error-divergent-revision-detail": "參考關聯修訂：\n*Elasticsearch：$1\n*資料庫：$2", "smw-es-replication-error-maintenance-mode": "因為在[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>維護模式</b>]下進行的緣故，Elasticsearch 複製目前受到限制，對實體與頁面做出的更改<b>不會</b>立即可見，另外查詢結果可能會含有過時資訊。", "smw-es-replication-error-no-connection": "複製間是無法執行任何檢查，因為這無法建立與 Elasticsearch 叢集的連接。", "smw-es-replication-error-bad-request-exception": "Elasticsearch 連接處理拋出一個表明在複製與搜尋請求期間所出現的持續問題之錯誤請求例外（\"400 conflict http error\"）。", "smw-es-replication-error-other-exception": "Elasticsearch 連接處理拋出一個例外：「$1」。", "smw-es-replication-error-suggestions": "建議編輯或清除頁面來移除掉差異。如果問題持續存在，請檢查看看 Elasticsearch 叢集本身（配置器、例外狀況、磁碟空間等等）。", "smw-es-replication-error-suggestions-maintenance-mode": "建議聯絡 wiki 管理員來檢查[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild 索引重建]是否有正確進行，或是 <code>refresh_interval</code> 尚未有被設定到預期的預設值。", "smw-es-replication-error-suggestions-no-connection": "建議聯絡 wiki 管理員並回報「無法連接」問題。", "smw-es-replication-error-suggestions-exception": "請查看日誌來取得關於 Elasticsearch 的狀態資訊、索引、以及可能會有的錯誤設置問題。", "smw-es-replication-error-file-ingest-missing-file-attachment": "複製監控找出「$1」缺少[[Property:File attachment|檔案附件]]註解指示，無法開始或結束檔案接納處理。", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "請確認[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion 檔案接納]任務有被安排，並請在註解與檔案索引可用之前執行。", "smw-report": "報告", "smw-legend": "圖例", "smw-datavalue-constraint-schema-category-invalid-type": "註解「$1」架構對於分類無效，需要是「$2」類型。", "smw-datavalue-constraint-schema-property-invalid-type": "註解「$1」架構對於分類無效，需要是「$2」類型。", "smw-entity-examiner-check": "在背景運行{{PLURAL:$1|審查}}", "smw-entity-examiner-indicator": "實體問題面板", "smw-entity-examiner-deferred-check-awaiting-response": "「$1」審查目前在等待來自後端的回應。", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "限制", "smw-entity-examiner-associated-revision-mismatch": "修訂", "smw-entity-examiner-deferred-fake": "虛假", "smw-entity-examiner-indicator-suggestions": "作為實體審查的一部分，找出了以下{{PLURAL:$1|問題}}，建議仔細地檢閱{{PLURAL:$1|問題}}並採取適當的{{PLURAL:$1|操作}}。", "smw-indicator-constraint-violation": "{{PLURAL:$1|限制}}", "smw-indicator-revision-mismatch": "修訂", "smw-indicator-revision-mismatch-error": "[https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner 相關修訂]檢查發現在 MediaWiki 引用的修訂之間內容不符合，且關聯到用於此實體的 Semantic MediaWiki。", "smw-indicator-revision-mismatch-comment": "不符合通常表示某些程序中斷了在 Semantic MediaWiki 的儲存操作。建議檢視伺服器日誌並查看例外或其它錯誤。", "smw-facetedsearch-intro-text": "Semantic MediaWiki 的 [https://www.semantic-mediawiki.org/wiki/Faceted_search <b>多面搜尋</b>]為使用者提供了一個簡單的介面，可藉由從依賴屬性和分類所建立的多面檢視內容裡，來快速縮小某個條件的查詢結果範圍。", "smw-facetedsearch-intro-tips": "*使用 <code>category:?</code>、<code>property:?</code>，或是 <code>concept:?</code> 能用來尋找可用於建立結構化條件集的分類、屬性、或概念\n* 使用 #ask 語法可用來描述條件（例如<code><nowiki>[[Category:Foo]]</nowiki></code>）\n* 使用「OR」、「AND」，或其他查詢表達式可建立複雜的條件 \n* <code>in:</code> 或 <code>phrase:</code> 這一類的表達式可用於全文字匹配或是非結構話搜尋（若所選的[https ://www.semantic-mediawiki.org/wiki/Query_engine 查詢引擎]支援這些表達式）", "smw-facetedsearch-profile-label-default": "預設個人檔案", "smw-facetedsearch-intro-tab-explore": "探索", "smw-facetedsearch-intro-tab-search": "搜尋", "smw-facetedsearch-explore-intro": "選擇一個集合並開始瀏覽。", "smw-facetedsearch-profile-options": "個人檔案選項", "smw-facetedsearch-size-options": "分頁選項", "smw-facetedsearch-order-options": "排序選項", "smw-facetedsearch-format-options": "顯示選項", "smw-facetedsearch-format-table": "表格", "smw-facetedsearch-input-filter-placeholder": "篩選…", "smw-facetedsearch-no-filters": "無篩選。", "smw-facetedsearch-no-filter-range": "無篩選範圍。", "smw-facetedsearch-no-output": "所選的「$1」格式沒有可用的輸出。", "smw-facetedsearch-clear-filters": "清除{{PLURAL:$1|篩選}}", "smw-search-placeholder": "搜尋…", "smw-listingcontinuesabbrev": "續", "smw-showingresults": "以下顯示從第 <strong>$2</strong> 筆開始，共 {{PLURAL:$1|<strong>1</strong> 筆結果|<strong>$1</strong> 筆結果}}："}