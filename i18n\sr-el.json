{"@metadata": {"authors": ["Acamicamacaraca", "Kghbln", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Milicevic01", "<PERSON><PERSON><PERSON>", "Rancher", "<PERSON><PERSON>", "Sociologist", "Zoranzoki21"]}, "smw-desc": "Čini viki pristupačnim — za ma<PERSON>ine „i” ljude ([https://www.semantic-mediawiki.org/wiki/Help:User_manual dokumentacija na mreži])", "smw-error": "Greška", "smw-upgrade-error-title": "Greška » Semantički Medijaviki", "smw-upgrade-error-why-title": "<PERSON><PERSON><PERSON><PERSON> vidim ovu stranicu?", "smw-upgrade-error-how-title": "<PERSON><PERSON> da ispravim ovu grešku?", "smw-upgrade-maintenance-why-title": "<PERSON><PERSON><PERSON><PERSON> vidim ovu stranicu?", "smw-semantics-not-enabled": "Funkcionalnost Semantičkog MediaWiki-ja nije omogućena za ovaj viki.", "smw_viewasrdf": "RDF fid", "smw_finallistconjunct": " i", "smw-factbox-head": "… više o stranici „$1”", "smw-factbox-facts": "Činjenice", "smw-factbox-attachments": "<PERSON><PERSON><PERSON><PERSON>", "smw-factbox-attachments-help": "Prikazuje dostupne priloge", "smw_isspecprop": "Ovo svojstvo je posebno na ovom vikiju.", "smw-concept-cache-header": "Upotreba keša", "smw-concept-no-cache": "<PERSON><PERSON> keš.", "smw_concept_description": "Opis koncepta „$1”", "smw_no_concept_namespace": "Koncepti jedino mogu da se definišu na stranicama u imenskom prostoru Concept:", "smw_multiple_concepts": "Svaka stranica koncepta sme da ima samo jednu definiciju koncepta.", "smw_concept_cache_miss": "Koncept \"$1\" se trenutno ne može k<PERSON>, po<PERSON><PERSON> konfiguracija vikija zahteva da bude komputovan oflajn.\nAko problem ne nestane za neko vreme, zatražite od administratora sajta da učini ovaj koncept dostupnim.", "smw_noinvannot": "Na obrtna svojstva ne mogu da se dodele vrednosti.", "version-semantic": "Semantički dodaci", "smw_baduri": "URI-ji za oblik „$1“ nisu dozvoljeni.", "smw_printername_count": "Rezultat brojanja", "smw_printername_csv": "CSV izvoz", "smw_printername_dsv": "DSV izvoz", "smw_printername_debug": "Upit za otklanjanje grešaka (za stručnjake)", "smw_printername_embedded": "Ugradi sadržaj stranice", "smw_printername_json": "JSON izvoz", "smw_printername_list": "Spisak", "smw_printername_plainlist": "Obična lista", "smw_printername_ol": "<PERSON><PERSON><PERSON><PERSON> spisak", "smw_printername_ul": "<PERSON><PERSON><PERSON><PERSON> spisak", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON><PERSON> tabela", "smw_printername_template": "Šablon", "smw_printername_templatefile": "Datoteka <PERSON>", "smw_printername_rdf": "RDF izvoz", "smw_printername_category": "Kategorija", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-limit": "Najveći broj rezultata koji će biti vraćeni", "smw-paramdesc-mainlabel": "Etiketa koja će se koristiti za ime glavne stranice", "smw-paramdesc-link": "Prikaži vrednosti u obliku veza", "smw-paramdesc-intro": "Tekst koji treba da se prikaže pre rezultata upita (u slučaju potrebe)", "smw-paramdesc-outro": "Tekst koji treba da se prikaže iza rezultata upita (u slučaju potrebe)", "smw-paramdesc-default": "Tekst koji treba da se prikaže u slučaju da upit nije dao rezultate", "smw-paramdesc-sep": "Razdvajač između rezultata", "smw-paramdesc-columns": "Broj kolona u kojima će se prikazati rezultati", "smw-paramdesc-embedonly": "Ne prikazuj zaglavlja", "smw-paramdesc-searchlabel": "Tekst veze s rezultatima", "smw-paramdesc-template-arguments": "Podešavanje isporuke imenovanih parametara do šablona", "smw_iq_disabled": "Semantički upiti su onemogućeni na ovom vikiju.", "smw_iq_moreresults": "... vi<PERSON>e rezultata", "smw_parseerror": "Data vrednost nije shvaćena.", "smw_decseparator": ",", "smw_kiloseparator": ".", "smw_notitle": "\"$1\" se ne može koristiti za ime stranice na ovom vikiju.", "smw_wrong_namespace": "Samo stranice iz imenskog prostora \"$1\" su dozvoljene ovde.", "smw_manytypes": "<PERSON><PERSON>š<PERSON> od jednog tipa je definisano za ovu osobinu.", "smw_emptystring": "Prazni stringovi nisu prihvatljivi.", "smw_notinenum": "„$1” nije na listi ($2)  [[Property:Allows value|mogućih vrednosti]] za svojstvo „$3”.", "smw_noboolean": "\"$1\" nije prepoznat kao Bolova vrednost (istinito/neistinito).", "smw_true_words": "is<PERSON><PERSON>, i, da, d", "smw_false_words": "neistinito, n, ne, n", "smw_nofloat": "\"$1\" nije broj.", "smw_infinite": "B<PERSON>jevi du<PERSON> kao \"$1\" nisu podržani.", "smw_novalues": "Nema naznačene vrednosti.", "smw_nodatetime": "Format datuma \"$1\" nije razumljiv.", "smw_toomanyclosing": "<PERSON>z<PERSON><PERSON> da je previše slučajeva tipa \"$1\" u upitu.", "smw_noclosingbrackets": "<PERSON>eke uglaste zagrade \"<nowiki>[[</nowiki>\" u vašem upitu, nisu zatvorene odgovarajućim \"]]\".", "smw_misplacedsymbol": "Simbol \"$1\" je iskorišćen na mestu gde nije od koristi.", "smw_unexpectedpart": "<PERSON><PERSON> <PERSON> \"$1\" nije sh<PERSON>.\nRezultati mogu biti neočekivani.", "smw_emptysubquery": "Neki podupiti ne sadrže valjane uslove.", "smw_misplacedsubquery": "Neki podupiti su korišćeni na mestu gde podupiti nisu dozvoljeni.", "smw_valuesubquery": "<PERSON>a vrednosti osobine \"$1\", podupiti nisu podrž<PERSON>.", "smw_badqueryatom": "<PERSON><PERSON> de<PERSON> \"<nowiki>[[…]]</nowiki>\" upita nije shva<PERSON>en.", "smw_propvalueproblem": "Vrednost za osobinu \"$1\" nije shvaćena.", "smw_noqueryfeature": "Neki delovi ovog upita nisu podržani na ovom vikiju, te je deo upita izostavljen ($1).", "smw_noconjunctions": "Konjukcije u upitima nisu podržane na ovom vikiju, te je deo upita izostavljen ($1).", "smw_nodisjunctions": "Disjunkcije u upitima nisu podržane na ovom vikiju, te je deo upita izostavljen ($1).", "smw_querytoolarge": "<PERSON><PERSON> moguće razmatrati {{PLURAL:$2|sledeći uslov upita $2|sledeće uslove upita $2}} zbog ograničenja vikija na veličinu i dubinu upita: <code>$1</code>.", "smw_notemplategiven": "Ponudite vrednost za parametar ”šablon”, kako bi ovaj format upita dao rezultat.", "smw_type_header": "<PERSON><PERSON><PERSON> tipa \"$1\"", "smw_typearticlecount": "Prikaz $1 {{PLURAL:$1|osobine|osobina}} za ovaj tip.", "smw_attribute_header": "Stranice koje koriste osobinu \"$1\"", "smw_attributearticlecount": "Prikaz $1 {{PLURAL:$1|stranice|stranica}} koje koriste ovu osobinu.", "smw-propertylist-subproperty-header": "Podsvojstva", "smw-propertylist-redirect-header": "<PERSON>nimi", "specialpages-group-smw_group": "Semantički Medijaviki", "specialpages-group-smw_group-maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exportrdf": "Izvoz stranica u RDF", "smw_exportrdf_docu": "Ova stranica omogućava preuzimanje podataka s neke stranice u formatu RDF.\nDa biste izvezli stranice, unesite naslove u kućicu ispod (po jedan naslov u svakom redu).", "smw_exportrdf_recursive": "Rekurzivno izvezi sve srodne stranice.\nImajte na umu da rezultat može biti veoma obiman.", "smw_exportrdf_backlinks": "Takođe izvezi sve stranice koje vežu na već izvezene stranice.\nGeneriše RDF koji je moguće pregledati.", "smw_exportrdf_lastdate": "Ne izvozi stranice koje nisu menjane od datog momenta.", "smw_exportrdf_submit": "Izvoz", "uriresolver": "URI rezolver", "properties": "Svojstva", "smw-categories": "Kategorije", "smw_properties_docu": "Sledeće osobine se koriste na vikiju.", "smw_property_template": "$1 tipa $2 ($3 {{PLURAL:$3|upotreba|upotrebe|upotreba}})", "smw_propertylackspage": "Sve osobine trebaju imati opisnu stranicu!", "smw_propertylackstype": "Nema definisanog tipa za ovu osobinu (za sada usvajam tip $1).", "smw_propertyhardlyused": "<PERSON><PERSON> oso<PERSON> jedva da se koristi na ovom  vikiju!", "smw-special-property-searchform-options": "Opcije", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Neodobreno", "concepts": "Koncepti", "smw-special-concept-header": "Lista koncepata", "smw-special-concept-empty": "<PERSON><PERSON> pronađen nijedan koncept.", "unusedproperties": "Neisko<PERSON><PERSON><PERSON><PERSON> osobine", "smw-unusedproperty-template": "$1 tipa $2", "wantedproperties": "T<PERSON><PERSON><PERSON> o<PERSON>bine", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|upotreba|upotreba}})", "smw_purge": "Osveži", "smw-purge-failed": "Semantički Medijaviki je pokušao da osveži stranicu, ali nije uspeo u tome", "types": "Tipovi", "smw_types_docu": "Lista [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes dostupnih tipova podataka] sa svakim [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipom] predstavlja jedinstven skup atributa koji opisuju vrednost u smislu karakteristika čuvanje i izlaganje koje su nasledne po dodeljenim svojstvima.", "smw-statistics": "Semantička statistika", "smw-statistics-query-size": "Veličina upita", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Koncept|Koncepti}}", "ask": "Semantička pretraga", "smw_ask_sortby": "Poređaj u kolone (opcionalno)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_descorder": "Opada<PERSON><PERSON><PERSON>", "smw-ask-order-rand": "Slučajna", "smw_ask_submit": "Pronađi rezultate", "smw_ask_editquery": "<PERSON><PERSON><PERSON> upit", "smw_add_sortcondition": "[<PERSON><PERSON><PERSON>]", "smw-ask-sort-add-action": "<PERSON><PERSON><PERSON>", "smw_ask_hidequery": "<PERSON><PERSON><PERSON><PERSON> upit (kompaktni prikaz)", "smw_ask_help": "Upit u stranice pomoći", "smw_ask_queryhead": "<PERSON><PERSON>", "smw_ask_printhead": "Izbor <PERSON>", "smw_ask_printdesc": "(dodaj jedno ime osobine po liniji)", "smw_ask_format_as": "<PERSON><PERSON><PERSON><PERSON><PERSON> kao:", "smw_ask_defaultformat": "podrazumevano", "smw_ask_otheroptions": "Druge opcije", "smw_ask_show_embed": "Prikaži ugrađeni kod", "smw_ask_hide_embed": "Sak<PERSON>j ugradbeni kod", "smw_ask_embed_instr": "Za neposredno gneždenje ovog upita u stranicu vikija, koristi donji kod.", "smw-ask-delete": "Ukloni", "smw-ask-sorting": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-options": "Opcije", "smw-ask-options-sort": "Opcije <PERSON>", "smw-ask-format-options": "Format i opcije", "smw-ask-parameters": "Parametri", "smw-ask-search": "Pretraga", "smw-ask-result": "Rezultat", "smw-ask-empty": "<PERSON><PERSON><PERSON><PERSON> sve unose", "smw-ask-format": "Format", "smw-ask-query-search-info": "Na upit <code><nowiki>$1</nowiki></code> odgovorio je {{PLURAL:$3|1=<code>$2</code> (iz keša)|<code>$2</code> (iz keša)|<code>$2</code>}} za $4 {{PLURAL:$4|sekund|sekunda|sekundi}}.", "smw-ask-extra-other": "Drugo", "searchbyproperty": "Pretraga po svojstvima", "smw_sbv_docu": "Pretraži sve stranice koje poseduju datu osobinu i vrednost.", "smw_sbv_novalue": "Unesite valjanu vrednost za ovu osobinu, ili pogledajte sve vrednosti osobine za \"$1\".", "smw_sbv_displayresultfuzzy": "Lista svih stranica koje sadrže svojstvo „$1” sa vrednošću „$2”.\nPošto je pronađeno samo nekoliko rezultata, takođe su prikazane približne vrednosti.", "smw_sbv_property": "Svojstvo:", "smw_sbv_value": "Vrednost:", "smw_sbv_submit": "Pronađi rezultate", "browse": "Pregled vikija", "smw_browselink": "<PERSON><PERSON>", "smw_browse_article": "Unesite ime početne stranice za pretragu.", "smw_browse_go": "<PERSON><PERSON>", "smw_browse_show_incoming": "Prikaži dolazna svojstva", "smw_browse_hide_incoming": "Sak<PERSON>j dolazna svojstva", "smw_browse_no_outgoing": "Ova stranica nema svojstva.", "smw_browse_no_incoming": "<PERSON>ema osobina koje vežu na ovu stranicu.", "smw-browse-show-group": "Prikaži grupe", "smw-browse-hide-group": "<PERSON>k<PERSON>j grupe", "smw_inverse_label_default": "$1 od", "smw_inverse_label_property": "Obrnuta oznaka svojstva", "pageproperty": "Stranica za pretragu svojstva", "pendingtasklist": "Spisak zadataka na čekanju", "smw_pp_docu": "Pretraži sve vrednosti osobine na datoj stranici.\nUnesite oboje, i stranicu, i osobinu.", "smw_pp_from": "Sa stranice:", "smw_pp_type": "Svojstvo:", "smw_pp_submit": "Pronađeni re<PERSON>", "smw_result_prev": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_result_next": "Sledećih", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "<PERSON><PERSON> rezultata.", "smwadmin": "Kontrolna tabla Semantičkog Medijavikija", "smw-admin-statistics-job-title": "Statistika poslova", "smw-admin-setupsuccess": "Mašina za skladištenje je postavljena.", "smw_smwadmin_return": "Nazad na $1", "smw_smwadmin_updatestarted": "Nov proces ažuriranja za osvežavanje semantičkih podataka je započet.\nSvi sačuvani podaci će biti ponovo izgrađeni ili popravljeni po potrebi.\nMožete da pratite tok ažuriranja na ovoj posebnoj stranici.", "smw_smwadmin_updatenotstarted": "Već je u toku jedan proces ažuriranja.\nNe pokrećite drugi.", "smw_smwadmin_updatestopped": "Svi postojeći procesi ažuriranja su obustavljeni.", "smw_smwadmin_updatenotstopped": "<PERSON><PERSON> bi<PERSON> o<PERSON> pokrenuti proces ažuriran<PERSON>, morate kliknuti na kvadratić da biste potvrdili da ste sigurni.", "smw-admin-docu": "<PERSON>va posebna stranica vam pomaže prilikom instaliranja, ažuriranja, održavanja i korišćenja <a href=\"https://www.semantic-mediawiki.org\">Semantičkog medijavikija</a> i takođe obezbeđuje dodatne funkcije administracije i zadatke, kao i statistiku.\nNe zaboravite da napravite kopiju vrednih podataka pre nego pokrenete funkcije administracije.", "smw-admin-db": "Podešavanje baze podataka", "smw-admin-dbdocu": "Semantički Medijaviki zahteva svoju strukturu baze podataka (koja je nezavisna od Medijavikijeve, te stoga ne obuhvata ostatak instalacije Medijavikija), kako bi mogli da se čuvaju semantički podaci.\nOva funkcija instaliranja se može pokretati više puta bez bojazni da će naneti neku štetu, ali je neophodna samo jednom prilikom instaliranja ili ažuriranja.", "smw-admin-permissionswarn": "Ako se operacija završi sa SQL greškama, korisnik baze podataka (pogledajte vašu LocalSettings.php datoteku) verovatno nema dovoljno dozvola za izvršavanje neophodnih operacija.\nIli dajte neophodne dozvole tom korisniku kako bi mogao da pravi i briše tabele u bazi, privremeno se prijavite kao koren baze podataka u LocalSettings.php datoteci, ili koristite skriptu za održavanje <code>setupStore.php</code>, koja može koristiti akreditacije administratora.", "smw-admin-dbbutton": "Pokretanje ili ažuriranje tabela", "smw-admin-announce": "Najavite Vašu vikiju", "smw-admin-deprecation-notice-title": "Napomene o zastarevanju", "smw-admin-deprecation-notice-docu": "Sledeća sekcija sadrži podešavanja koja su zastarela ili su uklonjena ali su detektovana kao aktivna na ovoj Viki. Očekuje se da će se nekim budućim ažuriranjem ukloniti podrša za ova podešavanja.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> je zastareo parametar i biće uklonjen u verziji $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> je zamenjen parametar sa parametrom <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> je uklonjen parametar u verziji $2", "smw-admin-deprecation-notice-title-notice": "Zastarela podešavanja", "smw-admin-deprecation-notice-title-replacement": "Zamenjena ili preimenovana podešavanja", "smw-admin-deprecation-notice-title-removal": "Uklonjena podešavanja", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw_smwadmin_datarefresh": "Popravljanje podataka", "smw_smwadmin_datarefreshdocu": "Postoji mogućnost za vraćanje svih podataka semantičkog Medijavikija koji su zasnovani na trenutnom sadržaju vikija.\nOvo je korisno za ispravku oštećenih podataka ili za obnovu podataka ako je unutrašnji format promenjen kao rezultat dogradnje softvera.\nAžuriranje se vrši za svaku stranicu ponaosob, i treba da prođe neko vreme kako bi se sve vratilo u normalu.\nIspod je prikazano da li je ažuriranje u toku, a to vam omogućava da započnete ili zaustavite dogradnje (osim ako tu mogućnost nije isključio administrator).", "smw_smwadmin_datarefreshprogress": "<strong><PERSON><PERSON> ažuriranje je već u toku.</strong>\nNormalno je da ažuriranje napreduje sporo, pošto se samo osvežavaju mali delovi podataka svaki put kada korisnik pristupi vikiju.\nDa bi se ovo ažuriranje brž<PERSON> zav<PERSON>, možete pokrenuti skriptu za održavanje Medijavikija <code>runJobs.php</code> (koristite opciju <code>--maxjobs 1000</code> za ograničenje broja ažuriranja u jednom paketu).\nProcena napredovanja trenutnog ažuriranja:", "smw_smwadmin_datarefreshbutton": "Zakaži ponovnu igradnju podataka", "smw_smwadmin_datarefreshstop": "Zaustavi ovo ažuriranje", "smw_smwadmin_datarefreshstopconfirm": "<PERSON>, {{GENDER:$1|siguran|sigurna}} sam.", "smw-admin-support": "Dobijanje podrške", "smw-admin-supportdocu": "Obezbeđeni su razni resursi da bi vam pomogli u slučaju problemâ:", "smw-admin-installfile": "<PERSON><PERSON> naiđete na problem pri instalaciji, najpre pročitajte smernice u <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">datoteci INSTALL</a> i <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">stranici za instalaciju</a>.", "smw-admin-smwhomepage": "Potpuna korisnička dokumentacija za Semantički medijaviki je na <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Greške mogu da se prijavljuju na <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">prat<PERSON><PERSON> g<PERSON></a>. Stranica „<a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">Prijavljivanje grešaka</a>” pruža neke smernice o tome kako da napišete efektivan izveštaj o greš<PERSON>.", "smw-admin-questions": "<PERSON><PERSON> imate dal<PERSON>h pitanja ili predloga, uključite se u diskusiju na <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">korisničkoj mejling listi</a>.", "smw-admin-other-functions": "Druge funkcije", "smw-admin-supplementary-section-subtitle": "Podržane osnovne funkcije", "smw-admin-supplementary-settings-title": "Konfiguracija i podešavanja", "smw-admin-supplementary-operational-statistics-title": "Operativna statistika", "smw-admin-supplementary-operational-statistics-cache-title": "Statistika keširanja", "smw-admin-supplementary-elastic-functions": "Podr<PERSON><PERSON>", "smw-admin-supplementary-elastic-settings-title": "Podešavanja (indicije)", "smw-admin-supplementary-elastic-mappings-summary": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-statistics-title": "Statistika", "smw_adminlinks_datastructure": "Struktura podataka", "smw_adminlinks_displayingdata": "Prikazivanje podataka", "smw_adminlinks_inlinequerieshelp": "Po<PERSON>ć za neposredne upite", "smw-info-par-message": "Poruka za prikaz.", "prefs-smw": "Semantički Medijaviki", "prefs-general-options": "Opšte opcije", "smw-ui-tooltip-title-info": "Informacije", "smw-ui-tooltip-title-warning": "Upozorenje", "smw-ui-tooltip-title-error": "Greška", "smw-ui-tooltip-title-parameter": "Parametar", "smw-ui-tooltip-title-event": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-note": "Na<PERSON>men<PERSON>", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "Referenca", "smw_unknowntype": "„$1” tip ovog svojstva je nevažeći.", "smw_concept_header": "<PERSON><PERSON><PERSON> koncepta \"$1\"", "smw_conceptarticlecount": "Prikaz $1 {{PLURAL:$1|stranice|stranica}} koje pripadaju tom konceptu.", "right-smw-admin": "prist<PERSON><PERSON><PERSON> <PERSON> (Semantički Medijaviki)", "action-smw-patternedit": "uređujete regularne izraze koje koristi Semantički Medijaviki", "group-smwadministrator": "Administratori (Semantički Medijaviki)", "grouppage-smwadministrator": "{{ns:project}}:<PERSON><PERSON> (Semantički Medijaviki)", "smw-sp-properties-header-label": "Spisak svojstava", "smw-sp-admin-settings-button": "Generiši listu podešavanja", "smw-admin-idlookup-input": "Pretraga:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Pregled", "smw-admin-tab-maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-tab-supplement": "Dopunske funkcije", "smw-admin-tab-registry": "Registar", "smw-admin-tab-alerts": "Upozorenja", "smw-admin-alerts-tab-maintenancealerts": "Upozorenja održavanja", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Zast<PERSON>i entiteti", "smw-admin-maintenance-no-description": "Bez opisa.", "smw-livepreview-loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "smw-sp-searchbyproperty-resultlist-header": "Lista rezultata", "smw-search-syntax": "Sin<PERSON>ks<PERSON>", "smw-search-profile-tooltip": "Pretražite funkcije u vezi Semantičkog Medijavikija", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-query-link": "<PERSON>a vi<PERSON><PERSON>, koristite $1.", "smw-search-profile-extended-help-find-forms": "dostupni oblici", "smw-search-profile-extended-section-sort": "Poređaj po", "smw-search-profile-extended-section-form": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-namespace": "<PERSON><PERSON><PERSON> prostor", "smw-search-profile-extended-section-query": "Upit", "smw-search-profile-link-caption-query": "iz<PERSON> upita", "smw-search-show": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-hide": "<PERSON><PERSON><PERSON><PERSON>", "log-name-smw": "Dnevnik Semantičkog Medijavikija", "log-show-hide-smw": "$1 dnevnik Semantičkog Medijavikija", "logeventslist-smw-log": "Dnevnik Semantičkog Medijavikija", "smw-type-tab-properties": "Svojstva", "smw-type-tab-types": "Tipovi", "smw-type-tab-errors": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-no-group": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-limitreport-intext-parsertime": "[SMW] Vreme raščlanjivanja unutartekstualnih anotacija", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|sekund|sekunda|sekundi}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekund|sekunde|sekundi}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|sekund|sekunda|sekundi}}", "smw-datavalue-allows-value-list-missing-marker": "Sadrž<PERSON>u liste „$1” nedostaju stavke sa * kao označivačem liste.", "smw-datavalue-wikipage-invalid-title": "Unesena vrednost tipa stranice „$1” sadrži nevalidne karaktere ili je nekompletna i stoga može da uzrokuje neočekivane rezultate tokom pretrage ili procesa označavanja.", "smw-datavalue-wikipage-property-invalid-title": "Svojstvo „$1” (kao tip stranice) sa unesenom vrednošću „$2” sadrži nevalidne karaktere ili je nekompletno i stoga može da uzrokuje neočekivane rezultate tokom pretrage ili procesa označavanja.", "smw-clipboard-copy-link": "<PERSON><PERSON>raj vezu na klipbord", "smw-data-lookup": "Dobavljanje podataka...", "smw-no-data-available": "<PERSON><PERSON> pod<PERSON>.", "smw-format-datatable-emptytable": "<PERSON><PERSON> podataka u tabeli", "smw-format-datatable-info": "Prikaz _START_ do _END_ od _TOTAL_ stavki", "smw-format-datatable-infoempty": "Prikaz 0 do 0 od 0 stavki", "smw-format-datatable-infofiltered": "(filtrirano od _MAX_ ukupno stavki)", "smw-format-datatable-infothousands": ".", "smw-format-datatable-lengthmenu": "Prikaži _MENU_ stavki", "smw-format-datatable-loadingrecords": "Učitavanje...", "smw-format-datatable-processing": "Obrada...", "smw-format-datatable-search": "Pretraga:", "smw-format-datatable-zerorecords": "Nema nađenih odgovarajućih podataka", "smw-format-datatable-first": "Prvi", "smw-format-datatable-last": "Poslednji", "smw-format-datatable-next": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-previous": "<PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-sortascending": ": uključite da biste rastuće poređali kolone", "smw-format-datatable-sortdescending": ": uključite da biste opadajuće poređali kolone", "smw-format-datatable-toolbar-export": "Izvoz", "smw-property-reserved-category": "Kategorija", "smw-category": "Kategorija", "smw-filter": "Filter", "smw-help": "<PERSON><PERSON><PERSON>", "smw-processing": "Obrađivanje...", "smw-loading": "Učitavanje...", "smw-fetching": "Dobavljanje...", "smw-preparing": "Pripremanje...", "smw-expand": "Proširi", "smw-collapse": "<PERSON><PERSON><PERSON>", "smw-copy": "<PERSON><PERSON><PERSON>", "smw-copy-clipboard-title": "Kopira sadržaj u ostavu", "smw-jsonview-expand-title": "Širi JSON prikaz", "smw-jsonview-collapse-title": "Skuplja JSON prikaz", "smw-types-title": "Vrsta: $1", "smw-schema-error-title": "Provera {{PLURAL:$1|gre<PERSON><PERSON>|gre<PERSON>ka}}", "smw-schema-error-violation": "[„$1”, „$2”]", "smw-schema-title": "<PERSON><PERSON>", "smw-schema-type": "<PERSON><PERSON>", "smw-schema-tag": "{{PLURAL:$1|Oznaka|Oznake}}", "smw-property-tab-usage": "Upotreba", "smw-property-tab-redirects": "<PERSON>nimi", "smw-property-tab-subproperties": "Podsvojstva", "smw-property-tab-specification": "… više", "smw-concept-tab-list": "Spisak", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-extra": "Dodatno", "smw-ask-tab-code": "<PERSON><PERSON><PERSON>", "smw-pendingtasks-setup-tasks": "<PERSON><PERSON><PERSON>", "smw-es-replication-error-divergent-date-short": "Sledeće informacije o datumu korišćene su radi poređenja:", "smw-facetedsearch-profile-label-default": "Podra<PERSON>mevan profil", "smw-facetedsearch-intro-tab-search": "Pretraga", "smw-facetedsearch-profile-options": "Opcije profila", "smw-facetedsearch-order-options": "Opcije <PERSON>", "smw-facetedsearch-format-options": "Opcije p<PERSON>za", "smw-facetedsearch-format-table": "<PERSON><PERSON><PERSON>", "smw-facetedsearch-input-filter-placeholder": "Filt<PERSON>raj...", "smw-facetedsearch-no-filters": "<PERSON>ema <PERSON>a.", "smw-search-placeholder": "Pretraga...", "smw-listingcontinuesabbrev": "nast.", "smw-showingresults": "{{PLURAL:$1|1=Prikazan je <strong>jedan</strong> rezultat|Prikazan je <strong>$1</strong> rezultat|Prikazana su <strong>$1</strong> rezultata|Prikazano je <strong>$1</strong> rezultata}}; broj prve stavke: <strong>$2</strong>."}