{"@metadata": {"authors": ["BaRaN6161 TURK", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Cobija", "<PERSON><PERSON><PERSON>", "Erdemaslancan", "HakanIST", "Hbseren", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "LisafBia", "McDut<PERSON><PERSON>", "<PERSON><PERSON>", "MuratTheTurkish", "SaldırganSincap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Suvarioglu", "Trockya", "<PERSON><PERSON>"]}, "smw-desc": "Viki<PERSON>zi makineler ''ve'' insanlar için daha eri<PERSON>ilebilir hale getirmesi ([https://www.semantic-mediawiki.org/wiki/Help:User_manual çevrimiçi belge])", "smw-error": "<PERSON><PERSON>", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] yüklendi ve etkinleştirildi, ancak uygun bir [https://www.semantic-mediawiki.org/wiki/Help:Upgrade yükseltme anahtarı eksik].", "smw-upgrade-release": "S<PERSON>r<PERSON><PERSON>", "smw-upgrade-progress": "<PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-progress-explain": "Veri deposunun boyutuna ve mevcut donanıma bağlı olduğundan ve daha büyük vikilerin tamamlanması biraz zaman alabileceğinden, yükseltmenin ne zaman tamamlanacağına ilişkin bir tahmin yapmak zordur.\n\nİlerleme hakkında daha fazla bilgi almak için lütfen yerel hizmetlisine başvurun.", "smw-upgrade-progress-create-tables": "<PERSON><PERSON><PERSON> ve endek<PERSON> (veya güncelleniyor) ...", "smw-upgrade-progress-post-creation": "Posta oluşturma görevleri çalıştırılıyor ...", "smw-upgrade-progress-table-optimization": "Tablo optimizasyonları çalıştırılıyor ...", "smw-upgrade-progress-supplement-jobs": "Ek işler ekleniyor ...", "smw-upgrade-error-title": "Hata » Semantic MediaWiki", "smw-upgrade-error-why-title": "Bu sayfayı neden görüyorum?", "smw-upgrade-error-why-explain": "Semantic MediaWiki'nin dahili veritabanı yapısı değişti ve bazı ayarların tamamen işlevsel olmasını gerektiriyor. Aşağıdakiler dahil çeşitli nedenler olabilir:\n* Ek sabit özellikler (ek tablo kurulumu gerektirir) eklendi\n* Yükseltme, verilere erişmeden önce müdahaleyi zorunlu kılan tablolarda veya endekslerde bazı değişiklikler içerir\n* Depolama veya sorgu motorundaki değişiklikler", "smw-upgrade-error-how-title": "Bu hatayı nasıl düzeltirim?", "smw-upgrade-error-how-explain-admin": "<PERSON><PERSON> hizmetli (veya hizmetli haklarına sahip herhangi bir kişi) ya MediaWiki'nin [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] ya da Semantic MediaWiki'nin [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] bakım betik çalıştırması gerekir.", "smw-upgrade-error-how-explain-links": "<PERSON>ha fazla yardım için a<PERSON><PERSON> sayfalara da bakabilirsiniz:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation Kurulumu] talimatları\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting Sorun giderme] yardım sayfası", "smw-extensionload-error-why-title": "Bu sayfayı neden görüyorum?", "smw-extensionload-error-why-explain": "Uzantı <code>enableSemantics</code> kullanılarak <b>y<PERSON><PERSON>nmedi</b> ve bunun yerine <PERSON> <code>wfLoadExtension('SemanticMediaWiki')</code> kullanımı gibi başka yollarla etkinleştirildi.", "smw-extensionload-error-how-title": "Bu hatayı nasıl düzeltirim?", "smw-extensionload-error-how-explain": "Uzantıyı etkinleştirmek ve ad alanı bildirimleri ve bekleyen yapılandırmalarla ilgili sorunları önlemek için, uzantıyı <code>ExtensionRegistry</code> aracılığıyla yüklemeden önce gerekli değişkenlerin ayarlanmasını sağlayacak <code>enableSemantics</code> kullanılması gerekir.\n\nDaha fazla yardım için lütfen [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics/tr enableSemantics] yardım sayfasına bakın.", "smw-upgrade-maintenance-title": "Bakım » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Bu sayfayı neden görüyorum?", "smw-upgrade-maintenance-note": "Sistem şu anda veri deposu ile birlikte [https://www.semantic-mediawiki.org/ Semantic MediaWiki] uzantısının [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/tr yükseltmesi] geçiriyor ve sizden sabrınızı istemek ve vikinin tekrar erişilebilir hale getirilmesinden önce bakımın devam etmesini sağlamak istiyoruz.", "smw-upgrade-maintenance-explain": "Bu uzantı, bakım görevlerini <code>update.php</code> işleminin ardına atarak gecikmeyi ve etki süresini azaltmaya çalışır. Fakat veritabanıyla alakalı bazı değişik<PERSON>lerin, veri uyumsuzluğu yaratmaması amacıyla öncelikli olarak yapılması gerekir. Şu değişikliler dahil olabilir:\n* Yeni alan eklemek veya var olanı değiştirmek gibi tablo değişimleri\n* Tablo indekslerinin değiştirilmesi veya eklenmesi\n* Tablo iyileştirmelirini çalıştırmak (etkin olduğu zaman)", "smw-semantics-not-enabled": "Bu viki için Semantic MediaWiki işlevselliği etkinleştirilmedi.", "smw_viewasrdf": "RDF beslemesi", "smw_finallistconjunct": " ve", "smw-factbox-head": "... ''$1'' hakkında daha fazla", "smw-factbox-facts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-factbox-facts-help": "Bir kullanıcı tarafından oluşturulan ifadeleri ve gerçekleri gösterir.", "smw-factbox-attachments": "Ekler", "smw-factbox-attachments-value-unknown": "Bilinmiyor", "smw-factbox-attachments-is-local": "<PERSON><PERSON>", "smw-factbox-attachments-help": "Kullanılabilir ekleri gösterir", "smw-factbox-facts-derived": "Türetilmiş gerçekler", "smw-factbox-facts-derived-help": "Kurallardan veya diğer akıl yürütme tekniklerinin yardımıyla elde edilen gerçekleri gösterir.", "smw_isspecprop": "Bu <PERSON><PERSON><PERSON>, bu vikideki özel bir özelliktir.", "smw-concept-cache-header": "Önbellek kullanımı", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count <PERSON><PERSON><PERSON>] {{PLURAL:$1|'''bir''' varlık |'''$1''' varlık}} içeriyor ($2).", "smw-concept-no-cache": "Önbellek yok.", "smw_concept_description": "\"$1\" kavramının tanımı", "smw_no_concept_namespace": "Kavramlar yalnızca kavram sayfalarında tanımlanabilir: ad alanı", "smw_multiple_concepts": "<PERSON><PERSON><PERSON> kavram say<PERSON>ı sadece birer kavram tanımına sahip olabilir.", "smw_concept_cache_miss": "\"$1\" kav<PERSON><PERSON> <PERSON>u anda <PERSON>, çünkü viki yapılandırması çevrimdışı hesaplanmasını gerektiriyor.\n<PERSON>run bir süre sonra ortadan kalkmazsa, site hizmetlisinden bu kavramı kullanılabilir yapmasını isteyin.", "smw_noinvannot": "Değerler ters özelliklere atanamaz.", "version-semantic": "Semantic uzantıları", "smw_baduri": "\"$1\" form URL'sine izin verilmiyor.", "smw_printername_count": "Sonuçları say", "smw_printername_csv": "CSV aktarımı", "smw_printername_dsv": "DSV aktarımı", "smw_printername_debug": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON> (uzmanlar için)", "smw_printername_embedded": "<PERSON><PERSON>", "smw_printername_json": "JSON aktarımı", "smw_printername_list": "Liste", "smw_printername_plainlist": "<PERSON><PERSON><PERSON> <PERSON>e", "smw_printername_ol": "Numaralı liste", "smw_printername_ul": "Madde işaretli liste", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "Geniş tablo", "smw_printername_template": "Şablon", "smw_printername_templatefile": "Şablon dosyası", "smw_printername_rdf": "RDF aktarımı", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "metin", "smw-paramdesc-limit": "Döndürülecek en fazla sonuç sayısı", "smw-paramdesc-offset": "<PERSON><PERSON> son<PERSON>un of<PERSON>", "smw-paramdesc-headers": "Başlıkları/özellik adlarını göster", "smw-paramdesc-mainlabel": "<PERSON><PERSON><PERSON> ismine verilecek etiket", "smw-paramdesc-link": "Değerleri bağlantı olarak göster", "smw-paramdesc-intro": "Sorgu sonuçlarından önce görüntülenecek metin, varsa", "smw-paramdesc-outro": "<PERSON><PERSON>u sonuçlarından sonra görüntülenecek metin, varsa", "smw-paramdesc-default": "<PERSON><PERSON><PERSON> son<PERSON>u bulu<PERSON><PERSON><PERSON><PERSON> gö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>k metin", "smw-paramdesc-sep": "Sonuçlar arasındaki ayırıcı", "smw-paramdesc-propsep": "Bir sonuç girişinin özellikleri arasındaki ayırıcı", "smw-paramdesc-valuesep": "Bir sonucun bir özelliği için değerler arasındaki ayırıcı", "smw-paramdesc-showsep": "CSV dosyasının üstünde ayırıcıyı göster (\"sep=<değer>\")", "smw-paramdesc-distribution": "<PERSON><PERSON><PERSON> değ<PERSON>leri gör<PERSON><PERSON><PERSON><PERSON><PERSON> yer<PERSON>, bun<PERSON><PERSON>n o<PERSON>larını sayın ve bunları gösterin.", "smw-paramdesc-distributionsort": "<PERSON><PERSON><PERSON><PERSON>mın<PERSON> olay say<PERSON> göre sı<PERSON>ın.", "smw-paramdesc-distributionlimit": "<PERSON><PERSON><PERSON> da<PERSON>ılımını yalnızca bazı değerlerin sayısıyla sınırlayın.", "smw-paramdesc-aggregation": "Toplamanın ne ile ilgili olması gerektiğini belirtin", "smw-paramdesc-template": "Çıkışların görüntüleneceği bir şablonun adı", "smw-paramdesc-columns": "Sonuçların görüntüleneceği sütun sayısı", "smw-paramdesc-userparam": "Şablon <PERSON>anılıyo<PERSON>, her şablon çağr<PERSON><PERSON><PERSON>na iletilen bir <PERSON>er", "smw-paramdesc-class": "Liste için ayarlanacak ek bir CSS sınıfı", "smw-paramdesc-introtemplate": "<PERSON><PERSON><PERSON>, sorgu sonuçlarından önce görüntülenecek şablonun adı", "smw-paramdesc-outrotemplate": "<PERSON><PERSON><PERSON>, sorgu sonuçlarından sonra görüntülenecek bir şablonun adı", "smw-paramdesc-embedformat": "Başlıkları tanımlamak için kullanılan HTML etiketi", "smw-paramdesc-embedonly": "Başlık gösterme", "smw-paramdesc-table-class": "Tablo için ayarlanacak ek bir CSS sınıfı", "smw-paramdesc-table-transpose": "Ta<PERSON><PERSON> başlıklarını dikey ve sonuçları yatay olarak gö<PERSON>üle", "smw-paramdesc-rdfsyntax": "Kullanılacak RDF sözdizimi", "smw-paramdesc-csv-sep": "Bir sütun ayırıcı belirtir", "smw-paramdesc-csv-valuesep": "Bir değer ayırıcı belirtir", "smw-paramdesc-csv-merge": "Satırları ve sütun değerlerini aynı konu tanımlayıcıyla (ilk sütun olarak da bilinir) birleştirme", "smw-paramdesc-csv-bom": "Çıkış dosyasının üstüne bir malzeme listesi (sinyal endianitesine karakter) ekleyin", "smw-paramdesc-dsv-separator": "Kullanılacak ayırıcı", "smw-paramdesc-dsv-filename": "DSV dosyasının adı", "smw-paramdesc-filename": "Çıkış dosyasının adı", "smw-smwdoc-description": "Varsayılan değer ve açıklamalarla birlikte, belirtilen sonuç biçimi için kullanılabilecek tüm parametrelerin bir tablosunu gösterir.", "smw-smwdoc-default-no-parameter-list": "<PERSON><PERSON> <PERSON><PERSON>, formata özgü parametreler sağlamaz.", "smw-smwdoc-par-format": "İçin parametre belgelerini görüntülemek için sonuç biçim<PERSON>.", "smw-smwdoc-par-parameters": "Hangi parametrelerin gösterileceği. biçime eklenenler için \"specific\", tüm biçimlerde bulunanlar için \"base\" ve her ikisi için de \"all\".", "smw-paramdesc-sort": "<PERSON><PERSON><PERSON>u sıralama ölçütü", "smw-paramdesc-order": "<PERSON><PERSON><PERSON> sı<PERSON>ı<PERSON>ın sırası", "smw-paramdesc-searchlabel": "<PERSON><PERSON><PERSON><PERSON> devam etmesi için metin", "smw-paramdesc-named_args": "Şablona iletilen bağımsız değişkenleri adlandırın", "smw-paramdesc-template-arguments": "Adlandırılmış bağ<PERSON>ms<PERSON>z değişkenlerin şablona nasıl aktarılacağını ayarlar", "smw-paramdesc-import-annotation": "Bir ekin ayrıştırılması sırasında ek açıklamalı veriler kopyalanacaktır", "smw-paramdesc-export": "<PERSON>kt<PERSON><PERSON><PERSON> seçeneği", "smw-paramdesc-prettyprint": "Ek girintiler ve yeni satırlar görüntüleyen güzel baskı çıkışı", "smw-paramdesc-json-unescape": "Çıkış karakteri olmayan eğik çizgiler ve çok baytlı Unicode karakterler içeren çıkış", "smw-paramdesc-json-type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ü<PERSON>", "smw-paramdesc-source": "Alternatif sorgu kaynağı", "smw-paramdesc-jsonsyntax": "Kullanılacak JSON sözdizimi", "smw-printername-feed": "RSS ve Atom beslemesi", "smw-paramdesc-feedtype": "<PERSON><PERSON><PERSON> tü<PERSON>", "smw-paramdesc-feedtitle": "Beslemenin başlığı olarak kullanılacak metin", "smw-paramdesc-feeddescription": "Beslemenin açıklaması olarak kullanılacak metin", "smw-paramdesc-feedpagecontent": "Besleme ile görüntülenecek sayfa içeriği", "smw-label-feed-description": "$1 $2 beslemesi.", "smw-paramdesc-mimetype": "Çıkış dosyası için ortam türü (MIME türü)", "smw_iq_disabled": "Bu viki için anlamsal sorgular devre dışı bırakıldı.", "smw_iq_moreresults": "... ek sonuçlar", "smw_parseerror": "Verilen değer anlaşılmadı.", "smw_notitle": "\"$1\" bu vikide sayfa adı olarak kullanılamaz.", "smw_noproperty": "\"$1\" bu vikide özellik adı olarak kullanılamaz.", "smw_wrong_namespace": "Burada yalnızca \"$1\" ad alanındaki sayfalara izin verilir.", "smw_manytypes": "Özellik için birden fazla tür tanı<PERSON>landı.", "smw_emptystring": "Boş dizgiler kabul edilmemektedir.", "smw_notinenum": "\"$1\", \"$3\" özelliği için [[Property:Allows value|izin verilen değerler]] listesinde ($2) değil.", "smw-datavalue-constraint-error-allows-value-list": "\"$1\", \"$3\" özelliği için [[Property:Allows value|izin verilen değerler]] listesinde ($2) değil.", "smw-datavalue-constraint-error-allows-value-range": "\"$1\", \"$3\" özelliği için [[Property:Allows value|değere izin verilen]] kısıtlaması tarafından belirtilen \"$2\" aralığı içinde değil.", "smw-constraint-error": "Kısıtlama sorunu", "smw-constraint-error-suggestions": "Lütfen tüm kısıtlama gereksinimlerinin karşılandığından emin olmak için listelenen ihlalleri ve özellikleri ek açıklama değerleriyle birlikte kontrol edin.", "smw-constraint-error-limit": "Liste maksimum $1 ihlal içerecek.", "smw_noboolean": "\"$1\", bir <PERSON><PERSON> (true/false) de<PERSON><PERSON> olarak tanınmıyor.", "smw_true_words": "true,t,yes,y", "smw_false_words": "false,f,no,n", "smw_nofloat": "\"$1\" bir sayı de<PERSON>.", "smw_infinite": "\"$1\" kadar b<PERSON>y<PERSON>k sayılar desteklenmez.", "smw_unitnotallowed": "\"$1\", bu özellik için geçerli bir ölçüm birimi olarak ilan edilmedi.", "smw_nounitsdeclared": "Bu özellik için hiçbir ölçü birimi bildirilmedi.", "smw_novalues": "<PERSON><PERSON><PERSON>.", "smw_nodatetime": "\"$1\" ta<PERSON><PERSON>.", "smw_toomanyclosing": "Sorguda çok fazla \"$1\" kullanımı mevcut.", "smw_noclosingbrackets": "Sorgunuzda bazı \"<nowiki>[[</nowiki>\" kullanımı, eşleşen \"]]\" tarafından kapatılmadı.", "smw_misplacedsymbol": "\"$1\" sembo<PERSON><PERSON>, ya<PERSON><PERSON><PERSON> olmadığı bir noktada kullanıldı.", "smw_unexpectedpart": "Sorgunun \"$1\" kısmı anlaşılmadı.\nSonuçlar beklendiği gibi olmayabilir.", "smw_emptysubquery": "Bazı alt sorguların geçerli bir koşulu yoktur.", "smw_misplacedsubquery": "Alt sorgulara izin verilmeyen bir yerde bazı alt sorgu kullanılmıştır.", "smw_valuesubquery": "\"$1\" özellik değerleri için alt sorgular desteklenmiyor.", "smw_badqueryatom": "So<PERSON><PERSON><PERSON> bir kısmı \"<nowiki>[[...]]</nowiki>\" anlaşılmadı.", "smw_propvalueproblem": "\"$1\" özelliğin değeri anlaşılmadı.", "smw_noqueryfeature": "Bu vikide bazı sorgu özellikleri desteklenmedi ve sorgunun bir kısmı bırakıldı ($1).", "smw_noconjunctions": "So<PERSON><PERSON><PERSON>i bağlaçlar bu vikide desteklenmiyor ve sorgunun bir kısmı kaldırıldı ($1).", "smw_nodisjunctions": "Sorgulardaki kesintiler bu vikide desteklenmiyor ve sorgunun bir kısmı düşürüldü ($1).", "smw_querytoolarge": "Aşağıdaki {{PLURAL:$2|sorgu koşulu|$2 sorgu koşulu}}, bu vikinin sorgu boyutu veya derinliği üzerindeki kısıtlamaları nedeniyle dikkate alınamadı: <code>$1</code>.", "smw_notemplategiven": "Bu sorgu biçiminin çalışması için \"template\" parametresi için bir değer girin.", "smw_db_sparqlqueryproblem": "Sorgu sonucu SPARQL veritabanından alınamadı. Bu hata geçici olabilir veya veritabanı yazılımında bir hata olduğunu gösterebilir.", "smw_db_sparqlqueryincomplete": "Sorguyu cevaplamak çok zor oldu ve iptal edildi. Bazı sonuçlar eksik olabilir. Mümkünse, bunun yerine daha basit bir sorgu kullanmayı deneyin.", "smw_type_header": "\"$1\" türünün özellikleri", "smw_typearticlecount": "Bu türü kullanarak $1 özellik gösteriliyor.", "smw_attribute_header": "\"$1\" özelliğini kullanan sayfalar", "smw_attributearticlecount": "Bu özelliği kullanarak $1 sayfa gösteriliyor.", "smw-propertylist-subproperty-header": "Alt özellikleri", "smw-propertylist-redirect-header": "Eşanlamlılar", "smw-propertylist-error-header": "Yanlış atamaları olan sayfalar", "smw-propertylist-count": "$1 ile ilgili varlık gösteriliyor.", "smw-propertylist-count-with-restricted-note": "$1 ile ilgili varlık gösteriliyor (daha fazlası var ancak ekran \"$2\" ile sınırlıdır).", "smw-propertylist-count-more-available": "$1 ile ilgili varlık (daha fazlası mevcut) gösteriliyor.", "specialpages-group-smw_group-maintenance": "Bakım", "specialpages-group-smw_group-properties-concepts-types": "<PERSON><PERSON><PERSON><PERSON>, kav<PERSON><PERSON> ve türler", "specialpages-group-smw_group-search": "Göz at ve ara", "exportrdf": "Sayfaları RDF'ye aktar", "smw_exportrdf_docu": "<PERSON><PERSON> say<PERSON>, RDF formatındaki bir sayfadan veri almanıza izin verir.\nSayfaları dışa aktarmak için, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> metin kut<PERSON>, satır başına bir başlık olan başlıkları girin.", "smw_exportrdf_recursive": "İlgili tüm sayfaları tekrar tekrar dışa aktarın.\nSonucun büyük olabileceğini unutmayın!", "smw_exportrdf_backlinks": "<PERSON><PERSON><PERSON><PERSON>, dışa aktarılan sayfalara başvuran tüm sayfaları dışa aktarın.\nGöz atılabilir RDF oluşturur.", "smw_exportrdf_lastdate": "<PERSON><PERSON><PERSON> olarak belirtilen noktadan bu yana de<PERSON><PERSON>rilmemiş sayfaları dışa aktarmayın.", "smw_exportrdf_submit": "Dışa aktar", "uriresolver": "URIResolver", "properties": "<PERSON><PERSON><PERSON><PERSON>", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "Vikide aşağıdaki özellikler kullanılıyor.", "smw_property_template": "$1 tipi $2 ($3 {{PLURAL:$1|kullanım|kullanım}})", "smw_propertylackspage": "Tüm özellikler bir sayfa tarafından açıklanmalıdır!", "smw_propertylackstype": "Bu özellik için tür belirtilmedi (şimdilik $1 olduğu varsayılarak).", "smw_propertyhardlyused": "<PERSON><PERSON> <PERSON><PERSON>, viki dahilinde nadiren kullanılmaktadır!", "smw-property-name-invalid": "$1 özelliği kullanılamaz (geçersiz özellik adı).", "smw-property-name-reserved": "\"$1\" ayrılmış ad olarak listelendi ve özellik olarak kullanılmamalıdır. Aşağıdaki [https://www.semantic-mediawiki.org/wiki/Help:Property_naming yardım sayfası], bu adın neden ayrıldığına ilişkin bilgiler içerebilir.", "smw-sp-property-searchform": "Şunları içeren görüntüleme özellikleri:", "smw-sp-property-searchform-inputinfo": "<PERSON><PERSON><PERSON>ük/küçük harfe duyarlıdır ve filtreleme için kullanıldığında, yaln<PERSON>zca koşulla eşleşen özellikler görüntülenir.", "smw-special-property-searchform": "Şunları içeren görüntüleme özellikleri:", "smw-special-property-searchform-inputinfo": "<PERSON><PERSON><PERSON>ük/küçük harfe duyarlıdır ve filtreleme için kullanıldığında, yaln<PERSON>zca koşulla eşleşen özellikler görüntülenir.", "smw-special-property-searchform-options": "Seçenekler", "smw-special-wantedproperties-filter-label": "Filtre:", "smw-special-wantedproperties-filter-none": "Hiç<PERSON>i", "smw-special-wantedproperties-filter-unapproved": "Onaylanmamış", "smw-special-wantedproperties-filter-unapproved-desc": "Yet<PERSON><PERSON> mod ile bağlantılı olarak kullanılan filtre seçeneği.", "concepts": "<PERSON><PERSON><PERSON><PERSON>", "smw-special-concept-docu": "Bir [https://www.semantic-mediawiki.org/wiki/Help:Concepts kavramı] \"dinamik kategori\" o<PERSON><PERSON>, yani man<PERSON>el olarak oluşturulmayan, ancak Semantic MediaWiki tarafından hesaplanan sayfaların bir toplamı olarak görülebilir.", "smw-special-concept-header": "<PERSON><PERSON><PERSON>", "smw-special-concept-count": "Aşağıdaki {{PLURAL:$1|kavram|$1 kavram}} listeleniyor.", "smw-special-concept-empty": "<PERSON><PERSON><PERSON> kavram bulunamadı.", "unusedproperties": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-unusedproperties-docu": "<PERSON><PERSON> sayfada, başka hiçbir sayfa kullanılmadığı halde beyan edilen [https://www.semantic-mediawiki.org/wiki/Unused_properties kullanılmayan özellikler] listelenmektedir. Farklılaştırılmış görünüm için [[Special:Properties|tümünü]] veya [[Special:WantedProperties|istenen özellikler]] özel sayfalarına bakın.", "smw-unusedproperty-template": "$1 türü $2", "wantedproperties": "<PERSON><PERSON><PERSON>", "smw-wantedproperties-docu": "<PERSON><PERSON> say<PERSON><PERSON>, vikide kullanılan ancak bunları açıklayan bir sayfası bulunmayan [https://www.semantic-mediawiki.org/wiki/Help:Special:WantedProperties/tr istenen özellikler] listelenir. Farklılaştırılmış bir görünüm için [[Special:Properties|tümünü]] veya [[Special:UnusedProperties|kullanılmayan özellikler]] özel sayfalarına bakın.", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|kullanım|kullanım}})", "smw-special-wantedproperties-docu": "<PERSON><PERSON> say<PERSON>da, vikide kullanılan ancak bunları açıklayan bir sayfası bulunmayan [https://www.semantic-mediawiki.org/wiki/Help:Special:WantedProperties/tr aranan özellikler] listelenir, ancak bunları açıklayan bir sayfanız yok. Farklılaştırılmış bir görünüm için [[Special:Properties|tümünü]] veya [[Special:UnusedProperties|kullanılmayan özellikler]] özel sayfalarına bakın.", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|kullanım|kullanım}})", "smw_purge": "<PERSON><PERSON><PERSON>", "smw-purge-update-dependencies": "Semantic MediaWiki, güncelleştirme gerektiren bazı eski bağımlılıklar nedeniyle geçerli sayfayı temizliyor.", "smw-purge-failed": "Semantic MediaWiki sayfayı temizlemeye çalıştı ancak başarısız oldu", "types": "<PERSON><PERSON><PERSON><PERSON>", "smw_types_docu": "[https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes Kullanılabilir veri türleri] ve her biri [https://www.semantic-mediawiki.org/wiki/Help:Datatype tür] ile atanmış bir özellik için kalıtsal olan depolama ve görüntüleme özellikleri açısından bir değeri tanımlayan özellikler.", "smw-special-types-no-such-type": "\"$1\" bilinmiyor veya geçerli veri tür<PERSON> olarak be<PERSON>ilmedi.", "smw-statistics": "Semantik istatistikleri", "smw-statistics-cached": "Semantik istatistikleri (önbelleğe alınmış)", "smw-statistics-entities-total": "Varl<PERSON>klar (toplam)", "smw-statistics-entities-total-info": "Varlıkların tahmini satır sayısı. <PERSON><PERSON><PERSON><PERSON>, kav<PERSON><PERSON> veya kimlik ataması gerektiren herhangi bir kayıtlı nesne temsilini içerir.", "smw-statistics-property-instance": "Özellik {{PLURAL:$1|değeri|değeri}} (toplam)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Özellik|Özellik}}]] (toplam)", "smw-statistics-property-total-info": "Kayıtlı özelliklerin toplamı.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Özellik|Özellik}} (toplam)", "smw-statistics-property-used": "{{PLURAL:$1|Özellik|Özellik}} (en az bir değerle kullanılır)", "smw-statistics-property-page": "{{PLURAL:$1|Özellik|Özellik}} (bir <PERSON><PERSON><PERSON> ka<PERSON>)", "smw-statistics-property-page-info": "Özel bir sayfası ve açıklaması olan özellikleri sayın.", "smw-statistics-property-type": "{{PLURAL:$1|Özellik|Özellik}} (bir veri tipine atanmış)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Sorgu|Sorgu}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Sorgu|Sorgu}}]] (gömülü, toplam)", "smw-statistics-query-format": "<code>$1</code> biçimi", "smw-statistics-query-size": "<PERSON><PERSON><PERSON> boy<PERSON>u", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Ka<PERSON>ram|Kavram}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Ka<PERSON><PERSON>|<PERSON><PERSON><PERSON>}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Alt nesne|Alt nesne}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Alt nesne|Alt nesne}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Veri türü|Veri türü}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Özellik değeri|Özellik değeri}} ([[Special:ProcessingErrorList|{{PLURAL:$1|uygunsuz ek açıklama|uygunsuz ek açıklama}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Özellik değeri|Özellik değeri}} ({{PLURAL:$1|uygunsuz ek açıklama|uygunsuz ek açıklama}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities Eski {{PLURAL:$1|varlık|varlık}}]", "smw-statistics-delete-count-info": "Çıkarılmak üzere işaretlenmiş ve verilen bakım betikleri kullanılarak düzenli olarak imha edilmesi gereken kuruluşlar.", "smw_uri_doc": "URI çözümleyici [$1 W3C TAG finding on httpRange-14] uygular.\nİnsanların web sitelerine dönüşmemesine özen gösterir.", "ask": "<PERSON><PERSON><PERSON> arama", "smw-ask-help": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>, <code>#ask</code> sözdiziminin nasıl kullanılacağını açıklamaya yardımcı olacak bazı bağlantılar içerir.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Say<PERSON> seçimi] seçilen sayfaların nasıl oluşturulacağını ve koşulların nasıl oluşturulacağını açıklar\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Arama operatörleri], aralık ve joker karakter sorguları da dahil olmak üzere mevcut arama operatörlerini listeler\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information B<PERSON><PERSON> görüntüleme], çıktı ifadelerinin ve biçimlendirme seçeneklerinin kullanımını ana hatlarıyla belirtir", "smw_ask_sortby": "Sütuna göre sırala (isteğe bağlı)", "smw_ask_ascorder": "<PERSON><PERSON>", "smw_ask_descorder": "<PERSON><PERSON><PERSON>", "smw-ask-order-rand": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Sonuçları bul", "smw_ask_editquery": "<PERSON><PERSON><PERSON><PERSON>", "smw_add_sortcondition": "[<PERSON><PERSON>ralama koşulu ekle]", "smw-ask-sort-add-action": "Sıralama koşulu ekle", "smw_ask_hidequery": "<PERSON><PERSON><PERSON><PERSON> gizle (kavram görünüm)", "smw_ask_help": "Sorgulama yardımı", "smw_ask_queryhead": "<PERSON><PERSON>", "smw_ask_printhead": "Çıkış seçimi", "smw_ask_printdesc": "(satır başına bir özellik adı girin)", "smw_ask_format_as": "<PERSON><PERSON><PERSON><PERSON>:", "smw_ask_defaultformat": "varsay<PERSON>lan", "smw_ask_otheroptions": "<PERSON><PERSON><PERSON>", "smw-ask-otheroptions-info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>k<PERSON>ş ifadelerini değiştiren seçenekler içerir. Parametre açıklamaları, üzerine getirilerek görüntülenebilir.", "smw-ask-otheroptions-collapsed-info": "Mevcut tüm seçenekleri görüntülemek için lütfen artı simgesini kullanın", "smw_ask_show_embed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kodunu g<PERSON>", "smw_ask_hide_embed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kodunu gizle", "smw_ask_embed_instr": "Bu sorguyu satır içi bir viki sayfasına gömmek için aşağıdaki kodu kullanın.", "smw-ask-delete": "Kaldır", "smw-ask-sorting": "Sıralama", "smw-ask-options": "Seçenekler", "smw-ask-options-sort": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-format-options": "<PERSON><PERSON><PERSON><PERSON> ve seçenekler", "smw-ask-parameters": "Parametreler", "smw-ask-search": "Ara", "smw-ask-debug": "Ayıkla", "smw-ask-debug-desc": "Sorgu hata ayıklama bilgileri oluşturur", "smw-ask-no-cache": "<PERSON><PERSON><PERSON> önbelleğini devre dışı bırak", "smw-ask-no-cache-desc": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "smw-ask-result": "<PERSON><PERSON><PERSON>", "smw-ask-empty": "<PERSON><PERSON><PERSON> g<PERSON>i temizle", "smw-ask-download-link-desc": "Sorgulanan sonuçları $1 biçiminde indir", "smw-ask-format": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-format-selection-help": "Seçilen biçimle ilgili yardım: $1", "smw-ask-condition-change-info": "Koşul değiştirildi ve arama motoru, yeni gereksinimlerle eşleşen sonuçlar üretmek için sorguyu yeniden çalıştırmayı gerektiriyor.", "smw-ask-input-assistance": "<PERSON><PERSON><PERSON>ımı", "smw-ask-condition-input-assistance": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sı<PERSON><PERSON> ve koşul alanı için [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Giriş yardımı] sağlanır. Koşul alanı, aşağıdaki öneklerden birini kullanmayı gerektirir:", "smw-ask-condition-input-assistance-property": "Özellik önerilerini almak için <code>p:</code> (ör. <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "kategori önerilerini getirmek için <code>c:</code>", "smw-ask-condition-input-assistance-concept": "ka<PERSON><PERSON>nerilerini getirmek için <code>con:</code>", "smw-ask-format-change-info": "<PERSON><PERSON>ç<PERSON>rildi ve yeni parametreleri ve görselleştirme seçeneklerini eşleştirmek için sorguyu yeniden yürütmek gerekiyor.", "smw-ask-format-export-info": "<PERSON><PERSON><PERSON><PERSON> biç<PERSON>, <PERSON><PERSON><PERSON><PERSON> temsili olmayan bir dışa aktarma biçimidir, bu ne<PERSON>le sonuçlar yalnızca karşıdan yükleme olarak sağlanır.", "smw-ask-query-search-info": "<code><nowiki>$1</nowiki></code> sorgusu {{PLURAL:$3|1=<code>$2</code> (önbellekten)|<code>$2</code>}} tarafından $4 saniye cevaplandı.", "smw-ask-extra-query-log": "<PERSON><PERSON><PERSON>", "smw-ask-extra-other": "<PERSON><PERSON><PERSON>", "searchbyproperty": "Özelliğe göre ara", "processingerrorlist": "İşleme hataları listesi", "constrainterrorlist": "Kısıtlama hata listesi", "propertylabelsimilarity": "Özellik etiketine ben<PERSON> raporu", "missingredirectannotations": "Eksik yönlendirme ek açıklamaları", "smw-processingerrorlist-intro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> listede, [https://www.semantic-mediawiki.org/ Semantic MediaWiki] ile bağlantılı olarak ortaya çıkan [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors/tr işleme hataları] hakkında bir genel bakış sunulmaktadır. Bu listenin düzenli olarak izlenmesi ve geçersiz değer ek açıklamalarının düzeltilmesi önerilir.", "smw-processingerrorlist-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Processing_errors/tr", "smw-constrainterrorlist-intro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> liste, [https://www.semantic-mediawiki.org/ Semantic MediaWiki] ile bağlantılı olarak görünen [https://www.semantic-mediawiki.org/wiki/Help:Constraint_errors/tr kısıtlama hataları] hakkında genel bir bakış sunar. Bu listeyi düzenli aralıklarla izlemeniz ve geçersiz değer açıklamalarını düzeltmeniz önerilir.", "smw-constrainterrorlist-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Constraint_error/tr", "smw-missingredirects-intro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Semantic MediaWiki'de [https://www.semantic-mediawiki.org/wiki/Redirects yönlendirmesi] ek açıklamaları eksik olan sayfaları listeler (MediaWiki'de depolanan bilgilerle karşılaştırılarak) ve bu ek açıklamaları manüel olarak geri yüklemek için [https://www.semantic-mediawiki.org/wiki/Help:Purge temizleme] sayfasına gidin veya <code>rebuildData.php</code> bakım betiğini çalıştırın (<code>--redirects</code> seçeneğiyle).", "smw-missingredirects-list": "Ek açıklamaları olmayan sayfalar", "smw-missingredirects-list-intro": "Yönlendirme ek açıklamaları eksik olan $1 sayfa gösteriliyor.", "smw-missingredirects-noresult": "Eksik yönlendirme ek açıklaması bulunamadı.", "smw_sbv_docu": "Belirtilen özellik ve değere sahip tüm sayfaları ara.", "smw_sbv_novalue": "Özellik için geçerli bir değer girin ya da \"$1\" için tüm özellik değerlerini görün.", "smw_sbv_displayresultfuzzy": "\"$1\" özelliğine sahip \"$2\" özelliğine sahip tüm sayfaların listesi.\nÇok az sonuç olduğu için yakındaki değerler de görüntülenir.", "smw_sbv_property": "Özellik:", "smw_sbv_value": "<PERSON><PERSON><PERSON>:", "smw_sbv_submit": "Sonuçları bul", "browse": "Vikiye göz at", "smw_browselink": "Özelliklere göz at", "smw_browse_article": "<PERSON><PERSON>z atmaya başlamak için sayfanın adını girin.", "smw_browse_go": "Git", "smw_browse_show_incoming": "Gelen özellikleri göster", "smw_browse_hide_incoming": "Yeni özel<PERSON>leri gizle", "smw_browse_no_outgoing": "Bu sayfa özelliğe sahip değil.", "smw_browse_no_incoming": "Bu sayfaya bağlantı veren özellik yok.", "smw-browse-from-backend": "<PERSON><PERSON> anda arka uçtan bilgi alınıyor.", "smw-browse-intro": "Bu sayfa bir konu ya da varlık vakası hakkında ayrıntılar sağlar, lütfen denetlenecek nesnenin adını girin.", "smw-browse-invalid-subject": "Konu doğrulaması \"$1\" hatasıyla döndü.", "smw-browse-api-subject-serialization-invalid": "Konu geçersiz bir serileştirme biçimine sa<PERSON>.", "smw-browse-js-disabled": "JavaScript'in devre dışı olduğundan veya kullanılamadığından şüpheleniliyor ve bunun desteklendiği bir tarayıcı kullanmanızı öneririz. Diğer seçenekler [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi/tr <code>$smwgBrowseByApi</code>] yapılandırma parametresi sayfasında ele alınmıştır.", "smw-browse-show-group": "Grupları göster", "smw-browse-hide-group": "Grupları gizle", "smw-noscript": "Bu sayfa veya işlem JavaScript'in çalışmasını gerektiriyor, lütfen tarayıcınızda JavaScript'i etkinleştirin veya işlevselliğin sunulabilmesi ve istendiği gibi sağlanabilmesi için bunun desteklendiği bir tarayıcı kullanın. Daha fazla yardım için lütfen [https://www.semantic-mediawiki.org/wiki/Help:Noscript/tr noscript] yardım sayfasına göz atın.", "smw_inverse_label_default": "$1", "smw_inverse_label_property": "Ters özellik etiketi", "pageproperty": "<PERSON><PERSON> ö<PERSON>ği a<PERSON>", "pendingtasklist": "Bekleyen görevlerin listesi", "smw_pp_docu": "Bir sayfa ve özellik girin ya da yalnızca atanan tüm değerleri almak için bir özellik girin.", "smw_pp_from": "Sayfadan:", "smw_pp_type": "Özellik:", "smw_pp_submit": "Sonuçları bul", "smw-prev": "önceki {{PLURAL:$1|$1}}", "smw-next": "sonraki {{PLURAL:$1|$1}}", "smw_result_prev": "<PERSON><PERSON><PERSON>", "smw_result_next": "<PERSON><PERSON><PERSON>", "smw_result_results": "Sonuçlar", "smw_result_noresults": "<PERSON><PERSON>ç yok.", "smwadmin": "Semantic MediaWiki Gösterge Tablosu", "smw-admin-statistics-job-title": "İş istatistikleri", "smw-admin-statistics-job-docu": "<PERSON>ş istatistikleri, he<PERSON><PERSON><PERSON> yü<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i<PERSON> planlanmış Semantic MediaWiki işleri hakkında bilgi görüntüler. İş sayısı biraz yanlış olabilir veya başarısız denemeler içerebilir, daha fazla bilgi için lütfen [https://www.mediawiki.org/wiki/Manual:Job_queue/tr kılavuzuna] bakın.", "smw-admin-statistics-querycache-title": "<PERSON><PERSON><PERSON>", "smw-admin-statistics-querycache-disabled": "Bu vikide [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] etkinleştirilmedi ve bu nedenle istatistik mevcut değil.", "smw-admin-statistics-querycache-legend": "Önbellek istatistikleri, aşağıdakileri içeren geçici kümülatif ve türetilmiş verileri içerecektir:\n* ulaşılamayan yanıtlarla önbellekten toplam veri alma girişimi olarak \"misses\", <PERSON><PERSON><PERSON><PERSON> depo (DB, üçlü depolama vb.)\n* önbellek tahliye işlemlerinin toplam miktarı olarak \"deletes\" (temizleme veya sorgu bağımlılığı yoluyla)\n* \"hits\", katıştırılmış (viki sayfasından çağrılan sorgular) veya katıştırılmamış (etkinleştirilmişse, Özel: Sor veya API gibi sayfalar tarafından talep edilen) kaynaklardan önbellek alma miktarını içerir\n* \"medianRetrievalResponseTime\", toplama işleminin süresi boyunca önbelleğe alınmış ve önbelleğe alınmamış alma istekleri için ortalama yanıt süresinin (saniye cinsinden) bir oryantasyon değeridir.\n* \"noCache\", sonuçları önbellekten almak için deneme isteği olmadığını (limit=0 sorgu, 'no-cache' seçeneği vb.) belirtir", "smw-admin-statistics-section-explain": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> hizmetliler için ek istatistikler sağlar.", "smw-admin-statistics-semanticdata-overview": "<PERSON><PERSON>", "smw-admin-permission-missing": "Bu sayfaya eri<PERSON>im eksik izinler nedeniyle en<PERSON>, lütfen gerekli ayarlarla ilgili ayrıntılar için [https://www.semantic-mediawiki.org/wiki/Help:Permissions izinler] yardım sayfasına bakın.", "smw-admin-setupsuccess": "Depolama motoru kuruldu.", "smw_smwadmin_return": "$1 sayfasına geri dön", "smw_smwadmin_updatestarted": "Anlamsal verileri yenilemek için yeni bir güncelleme işlemi başlatıldı.\nSaklanan tüm veriler gerektiğinde yeniden oluşturulur veya onarılır.\nGüncellemenin ilerlemesini bu özel sayfada takip edebilirsiniz.", "smw_smwadmin_updatenotstarted": "Çalışan bir güncelleme işlemi zaten var.\nBaşka bir tane oluşturmuyor.", "smw_smwadmin_updatestopped": "Mevcut tüm güncelleme işlemleri durduruldu.", "smw_smwadmin_updatenotstopped": "Çalışan güncelleme işlemini durdurmak için, gerçekten emin olduğunuzu belirtmek üzere onay kutusunu etkinleştirmeniz gerekir.", "smw-admin-docu": "<PERSON><PERSON> <PERSON><PERSON> sayfa, <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a>'nin kuru<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bakımı ve kullanımı sırasında size yardımcı olur ve ayrıca istatistiklerin yanı sıra daha fazla yönetici işlevi ve görevi de sağlar.\nYönetim işlevlerini yerine getirmeden önce değerli verileri yedeklemeyi unutmayın.", "smw-admin-environment": "Yaz<PERSON>lım ortamı", "smw-admin-db": "Veritabanı kurulumu", "smw-admin-db-preparation": "Tablo başlatma devam ediyor ve boyut ve olası tablo optimizasyonlarına kadar sonuçların görüntülenmesi biraz zaman alabilir.", "smw-admin-dbdocu": "Semantic MediaWiki, anlamsal verileri saklamak için kendi veritabanı yapısını gerektirir (ve MediaWiki'den bağ<PERSON>ızdır, dolayısıyla MediaWiki kurulumunun geri kalanını etkilemez).\n<PERSON><PERSON> kurulum işlevi, her<PERSON><PERSON> bir zarar vermeden birden çok kez yürütülebilir, ancak kurulum veya yükseltme sırasında yalnızca bir kez gereklidir.", "smw-admin-permissionswarn": "İşlem SQL hatalarıyla başarısız olursa, vikiniz tarafından kullanılan veritabanı kullanıcısının (\"LocalSettings.php\" dosyanızı kontrol edin) muhtemelen yeterli izinlere sahip değildir.\nBu kullanıcıya tablo oluşturma ve silme için ek izinler verin, geçici olarak veritabanı kökünüzün girişini \"LocalSettings.php\" dosyasına girin veya kimlik bilgilerini kullanabilen <code>setupStore.php</code> bakım betiğini kullanın.", "smw-admin-dbbutton": "Tabloları başlat veya yükselt", "smw-admin-announce": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-announce-text": "Vikiniz herkese açıksa, <a href=\"https://wikiapiary.com\">WikiApiary</a> is<PERSON>li viki takip vikisinde kaydını yapabilirsiniz.", "smw-admin-deprecation-notice-title": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaldırma bild<PERSON>", "smw-admin-deprecation-notice-docu": "A<PERSON>ağ<PERSON><PERSON><PERSON> b<PERSON>, kull<PERSON><PERSON><PERSON><PERSON> kaldırılmış veya kaldırılmış ancak bu vikide etkin olduğu tespit edilen ayarları içerir. Gelecekteki sürümlerin bu yapılandırmalar için desteği kaldırması beklenmektedir.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> kull<PERSON><PERSON><PERSON><PERSON> kaldırıldı ve $2 sürümünde kaldırılacaktır", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|seçenek|seçenek}} aşağıdakileri kaldıracak (veya değiştirecek):", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> kullanı<PERSON>dan kaldırıldı ve $2 sürümünde kaldırılacaktır", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>, <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>, <code>$2</code> tara<PERSON>ı<PERSON>n değiştiril<PERSON>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|seçenek|seçenek}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code>, <code>$2</code> ile değ<PERSON>tiriliyor", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> $2 sürümünde kaldırıldı", "smw-admin-deprecation-notice-title-notice": "Kullanımdan kaldırılmış ayarlar", "smw-admin-deprecation-notice-title-notice-explanation": "<b><PERSON><PERSON><PERSON><PERSON><PERSON> kaldı<PERSON>ılmış ayarlar</b>, bu vikide kullanıldığı tespit edilen ve gelecekteki bir sürümde kaldırılması veya değiştirilmesi planlanan ayarları gösterir.", "smw-admin-deprecation-notice-title-replacement": "Değiştirilen veya yeniden adlandırılan ayarlar", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Değiştirilen veya yeniden adlandırılan ayarlar</b>, yeniden adlandırılan veya başka bir şekilde değiştirilen ayarları içerir ve adlarının veya biçimlerinin önceden güncellenmesi önerilir.", "smw-admin-deprecation-notice-title-removal": "Kaldırılan ayarlar", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Kald<PERSON>r<PERSON><PERSON> ayarlar</b>, önceki bir sürümde kaldırılan ancak bu vikide kullanıldığı tespit edilen ayarları tanımlar.", "smw-admin-deprecation-notice-section-legend": "Gösterge", "smw-smwadmin-refresh-title": "<PERSON>eri on<PERSON> ve g<PERSON>me", "smw_smwadmin_datarefresh": "Verileri yeniden oluştur", "smw_smwadmin_datarefreshdocu": "Vikinin mevcut içeriğine bağlı olarak tüm Semantic MediaWiki verilerini geri yüklemek mümkündür.\nBu, bazı yazılım yükseltmeleri nedeniyle dahili format değiştiyse bozuk verileri onarmak veya verileri yenilemek için yararlı olabilir.\nGüncelleme sayfa sayfa yürütülür ve hemen tamamlanmaz.\nAşağıda bir güncellemenin devam edip etmediği gösterilir ve güncellemeleri başlatmanıza veya durdurmanıza izin verir (bu özellik site yöneticisi tarafından devre dışı bırakılmadığı sürece).", "smw_smwadmin_datarefreshprogress": "<strong><PERSON><PERSON> günce<PERSON><PERSON> zaten sürüyor.</strong>\n<PERSON>ir kullanıcı vikiye her eriştiğinde verileri yalnızca küçük parçalar halinde yenilediğinden güncellemenin yalnızca yavaş ilerlemesi normaldir.\nBu güncelleştirmeyi daha hızlı bir şekilde bitirmek için, MediaWiki bakım komut dosyasını <code>runJobs.php</code> çağırabilirsiniz (bir toplu işte yapılan güncelleme sayısını kısıtlamak için <code>--maxjobs 1000</code> seçeneğini kullanın).\nMevcut güncellemenin tahmini ilerlemesi:", "smw_smwadmin_datarefreshbutton": "Verilerin yeniden oluşturulmasını zamanla", "smw_smwadmin_datarefreshstop": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> durdur", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, {{GENDER:$1|eminim}}.", "smw-admin-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/tr", "smw-admin-job-scheduler-note": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (etkin olanlar), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sırasında kilitlenme durumlarından kaçınmak için iş kuyruğu aracılığıyla gerçekleştirilir. İşlemden [https://www.mediawiki.org/wiki/Manual:Job_queue/tr iş kuyruğu] sorumludur ve <code>runJobs.php</code> bakım betiğinin uygun bir kapasiteye sahip olması önemlidir (ayrıca <code>$wgRunJobsAsync</code> yapılandırma parametresini bakın).", "smw-admin-outdateddisposal-title": "Eski varlıkların elden <PERSON>ı<PERSON>ı", "smw-admin-outdateddisposal-intro": "Bazı etkinlikler (bir özellik türünde değişiklik, vikisayfaların kaldırılması veya hata değerlerinin düzeltilmesi) [https://www.semantic-mediawiki.org/wiki/Outdated_entities/tr eski varlıklar] ile sonuçlanır ve ilişkili tablo alanını boşaltmak için bunların periyodik olarak kaldırılması önerilir.", "smw-admin-outdateddisposal-active": "Eski bir varlık imha işi planlandı.", "smw-admin-outdateddisposal-button": "Bertarafı planlayın", "smw-admin-feature-disabled": "Bu özellik bu vikide devre dışı bırakıldı, lütfen <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">a<PERSON><PERSON></a> yardım sayfasına bakın veya sistem hizmetlisine başvurun.", "smw-admin-propertystatistics-title": "Özellik istatistiklerinin yeniden oluşturulması", "smw-admin-propertystatistics-intro": "Tüm özellik kullanım istatistiklerini yeniden oluşturur ve buradaki özelliklerin [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count kullanım sayısını] günceller ve düzeltir.", "smw-admin-propertystatistics-active": "Özellik istatistiklerini yeniden oluşturma işi zamanlandı.", "smw-admin-propertystatistics-button": "İstatistiklerin yeniden oluşturulmasını zamanla", "smw-admin-fulltext-title": "Tam metin aramayı yeniden oluşturma", "smw-admin-fulltext-intro": "<PERSON><PERSON>, etkin bir [https://www.semantic-mediawiki.org/wiki/Help:Full-text_search/tr tam metin araması] veri türüyle özellik tablolarından yeniden oluşturur. <PERSON><PERSON> kurallarında (değiştirilmiş stopwords, yeni stemmer vb.) ve/veya yeni eklenen veya değiştirilen bir tabloda yapılan değişiklikler, bu işin tekrar çalıştırılmasını gerektirir.", "smw-admin-fulltext-active": "<PERSON> metin aramayı yeniden oluşturma işi zamanlandı.", "smw-admin-fulltext-button": "Tam metin yeniden oluşturmayı zamanla", "smw-admin-support": "Destek alma", "smw-admin-supportdocu": "Sorun olması durumunda size yardımcı olacak çeşitli kaynaklar sunulmaktadır:", "smw-admin-installfile": "Ku<PERSON><PERSON><PERSON>uzla ilgili sorunlar yaşıyorsanız, <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">INSTALL dosyasındaki</a> yönergeleri kontrol ederek ve <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation/tr\">kurulum sayfasına</a> kontrol ederek başlayın.", "smw-admin-smwhomepage": "Semantic MediaWiki ile ilgili tüm kullanıcı belgeleri <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b> adresindedir.", "smw-admin-bugsreport": "Hatalar <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">sorun <PERSON></a>, <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs/tr\">hata bildirme</a> say<PERSON><PERSON><PERSON>nda, etkili bir sorun raporunun nasıl yazılacağı konusunda bazı bilgiler verilmektedir.", "smw-admin-questions": "Başka sorularınız veya önerileriniz varsa Semantic MediaWiki <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">kullanıcı posta listesindeki</a> tartışmaya katılın.", "smw-admin-other-functions": "<PERSON><PERSON><PERSON>", "smw-admin-statistics-extra": "İstatistik işlevleri", "smw-admin-statistics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-section-title": "Tamamlayıcı işlevler", "smw-admin-supplementary-section-subtitle": "Desteklenen çekirdek <PERSON>", "smw-admin-supplementary-section-intro": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, bakım faaliyetleri kapsamı dışında ek işlevler sağlar ve listelenen bazı işlevlerin ([https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions/tr belgeyi] bakın) kısıtlanmış veya kullanılamaz ve bu nedenle bu vikide erişilemez.", "smw-admin-supplementary-settings-title": "Yapılandırma ve ayarları", "smw-admin-supplementary-settings-intro": "<u>$1</u> Semantic MediaWiki'nin davranışını tanımlayan parametreleri gösterir", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Operasyonel istatist<PERSON>", "smw-admin-supplementary-operational-statistics-short-title": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-operational-statistics-intro": "Genişletilmiş bir <u>$1</u> k<PERSON><PERSON><PERSON>", "smw-admin-supplementary-idlookup-title": "Varlık arama ve elden çıkarma", "smw-admin-supplementary-idlookup-short-title": "varlık arama ve elden çı<PERSON>ma", "smw-admin-supplementary-idlookup-intro": "Basit bir <u>$1</u> işlevini destekler", "smw-admin-supplementary-duplookup-title": "Çift varlıklar araması", "smw-admin-supplementary-duplookup-intro": "Seçilen tablo matrisi için kopya olarak kategorilenmiş varlıkları bulmak için <u>$1</u>", "smw-admin-supplementary-duplookup-docu": "<PERSON><PERSON> sayfada, [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities/tr kopyaları] olarak kategorize edilmiş seçili tablolardan girişler listelenmektedir. Kopyalanan girişler (eğer varsa) yalnızca sonlandırılmış bir güncelleme veya başarısız geri alma işleminden kaynaklanabilecek nadir durumlarda gerçekleşmelidir.", "smw-admin-supplementary-duplookup-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities/tr", "smw-admin-supplementary-operational-statistics-cache-title": "Önbellek istatistikleri", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> önbellekle ilgili seçili istatistikleri gösterir", "smw-admin-supplementary-operational-table-statistics-title": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-operational-table-statistics-short-title": "tablo istatistik<PERSON>i", "smw-admin-supplementary-operational-table-statistics-intro": "Seçilen bir tablo kümesi için <u>$1</u> oluşturur", "smw-admin-supplementary-operational-table-statistics-explain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yöneticilerin ve veri küratörlerinin arka uç ve depolama motoru durumu hakkında bilinçli kararlar vermelerine yardımcı olmak için seçilen tablo istatistiklerini içerir.", "smw-admin-supplementary-operational-table-statistics-legend": "<PERSON><PERSON><PERSON><PERSON>, tablo istatistikleri için kullanılan bazı anahtarları açıklar ve şunları içerir:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> bir tablodaki toplam satır sayısı", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> şu anda kull<PERSON>da olan son kimlik\n* <code>duplicate_count</code> id_table içinde bulunan kopya sayısı (ayrıca bakınız: [[Special:SemanticMediaWiki/duplicate-lookup|Çift varlık araması]]) \n* <code>rows.rev_count</code> doğrudan vikisayfa bağlantısını gösteren bir revision_id atanmış satır sayısı\n* <code>rows.smw_namespace_group_by_count</code> tabloda kullanılan ad alanları için birleştirilmiş satırların sayısı\n* <code>rows.smw_proptable_hash.query_match_count</code> tablo referansına sahip sorgu alt nesnelerinin sayısı\n* <code>rows.smw_proptable_hash.query_null_count</code> tablo başvurusu olmayan sorgu alt nesnelerinin sayısı (ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kayan kaynak)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> ben<PERSON><PERSON> olan terimlerin yüzdesi (düşük yüzde oranı, yinelenen terimlerin tablo içeriğini ve dizini işgal ettiğini gösterir)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> yalnızca bir kez görünen terimlerin sayısı\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> birden fazla görünen terim sayısı", "smw-admin-supplementary-elastic-version-info": "S<PERSON>r<PERSON><PERSON>", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> a<PERSON><PERSON> ve dizin istatistikleri hakkındaki ayrıntıları gösterir", "smw-admin-supplementary-elastic-docu": "<PERSON><PERSON> <PERSON><PERSON>, Semantic MediaWiki'ye ve onun [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>]'a bağlı bir Elasticsearch kümesine il<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, sağlık ve dizin istatistikleri hakkında bilgi içerir.", "smw-admin-supplementary-elastic-functions": "Desteklenen işlevler", "smw-admin-supplementary-elastic-settings-title": "<PERSON><PERSON><PERSON> (indisler)", "smw-admin-supplementary-elastic-settings-intro": "Semantic MediaWiki endekslerini yönetmek için Elasticsearch tarafından kullanılan <u>$1</u>", "smw-admin-supplementary-elastic-mappings-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-mappings-intro": "Endeksleri ve alan e<PERSON>lemelerini listelemek için <u>$1</u>", "smw-admin-supplementary-elastic-mappings-docu": "<PERSON><PERSON> say<PERSON>, ge<PERSON><PERSON><PERSON> dizin tarafından kullanılan alan eşleme ayrıntılarını içerir. Eşlemelerin <code>index.mapping.total_fields.limit</code> ile bağlantılı olarak izlenmesi önerilir (bir dizindeki izin verilen maksimum alan sayısını belirtir).", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code>, dizinlenmiş çekirdek alanların sayısını belirtirken, <code>nested_fields</code>, belirli ya<PERSON>landırılmış arama modellerini desteklemek için bir çekirdek alana atanan ek alanların toplam sayısını gösterir.", "smw-admin-supplementary-elastic-mappings-summary": "Özet", "smw-admin-supplementary-elastic-mappings-fields": "<PERSON>", "smw-admin-supplementary-elastic-nodes-title": "Noktalar", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> dü<PERSON><PERSON>m istatistiklerini gösteriyor", "smw-admin-supplementary-elastic-indices-title": "Endeksler", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> mevcut endekslere ve istatistiklerine genel bir bakış sunar", "smw-admin-supplementary-elastic-statistics-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> dizin seviyesi istatistiklerini gösterir", "smw-admin-supplementary-elastic-statistics-docu": "<PERSON><PERSON> say<PERSON>, bir dizin düzeyinde gerçekleşen farklı işlemler için endeks istatistikleri hakkında bir fikir verir, d<PERSON><PERSON><PERSON>r<PERSON>len istatistikler primerler ve toplam toplamalarla toplanır. [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html Yardım sayfası] mevcut indeks istatistiklerinin ayrıntılı bir açıklamasını içerir.", "smw-admin-supplementary-elastic-status-replication": "Çoğaltma durumu", "smw-admin-supplementary-elastic-status-last-active-replication": "Son aktif <PERSON>: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Yenileme aralığı: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Kurtarma işi biriktirme listesi: $1 (tahmin)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Besleme (dosya) iş biriktirme listesi: $1 (tahmin)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Çoğaltma kilitlendi: $1 (yeniden oluşturma devam ediyor)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Çoğaltma izleme (aktif): $1", "smw-admin-supplementary-elastic-replication-header-title": "Çoğaltma durumu", "smw-admin-supplementary-elastic-replication-function-title": "Çoğaltma", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> başarısız çoğaltmalarla ilgili bilgileri gösterir", "smw-admin-supplementary-elastic-replication-docu": "<PERSON><PERSON> <PERSON><PERSON>, Elasticsearch kümesiyle ilgili sorunları olduğu bildirilen varlıkların [https://www.semantic-mediawiki.org/wiki/Help:Replication_moncessing/tr çoğaltma durumu] hakkında bilgi sağlar. Geçici bir sorun olduğunu doğrulamak için listelenen varlıkları incelemeniz ve içeriği temizlemeniz önerilir.", "smw-admin-supplementary-elastic-replication-files-docu": "<PERSON><PERSON><PERSON> list<PERSON> i<PERSON>, ilk önce [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion dosya alma] işinin yürütülmesi ve işleminin tamamlanması gerektiği unutulmamalıdır.", "smw-admin-supplementary-elastic-replication-files": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-endpoints": "Bitiş noktaları", "smw-admin-supplementary-elastic-config": "Yapılandırmalar", "smw-admin-supplementary-elastic-no-connection": "Viki şu anda Elasticsearch kümesine bağlantı '''kuramıyor''', lütfen sistemin dizin ve sorgu yeteneğini etkisiz hale getirdiğinden sorunu araştırmak için viki yöneticisine başvurun.", "smw-list-count": "Liste $1 giriş içeriyor.", "smw-property-label-uniqueness": "\"$1\" etiketi en az bir diğer mülk gösterimiyle eşleştirildi. Lütfen bu sorunun nasıl çözüleceğ<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness yardım sayfasına] bakın.", "smw-property-label-similarity-title": "Özellik etiketi benzerlik raporu", "smw-property-label-similarity-intro": "<u>$1</u> mevcut özellik etiketleri için ben<PERSON>likleri hesaplar", "smw-property-label-similarity-threshold": "Eşik:", "smw-property-label-similarity-type": "Ekran Türü <PERSON>i", "smw-property-label-similarity-noresult": "Seçilen seçenekler için sonuç bulunamadı.", "smw-property-label-similarity-docu": "<PERSON><PERSON> say<PERSON><PERSON>, özellik etiketleri arasındaki [https://www.semantic-mediawiki.org/wiki/Help:Property_similarity/tr benzerlik mesafesini] (anlamsal veya sözcüksel benzerlikle karıştırılmamalıdır) karşılaştırır ve eşiği aşmaları halinde bunları bildirir. Rapor, aynı kavramı temsil eden yanlış yazılmış veya eşdeğer özellikleri filtrelemeye yardımcı olabilir (bildirilen özelliklerin kavramını ve kullanımını netleştirmeye yardımcı olması için [[Special:Properties|özellikler]] özel sayfasına bakın). Eşik, yaklaşık eşleştirme için kullanılan mesafeyi genişletmek veya daraltmak için ayarlanabilir. <code>[[Property:$1|$1]]</code>, özellikleri analizden muaf tutmak için kull<PERSON>.", "smw-admin-operational-statistics": "<PERSON><PERSON> <PERSON><PERSON>, Semantic MediaWiki ile ilgili işlevlerde veya bunlardan toplanan işlem istatistiklerini içerir. Vikiye özgü istatistiklerin geniş bir listesini [[Special:Statistics|<b>burada</b>]] bulabilirsiniz.", "smw_adminlinks_datastructure": "Veri yapısı", "smw_adminlinks_displayingdata": "<PERSON><PERSON>", "smw_adminlinks_inlinequerieshelp": "Satıriçi sorgu yardımı", "smw-page-indicator-usage-count": "<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count kullanım sayısı]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|Kullanıcı|Sistem}} tanımlı özellik", "smw-property-indicator-last-count-update": "<PERSON><PERSON><PERSON> kull<PERSON>ım sayısı\nSon güncelleme: $1", "smw-concept-indicator-cache-update": "Önbellek sayısı\nSon güncelleme: $1", "smw-createproperty-isproperty": "$1 türünde bir özelliktir.", "smw-createproperty-allowedvals": "Bu özellik için izin verilen {{PLURAL:$1|değeri bu özellik için olan <PERSON>|değeri bu özellik için olan <PERSON>}}:", "smw-paramdesc-category-delim": "Sınırlayıcı", "smw-paramdesc-category-template": "Öğeleri biçimlendirmek için bir şablon", "smw-paramdesc-category-userparam": "Şablona iletilecek bir parametre", "smw-info-par-message": "Görüntülenecek mesaj.", "smw-info-par-icon": "Gösterilecek simge, \"info\" veya \"warning\".", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "<PERSON><PERSON>", "prefs-extended-search-options": "Detaylı arama", "prefs-ask-options": "<PERSON><PERSON><PERSON> arama", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] ve ilişkili uzantılar, bir grup seçili özellik ve işlev için bireysel tercihler sağlar. Açıklamaları ve özellikleriyle birlikte bireysel ayarların bir listesi aş<PERSON>ğıdaki [https://www.semantic-mediawiki.org/wiki/Help:User_preferences/tr yardım sayfasında] mevcuttur.", "smw-prefs-ask-options-tooltip-display": "Parametre metnini #ask [[Special:Ask|sorgu oluşturucusu]] özel sayfasında bir bilgi ipucu olarak görüntüleyin.", "smw-prefs-ask-options-compact-view-basic": "<PERSON><PERSON> kavram gö<PERSON><PERSON><PERSON><PERSON><PERSON> etkinleştir", "smw-prefs-help-ask-options-compact-view-basic": "Etkinleştirilirse, Special:Ask küçük boyutlu sor görünümünde azaltılmış bir bağlantı kümesi görünt<PERSON>ler.", "smw-prefs-general-options-time-correction": "<PERSON><PERSON> [[Special:Preferences#mw-prefsection-rendering|zaman ofseti]] tercihini kullanarak <PERSON>zel sayfalar için zaman düzeltmesini etkinleştirin", "smw-prefs-general-options-jobqueue-watchlist": "İş sırasındaki izleme listemi kişisel çubuğumda göster", "smw-prefs-help-general-options-jobqueue-watchlist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, se<PERSON><PERSON><PERSON> bekleyen iş<PERSON>in tahmini kuyruk boyutlarıyla birlikte bekleyen [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist listesini] gösterir.", "smw-prefs-general-options-disable-editpage-info": "Düzenleme sayfasındaki tanıtım metnini devre dışı bırakın", "smw-prefs-general-options-disable-search-info": "Standart arama sayfasındaki sözdizimi destek bilgilerini devre dışı bırakın", "smw-prefs-general-options-suggester-textinput": "Anlamsal varlıklar için giriş yardımını etkinleştir", "smw-prefs-help-general-options-suggester-textinput": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir giriş bağlamındaki özellikleri, kav<PERSON>ları ve kategorileri bulmak için bir [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance giriş yardımı] kullanılmasına izin verir.", "smw-ui-tooltip-title-property": "Özellik", "smw-ui-tooltip-title-quantity": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-info": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-service": "Hizmet bağlantıları", "smw-ui-tooltip-title-warning": "Uyarı", "smw-ui-tooltip-title-error": "<PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Parametre", "smw-ui-tooltip-title-event": "<PERSON><PERSON>", "smw-ui-tooltip-title-note": "Not", "smw-ui-tooltip-title-legend": "Gösterge", "smw-ui-tooltip-title-reference": "<PERSON><PERSON><PERSON>", "smw_unknowntype": "<PERSON><PERSON> özelliğin \"$1\" türü geçersiz", "smw-concept-cache-text": "<PERSON><PERSON><PERSON> toplam $1 sayfa ve son olarak $3, $2 tari<PERSON><PERSON> g<PERSON>.", "smw_concept_header": "\"$1\" kav<PERSON><PERSON>n say<PERSON>ı", "smw_conceptarticlecount": "Aşağıda $1 sayfa gösteriliyor.", "smw-qp-empty-data": "Bazı yetersiz seçim kriterleri nedeniyle istenen veriler görüntülenemedi.", "right-smw-admin": "<PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "right-smw-patternedit": "İzin verilen düzenli ifadeleri ve kalıpları korumak için erişimi düzenleyin (Semantic MediaWiki)", "right-smw-pageedit": "Açıklamalı sayfalar <code>Düzenleme korumalı</code> için düzenleme er<PERSON>şimi (Semantic MediaWiki)", "right-smw-schemaedit": "[https://www.semantic-mediawiki.org/wiki/Help:<PERSON><PERSON><PERSON>lar<PERSON>] d<PERSON><PERSON><PERSON> (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "<PERSON>ş kuyruğuna [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist izleme listesi] <PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Bir kuruluşla ilişkili revizyon uyumsuzluğu hakkındaki bilgilere erişin (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "[https://www.semantic-mediawiki.org/wiki/Help:Edit_help D<PERSON>zenleme yardımı] g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "restriction-level-smw-pageedit": "korumalı (yalnızca uygun kullanıcılar)", "action-smw-patternedit": "Semantic MediaWiki tarafından kullanılan normal ifadeleri düzenle", "action-smw-pageedit": "<code><PERSON><PERSON>zen<PERSON>e k<PERSON>alı</code> ile açıklamalı sayfaları düzenle (Semantic MediaWiki)", "group-smwadministrator": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|hiz<PERSON><PERSON> (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|k<PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smweditor": "Editörler (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)}}", "grouppage-smweditor": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "action-smw-admin": "Semantic MediaWiki yönetim görevlerine erişim", "action-smw-ruleedit": "kural sayfalarını düzenle (Semantic MediaWiki)", "smw-property-namespace-disabled": "[https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks Ad alanı] özelliği devre dışı bırakıldı, bu mülk için bir tür veya özelliği özgü özellikler bildirmeye çalışmak mümkün değil.", "smw-property-predefined-default": "\"$1\", $2 türünün önceden tanımlanmış bir özelliğidir.", "smw-property-predefined-common": "Bu özellik önceden konuşlandırılmıştır ([https://www.semantic-mediawiki.org/wiki/Help:Special_properties özel özellik] olarak da bilinir) ve ek yönetici ayrıcalıklarıyla birlikte gelir ancak diğer tüm yetkiler [https://www.semantic-mediawiki.org/wiki/Property kullanıcı tanımlı özellik] gibi kullanılabilir.", "smw-property-predefined-ask": "\"$1\", tek tek sorgular hakkında meta bilgileri ([https://www.semantic-mediawiki.org/wiki/Subobject alt nesnesi şeklinde]) temsil eden ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] biçiminde temsil eden önceden tanımlanmış bir özelliktir.", "smw-property-predefined-asksi": "\"$1\", bir sorguda kullanılan koşulların sayısını toplayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-askde": "\"$1\", bir sorgunun derinliği hakkında bilgi veren ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-askde": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth/tr $smwgQMaxDepth]</code> yapılandırma parametresi tarafından kısıtlanan bir sorgunun yürütülmesi ile alt sorgu yuvalaması, özellik zincirleri ve kullanılabilir açıklama öğeleri temelinde hesaplanan sayısal bir değerdir.", "smw-property-predefined-askpa": "\"$1\", bir sorgu sonucunu etkileyen parametreleri açıklayan önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-askpa": "[https://www.semantic-mediawiki.org/wiki/Help:Query_profiler <PERSON><PERSON><PERSON> profi<PERSON>] belirten bir özellik koleksiyonunun parçasıdır.", "smw-sp-properties-docu": "Bu sayfada [https://www.semantic-mediawiki.org/wiki/Property özellikleri] ve bu viki için kullanılabilecek kullanım sayıları listelenmektedir. Güncel sayım istatistikleri için [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics özellik istatistikleri] bakım betiğin düzenli olarak çalıştırılması önerilir. Farklı bir görünüm için [[Special:UnusedProperties|kullanılmayan]] veya [[Special:WantedProperties|istenen özellikler]] özel sayfalarına bakın.", "smw-sp-properties-cache-info": "Listelenen veriler [https://www.semantic-mediawiki.org/wiki/Caching önbellek] üzerinden alındı ve en son $1 tarihinde güncellendi.", "smw-sp-properties-header-label": "Özellikler listesi", "smw-admin-settings-docu": "Semantic MediaWiki ortamıyla ilgili tüm varsayılan ve yerel ayarların bir listesini görüntüler. Kişisel ayarlarla ilgili ayrıntılar için lütfen [https://www.semantic-mediawiki.org/wiki/Help:Configuration yapılandırma] yardım sayfasına bakın.", "smw-sp-admin-settings-button": "<PERSON><PERSON><PERSON> listesi oluş<PERSON>", "smw-admin-idlookup-title": "<PERSON><PERSON>", "smw-admin-idlookup-docu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Semantic MediaWiki'deki tek bir varlık (vikisayfa, alt nesne, özellik, vb.) hakkında teknik ayrıntıları gösterir. <PERSON><PERSON><PERSON>, ilgili arama alanıyla eşleşen sayısal bir kimlik veya dize değeri olabilir, ancak herhangi bir kimlik kaynağı MediaWiki'nin sayfası veya revizyon kimliğiyle değil Semantic MediaWiki ile ilgilidir.", "smw-admin-iddispose-title": "<PERSON><PERSON><PERSON>", "smw-admin-iddispose-docu": "Elden çıkarma işleminin sınırsız olduğu ve onaylanması halinde, bekleyen tablolardaki tüm kaynaklarıyla birlikte varlığı depolama motorundan çıkaracağı unutulmamalıdır. Lütfen bu görevi '''dikkatlice''' ve yalnızca [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal/tr belgelere] okuduktan sonra gerçekleştirin.", "smw-admin-iddispose-done": "\"$1\" kimliği depolama arka ucundan kaldırıldı.", "smw-admin-iddispose-references": "\"$1\" kim<PERSON>ğinde {{PLURAL:$2|en az bir}} etkin kaynak var:", "smw-admin-iddispose-references-multiple": "En az bir etkin kaynak kaydına sahip eşleşmelerin listesi.", "smw-admin-iddispose-no-references": "Arama \"$1\" ifadesini bir tablo girişiyle eşleştiremedi.", "smw-admin-idlookup-input": "Ara:", "smw-admin-objectid": "Kimlik:", "smw-admin-tab-general": "<PERSON><PERSON>", "smw-admin-tab-notices": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaldırma bild<PERSON>", "smw-admin-tab-maintenance": "Bakım", "smw-admin-tab-supplement": "Tamamlayıcı işlevler", "smw-admin-tab-registry": "<PERSON><PERSON><PERSON>", "smw-admin-tab-alerts": "Uyarılar", "smw-admin-alerts-tab-deprecationnotices": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaldırma bild<PERSON>", "smw-admin-alerts-tab-maintenancealerts": "Bakım uyarıları", "smw-admin-alerts-section-intro": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, uygun haklara sahip bir yönetici veya kullanıcıdan dikkat gerektirecek şekilde sınıflandırılmış ayarlar, işlemler ve diğer etkinliklerle ilgili uyarıları ve bildirimleri gösterir.", "smw-admin-maintenancealerts-section-intro": "Aşağıdaki uyarılar ve bildirimler çözülmelidir ve gerekli olmasa da, sistemin ve operasyonel sürdürülebilirliğin iyileştirilmesine yardımcı olması beklenmektedir.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "<PERSON><PERSON><PERSON>ti<PERSON>", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "<PERSON><PERSON><PERSON>, son [https://www.semantic-mediawiki.org/wiki/Database/Table optimization/tr tablo optimizasyonun] $2 gün önce ($1 üzerinden başlayan) $3 günlük bakım eşiğini aştığını tespit etti. Belgelerde belirtildiği gibi, optimizasyonların çalıştırılması, sorgu planlayıcının sorgular hakkında daha iyi kararlar vermesini sağlayacaktır, bu nedenle tablo optimizasyonunun düzenli olarak çalıştırılması önerilir.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Eski varlıklar", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Sistem $1 saydığı [https://www.semantic-mediawiki.org/wiki/Outdated_entities eski varlıklar] ve $2 eşiğini aşarak kritik bir katılımsız bakım düzeyine ulaştı. [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] bakım betiğini çalıştırmanız önerilir.", "smw-admin-maintenancealerts-invalidentities-alert-title": "Geçersiz varlıklar", "smw-admin-maintenancealerts-invalidentities-alert": "Sistem $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities varlık] ile bir [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace saklanmamış ad alanı] ile eşleşti ve [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] veya [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>] bakım betiği çalıştırmasını önerilir.", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "<PERSON><PERSON><PERSON>", "smw-admin-configutation-tab-namespaces": "Ad alanları", "smw-admin-configutation-tab-schematypes": "<PERSON><PERSON>", "smw-admin-maintenance-tab-tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-maintenance-tab-scripts": "Bakım bet<PERSON>i", "smw-admin-maintenance-no-description": "Açıklama yok.", "smw-admin-maintenance-script-section-title": "Kullanılabilir bakım betiklerin listesi", "smw-admin-maintenance-script-section-intro": "<PERSON><PERSON>a<PERSON><PERSON><PERSON><PERSON> bakım betikleri, listelenen komut dosyalarını yürütebilmek için bir yönetici ve komut satırına eri<PERSON>im gerektirir.", "smw-admin-maintenance-script-description-dumprdf": "Mevcut üçlülerin RDF dışa aktarımı.", "smw-admin-maintenance-script-description-rebuildconceptcache": "<PERSON><PERSON> bet<PERSON>, seçilen önbellekleri oluşturabileceği, kald<PERSON>rabileceği ve güncelleyebileceği Semantic MediaWiki için kavram önbelleklerini yönetmek için kullanılır.", "smw-admin-maintenance-script-description-rebuilddata": "Anlamsal verilere sahip olabilecek tüm sayfalar arasında dolaşarak veritabanındaki tüm anlamsal verileri yeniden oluşturur.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Anlamsal verileri olan tüm varlıklar arasında geçiş yaparak Elasticsearch dizinini (yalnızca <code>ElasticStore</code> kullanan kurulumlar için) yeniden oluşturur.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Elasticsearch'te eksik varlıkları bulun (yalnızca <code>ElasticStore</code> kullanan kurulumlar için) ve uygun güncelleme işlerini zamanlayın.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "<code>SQLStore</code> tam metin arama dizinini yeniden oluşturur (ayarın etkinleştirildiği kurulumlar için.", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Tüm özellik varlıklarının kullanım istatistiklerini yeniden oluşturur.", "smw-admin-maintenance-script-description-removeduplicateentities": "Etkin kaynakça olmayan seçili tablolarda bulunan yinelenen varlıkları kaldırır.", "smw-admin-maintenance-script-description-setupstore": "Depolama alanı ve sorgu arka ucunu <code>LocalSettings.php</code> içinde tanımlandığı şekilde ayarlar.", "smw-admin-maintenance-script-description-updateentitycollation": "<code>SQLStore</code> içindeki <code>smw_sort</code> al<PERSON><PERSON><PERSON><PERSON> günceller ([https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation] ayarına göre).", "smw-admin-maintenance-script-description-populatehashfield": "Değeri eksik satırlar için <code>smw_hash</code> alanını doldurur.", "smw-admin-maintenance-script-description-purgeentitycache": "Bilinen varlıklar ve ilişkili verileri için önbellek girdilerini temizleyin.", "smw-admin-maintenance-script-description-updatequerydependencies": "Sorguları ve sorgu bağımlılıklarını güncelleyin ([https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore] ayarına bakın).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Eski varlıkları ve sorgu bağlantılarını atın.", "smw-admin-maintenance-script-description-runimport": "Otomatik olarak keşfedilen içeriği [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs] adresinden doldurun ve içe aktarın.", "smw-admin-maintenance-script-section-update": "Betikleri güncelle", "smw-admin-maintenance-script-section-rebuild": "Betikleri yeniden oluştur", "smw-livepreview-loading": "Yükleniyor...", "smw-sp-searchbyproperty-description": "<PERSON>u sayfa, bir özellik ve adlandırılmış bir değer tarafından tanımlanan varlıkları bulmak için basit bir [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces/tr tarama arayüzü] sağlar. Kullanılabilir diğer arama arayüzleri arasında [[Special:PageProperty|sayfa özellik arama]] ve [[Special:Ask|ask sorgu oluşturucu]] yer alır.", "smw-sp-searchbyproperty-resultlist-header": "<PERSON><PERSON><PERSON>", "smw-sp-searchbyproperty-nonvaluequery": "\"$1\" özelliğine atanmış değerlerin listesi.", "smw-sp-searchbyproperty-valuequery": "\"$1\" özelliği eklenmiş \"$2\" değerine sahip sayfaların listesi.", "smw-datavalue-number-textnotallowed": "\"$1\", $2 değerine sahip beyan edilmiş bir sayı türüne atanamaz.", "smw-datavalue-number-nullnotallowed": "\"$1\", sayı olarak izin verilmeyen bir \"NULL\" ile döndü.", "smw-editpage-annotation-enabled": "<PERSON><PERSON> say<PERSON>, Semantic MediaWiki tarafından sağlanan yapılandırılmış ve sorgulanabilir içerik oluşturmak için semantik metin içi ek açıklamaları (örn. <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) destekler. Ek açıklamaların veya #ask ayrıştırıcı işlevinin nasıl kullanılacağına ilişkin kapsamlı bir açıklama için lütfen [https://www.semantic-mediawiki.org/wiki/Help:Getting_started başlarken], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation metin içi ek açıklama] veya [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries satır içi sorgular] yardım sayfalarına bakın.", "smw-editpage-annotation-disabled": "<PERSON>u sayfa, ad alanı kısıtlamaları nedeniyle semantik metin içi ek açıklamalar için etkinleştirilmedi. Ad alanının nasıl etkinleştirileceğiyle ilgili ayrıntılar [https://www.semantic-mediawiki.org/wiki/Help:Configuration yapılandırma] yardım sayfasında bulunabilir.", "smw-editpage-property-annotation-enabled": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, bir veri tür<PERSON> (ör. <nowiki>\"[[Has type::Page]]\"</nowiki>) veya diğer deste<PERSON> bild<PERSON> (örn. <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>) belirtmek için anlamsal ek açıklamalar kullanılarak genişletilebilir. Bu sayfanın nasıl artırılacağına ilişkin açıklama için [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration bir mülk beyanı] veya [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes kullanılabilir veri türlerinin listesi] yardım sayfalarına bakın.", "smw-editpage-property-annotation-disabled": "<PERSON><PERSON><PERSON><PERSON>, ö<PERSON>den tanımlandığı için bir veri türü ek açıklamasıyla (ör. <nowiki>\"[[Has type::Page]]\"</nowiki>) genişletilemez ([https://www.semantic-mediawiki.org/wiki/Help:Special_properties özel özellikleri] yardım sayfasına bakın).", "smw-editpage-concept-annotation-enabled": "Bu kavram #concept ayrıştırıcı işlevi kullanılarak genişletilebilir. #concept'in nasıl kullanılacağına ilişkin açıklama için [https://www.semantic-mediawiki.org/wiki/Help:Concepts kavramı] yardım sayfasına bakın.", "smw-search-syntax-support": "<PERSON><PERSON>, Semantic MediaWiki'yi kullanarak sonuçları eşleştirmeye yardımcı olmak için an<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search sorgu sözdizimi] kullanımını destekler.", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance <PERSON><PERSON><PERSON> yardımcısı] da kullanılabilir özelliklerin ve kategorilerin ön seçimini kolaylaştırmak için etkinleştirilmiştir.", "smw-search-help-intro": "Bir <code><nowiki>[[ ... ]]</nowiki></code> g<PERSON><PERSON><PERSON>, Semantic MediaWiki arama arka ucunu kullanmak için giriş işlemcisine sinyal gönderir. <code><nowiki>[[ ... ]]</nowiki></code> ile <code><nowiki>[[ ... ]]</nowiki></code> desteklenmez VEYA Lorem ipsum gibi yapılandırılmamış bir metin aramasıyla birleştirildiğine dikkat edilmelidir.", "smw-search-help-structured": "<PERSON><PERSON><PERSON><PERSON> aramalar:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> ([https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context filtrelenmiş içerik] olarak)\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> ([https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context sorgu içeriği] ile)", "smw-search-help-proximity": "Yakınlık aramaları (bir özellik bilinmemektedir, '''yalnızca''' tam metin araması entegrasyonu sağlayan arka uçlar için kullanılabilir):\n\n* <code><nowiki>[[in:lorem ipsum]]</nowiki></code> (tüm dokümanlarda dizine eklenen \"lorem\" ve \"ipsum\" kelimelerini arayın)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (\"lorem ipsum\" ifadesini ifade olarak eşleştirin)", "smw-search-help-ask": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>, <code>#ask</code> sözdiziminin nasıl kullanılacağını açıklar.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Sayfa seçimi] seçilen sayfaların nasıl oluşturulacağını ve koşulların nasıl oluşturulacağını açıklar\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators Arama operatörleri], aralık ve joker karakter sorguları da dahil olmak üzere mevcut arama operatörlerini listeler", "smw-search-input": "<PERSON><PERSON><PERSON> ve arama", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance/tr <PERSON><PERSON><PERSON> yard<PERSON>], giriş alanı için sağlan<PERSON>ıştır ve aşağıdaki öneklerden birini kullanmasını gerektirir:\n\n* özellik önerilerini etkinleştirmek için <code>p:</code> (ör. <code><nowiki>[[p:Has...</nowiki></code>)\n\n* kategori önerilerini etkinleştirmek için <code>c:</code>\n\n* kavram önerilerini etkinleştirmek için <code>con:</code>", "smw-search-syntax": "Sözdizim", "smw-search-profile": "Genişletilmiş", "smw-search-profile-tooltip": "Semantic MediaWiki ile bağlantılı işlevleri ara", "smw-search-profile-sort-best": "En iyi eşleşme", "smw-search-profile-sort-recent": "En yeni", "smw-search-profile-sort-title": "Başlık", "smw-search-profile-extended-help-intro": "Special:Search [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile genişletilmiş profili] Semantic MediaWiki ve onun desteklenen sorgu arka ucuna özgü arama işlevlerine er<PERSON><PERSON><PERSON>.", "smw-search-profile-extended-help-sort": "Sonuç ekranı için aşağıdakileri içeren bir sıralama tercihi belirtir:", "smw-search-profile-extended-help-sort-title": "* Sıralama ölçütü olarak sayfa başlığını (veya görünen başlığı) kullanan \"Başlık\"", "smw-search-profile-extended-help-sort-recent": "* \"En son\" önce en son değiştirilen varlıkları gösterecektir (bu varlıklar bir [[Property:Modification date|Değişiklik tarihi]] ile açıklanmadığından alt nesne varlıkları bastırılacaktır)", "smw-search-profile-extended-help-sort-best": "* \"En iyi eşleşme\", varlıkları arka uç tarafından sağlanan puanlara göre [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy alaka düzeyine] göre sıralar", "smw-search-profile-extended-help-form": "G<PERSON>ş sürecini daraltmak ve kullanıcıların bir arama isteğine devam etmesini kolaylaştırmak için farklı özellik ve değer alanlarını göstererek belirli kullanım örneklerini eşleştirmek için formlar sağlanır (korunuyorsa). ($1 bakın)", "smw-search-profile-extended-help-namespace": "Ad alanı seçim kutusu, bir form seçilir seçilmez gizlenir ancak \"göster/gizle\" düğmesiyle görünür hale getirilebilir.", "smw-search-profile-extended-help-search-syntax": "<PERSON><PERSON>, Semantik MediaWiki'ye özgü bir arama bağlamı tanımlamak için <code>#ask</code> sözdiziminin kullanılmasını destekler. Yararlı ifadeler şunları içerir:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> \"...\" içeren bir şey bulmak için kullanılır ve özellikle ilgili arama bağlamı veya özellikleri bilinmediğinde yararlıdır (örneğin <code>in:(lorem && ipsum)</code> <code> <nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>) ile eşdeğerdir.", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> ile aynı sı<PERSON>la \"...\" içeren herhangi bir şey bulun", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> her<PERSON><PERSON> bir varlığı \"...\" özelliğiyle eşleştirmek içindir (örn. <code>(Foo && Bar)</code>, <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code>, \"...\" içeren hiçbir öğeyle eşleşmez", "smw-search-profile-extended-help-search-syntax-prefix": "* Aşağıdaki gibi ek özel önekler mevcuttur ve tanımlanmıştır: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* <nowiki>$1</nowiki> gibi bazı ifadeler saklıdır.", "smw-search-profile-extended-help-search-syntax-note": "''Listelenen işlemlerden bazıları yalnızca etkin bir tam metin dizini veya ElasticStore ile bağlantılı olarak kullanışlıdır.''", "smw-search-profile-extended-help-query": "<PERSON><PERSON><PERSON> o<PERSON>ak <code><nowiki>$1</nowiki></code> kullanıld<PERSON>.", "smw-search-profile-extended-help-query-link": "Daha fazla ayrıntı için lütfen $1 kullanın.", "smw-search-profile-extended-help-find-forms": "mevcut formlar", "smw-search-profile-extended-section-sort": "Sıralama ölçütü:", "smw-search-profile-extended-section-form": "Formlar", "smw-search-profile-extended-section-search-syntax": "<PERSON><PERSON>", "smw-search-profile-extended-section-namespace": "Ad alanı", "smw-search-profile-extended-section-query": "Sorg<PERSON>", "smw-search-profile-link-caption-query": "sorgu oluşturucu", "smw-search-show": "<PERSON><PERSON><PERSON>", "smw-search-hide": "<PERSON><PERSON><PERSON>", "log-name-smw": "Semantic MediaWiki günlüğü", "log-show-hide-smw": "Semantic MediaWiki günlüğü $1", "logeventslist-smw-log": "Semantik MediaWiki günlüğü", "log-description-smw": "Semantic MediaWiki ve bileşenleri tarafından bildirilen [https://www.semantic-mediawiki.org/wiki/Help:Logging etkin olay türle<PERSON>] etkinlikleri.", "logentry-smw-maintenance": "Semantic MediaWiki tarafından sağlanan bakımla ilgili et<PERSON>", "smw-datavalue-import-unknown-namespace": "\"$1\" içe aktarma ad alanı bilinmiyor. Lütfen OWL içe aktarma ayrıntılarının [[MediaWiki:Smw import $1]] aracılığıyla erişilebilir olduğundan emin olun", "smw-datavalue-import-missing-namespace-uri": "[[MediaWiki:Smw import $1|$1 aktarımda]] \"$1\" ad alanı URI'si bulunamadı.", "smw-datavalue-import-missing-type": "[[MediaWiki:Smw import $2|$2 aktarımda]] \"$1\" için tür tanımı bulunamadı.", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1 aktarımı]]", "smw-datavalue-import-invalid-value": "\"$1\" geçerli bir biçim değ<PERSON> ve \"namespace\":\"identifier\" (ör. \"foaf:name\") içermesi bekleniyor.", "smw-datavalue-import-invalid-format": "\"$1\" dizesinin dört parçaya bölünmesi be<PERSON>, ancak biçim anlaşılamadı.", "smw-property-predefined-impo": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary ithal edilen kelime haznesi] ile bir ilişkiyi tanımlayan önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanmaktadır.", "smw-property-predefined-type": "\"$1\", bir <PERSON><PERSON><PERSON> [[Special:Types|veri türü<PERSON>]] tanımlayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-sobj": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Container konteyner] yapısını temsil eden önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanmaktadır.", "smw-property-predefined-long-sobj": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, normal bir viki sayfasınınkine benzer ancak katıştırma konusuna bağlıyken farklı bir varlık alanında özellik değeri atamalarının biriktirilmesine izin verir.", "smw-property-predefined-errp": "\"$1\", d<PERSON><PERSON><PERSON><PERSON> değer ek açıklamaları için giriş hatalarını izleyen ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-errp": "Çoğu durumda bir tür uyumsuzluğu veya [[Property:Allows value|değer]] kısıtlaması neden olur.", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"], bir <PERSON>zelliğe il<PERSON> değer atamalarını kısıtlamak için izin verilen değerlerin bir listesini tanımlayabilen önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list \"$1\"], bir mü<PERSON>e il<PERSON>kin değer atamalarını kısıtlamak için izin verilen değerleri tutan bir listeye başvuru belirtebilecek ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanmaktadır.", "smw-datavalue-property-restricted-annotation-use": "\"$1\" özelliği sınırlı bir uygulama alanına sahip ve kullanıcı tarafından ek açıklama özelliği olarak kullanılamaz.", "smw-datavalue-property-restricted-declarative-use": "\"$1\" <PERSON><PERSON><PERSON><PERSON><PERSON>, bildirim niteliğindeki bir özelliktir ve yalnızca bir mülk veya kategori sayfasında kullanılabilir.", "smw-datavalue-property-create-restriction": "\"$1\" özelliği mevcut değil ve kullanıcının onaylanmamış değerleri oluşturmak veya bunlara ek açıklama eklemek için \"$2\" izni yok ([https://www.semantic-mediawiki.org/wiki/Help:Authority_mode yetki modu] sayfasına bakın).", "smw-datavalue-property-invalid-character": "\"$1\", özellik etiketinin bir parçası olarak listelenen \"$2\" karakterini içerir ve bu nedenle geçersiz olarak sınıflandırılmıştır.", "smw-datavalue-property-invalid-chain": "Ek açıklama işlemi sırasında özellik zinciri olarak \"$1\" kullanılmasına izin verilmez.", "smw-datavalue-restricted-use": "<PERSON>eri değ<PERSON> \"$1\", sınırl<PERSON> kullanım için işaretlendi.", "smw-datavalue-invalid-number": "\"$1\" sayı olarak yorumlanamaz.", "smw-query-condition-circular": "\"$1\" içinde olası bir dairesel durum tespit edildi.", "smw-query-condition-empty": "<PERSON><PERSON><PERSON> açıklamasının durumu boş.", "smw-types-list": "<PERSON><PERSON>", "smw-types-default": "\"$1\" yerle<PERSON>ik bir veri tipidir.", "smw-types-help": "Daha fazla bilgi ve örnekler bu [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 yardım sayfasında] bulunabilir.", "smw-type-anu": "\"$1\", [[Special:Types/URL|URL]] veri türün<PERSON>n bir çeşididir ve ç<PERSON>ğunluk<PERSON> ''owl:AnnotationProperty'' dışa aktarma bildirimi için kull<PERSON>lı<PERSON>.", "smw-type-boo": "\"$1\", true/false de<PERSON><PERSON><PERSON> tanı<PERSON>layan temel bir veri tü<PERSON>ü<PERSON>ü<PERSON>.", "smw-type-cod": "\"$1\", kaynak kodu listeleri gibi isteğe bağlı uzunluktaki teknik metinler için kullanılacak [[Special:Types/Metin|Metin]] veri türünün bir çeşididir.", "smw-type-geo": "\"$1\", co<PERSON><PERSON><PERSON> konumları tanımlayan ve genişletilmiş bir işlevsellik sağlamak için [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] uzantısını gerektiren bir veri türüdür.", "smw-type-tel": "\"$1\", RFC 3966'ya göre uluslararası telefon numaralarını tanımlayan özel bir veri türüdür.", "smw-type-txt": "\"$1\", rast<PERSON>e uzunluktaki dizeleri tanımlayan temel bir veri türüdür.", "smw-type-dat": "\"$1\" zaman içindeki noktaları birleşik bir formatta temsil eden temel bir veri tipidir.", "smw-type-ema": "\"$1\", bir e-postayı temsil eden özel bir veri türü<PERSON>ür.", "smw-type-tem": "\"$1\", sıcaklığı temsil eden özel bir sayısal veri tipidir.", "smw-type-qty": "\"$1\", sayısal temsili ve ölçü birimiyle miktarları tanımlayan bir veri türüdür.", "smw-type-rec": "\"$1\", yazılan özelliklerin listesini sabit bir sı<PERSON>la belirten bir kapsayıcı veri türüdür.", "smw-type-extra-tem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Fahrenheit ve <PERSON>ine gibi desteklenen birimleri içerir.", "smw-type-tab-properties": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-tab-types": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-tab-type-ids": "<PERSON><PERSON><PERSON>", "smw-type-tab-errors": "<PERSON><PERSON><PERSON>", "smw-type-primitive": "Temel", "smw-type-contextual": "Bağlamsal", "smw-type-compound": "Bileşik", "smw-type-container": "Konteyner", "smw-type-no-group": "Sınıflandırılmamış", "smw-special-pageproperty-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:PageProperty/tr", "smw-special-pageproperty-description": "<PERSON><PERSON> say<PERSON>, bir özelliğin ve belirli bir sayfanın tüm değerlerini bulmak için bir tarama arayüzü sağlar. Diğer kullanılabilir arama arayüzleri arasında [[Special:SearchByProperty|özellik arama]] ve [[Special:Ask|ask sorgu oluşturucu]] yer alır.", "smw-property-predefined-errc": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Special_properties/tr Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir ve uygun olmayan değer ek açıklamaları veya giriş işlemleriyle bağlantılı olarak ortaya çıkan hataları temsil eder.", "smw-property-predefined-long-errc": "<PERSON><PERSON><PERSON>, tutarsızlığa neden olan özellik kaynağı içerebilecek bir [https://www.semantic-mediawiki.org/wiki/Help:Container kaps<PERSON>ı] içinde toplanır.", "smw-property-predefined-errt": "\"$1\", bir hatanın metinsel açıklamasını içeren önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-subobject-parser-invalid-naming-scheme": "Kullanıcı tanımlı bir alt nesne, geçersiz bir adlandırma düzeni içeriyordu. İlk beş karakterde kullanılan nokta gösterimi ($1) uzantılar için ayrılmıştır. Bir [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects/tr#Adlandırılmış_tanımlayıcı adlandırılmış tanımlayıcı] ayarlayabilirsiniz.", "smw-datavalue-record-invalid-property-declaration": "<PERSON><PERSON><PERSON> tanı<PERSON>, kendisi kayıt türü olarak bildirilen ve buna izin verilmeyen \"$1\" özelliğini içerir.", "smw-property-predefined-mdat": "\"$1\", bir konunun son de<PERSON><PERSON><PERSON><PERSON><PERSON>me tarihine karşılık gelen ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-cdat": "\"$1\", bir konunun ilk revizyon tarihine karşılık gelen ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-newp": "\"$1\", bir konunun yeni olup olmadığını belirten ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-ledt": "\"$1\", son <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oluşturan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan kullanıcının sayfa adını içeren önceden tanımlanmış bir özelliktir.", "smw-property-predefined-mime": "\"$1\", yü<PERSON>nen bir dosyanın MIME türünü tanımlayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-media": "\"$1\", yü<PERSON><PERSON> bir dosyanın ortam türünü açıklayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON>, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-askfo": "\"$1\", bir sorguda kullanılan sonuç biçiminin adını tutan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON>, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-askst": "\"$1\", sorg<PERSON><PERSON> koş<PERSON>arını dize olarak tanımlayan önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-askdu": "\"$1\", sorgu y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tamamlamak için gereken ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan bir zaman de<PERSON> (saniye cinsinden) içeren önceden tanımlanmış bir özelliktir.", "smw-property-predefined-asksc": "\"$1\", alternatif (ör. uzak, birleşik) sorgu kaynaklarını tanımlayan [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON>, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-askco": "\"$1\", bir sorgunun veya bileşenlerinin durumunu tanımlamak için [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON>, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-askco": "Atanan numara veya numaralar [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler <PERSON><PERSON><PERSON> say<PERSON>ı<PERSON>] açıklanan dahili bir kodlanmış durumu temsil eder.", "smw-property-predefined-prec": "\"$1\", sayısal veri türleri için bir [https://www.semantic-mediawiki.org/wiki/Help:Display_precision görüntü hassasiyeti]ni (ondalık basamaklarla) açıklayan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-attch-link": "\"$1\", bir sayfada bulunan gömülü dosya ve resim bağlantılarını toplayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-inst": "\"$1\", MediaWiki'den bağımsız kategori bilgilerini depolayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlı dahili bir özelliktir.", "smw-property-predefined-unit": "\"$1\", say<PERSON><PERSON> ya<PERSON>ılan özellikler için görüntüleme birimlerini tanımlamak üzere önceden tanımlanmış bir tanımlayıcı özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-unit": "Virgülle ayrılmış bir liste, ekran için kullanılacak birimleri veya biçimleri açıklamaya olanak tanır.", "smw-property-predefined-conv": "\"$1\", fiziksel bir miktarın bazı birimleri için dönüşüm faktörünü tanımlayan önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-serv": "\"$1\", bir özellik hizmet bağlantıları eklemek için önceden tanımlanmış, tanımlayıcı bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-redi": "\"$1\", yönlendirmeleri kaydetmek için önceden tanımlanmış dahili bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-subp": "\"$1\", bir <PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of alt özelliği] olduğunu ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-subc": "\"$1\", bir kategor<PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of alt kategorisi] olduğunu ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından tanımlandığı önceden tanımlanmış bir özelliktir.", "smw-property-predefined-conc": "\"$1\", il<PERSON><PERSON><PERSON><PERSON> bir kavramı tanımlamak için önceden tanımlanmış dahili bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-err-type": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors iş<PERSON>e hataları] grubunu veya sınıfını tanımlamak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-skey": "\"$1\", bir sıralama baş<PERSON><PERSON>u yapmak için önceden tanımlanmış dahili bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantik MediaWiki] tarafından sağlanır.", "smw-property-predefined-pplb": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label tercih edilen mülk etiketi] belirtmek için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help: Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-chgpro": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation değişiklik yayılımı] bilgilerini tutmak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-schema-link": " ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafı<PERSON>n sağlanır.", "smw-property-predefined-format-schema": " ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafı<PERSON>n sağlanır.", "smw-property-predefined-profile-schema": " ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafı<PERSON>n sağlanır.", "smw-property-predefined-trans": " ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafı<PERSON>n sağlanır.", "smw-property-predefined-trans-source": " ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafı<PERSON>n sağlanır.", "smw-property-predefined-trans-group": " ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafı<PERSON>n sağlanır.", "smw-property-predefined-cont-len": "\"$1\", uzunluk bilgilerini depolamak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-cont-len": "Uzunluk bilgilerini toplamak ve saklamak için [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ve [https://www.semantic-mediawiki.org/Attachment_processor bağlantı işlemcisi]) ile bağlantılı olarak kullanılır alınan bir dosyadan alınır (varsa).", "smw-property-predefined-cont-lang": "\"$1\", dil bilgilerini saklamak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-cont-lang": "Dil bilgilerini toplamak ve depolamak için [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ve [https://www.semantic-mediawiki.org/Attachment_processor ek işlemcisi]) ile bağlantılı olarak kullanılır alınan bir dosyadan alınır (varsa).", "smw-property-predefined-cont-title": "\"$1\" başlık bilgisini saklamak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-cont-title": "Başlık bilgilerini toplamak ve saklamak için [https://www.semantic-mediawiki.org/ElasticStore Elastik Mağazası] (ve [https://www.semantic-mediawiki.org/Attachment_processor ek işlemcisi]) ile bağlantılı olarak kullanılır alınan bir dosyadan alınır (varsa).", "smw-property-predefined-cont-author": "\"$1\", yazar bil<PERSON>ini depolamak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-cont-author": "<PERSON><PERSON> bilgi<PERSON>ini toplamak ve depolamak için [https://www.semantic-mediawiki.org/ElasticStore Elastik Mağazası] (ve [https://www.semantic-mediawiki.org/Attachment_processor ek işlemcisi]) ile bağlantılı olarak kullanılır alınan bir dosyadan alınır (varsa).", "smw-property-predefined-cont-date": "\"$1\", tarih bilgilerini saklamak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-cont-date": "<PERSON><PERSON><PERSON> bil<PERSON>lerini toplamak ve depolamak için [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ve [https://www.semantic-mediawiki.org/Attachment_processor ek işlemcisi]) ile bağlantılı olarak kullanılır alınan bir dosyadan alınır (varsa).", "smw-property-predefined-cont-type": "\"$1\", dosya türü bilgilerini depolamak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-cont-type": "<PERSON><PERSON>r bilgilerini toplamak ve saklamak için [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ve [https://www.semantic-mediawiki.org/Attachment_processor ek işlemcisi]) ile bağlantılı olarak kullanılır alınan bir dosyadan alınır (varsa).", "smw-property-predefined-cont-keyw": "\"$1\", anahtar kelimeleri temsil etmek için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-long-cont-keyw": "Alınan anahtar kelimeleri toplamak ve saklamak için [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ve [https://www.semantic-mediawiki.org/Attachment_processor ek işlemcisi]) ile bağlantılı olarak kullanılan dosya (sağlanmışsa).", "smw-property-predefined-file-attch": "\"$1\", ek bilgilerini depolayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan bir kapsayıcıyı temsil eden önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-file-attch": "İçeriğe özgü tüm bilgileri toplamak için [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ve [https://www.semantic-mediawiki.org/Attachment_processor ek işlemcisi]) ile bağlantılı olarak kullanılır alınan bir dosyadan alınabilir (varsa).", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\" uzantısı] tespit edilmediğinden, \"$1\" çalışma kapasitesiyle sınırlandırıldı.", "smw-datavalue-monolingual-dataitem-missing": "Tek dilli bir bileşik değer oluşturmak için beklenen bir öğe eksik.", "smw-datavalue-languagecode-missing": "\"$1\" ek açıklaması için ayrıştırıcı bir dil kodu belirleyemedi (yani \"foo@en\").", "smw-datavalue-languagecode-invalid": "\"$1\", desteklenen bir dil kodu olarak tanınmadı.", "smw-property-predefined-lcode": "\"$1\", BCP47 biçimli bir dil kodunu temsil eden ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON>n, önceden tanımlanmış bir özelliktir.", "smw-type-mlt-rec": "\"$1\", bir metin değerini belirli bir [[Property:Language code|dil kodu]] ile ilişkilendiren bir [https://www.semantic-mediawiki.org/wiki/Help:Container kaps<PERSON>ı<PERSON>ısı] veri türüd<PERSON>r.", "smw-types-extra-mlt-lcode": "Veri türü bir dil kodunu {{PLURAL:$2|gerektirir|gerektirmez}} yapar (yani {{PLURAL:$2|dil kodu olmayan bir değer ek açıklaması kabul edilmez|dil kodu olmayan bir değer ek açıklaması kabul edilir}}).", "smw-property-predefined-text": "\"$1\", rast<PERSON>e uzunluktaki metni temsil eden ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON> sa<PERSON>, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-pdesc": "\"$1\", bir özelliği bir dil bağlamında tanımlamaya izin veren önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-list": "\"$1\", [[Special:Types/Kayıt|Kayıt]] türünde bir mülkle kullanılan özelliklerin listesini tanımlamak için önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-limitreport-intext-parsertime": "[SMW] Metin içi ek açıklama ayrıştırıcısı zamanı", "smw-limitreport-intext-postproctime": "[SMW] işlem sonrası süresi", "smw-limitreport-intext-parsertime-value": "$1 saniye", "smw-limitreport-intext-postproctime-value": "$1 saniye", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Mağaza güncelleme sü<PERSON>i (sayfa temizleme)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 saniye", "smw_allows_pattern": "<PERSON>u sayfanın, [[Property:Allows pattern|Desene izin verme]] özelliği tarafından kullanılabilecek kaynakların bir listesini (ardından [https://tr.wikipedia.org/wiki/Düzenli_ifade düzenli ifadeler]) içermesi beklenir. Bu sayfayı düzenlemek için <code>smw-patternedit</code> hakkı gerekir.", "smw-datavalue-allows-pattern-mismatch": "\"$1\", \"$2\" normal ifadesi tarafından geçersiz olarak sınıflandırıldı.", "smw-datavalue-allows-pattern-reference-unknown": "\"$1\" kalıp referansı, [[MediaWiki:Smw allows pattern]] içindeki bir girişle eşleştirilemedi.", "smw-datavalue-allows-value-list-unknown": "\"$1\" liste kaynağı [[MediaWiki:Smw allows list $1]] sayfasıyla eşleştirilemedi.", "smw-datavalue-allows-value-list-missing-marker": "\"$1\" liste içeriğinde * liste işaretçisi olan öğeler eksik.", "smw-datavalue-feature-not-supported": "\"$1\" özelliği bu vikide desteklenmiyor veya devre dışı bırakıldı.", "smw-property-predefined-pvap": "\"$1\", [https://tr.wikipedia.org/wiki/<PERSON><PERSON><PERSON>li_ifade düzenli ifade] eşleşmesini uygulamak için [[MediaWiki:Smw allows pattern|desen kaynağı]] belirleyebilen önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-dtitle": "\"$1\", bir varlığa farklı bir ekran başlığı atayabilen önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-pvuc": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, her ö<PERSON><PERSON><PERSON> (veya en fazla bir) de<PERSON>er atamasını kısıtlamak için önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-pvuc": "<PERSON><PERSON><PERSON><PERSON>, iki değer değişmez temsillerinde eşit olmadığında belirlenir ve bu kısıtlamanın herhangi bir ihlali hata olarak sınıflandırılır.", "smw-datavalue-constraint-uniqueness-violation": "\"$1\" özelliği sadece ben<PERSON><PERSON> değer atamalarını kabul eder ve ''$2'' halihazırda \"$3\" konusu için atanmış durumda.", "smw-datavalue-constraint-uniqueness-violation-isknown": "\"$1\" özelliği yalnızca benzersiz değer ek açıklamalarına izin verir, ''$2'' zaten atanmış bir değer içeriyor. \"$3\" benzersizlik kısıtlamasını ihlal ediyor.", "smw-datavalue-constraint-violation-non-negative-integer": "\"$1\" özelliği \"negatif olmayan bir tamsayı\" kısıtlamasına sahip ve ''$2'' değeri bu gereksinimi ihlal ediyor.", "smw-datavalue-constraint-violation-must-exists": "\"$1\" özelliğinin bir <code>must_exists</code> kısıtlaması var ve ''$2'' de<PERSON>eri bu gereksinimi ihlal ediyor.", "smw-datavalue-constraint-violation-single-value": "\"[[Property:$1|$1]]\" özelliğinin bir <code>single_value</code> kısıtlaması var ve \"$2\" değeri bu gereksinimi ihlal ediyor.", "smw-constraint-violation-uniqueness": "Yalnızca benzersiz değer atamalarına izin veren \"[[Property:$1|$1]]\" özelliğine bir <code>unique_value_constraint</code> kısıtlaması atanmıştır ve ''$2'' değer ek açıklamasının \"$3\" konusu.", "smw-constraint-violation-uniqueness-isknown": "Bir <code>unique_value_constraint</code> kıs<PERSON>tlaması, \"[[Property:$1|$1]]\" özelliğine tabidir, bu nedenle yalnızca benzersiz değer ek açıklamalarına izin verir, ''$2'' zaten \"$3\" ile şimdiki özne için teklik kısıtı.", "smw-constraint-violation-non-negative-integer": "\"[[Property:$1|$1]]\" özelliğine bir <code>non_negative_integer</code> kısıtlaması atanmıştır ve ''$2'' değer ek açıklaması sınırlama gereksinimini ihlal etmektedir.", "smw-constraint-violation-must-exists": "\"[[Property:$1|$1]]\" özelliğine bir <code>must_exists</code> kısıtlaması atanmıştır ve ''$2'' değer ek açıklaması sınırlama gereksinimini ihlal etmektedir.", "smw-constraint-violation-single-value": "\"[[Property:$1|$1]]\" özelliğine bir <code>single_value</code> kısıtlaması atandı ve \"$2\" değer ek açıklaması, sınırlama gereksinimini ihlal ediyor.", "smw-constraint-violation-class-shape-constraint-missing-property": "<code>property</code> an<PERSON><PERSON><PERSON><PERSON> \"[[:$1]]\" kategorisine bir <code>shape_constraint</code> at<PERSON><PERSON><PERSON>, gere<PERSON><PERSON> \"$2\" özelliği eksik.", "smw-constraint-violation-class-shape-constraint-wrong-type": "<code>shape_constraint</code> an<PERSON><PERSON><PERSON><PERSON> \"[[:$1]]\" kategorisine bir <code>property_type</code> atan<PERSON>r, \"$2\" <PERSON><PERSON>liği, \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "<code>max_cardinality</code> anah<PERSON><PERSON>na sahip \"[[:$1]]\" kategorisine bir <code>shape_constraint</code> at<PERSON><PERSON><PERSON>, \"$2\" özelliği, \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "<code>min_textlength</code> an<PERSON><PERSON><PERSON><PERSON> \"[[:$1]]\" kategorisine bir <code>shape_constraint</code> atan<PERSON>r, \"$2\" özelliği uzunluk \"$3\" gereksinimiyle eşleşmez.", "smw-constraint-violation-class-mandatory-properties-constraint": "\"[[:$1]]\" kategorisine bir <code>mandatory_properties</code> kısıtlaması atanmıştır ve aşağıdaki zorunlu özellikleri gerektirir: $2", "smw-constraint-violation-allowed-namespace-no-match": "\"[[Property:$1|$1]]\" özelliğine bir <code>allowed_namespaces</code> kısıtlaması atanır ve \"$2\" ad alanı gereksinimini ihlal eder, yaln<PERSON><PERSON><PERSON> a<PERSON>ağıdaki \"$3\" ad alanlarına izin verilir.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "<code>allowed_namespaces</code> kısıtlaması bir sayfa türü gerektirir.", "smw-constraint-schema-category-invalid-type": "Açıklamalı \"$1\" şeması bir kategori için geçersiz, \"$2\" türü gerektiriyor.", "smw-constraint-schema-property-invalid-type": "Ek açıklamalı \"$1\" şeması bir mülk için geçersiz, \"$2\" türü gerektiriyor.", "smw-constraint-error-allows-value-list": "\"$1\", \"$3\" özelliği için [[Property:Allows value|izin verilen değerlere izin verir]] listesinde ($2) değil.", "smw-constraint-error-allows-value-range": "\"$1\", \"$3\" özelliği için [[Property:Allows value|değere izin verir]] kısıtlaması tarafından belirtilen \"$2\" aralığı içinde değil.", "smw-property-predefined-boo": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan ve boole değerlerini temsil eden önceden tanımlanmış bir özelliktir.", "smw-property-predefined-num": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sayısal değerleri temsil etmek için sağlanan [[Special:Types/Number|tür]] ve önceden tanımlanmış bir özelliktir.", "smw-property-predefined-dat": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından tarih değerlerini göstermek için sağ<PERSON>n [[Special:Types/Date|tür]] ve önceden tanımlanmış bir özelliktir.", "smw-property-predefined-uri": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından URI/URL değerlerini temsil etmek üzere sağlanan [[Special:Types/URL|tür]] ve önceden tanımlanmış bir özelliktir.", "smw-property-predefined-qty": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından miktar değerlerini temsil etmek için sağ<PERSON>n [[Special:Types/Quantity|tür]] ve önceden tanımlanmış bir özelliktir.", "smw-datavalue-time-invalid-offset-zone-usage": "\"$1\", desteklenmeyen bir uzaklık ve bölge tanımlayıcısı içeriyor.", "smw-datavalue-time-invalid-values": "\"$1\" değeri \"$2\" biçiminde yorumlanamayan bilgiler içeriyor.", "smw-datavalue-time-invalid-date-components-common": "\"$1\" yo<PERSON><PERSON><PERSON>yan bazı bilgiler içeriyor.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\", ta<PERSON>h yo<PERSON>u i<PERSON>in geçersiz bir dış çizgi veya başka karakterler içeriyor.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" bazı boş bileşenler içeriyor.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" bir tarih yorumu için üçten fazla bileşen içeriyor.", "smw-datavalue-time-invalid-date-components-sequence": "\"$1\", tarih bileşenleri için kullanılabilir bir eşleşme matrisine yorum<PERSON>yan bir dizi içeriyor.", "smw-datavalue-time-invalid-ampm": "\"$1\", 12 saatlik bir kural için geçersiz saat öğesi olarak \"$2\" içerir.", "smw-datavalue-time-invalid-jd": "\"$1\" g<PERSON><PERSON>, \"$2\" bildirildiği şekilde geçerli <PERSON> (<PERSON>) numarası olarak yorumlanamıyor.", "smw-datavalue-time-invalid-prehistoric": "<PERSON><PERSON><PERSON> \"$1\" giri<PERSON> değeri yorumlanamıyor. <PERSON><PERSON><PERSON><PERSON>, yıllar veya bir takvim modeli belirtmek, ta<PERSON>h <PERSON> bir bağlamda beklenmedik sonuçlar döndürebilir.", "smw-datavalue-time-invalid": "\"$1\" g<PERSON><PERSON>, \"$2\" bildirildiği şekilde geçerli tarih veya saat bileşeni olarak yorumlanamıyor.", "smw-datavalue-external-formatter-uri-missing-placeholder": "Formatter URI'sında ''$1'' yer tutucu eksik.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" geçersiz bir URL.", "smw-datavalue-external-identifier-formatter-missing": "Özellikte [[Property:External formatter uri|\"Harici biçimlendirici URI\"]] ataması eksik.", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "\"$1\" ha<PERSON>i tan<PERSON>ıcısı çok alanlı bir ikame bekler, ancak geçerli \"$2\" değerinde gereksinimi karşılamak için en az bir değer parametresi eksik.", "smw-datavalue-keyword-maximum-length": "<PERSON><PERSON><PERSON> kelime maksimum uzunluğu $1 {{PLURAL:$1|karakter|karakter}} aştı.", "smw-property-predefined-eid": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından harici tanımlayıcıları temsil etmek için sağlanan [[Special:Types/<PERSON>ci tanımlayıcı|tür]] ve önceden tanımlanmış bir özelliktir.", "smw-property-predefined-peid": "\"$1\", ha<PERSON>i bir tanımlayıcı belirten ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON> sa<PERSON>, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-pefu": "\"$1\", bir yer tutucuyla harici bir kaynak belirtmek için [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON>, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-pefu": "URI'nin, geçerli bir kaynak başvurusu oluşturmak için [[Special:Types/<PERSON><PERSON> tanımlayıcı|ha<PERSON><PERSON> tanımlayıcı]] değeriyle ayarlanacak bir yer tutucu içermesi beklenir.", "smw-type-eid": "\"$1\", ha<PERSON><PERSON> (URI tabanlı) tanımlamak için [[Special:Types/Metin|Metin]] veri türünün bir varyantıdır ve bir [[Property:External formatter uri|Harici biçimlendirici URI]] bildirmek için atanmış özellikler gerektirir.", "smw-property-predefined-keyw": "\"$1\", önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan ve bir metni normalleştiren ve kısıtlı olan [[Special:Types/Anahtar <PERSON>|tür]] karakter uzunluğu.", "smw-type-keyw": "\"$1\", normalleştirilmiş içerik gösterimi ile sınırlı karakter uzunluğuna sahip [[Special:Types/Metin|Metin]] veri türünün bir varyantıdır.", "smw-datavalue-stripmarker-parse-error": "Verilen \"$1\" değeri [https://en.wikipedia.org/wiki/Help:Strip_markers şerit işaretleyicilerini] içerir ve bu nedenle yeterince ayrıştırılamaz.", "smw-datavalue-parse-error": "<PERSON><PERSON><PERSON> \"$1\" değeri anlaşılamadı.", "smw-datavalue-propertylist-invalid-property-key": "\"$1\" özellik listesi geçersiz bir \"$2\" özellik anahtarı içeriyordu.", "smw-datavalue-type-invalid-typeuri": "\"$1\" türü geçerli bir URI gösterimine dönüştürülemedi.", "smw-datavalue-wikipage-missing-fragment-context": "\"$1\" viki say<PERSON>, bir ba<PERSON><PERSON> sayfası olmadan kullanılamaz.", "smw-datavalue-wikipage-invalid-title": "\"$1\" sayfa türü giriş değeri geçersiz karakterler içeriyor veya eksik ve bu nedenle bir sorgu veya ek açıklama işlemi sırasında beklenmedik sonuçlara neden olabilir.", "smw-datavalue-wikipage-property-invalid-title": "\"$2\" giriş değerine sahip \"$1\" özelliği (sayfa türü olarak) geçersiz karakterler içeriyor veya eksik ve bu nedenle sorgu veya ek açıklama işlemi sırasında beklenmedik sonuçlara neden olabilir.", "smw-datavalue-wikipage-empty": "Vikisayfa g<PERSON>ş <PERSON> b<PERSON> (örn. <code>[[SomeProperty::]], [[]]</code>) ve bu nedenle ad olarak veya sorgu koşulunun bir parçası olarak kullanılamaz.", "smw-type-ref-rec": "\"$1\", bir de<PERSON><PERSON> ataması hakkında ek bilgilerin (ör. Provenance verileri) kaydedilmesine izin veren bir [https://www.semantic-mediawiki.org/wiki/Container kapsayıcısı] türüdür.", "smw-datavalue-reference-invalid-fields-definition": "[[Special:Types/Kaynakça|Kaynakça]] tür<PERSON>, [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Has alanları] özelliği kullanılarak bildirilecek özelliklerin bir listesini bekliyor", "smw-parser-invalid-json-format": "JSON ayrıştırıcısı \"$1\" ile döndü.", "smw-property-preferred-label-language-combination-exists": "\"$2\" dili \"$3\" etiketine zaten atanmış olduğundan, \"$1\" tercih edilen etiket olarak kullanılamaz.", "smw-clipboard-copy-link": "Bağlantıyı panoya kopyala", "smw-property-userdefined-fixedtable": "\"$1\", [https://www.semantic-mediawiki.org/wiki/Fixed_properties Düzenlenmiş özellik] olarak yapılandırıldı ve [https://www.semantic-mediawiki.org/wiki/Type_declaration tip bildirimi] de herhangi bir değişiklik yapılması gerekiyor <code> setupStore.php</code> çalıştırmak veya özel [[Special:SemanticMediaWiki|\"Veritabanı yükleme ve yükseltme\"]] görevini tamamlamak için.", "smw-data-lookup": "Veri işleniyor ...", "smw-data-lookup-with-wait": "İstek işleniyor ve biraz zaman alabilir.", "smw-no-data-available": "Veri yok.", "smw-property-req-violation-missing-fields": "\"$1\" <PERSON><PERSON><PERSON><PERSON><PERSON>, bu \"$2\" tür<PERSON> i<PERSON> bir [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] bild<PERSON><PERSON> e<PERSON>.", "smw-property-req-violation-multiple-fields": "\"$1\" mü<PERSON><PERSON> birden fazla (burada rekabet eden) [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] bild<PERSON><PERSON> içeriyor, bu \"$2\" türü için yalnızca bir tane bekleniyor.", "smw-property-req-violation-missing-formatter-uri": "\"$1\" ö<PERSON><PERSON>ğinde, <code>Harici biçimlendirici URI</code> özelliği tanımlanamadığı için açıklamalı tür için bildirim ayrıntıları eksik.", "smw-property-req-violation-predefined-type": "Önceden tanımlanmış özellik olarak \"$1\" <PERSON><PERSON><PERSON><PERSON><PERSON>, bu <PERSON><PERSON><PERSON><PERSON><PERSON> varsayılan türüyle uyumsuz bir \"$2\" türü bildirimi içeriyor.", "smw-property-req-violation-import-type": "İçe aktarılan \"$1\" sözcük dağarcığının önceden tanımlanmış türü ile uyumsuz bir tür bildirimi algılandı. <PERSON><PERSON>, bilgi içe aktarma tanımından alındığı için bir tür bildirilmesine gerek yoktur.", "smw-property-req-violation-change-propagation-locked-error": "\"$1\" özelliği de<PERSON>iştirildi ve atanan varlıkların [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation/tr değişiklik yayılımı] işlemi kullanılarak yeniden değerlendirilmesini gerektiriyor. Özellik sayfası, ara kesintileri veya çelişkili spesifikasyonları önlemek için birincil spesifikasyon güncellemesi tamamlanıncaya kadar kilitlenmiştir. [https://www.mediawiki.org/wiki/Manual:Job_queue/tr İş kuyruğu] zamanlayıcısının boyutuna ve sıklığına bağlı olduğundan, işlem sayfanın kilidinin açılmasından önce biraz zaman alabilir.", "smw-property-req-violation-change-propagation-locked-warning": "\"$1\" özelliği de<PERSON>iştirildi ve atanan varlıkların [https://www.semantic-mediawiki.org/wiki/Change_propagation değişiklik yayılımı] işlemi kullanılarak yeniden değerlendirilmesini gerektiriyor. Güncelleme, [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue İş kuyruğu] zamanlayıcısının boyutuna ve sıklığına bağlı olduğundan ve aracı kesintileri önlemek için mülkteki değişiklikleri ertelemeniz veya çelişkili özellikler.", "smw-property-req-violation-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation Değişiklik yayılımı] güncellemeleri bekliyor ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue iş] tahmin edilir) ve ara kesintileri veya çelişkili spesifikasyonları önlemek için işlem tamamlanıncaya kadar bir özellikte değişiklik yapılması beklenir.", "smw-property-req-violation-missing-maps-extension": "Semantic MediaWiki, önkoşul olan ve sonuç olarak işlevselliği sınırlayan (yani coğrafi verileri depolayamayan veya işleyemeyen [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] uzantısını algılayamadı) ekleyin.", "smw-property-req-violation-type": "<PERSON><PERSON><PERSON>, geçersiz değer ek açıklamalarına neden olabilecek rakip tür belirtimleri içeriyor, bu ne<PERSON>le kullanıcının uygun bir tür ataması bekleniyor.", "smw-property-req-error-list": "Özellik aşağıdaki hataları veya uyarıları içerir:", "smw-property-req-violation-parent-type": "\"$1\" özelliği ve atanan \"$2\" üst özelliğinin farklı tür ek açıklamaları var.", "smw-property-req-violation-forced-removal-annotated-type": "[https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_datatype_inheritance/tr Zorunlu üst türü mirası] zorlaması etkinleştirildi, \"$1\" özelliği için ek açıklama türü, üst özelliği \"$2\" tür<PERSON><PERSON> e<PERSON>leşmiyor ve bu gereksinimi yansıtacak şekilde değiştirilmiştir. Sayfa içi tür tanımının, bu özellik için hata mesajı ve zorunlu yaptırımın kaldırılması için ayarlanması önerilir.", "smw-change-propagation-protection": "[https://www.semantic-mediawiki.org/wiki/Change_propagation Değişiklik yayılımı] güncellemesi çalışırken yanlışlıkla veri değişikliğini önlemek için bu sayfa kilitlenir. [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue İş kuyruğu] zamanlayıcısının boyutuna ve sıklığına bağlı olduğundan, işlem sayfanın kilidinin açılmasından önce biraz zaman alabilir.", "smw-category-change-propagation-locked-error": "\"$1\" kategor<PERSON> değiştirildi ve atanan varlıkların bir [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation/tr değişiklik yayılımı] işlemi kullanılarak yeniden değerlendirilmesini gerektiriyor. Bu arada, ara kesintileri veya çelişkili spesifikasyonları önlemek için kategori sayfası birincil spesifikasyon güncellemesi tamamlanana kadar kilitlendi. [https://www.mediawiki.org/wiki/Manual:Job_queue/tr İş kuyruğu] zamanlayıcısının boyutuna ve sıklığına bağlı olduğundan, işlem sayfanın kilidinin açılmasından önce biraz zaman alabilir.", "smw-category-change-propagation-locked-warning": "\"$1\" kategorisi değiştirildi ve atanan varlıkların bir [https://www.semantic-mediawiki.org/wiki/Change_propagation değişiklik yayılımı] işlemi kullanılarak yeniden değerlendirilmesini gerektiriyor. Güncelleme, [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue iş kuyruğu] zamanlayıcısının boyutuna ve sıklığına bağlı olduğundan ve ara kesintileri önlemek için kategorideki değişikliklerin ertelenmesi veya çelişkili özellikler.", "smw-category-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation Değişiklik yayılımı] güncellemeleri bekliyor ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue iş] tahmin edilir) ve ara kesintileri veya çelişkili spesifikasyonları önlemek için işlem sonlandırılana kadar bir kategoride değişiklik yapılması beklenir.", "smw-category-invalid-value-assignment": "\"$1\", ge<PERSON><PERSON>li kategori veya değer ek açıklaması olarak tanınmıyor.", "protect-level-smw-pageedit": "Yalnızca sayfa düzenleme iznine sahip kullanıcılara izin ver (Semantic MediaWiki)", "smw-create-protection": "\"$1\" ö<PERSON><PERSON><PERSON><PERSON>, uygun \"$2\" hakkına (veya [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups kullanıcı grubu]) sa<PERSON> kullanıcılar ile sınırlıdır; [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode yetki modu] etkin.", "smw-create-protection-exists": "\"$1\" özelliğindek<PERSON>, uygun \"$2\" hakkına (veya [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups kullanıcı grubu]) sahip kullanıcılar ile sınırlıdır; [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode yetki modu] etkin.", "smw-edit-protection": "<PERSON><PERSON> <PERSON><PERSON>, yan<PERSON>ışlıkla veri değiştirilmesini önlemek için [[Property:Is edit protected|korumalı]] ve yalnızca uygun düzenleme hakkına (\"$1\") veya [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups kullanıcı grubu].", "smw-edit-protection-disabled": "Düzenleme koruması devre dışı bırakıldığından, \"$1\" varlık sayfalarını yetkisiz düzenlemelerden korumak için kullanılamaz.", "smw-edit-protection-auto-update": "Semantik MediaWiki koruma durumunu \"Düzenleme korumalı\" özelliğine göre güncelledi.", "smw-edit-protection-enabled": "Korumalı düzenle (Semantic MediaWiki)", "smw-patternedit-protection": "<PERSON>u sayfa korunmaktadır ve yalnızca uygun <code>smw-patternedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions izni] olan kullanıcılar tarafından düzenlenebilir.", "smw-property-predefined-edip": "\"$1\", düzenlemenin korumalı olup olmadığını belirtmek için [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON> sa<PERSON>, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-edip": "Herhangi bir kullanıcı bu özelliği bir nesneye ekleme yetkisine sahip olsa da, yalnızca özel izinli bir kullanıcı bir varlık eklendikten sonra korumayı düzenleyebilir veya iptal edebilir.", "smw-query-reference-link-label": "<PERSON><PERSON><PERSON>", "smw-format-datatable-emptytable": "Tabloda veri yok", "smw-format-datatable-info": "_TOTAL_ girişten _START_ - _END_ arasındakiler gösteriliyor", "smw-format-datatable-infoempty": "0 girdiden 0 ile 0 arası gösteriliyor", "smw-format-datatable-infofiltered": "(toplam _MAX_ girişten filtrelendi)", "smw-format-datatable-lengthmenu": "_MENU_ giriş<PERSON>ini göster", "smw-format-datatable-loadingrecords": "Yükleniyor...", "smw-format-datatable-processing": "İşleniyor...", "smw-format-datatable-search": "Ara:", "smw-format-datatable-zerorecords": "Hiçbir eşleşen kayıt bulunamadı", "smw-format-datatable-first": "İlk", "smw-format-datatable-last": "Son", "smw-format-datatable-next": "<PERSON><PERSON><PERSON>", "smw-format-datatable-previous": "<PERSON><PERSON><PERSON>", "smw-format-datatable-sortascending": ": art<PERSON> s<PERSON><PERSON> s<PERSON> i<PERSON>", "smw-format-datatable-sortdescending": ": a<PERSON><PERSON> s<PERSON><PERSON> sı<PERSON>ını etkinleştir", "smw-format-datatable-toolbar-export": "Dışa aktar", "smw-category-invalid-redirect-target": "\"$1\" ka<PERSON><PERSON><PERSON>, kate<PERSON><PERSON> o<PERSON> bir ad alanına geçersiz bir yönlendirme hedefi içeriyor.", "smw-parser-function-expensive-execution-limit": "Ayrıştırıcı işlevi pahalı yürütme sınırına ulaştı ( [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>] sayfasına bakın).", "smw-postproc-queryref": "Semantic MediaWiki, gerekli bazı sorgu sonrası işlemleri koşulu ile geçerli sayfayı yeniliyor.", "apihelp-smwinfo-summary": "Semantic MediaWiki istatistikleri ve diğer meta bilgiler hakkında bilgi almak için API modülü.", "apihelp-ask-summary": "So<PERSON><PERSON><PERSON> di<PERSON> kull<PERSON>rak Semantic MediaWiki'yi sorgulamak için API modülü.", "apihelp-askargs-summary": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>şları ve parametreler listesi olarak ask dilini kullanarak Semantic MediaWiki'yi sorgulamak için API modülü.", "apihelp-browsebyproperty-summary": "Bir özellik veya özellik listesi hakkında bilgi almak için API modülü.", "apihelp-browsebysubject-summary": "Bir konu hakkında bilgi almak için API modülü.", "apihelp-smwtask-summary": "Semantic MediaWiki ile ilgili görevleri yürütmek için API modülü (yalnızca dahili kullanım için, genel kullanım için değil).", "apihelp-smwbrowse-summary": "Semantic MediaWiki'deki farklı varlık türleri için göz atma etkinliklerini destekleyen API modülü.", "apihelp-ask-parameter-api-version": "Çıkış biçimlendirme:\n;2:<PERSON><PERSON><PERSON> listesi için {} kullanarak geriye dönük uyumlu biçim.\n;3:<PERSON><PERSON><PERSON> listesi olarak [] kullanılarak deneysel biçim.", "apihelp-smwtask-param-task": "Görev türü<PERSON>ü <PERSON>", "apihelp-smwtask-param-params": "Seçilen görev türü gereksinimiyle eşleşen JSON kodlu parametreler", "smw-apihelp-smwtask-example-update": "Belirli bir konu için güncelleme görevi çalıştırma örneğ<PERSON>:", "smw-api-invalid-parameters": "Geçersiz parametreler, \"$1\"", "smw-parser-recursion-level-exceeded": "Bir ayrıştırma işlemi sırasında $1 özyineleme düzeyi aşıldı. Şablon yapısını doğrulamanız veya gerekirse <code>$maxRecursionDepth</code> yapılandırma parametresini ayarlamanız önerilir.", "smw-property-page-list-count": "Bu özelliği kullanımı $1 sayfa gösteriliyor.", "smw-property-page-list-search-count": "$1 sayfa bu özelliği \"$2\" değer eşleşmesi ile gösteriyor.", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter Arama filtresi], [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions sorgu ifadeleri] ifadesinin dahil edilmesine izin verir <code>~</code> veya <code>!</code> olarak ekleyin. Seçilen [https://www.semantic-mediawiki.org/wiki/Query_engine sorgu motoru] ayrıca büyük/küçük harfe duyarlı olmayan eşleşmeyi veya aşağıdakiler gibi diğer kısa ifadeleri de destekleyebilir:\n\n* <code>in:</code> sonuç terimi içermelidir, ör.'<code>in:Foo</code>'\n\n* <code>not:</code> sonuç terimi içermemelidir, ör. '<code>not:Bar</code>'", "smw-property-reserved-category": "<PERSON><PERSON><PERSON>", "smw-category": "<PERSON><PERSON><PERSON>", "smw-datavalue-uri-invalid-scheme": "\"$1\", geçerli URI şeması olarak listelenmedi.", "smw-datavalue-uri-invalid-authority-path-component": "\"$1\", ge<PERSON><PERSON><PERSON> bir \"$2\" yetkisi veya yol bileşeni içerdiği belirlendi.", "smw-browse-property-group-title": "Özellik grubu", "smw-browse-property-group-label": "Özellik grubu etiketi", "smw-browse-property-group-description": "Özellik grubu açıklaması", "smw-property-predefined-ppgr": "\"$1\", özellikler için gruplama örneği olarak kullanılan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan varlıkları (çoğunlukla kategorileri) tanımlayan önceden tanımlanmış bir özelliktir.", "smw-filter": "Filtre", "smw-section-expand": "Bölümü genişlet", "smw-section-collapse": "Bölüm<PERSON>", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1] biçimi", "smw-help": "Yardım", "smw-cheat-sheet": "<PERSON><PERSON><PERSON>", "smw-personal-jobqueue-watchlist": "İş kuyruğu izleme listesi", "smw-personal-jobqueue-watchlist-explain": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>yi bekleyen iş kuyruğu girişlerinin bir tahminini gösterir.", "smw-property-predefined-label-skey": "Sıralama anahtarı", "smw-processing": "İşleniyor...", "smw-loading": "Yükleniyor...", "smw-fetching": "Getiriliyor...", "smw-preparing": "Hazırlanıyor...", "smw-expand": "Genişlet", "smw-collapse": "Dar<PERSON><PERSON>", "smw-copy": "Kopyala", "smw-copy-clipboard-title": "İçeriği panoya kopyalar", "smw-jsonview-expand-title": "JSON görünümünü genişletir", "smw-jsonview-collapse-title": "JSON görünümünü daraltır", "smw-jsonview-search-label": "Ara:", "smw-redirect-target-unresolvable": "<PERSON><PERSON><PERSON>, \"$1\" ne<PERSON><PERSON><PERSON>", "smw-types-title": "Tür: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Bir [https://www.semantic-mediawiki.org/wiki/Help:<PERSON><PERSON>a şema sayfası]nın içerik modelinin değiştirilmesine izin verilmez.", "smw-schema-namespace-edit-protection": "<PERSON>u sayfa korunmaktadır ve yalnızca uygun <code>smw-schemaedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions izni] olan kullanıcılar tarafından düzenlenebilir.", "smw-schema-namespace-edit-protection-by-import-performer": "<PERSON><PERSON> say<PERSON>, listelenen bir [https://www.semantic-mediawiki.org/wiki/Import_performer içe aktarma sanatçısı] tarafından içe aktarıldı ve bu sayfanın içeriğini değiştirmenin yalnızca listelenen kullanıcılarla sınırlı olduğu anlamına gelir.", "smw-schema-error-title": "Doğrulama {{PLURAL:$1|hatası|hataları}}", "smw-schema-error-schema": "'''$1''' doğrulama şeması aşağıdaki tutarsızlıkları buldu:", "smw-schema-error-miscellaneous": "Çeş<PERSON><PERSON> hatalar ($1)", "smw-schema-error-validation-json-validator-inaccessible": "\"<b>$1</b>\" JSON doğrulayıcısına er<PERSON>miyor (veya yüklenemiyor) ve \"$2\" dosyasının incelenememesinin nedeni, ge<PERSON><PERSON>li sayfanın kaydedilmesini veya değiştirilmesini engelliyor.", "smw-schema-error-validation-file-inaccessible": "\"$1\" doğ<PERSON><PERSON>a dosyasına erişilemiyor.", "smw-schema-error-type-missing": "İçeriğin [https://www.semantic-mediawiki.org/wiki/Help:Schema şema ad alanında] tanınması ve kullanılabilmesi için bir türü eksik.", "smw-schema-error-type-unknown": "\"$1\" tür<PERSON> ka<PERSON> ve [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema] ad alanındaki içerik için kullanılamaz.", "smw-schema-error-json": "JSON hatası: \"$1\"", "smw-schema-error-input": "G<PERSON><PERSON> doğrulaması aşağıdaki sorunları buldu, içerik kaydedilmeden önce bunların ele alınması gerekiyor. [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling/tr <PERSON>ema yardımı] sayfas<PERSON>, tutarsızlıkların nasıl giderileceği veya şema girdisiyle ilgili sorunların nasıl çözüleceği konusunda bazı tavsiyeler sağlayabilir.", "smw-schema-error-input-schema": "'''$1''' doğrulama şeması aşağıdaki tutarsızlıkları buldu ve içerik kaydedilebilmesi için bunların ele alınması gerekiyor. [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling/tr Şema yardımı] sayfası, bu sorunların nasıl çözüleceğine ilişkin bazı öneriler sağlayabilir.", "smw-schema-error-title-prefix": "<PERSON><PERSON> <PERSON><PERSON> tü<PERSON>, <PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON> \"$1\" önekiyle başlamasını gerektirir.", "smw-schema-validation-error": "\"$1\" tür<PERSON> ka<PERSON> ve [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema] ad alanındaki içerik için kullanılamaz.", "smw-schema-validation-schema-title": "JSON şeması", "smw-schema-summary-title": "Özet", "smw-schema-title": "<PERSON><PERSON>", "smw-schema-usage": "Kullanım", "smw-schema-type": "<PERSON><PERSON>ü<PERSON>", "smw-schema-type-description": "<PERSON><PERSON><PERSON>", "smw-schema-description": "Şema açıklaması", "smw-schema-description-link-format-schema": "Bu şema türü, atanan [[Property:Formatter schema|biçimlendirici şeması]] özelliğiyle bağlantılı olarak içeriğe duyarlı bağlantılar oluşturmak için özelliklerin tanımını destekler.", "smw-schema-description-search-form-schema": "<PERSON><PERSON> <PERSON><PERSON>ü<PERSON>, g<PERSON><PERSON>ın nasıl oluşturulacağına, varsay<PERSON>lan ad alanlarının nasıl tanımlanacağına veya bir arama isteği için önek ifadelerinin nasıl bildirileceğine ilişkin talimatları içeren [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/tr genişletilmiş arama] profili için giriş formlarının ve özelliklerinin tanımını destekler.", "smw-schema-description-property-profile-schema": "<PERSON>u <PERSON>ema türü, atanan özelliğin özelliklerini ve ek açıklama değerlerini bildirmek için bir profilin tanımını destekler.", "smw-schema-description-property-group-schema": "<PERSON><PERSON> <PERSON><PERSON> türü, [https://www.semantic-mediawiki.org/wiki/Help:Special:Browsing göz atma] a<PERSON><PERSON><PERSON><PERSON>n<PERSON>n yapılandırılmasına yardımcı olmak için [https://www.semantic-mediawiki.org/wiki/Help:Property_group/tr özellik grupları] tanımını destekler.", "smw-schema-description-property-constraint-schema": "<PERSON>u özellik, bir özellik örneği için sınırlama kurallarının tanımını ve buna atanan değerleri destekler.", "smw-schema-description-class-constraint-schema": "<PERSON><PERSON> <PERSON><PERSON> tü<PERSON>, bir sı<PERSON><PERSON><PERSON> (yani ka<PERSON>) i<PERSON>in kısıtlama kurallarının tanımını destekler.", "smw-schema-tag": "{{PLURAL:$1|Etiket|Etiket}}", "smw-property-predefined-constraint-schema": "\"$1\", bir kısıtlama şeması tanımlayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-schema-desc": "\"$1\", bir şema açıklaması saklayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-schema-def": "\"$1\", <PERSON><PERSON> içeriğini depolayan ve önceden tanımlanmış bir özelliktir ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanır.", "smw-property-predefined-schema-tag": "\"$1\", bir <PERSON>ema koleksiyonunu tanımlamak için [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tara<PERSON><PERSON><PERSON><PERSON> sa<PERSON>n, önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-schema-tag": "Benzer içerik veya özelliklerin şemalarını tanımlayan bir etiket.", "smw-property-predefined-schema-type": "\"$1\", bir grup şemayı ayırt etmek için bir tür tanımlayan ve [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] tarafından sağlanan önceden tanımlanmış bir özelliktir.", "smw-property-predefined-long-schema-type": "Her [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type türü], sözdizimi öğelerinin ve uygulama kurallarının kendi yorumunu sağlar ve bir [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation geçerlilik şeması] yardımı ile ifade edilebilir.", "smw-ask-title-keyword-type": "<PERSON><PERSON><PERSON> k<PERSON>", "smw-ask-message-keyword-type": "<PERSON>u arama <code><nowiki>$1</nowiki></code> k<PERSON><PERSON><PERSON>yla eşleşir.", "smw-remote-source-unavailable": "<PERSON><PERSON> \"$1\" hedefine bağlanılamıyor.", "smw-remote-source-disabled": "'''$1''' kaynağı uzaktan istek desteğini devre dışı bıraktı!", "smw-remote-source-unmatched-id": "'''$1''' ka<PERSON><PERSON><PERSON>, uzak bir isteği destekleyebilen bir Semantic MediaWiki sürümüyle eşleşmiyor.", "smw-remote-request-note": "<PERSON>u<PERSON>, '''$1''' <PERSON><PERSON> ka<PERSON>ından getirilir ve oluşturulan içeriğin geçerli vikide bulunmayan bilgileri içermesi muhtemeldir.", "smw-remote-request-note-cached": "Sonuç, '''$1''' uzak kaynağında<PERSON> '''önbellekli'''dir ve oluşturulan içeriğin geçerli viki içinde bulunmayan bilgileri içermesi muhtemeldir.", "smw-parameter-missing": "\"$1\" parametresi eksik.", "smw-property-tab-usage": "Kullanım", "smw-property-tab-profile-schema": "<PERSON>il <PERSON>", "smw-property-tab-redirects": "Eşanlamlılar", "smw-property-tab-subproperties": "Alt özellikleri", "smw-property-tab-errors": "Yanlış atamalar", "smw-property-tab-constraint-schema": "Kısıtlama şeması", "smw-property-tab-constraint-schema-title": "Derlenmiş kısıtlama şeması", "smw-property-tab-specification": "... fazlası", "smw-concept-tab-list": "Liste", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-ask-tab-result": "<PERSON><PERSON><PERSON>", "smw-ask-tab-extra": "Ekstra", "smw-ask-tab-debug": "Ayıkla", "smw-ask-tab-code": "Kod", "smw-install-incomplete-tasks-title": "Eksik yönetim görevleri", "smw-install-incomplete-intro": "[https://www.semantic-mediawiki.org Semantic MediaWiki]'nin {{PLURAL:$1|kurulumu|yükseltmesi}} işini bitirmek için $2 eksik veya [[Special:PendingTaskList|beklemede]] g<PERSON><PERSON><PERSON> var. Yet<PERSON>li haklara sahip bir yönetici veya kullanıcı bunu tamamlayabilir. Bu, tutarsızlıkları önlemek için yeni veriler eklenmeden önce yapılmalıdır.", "smw-install-incomplete-intro-note": "<PERSON>üm ilgili görevler çözüldükten sonra bu mesaj kaybolacaktır.", "smw-pendingtasks-intro-empty": "Semantic MediaWiki ile bağlantılı olarak hiçbir görev beklemede, eksik veya olağanüstü olarak sınıflandırılmamıştır.", "smw-pendingtasks-intro": "<PERSON><PERSON> <PERSON><PERSON>, Semantic MediaWiki ile bağlantılı olarak bekleyen, tamamlanmamış veya bekleyen olarak sınıflandırılmış görevler hakkında bilgi sağlar.", "smw-pendingtasks-setup-no-tasks-intro": "<PERSON><PERSON><PERSON><PERSON> (veya yü<PERSON>) ta<PERSON><PERSON>landı, şu anda bekleyen veya bekleyen görev yok.", "smw-pendingtasks-tab-setup": "<PERSON><PERSON>", "smw-updateentitycollation-incomplete": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> ayarı yakın zamanda değiştirildi ve <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> bet<PERSON><PERSON><PERSON>, varl<PERSON>kların güncellenmesi ve doğru sıralama alanı değerini içermesi için yürütülür.", "smw-updateentitycountmap-incomplete": "<code>smw_countmap</code> alanı yeni bir sürüme eklendi ve <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> betiği işlevlerin bu alanın içeriğine erişebilmesi için yürütülür.", "smw-populatehashfield-incomplete": "<PERSON><PERSON><PERSON> sırasında <code>smw_hash</code> al<PERSON>, <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> betiğinin yürütülmesi gerekir.", "smw-install-incomplete-populate-hash-field": "<PERSON><PERSON><PERSON> sırasında <code>smw_hash</code> al<PERSON>, <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> betiğinin yürütülmesi gerekir.", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code> [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore varsayılan deposu] olar<PERSON> seçildi, ancak uzantı <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> betiğ<PERSON> yü<PERSON>üldü, lütfen betiğini talimatlara göre çalıştırın.", "smw-elastic-rebuildelasticindex-run-incomplete": "<code>ElasticStore</code> [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore varsayılan deposu] olar<PERSON> seçildi, ancak uzantı <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> betiğ<PERSON> yü<PERSON>üldü, lütfen betiğini talimatlara göre çalıştırın.", "smw-pendingtasks-setup-intro": "<b>Semantic MediaWiki</b> sayfasının {{PLURAL:$1|kurulum|yükseltme}}, aşağıdaki görevleri [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade/tr eksik] olarak sınıflandırdı ve bir yönet<PERSON>nin (veya yeterli haklara sahip bir kullanıcının), kullanıcılar içerik oluşturmaya veya değiştirmeye devam etmeden önce bu görevleri çözmesi beklenir.", "smw-pendingtasks-setup-tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-filter-count": "Filtre sayısı", "smw-es-replication-check": "Çoğaltma denetimi (Elasticsearch)", "smw-es-replication-error": "Elasticsearch çoğaltma sorunu", "smw-es-replication-file-ingest-error": "<PERSON><PERSON><PERSON> alma sorunu", "smw-es-replication-maintenance-mode": "Elasticsearch bakımı", "smw-es-replication-error-missing-id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Elasticsearch arka ucunda \"$1\" (ID: $2) adlı makalenin eksik olduğunu buldu.", "smw-es-replication-error-divergent-date": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>zlemesi, \"$1\" maddesi (Kimlik: $2) i<PERSON><PERSON> <b>değişiklik tarihinin</b> bir tutarsızlık gösterdiğini tespit etti.", "smw-es-replication-error-divergent-date-short": "Karş<PERSON>laş<PERSON><PERSON><PERSON> iç<PERSON> a<PERSON>ğıdaki tarih bilgileri kullanıldı:", "smw-es-replication-error-divergent-date-detail": "Başvurulan değişiklik tarihi:\n*Elasticsearch: $1 \n*Veri tabanı: $2", "smw-es-replication-error-divergent-revision": "Ç<PERSON><PERSON><PERSON><PERSON><PERSON> izlemesi, \"$1\" maddesi (ID: $2) i<PERSON>in <b>iliş<PERSON>li düzeltme</b>nin bir tutarsızlık gösterdiğini tespit etti.", "smw-es-replication-error-divergent-revision-short": "Karşılaştırma için a<PERSON>ğıdaki ilişkili revizyon verileri kullanılmıştır:", "smw-es-replication-error-divergent-revision-detail": "Kaynakça verilen ilişkili revizyon:\n*Elasticsearch: $1 \n*Veri tabanı: $2", "smw-es-replication-error-maintenance-mode": "Elasticsearch replikas<PERSON>u şu anda kısıtlanmıştır çü<PERSON> bir [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode/tr <b>bakım modunda</b>] <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, varl<PERSON>klar ve sayfalardaki değişiklikler hemen <b>görünmez</b> ve sorgu sonuçları güncel olmayan bilgiler içerebilir.", "smw-es-replication-error-no-connection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Elasticsearch kümesine bağlantı kuramadığından herhangi bir denetim gerçekleştiremiyor.", "smw-es-replication-error-bad-request-exception": "Elasticsearch bağlantı işleyicisi, çoğaltma ve arama istekleri sırasında devam eden bir sorunu belirten kötü bir istek özel durumu (\"400 çakışması http hatası\") verdi.", "smw-es-replication-error-other-exception": "Elasticsearch bağlantı işleyicisi bir istisna attı: \"$1\".", "smw-es-replication-error-suggestions": "Tutarsızlığı gidermek için sayfanın düzenlenmesi veya temizlenmesi önerilmektedir. <PERSON><PERSON> ed<PERSON>, Elasticsearch kümesinin kendisini (ayırıcı, istisnalar, disk alanı vb.) kontrol edin.", "smw-es-replication-error-suggestions-maintenance-mode": "Bir [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild/tr dizin yeniden oluşturma] işleminin şu anda devam edip etmediğini veya <code>refresh_interval</code> beklenen varsayılan değere ayarlanıp ayarlanmadığını kontrol etmek için viki hizmetlisine başvurmanız önerilir.", "smw-es-replication-error-suggestions-no-connection": "Viki hizmetlisine başvurmanız ve \"bağlantı yok\" sorununu bildirmeniz önerilir.", "smw-es-replication-error-suggestions-exception": "Elasticsearch'ın durumu, endeksleri ve olası yanlış yapılandırma sorunları hakkında bilgi için lütfen günlükleri kontrol edin.", "smw-es-replication-error-file-ingest-missing-file-attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> izlemesi, \"$1\" <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dosya alma işlemcisinin başlatılmadığını veya tamamlanmadığını belirten [[Property:File attachment|Dosya eki]] ek açıklamasının eksik olduğunu buldu.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Ek açıklama ve dosya dizini kullanıma sunulmadan önce lütfen [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion dosya alma] işinin planlandığından ve yürütüldüğünden emin olun.", "smw-report": "<PERSON><PERSON><PERSON>", "smw-legend": "Gösterge", "smw-datavalue-constraint-schema-category-invalid-type": "Açıklamalı \"$1\" şeması bir kategori için geçersiz, \"$2\" türü gerektiriyor.", "smw-datavalue-constraint-schema-property-invalid-type": "Ek açıklamalı \"$1\" şeması bir mülk için geçersiz, \"$2\" türü gerektiriyor.", "smw-entity-examiner-check": "Bir {{PLURAL:$1|denetçiyi}} arka planda çalıştırılıyor", "smw-entity-examiner-indicator": "Varlık sorunu paneli", "smw-entity-examiner-deferred-check-awaiting-response": "\"$1\" denetçisi şu anda arka uçtan yanıt bekliyor.", "smw-entity-examiner-deferred-elastic-replication": "Elastik", "smw-entity-examiner-deferred-constraint-error": "Kısıtlama", "smw-entity-examiner-associated-revision-mismatch": "Revizyon", "smw-entity-examiner-deferred-fake": "<PERSON><PERSON><PERSON>", "smw-entity-examiner-indicator-suggestions": "Varlık incelemesinin bir par<PERSON>ı o<PERSON>, aşağıdaki {{PLURAL:$1|sorun|sorun}} bulundu ve {{PLURAL:$1|sorun|sorun}} dikkatle incelemeniz ve uygun {{PLURAL:$1|eylem|eylem}}.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Kısıtlama|Kısıtlama}}", "smw-indicator-revision-mismatch": "Revizyon", "smw-indicator-revision-mismatch-error": "[https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner İlişkilendirilmiş düzeltme] denetimi, MediaWiki'de başvurulan düzeltme ile bu varlık için Semantic MediaWiki'de ilişkilendirilen düzeltme arasında bir uyumsuzluk buldu.", "smw-indicator-revision-mismatch-comment": "Uyumsuzluk normalde bazı işlemlerin Semantic MediaWiki'deki depolama işlemini kesintiye uğrattığını gösterir. <PERSON><PERSON><PERSON>rini incelemeniz ve özel durumlar veya diğer hatalar olup olmadığına bakmanız önerilir.", "smw-listingcontinuesabbrev": "devam", "smw-showingresults": "$2. sonuçtan başlayarak {{PLURAL:$1|<strong>1</strong> sonuç|<strong>$1</strong> sonuç}} aşağıdadır:"}