{"@metadata": {"authors": ["555", "Athena in Wonderland", "CaiusSPQR", "Crazymadlover", "Diniscoelho", "<PERSON>", "Eduardoad<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Imperadeiro98", "Jaideraf", "Jkb8", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Blade", "Mal<PERSON>aya", "<PERSON><PERSON>", "Mbs262", "MokaAkashiyaPT", "Nemo bis", "Ngl2016", "SandroHc", "Vitorvicentevalente", "Waldir", "<PERSON><PERSON><PERSON><PERSON>", "아라"]}, "smw-desc": "Tornar a sua wiki mais inteligível - para máquinas ''e'' seres humanos ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentação ''online''])", "smw-error": "Erro", "smw-upgrade-error": "O [https://www.semantic-mediawiki.org/ MediaWiki Semântico] foi instalado e ativado, mas falta uma [https://www.semantic-mediawiki.org/wiki/Help:Upgrade chave de atualização] apropriada.", "smw-upgrade-release": "Vers<PERSON>", "smw-upgrade-progress": "Progresso", "smw-upgrade-progress-explain": "É difícil antecipar quando a atualização estará concluída porque depende do tamanho do repositório de dados e do ''hardware'' disponível, e pode demorar algum tempo para concluir a atualização de wikis grandes.\n\nPara obter mais informações acerca do progresso da atualização, contacte o seu administrador local, por favor.", "smw-upgrade-progress-create-tables": "A criar (ou atualizar) tabelas e índices...", "smw-upgrade-progress-post-creation": "A executar tarefas pós-criação...", "smw-upgrade-progress-table-optimization": "A executar otimizações de tabelas...", "smw-upgrade-progress-supplement-jobs": "A adicionar processos suplementares...", "smw-upgrade-error-title": "Erro » MediaWiki Semântico", "smw-upgrade-error-why-title": "Porque estou a ver esta página?", "smw-upgrade-error-why-explain": "A estrutura interna da base de dados do MediaWiki Semântico mudou e necessita de alguns ajustamentos para ficar completamente funcional. Pode haver vários motivos para a mudança, incluindo: \n* Foram adicionadas propriedades fixas (requer configuração adicional de tabelas)\n* Uma atualização contém algumas mudanças nas tabelas ou índices, tornando obrigatória uma interceção antes de se aceder aos dados\n* Alterações do armazenamento ou do motor de consultas", "smw-upgrade-error-how-title": "Como corrijo este erro?", "smw-upgrade-error-how-explain-admin": "Um administrador (ou qualquer pessoa com privilégios de administrador) tem de executar o ''script'' de manutenção [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] do MediaWiki ou o [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] do MediaWiki Semântico.", "smw-upgrade-error-how-explain-links": "Para obter mais ajuda também pode consultar as seguintes páginas:\n* Instruções de [https://www.semantic-mediawiki.org/wiki/Help:Installation instalação]\n* Página de ajuda sobre [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting resolução de problemas]", "smw-extensionload-error-why-title": "Porque vejo esta página?", "smw-extensionload-error-why-explain": "A extensão <b>não</b> foi carregada usando <code>enableSemantics</code> mas, antes, ativada por outros meios, como usar <code>wfLoadExtension( 'SemanticMediaWiki' )</code> diretamente.", "smw-extensionload-error-how-title": "Como corrijo este erro?", "smw-extensionload-error-how-explain": "Para ativar a extensão e evitar problemas com declarações de espaços nominais e configurações pendentes, é necessário usar <code>enableSemantics</code> que garantirá que as variáveis obrigatórias sejam definidas antes de carregar a extensão através de <code>ExtensionRegistry</code>. \n\nConsulte a página de ajuda [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] para mais informações, por favor.", "smw-upgrade-maintenance-title": "Manutenção » MediaWiki Semântico", "smw-upgrade-maintenance-why-title": "Porque vejo esta página?", "smw-upgrade-maintenance-note": "Neste momento o sistema está a sofrer uma [https://www.semantic-mediawiki.org/wiki/Help:Upgrade atualização] da extensão [https://www.semantic-mediawiki.org/ MediaWiki Semântico] e do respetivo repositório de dados, e solicitamos a sua paciência de forma a permitir que a manutenção prossiga antes que a wiki seja novamente tornada acessível.", "smw-upgrade-maintenance-explain": "A extensão tenta minimizar o impacto e o período de indisponibilidade, adiando a maior parte das suas tarefas de manutenção para depois do <code>update.php</code> mas é preciso que algumas alterações relacionadas com a base de dados terminem primeiro para evitar inconsistências de dados. Estas alterações podem incluir: \n* Alteração da estrutura de tabelas, como a adição de campos novos ou a alteração de campos existentes \n* Alteração ou adição de índices sobre tabelas\n* Executar otimizações das tabelas (quando ativadas)", "smw-semantics-not-enabled": "A funcionalidade MediaWiki Semântico não foi ativada nesta wiki.", "smw_viewasrdf": "Feed RDF", "smw_finallistconjunct": "e", "smw-factbox-head": "... mais sobre \"$1\"", "smw-factbox-facts": "Factos", "smw-factbox-facts-help": "<PERSON>ra as declarações e factos que foram criados por um utilizador", "smw-factbox-attachments": "Anexos", "smw-factbox-attachments-value-unknown": "N/D", "smw-factbox-attachments-is-local": "É local", "smw-factbox-attachments-help": "Mostra os anexos disponíveis", "smw-factbox-facts-derived": "Factos derivados", "smw-factbox-facts-derived-help": "Mostra os factos que foram derivados das regras ou com a ajuda de outras técnicas de raciocínio", "smw_isspecprop": "Esta é uma propriedade especial nesta wiki.", "smw-concept-cache-header": "Utilização da cache", "smw-concept-cache-count": "A [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count cache de conceitos] contém {{PLURAL:$1|'''uma''' entidade|'''$1''' entidades}} ($2).", "smw-concept-no-cache": "Não há nenhuma cache disponível.", "smw_concept_description": "Descrição do conceito \"$1\"", "smw_no_concept_namespace": "Os conceitos só podem ser definidos em páginas do espaço nominal Concept:.", "smw_multiple_concepts": "Cada página de conceito só pode conter a definição de um conceito.", "smw_concept_cache_miss": "O conceito \"$1\" não pode ser usado neste momento, porque a configuração da wiki requer que ele seja calculado ''off-line''. Caso o problema não seja resolvido dentro de algum tempo, peça a um administrador da wiki que disponibilize este conceito.", "smw_noinvannot": "Não podem ser atribuídos valores a propriedades inversas.", "version-semantic": "Extensões semânticas", "smw_baduri": "Não são permitidos identificadores URI na forma \"$1\".", "smw_printername_count": "Contar resultados", "smw_printername_csv": "Exportação para CSV", "smw_printername_dsv": "Exportação para DSV", "smw_printername_debug": "Depurar a consulta (para peritos)", "smw_printername_embedded": "Incorporar o conteúdo da página", "smw_printername_json": "Exportação para JSON", "smw_printername_list": "Lista", "smw_printername_plainlist": "Lista simples", "smw_printername_ol": "Lista numerada", "smw_printername_ul": "Lista com marcadores", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "Tabela ampla", "smw_printername_template": "Predefinição", "smw_printername_templatefile": "Ficheiro da predefinição", "smw_printername_rdf": "Exportação para RDF", "smw_printername_category": "Categoria", "validator-type-class-SMWParamSource": "texto", "smw-paramdesc-limit": "O número máximo de resultados a serem devolvidos", "smw-paramdesc-offset": "A posição do primeiro resultado", "smw-paramdesc-headers": "Apresentar os nomes dos cabeçalhos e propriedades", "smw-paramdesc-mainlabel": "A etiqueta a atribuir ao nome da página principal", "smw-paramdesc-link": "Apresentar os valores na forma de hiperligações", "smw-paramdesc-intro": "O texto a apresentar antes dos resultados da consulta, caso existam", "smw-paramdesc-outro": "O texto a apresentar após os resultados da consulta, caso existam", "smw-paramdesc-default": "O texto a apresentar se a consulta não produzir resultados", "smw-paramdesc-sep": "O separador de resultados", "smw-paramdesc-propsep": "O separador entre as propriedades da entrada de um resultado", "smw-paramdesc-valuesep": "O separador entre os valores para uma propriedade de um resultado", "smw-paramdesc-showsep": "Mostrar separador no topo do ficheiro CSV (\"set=<value>\")", "smw-paramdesc-distribution": "Em vez de apresentar todos os valores, contar as respetivas ocorrências e apresentá-las.", "smw-paramdesc-distributionsort": "Ordenar a distribuição de valores pela contagem de ocorrências.", "smw-paramdesc-distributionlimit": "Limitar a distribuição de valores à contagem de ocorrência de apenas alguns valores.", "smw-paramdesc-aggregation": "Especifique com que é que a agregação se deve relacionar", "smw-paramdesc-template": "O nome da predefinição com a qual são apresentadas as impressões", "smw-paramdesc-columns": "O número de colunas em que os resultados serão apresentados", "smw-paramdesc-userparam": "Um valor fornecido a cada chamada da predefinição, se for usada uma predefinição", "smw-paramdesc-class": "Uma classe CSS adicional a ser definida para a lista", "smw-paramdesc-introtemplate": "O nome de uma predefinição para apresentar antes dos resultados da consulta, se existirem", "smw-paramdesc-outrotemplate": "O nome de uma predefinição para apresentar após os resultados da consulta, se existirem", "smw-paramdesc-embedformat": "O elemento HTML usado para definir cabeçalhos", "smw-paramdesc-embedonly": "Não mostrar cabeçalhos", "smw-paramdesc-table-class": "Uma classe CSS adicional a definir para a tabela", "smw-paramdesc-table-transpose": "Apresentar os cabeçalhos das tabelas na vertical e os resultados na horizontal", "smw-paramdesc-rdfsyntax": "A sintaxe RDF a utilizar", "smw-paramdesc-csv-sep": "Especifica um separador de colunas", "smw-paramdesc-csv-valuesep": "Especifica um separador de valores", "smw-paramdesc-csv-merge": "Fundir os valores de linhas e colunas que têm um identificador de assunto idêntico (aliás, primeira coluna)", "smw-paramdesc-csv-bom": "Adicionar um BOM (carácter para indicar a \"endianness\") no topo do ficheiro de saída", "smw-paramdesc-dsv-separator": "O separador a usar", "smw-paramdesc-dsv-filename": "O nome para o ficheiro DSV", "smw-paramdesc-filename": "O nome para o ficheiro de saída", "smw-smwdoc-description": "Apresenta uma tabela de todos os parâmetros que podem ser usados para o formato de resultados especificado, com os respetivos valores por omissão e descrições", "smw-smwdoc-default-no-parameter-list": "Este formato de resultado não fornece parâmetros específicos do formato.", "smw-smwdoc-par-format": "O formato de resultados para o qual será apresentada a documentação dos parâmetros.", "smw-smwdoc-par-parameters": "Os parâmetros a serem apresentados. \"specific\" (específicos) para aqueles adicionados pelo formato, \"base\" para aqueles disponíveis em todos os formatos, e \"all\" (todos) para ambos.", "smw-paramdesc-sort": "A propriedade pela qual a consulta será ordenada", "smw-paramdesc-order": "O sentido de ordenação da consulta", "smw-paramdesc-searchlabel": "Texto para continuação da pesquisa", "smw-paramdesc-named_args": "Nomear os argumentos passados à predefinição", "smw-paramdesc-template-arguments": "Define a forma como os argumentos nomeados são passados à predefinição", "smw-paramdesc-import-annotation": "Serão copiados dados anotados adicionais durante a análise sintática de um assunto", "smw-paramdesc-export": "Opção de exportação", "smw-paramdesc-prettyprint": "Um resultado com realce sintático que apresenta indentações e novas linhas adicionais", "smw-paramdesc-json-unescape": "O resultado irá conter barras sem caracteres de escape, e caracteres Unicode multibytes", "smw-paramdesc-json-type": "Tipo de seriação", "smw-paramdesc-source": "Fonte alternativa de consulta", "smw-paramdesc-jsonsyntax": "Sintaxe JSON a ser utilizada", "smw-printername-feed": "Feed RSS e Atom", "smw-paramdesc-feedtype": "Tipo de ''feed''", "smw-paramdesc-feedtitle": "O texto a ser usado como título de ''feed''", "smw-paramdesc-feeddescription": "O texto a ser usado como descrição do ''feed''", "smw-paramdesc-feedpagecontent": "O conteúdo da página a ser apresentado com o ''feed''", "smw-label-feed-description": "''Feed'' $2 $1", "smw-paramdesc-mimetype": "O tipo de multimédia (tipo MIME) para o ficheiro de saída.", "smw_iq_disabled": "As consultas semânticas foram impossibilitadas nesta wiki.", "smw_iq_moreresults": "… mais resultados", "smw_parseerror": "O valor fornecido não foi compreendido.", "smw_decseparator": ",", "smw_kiloseparator": "&nbsp;", "smw_notitle": "“$1” não pode ser usado como nome de uma página nesta wiki.", "smw_noproperty": "“$1” não pode ser usado como nome de uma propriedade nesta wiki.", "smw_wrong_namespace": "Só são permitidas aqui páginas do espaço nominal \"$1\".", "smw_manytypes": "Foi definido mais de um tipo para a propriedade.", "smw_emptystring": "Não são aceites textos vazios.", "smw_notinenum": "\"$1\" não está na lista ($2) dos [[Property:Allows value|valores permitidos]] para a propriedade \"$3\".", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" não está na lista ($2) de [[Property:Allows value|valores permitidos]] para a propriedade \"$3\".", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" não está no intervalo de \"$2\" especificado pela restrição [[Property:Allows value|permite valor]] para a propriedade \"$3\".", "smw-constraint-error": "Problema em restrição", "smw-constraint-error-suggestions": "Verifique as infringências e propriedades listadas, em conjunto com os seus valores anotados, para se certificar de que todos os requisitos das restrições são cumpridos.", "smw-constraint-error-limit": "A lista irá conter um máximo de $1 violações.", "smw_noboolean": "\"$1\" não é reconhecido como um valor booliano (verdadeiro/falso).", "smw_true_words": "verda<PERSON><PERSON>,v,sim,s", "smw_false_words": "falso,f,não,nao,n", "smw_nofloat": "“$1” não é um número.", "smw_infinite": "Números da dimensão de “$1” não são suportados.", "smw_unitnotallowed": "\"$1\" não está declarada como unidade de medida válida para esta propriedade.", "smw_nounitsdeclared": "Não foi declarada nenhuma unidade de medida para esta propriedade.", "smw_novalues": "Não foram especificados valores.", "smw_nodatetime": "A data “$1” não foi compreendida.", "smw_toomanyclosing": "Parece haver demasiadas ocorrências de \"$1\" na consulta.", "smw_noclosingbrackets": "Um uso de \"<nowiki>[[</nowiki>\" na sua consulta não foi fechado com o \"]]\" correspondente.", "smw_misplacedsymbol": "O símbolo “$1” foi usado num sítio onde não tem utilidade.", "smw_unexpectedpart": "A parte \"$1\" da consulta não foi compreendida.\nOs resultados poderão não ser os esperados.", "smw_emptysubquery": "Há uma subconsulta que não tem nenhuma condição válida.", "smw_misplacedsubquery": "Foi usada uma subconsulta num local onde não são permitidas subconsultas.", "smw_valuesubquery": "Não são suportadas subconsultas para valores da propriedade \"$1\".", "smw_badqueryatom": "Uma parte \"<nowiki>[[…]]</nowiki>\" da consulta não foi compreendida.", "smw_propvalueproblem": "O valor da propriedade “$1” não foi compreendido.", "smw_noqueryfeature": "Uma característica da consulta não é suportada nesta wiki e parte da consulta foi descartada ($1).", "smw_noconjunctions": "Conjunções em consultas não são suportadas nesta wiki e parte da consulta foi descartada ($1).", "smw_nodisjunctions": "Disjunções em consultas não são suportadas nesta wiki e parte da consulta foi descartada ($1).", "smw_querytoolarge": "Não foi possível considerar {{PLURAL:$2|a seguinte condição|as seguintes $2 condições}} da consulta (<code>$1</code>), devido às restrições da wiki para o tamanho e profundidade das consultas.", "smw_notemplategiven": "Para este formato de consulta funcionar, forneça um valor para o parâmetro \"template\".", "smw_db_sparqlqueryproblem": "Não foi possível obter da base de dados SPARQL o resultado da consulta. Este erro pode ser temporário ou indicativo de um defeito no ''software'' da base de dados.", "smw_db_sparqlqueryincomplete": "A consulta revelou-se difícil e foi interrompida. Podem faltar alguns resultados. Se possível, tente usar uma consulta mais simples, por favor.", "smw_type_header": "Propriedades do tipo “$1”", "smw_typearticlecount": "A apresentar {{PLURAL:$1|uma propriedade que usa|$1 propriedades que usam}} este tipo.", "smw_attribute_header": "Páginas que usam a propriedade “$1”", "smw_attributearticlecount": "A apresentar {{PLURAL:$1|uma página que usa|$1 páginas que usam}} esta propriedade.", "smw-propertylist-subproperty-header": "Subpropriedades", "smw-propertylist-redirect-header": "Sinónimos", "smw-propertylist-error-header": "Páginas com atribuições indevidas", "smw-propertylist-count": "A mostrar $1 {{PLURAL:$1|entidade relacionada|entidades relacionadas}}.", "smw-propertylist-count-with-restricted-note": "A mostrar $1 {{PLURAL:$1|entidade relacionada|entidades relacionadas}} (há mais disponíveis mas a apresentação está limitada a \"$2\").", "smw-propertylist-count-more-available": "A mostrar $1 {{PLURAL:$1|entidade relacionada|entidades relacionadas}} (há mais disponíveis).", "specialpages-group-smw_group-maintenance": "Manutenção", "specialpages-group-smw_group-properties-concepts-types": "Propriedades, conceitos e tipos", "specialpages-group-smw_group-search": "Navegar e pesquisar", "exportrdf": "Exportar páginas para RDF", "smw_exportrdf_docu": "Esta página permite-lhe obter dados de uma página com o formato RDF.\nPara exportar páginas, introduza os seus títulos na caixa de texto abaixo, um título por linha.", "smw_exportrdf_recursive": "Exportar recursivamente todas as páginas relacionadas.\nNote que o resultado pode ser volumoso!", "smw_exportrdf_backlinks": "Exportar também todas as páginas que referem as páginas exportadas.\nGera um RDF navegável.", "smw_exportrdf_lastdate": "Não exportar páginas que não tenham sido alteradas desde o ponto no tempo especificado.", "smw_exportrdf_submit": "Exportar", "uriresolver": "Resolvedor de identificadores URI", "properties": "<PERSON><PERSON><PERSON><PERSON>", "smw-categories": "Categorias", "smw_properties_docu": "As se<PERSON><PERSON> propriedades são usadas nesta wiki.", "smw_property_template": "$1 de tipo $2 ($3 {{PLURAL:$3|utilização|utilizações}})", "smw_propertylackspage": "<PERSON><PERSON> as propriedades devem ser descritas por uma página!", "smw_propertylackstype": "Não foi especificado nenhum tipo para esta propriedade (a presumir o tipo $1 por agora).", "smw_propertyhardlyused": "Esta propriedade é muito pouco usada nesta wiki!", "smw-property-name-invalid": "A propriedade $1 não pode ser usada (nome inválido).", "smw-property-name-reserved": "\"$1\" estava listado como sendo um nome reservado e não deve ser usado como propriedade. Talvez a seguinte [https://www.semantic-mediawiki.org/wiki/Help:Property_naming página de ajuda] contenha informação sobre a razão pela qual o nome estava reservado.", "smw-sp-property-searchform": "Apresentar as propriedades que contêm:", "smw-sp-property-searchform-inputinfo": "O texto de entrada é sensível a maiúsculas e minúsculas e, quando é usado para a filtragem, só são apresentadas as propriedades que correspondem à condição.", "smw-special-property-searchform": "Apresentar as propriedades que contêm:", "smw-special-property-searchform-inputinfo": "O texto de entrada é sensível a maiúsculas e minúsculas e, quando é usado para a filtragem, só são apresentadas as propriedades que correspondem à condição.", "smw-special-property-searchform-options": "Opções", "smw-special-wantedproperties-filter-label": "Filtro:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "não aprovadas", "smw-special-wantedproperties-filter-unapproved-desc": "Opção de filtro usada em relação com o modo de autoridade.", "concepts": "Conceitos", "smw-special-concept-docu": "Um [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceito] pode ser considerado uma \"categoria dinâmica\"; por exemplo, um conjunto de páginas que não são criadas manualmente, mas sim calculadas pelo MediaWiki Semântico a partir da descrição de uma determinada consulta.", "smw-special-concept-header": "Lista de conceitos", "smw-special-concept-count": "{{PLURAL:$1|O seguinte conceito está a ser incluído|Os $1 conceitos seguintes estão a ser incluídos}} na lista.", "smw-special-concept-empty": "Não foi encontrado nenhum conceito.", "unusedproperties": "Propriedades não usadas", "smw-unusedproperties-docu": "Esta página lista [https://www.semantic-mediawiki.org/wiki/Unused_properties propriedades não utilizadas] que foram declaradas, embora nenhuma outra página as utilize. Para uma visão diferenciada, consulte as páginas especiais com [[Special:Properties|todas as propriedades]] ou com as [[Special:WantedProperties|propriedades em falta]].", "smw-unusedproperty-template": "$1 do tipo $2", "wantedproperties": "Propriedades em falta", "smw-wantedproperties-docu": "Esta página lista [https://www.semantic-mediawiki.org/wiki/Wanted_properties propriedades em falta] que são utilizadas na wiki, mas que não têm uma página a descrevê-las. Para uma visão diferenciada, consulte as páginas especiais com [[Special:Properties|todas as propriedades]] ou com as [[Special:UnusedProperties|propriedades não utilizadas]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw-special-wantedproperties-docu": "Esta página lista [https://www.semantic-mediawiki.org/wiki/Wanted_properties propriedades em falta] que são utilizadas na wiki, mas que não têm uma página a descrevê-las. Para uma visão diferenciada, consulte as páginas especiais com [[Special:Properties|todas as propriedades]] ou com as [[Special:UnusedProperties|propriedades não utilizadas]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw_purge": "<PERSON><PERSON><PERSON><PERSON>", "smw-purge-update-dependencies": "O MediaWiki Semântico está a purgar a página atual devido a algumas dependências desatualizadas que detetou e que requerem uma atualização.", "smw-purge-failed": "O MediaWiki Semântico tentou purgar a página mas falhou", "types": "Tipos", "smw_types_docu": "Lista dos [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tipos de dados disponíveis] em que cada [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipo] representa um conjunto único de atributos, para descrever um valor em termos das características de armazenamento e apresentação que são hereditárias para uma propriedade atribuída.", "smw-special-types-no-such-type": "\"$1\" é desconhecido ou não foi especificado como tipo de dados válido.", "smw-statistics": "Estatísticas de semântica", "smw-statistics-cached": "Estatísticas semânticas (em cache)", "smw-statistics-entities-total": "Entidades (total)", "smw-statistics-entities-total-info": "Uma contagem estimada de linhas de entidades. <PERSON>lui propriedades, conceitos ou qualquer outra representação de objeto registado que requeira a atribuição de uma identificação.", "smw-statistics-property-instance": "{{PLURAL:$1|Valor|valores}} de propriedade (total)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Propriedade|Propriedades}}]] (total)", "smw-statistics-property-total-info": "O total de propriedades registadas.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Propriedade|Propriedades}} (total)", "smw-statistics-property-used": "{{PLURAL:$1|Propriedade (utilizada com pelo menos um valor)|Propriedades (utilizadas com pelo menos um valor)}}", "smw-statistics-property-page": "{{PLURAL:$1|Propriedade|Propriedades}} ({{PLURAL:$1|registada|registadas}} com uma página)", "smw-statistics-property-page-info": "Contagem de propriedades que têm uma página dedicada e uma descrição.", "smw-statistics-property-type": "{{PLURAL:$1|Propriedade|Propriedades}} ({{PLURAL:$1|atribuída|atribuídas}} a um tipo de dados)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Consulta|Consultas}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Consulta|Consultas}}]] ({{PLURAL:$1|incorporada|incorporadas}}, total)", "smw-statistics-query-format": "formato <code>$1</code>", "smw-statistics-query-size": "<PERSON><PERSON><PERSON>", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Conceito|Conceitos}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Conceito|Conceitos}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Subobjeto|Subobjetos}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Subobjeto|Subobjetos}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Tipo de dados|Tipos de dados}}]]", "smw-statistics-error-count": "{{PLURAL:$1|<PERSON><PERSON> de propriedade|<PERSON>ores de propriedades}} ([[Special:ProcessingErrorList|{{PLURAL:$1|anotação incorreta|anotações incorretas}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|<PERSON><PERSON> de propriedade|<PERSON>ores de propriedades}} ({{PLURAL:$1|anotação incorreta|anotações incorretas}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Entidade desatualizada|Entidades desatualizadas}}]", "smw-statistics-delete-count-info": "As entidades que foram marcadas para remoção devem ser eliminadas regularmente usando os ''scripts'' de manutenção fornecidos.", "smw_uri_doc": "O resolvedor de endereços URI implementa a [$1 descoberta TAG da W3C sobre o httpRange-14].\nCertifica-se de que os seres humanos não se tornem em sítios da Internet.", "ask": "Pesquisa semântica", "smw-ask-help": "Esta secção contém algumas hiperligações que explicam como usar a sintaxe <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecionar páginas] descreve como selecionar páginas e construir condições\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadores de pesquisa] lista os operadores de pesquisa disponíveis, incluindo os para consultas de intervalos e consultas com caracteres de substituição\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Apresentar informação] descreve o uso de instruções de impressão e de opções de formatação", "smw_ask_sortby": "Ordenar por coluna (opcional)", "smw_ask_ascorder": "Ascendente", "smw_ask_descorder": "Descendente", "smw-ask-order-rand": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Procurar resultados", "smw_ask_editquery": "Editar consulta", "smw_add_sortcondition": "[Adicionar condição de ordenação]", "smw-ask-sort-add-action": "Adicionar condição de ordenação", "smw_ask_hidequery": "Esconder consulta (vista compacta)", "smw_ask_help": "Ajuda sobre consultas", "smw_ask_queryhead": "Condição", "smw_ask_printhead": "Seleção de dados adicionais para apresentar", "smw_ask_printdesc": "(adicionar um nome de propriedade por linha)", "smw_ask_format_as": "Formatar como:", "smw_ask_defaultformat": "<PERSON><PERSON><PERSON>", "smw_ask_otheroptions": "Outras opções", "smw-ask-otheroptions-info": "Esta secção contém opções que alteram as instruções de apresentação. Pode ver as descrições dos parâmetros colocando o ponteiro do rato sobre eles.", "smw-ask-otheroptions-collapsed-info": "Por favor, use o sinal de mais para ver todas as opções disponíveis", "smw_ask_show_embed": "Mostrar código para incorporação", "smw_ask_hide_embed": "Ocultar código de incorporação", "smw_ask_embed_instr": "Para incorporar esta consulta numa página da wiki, use o código abaixo.", "smw-ask-delete": "Remover", "smw-ask-sorting": "Ordenação", "smw-ask-options": "Opções", "smw-ask-options-sort": "Opções de ordenação", "smw-ask-format-options": "Formato e opções", "smw-ask-parameters": "Parâmetros", "smw-ask-search": "Pesquisa", "smw-ask-debug": "Debug", "smw-ask-debug-desc": "Gera informação para despistagem de erros das consultas", "smw-ask-no-cache": "Desativar cache de consultas", "smw-ask-no-cache-desc": "Resultados sem cache de consulta", "smw-ask-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-empty": "<PERSON><PERSON> as entradas", "smw-ask-download-link-desc": "Descarregar os resultados da consulta no formato $1", "smw-ask-format": "Formato", "smw-ask-format-selection-help": "Ajude com o formato selecionado: $1", "smw-ask-condition-change-info": "A condição foi alterada e o motor de pesquisa tem de voltar a executar a consulta para produzir resultados que correspondam aos novos requisitos.", "smw-ask-input-assistance": "Assistência de preenchimento", "smw-ask-condition-input-assistance": "É fornecida [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance ajuda de introdução] nos campos de impressão, ordenação e condição. O campo condição requer o uso de um dos seguintes prefixos:", "smw-ask-condition-input-assistance-property": "<code>p:</code> para obter as sugest<PERSON><PERSON> de propriedades (p. ex.: <code>[[p:Tem ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> para obter as sugestões de categorias", "smw-ask-condition-input-assistance-concept": "<code>con:</code> para obter as sugestões de conceitos", "smw-ask-format-change-info": "O formato foi modificado e a consulta tem de voltar a ser executada para produzir resultados que correspondam aos novos parâmetros e opções de visualização.", "smw-ask-format-export-info": "O formato selecionado é um formato de exportação que não tem representação visual, portanto os resultados só são fornecidos na forma de ficheiro.", "smw-ask-query-search-info": "A consulta <code><nowiki>$1</nowiki></code> foi realizada pelo servidor {{PLURAL:$3|1=<code>$2</code> (da cache)|<code>$2</code> (da cache)|<code>$2</code>}} em $4 {{PLURAL:$4|segundo|segundos}}.", "smw-ask-extra-query-log": "Registo de consultas", "smw-ask-extra-other": "Outro", "searchbyproperty": "Pesquisa por propriedade", "processingerrorlist": "Lista de erros de processamento", "constrainterrorlist": "Lista de erros de restrições", "propertylabelsimilarity": "Relatório de semelhanças das etiquetas de propriedade", "missingredirectannotations": "Anotações de redirecionamento em falta", "smw-processingerrorlist-intro": "A seguinte lista dá uma perspetiva geral das [https://www.semantic-mediawiki.org/wiki/Processing_errors falhas de processamento] que surgiram, relacionadas com o [https://www.semantic-mediawiki.org/ MediaWiki Semântico]. Recomenda-se monitorizar esta lista regularmente e corrigir as anotações de valores inválidos.", "smw-constrainterrorlist-intro": "A seguinte lista dá uma perspetiva geral dos [https://www.semantic-mediawiki.org/wiki/Constraint_errors erros de restrições] que surgiram, relacionados com o [https://www.semantic-mediawiki.org/ MediaWiki Semântico]. Recomenda-se monitorizar esta lista regularmente e corrigir as anotações de valores inválidos.", "smw-missingredirects-intro": "A seguinte secção lista as páginas que têm anotações de [https://www.semantic-mediawiki.org/wiki/Redirects redirecionamento] em falta no MediaWiki Semântico (comparando com a informação armazenada no MediaWiki) e permite restaurar essas anotações quer [https://www.semantic-mediawiki.org/wiki/Help:Purge purgando] manualmente a página quer executando o ''script'' de manutenção <code>rebuildData.php</code> (com a opção <code>--redirects</code>).", "smw-missingredirects-list": "Páginas com anotações em falta", "smw-missingredirects-list-intro": "A mostrar $1 {{PLURAL:$1|página|páginas}} com anotações de redirecionamento em falta.", "smw-missingredirects-noresult": "Não foi encontrada nenhuma anotação de redirecionamento em falta.", "smw_sbv_docu": "<PERSON><PERSON><PERSON> todas as páginas que têm uma determinada propriedade e valor.", "smw_sbv_novalue": "Introduza um valor válido para a propriedade, ou veja todos os valores válidos da propriedade “$1”.", "smw_sbv_displayresultfuzzy": "Uma lista de todas as páginas que têm a propriedade \"$1\" com o valor \"$2\".\nComo houve poucos resultados, também são apresentados valores próximos.", "smw_sbv_property": "Propriedade:", "smw_sbv_value": "Valor:", "smw_sbv_submit": "Procurar resultados", "browse": "Navegar pela wiki", "smw_browselink": "<PERSON><PERSON><PERSON> propriedades", "smw_browse_article": "Introduza o nome da página a partir da qual deseja começar a navegar.", "smw_browse_go": "Prosseguir", "smw_browse_show_incoming": "<PERSON>rar propriedades afluentes", "smw_browse_hide_incoming": "Ocultar propriedades afluentes", "smw_browse_no_outgoing": "Esta página não tem propriedades.", "smw_browse_no_incoming": "<PERSON><PERSON><PERSON><PERSON> das propriedades aponta para esta página.", "smw-browse-from-backend": "Neste momento está a ser obtida informação do servidor.", "smw-browse-intro": "Esta página fornece detalhes sobre um assunto ou uma instância de uma entidade. Introduza o nome de um objeto a ser inspecionado, por favor.", "smw-browse-invalid-subject": "A validação do assunto terminou com um erro \"$1\".", "smw-browse-api-subject-serialization-invalid": "O assunto tem um formato de seriação inválido.", "smw-browse-js-disabled": "É provável que o JavaScript esteja desativado ou indisponível, e recomendamos que utilize um ''browser'' que o suporte. Talvez encontre outras opções na página de configuração do parâmetro [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Mostrar grupos", "smw-browse-hide-group": "Ocultar grupos", "smw-noscript": "Esta página ou ação requer o JavaScript para funcionar. Ative o JavaScript no seu browser ou utilize um browser que o suporte, para que esta funcionalidade possa ser fornecida como pedido. Para mais informações, consulte a  página de ajuda [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "<PERSON>ti<PERSON><PERSON> da propriedade inversa", "pageproperty": "Pesquisa das propriedades das páginas", "pendingtasklist": "Lista de tarefas pendentes", "smw_pp_docu": "Introduza uma página e propriedade, ou só uma propriedade, para obter todos os valores atribuídos.", "smw_pp_from": "Da página:", "smw_pp_type": "Propriedade:", "smw_pp_submit": "Procurar resultados", "smw-prev": "{{PLURAL:$1|anterior|$1 anteriores}}", "smw-next": "{{PLURAL:$1|seguinte|$1 seguintes}}", "smw_result_prev": "Anteriores", "smw_result_next": "<PERSON><PERSON><PERSON>", "smw_result_results": "Resul<PERSON><PERSON>", "smw_result_noresults": "Não há resultados.", "smwadmin": "Painel de controlo do MediaWiki Semântico", "smw-admin-statistics-job-title": "Estatísticas de tarefas", "smw-admin-statistics-job-docu": "As estatísticas de tarefas apresentam informação sobre tarefas agendadas do MediaWiki Semântico que ainda não foram executadas. O número de tarefas pode ter ligeiras imprecisões ou conter tentativas falhadas; consulte o [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manual] para mais informações.", "smw-admin-statistics-querycache-title": "<PERSON><PERSON> de <PERSON>", "smw-admin-statistics-querycache-disabled": "A [https://www.semantic-mediawiki.org/wiki/QueryCache ''cache'' de consultas] não foi ativada nesta wiki, portanto não há estatísticas disponíveis.", "smw-admin-statistics-querycache-legend": "As estatísticas da ''cache'' irão conter dados acumulados provisórios e derivados, incluindo:\n* \"misses\" (falhas) o número total de tentativas de obter dados da ''cache'' com respostas inatingíveis, forçando a obtenção direta do repositório (base de dados, repositório de triplas, etc.)\n* \"deletes\" (eliminações) o número total de operações de despejo da ''cache'' (por dependências de purga ou de consulta)\n* \"hits\" (acertos) contém o número de obtenções de dados da ''cache'', tendo por fonte quer consultas incorporadas (consultas feitas a partir de uma página da wiki) quer não incorporadas (se ativas, pedidas por páginas como Special:Ask ou pela API)\n* \"medianRetrievalResponseTime\" (mediana do tempo de resposta) um valor indicativo da mediana do tempo de resposta (em segundos) para pedidos de obtenção servidos, ou não, pela ''cache'' durante o período de execução do processo de recolha de dados\n* \"noCache\" (sem ''cache'') indica a quantidade de pedidos sem tentativa de obter dados da ''cache'' (consultas com limite=0, opção 'sem ''cache''', etc.)", "smw-admin-statistics-section-explain": "A secção fornece estatísticas adicionais para administradores.", "smw-admin-statistics-semanticdata-overview": "Visão geral", "smw-admin-permission-missing": "O acesso a esta página foi bloqueado devido à falta de permissões. Consulte a página de ajuda sobre [https://www.semantic-mediawiki.org/wiki/Help:Permissions permissões] para detalhes sobre as definições necessárias, por favor.", "smw-admin-setupsuccess": "O motor de armazenamento foi configurado.", "smw_smwadmin_return": "Voltar a $1", "smw_smwadmin_updatestarted": "Foi iniciado um novo processo de atualização para recarregar os dados semânticos.\nTodos os dados armazenados serão reconstruidos ou reparados, conforme for necessário.\nPode seguir o progresso da atualização nesta página especial.", "smw_smwadmin_updatenotstarted": "Já existe um processo de atualização em curso.\nNão foi criado outro.", "smw_smwadmin_updatestopped": "Todos os processos de atualização existentes foram parados.", "smw_smwadmin_updatenotstopped": "Para parar o processo de atualização em curso, deve marcar a caixa de seleção para indicar que tem realmente a certeza.", "smw-admin-docu": "Esta página especial auxilia-o durante a instalação, atualização, manutenção e utilização do <a href=\"https://www.semantic-mediawiki.org\">MediaWiki Semântico</a> e fornece outras funções e tarefas administrativas, assim como estatísticas.\nLembre-se de efetuar cópias de segurança dos dados importantes antes de executar funções administrativas.", "smw-admin-environment": "Ambiente de ''software''", "smw-admin-db": "Configuração da base de dados", "smw-admin-db-preparation": "A inicialização da tabela está em curso e pode demorar algum tempo até os resultados serem apresentados, dependendo do tamanho da tabela e de possíveis otimizações da mesma.", "smw-admin-dbdocu": "O MediaWiki Semântico requer uma estrutura própria de dados (que é independente do MediaWiki e, portanto, não afeta o resto da instalação do MediaWiki), para armazenar os dados semânticos.\nEsta função de preparação pode ser executada várias vezes sem causar quaisquer danos, mas é necessária apenas uma vez, na instalação ou durante uma atualização.", "smw-admin-permissionswarn": "Se a operação falhar com erros de SQL, é provável que o utilizador da base de dados usado pela sua wiki (consulte o seu ficheiro \"LocalSettings.php\") não tenha as permissões necessárias.\nConceda a este utilizador permissões adicionais para criar e eliminar tabelas, introduza temporariamente as credenciais do seu super-utilizador (''root'') da base de dados no ficheiro \"LocalSettings.php\", ou use o ''script'' de manutenção <code>setupStore.php</code>, o qual pode usar as credenciais de um administrador.", "smw-admin-dbbutton": "<PERSON><PERSON><PERSON><PERSON> ou at<PERSON><PERSON><PERSON> tabelas", "smw-admin-announce": "Anuncie a sua wiki", "smw-admin-announce-text": "Se a sua wiki é pública, pode registá-la na <a href=\"https://wikiapiary.com\">Wiki Apiário</a>, a wiki que regista wikis.", "smw-admin-deprecation-notice-title": "Avisos de descontinuação", "smw-admin-deprecation-notice-docu": "A seguinte secção contém definições que já foram descontinuadas ou removidas mas que se detetou estarem ativadas nesta wiki. É esperado que qualquer atualização futura deixe de suportar estas configurações.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi descontinuado e será removido na versão $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> irá remover (ou substituir) {{PLURAL:$2|a seguinte opção|as seguintes opções}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> foi descontinuado e será removido na versão $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi substituído por <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi substituído por <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "{{PLURAL:$2|Opção|Opções}} de <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> está a ser substituído por <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi removido na versão $2", "smw-admin-deprecation-notice-title-notice": "Definições descontinuadas", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Definições descontinuadas</b> mostra definições que foi detetado estarem a ser usadas nesta wiki e que está planeado serem removidas ou alteradas numa versão futura.", "smw-admin-deprecation-notice-title-replacement": "Definições substituídas ou com nome alterado", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Definições substituídas ou cujo nome foi alterado</b> contém definições que sofreram alteração do nome ou outra modificação e é recomendado que seja atualizado de imediato o nome ou o formato das mesmas.", "smw-admin-deprecation-notice-title-removal": "Definições removidas", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Definições removidas</b> identifica definições que foram removidas numa versão anterior mas foi detetado que continuam a ser usadas nesta wiki.", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw-smwadmin-refresh-title": "Reparação e atualização de dados", "smw_smwadmin_datarefresh": "Reconstrução de dados", "smw_smwadmin_datarefreshdocu": "É possível restaurar todos os dados do MediaWiki Semântico baseado no conteúdo atual da wiki.\n<PERSON>to pode ser útil para reparar dados corrompidos ou para refrescar os dados se o formato interno tiver sido alterado devido a alguma evolução do ''software''.\nA atualização é executada página a página e não ficará completa de imediato.\nO seguinte mostra se uma atualização está a decorrer e permite-lhe iniciar ou parar atualizações (a menos que esta funcionalidade tenha sido desativada pelo administrador do sítio).", "smw_smwadmin_datarefreshprogress": "<strong>Já se encontra em progresso uma atualização.</strong>\nÉ normal que a atualização progrida lentamente, já que apenas refresca dados em pequenos blocos de cada vez que um utilizador acede à wiki.\nPara terminar esta atualização mais rapidamente, pode executar o ''script'' de manutenção do MediaWiki <code>runJobs.php</code> (use a opção <code>--maxjobs 1000</code> para restringir o número de atualizações feitas em cada bloco).\nProgresso estimado da atualização em curso:", "smw_smwadmin_datarefreshbutton": "Programar a reconstrução dos dados", "smw_smwadmin_datarefreshstop": "Parar esta atualização", "smw_smwadmin_datarefreshstopconfirm": "<PERSON>m, {{GENDER:$1|tenho}} a certeza.", "smw-admin-job-scheduler-note": "As tarefas (ativadas) desta secção são executadas através da fila de tarefas para evitar situações de impasse durante a sua execução. A [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas] é responsável pelo processamento, pelo que é crítico que o ''script'' de manutenção <code>runJobs.php</code> tenha uma capacidade adequada (ver também o parâmetro de configuração <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Eliminação de entidades desatualizadas", "smw-admin-outdateddisposal-intro": "Algumas atividades (a alteração de um tipo de propriedade, a remoção de páginas da wiki, ou a correção de valores em erro) resultam em [https://www.semantic-mediawiki.org/wiki/Outdated_entities entidades desatualizadas] e é recomendado que estas sejam removidas periodicamente para libertar o respetivo espaço nas tabelas.", "smw-admin-outdateddisposal-active": "Foi agendado um processo de eliminação de entidades desatualizadas.", "smw-admin-outdateddisposal-button": "Agendar eliminação", "smw-admin-feature-disabled": "Esta funcionalidade foi desativada nesta wiki. Consulte a página de ajuda das <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">configurações</a> ou contacte o administrador do sistema.", "smw-admin-propertystatistics-title": "Reconstrução das estatísticas das propriedades", "smw-admin-propertystatistics-intro": "Reconstrói todas as estatísticas de utilização de propriedades e nesse processo atualiza e corrige a [https://www.semantic-mediawiki.org/wiki/help:Property_usage_count contagem de usos] de propriedades.", "smw-admin-propertystatistics-active": "Foi agendado um processo de reconstrução das estatísticas de propriedades.", "smw-admin-propertystatistics-button": "Agendar reconstrução das estatísticas", "smw-admin-fulltext-title": "Reconstrução da pesquisa de texto completo", "smw-admin-fulltext-intro": "Reconstrói o índice de pesquisas com base nas tabelas de propriedade, usando um tipo de dados que suporta a [https://www.semantic-mediawiki.org/wiki/full-text pesquisa de texto integral]. Alterações das normas de indexação (''stopwords'' mudadas, novo ''stemmer'', etc.) ou uma tabela nova ou mudada requerem que este processo volte a ser executado.", "smw-admin-fulltext-active": "Foi agendado um processo de reconstrução de pesquisa de texto integral.", "smw-admin-fulltext-button": "Agendar reconstrução de pesquisa de texto integral", "smw-admin-support": "Obter suporte", "smw-admin-supportdocu": "São fornecidos vários recursos para ajudar em caso de problemas:", "smw-admin-installfile": "Se tiver problemas com a instalação, comece por verificar as linhas orientadoras no <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">ficheiro INSTALL</a> e a <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">página de instalação</a>.", "smw-admin-smwhomepage": "A documentação completa para o utilizador do MediaWiki Semântico está em <b><a href=\"https://www.semantic-mediawiki.org/wiki/P%C3%A1gina_principal_pt\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Pode reportar defeitos no <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">sistema de registo de defeitos</a>, onde a página para <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">reportar defeitos</a> fornece instruções para criar relatórios eficazes.", "smw-admin-questions": "Se tem mais questões ou sugestões, junte-se à discussão na <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">lista de divulgação</a> do MediaWiki Semântico.", "smw-admin-other-functions": "Outras funções", "smw-admin-statistics-extra": "Funções estatísticas", "smw-admin-statistics": "Estatísticas", "smw-admin-supplementary-section-title": "Funções suplementares", "smw-admin-supplementary-section-subtitle": "Funções centrais suportadas", "smw-admin-supplementary-section-intro": "Esta secção disponibiliza funções adicionais fora do âmbito das atividades de manutenção e é possível que algumas das funções listadas (ver a [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentação]) estejam restringidas ou indisponíveis e, portanto, inacessíveis nesta wiki.", "smw-admin-supplementary-settings-title": "Configuração e definições", "smw-admin-supplementary-settings-intro": "<u>$1</u> mostra parâmetros que definem o comportamento do MediaWiki Semântico", "smw-admin-main-title": "MediaWiki Semântico » $1", "smw-admin-supplementary-operational-statistics-title": "Estatísticas operacionais", "smw-admin-supplementary-operational-statistics-short-title": "estatísticas operacionais", "smw-admin-supplementary-operational-statistics-intro": "Apresenta um conjunto alargado de <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Pesquisa e eliminação de entidades", "smw-admin-supplementary-idlookup-short-title": "pesquisa e eliminação de entidades", "smw-admin-supplementary-idlookup-intro": "Suporta uma função simples de <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Consulta de entidades duplicadas", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> para encontrar entidades que são categorizadas como duplicadas para a matriz de tabelas selecionada", "smw-admin-supplementary-duplookup-docu": "Esta página lista entradas de tabelas selecionadas que tenham sido categorizadas como [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities duplicadas]. Entradas duplicadas só devem ocorrer (se de todo) em ocasiões raras, possivelmente causadas por uma atualização interrompida por uma transação de rollback não concluída.", "smw-admin-supplementary-operational-statistics-cache-title": "Estatísticas da cache", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> mostra um conjunto selecionado de estatísticas relacionadas com a cache", "smw-admin-supplementary-operational-table-statistics-title": "Estatísticas de tabelas", "smw-admin-supplementary-operational-table-statistics-short-title": "estatísticas de tabelas", "smw-admin-supplementary-operational-table-statistics-intro": "Gera <u>$1</u> para um conjunto selecionado de tabelas", "smw-admin-supplementary-operational-table-statistics-explain": "Esta secção contém estatísticas de tabelas selecionadas para ajudar os administradores e curadores de dados a tomarem decisões informadas sobre o estado do servidor e do motor de armazenamento.", "smw-admin-supplementary-operational-table-statistics-legend": "<PERSON><PERSON><PERSON> as chaves usadas para as estatísticas de tabelas, incluindo:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> número total de linhas numa tabela", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> último identificador atualmente em uso\n* <code>duplicate_count</code> número de duplicados encontrados na tabela id_table (ver também [[Special:SemanticMediaWiki/duplicate-lookup|Pesquisa de entidades duplicadas]]) \n* <code>rows.rev_count</code> número de linhas que têm um identificador de revisão (revision_id) atribuído que indica um hiperligação direta para uma página da wiki\n* <code>rows.smw_namespace_group_by_count</code> números de linhas agregadas para espaços nominais usados na tabela\n* <code>rows.smw_proptable_hash.query_match_count</code> número de subobjetos de consulta com uma referência de tabela correspondente \n* <code>rows.smw_proptable_hash.query_null_count</code> número de subobjetos de consulta sem uma referência de tabela (sem hiperligação, referência flutuante)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> percentagem dos termos que são únicos (uma percentagem baixa indica que termos repetitivos ocupam o conteúdo e índice da tabela)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> número de termos que só aparecem uma vez\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> número de termos que aparecem mais do que uma vez", "smw-admin-supplementary-elastic-version-info": "Vers<PERSON>", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> mostra detalhes sobre configurações e estatísticas de indexação", "smw-admin-supplementary-elastic-docu": "Esta página contém informação sobre as configurações, os mapeamentos, o estado e as estatísticas dos índices, de um ''cluster'' Elasticsearch que está ligado ao MediaWiki Semântico e ao respetivo [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Funções suportadas", "smw-admin-supplementary-elastic-settings-title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (índices)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> usadas pelo Elasticsearch para gerir os índices do MediaWiki Semântico", "smw-admin-supplementary-elastic-mappings-title": "Mapeamentos", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> para listar índices e mapeamentos de campos", "smw-admin-supplementary-elastic-mappings-docu": "Esta página contém detalhes do mapeamento de campos usados pelo índice atual. É recomendado que os mapeamentos sejam monitorizados em ligação com o limite <code>index.mapping.total_fields.limit</code> (especifica o número máximo de campos permitidos num índice).", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code> refere-se à contagem de campos nucleares indexados, enquanto que <code>nested_fields</code> se refere a uma contagem acumulada de campos adicionais atribuídos a um campo nuclear para apoiar padrões específicos de pesquisa estruturada.", "smw-admin-supplementary-elastic-mappings-summary": "Resumo", "smw-admin-supplementary-elastic-mappings-fields": "Mapeamentos de campos", "smw-admin-supplementary-elastic-nodes-title": "<PERSON>ós", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> mostra estatísticas dos nós", "smw-admin-supplementary-elastic-indices-title": "Índices", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> fornece uma visão geral dos índices disponíveis e das suas estatísticas", "smw-admin-supplementary-elastic-statistics-title": "Estatísticas", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> mostra as estatísticas ao nível dos índices", "smw-admin-supplementary-elastic-statistics-docu": "Esta página fornece uma perspetiva das estatísticas de índices para as diferentes operações que estão a ocorrer ao nível do índice. As estatísticas produzidas estão agrupadas por agregações primárias e totais. A [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html página de ajuda] contém uma descrição detalhada das estatísticas de índices disponíveis.", "smw-admin-supplementary-elastic-status-replication": "Estado da replicação", "smw-admin-supplementary-elastic-status-last-active-replication": "Última replicação ativa: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Intervalo de atualização: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Tarefas de recuperação em atraso: $1 (estimativa)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Tarefas de ingestão (de ficheiros) em atraso: $1 (estimativa)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replicação trancada: $1 (recriação em progresso)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Monitorização de replicação (ativa): $1", "smw-admin-supplementary-elastic-replication-header-title": "Estado da replicação", "smw-admin-supplementary-elastic-replication-function-title": "Replicação", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> mostra informações sobre replicações falhadas", "smw-admin-supplementary-elastic-replication-docu": "Esta página fornece informação sobre o  [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring estado de replicação] das entidades reportadas como tendo problemas com o ''cluster'' Elasticsearch. É recomendado rever as entidades listadas e purgar o conteúdo para confirmar que era um problema temporário.", "smw-admin-supplementary-elastic-replication-files-docu": "Deve ser notado que, para a lista de ficheiros, é obrigatório que a tarefa de [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion ingestão de ficheiros] seja executada primeiro e conclua o seu processamento.", "smw-admin-supplementary-elastic-replication-files": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-endpoints": "Pontos de terminação", "smw-admin-supplementary-elastic-config": "Configurações", "smw-admin-supplementary-elastic-no-connection": "De momento, a wiki '''não consegue''' estabelecer uma ligação ao ''cluster'' Elasticsearch; contacte o administrador da wiki para investigar o problema, por favor, porque este torna indisponíveis as capacidades de indexação e consulta do sistema.", "smw-list-count": "A lista contém $1 {{PLURAL:$1|entrada|entradas}}.", "smw-property-label-uniqueness": "Foi encontrada uma correspondência entre a etiqueta \"$1\" e pelo menos uma outra representação de propriedade. Consulte a [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness página de ajuda] sobre a resolução deste problema, por favor.", "smw-property-label-similarity-title": "Relatório de semelhanças das etiquetas de propriedade", "smw-property-label-similarity-intro": "<u>$1</u> calcula as semelhanças das etiquetas de propriedade existentes", "smw-property-label-similarity-threshold": "Patamar:", "smw-property-label-similarity-type": "Apresentar o identificador do tipo", "smw-property-label-similarity-noresult": "Não foram encontrados resultados para as opções selecionadas.", "smw-property-label-similarity-docu": "Esta página compara a [https://www.semantic-mediawiki.org/wiki/Property_similarity distância da semelhança sintática] (não confundir com uma semelhança semântica ou lexical) entre etiquetas de propriedades e reporta-as se excederem o patamar. O relatório pode ajudar a filtrar propriedades com erros ortográficos ou as propriedades equivalentes que representam o mesmo conceito (ver a página especial [[Special:Properties|Propriedades]] para clarificar o conceito e a utilização das propriedades reportadas). O patamar pode ser ajustado para aumentar ou reduzir a distância usada para a correspondência por aproximação. <code>[[Property:$1|$1]]</code> é usado para isentar propriedades desta análise.", "smw-admin-operational-statistics": "Esta página contém estatísticas operacionais recolhidas em (ou por) funções relacionadas com o MediaWiki Semântico. Encontra uma lista expandida de estatísticas específicas da wiki [[Special:Statistics|<b>aqui</b>]].", "smw_adminlinks_datastructure": "Estrutura de dados", "smw_adminlinks_displayingdata": "Apresentação de dados", "smw_adminlinks_inlinequerieshelp": "Ajuda para consultas dinâmicas (''inline queries'')", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Número de utilizações] estimado: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Propriedade definida pelo {{PLURAL:$1|utilizador|sistema}}", "smw-property-indicator-last-count-update": "Número estimado de utilizações\nÚltima atualização: $1", "smw-concept-indicator-cache-update": "Contagem da cache\nÚltima atualização: $1", "smw-createproperty-isproperty": "É uma propriedade do tipo $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|O valor permitido para esta propriedade é|Os valores permitidos para esta propriedade são}}:", "smw-paramdesc-category-delim": "O delimitador", "smw-paramdesc-category-template": "Uma predefinição para formatação dos elementos", "smw-paramdesc-category-userparam": "Um parâmetro para passar à predefinição", "smw-info-par-message": "A mensagem a ser apresentada.", "smw-info-par-icon": "Ícone a ser mostrado, \"informação\" ou \"aviso\".", "prefs-smw": "MediaWiki Semântico", "prefs-general-options": "Opções gerais", "prefs-extended-search-options": "Pesquisa avançada", "prefs-ask-options": "Pesquisa semântica", "smw-prefs-intro-text": "O [https://www.semantic-mediawiki.org/ MediaWiki Semântico] e as extensões relacionadas fornecem preferências individuais para um grupo de funcionalidades e funções selecionadas. Está disponível uma lista de definições individuais com as suas descrições e caraterísticas na seguinte [https://www.semantic-mediawiki.org/wiki/Help:User_preferences página de ajuda].", "smw-prefs-ask-options-tooltip-display": "Mostrar o texto do parâmetro na forma de dica informativa na página especial de [[Special:Ask|construção de consultas]] #ask.", "smw-prefs-ask-options-compact-view-basic": "Ativar a vista compacta básica", "smw-prefs-help-ask-options-compact-view-basic": "Se ativado, apresenta um conjunto reduzido de hiperligações na vista compacta Special:Ask.", "smw-prefs-general-options-time-correction": "Ativar a correção da hora para as páginas especiais, usando a preferência do [[Special:Preferences#mw-prefsection-rendering|fuso horário]].", "smw-prefs-general-options-jobqueue-watchlist": "Mostrar a lista de vigilância da fila de tarefas na minha barra pessoal", "smw-prefs-help-general-options-jobqueue-watchlist": "Se ativado, mostra uma [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist lista] de tarefas selecionadas pendentes, com os respetivos tamanhos de fila estimados.", "smw-prefs-general-options-disable-editpage-info": "Desativar o texto introdutório na página de edição", "smw-prefs-general-options-disable-search-info": "<PERSON>ativar as informações de suporte da sintaxe na página de pesquisa padrão", "smw-prefs-general-options-suggester-textinput": "Ativar o auxiliar de preenchimento para as entidades semânticas", "smw-prefs-help-general-options-suggester-textinput": "Se ativado, permite usar um [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance auxiliar de preenchimento] para encontrar propriedades, conceitos e categorias num contexto de entrada.", "smw-prefs-general-options-show-entity-issue-panel": "Mostrar o painel de problemas de entidades", "smw-prefs-help-general-options-show-entity-issue-panel": "Se ativado, executa verificações de integridade em cada página e mostra o [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel painel de problemas de entidades].", "smw-ui-tooltip-title-property": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-quantity": "Conversão de unidades", "smw-ui-tooltip-title-info": "Informação", "smw-ui-tooltip-title-service": "Ligações de serviços", "smw-ui-tooltip-title-warning": "Aviso", "smw-ui-tooltip-title-error": "Erro", "smw-ui-tooltip-title-parameter": "Parâmetro", "smw-ui-tooltip-title-event": "Evento", "smw-ui-tooltip-title-note": "<PERSON>a", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "Referência", "smw_unknowntype": "O tipo \"$1\" desta propriedade é inválido", "smw-concept-cache-text": "O conceito tem no total $1 {{PLURAL:$1|página|páginas}}, e foi atualizado pela última vez a $3, às $2.", "smw_concept_header": "Páginas do conceito \"$1\"", "smw_conceptarticlecount": "A apresentar $1 {{PLURAL:$1|página|páginas}} abaixo.", "smw-qp-empty-data": "Não foi possível apresentar os dados pedidos devido a alguns critérios de seleção insuficientes.", "right-smw-admin": "Acesso às tarefas de administração (MediaWiki Semântico)", "right-smw-patternedit": "Acesso de edição para manter as expressões regulares e os padrões permitidos (MediaWiki Semântico)", "right-smw-pageedit": "Acesso para edição de páginas com a anotação <code>Está protegida contra edições</code> (MediaWiki Semântico)", "right-smw-schemaedit": "Editar [https://www.semantic-mediawiki.org/wiki/Help:Schema páginas de esquema] (MediaWiki Semântico)", "right-smw-viewjobqueuewatchlist": "Acesso à funcionalidade de [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist páginas vigiadas] da fila de tarefas (MediaWiki Semântico)", "right-smw-viewentityassociatedrevisionmismatch": "Aceder a informações sobre erros de correspondência de revisões associados a uma entidade (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "Ver [https://www.semantic-mediawiki.org/wiki/Help:Edit_help ajuda sobre edição] (MediaWiki Semântico)", "restriction-level-smw-pageedit": "protegida (só utilizadores elegíveis)", "action-smw-patternedit": "editar expressões regulares usadas pelo MediaWiki Semântico", "action-smw-pageedit": "editar páginas anotadas com <code>Está protegida contra edições</code> (MediaWiki Semântico)", "group-smwadministrator": "Administradores (MediaWiki Semântico)", "group-smwadministrator-member": "{{GENDER:$1|administrador|administradora}} (MediaWiki Semântico)", "grouppage-smwadministrator": "{{ns:project}}:Administradores (MediaWiki Semântico)", "group-smwcurator": "Curadores (MediaWiki Semântico)", "group-smwcurator-member": "{{GENDER:$1|curador|curadora}} (MediaWiki Semântico)", "grouppage-smwcurator": "{{ns:project}}:Curadores (MediaWiki Semântico)", "group-smweditor": "Editores (MediaWiki Semântico)", "group-smweditor-member": "{{GENDER:$1|editor|editora}} (MediaWiki Semântico)", "grouppage-smweditor": "{{ns:project}}:Editores (MediaWiki Semântico)", "action-smw-admin": "aceder às tarefas de administração do MediaWiki Semântico", "action-smw-ruleedit": "editar páginas de regras (MediaWiki Semântico)", "smw-property-namespace-disabled": "A propriedade [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks espaço nominal] está desativada; não é possível tentar declarar um tipo para esta propriedade nem outras características específicas da mesma.", "smw-property-predefined-default": "\"$1\" é uma propriedade predefinida do tipo $2.", "smw-property-predefined-common": "Esta propriedade é predefinida (também chamada [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propriedade especial]) e vem com privilégios de administração adicionais, mas pode ser usada como qualquer outra [https://www.semantic-mediawiki.org/wiki/Property propriedade definida pelo utilizador].", "smw-property-predefined-ask": "\"$1\" é uma propriedade predefinida que representa metainformação (na forma de [https://www.semantic-mediawiki.org/wiki/Subobject subobjeto]) acerca de consultas individuais, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-asksi": "\"$1\" é uma propriedade predefinida que recolhe o número de condições usadas numa consulta, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-askde": "\"$1\" é uma propriedade predefinida que fornece informação sobre a profundidade de uma consulta, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-askde": "É um valor numérico calculado com base nas consultas intercaladas, nas cadeias de propriedades e nos elementos descritivos disponíveis, sendo que a execução de cada consulta está restringida pelo parâmetro de configuração <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "\"$1\" é uma propriedade predefinida que descreve parâmetros que influenciam um resultado de consulta, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-askpa": "É parte de um conjunto de propriedades que especificam um [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler perfil de consulta].", "smw-sp-properties-docu": "Esta página lista as [https://www.semantic-mediawiki.org/wiki/Property propriedades] e a respetiva contagem de utilizações nesta wiki. Para ter estatísticas de contagem atualizadas, é recomendado que o [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics ''script'' de manutenção das estatísticas de propriedades] seja executado com regularidade. Para obter uma perspetiva diferente, veja as páginas especiais das [[Special:UnusedProperties|propriedades não utilizadas]] e [[Special:WantedProperties|propriedades desejadas]].", "smw-sp-properties-cache-info": "Os dados listados foram obtidos da [https://www.semantic-mediawiki.org/wiki/Caching ''cache''] e foram atualizados pela última vez a $1.", "smw-sp-properties-header-label": "Lista de propriedades", "smw-admin-settings-docu": "Apresenta uma lista de todas as configurações padrão e localizadas que são relevantes para o ambiente do MediaWiki Semântico. Para mais detalhes sobre as configurações individuais, consulte a página de ajuda da [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuração], por favor.", "smw-sp-admin-settings-button": "Gerar lista de configurações", "smw-admin-idlookup-title": "Consulta", "smw-admin-idlookup-docu": "Esta secção mostra detalhes técnicos de uma entidade individual (página da wiki, subobjeto, propriedade, etc.) no MediaWiki Semântico. Os dados de entrada podem ser um identificador numérico ou um texto que corresponda ao campo de pesquisa relevante, mas qualquer referência a um identificador refere-se ao MediaWiki Semântico e não deve ser confundida com os identificadores de página ou de revisão do MediaWiki.", "smw-admin-iddispose-title": "Eliminação", "smw-admin-iddispose-docu": "Note que a operação de eliminação não tem restrições e, se for confirmada, irá remover a entidade do motor de armazenamento e todas as referências à mesma das tabelas pendentes. Realize esta operação com '''cuidado''' e só depois de consultar a [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentação], por favor.", "smw-admin-iddispose-done": "O identificador \"$1\" foi removido do servidor de armazenamento.", "smw-admin-iddispose-references": "O identificador \"$1\" {{PLURAL:$2|não tem nenhuma|tem pelo menos uma}} referência ativa.", "smw-admin-iddispose-references-multiple": "Lista de correspondências com pelo menos um registo de referência ativo.", "smw-admin-iddispose-no-references": "A pesquisa não encontrou correspondência entre \"$1\" e uma entrada da tabela.", "smw-admin-idlookup-input": "Pesquisar:", "smw-admin-objectid": "Identificador:", "smw-admin-tab-general": "Visão geral", "smw-admin-tab-notices": "Avisos de descontinuação", "smw-admin-tab-maintenance": "Manutenção", "smw-admin-tab-supplement": "Funções suplementares", "smw-admin-tab-registry": "Registo", "smw-admin-tab-alerts": "<PERSON><PERSON><PERSON>", "smw-admin-alerts-tab-deprecationnotices": "Avisos de descontinuação", "smw-admin-alerts-tab-maintenancealerts": "Alertas de manutenção", "smw-admin-alerts-section-intro": "Esta secção mostra alertas e avisos relacionados com definições, operações e outras atividades que foram classificadas como requerendo a atenção de um administrador ou utilizador com direitos apropriados.", "smw-admin-maintenancealerts-section-intro": "Os seguintes alertas e avisos devem ser resolvidos e, embora não sejam essenciais, é esperado que ajudem a melhorar a manutenção do sistema e operacional.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Otimização de tabelas", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "O sistema descobriu que a última [https://www.semantic-mediawiki.org/wiki/Table_optimization otimização de tabelas] foi executada há $2 dias (registo de $1), o que excede o patamar de manutenção de $3 dias. Conforme menciona a documentação, a execução de otimizações permitirá que o planeador de consultas tome melhores decisões sobre consultas. Portanto, é sugerido que seja executada a otimização de tabelas com regularidade.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Entidades desatualizadas", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "O sistema contabilizou $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities entidades desatualizadas] e atingiu um nível crítico de manutenção autónoma ao exceder o patamar de $2. Recomenda-se que seja executado o ''script'' de manutenção [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>].", "smw-admin-maintenancealerts-invalidentities-alert-title": "Entidades inválidas", "smw-admin-maintenancealerts-invalidentities-alert": "O sistema detetou que $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1| entidade|entidades}}] estão num [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace espaço nominal que não é mantido] e é recomendado executar o ''script'' de manutenção [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] ou o [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>].", "smw-admin-deprecation-notice-section": "MediaWiki Semântico", "smw-admin-configutation-tab-settings": "Configurações", "smw-admin-configutation-tab-namespaces": "Espaços nominais", "smw-admin-configutation-tab-schematypes": "Tipos de esquema", "smw-admin-maintenance-tab-tasks": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-maintenance-tab-scripts": "''Scripts'' de manutenção", "smw-admin-maintenance-no-description": "Sem descrição.", "smw-admin-maintenance-script-section-title": "Lista dos ''scripts'' de manutenção disponíveis", "smw-admin-maintenance-script-section-intro": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ''scripts'' de manutenção requerem um administrador e acesso à linha de comandos para poder executar os ''scripts'' listados.", "smw-admin-maintenance-script-description-dumprdf": "Exportação para RDF das triplas existentes.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Este ''script'' é usado para gerir as ''caches'' de conceitos do MediaWiki Semântico, sendo que pode criar, remover e atualizar ''caches'' selecionadas.", "smw-admin-maintenance-script-description-rebuilddata": "Re<PERSON>ria todos os dados semânticos na base de dados, percorrendo todas as páginas que possam ter dados semânticos.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Reconstrói o índice Elasticsearch (só nas instalações que usam o <code>ElasticStore</code>), percorrendo to<PERSON> as entidades que têm dados semânticos.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Encontrar as entidades em falta no Elasticsearch (só nas instalações que usam o <code>ElasticStore</code>) e agendar os processos de atualização apropriados.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Reconstrói o índice de pesquisa de texto completo <code>SQLStore</code> (nas instalações onde a configuração foi ativada).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Reconstrói as estatísticas de utilização de todas as entidades propriedade.", "smw-admin-maintenance-script-description-removeduplicateentities": "Remove as entidades duplicadas encontradas em tabelas selecionadas que não tenham referências ativas.", "smw-admin-maintenance-script-description-setupstore": "Configura o servidor de armazenamento e consulta como definido em <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Atualiza o campo <code>smw_sort</code> do <code>SQLStore</code> (de acordo com a configuração [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "Popula o campo <code>smw_hash</code> das linhas em que este não tem um valor.", "smw-admin-maintenance-script-description-purgeentitycache": "<PERSON><PERSON><PERSON> as entradas da ''cache'' para entidades conhecidas e os dados a elas associados.", "smw-admin-maintenance-script-description-updatequerydependencies": "Atualizar consultas e dependências das consultas (ver a configuração [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Descartar entidades desatualizadas e hiperligações de consulta.", "smw-admin-maintenance-script-description-runimport": "Preencher e importar conteúdo descoberto automaticamente de [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Ficheiros de comandos de atualização", "smw-admin-maintenance-script-section-rebuild": "Ficheiros de comandos de recriação", "smw-livepreview-loading": "A carregar…", "smw-sp-searchbyproperty-description": "Esta página fornece uma [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces interface de navegação] simples para encontrar entidades descritas por uma propriedade e pelo nome de um valor. Entre as outras interfaces de pesquisa disponíveis, incluem-se a [[Special:PageProperty|página de pesquisa de propriedades]] e o [[Special:Ask|construtor de consultas ''ask'']].", "smw-sp-searchbyproperty-resultlist-header": "Lista de resultados", "smw-sp-searchbyproperty-nonvaluequery": "Uma lista de valores que têm atribuída a propriedade \"$1\".", "smw-sp-searchbyproperty-valuequery": "Uma lista de páginas que têm a propriedade \"$1\" com o valor \"$2\" anotado.", "smw-datavalue-number-textnotallowed": "\"$1\" não pode ser atribuído a um tipo de número declarado com o valor $2.", "smw-datavalue-number-nullnotallowed": "\"$1\" devolveu o valor \"NULL\" (nulo), que não é permitido como número.", "smw-editpage-annotation-enabled": "Esta página suporta anotações semânticas de texto (por exemplo, <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) para construir conteúdo estruturado e consultável, e é fornecida pelo MediaWiki Semântico. Para uma descrição detalhada do uso de anotações, ou da função #ask do analisador sintático, consulte as páginas de ajuda [https://www.semantic-mediawiki.org/wiki/Help:Getting_started começar], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation anotação de texto], ou [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries consultas em linha (''inline'')].", "smw-editpage-annotation-disabled": "Não são permitidas anotações semânticas de texto nesta página, devido a restrições do espaço nominal. Para detalhes sobre como ativar o espaço nominal consulte a página de ajuda da [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuração].", "smw-editpage-property-annotation-enabled": "Esta propriedade pode ser expandida usando anotações semânticas para especificar um tipo de dados (por exemplo, <nowiki>\"[[Has type::Page]]\"</nowiki>) ou inserir outras declarações de apoio (por exemplo, <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). Para uma descrição das formas de ampliação desta página, consulte as páginas de ajuda [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaração de uma propriedade] e [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes lista dos tipos de dados disponíveis].", "smw-editpage-property-annotation-disabled": "Esta propriedade não pode ser expandida com uma anotação de tipo de dados (por exemplo, <nowiki>\"[[Has type::Page]]\"</nowiki>) porque o tipo já está predefinido (para mais informações, consulte a página de ajuda [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propriedades especiais]).", "smw-editpage-concept-annotation-enabled": "Este conceito pode ser expandido usando a função #concept do analisador sintático. Para uma descrição do uso da função #concept, consulte a página de ajuda [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceitos].", "smw-search-syntax-support": "A entrada da pesquisa permite o uso da [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search sintaxe de consultas] semânticas para ajudar a encontrar correspondências usando o MediaWiki Semântico.", "smw-search-input-assistance": "O [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance ajudante de preenchimento] também está ativado para facilitar a pré-seleção das propriedades e categorias disponíveis.", "smw-search-help-intro": "Uma entrada com a forma <code><nowiki>[[ ... ]]</nowiki></code> assinala ao processador da entrada que deve usar o motor de pesquisa do MediaWiki Semântico. Note que a combinação de <code><nowiki>[[ ... ]]</nowiki></code> com uma pesquisa de texto não estruturado, como <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code>, não é suportada.", "smw-search-help-structured": "Pesquisas estruturadas:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (como [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context contexto filtrado])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (com um [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context contexto de consulta])", "smw-search-help-proximity": "Pesquisas aproximidas (uma propriedade ser desconhecida, '''só''' disponível para aqueles motores de pesquisa que fornecem uma integração com a pesquisa de texto completo):\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code> (pesquisar em todos os documentos os termos \"lorem\" e \"ipsum\" que tenham sido indexados)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (pesquisar correspondências com \"lorem ipsum\" como frase)", "smw-search-help-ask": "As seguintes hiperligações explicam como usar a sintaxe <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecionar páginas] descreve como selecionar páginas e construir condições\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadores de pesquisa] lista os operadores de pesquisa disponíveis, incluindo os para consultas de intervalos e consultas com caracteres de substituição", "smw-search-input": "Introdução e pesquisa", "smw-search-help-input-assistance": "É fornecida uma [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance ajuda de introdução] para o campo de entrada e requer que seja usado um dos seguintes prefixos:\n\n*<code>p:</code> para ativar as sugestões de propriedades (p. ex.: <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> para ativar as sugestões de categorias\n\n*<code>con:</code> para ativar as sugestões de conceitos", "smw-search-syntax": "Sintaxe", "smw-search-profile": "Expandida", "smw-search-profile-tooltip": "Funções de pesquisa relacionadas com o MediaWiki Semântico", "smw-search-profile-sort-best": "<PERSON><PERSON>", "smw-search-profile-sort-recent": "<PERSON><PERSON>e", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-intro": "O [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile perfil alar<PERSON>] da página Special:Search dá acesso a funções de pesquisa específicas da extensão MediaWiki Semântico e do seu servidor de consultas suportado.", "smw-search-profile-extended-help-sort": "Especifica uma preferência de ordenação para a apresentação do resultado com:", "smw-search-profile-extended-help-sort-title": "*\"Título\" usando o título da página (ou título de apresentação) como critério de ordenação", "smw-search-profile-extended-help-sort-recent": "*\"Mais recente\" mostrar<PERSON> primeiro as entidades modificadas mais recentemente (as entidades subobjetos são suprimidas porque essas entidades não estão anotadas com uma [[Property:Modification date|data de modificação]])", "smw-search-profile-extended-help-sort-best": "*\"Melhor correspondência\" ordenará as entidades por [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy relevância] com base em classificações fornecidas pelo servidor", "smw-search-profile-extended-help-form": "São disponibilizados formulários (se estes forem mantidos) para certos casos específicos de utilização. Os formulários podem expor diferentes campos de propriedade e de entrada de valor, para limitar o processo de introdução e facilitar a criação de pedidos de pesquisa (ver $1).", "smw-search-profile-extended-help-namespace": "A caixa de seleção do espaço nominal/domínio será ocultada logo que um formulário for selecionado mas pode ser tornada visível com a ajuda do botão \"mostrar/esconder\".", "smw-search-profile-extended-help-search-syntax": "O campo de entrada da pesquisa permite o uso da sintaxe <code>#ask</code> para definir um contexto de pesquisa específico do MediaWiki Semântico. Algumas expressões úteis:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> para encontrar tudo o que contenha \"...\" e é especialmente útil quando não se conhece o contexto da pesquisa ou as propriedades envolvidas (por exemplo, <code>in:(lorem && ipsum)</code> é equivalente a <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> para encontrar tudo o que contenha \"...\" exatamente na mesma ordem", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> para corresponder com qualquer entidade com uma propriedade \"...\" (por exemplo, <code>has:(Foo && Bar)</code> é equivalente a <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> para não corresponder com nenhuma entidade que inclui \"...\"", "smw-search-profile-extended-help-search-syntax-prefix": "* Estão disponíveis e definidos prefixos personalizados adicionais, como: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Algumas expressões estão reservadas, como: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Algumas das operações listadas só são úteis em ligação com um índice ativado de texto completo ou com o ElasticStore.''", "smw-search-profile-extended-help-query": "<code><nowiki>$1</nowiki></code> foi usado como consulta.", "smw-search-profile-extended-help-query-link": "(Para mais detalhes use o $1, por favor).", "smw-search-profile-extended-help-find-forms": "formulá<PERSON>s disponí<PERSON>is", "smw-search-profile-extended-section-sort": "Ordenar por", "smw-search-profile-extended-section-form": "Formulários", "smw-search-profile-extended-section-search-syntax": "Entrada a pesquisar", "smw-search-profile-extended-section-namespace": "Espaço nominal/domínio", "smw-search-profile-extended-section-query": "Consulta", "smw-search-profile-link-caption-query": "criador de consultas", "smw-search-show": "Mostrar", "smw-search-hide": "Ocultar", "log-name-smw": "Registo do MediaWiki Semântico", "log-show-hide-smw": "$1 o registo do MediaWiki Semântico", "logeventslist-smw-log": "Registo do MediaWiki Semântico", "log-description-smw": "Atividades para os [https://www.semantic-mediawiki.org/wiki/Help:Logging tipos de eventos ativados] que foram reportados pelo MediaWiki Semântico e pelos seus componentes.", "logentry-smw-maintenance": "Ocorrências relacionadas com manutenção, emitidas pelo MediaWiki Semântico", "smw-datavalue-import-unknown-namespace": "O espaço nominal de importação \"$1\" é desconhecido. Verifique que os detalhes de importação OWL estão disponíveis via [[MediaWiki:Smw import $1]], por favor", "smw-datavalue-import-missing-namespace-uri": "Não foi possível encontrar um URI do espaço nominal \"$1\" na [[MediaWiki:Smw import $1|importação de $1]].", "smw-datavalue-import-missing-type": "Não foi encontrada nenhuma definição de tipo para \"$1\" na [[MediaWiki:Smw import $2|importação de $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|importação de $1]]", "smw-datavalue-import-invalid-value": "\"$1\" não é um formato válido e deve ter a forma \"espaço\":\"identificador\" (por exemplo, \"xpto:nome\").", "smw-datavalue-import-invalid-format": "Era esperado que o texto \"$1\" estivesse dividido em quatro partes, mas o formato não foi compreendido.", "smw-property-predefined-impo": "\"$1\" é uma propriedade predefinida que descreve uma relação com um [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabulário importado] e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-type": "\"$1\" é uma propriedade predefinida que descreve o [[Special:Types|tipo de dados]] de uma propriedade e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-sobj": "\"$1\" é uma propriedade predefinida que representa uma estrutura [https://www.semantic-mediawiki.org/wiki/Help:Container recipiente] e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-sobj": "A estrutura recipiente permite acumular atribuições de valores a propriedades, e é semelhante a uma página wiki normal mas num espaço de entidades diferente, estando ligada ao assunto que a incorpora.", "smw-property-predefined-errp": "\"$1\" é uma propriedade predefinida que regista erros de entrada em anotações de valor irregular e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-errp": "Na maioria dos casos, a causa é uma não correspondência de tipos ou uma restrição do [[Property:Allows value|valor]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] é uma propriedade predefinida que pode definir uma lista de valores permitidos, para restringir a atribuição de valores a uma propriedade, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list \"$1\"] é uma propriedade predefinida que pode especificar uma referência para uma lista de valores permitidos, para restringir a atribuição de valores a uma propriedade, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-datavalue-property-restricted-annotation-use": "A propriedade \"$1\" tem uma área de aplicação restrita e não pode ser usada como propriedade de anotação por um utilizador.", "smw-datavalue-property-restricted-declarative-use": "A propriedade \"$1\" é uma propriedade declarativa e só pode ser usada numa propriedade ou página de categoria.", "smw-datavalue-property-create-restriction": "A propriedade \"$1\" não existe e o utilizador não tem a permissão \"$2\" (consulte [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade]) para criar ou anotar valores com uma propriedade não aprovada.", "smw-datavalue-property-invalid-character": "\"$1\" contém um carácter \"$2\" listado como parte da etiqueta de propriedade e portanto foi classificado como inválido.", "smw-datavalue-property-invalid-chain": "Usar \"$1\" como cadeia de propriedades não é permitido durante o processo de anotação.", "smw-datavalue-restricted-use": "O valor de dados \"$1\" foi marcado para uso restrito.", "smw-datavalue-invalid-number": "\"$1\" não pode ser interpretado como um número.", "smw-query-condition-circular": "Foi detetada uma possível condição circular em \"$1\".", "smw-query-condition-empty": "A descrição da consulta tem uma condição vazia.", "smw-types-list": "Lista de tipos de dados", "smw-types-default": "\"$1\" é um tipo de dados interno.", "smw-types-help": "Mais informações e exemplos podem ser encontrados nesta [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 página de ajuda].", "smw-type-anu": "\"$1\" é uma variante do tipo de dados [[Special:Types/URL|URL]], usada sobretudo para uma declaração de exportação ''owl:AnnotationProperty''.", "smw-type-boo": "\"$1\" é um tipo de dados básico para descrever um valor verdadeiro ou falso.", "smw-type-cod": "\"$1\" é uma variante do tipo de dados [[Special:Types/Text|Texto]], usado para textos técnicos de comprimento arbitrário, como listagens de código fonte.", "smw-type-geo": "\"$1\" é um tipo de dados que descreve localizações geográficas e requer a extensão [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Mapas\"] para disponibilizar uma funcionalidade alargada.", "smw-type-tel": "\"$1\" é um tipo de dados especial para descrever números telefónicos internacionais segundo o RFC 3966.", "smw-type-txt": "\"$1\" é um tipo de dados básico para descrever textos (''strings'') de comprimento arbitrário.", "smw-type-dat": "\"$1\" é um tipo de dados básico para representar pontos no tempo num formato unificado.", "smw-type-ema": "\"$1\" é um tipo de dados especial para representar um correio eletrónico.", "smw-type-tem": "\"$1\" é um tipo de dados numérico especial para representar uma temperatura.", "smw-type-qty": "\"$1\" é um tipo de dados para descrever quantidades com uma representação numérica e uma unidade de medida.", "smw-type-rec": "\"$1\" é um tipo de dados recipiente que especifica uma lista de propriedades com tipos, numa ordem fixa.", "smw-type-extra-tem": "O esquema de conversão inclui as unidades suportadas, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Fahrenheit e Rankine.", "smw-type-tab-properties": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-tab-types": "Tipos", "smw-type-tab-type-ids": "Identificadores de tipos", "smw-type-tab-errors": "<PERSON><PERSON><PERSON>", "smw-type-primitive": "Básico", "smw-type-contextual": "Contextual", "smw-type-compound": "Composto", "smw-type-container": "Recipiente", "smw-type-no-group": "Não classificados", "smw-special-pageproperty-description": "Esta página fornece uma interface de navegação para encontrar todos os valores de uma propriedade e uma determinada página. Entre as outras interfaces de pesquisa disponíveis, incluem-se a [[Special:SearchByProperty|pesquisa de propriedades]] e o [[Special:Ask|construtor de consultas ''ask'']].", "smw-property-predefined-errc": "\"$1\" é uma propriedade predefinida, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico], e representa erros relacionados com anotações de valor incorretas ou com o processamento de entradas.", "smw-property-predefined-long-errc": "Os erros são recolhidos numa [https://www.semantic-mediawiki.org/wiki/Help:Container estrutura recipiente] que pode incluir uma referencia à propriedade que causou a discrepância.", "smw-property-predefined-errt": "\"$1\" é uma propriedade predefinida que contém uma descrição textual de um erro, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-subobject-parser-invalid-naming-scheme": "Um subobjeto definido pelo utilizador continha um nome com formato inválido. O uso de um ponto ($1) nos primeiros cinco caracteres é uma notação reservada a extensões. Pode definir um [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier identificador nomeado].", "smw-datavalue-record-invalid-property-declaration": "A definição do registo contém a propriedade \"$1\", que está definida como um tipo de registo, e isso não é permitido.", "smw-property-predefined-mdat": "\"$1\" é uma propriedade predefinida que corresponde à data da última modificação de um assunto, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-cdat": "\"$1\" é uma propriedade predefinida que corresponde à data da primeira revisão de um assunto, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-newp": "\"$1\" é uma propriedade predefinida que indica se um assunto é novo ou não, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-ledt": "\"$1\" é uma propriedade predefinida que contém o nome da página do utilizador que criou a última revisão, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-mime": "\"$1\" é uma propriedade predefinida que descreve o tipo MIME de um ficheiro carregado, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-media": "\"$1\" é uma propriedade predefinida que descreve o tipo de multimédia de um ficheiro carregado, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-askfo": "\"$1\" é uma propriedade predefinida que contém o nome do formato de resultado usado numa consulta, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-askst": "\"$1\" é uma propriedade predefinida que descreve as condições da consulta na forma de texto, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-askdu": "\"$1\" é uma propriedade predefinida que contém o tempo (em segundos) necessário para a execução da consulta, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-asksc": "\"$1\" é uma propriedade predefinida, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico], que identifica fontes de consulta alternativas (por exemplo, remotas, federadas).", "smw-property-predefined-askco": "\"$1\" é uma propriedade predefinida fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para descrever o estado de uma consulta ou dos seus componentes.", "smw-property-predefined-long-askco": "O número ou números atribuídos representam um estado interno codificado que é explicado na [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler págin<PERSON> de ajuda].", "smw-property-predefined-prec": "\"$1\" é uma propriedade predefinida que descreve a [https://www.semantic-mediawiki.org/wiki/Help:Display_precision precisão de apresentação] (em casas decimais) para os tipos de dados numéricos.", "smw-property-predefined-attch-link": "\"$1\" é uma propriedade predefinida que recolhe as hiperligações para ficheiros e imagens encontradas numa página, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-inst": "\"$1\" é uma propriedade predefinida interna que armazena informação sobre categorias independente do MediaWiki, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-unit": "\"$1\" é uma propriedade predefinida declarativa para definir unidades de apresentação para propriedades de tipo numéricas, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-unit": "Uma lista separada por vírgulas permite descrever unidades ou formatos a serem usados para a apresentação.", "smw-property-predefined-conv": "\"$1\" é uma propriedade predefinida declarativa para definir o fator de conversão para uma unidade de uma quantidade física, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-serv": "\"$1\" é uma propriedade predefinida declarativa para adicionar ligações de serviços a uma propriedade, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-redi": "\"$1\" é uma propriedade predefinida interna para registar redirecionamentos, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-subp": "\"$1\" é uma propriedade predefinida declarativa usada para definir que uma propriedade é uma [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of subpropriedade de] outra, e é fornecida pelo [https://www.semantic- mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-subc": "\"$1\" é uma propriedade predefinida usada para definir que uma categoria é uma [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of subcategoria de] outra, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-conc": "\"$1\" é uma propriedade predefinida interna usada para definir um conceito associado, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-err-type": "\"$1\" é uma propriedade predefinida para identificar um grupo ou classe de [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors erros de processamento], e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-skey": "\"$1\" é uma propriedade predefinida interna para manter uma referência de ordenação, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-pplb": "\"$1\" é uma propriedade predefinida declarativa para especificar uma [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label etiqueta de propriedade preferida], e é fornecida pelo [https://www.semantic- mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-chgpro": "\"$1\" é uma propriedade predefinida para manter informação de [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation propagação de mudanças], e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-schema-link": ", e <PERSON> fornecid<PERSON>elo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-format-schema": ", e <PERSON> fornecid<PERSON>elo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-profile-schema": ", e <PERSON> fornecid<PERSON>elo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-trans": ", e <PERSON> fornecid<PERSON>elo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-trans-source": ", e <PERSON> fornecid<PERSON>elo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-trans-group": ", e <PERSON> fornecid<PERSON>elo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-cont-len": "\"$1\" é uma propriedade predefinida para armazenar informações de tamanho, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-cont-len": "É usada em ligação com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coligir e armazenar informações de tamanho obtidas de um ficheiro ingerido (se fornecidas).", "smw-property-predefined-cont-lang": "\"$1\" é uma propriedade predefinida para armazenar informação sobre a língua, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-cont-lang": "É usada em ligação com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coligir e armazenar informações sobre a língua obtidas de um ficheiro ingerido (se fornecidas).", "smw-property-predefined-cont-title": "\"$1\" é uma propriedade predefinida para armazenar informações sobre o título, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-cont-title": "É usada em ligação com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coligir e armazenar informações sobre o título obtidas de um ficheiro ingerido (se fornecidas).", "smw-property-predefined-cont-author": "\"$1\" é uma propriedade predefinida para armazenar informações sobre o autor, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-cont-author": "É usada em ligação com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coligir e armazenar informações sobre o autor obtidas de um ficheiro ingerido (se fornecidas).", "smw-property-predefined-cont-date": "\"$1\" é uma propriedade predefinida para armazenar informações sobre a data, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-cont-date": "É usada em ligação com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coligir e armazenar informações sobre a data obtidas de um ficheiro ingerido (se fornecidas).", "smw-property-predefined-cont-type": "\"$1\" é uma propriedade predefinida para armazenar informações sobre o tipo do ficheiro, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-cont-type": "É usada em ligação com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coligir e armazenar informações sobre o tipo do ficheiro obtidas de um ficheiro ingerido (se fornecidas).", "smw-property-predefined-cont-keyw": "\"$1\" é uma propriedade predefinida para representar palavras-chave, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-cont-keyw": "É usada em ligação com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coligir e armazenar palavras-chave obtidas de um ficheiro ingerido (se fornecidas).", "smw-property-predefined-file-attch": "\"$1\" é uma propriedade predefinida para representar um recipiente que armazena informação sobre anexos, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-file-attch": "É usada em ligação com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coligir todas as informações específicas do conteúdo que podem ser obtidas de um ficheiro ingerido (se fornecidas).", "smw-types-extra-geo-not-available": "Não foi detetada a [https://www.semantic-mediawiki.org/wiki/Extension:Maps extensão \"Mapas\"], portanto, \"$1\" está restringido na sua capacidade operacional.", "smw-datavalue-monolingual-dataitem-missing": "Falta um elemento esperado para construir um valor composto monolingue.", "smw-datavalue-languagecode-missing": "Para a anotação \"$1\", o analisador sintático não conseguiu determinar o código de língua (isto é, \"algo@pt\").", "smw-datavalue-languagecode-invalid": "\"$1\" não foi reconhecido como um código de língua suportado.", "smw-property-predefined-lcode": "\"$1\" é uma propriedade predefinida que representa um código de língua formatado BCP47, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-type-mlt-rec": "\"$1\" é um tipo de dados [https://www.semantic-mediawiki.org/wiki/Help:Container recipiente] (<i>container</i>) que associa um valor em texto a um [[Property:Language code|código de língua]] específico.", "smw-types-extra-mlt-lcode": "O tipo de dados {{PLURAL:$2|necessita|não necessita}} de um código de língua (ou seja, uma anotação de valor sem um código de língua {{PLURAL:$2|não é|é}} aceite).", "smw-property-predefined-text": "\"$1\" é uma propriedade predefinida que representa texto de comprimento arbitrário, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-pdesc": "\"$1\" é uma propriedade predefinida que permite descrever uma propriedade no contexto de uma língua, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-list": "\"$1\" é uma propriedade predefinida, para definir uma lista de propriedades usadas com uma propriedade de tipo [[Special:Types/Record|registo]], e <PERSON> fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-limitreport-intext-parsertime": "[SMW] Tempo de análise sintática das anotações de texto", "smw-limitreport-intext-postproctime": "[SMW] tempo pós-processamento", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Tempo de atualização de armazenamento (na purga de páginas)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw_allows_pattern": "É esperado que esta página contenha uma lista de referências (seguidas por [https://en.wikipedia.org/wiki/Regular_expression expressões regulares]) que serão disponibilizadas pela propriedade [[Property:Allows pattern|Padrão permitido]]. Para editar esta página é necessária a permissão <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "\"$1\" foi considerada inválida pela expressão regular \"$2\".", "smw-datavalue-allows-pattern-reference-unknown": "A referência \"$1\" do padrão permitido não foi encontrada na página [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "A referência de lista \"$1\" não tem correspondência com uma página [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "O conteúdo da lista \"$1\" não contém elementos com o marcador de lista *.", "smw-datavalue-feature-not-supported": "A funcionalidade \"$1\" não é suportada, ou foi desativada, nesta wiki.", "smw-property-predefined-pvap": "\"$1\" é uma propriedade predefinida, que pode especificar uma [[MediaWiki:Smw allows pattern|referência de padrão permitido]] para aplicar uma [https://en.wikipedia.org/wiki/Regular_expression expressão regular], e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-dtitle": "\"$1\" é uma propriedade predefinida que pode atribuir a uma entidade um título de apresentação distinto, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-pvuc": "\"$1\" é uma propriedade predefinida, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico], para restringir a valores únicos (ou a um, no máximo) a atribuição de valores para cada instância.", "smw-property-predefined-long-pvuc": "A unicidade é estabelecida quando as representações literais de dois valores não são iguais e qualquer violação desta restrição será categorizada como erro.", "smw-datavalue-constraint-uniqueness-violation": "A propriedade \"$1\" só permite a atribuição de valores únicos e ''$2'' já foi anotado no assunto \"$3\".", "smw-datavalue-constraint-uniqueness-violation-isknown": "A propriedade \"$1\" só permite anotações de valores únicos, ''$2'' já contém um valor atribuído. \"$3\" viola a restrição de unicidade.", "smw-datavalue-constraint-violation-non-negative-integer": "A propriedade \"$1\" tem uma restrição \"inteiro não negativo\" e o valor ''$2'' viola aquele requisito.", "smw-datavalue-constraint-violation-must-exists": "A propriedade \"$1\" tem uma restrição <code>must_exists</code> e o valor ''$2'' viola aquele requisito.", "smw-datavalue-constraint-violation-single-value": "A propriedade \"[[Property:$1|$1]]\" tem uma restrição <code>single_value</code> e o valor \"$2\" viola aquele requisito.", "smw-constraint-violation-uniqueness": "Está atribuída à propriedade \"[[Property:$1|$1]]\" uma restrição <code>unique_value_constraint</code>, que só permite atribuições de valores exclusivos, e a anotação de valor \"$2\" já foi encontrada no assunto \"$3\".", "smw-constraint-violation-uniqueness-isknown": "Está atribuída à propriedade \"[[Property:$1|$1]]\" uma restrição <code>unique_value_constraint</code>, que só permite atribuições de valores exclusivos. ''$2'' já contém um valor anotado com \"$3\", o que viola a restrição de unicidade do assunto corrente.", "smw-constraint-violation-non-negative-integer": "Está atribuída à propriedade \"[[Property:$1|$1]]\" uma restrição <code>non_negative_integer</code>, que só permite atribuições de números inteiros não negativos, e a anotação de valor \"$2\" viola o requisito da restrição.", "smw-constraint-violation-must-exists": "Está atribuída à propriedade \"[[Property:$1|$1]]\" uma restrição <code>must_exists</code>, e a anotação de valor \"$2\" viola o requisito da restrição.", "smw-constraint-violation-single-value": "Está atribuída à propriedade \"[[Property:$1|$1]]\" uma restrição <code>single_value</code>, e a anotação de valor \"$2\" viola o requisito da restrição.", "smw-constraint-violation-class-shape-constraint-missing-property": "Está atribuída à categoria \"[[:$1]]\" uma restrição <code>shape_constraint</code> com uma chave <code>property</code>, e a propriedade \"$2\" exigida está em falta.", "smw-constraint-violation-class-shape-constraint-wrong-type": "Está atribuída à categoria \"[[:$1]]\" uma restrição <code>shape_constraint</code> com uma chave <code>property_type</code>, e a propriedade \"$2\" não corresponde ao tipo de \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "Está atribuída à categoria \"[[:$1]]\" uma restrição <code>shape_constraint</code> com uma chave <code>max_cardinality</code>, e a propriedade \"$2\" não corresponde à cardinalidade de \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "Está atribuída à categoria \"[[:$1]]\" uma restrição <code>shape_constraint</code> com uma chave <code>min_textlength</code>, e a propriedade \"$2\" não respeita o requisito de comprimento de \"$3\".", "smw-constraint-violation-class-mandatory-properties-constraint": "Está atribuída à categoria \"[[:$1]]\" uma restrição <code>mandatory_properties</code> que requer as seguin<PERSON> propriedades obrigatórias: $2", "smw-constraint-violation-allowed-namespace-no-match": "Está atribuída à propriedade \"[[Property:$1|$1]]\" uma restrição <code>allowed_namespaces</code>, e \"$2\" viola o requisito do espaço nominal, só sendo permitidos os espaços nominais \"$3\".", "smw-constraint-violation-allowed-namespaces-requires-page-type": "A restrição <code>allowed_namespaces</code> requer um tipo de página.", "smw-constraint-schema-category-invalid-type": "O esquema anotado \"$1\" é inválido para uma categoria e requer um tipo \"$2\".", "smw-constraint-schema-property-invalid-type": "O esquema anotado \"$1\" é inválido para uma propriedade e requer um tipo \"$2\".", "smw-constraint-error-allows-value-list": "\"$1\" não está na lista ($2) dos [[Property:Allows value|valores permitidos]] para a propriedade \"$3\".", "smw-constraint-error-allows-value-range": "\"$1\" não está no intervalo de \"$2\" especificado pela restrição [[Property:Allows value|permite valor]] da propriedade \"$3\".", "smw-property-predefined-boo": "\"$1\" é um [[Special:Types/Boolean|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para representar valores boolianos.", "smw-property-predefined-num": "\"$1\" é um [[Special:Types/Number|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para representar valores numéricos.", "smw-property-predefined-dat": "\"$1\" é um [[Special:Types/Date|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para representar valores de datas.", "smw-property-predefined-uri": "\"$1\" é um [[Special:Types/URL|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para representar valores URI/URL.", "smw-property-predefined-qty": "\"$1\" é um [[Special:Types/Quantity|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para representar valores de quantidade.", "smw-datavalue-time-invalid-offset-zone-usage": "\"$1\" contém um desfasamento e um indicador de zona que não são suportados.", "smw-datavalue-time-invalid-values": "O valor \"$1\" contém informação que não pode ser interpretada, na forma \"$2\".", "smw-datavalue-time-invalid-date-components-common": "\"$1\" contém alguma informação que não pode ser interpretada.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\" contém um travessão ou traço de ligação extrínseco ou outros caracteres que são inválidos na interpretação de uma data.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" contém alguns componentes vazios.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" contém mais de três componentes necessários para a interpretação de uma data.", "smw-datavalue-time-invalid-date-components-sequence": "\"$1\" contém uma sequência que não foi possível interpretar contra uma matriz de correspondências disponível para componentes de datas.", "smw-datavalue-time-invalid-ampm": "\"$1\" contém \"$2\" como elemento hora, que é inválido no formato de relógio de 12 horas.", "smw-datavalue-time-invalid-jd": "Não foi possível interpretar o valor de entrada \"$1\" como um dia válido no calend<PERSON><PERSON>, tendo sido reportado \"$2\".", "smw-datavalue-time-invalid-prehistoric": "Não é possível interpretar o valor de entrada pré-histórico \"$1\". <PERSON>r exemplo, especificar mais do que anos, ou do que um modelo de calendário, poderá produzir resultados inesperados num contexto pré-histórico.", "smw-datavalue-time-invalid": "Não foi possível interpretar o valor de entrada \"$1\" como uma data válida ou como um componente para expressão de tempo, tendo sido reportado \"$2\".", "smw-datavalue-external-formatter-uri-missing-placeholder": "Falta o espaço reservado \"$1\" no URI do formatador.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" é um URL inválido.", "smw-datavalue-external-identifier-formatter-missing": "Falta à propriedade a atribuição de um [[Property:External formatter uri|\"URI de formatador externo\"]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "O identificador externo \"$1\" espera uma substituição de vários campos, mas falta ao valor atual \"$2\" pelo menos um parâmetro de valor para cumprir o requisito.", "smw-datavalue-keyword-maximum-length": "A palavra-chave excedeu o tamanho máximo de $1 {{PLURAL:$1|carácter|caracteres}}.", "smw-property-predefined-eid": "\"$1\" é um [[Special:Types/External identifier|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para representar identificadores externos.", "smw-property-predefined-peid": "\"$1\" é uma propriedade predefinida que especifica um identificador externo, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-pefu": "\"$1\" é uma propriedade predefinida fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para especificar um recurso externo com um espaço reservado.", "smw-property-predefined-long-pefu": "Espera-se que o URI contenha um espaço reservado que será ajustado com um valor de [[Special:Types/External identifier|identificador externo]] para formar uma referência de recursos válida.", "smw-type-eid": "\"$1\" é uma variante do tipo de dados [[Special:Types/Text|texto]] para descrever recursos externos (baseados em URI) e requer propriedades atribuídas para declarar um [[Property:External formatter uri|URI de formatador externo]].", "smw-property-predefined-keyw": "\"$1\" é uma propriedade predefinida e um [[Special:Types/Keyword|tipo]], fornecidas pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico], que normalizam um texto e têm um tamanho restrito de caracteres.", "smw-type-keyw": "\"$1\" é uma variante do tipo de dados de [[Special:Types/Text|texto]] que tem um tamanho limitado de caracteres com uma representação do conteúdo normalizada.", "smw-datavalue-stripmarker-parse-error": "O valor fornecido, \"$1\", contém [https://en.wikipedia.org/wiki/Help:Strip_markers marcadores do analisador sintático] e não pode ser suficientemente analisado.", "smw-datavalue-parse-error": "O valor fornecido, \"$1\", não foi compreendido.", "smw-datavalue-propertylist-invalid-property-key": "A lista de propriedades \"$1\" continha uma chave de propriedade inválida \"$2\".", "smw-datavalue-type-invalid-typeuri": "Não foi possível transformar o tipo \"$1\" numa representação URI válida.", "smw-datavalue-wikipage-missing-fragment-context": "O valor de entrada \"$1\" da página da wiki não pode ser usado sem uma página de contexto.", "smw-datavalue-wikipage-invalid-title": "O valor de entrada \"$1\" do tipo de página contém caracteres inválidos ou está incompleto e, portanto, pode provocar resultados inesperados numa consulta ou num processo de anotação.", "smw-datavalue-wikipage-property-invalid-title": "A propriedade \"$1\" (como tipo de página) com o valor de entrada \"$2\" contém caracteres inválidos ou está incompleta e, portanto, pode provocar resultados inesperados numa consulta ou num processo de anotação.", "smw-datavalue-wikipage-empty": "O valor de entrada da página da wiki está vazio (por exemplo, <code>[[SomeProperty::]], [[]]</code>) e, portanto, não pode ser usado como nome ou como parte da condição de uma consulta.", "smw-type-ref-rec": "\"$1\" é um tipo de dados [https://www.semantic-mediawiki.org/wiki/Container recipiente] (<i>container</i>) que permite registar informação adicional sobre a atribuição de um valor (por exemplo, dados de proveniência).", "smw-datavalue-reference-invalid-fields-definition": "O tipo [[Special:Types/Reference|Referência]] espera que seja declarada uma lista de propriedades, usando a propriedade [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Tem campos].", "smw-parser-invalid-json-format": "O analisador JSON terminou com \"$1\".", "smw-property-preferred-label-language-combination-exists": "\"$1\" não pode ser usado como etiqueta preferida porque a língua \"$2\" já está atribuída à etiqueta \"$3\".", "smw-clipboard-copy-link": "Copiar hiperligação para a área de transferência", "smw-property-userdefined-fixedtable": "\"$1\" estava configurada como [https://www.semantic-mediawiki.org/wiki/Fixed_properties propriedade fixa] e qualquer modificação da sua [https://www.semantic-mediawiki.org/wiki/Type_declaration declaração de tipo] requer: ou que seja executado <code>setupStore.php</code>, ou que seja completada a tarefa especial [[Special:SemanticMediaWiki|\"Instalação e atualização da base de dados\"]].", "smw-data-lookup": "A obter dados...", "smw-data-lookup-with-wait": "O pedido está a ser processado e pode levar algum tempo.", "smw-no-data-available": "Não há dados disponíveis.", "smw-property-req-violation-missing-fields": "Falta à propriedade \"$1\" uma declaração obrigatória [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] para este tipo de \"$2\".", "smw-property-req-violation-multiple-fields": "A propriedade \"$1\" contém mais do que uma declaração (neste caso, em concorrência) [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>]; é esperada uma única para este tipo de \"$2\".", "smw-property-req-violation-missing-formatter-uri": "<PERSON><PERSON><PERSON>, à propriedade \"$1\", detalhes de declaração para o tipo anotado porque não foi definida a propriedade <code>URI do formatador externo</code>.", "smw-property-req-violation-predefined-type": "A propriedade \"$1\", como propriedade predefinida, contém uma declaração de tipo \"$2\" que é incompatível com o tipo padrão desta propriedade.", "smw-property-req-violation-import-type": "Foi detetada uma declaração de tipo que é incompatível com o tipo predefinido do vocabulário importado \"$1\". Em geral, não é necessário declarar um tipo porque são obtidas informações da definição da importação.", "smw-property-req-violation-change-propagation-locked-error": "A propriedade \"$1\" foi alterada e requer que as entidades atribuídas sejam reavaliadas usando um processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações]. A página da propriedade foi trancada até que a atualização da especificação primária esteja finalizada para impedir interrupções intermédias ou especificações contraditórias. O processo pode demorar algum tempo até que a página possa ser destrancada porque depende do tamanho e frequência da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas].", "smw-property-req-violation-change-propagation-locked-warning": "A propriedade \"$1\" foi alterada e requer que as entidades atribuídas sejam reavaliadas usando um processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações]. A atualização pode demorar algum tempo porque depende do tamanho e frequência da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas] e é sugerido que as alterações da propriedade sejam adiadas para impedir interrupções intermédias ou especificações contraditórias.", "smw-property-req-violation-change-propagation-pending": "Estão pendentes atualizações devidas à [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações] ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|tarefa estimada|tarefas estimadas}}]) e é recomendado que não altere propriedades até que o processo esteja finalizado, para impedir interrupções intermédias ou especificações contraditórias.", "smw-property-req-violation-missing-maps-extension": "O MediaWiki Semântico não conseguiu detetar a extensão [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"], que é um pré-requisito e, em consequência, limita a funcionalidade (isto é, impede de armazenar ou processar dados geográficos) desta propriedade.", "smw-property-req-violation-type": "A propriedade contém especificações de tipo concorrentes, que podem resultar em anotações de valor inválidas; é, portanto, esperado que um(a) utilizador(a) atribua um tipo adequado.", "smw-property-req-error-list": "A propriedade contém os seguintes erros ou avisos:", "smw-property-req-violation-parent-type": "A propriedade \"$1\" e a propriedade mãe atribuída \"$2\" têm anotações de tipos diferentes.", "smw-property-req-violation-forced-removal-annotated-type": "A aplicação da [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance herança obrigatória do tipo da propriedade mãe] está ativada, o tipo anotado para a propriedade \"$1\" não corresponde ao tipo \"$2\" da sua propriedade mãe e foi alterado para refletir aquele requisito. É recomendado ajustar a definição do tipo na página, para que a mensagem de erro e aplicação obrigatória sejam removidas desta propriedade.", "smw-change-propagation-protection": "Esta página está trancada para evitar modificações acidentais dos dados durante uma atualização devida à [https://www.semantic-mediawiki.org/wiki/change_propagation propagação de alterações]. O processo pode demorar algum tempo a destrancar a página porque depende do tamanho e da frequência da gestão da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas].", "smw-category-change-propagation-locked-error": "A categoria \"$1\" foi alterada e requer que as entidades atribuídas sejam reavaliadas usando um processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações]. Entretanto, a página da categoria foi trancada até que a atualização da especificação primária esteja finalizada para impedir interrupções intermédias ou especificações contraditórias. O processo pode demorar algum tempo até destrancar a página porque depende do tamanho e da frequência da gestão da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas].", "smw-category-change-propagation-locked-warning": "A categoria \"$1\" foi alterada e requer que as entidades atribuídas sejam reavaliadas usando um processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações]. A atualização pode demorar algum tempo porque depende do tamanho e frequência da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas] e é sugerido que adie alterações à categoria para impedir interrupções intermédias ou especificações contraditórias.", "smw-category-change-propagation-pending": "Estão pendentes atualizações devidas à [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações] ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|tarefa estimada|tarefas estimadas}}]) e é recomendado que aguarde até que o processo esteja finalizado antes de fazer alterações à categoria, para impedir interrupções intermédias ou especificações contraditórias.", "smw-category-invalid-value-assignment": "\"$1\" não é reconhecido como categoria válida ou anotação de valor.", "protect-level-smw-pageedit": "Permitir só utilizadores com permissão de edição de páginas (MediaWiki Semântico)", "smw-create-protection": "A criação da propriedade \"$1\" está restrita a utilizadores com o direito \"$2\" ou um [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de utilizadores] apropriado enquanto o [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade] está ativado.", "smw-create-protection-exists": "A alteração da propriedade \"$1\" está restrita a utilizadores com o direito \"$2\" ou um [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de utilizadores] apropriado enquanto o [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade] está ativado.", "smw-edit-protection": "Esta página está [[Property:Is edit protected|protegida]] para impedir a modificação acidental de dados e só pode ser editada por utilizadores com o direito de edição (\"$1\") ou que estão no [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de utilizadores] apropriado.", "smw-edit-protection-disabled": "A proteção contra edições foi desativada, portanto, a propriedade \"$1\" não pode ser usada para proteger páginas de entidades contra edições não autorizadas.", "smw-edit-protection-auto-update": "O MediaWiki Semântico atualizou o estado de proteção de acordo com a propriedade \"Está protegida contra edições\".", "smw-edit-protection-enabled": "Protegida contra edições (MediaWiki Semântico)", "smw-patternedit-protection": "Esta página está protegida e só pode ser editada por utilizadores com a <code>smw-patternedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions permissão] apropriada.", "smw-property-predefined-edip": "\"$1\" é uma propriedade predefinida fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para indicar se a página está protegida contra edições ou não.", "smw-property-predefined-long-edip": "Embora qualquer utilizador possa adicionar esta propriedade a um assunto, só um utilizador com uma permissão específica pode editar ou revogar a proteção de uma entidade após essa proteção ter sido adicionada.", "smw-query-reference-link-label": "Referência de consulta", "smw-format-datatable-emptytable": "Não há dados disponíveis na tabela", "smw-format-datatable-info": "A mostrar entradas _START_ a _END_, de _TOTAL_", "smw-format-datatable-infoempty": "A mostrar entradas 0 a 0, de 0", "smw-format-datatable-infofiltered": "(filtradas de _MAX_ entradas no total)", "smw-format-datatable-infothousands": "&nbsp;", "smw-format-datatable-lengthmenu": "Mostrar _MENU_ entradas", "smw-format-datatable-loadingrecords": "A carregar…", "smw-format-datatable-processing": "A processar...", "smw-format-datatable-search": "Pesquisar:", "smw-format-datatable-zerorecords": "Não foram encontrados registos com correspondências", "smw-format-datatable-first": "<PERSON><PERSON>", "smw-format-datatable-last": "Último", "smw-format-datatable-next": "Próximo", "smw-format-datatable-previous": "Anterior", "smw-format-datatable-sortascending": ": ativar para ordenar a coluna por ordem crescente", "smw-format-datatable-sortdescending": ": ativar para ordenar a coluna por ordem decrescente", "smw-format-datatable-toolbar-export": "Exportar", "smw-category-invalid-redirect-target": "A categoria \"$1\" contém um destino inválido de redirecionamento para um espaço nominal que não é de categorias.", "smw-parser-function-expensive-execution-limit": "A função do analisador sintático atingiu o limite para execuções de funções exigentes (consulte o parâmetro de configuração [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "O MediaWiki Semântico está a recarregar a página atual devido à necessidade de algum processamento após a consulta.", "apihelp-smwinfo-summary": "Módulo da API para obter informação sobre estatísticas e outra metainformação do MediaWiki Semântico.", "apihelp-ask-summary": "Módulo da API para consultar o MediaWiki Semântico usando a linguagem \"ask\".", "apihelp-askargs-summary": "Módulo da API para consultar o MediaWiki Semântico usando a linguagem \"ask\" na forma de lista de condições, propriedades a mostrar e parâmetros.", "apihelp-browsebyproperty-summary": "Módulo da API para obter informação sobre uma propriedade ou lista de propriedades.", "apihelp-browsebysubject-summary": "Módulo da API para obter informação sobre um assunto.", "apihelp-smwtask-summary": "Módulo da API para executar tarefas relacionadas com o MediaWiki Semântico (só para uso interno, não para uso público).", "apihelp-smwbrowse-summary": "Módulo da API para suportar atividades de navegação para diferentes tipos de entidades no MediaWiki Semântico.", "apihelp-ask-parameter-api-version": "Formatação da saída:\n;2:Formato compatível com versões anteriores, usando {} para a lista de resultados.\n;3:Formato experimental, usando [] como lista de resultados.", "apihelp-smwtask-param-task": "Define o tipo de tarefa", "apihelp-smwtask-param-params": "Parâmetros codificados JSON que correspondem ao requisito do tipo de tarefa selecionado", "smw-apihelp-smwtask-example-update": "Exemplo da execução de uma tarefa de atualização para um assunto específico:", "smw-api-invalid-parameters": "Parâ<PERSON><PERSON> inválid<PERSON>, \"$1\"", "smw-parser-recursion-level-exceeded": "O nível de $1 recursões foi excedido durante um processo de análise sintática. É sugerido que valide a estrutura de predefinições, ou que ajuste o parâmetro de configuração <code>$maxRecursionDepth</code> se necessário.", "smw-property-page-list-count": "A apresentar {{PLURAL:$1|uma página que usa|$1 páginas que usam}} esta propriedade.", "smw-property-page-list-search-count": "A apresentar {{PLURAL:$1|uma página que usa|$1 páginas que usam}} esta propriedade com uma correspondência de valor \"$2\".", "smw-property-page-filter-note": "O [https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter filtro de pesquisa] permite a inclusão de [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions expressões de consulta] como <code>~</code> ou <code>!</code> . O [https://www.semantic-mediawiki.org/wiki/Query_engine motor de consultas] selecionado também pode oferecer suporte de correspondência que não diferencia maiúsculas de minúsculas ou outras expressões curtas como:\n\n* <code>in:</code> o resultado deve incluir o termo, por exemplo: '<code>in:Foo</code>'\n\n* <code>not:</code> o resultado não deve incluir o termo, por exemplo: '<code>not:Bar</code>'", "smw-property-reserved-category": "Categoria", "smw-category": "Categoria", "smw-datavalue-uri-invalid-scheme": " \"$1\" não foi listado como um esquema URI válido.", "smw-datavalue-uri-invalid-authority-path-component": "Foi identificado que \"$1\" contém uma autoridade ou um componente de caminho \"$2\" inválido.", "smw-browse-property-group-title": "Grupo de propriedades", "smw-browse-property-group-label": "Etiqueta do grupo de propriedades", "smw-browse-property-group-description": "Descrição do grupo de propriedades", "smw-property-predefined-ppgr": "\"$1\" é uma propriedade predefinida que identifica as entidades (principalmente as categorias) que são utilizadas como instâncias de agrupamento para as propriedades, e é fornecida pela extensão [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-filter": "Filtro", "smw-section-expand": "Expandir a secção", "smw-section-collapse": "Recolher a secção", "smw-ask-format-help-link": "Formato [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Auxiliar", "smw-personal-jobqueue-watchlist": "Lista de vigilância da fila de tarefas", "smw-personal-jobqueue-watchlist-explain": "Os números indicam uma estimativa das entradas da fila de tarefas que aguardam execução.", "smw-property-predefined-label-skey": "Chave de ordenação", "smw-processing": "A processar...", "smw-loading": "A carregar...", "smw-fetching": "A procurar...", "smw-preparing": "A preparar...", "smw-expand": "Expandir", "smw-collapse": "<PERSON><PERSON><PERSON><PERSON>", "smw-copy": "Copiar", "smw-copy-clipboard-title": "Copia o conteúdo para a área de transferência", "smw-jsonview-expand-title": "Expande a vista JSON", "smw-jsonview-collapse-title": "Recolhe a vista JSON", "smw-jsonview-search-label": "Pesquisar:", "smw-redirect-target-unresolvable": "O destino é irresolúvel pela razão \"$1\"", "smw-types-title": "Tipo: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Não é permitido alterar o modelo de conteúdo de uma [https://www.semantic-mediawiki.org/wiki/Help:Schema página de esquema].", "smw-schema-namespace-edit-protection": "Esta página está protegida e só pode ser editada por utilizadores com a [https://www.semantic-mediawiki.org/wiki/Help:Permissions permissão] <code>smw-schemaedit</code> adequada.", "smw-schema-namespace-edit-protection-by-import-performer": "Esta página foi importada por um [https://www.semantic-mediawiki.org/wiki/Import_performer realizador de importações] listado e isso significa que alterar o conteúdo desta página está restringido somente aos utilizadores listados.", "smw-schema-error-title": "{{PLURAL:$1|<PERSON>rro|<PERSON>rros}} de validação", "smw-schema-error-schema": "O esquema de validação '''$1''' identificou as seguintes inconsistências:", "smw-schema-error-miscellaneous": "Erro variado ($1)", "smw-schema-error-validation-json-validator-inaccessible": "O validador JSON \"<b>$1</b>\" não está acessível (ou não foi instalado) e por este motivo o ficheiro \"$2\" não pode ser examinado, o que impede que a página corrente seja gravada ou alterada.", "smw-schema-error-validation-file-inaccessible": "O ficheiro de validação \"$1\" não está acessível.", "smw-schema-error-type-missing": "Falta um tipo ao conteúdo para este ser reconhecido e utilizável no [https://www.semantic-mediawiki.org/wiki/Help:Schema espaço nominal/domínio do esquema].", "smw-schema-error-type-unknown": "O tipo \"$1\" não está registado e não pode ser usado para conteúdo no espaço nominal/domínio [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-error-json": "Erro JSON: \"$1\"", "smw-schema-error-input": "A validação da entrada encontrou os seguintes problemas, que precisam de ser endereçados antes de poder guardar o conteúdo. A página de [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling ajuda sobre esquemas] pode conter alguns conselhos sobre como remover inconsistências ou resolver problemas com o esquema da entrada.", "smw-schema-error-input-schema": "O esquema de validação '''$1''' encontrou as seguintes inconsistências, e estas precisam de ser endereçadas antes de poder guardar o conteúdo. A página de [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling ajuda sobre esquemas] pode conter alguns conselhos sobre como resolver estes problemas.", "smw-schema-error-title-prefix": "Este tipo de esquema requer que o título do esquema comece com um prefixo \"$1\".", "smw-schema-validation-error": "O tipo \"$1\" não está registado e não pode ser usado para conteúdo no espaço nominal/domínio [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-validation-schema-title": "Esquema JSON", "smw-schema-summary-title": "Resumo", "smw-schema-title": "Esquema", "smw-schema-usage": "<PERSON><PERSON>", "smw-schema-type": "T<PERSON><PERSON> de <PERSON>", "smw-schema-type-description": "Descrição do tipo", "smw-schema-description": "Descrição do esquema", "smw-schema-description-link-format-schema": "Este tipo de esquema permite a definição de características para criar hiperligações dependentes do contexto com relação a uma propriedade atribuída de [[Property:Formatter schema|esquema formatador]].", "smw-schema-description-search-form-schema": "Este tipo de esquema permite a definição de formulários de entrada, e características para o perfil de [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch pesquisa avançada], onde este contém instruções para gerar campos de introdução de dados, definir espaços nominais/domínios padrão, ou declarar expressões prefixo para um pedido de pesquisa.", "smw-schema-description-property-profile-schema": "Este tipo de esquema suporta a definição de um perfil para declarar características à propriedade atribuída e seus valores de anotação.", "smw-schema-description-property-group-schema": "Este tipo de esquema permite a definição de [https://www.semantic-mediawiki.org/wiki/Help:Property_group grupos de propriedades] para ajudarem a estruturar a interface de [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse navegação].", "smw-schema-description-property-constraint-schema": "Isto permite a definição de regras de restrição para uma instância de propriedade, assim como os valores que lhe sejam atribuídos.", "smw-schema-description-class-constraint-schema": "Este tipo de esquema permite a definição de regras de restrição para uma instância de classe (também designada categoria).", "smw-schema-tag": "{{PLURAL:$1|Etiqueta|Etiquetas}}", "smw-property-predefined-constraint-schema": "\"$1\" é uma propriedade predefinida que define um esquema de restrição, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-schema-desc": "\"$1\" é uma propriedade predefinida que armazena uma descrição de esquema, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-schema-def": "\"$1\" é uma propriedade predefinida que armazena o conteúdo do esquema, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-schema-tag": "\"$1\" é uma propriedade predefinida fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico] para identificar um conjunto de esquemas.", "smw-property-predefined-long-schema-tag": "Uma etiqueta que identifica esquemas de conteúdos ou características semelhantes.", "smw-property-predefined-schema-type": "\"$1\" é uma propriedade predefinida que descreve um tipo para distinguir um grupo de esquemas, e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semântico].", "smw-property-predefined-long-schema-type": "Cada [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type tipo] fornece a sua própria interpretação dos elementos sintáticos e das regras de aplicação, e pode ser expresso com a ajuda de um [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation esquema de validação].", "smw-ask-title-keyword-type": "<PERSON><PERSON>rar palavra-chave", "smw-ask-message-keyword-type": "Esta pesquisa coincide com a condição <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Não foi possível estabelecer ligação ao destino \"$1\" remoto.", "smw-remote-source-disabled": "A fonte '''$1''' desativou o suporte de pedidos remotos!", "smw-remote-source-unmatched-id": "A fonte '''$1''' não corresponde a nenhuma versão do MediaWiki Semântico que possa suportar um pedido remoto.", "smw-remote-request-note": "O resultado é obtido da fonte remota '''$1''' e é provável que o conteúdo gerado contenha informação que não está disponível na wiki corrente.", "smw-remote-request-note-cached": "O resultado é '''armazenado em cache''' a partir da fonte remota '''$1''' e é provável que o conteúdo gerado contenha informação que não está disponível na wiki corrente.", "smw-parameter-missing": "O parâmetro \"$1\" está em falta.", "smw-property-tab-usage": "<PERSON><PERSON>", "smw-property-tab-profile-schema": "Esquema de perfil", "smw-property-tab-redirects": "Sinónimos", "smw-property-tab-subproperties": "Subpropriedades", "smw-property-tab-errors": "Atribuições indevidas", "smw-property-tab-constraint-schema": "Esquema de restrição", "smw-property-tab-constraint-schema-title": "Esquema de restrição compilado", "smw-property-tab-specification": "... mais", "smw-concept-tab-list": "Lista", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-ask-tab-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-extra": "Extra", "smw-ask-tab-debug": "Despistagem de defeitos", "smw-ask-tab-code": "Código", "smw-install-incomplete-tasks-title": "Tarefas de administração incompletas", "smw-install-incomplete-intro": "Há $2 {{PLURAL:$2|tarefa incompleta|tarefas incompletas}} ou [[Special:PendingTaskList|{{PLURAL:$2|pendente|pendentes}}]] para terminar a {{PLURAL:$1| instalação|atualização}} do [https://www.semantic-mediawiki.org MediaWiki Semântico]. Um administrador ou outro utilizador com direitos suficientes pode {{PLURAL:$2|completá-la|completá-las}}. Isto deve ser feito antes de adicionar dados novos, para evitar inconsistências.", "smw-install-incomplete-intro-note": "Esta mensagem desaparecerá depois de todas as tarefas relevantes terem sido concluídas.", "smw-pendingtasks-intro-empty": "Nenhuma tarefa relacionada com o MediaWiki Semântico foi classificada como pendente, incompleta ou por executar.", "smw-pendingtasks-intro": "Esta página fornece informações sobre tarefas relacionadas com o MediaWiki Semântico que foram classificadas como pendentes, incompletas ou por executar.", "smw-pendingtasks-setup-no-tasks-intro": "A instalação (ou atualização) foi concluída, não existindo neste momento tarefas pendentes ou por executar.", "smw-pendingtasks-tab-setup": "Configuração", "smw-updateentitycollation-incomplete": "A definição <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> foi alterada recentemente e requer que seja executado o ''script'' <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> para que as entidades sejam atualizadas e contenham o valor correto no campo de ordenação.", "smw-updateentitycountmap-incomplete": "O campo <code>smw_countmap</code> foi acrescentado numa versão recente e requer que seja executado o ''script'' <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> para que as funções possam aceder ao conteúdo deste campo.", "smw-populatehashfield-incomplete": "O preenchimento do campo <code>smw_hash</code> foi saltado durante a configuração, por isso é necessário executar o ''script'' <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-populate-hash-field": "O preenchimento do campo <code>smw_hash</code> foi saltado durante a configuração, por isso é necessário executar o ''script'' <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-elasticstore-indexrebuild": "O <code>ElasticStore</code> foi selecionado como [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore armazenamento padrão], mas a extensão não encontrou nenhum registo de execução do ''script'' <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code>; execute-o como indicado, por favor.", "smw-elastic-rebuildelasticindex-run-incomplete": "O <code>ElasticStore</code> foi selecionado como [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore armazenamento padrão], mas a extensão não encontrou nenhum registo de execução do ''script'' <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code>; execute-o como indicado, por favor.", "smw-pendingtasks-setup-intro": "A {{PLURAL:$1|instalação|atualização}} do <b>MediaWiki Semântico</b> classificou as seguintes tarefas como [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade incompletas] e é esperado que um administrador (ou outro utilizador com direitos suficientes) conclua essas tarefas antes de os utilizadores voltarem a criar ou alterar conteúdo.", "smw-pendingtasks-setup-tasks": "<PERSON><PERSON><PERSON><PERSON>", "smw-filter-count": "N.º de filtros", "smw-es-replication-check": "Verificação da replicação (Elasticsearch)", "smw-es-replication-error": "Problema de replicação do Elasticsearch", "smw-es-replication-file-ingest-error": "Problema de ingestão de ficheiro", "smw-es-replication-maintenance-mode": "Manutenção do Elasticsearch", "smw-es-replication-error-missing-id": "A monitorização da replicação identificou que o artigo \"$1\" (Identificador: $2) está em falta no servidor Elasticsearch.", "smw-es-replication-error-divergent-date": "A monitorização da replicação identificou que, para o artigo \"$1\" (Identificador: $2), a <b>data de modificação</b> apresenta uma discrepância.", "smw-es-replication-error-divergent-date-short": "As seguintes informações de data foram usadas para comparação:", "smw-es-replication-error-divergent-date-detail": "Data de modificação de referência:\n*Elasticsearch: $1 \n*Base de dados: $2", "smw-es-replication-error-divergent-revision": "A monitorização da replicação identificou que, para o artigo \"$1\" (Identificador: $2), a <b>revisão associada</b> apresenta uma discrepância.", "smw-es-replication-error-divergent-revision-short": "Os seguintes dados de revisão associados foram usados para comparação:", "smw-es-replication-error-divergent-revision-detail": "Revisão associada de referência:\n*Elasticsearch: $1 \n*Base de dados: $2", "smw-es-replication-error-maintenance-mode": "A replicação do Elasticsearch está de momento limitada porque opera num [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>modo de manutenção</b>], as alterações das entidades e páginas <b>não</b> são imediatamente visíveis e os resultados das consultas podem conter informação desatualizada.", "smw-es-replication-error-no-connection": "A monitorização da replicação não consegue efetuar verificações, dado que não consegue estabelecer uma ligação ao ''cluster'' Elasticsearch.", "smw-es-replication-error-bad-request-exception": "O gestor da ligação Elasticsearch gerou uma exceção por pedido incorreto (\"Erro HTTP de conflito 400\") que indica um erro persistente durante pedidos de replicação e pesquisa.", "smw-es-replication-error-other-exception": "O gestor da ligação Elasticsearch gerou uma exceção: \"$1\".", "smw-es-replication-error-suggestions": "É sugerido que edite ou purgue a página para remover a discrepância. Se o problema permanecer, então verifique o próprio ''cluster'' Elasticsearch (atribuidor, exceções, espaço em disco, etc.).", "smw-es-replication-error-suggestions-maintenance-mode": "É sugerido que contacte o administrador da wiki para verificar se está a ocorrer uma [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild recriação do índice] ou se o <code>refresh_interval</code> não foi definido com o valor por omissão esperado.", "smw-es-replication-error-suggestions-no-connection": "É sugerido que contacte o administrador da wiki e reporte o problema \"falta de ligação\".", "smw-es-replication-error-suggestions-exception": "Verifique os registos para informação sobre o estado do Elasticsearch, os índices do mesmo, e possíveis problemas de configuração.", "smw-es-replication-error-file-ingest-missing-file-attachment": "A monitorização da replicação identificou que \"$1\" tem em falta uma anotação de [[Property:File attachment|Ficheiro anexo]] o que indica que o processador de ingestão de ficheiros não começou ou ainda não acabou.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Assegure-se de que a tarefa de [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion ingestão de ficheiros] é programada e executada antes de a anotação e o índice do ficheiro serem disponibilizados, por favor.", "smw-report": "Relat<PERSON><PERSON>", "smw-legend": "<PERSON>a", "smw-datavalue-constraint-schema-category-invalid-type": "O esquema anotado \"$1\" é inválido para uma categoria; requer um tipo \"$2\".", "smw-datavalue-constraint-schema-property-invalid-type": "O esquema anotado \"$1\" é inválido para uma propriedade; requer um tipo \"$2\".", "smw-entity-examiner-check": "Execução de {{PLURAL:$1|um examinador|examinadores}} em segundo plano", "smw-entity-examiner-indicator": "Painel de problemas de entidades", "smw-entity-examiner-deferred-check-awaiting-response": "O examinador \"$1\" está neste momento a aguardar uma resposta do servidor.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "Restrição", "smw-entity-examiner-associated-revision-mismatch": "<PERSON><PERSON><PERSON>", "smw-entity-examiner-deferred-fake": "<PERSON><PERSON><PERSON>", "smw-entity-examiner-indicator-suggestions": "Como parte do exame da entidade {{PLURAL:$1|foi encontrado o seguinte problema|foram encontrados os seguintes problemas}} e é sugerido {{PLURAL:$1|que este seja cuidadosamente analisado|que estes sejam cuidadosamente analisados}} e tomadas as medidas devidas.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Restrição|Restrições}}", "smw-indicator-revision-mismatch": "<PERSON><PERSON><PERSON>", "smw-indicator-revision-mismatch-error": "A verificação [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner revisão associada] encontrou uma falta de correspondência para esta entidade entre a revisão referenciada no MediaWiki e a que está a ser associada no MediaWiki Semântico.", "smw-indicator-revision-mismatch-comment": "Uma falta de correspondência normalmente indica que um qualquer processo interrompeu a operação de armazenamento no MediaWiki Semântico. É recomendado rever os registos do servidor e procurar exceções ou outras falhas.", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "{{PLURAL:$1|É apresentado <strong>um</strong> resultado|São apresentados até <strong>$1</strong> resultados}} abaixo{{PLURAL:$1||, começando pelo <strong>$2</strong>º}}."}