{"@metadata": {"authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Neuraxıs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, "smw_viewasrdf": "RDF-mating", "smw_finallistconjunct": " og", "smw_isspecprop": "Denne eigenskapen er ein spesialeigenskap på denne wikien", "smw_concept_description": "Skildring av konseptet «$1»", "smw_no_concept_namespace": "Konsept kan berre bli definerte frå sider i Concept:-namnerommet.", "smw_multiple_concepts": "<PERSON><PERSON> konseptsida kan berre ha <PERSON>in konseptdefinisjon.", "smw_concept_cache_miss": "Konseptet «$1» kan ikkje bli nytta for augneblinken, då wikikonfigurasjonen krev at det blir sett saman fråkopla.\nOm problemet ikkje går vekk etter noko tid, be sideadministratoren om å gjera dette konseptet tilgjengleg.", "smw_baduri": "URI-ar på forma «$1» er ikkje tillatne.", "smw_printername_count": "Tel opp resultata", "smw_printername_csv": "CSV eksport", "smw_printername_json": "JSON eksport", "smw_printername_list": "Liste", "smw_printername_ol": "Nummerering", "smw_printername_ul": "Objektisering", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON><PERSON> tabell", "smw_printername_template": "Mal", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-link": "Vis verdiar som lenkjar", "smw-paramdesc-default": "Tekst som skal visast dersom det ikkje er nokon resultat for spørjinga", "smw-paramdesc-embedonly": "<PERSON><PERSON><PERSON><PERSON> vis overskrifter", "smw-paramdesc-table-transpose": "Vis tabelloverskrifter vertikalt og resultat horisontalt", "smw-paramdesc-dsv-filename": "Namnet på DSV-fila", "smw-paramdesc-filename": "Namnet på utdatafila", "smw-paramdesc-sort": "Eigenskap som spørjinga skal sorterast etter", "smw-paramdesc-order": "Sorteringsrekkjefølgje for spørjinga", "smw-paramdesc-named_args": "<PERSON><PERSON><PERSON> parametrane for malen", "smw-paramdesc-export": "Eksportval", "smw-printername-feed": "RSS- og Atom-straum", "smw-paramdesc-feedtype": "Straumtype", "smw_iq_disabled": "Semantiske spørjingar har blitt slegne av på denne wikien.", "smw_iq_moreresults": "… fleire resultat", "smw_parseerror": "Den gjevne verdien blei ikkje forstått.", "smw_decseparator": ",", "smw_kiloseparator": ".", "smw_notitle": "«$1» kan ikkje bli nytta som sidenamn på denne wikien.", "smw_wrong_namespace": "<PERSON><PERSON> sider i namnerommet «$1» er tillatne her.", "smw_manytypes": "<PERSON><PERSON> enn <PERSON>in type definert for eigenskapen.", "smw_emptystring": "<PERSON><PERSON> strenger blir ikk<PERSON>.", "smw_notinenum": "«$1» er ikkje i lista over moglege verdiar ($2) for denne eigenskapen.", "smw_noboolean": "«$1» blir ikk<PERSON> kjend att som ein Bo<PERSON>-verdi (sant/usant).", "smw_true_words": "true,t,yes,y,ja,j,sant,s", "smw_false_words": "false,f,no,n,nei,usant,u", "smw_nofloat": "«$1» er ikkje eit tal.", "smw_infinite": "Tal so store som «$1» er ikkje støtta.", "smw_nodatetime": "Datoen «$1» vart ikkje forstått.", "smw_toomanyclosing": "«$1» finst for mange gonger i spørjinga.", "smw_noclosingbrackets": "Nokre klammar i spørjinga di («<nowiki>[[</nowiki>») vart ikkje lukka påfølgjande klammar («]]»).", "smw_misplacedsymbol": "Symbolet «$1» vart nytta på ein stad der det ikkje er gagnleg.", "smw_unexpectedpart": "«$1»-delen av spørjinga var uforståeleg.\nResultata kan vera uventa.", "smw_emptysubquery": "Ei underspørjing har ikkje gyldige føresetnader.", "smw_misplacedsubquery": "Ei underspørjing vart nytta på ein stad der underspørjingar ikkje er tillatne.", "smw_valuesubquery": "Undersp<PERSON><PERSON><PERSON><PERSON> er ikkje støtta for verdiar av eigenskapen «$1».", "smw_badqueryatom": "Ein lut («<nowiki>[[…]]</nowiki>») av spørjinga vart ikkje forstått.", "smw_propvalueproblem": "Verdien av eigenskapen «$1» vart ikkje forstått.", "smw_noqueryfeature": "No<PERSON>re spørjefunksjonar er ikkje støtta på denne wikien, og luter av spørjinga vart difor hoppa over ($1).", "smw_noconjunctions": "Konjunksjonar i spørjingar er ikkje støtta på denne wikien, og luter av spørjinga vart difor hoppa over ($1).", "smw_nodisjunctions": "Disjunksjonar i spørjingar er ikkje støtta på denne wikien, og luter av spørjinga vart difor hoppa over ($1).", "smw_querytoolarge": "Følgjande spørjeføresetnader kunne ikkje verta teke omsyn til grunna avgrensingane til wikien når det gjeld spørjestorleik eller djupn: $1", "smw_notemplategiven": "<PERSON>pg<PERSON> ein verdi for parameteren «mal» for at dette spørjeformatet skal verka.", "smw_type_header": "Eigenskapar av typen «$1»", "smw_typearticlecount": "Syner {{PLURAL:$1|éin eigenskap|$1 eigenskapar}} som nyttar denne typen.", "smw_attribute_header": "Sider som nyttar eigenskapen «$1»", "smw_attributearticlecount": "Syner {{PLURAL:$1|éi sida|$1 sider}} som nyttar denne eigenskapen.", "exportrdf": "Eksporter sider til RDF", "smw_exportrdf_docu": "Denne sida lèt deg skaffa data frå ei sida i RDF-format.\nSkriv inn titlar i tekstboksten nedanfor for å eksportera sider, éin tittel per linja.", "smw_exportrdf_recursive": "Eksporter alle relaterte sider rekursivt.\nMerk at resultatet kan vera stort.", "smw_exportrdf_backlinks": "Eksporter òg alle sider som refererer til dei eksporterte sidene.\nLagar ein RDF som ein kan gå gjennom.", "smw_exportrdf_lastdate": "Ikkje eksporter sider som ikkje vart endra sidan det oppgjevne tidspunktet.", "smw_exportrdf_submit": "Eksport", "uriresolver": "URI-løysar", "properties": "Eigenskapar", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "Følgjande eigenskapar er nytta på wikien.", "smw_property_template": "$1 av typen $2 ($3)", "smw_propertylackspage": "Alle eigenskapar burde vore skildra av ei sida.", "smw_propertylackstype": "Ingen type blei oppgjeven for denne eigenskapen (reknar med at typen er $1 mellombels)", "smw_propertyhardlyused": "Denne eigenskapen blir knapt nytta på wikien!", "smw-sp-property-searchform": "Vis eigenskapar som inneheld:", "concepts": "Omgrep", "smw-special-concept-header": "Liste over omgrep", "unusedproperties": "Unytta eigenskapar", "smw-unusedproperties-docu": "<PERSON>i følgjande eigenskpane finst, sjølv om ingen andre sider nyttar dei.", "smw-unusedproperty-template": "$1 av typen $2", "wantedproperties": "Ynskte eigenskapar", "smw-wantedproperties-docu": "<PERSON>i følgjande eigenskapen er nytta på wikien, men har ikkje sider som skildrar dei.", "smw-wantedproperty-template": "$1 (nytta {{PLURAL:$2|éin gong|$2 gonger}})", "smw_purge": "Oppfrisk", "types": "Typar", "smw_types_docu": "Fylgjande er ei lista over alle datatypar som eigenskapar kan ha.\nKvar datatype har ei sida der ekstra informasjon kan verta oppgjeven.", "smw-special-types-no-such-type": "Den angjevne datatypen finst ikkje", "smw_uri_doc": "URI-løysaren implementerer [$1 finning av W3C TAG-ar på «httpRange-14»].\n<PERSON> syter for at menneske ikkje vert til nettstader.", "ask": "Semantisk søk", "smw_ask_sortby": "Sorter etter kolonne (valfritt)", "smw_ask_ascorder": "Stigande", "smw_ask_descorder": "<PERSON><PERSON>k<PERSON><PERSON><PERSON>", "smw_ask_submit": "Finn resultat", "smw_ask_editquery": "<PERSON><PERSON><PERSON>", "smw_add_sortcondition": "[Legg til sorteringsføresetnader]", "smw_ask_hidequery": "<PERSON><PERSON><PERSON>", "smw_ask_help": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_queryhead": "S<PERSON><PERSON><PERSON><PERSON>g", "smw_ask_printhead": "Ekstra utskrifter (valfritt)", "smw_ask_format_as": "Formatér som:", "smw_ask_defaultformat": "standard", "smw_ask_otheroptions": "<PERSON>", "searchbyproperty": "<PERSON><PERSON><PERSON> etter eigenskap", "smw_sbv_docu": "<PERSON><PERSON><PERSON> etter alle sider som har ein viss eigenskap og verdi.", "smw_sbv_novalue": "Skriv inn ein gyldig verdi for eigenskapen, eller sjå alle eigenskapsverdiar for «$1».", "smw_sbv_displayresultfuzzy": "Ei lista over alle sider som har eigenskapen «$1» med verdien «$2».\nSidan det berre kom fram nokre få resultat er òg nære verdiar viste.", "smw_sbv_property": "Eigenskap:", "smw_sbv_value": "Verdi:", "smw_sbv_submit": "Finn resultat", "browse": "Bla gjennom wikien", "smw_browselink": "Bla gjennom eigenskapar", "smw_browse_article": "Skriv inn namnet på sida du vil starta å bla frå.", "smw_browse_go": "Gå", "smw_browse_show_incoming": "syn eigenskapar som lenkjar hit", "smw_browse_hide_incoming": "<PERSON><PERSON><PERSON> eigenskapar som lenkjar hit", "smw_browse_no_outgoing": "Denne sida har ingen eigenskapar.", "smw_browse_no_incoming": "Ingen eigenskapar lenkjar til denne sida.", "smw_inverse_label_default": "$1 av", "smw_inverse_label_property": "Vrengd eigenskapsmerke", "pageproperty": "Sideeigenskapssøk", "smw_pp_docu": "Søk etter all eigenskapstekst på ei viss side.\nSkriv inn både sidenamn og eigenskap.", "smw_pp_from": "Frå side", "smw_pp_type": "Eigenskap", "smw_pp_submit": "Finn resultat", "smw_result_prev": "<PERSON><PERSON><PERSON>", "smw_result_next": "Neste", "smw_result_results": "Resultat", "smw_result_noresults": "Fann ingen resultat.", "smwadmin": "Administratorfunksjonar for Semantic MediaWiki", "smw-admin-setupsuccess": "Lagringseininga blei sett opp.", "smw_smwadmin_return": "Attende til $1", "smw_smwadmin_updatestarted": "Ein ny oppdateringsprosses for å oppfriska dei semantiske dataa blei starta.\nAlle lagra data vil bygt om eller reparert der det trengst.\nDu kan fylgja med på framgangen til oppdateringa på denne spesialsida.", "smw_smwadmin_updatenotstarted": "Ein oppdateringsprosses køyrer frå før.\nStartar ikkje ein ny ein.", "smw_smwadmin_updatestopped": "Alle eksisterande oppdateringsprosessar har blitt stoppa.", "smw_smwadmin_updatenotstopped": "For å stoppa oppdateringsprosessen som køyrer, må du markera boksen for å syna at du verkeleg meiner det.", "smw-admin-docu": "Denne spesialsida hjelper deg under installering og oppgradering av <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a>.\nHugs å ta kopiar av viktige data før du køyrer administrerande funksjonar.", "smw-admin-db": "Databaseinnstallering og -oppgradering", "smw-admin-dbdocu": "Semantic MediaWiki krev nokre utvidingar i MediaWiki-databasen for å kunna lagra semantiske data.\nFunksjonen under syter for at databasen din er sett opp rett.\nEndringar gjort i dette steget har ikkje nokon effekt på resten av MediaWiki-databasen og kan enkelt rullast attende om det skulle vera ynskjeleg.\nDenne oppsettsfunksjonen kan køyrast fleire gongen utan at ein gjer nokon skade, men han trengst berre éin gong under installering eller oppgradering.", "smw-admin-permissionswarn": "Om operasjonen mislukkast med SQL-feil, har sannsynlegvis ikkje databasebrukaren nytta av wikien din dei rette løyva (sjekk LocalSettings.php).\nAnten gje denne brukaren dei rette løyva til å oppretta og sletta tabellar og mellombels skriv inn innloggingsinformasjonen til database-rooten din i LocalSettings.php, eller køyr vedlikehaldsskriptet <code>setupStore.php</code> som kan nytta løyva til AdminSettings.php.", "smw-admin-dbbutton": "Set i verk eller oppgrader tabellar", "smw-admin-announce": "<PERSON><PERSON>gjer wikien din", "smw-smwadmin-refresh-title": "Datareparasjon- og oppdatering", "smw_smwadmin_datarefresh": "Dataombygging", "smw_smwadmin_datarefreshdocu": "Det er mogleg å få attende alle Semantic MediaWiki-dataa basert på det noverande innahaldet til wikien.\nDette kan vera nyttig for å reparera øydelagde data eller for å oppfriska dataa om det interne formatet har blitt endra grunna programvareoppdatering.\nOppdateringa blir utført sida for sida og vil ikkje vera ferdig med ein gong.\nDet fylgjande syner om ei oppdatering er på gong og lèt deg byrja eller stoppa oppdateringar (viss ikkje denne moglegheita er deaktivert av sideadministratoren).", "smw_smwadmin_datarefreshprogress": "<strong><PERSON><PERSON> opp<PERSON><PERSON> held allereie på.</strong>\nDet er normalt at oppdateringa går sakte då ho berre oppfriskar data i små bitar kvar gong ein brukar er innom wikien.\nFor å fullføra denne oppdateringa på ein kjappare måte, kan du setja i gang MediaWiki-vedlikehaldsskriptet <code>runJobs.php</code> (nytt valet <code>--maxjobs 1000</code> for å avgrensa talet på oppdetaringar som blir gjort i eitt stykke).\nEstimert framdrift på den noverande oppdateringa:", "smw_smwadmin_datarefreshbutton": "Start oppdatering", "smw_smwadmin_datarefreshstop": "Stopp denne <PERSON>", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, eg er sikker.", "smw-admin-support": "<PERSON><PERSON><PERSON> stø<PERSON>", "smw-admin-supportdocu": "Diverse ressursar kan kanskje hjelpa deg om du skulle få problem:", "smw-admin-installfile": "Om du møter på problem under installeringa, start med å studera retningslinene i <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">INSTALL-fila</a>.", "smw-admin-smwhomepage": "Den komplette brukardokumentasjonen for Semantic MediaWiki finn du på <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "<PERSON>il kan bli rapporterte til <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>.", "smw-admin-questions": "<PERSON>m du har fleire spø<PERSON><PERSON><PERSON><PERSON> el<PERSON> forslag, bli med i diskusjonen på <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">Semantic MediaWiki-brukarforumet</a>.", "smw-createproperty-isproperty": "Dette er ein eigenskap av typen $1.", "smw-createproperty-allowedvals": "Dei tillatne verdiane for denne eigenskapen er:", "smw_unknowntype": "Typen «$1» som er definert for eigenskapen er ikkje støtta.", "smw_concept_header": "Sider av konseptet «$1»", "smw_conceptarticlecount": "Syner nedanfor {{PLURAL:$1|éi side|$1 sider}}", "smw-livepreview-loading": "Lastar inn&nbsp;…", "smw-type-tab-properties": "Eigenskapar", "smw-listingcontinuesabbrev": "vidare", "smw-showingresults": "Nedanfor er opp til {{PLURAL:$1|<strong>eitt</strong>|<strong>$1</strong>}} resultat som byrjar med nummer <strong>$2</strong> vist{{PLURAL:$1||e}}."}