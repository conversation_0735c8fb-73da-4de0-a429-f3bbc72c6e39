{"@metadata": {"authors": ["1233qwer1234qwer4", "888levant888", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Athena Atterdag", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Drbug", "Edible <PERSON>", "<PERSON>", "Eduardoad<PERSON>", "Eleferen", "Evs", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Happy13241", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Iniquity", "Innv", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kghbln", "LeonardoGW", "<PERSON><PERSON>", "Lugimax", "<PERSON><PERSON><PERSON>", "Mailman", "<PERSON><PERSON>", "Marshmallych", "McDut<PERSON><PERSON>", "Megakott", "Meshkov.a", "Mouse21", "<PERSON><PERSON><PERSON>", "NBS", "Nemo bis", "<PERSON><PERSON>", "Nk88", "Okras", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Putnik", "QuestPC", "Redredsonia", "Rubin<PERSON>", "<PERSON><PERSON><PERSON>", "Silicium Power", "Soul Train", "Staspotanin2", "Tourorist", "<PERSON>", "Vanished 904959300", "Vlad5250", "Wikisaurus", "Wirbel78", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Александ<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>а<PERSON><PERSON>р Комдошев", "Туллук", "Умар", "Ядерный Трамвай", "아라"]}, "smw-desc": "Делает вашу вики более доступной — для машин ''и'' людей ([https://www.semantic-mediawiki.org/wiki/Help:User_manual документация в сети])", "smw-error": "Ошибка", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] был установлен и включен, но отсутствует соответствующий [https://www.semantic-mediawiki.org/wiki/Help:Upgrade ключ обновления].", "smw-upgrade-release": "Версия", "smw-upgrade-progress": "Прогресс", "smw-upgrade-progress-create-tables": "Создание (или обновление) таблиц и индексов...", "smw-upgrade-progress-post-creation": "Выполнение задач по созданию записей...", "smw-upgrade-error-title": "Ошибка » Semantic MediaWiki", "smw-upgrade-error-why-title": "Почему я вижу эту страницу?", "smw-upgrade-error-how-title": "Как исправить эту ошибку?", "smw-extensionload-error-why-title": "Почему я вижу эту страницу?", "smw-extensionload-error-how-title": "Как исправить эту ошибку?", "smw-upgrade-maintenance-why-title": "Почему я вижу эту страницу?", "smw-semantics-not-enabled": "Функциональность Semantic MediaWiki не подключена на этой вики.", "smw_viewasrdf": "RDF источник", "smw_finallistconjunct": " и", "smw-factbox-head": "... больше о \"$1\"", "smw-factbox-facts": "Факты", "smw-factbox-facts-help": "Показывает высказывания и факты, которые были созданы пользователем", "smw-factbox-attachments": "Вложения", "smw-factbox-attachments-value-unknown": "Н/Д", "smw-factbox-attachments-is-local": "Является локальным", "smw-factbox-attachments-help": "Показывает доступные вложения", "smw-factbox-facts-derived": "Полученные факты", "smw-factbox-facts-derived-help": "Показывает факты, которые были получены из правил, или с помощью других методов рассуждений", "smw_isspecprop": "Это свойство относится к числу специальных свойств данного вики-сайта.", "smw-concept-cache-header": "Использование кэша", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count concept cache] содержит {{PLURAL:$1|'''one''' entity|'''$1''' entities}} ($2).", "smw-concept-no-cache": "Кэш отсутствует.", "smw_concept_description": "Описание концепта «$1»", "smw_no_concept_namespace": "Концепты могут определяться только на страницах, принадлежащих пространству имён «Концепт:» («Concept:»).", "smw_multiple_concepts": "Страница представления может содержать только одно определение представления.", "smw_concept_cache_miss": "Концепт «$1» в настоящий момент не может быть использовано, так как настройка вики-сайта требует, чтобы его результат определялся в фоновом режиме. Если данное сообщение не исчезнет через некоторое время, обратитесь к администратору вики-сайта для включения данного концепта.", "smw_noinvannot": "Обратным свойствам не могут быть присвоены значения.", "version-semantic": "Семантические расширения", "smw_baduri": "Извините, но ссылки из диапазона \"$1\" не доступны отсюда.", "smw_printername_count": "Результаты подсчёта", "smw_printername_csv": "Экспорт CSV", "smw_printername_dsv": "Экспорт DSV", "smw_printername_debug": "Отладка запросов (для экспертов)", "smw_printername_embedded": "Включаемое содержимое страниц", "smw_printername_json": "Экспорт JSON", "smw_printername_list": "Список", "smw_printername_plainlist": "Обычный список", "smw_printername_ol": "Нумерованный список", "smw_printername_ul": "Маркированный список", "smw_printername_table": "Таблица", "smw_printername_broadtable": "Широкая таблица", "smw_printername_template": "Шабл<PERSON>н", "smw_printername_templatefile": "Файл шаблона", "smw_printername_rdf": "RDF-экспорт", "smw_printername_category": "Категория", "validator-type-class-SMWParamSource": "текст", "smw-paramdesc-limit": "Максимальное число возвращаемых результатов", "smw-paramdesc-offset": "Смещение первого результата", "smw-paramdesc-headers": "Показывать заголовки/названия свойств", "smw-paramdesc-mainlabel": "Обозначение, даваемое названию главной страницы", "smw-paramdesc-link": "Показывать значения как ссылки", "smw-paramdesc-intro": "Текст для отображения перед результатами запроса, если они есть", "smw-paramdesc-outro": "Текст для отображения после результатов запроса, если они есть", "smw-paramdesc-default": "Текст для отображения, если нет результатов запроса", "smw-paramdesc-sep": "Разделитель значений", "smw-paramdesc-propsep": "Разделитель между свойствами в конечной записи", "smw-paramdesc-valuesep": "Разделитель между значениями переменной для этого свойства", "smw-paramdesc-showsep": "Показать разделитель в верхней части файла CSV (\"sep=<value>\")", "smw-paramdesc-distribution": "Вместо отображения всех значений, подсчитать и отобразить их вхождения.", "smw-paramdesc-distributionsort": "Сортировать распределение значений по частоте появления.", "smw-paramdesc-distributionlimit": "Ограничение распределения значений для подсчёта только некоторыми значениями.", "smw-paramdesc-aggregation": "Укажите, к чему должна относиться агрегация", "smw-paramdesc-template": "Название шаблона, с помощью которого будут выводиться данные", "smw-paramdesc-columns": "Количество столбцов для вывода результатов поиска", "smw-paramdesc-userparam": "Значение, передаваемое в каждый вызов шаблонов, если шаблон используется", "smw-paramdesc-class": "Дополнительный класс CSS для списка", "smw-paramdesc-introtemplate": "Название шаблона для отображения перед результатами запроса, если они есть", "smw-paramdesc-outrotemplate": "Название шаблона для отображения после результатов запроса, если они есть", "smw-paramdesc-embedformat": "HTML-тег, используемый для обозначения заголовков", "smw-paramdesc-embedonly": "Не отображать заголовки", "smw-paramdesc-table-class": "Дополнительный класс CSS для таблиц", "smw-paramdesc-table-transpose": "Отобразить заголовки таблицы по вертикали, а результаты — по горизонтали", "smw-paramdesc-rdfsyntax": "Какой синтаксис RDF использовать", "smw-paramdesc-csv-sep": "Использовать разделитель", "smw-paramdesc-csv-valuesep": "Использовать разделитель значений", "smw-paramdesc-csv-merge": "Объединить значения столбцов и колонок с идентичным идентификатором субъекта (aka первая колонка)", "smw-paramdesc-csv-bom": "Добавить BOM (символ для сигнальной сущности) в верху выходного файла", "smw-paramdesc-dsv-separator": "Использовать разделитель", "smw-paramdesc-dsv-filename": "Имя файла DSV", "smw-paramdesc-filename": "Имя для выходного файла", "smw-smwdoc-description": "Показывает таблицу всех параметров, которые могут использоваться для указанного формата вывода результата вместе со значениями по умолчанию и описаниями.", "smw-smwdoc-default-no-parameter-list": "Этот результат не предоставляет специфичные для формата параметры.", "smw-smwdoc-par-format": "Результирующий формат для отображения документации по параметрам.", "smw-smwdoc-par-parameters": "Какие параметры показыватьː «specific» — для тех, которые добавлены форматом, «base» — для тех, которые доступных во всех форматах, и «all» — для обоих.", "smw-paramdesc-sort": "Свойство, по которому сортировать запрос", "smw-paramdesc-order": "Порядок сортировки запроса", "smw-paramdesc-searchlabel": "Текст для продолжения поиска", "smw-paramdesc-named_args": "Имена аргументов, передаваемых в шаблон", "smw-paramdesc-template-arguments": "Определяет, как названные аргументы передаются к шаблону", "smw-paramdesc-import-annotation": "Дополнительные аннотированные данные, которые будут скопированы во время парсинга субъекта.", "smw-paramdesc-export": "Параметры экспорта", "smw-paramdesc-prettyprint": "Красивый вывод на печать, который отображает дополнительные отступы и переводы строк", "smw-paramdesc-json-unescape": "Выводить неэкранированные слеши и многобайтные символы Юникода.", "smw-paramdesc-json-type": "Тип сериализации", "smw-paramdesc-source": "Альтернативный источник запроса", "smw-paramdesc-jsonsyntax": "Синта<PERSON><PERSON><PERSON><PERSON>, который будет использоваться", "smw-printername-feed": "Лента RSS и Atom", "smw-paramdesc-feedtype": "<PERSON>и<PERSON> канала", "smw-paramdesc-feedtitle": "Текст для использования в качестве названия канала", "smw-paramdesc-feeddescription": "Текст для использования в качестве описание канала", "smw-paramdesc-feedpagecontent": "Содержимое страницы, которое будет отображаться на канале", "smw-label-feed-description": "$2-канал $1", "smw-paramdesc-mimetype": "Тип медиа<PERSON><PERSON><PERSON><PERSON><PERSON> (тип MIME) для файла на выходе", "smw_iq_disabled": "Извините, но встроенные запросы отключены для этого сайта.", "smw_iq_moreresults": "… следующие результаты", "smw_parseerror": "Переданное значение не было понято.", "smw_decseparator": ",", "smw_kiloseparator": "", "smw_notitle": "“$1” не может быть использован как заголовок статьи на данном сайте.", "smw_noproperty": "«$1» не может использоваться как имя свойства в вики.", "smw_wrong_namespace": "Здесь допустимы только страницы из пространства имён «$1».", "smw_manytypes": "Более одного типа определено для свойства.", "smw_emptystring": "Пустые строки недопустимы.", "smw_notinenum": "«$1» не входит в список ($2) [[Property:Allows value|допустимых значений]] для свойства «$3».", "smw-datavalue-constraint-error-allows-value-list": "«$1» не входит в список ($2) [[Property:Allows value|допустимых значений]] для свойства «$3».", "smw-datavalue-constraint-error-allows-value-range": "«$1» не входит в список ($2) ограничений для [[Property:Allows value|допустимых значений]] для свойства «$3».", "smw_noboolean": "«$1» — не булево значение (да/нет).", "smw_true_words": "да,t,yes,д,истина,и,true", "smw_false_words": "нет,f,no,n,н,ложь,л,false", "smw_nofloat": "«$1» — не число.", "smw_infinite": "Столь длинные числа как $1 не поддерживаются.", "smw_unitnotallowed": "«$1» не объявлена ​​как допустимая единица измерения этого свойства.", "smw_nounitsdeclared": "Для данного свойства не объявлены единицы измерения.", "smw_novalues": "Не указаны значения.", "smw_nodatetime": "Дата «$1» не распознана.", "smw_toomanyclosing": "Ошибка: Слишком много вхождений “$1” в данном запросе.", "smw_noclosingbrackets": "Открывающаяся пара скобок «<nowiki>[[</nowiki>» не была закрыта парой соответствующих ей закрывающих скобок «]]» в данном запросе.", "smw_misplacedsymbol": "Ошибка: Использование символа “$1” в данном месте лишено смысла.", "smw_unexpectedpart": "Ошибка: Часть “$1” запроса не была распознана. Результаты могут отличаться от ожидаемых.", "smw_emptysubquery": "Ошибка: В одном из подзапросов не указано правильного знака условия.", "smw_misplacedsubquery": "Ошибка: Подзапрос используется в месте, где подзапросы не разрешены.", "smw_valuesubquery": "Подзапросы не поддерживаются для значений свойства «$1».", "smw_badqueryatom": "Часть запроса «<nowiki>[[…]]</nowiki>» не была разобрана.", "smw_propvalueproblem": "Ошибка: Значение свойства “$1” не разобрано.", "smw_noqueryfeature": "Часть запроса была опущена, так как некоторые из возможностей языка запросов не поддерживаются на этом вики-сайте ($1).", "smw_noconjunctions": "Часть запроса была опущена, так как операция «Логическое И» не поддерживается на этом вики-сайте ($1).", "smw_nodisjunctions": "Ошибка: Диз<PERSON>юнкции (логическое ИЛИ) не поддерживаются данным сайтом, поэтому использующая их часть запроса была проигнорирована ($1).", "smw_querytoolarge": "{{PLURAL:$2|1=Указанное условие|$2 указанных условия|$2 указанных условий}} запроса не могут быть выполнены из-за ограничения на глубину или размер запроса: <code>$1</code>.", "smw_notemplategiven": "Чтобы данный запрос выполнялся, необходимо задать значение для параметра «template».", "smw_db_sparqlqueryproblem": "Не удалось получить результат запроса к базе данных SPARQL. Эта может быть временная ошибка или проблема в программном обеспечении базы данных.", "smw_db_sparqlqueryincomplete": "Поиск ответа на запрос оказался слишком сложным и был прерван. Некоторые результаты могут быть не показаны. По возможности попробуйте упростить запрос.", "smw_type_header": "Свойства типа “$1”", "smw_typearticlecount": "{{PLURAL:$1|Отображается|Отображаются}} $1 {{PLURAL:$1|свойство|свойства|свойств}} этого типа.", "smw_attribute_header": "Страницы, использующие свойство “$1”", "smw_attributearticlecount": "{{PLURAL:$1|Отображается|Отображаются}} $1 {{PLURAL:$1|страница, использующая|страницы, использующие|страниц, использующих}} это свойство.", "smw-propertylist-subproperty-header": "Подсвойства", "smw-propertylist-redirect-header": "Синонимы", "smw-propertylist-error-header": "Страницы с неправильными назначениями", "smw-propertylist-count": "Показ $1 {{PLURAL:$1|1=связанной сущности|связанных сущностей}}.", "smw-propertylist-count-with-restricted-note": "Показ $1 {{PLURAL:$1|1=связанной сущности|связанных сущностей}} (доступно больше, но отобразить можно только «$2»).", "smw-propertylist-count-more-available": "Показ $1 {{PLURAL:$1|1=связанной сущности|связанных сущностей}} (доступно больше).", "specialpages-group-smw_group-maintenance": "Обслуживание", "specialpages-group-smw_group-properties-concepts-types": "Свойства, концепты и типы", "specialpages-group-smw_group-search": "Обзор и поиск", "exportrdf": "Экспорт страниц в RDF", "smw_exportrdf_docu": "Эта страница позволяет экспортировать части статьи в формате RDF. Наберите заголовки необходимых статей по одному на строку.", "smw_exportrdf_recursive": "Рекурсивный экспорт всех связанных страниц. Результат этой операции может быть очень большим!", "smw_exportrdf_backlinks": "Также экспортировать все страницы, которые ссылаются на экспортируемые страницы. Генерирует RDF с поддержкой полноценной навигации.", "smw_exportrdf_lastdate": "Не экспортировать страницы, которые не менялись с указанной даты.", "smw_exportrdf_submit": "Экспорт", "uriresolver": "Преобразователь URI", "properties": "Свойства", "smw-categories": "Категории", "smw_properties_docu": "Следующие свойства используются на данном сайте.", "smw_property_template": "$1 имеет тип $2, ($3 {{PLURAL:$3|использование|использования|использований}})", "smw_propertylackspage": "Каждое свойство должно иметь свою страницу описания!", "smw_propertylackstype": "Данному свойству не сопоставлен тип данных (по умолчанию будет использоваться тип $1).", "smw_propertyhardlyused": "Это свойство почти не используется на сайте.", "smw-property-name-invalid": "Свойство $1 не может быть использовано (недопустимое имя свойства).", "smw-property-name-reserved": "«$1» входит в число зарезервированных имён и не должно быть использовано как свойство. Информация о том, почему это имя зарезервировано, может содержаться в [https://www.semantic-mediawiki.org/wiki/Help:Property_naming этой справочной странице].", "smw-sp-property-searchform": "Показать свойства, которые содержат:", "smw-sp-property-searchform-inputinfo": "Ввод чувствителен к регистру, и когда используется для фильтрации, отображаются только те свойства, которые соответствуют условию.", "smw-special-property-searchform": "Показать свойства, которые содержат:", "smw-special-property-searchform-inputinfo": "Ввод чувствителен к регистру, и когда используется для фильтрации, отображаются только те свойства, которые соответствуют условию.", "smw-special-property-searchform-options": "Параметры", "smw-special-wantedproperties-filter-label": "Фильтр:", "smw-special-wantedproperties-filter-none": "Ничего", "smw-special-wantedproperties-filter-unapproved": "неподтвержденный", "smw-special-wantedproperties-filter-unapproved-desc": "Опция фильтра использована в связи с режимом полномочий", "concepts": "Концепты", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Концепты Концепт] можно рассматривать как «динамическую категорию», т.е. как совокупность страниц, которая не создана вручную, а собрана Семантической Медиавики из описания данного запроса.", "smw-special-concept-header": "Список концептов", "smw-special-concept-count": "В списке {{PLURAL:$1|1=числится следующий|числятся следующие}} {{PLURAL:$1|$1 концепция|$1 концепции|$1 концепций|1=концепция}}", "smw-special-concept-empty": "Концепции не найдены.", "unusedproperties": "Неиспользуемые свойства", "smw-unusedproperties-docu": "На этой странице перечислены [https://www.semantic-mediawiki.org/wiki/Wanted_properties неиспользуемые свойства], которые определены, но при этом ни одна страница не использует их. См. также:[[Special:Properties|используемые]] и [[Special:WantedProperties|требуемые свойства]].", "smw-unusedproperty-template": "$1 имеет тип $2", "wantedproperties": "Неописанные свойства", "smw-wantedproperties-docu": "На этой странице перечислены [https://www.semantic-mediawiki.org/wiki/Wanted_properties необходимые свойства], которые используются на вики, но не имеют страницу с их описанием. Для дифференцированного подхода, см. служебные страницы с [[Special:Properties|заполненными]] или [[Special:UnusedProperties|неиспользуемыми свойствами]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|использование|использования|использований}})", "smw-special-wantedproperties-docu": "На этой странице перечислены [https://www.semantic-mediawiki.org/wiki/Wanted_properties необходимые свойства], используемые в вики, но не имеющие своей страницы описания. Для раздельного просмотре см. служебные страницы со списком [[Special:Properties|доступных]] или [[Special:UnusedProperties|неиспользуемых]] свойств.", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|использование|использования|использований}})", "smw_purge": "Обновить", "smw-purge-update-dependencies": "Semantic MediaWiki очищает кэш текущей страницы из-за каких-то устаревших зависимостей, которые она обнаружила, и которые нуждаются в обновлении.", "smw-purge-failed": "Semantic MediaWiki пыталось обновить страницу, но неуспешно", "types": "Типы", "smw_types_docu": "Список [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes доступных типов данных], в котором каждый [https://www.semantic-mediawiki.org/wiki/Help:Datatype тип данных] представляет собой уникальный набор атрибутов для описания значения с точки зрения хранения и отображения характеристик, которые можно задавать для свойств.", "smw-special-types-no-such-type": "\"$1\" неизвестен или не был указан как действительный тип данных.", "smw-statistics": "Семантическая статистика", "smw-statistics-cached": "Семантическая статистика (кэшированная)", "smw-statistics-entities-total": "Сущности (всего)", "smw-statistics-entities-total-info": "Примерный подсчет строк сущностей. Он включает свойства, понятия, и любые другие зарегистрированные репрезентации объектов, которые требуют назначения идентификатора.", "smw-statistics-property-instance": "{{PLURAL:$1|1=Значение|Значения|Значений}} свойства (всего)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|1=Свойство|Свойства|Свойств}}]] (всего)", "smw-statistics-property-total-info": "Общее количество зарегистрированных свойств.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|1=Свойство|Свойства|Свойств}} (всего)", "smw-statistics-property-used": "{{PLURAL:$1|Свойство|Свойства}} (используется по крайней мере с одним значением)", "smw-statistics-property-page": "{{PLURAL:$1|Свойство|Свойства|Свойств}} (зарегистрировано на странице)", "smw-statistics-property-type": "{{PLURAL:$1|Свойство|Свойства|Свойств}} (назначено для типа данных)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Запрос|Запроса|Запросов}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Запрос|Запросы}}]] (встроено, всего)", "smw-statistics-query-format": "Формат <code>$1</code>", "smw-statistics-query-size": "Размер запроса", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|1=Концепция|Концепции|Концепций}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|1=Концепция|Концепции|Концепций}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Подобъект|Подобъекта|Подобъектов}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Подобъект|Подобъекта|Подобъектов}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|1=Тип данных|Типа данных|Типов данных}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Значение свойства|Значения свойства|Значений свойств}} ([[Special:ProcessingErrorList|{{PLURAL:$1|неправильная аннотация|неправильные аннотации|неправильных аннотаций}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Значение свойства|Значения свойства|Значений свойств}} ({{PLURAL:$1|неправильная аннотация|неправильные аннотации|неправильных аннотаций}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Устаревший объект|Устаревшие объекты}}]", "smw-statistics-delete-count-info": "От сущностей, помеченных на удаление, следует избавляться регулярно с помощью скриптов обслуживания.", "smw_uri_doc": "Преобразователь URI осуществляет [$1 W3C поиск http тэгов с использованием Range-14].\nОн обеспечивает доставку RDF-представления (для машин) или вики-страницы (для людей) в зависимости от запроса.", "ask": "Семантический поиск", "smw-ask-help": "Этот раздел содержит ссылки, которые помогают объяснить, как использовать синтаксис <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecting pages] описывает, как выбирать страницы и строить условия\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Search operators] список доступных операторов поиска, включая операторы для запроса диапазона и знаки подстановки\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Displaying information] выделяет использование распечатки условий и опций форматирования", "smw_ask_sortby": "Сортировать по столбцу (необязательно)", "smw_ask_ascorder": "По возрастанию", "smw_ask_descorder": "По убыванию", "smw-ask-order-rand": "случайно", "smw_ask_submit": "Найти", "smw_ask_editquery": "Редактировать запрос", "smw_add_sortcondition": "[Добавить условие сортировки]", "smw-ask-sort-add-action": "Добавить условие сортировки", "smw_ask_hidequery": "Скрыть запрос (компактный вид)", "smw_ask_help": "Помощь по составлению запросов", "smw_ask_queryhead": "Условие", "smw_ask_printhead": "Выбор распечатки", "smw_ask_printdesc": "(добавляйте по одному названию свойства на строку)", "smw_ask_format_as": "Форматировать как:", "smw_ask_defaultformat": "по умолчанию", "smw_ask_otheroptions": "Другие настройки", "smw-ask-otheroptions-info": "Этот раздел содержит параметры, которые изменяют настройки вывода. Описания параметров можно просмотреть, наведя на них курсор мыши.", "smw-ask-otheroptions-collapsed-info": "Пожалуйста, используйте значок плюса для просмотра всех доступных параметров", "smw_ask_show_embed": "Показать встроенный код", "smw_ask_hide_embed": "Скрыть встроенный код", "smw_ask_embed_instr": "Для линейного встраивания этого запроса в вики-страницу, используйте приведённых ниже код.", "smw-ask-delete": "Удалить", "smw-ask-sorting": "Сортировка", "smw-ask-options": "Настройки", "smw-ask-options-sort": "Настройки сортировки", "smw-ask-format-options": "Формат и опции", "smw-ask-parameters": "Параметры", "smw-ask-search": "Поиск", "smw-ask-debug": "Отладка", "smw-ask-debug-desc": "Генерирует информацию об отладке запроса", "smw-ask-no-cache": "Отключить кэш", "smw-ask-no-cache-desc": "Результаты без кеша запроса", "smw-ask-result": "Результат", "smw-ask-empty": "Очистить все записи", "smw-ask-download-link-desc": "Загружать запрошенные результаты в формате $1", "smw-ask-format": "Формат", "smw-ask-format-selection-help": "Справка для выбранного формата: $1.", "smw-ask-condition-change-info": "Условие было изменено, и поисковой системе требуется повторить запрос для получения результатов, соответствующих новым требованиям.", "smw-ask-input-assistance": "Помощь при вводе", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Помощь по вводу] предоставляется для полей печати, сортировки и условия. Поле условия должно иметь одну из следующих приставок:", "smw-ask-condition-input-assistance-property": "<code>p:</code> для включения подсказок о свойствах (к примеру <code>[[p:Имеет ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> для получения подсказок по категориям", "smw-ask-condition-input-assistance-concept": "<code>con:</code> для получения подсказок по концепции", "smw-ask-format-change-info": "Формат был изменен и требуется совершить запрос снова, для соответствия новых параметров и настроек визуализации.", "smw-ask-format-export-info": "Выбранный формат является форматом экспорта, который не имеет визуальной репрезентации, поэтому результаты доступны только после загрузки.", "smw-ask-query-search-info": "На запрос <code><nowiki>$1</nowiki></code> был получен ответ из {{PLURAL:$3|1=<code>$2</code> (из кеша)|<code>$2</code> (из кеша)|<code>$2</code>}} за $4 {{PLURAL:$4|секунду|секунды|секунд}}.", "smw-ask-extra-query-log": "<PERSON><PERSON><PERSON><PERSON><PERSON> запрос<PERSON>", "smw-ask-extra-other": "Друг<PERSON>й", "searchbyproperty": "Искать по свойству", "processingerrorlist": "Список ошибок обработки", "constrainterrorlist": "Список ошибок ограничений", "propertylabelsimilarity": "Сообщение о похожести метки свойств", "missingredirectannotations": "Отсутствуют аннотации перенаправлений", "smw-processingerrorlist-intro": "Следующий список предоставляет собой обзор [https://www.semantic-mediawiki.org/wiki/Processing_errors ошибок обработки], которые возникли в связи с [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Рекомендуется регулярно следить за этим списком и исправлять некорректные аннотации значений.", "smw_sbv_docu": "Искать все страницы, которые содержат указанное свойство и значение.", "smw_sbv_novalue": "Укажите значение или просмотрите все значения свойства $1.", "smw_sbv_displayresultfuzzy": "Список всех страниц, содержащих свойство «$1» со значением «$2».\nТак как количество точных результатов невелико, также показаны страницы, содержащие близкие значения данного свойства.", "smw_sbv_property": "Свойство:", "smw_sbv_value": "Значение:", "smw_sbv_submit": "Найти", "browse": "Об<PERSON><PERSON>р страниц", "smw_browselink": "Узнать свойства", "smw_browse_article": "Введите имя страницы для начала обзора.", "smw_browse_go": "Перейти", "smw_browse_show_incoming": "Показать входящие свойства", "smw_browse_hide_incoming": "Скрыть свойства, ссылающиеся сюда", "smw_browse_no_outgoing": "Эта страница не содержит свойств.", "smw_browse_no_incoming": "Нет свойств, ссылающихся на эту страницу.", "smw-browse-from-backend": "В настоящее время идёт получение информации от серверного приложения.", "smw-browse-intro": "Эта страница предоставляет детали о субъекте или экземпляре сущности, пожалуйста введите имя объекта, который будет инспектирован.", "smw-browse-invalid-subject": "Валидация субъекта вернула ошибку \"$1\" .", "smw-browse-api-subject-serialization-invalid": "Субъект имеет неверный формат сериализации.", "smw-browse-js-disabled": "Есть подозрение, что JavaScript отключён или недоступен. Мы рекомендуем использовать браузер, в котором он поддерживается. Другие варианты обсуждаются на странице параметра конфигурации [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Показать группы", "smw-browse-hide-group": "Скрыть группы", "smw-noscript": "Эта страница или действие требует работы JavaScript. Пожалуйста, включите JavaScript в вашем браузере, или используйте браузер, в котором он поддерживается, чтобы эта функциональность могла предоставляться по запросу. Если Вам нужна дополнительная поддержка, пожалуйста, посмотрите страницу справки [https://www.semantic-mediawiki.org/wiki/Help:Noscript «Noscript»]", "smw_inverse_label_default": "$1 из", "smw_inverse_label_property": "Метка обратного свойства", "pageproperty": "Страница поиска свойств", "smw_pp_docu": "Введите страницу и свойство, либо просто свойство для получения всех назначенных значений.", "smw_pp_from": "Со страницы:", "smw_pp_type": "Свойство:", "smw_pp_submit": "Поиск результатов", "smw_result_prev": "Предыдущая", "smw_result_next": "Следующая", "smw_result_results": "Результаты", "smw_result_noresults": "Извините, но ничего не найдено.", "smwadmin": "Панель инструментов Semantic MediaWiki", "smw-admin-statistics-job-title": "Статистика заданий", "smw-admin-statistics-job-docu": "Статистика заданий отображает информацию о запланированных заданиях Semantic MediaWiki, которые ещё не были выполнены. Количество заданий может быть неточным или может содержать неудачные попытки их выполнения. Обратитесь к [https://www.mediawiki.org/wiki/Special:MyLanguage/manual:Job_queue к руководству] за дополнительной информацией.", "smw-admin-statistics-querycache-title": "Кэ<PERSON> запросов", "smw-admin-statistics-querycache-disabled": "[https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] не был включён на этой вики, поэтому статистика недоступна.", "smw-admin-statistics-semanticdata-overview": "Обзор", "smw-admin-permission-missing": "Доступ к этой странице ограничен. Пожалуйста, обратитесь к [https://www.semantic-mediawiki.org/wiki/Help:Permissions справочоной] странице помощи за подробной информацией.", "smw-admin-setupsuccess": "Система хранения была установлена.", "smw_smwadmin_return": "Вернуться к $1", "smw_smwadmin_updatestarted": "Запущен новый процесс обновления семантических данных.\nВсе сохранённые данные будут перестроены и восстановлены, где это необходимо.\nВы можете следить за ходом обновления на этой служебной странице.", "smw_smwadmin_updatenotstarted": "Уже запущен один процесс обновления.\nДругой не создаётся.", "smw_smwadmin_updatestopped": "Все существующие процессы обновления остановлены.", "smw_smwadmin_updatenotstopped": "Чтобы остановить запущенный процесс обновления, вы должны поставить отметку, подтверждающую, что вы действительно уверены в этом решении.", "smw-admin-docu": "Эта служебная страница поможет вам в процессе установки, обновления, обслуживания и использования расширения <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a>.\nНе забывайте производить резервное копирование важных данных перед выполнением административных действий.", "smw-admin-environment": "Оболочка программного обеспечения", "smw-admin-db": "Настройка базы данных", "smw-admin-db-preparation": "Идёт установка таблицы. Это может занять некоторое время, прежде чем отобразятся результаты и возможная оптимизация таблицы.", "smw-admin-dbdocu": "Semantic MediaWiki для хранения семантических данных требует своей собственной структуры базы данных. Она является независимой от MediaWiki, а потому не влияет на остальные инсталляции MediaWiki.\n\nХотя эта процедура может быть многократно выполнена без каких-либо последствий, её запуск необходим только один раз — во время установки или обновления Semantic MediaWiki.", "smw-admin-permissionswarn": "Причиной сбоев при выполнении SQL-команд может быть отсутствие необходимых прав у пользователя, от имени которого вы подключаетесь к базе данных вики (проверьте файл LocalSettings.php).\nПопробуйте предоставить этому пользователю дополнительные права на создание и удаление таблиц; временно введите логин «root» в файле LocalSettings.php, или используйте обслуживающий скрипт <code>setupStore.php</code>, который может использовать настройки администратора.", "smw-admin-dbbutton": "Инициализировать или обновить таблицы", "smw-admin-announce": "Объявление вашей вики", "smw-admin-announce-text": "Если ваша вики общедоступная, вы можете зарегистрировать её в <a href=\"https://wikiapiary.com\">WikiApiary</a>.", "smw-admin-deprecation-notice-title": "Уведомления об устаревании", "smw-admin-deprecation-notice-docu": "Следующий раздел содержит устаревшие или удалённые параметры, которые однако всё ещё активны на данном вики-проекте. Предполагается, что при каждом обновлении будет удалена поддержка этих конфигураций.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> устарел и будет удалён в $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> уберёт (или заменит) {{PLURAL:$2|следующую опцию|следующие опции}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> устарел и будет удалён в $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> заменён на <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> был заменен на <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|опция|опции|опций}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> заменён на <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> удалён в $2", "smw-admin-deprecation-notice-title-notice": "Устаревшие настройки", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Устаревшие настройки</b> показывает те настройки, использование которых было обнаружено в этой вики, и которые планируют изъять или изменить в будущем выпуске.", "smw-admin-deprecation-notice-title-replacement": "Заменённые или переименованные настройки", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Замененные или переименованные настройки</b> содержит настройки, которые были переименованы или изменены иным образом, а поэтому рекомендуется немедленно обновить их названия или формат.", "smw-admin-deprecation-notice-title-removal": "Удалённые настройки", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Удаленные настройки</b> идентифицирует те настройки, которые были изъяты в предыдущем выпуске, но было обнаружено, что они всё ещё используются в этой вики.", "smw-admin-deprecation-notice-section-legend": "Легенда", "smw-smwadmin-refresh-title": "Исправление и обновление данных", "smw_smwadmin_datarefresh": "Перестроение данных", "smw_smwadmin_datarefreshdocu": "Можно восстановить все данные Semantic MediaWiki, основанные на текущем содержимом вики.\nЭто может быть полезно для восстановления сломанных данных, обновления данных в случае, если внутренний формат изменился в связи с некоторым обновлением программного обеспечения.\nОбновление будет выполняться страница за страницей и займёт некоторое время.\nНиже показано как идёт процесс обновления, даётся возможность начать или прекратить обновление (если эта функция не была отключена на администратором сайте).", "smw_smwadmin_datarefreshprogress": "<strong>Обновление уже запущено.</strong>\nЭто нормально, что процесс обновления идёт достаточно медленно, поскольку он обновляет данные лишь небольшими порциями, когда пользователь обращается к вики.\nЧтобы закончить данное обновление быстрее, вы можете вызвать скрипт обслуживания MediaWiki <code>runJobs.php</code> (используйте настройку <code>- maxjobs 1000</code>, чтобы ограничить количество обновлений в одной партии).\nОриентировочный ход выполнения текущего обновления:", "smw_smwadmin_datarefreshbutton": "Восстановление данных по расписанию", "smw_smwadmin_datarefreshstop": "Остановить это обновление", "smw_smwadmin_datarefreshstopconfirm": "Да, я уверен{{GENDER:$1||а}}.", "smw-admin-job-scheduler-note": "Активные задачи в этом разделе выполняются в очереди Планировщика заданий, чтобы избежать ситуаций блокировки во время их выполнения. [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Планировщик] отвечает за обработку, поэтому очень важно, чтобы сценарий обслуживания <code>runJobs.php</code> имел достаточно ресурсов (см. также параметр конфигурации <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Ликвидация устаревших сущностей", "smw-admin-outdateddisposal-intro": "Некоторые действия (изменение типа свойства, удаление викистраниц или исправление ошибочных значений) вызовут появление [https://www.semantic-mediawiki.org/wiki/Outdated_entities устаревших сущностей], поэтому рекомендуется периодически убирать их, чтобы освободить пространство в связанной таблице.", "smw-admin-outdateddisposal-active": "Назначена работа по ликвидации устаревших объектов.", "smw-admin-outdateddisposal-button": "Запланировать ликвидацию", "smw-admin-feature-disabled": "Эта функция отключена на этой вики. Пожалуйста, посмотрите справочную страницу о <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">настройках</a>, или обратитесь к системному администратору.", "smw-admin-propertystatistics-title": "Перестроить статистику свойства", "smw-admin-propertystatistics-intro": "Перестраивает всю статистику использования свойств, после чего обновляет и исправляет [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count счётчик использования] свойств.", "smw-admin-propertystatistics-active": "Запланирована работа по перестройке статистики свойства.", "smw-admin-propertystatistics-button": "Запланировать перестройку свойства", "smw-admin-fulltext-title": "Перестройка полнотекстового поиска", "smw-admin-fulltext-intro": "Перестраивает поисковый индекс из таблиц свойств с включённым типом данных [https://www.semantic-mediawiki.org/wiki/Full-text полнотекстового поиска]. Изменения в правила индексации (изменённые шумовые слова, новый стемер и т. п.) и/или заново добавленная или изменённая таблица требуют повторного запуска этого задания.", "smw-admin-fulltext-active": "Была запланирована работа по перепостроению полнотекстового поиска.", "smw-admin-fulltext-button": "Запланировать полнотекстовую перестройку", "smw-admin-support": "Получение поддержки", "smw-admin-supportdocu": "Различные ресурсы, которые помогут вам в случае проблем:", "smw-admin-installfile": "Если вы испытываете затруднения при установке, начните с изучения руководства в <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">файле INSTALL</a> и на <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">странице установки</a>.", "smw-admin-smwhomepage": "Полная документация пользователя Semantic MediaWiki на <b><a href=\"https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_-_%D0%B7%D0%B0%D0%B3%D0%BB%D0%B0%D0%B2%D0%BD%D0%B0%D1%8F_%D1%81%D1%82%D1%80%D0%B0%D0%BD%D0%B8%D1%86%D0%B0\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Об ошибках можно сообщать на <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>, а на <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">соответствующей странице справочного раздела</a> приведены некоторые рекомендации о том, как написать эффективное сообщение о проблеме.", "smw-admin-questions": "Если у вас есть дополнительные вопросы или предложения, присоединяйтесь к обсуждению в <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">списках рассылки пользователей Semantic MediaWiki</a>.", "smw-admin-other-functions": "Другие функции", "smw-admin-statistics-extra": "Статистические функции", "smw-admin-statistics": "Статистика", "smw-admin-supplementary-section-title": "Дополнительные функции", "smw-admin-supplementary-section-subtitle": "Поддерживаемые функции ядра", "smw-admin-supplementary-section-intro": "Эта секция содержит дополнительные функции кроме  обслуживающих. Возможно, что некоторые функции, которые перечислены (см. [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions документацию]), ограничены, а потому недоступны в этой вики.", "smw-admin-supplementary-settings-title": "Конфигурация и настройки", "smw-admin-supplementary-settings-intro": "<u>$1</u> показывает параметры, определяющие поведение Semantic MediaWiki", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Оперативная статистика", "smw-admin-supplementary-operational-statistics-short-title": "операционная статистика", "smw-admin-supplementary-operational-statistics-intro": "Отображ<PERSON><PERSON>т расширенный набор <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Поиск и удаление сущности", "smw-admin-supplementary-idlookup-intro": "Поддерживает простую функцию «<u>$1</u>»", "smw-admin-supplementary-duplookup-title": "Просмотр сущностей-дублика<PERSON>ов", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> для поиска сущностей, которые классифицируются как дубликаты в выбранной матрице таблиц", "smw-admin-supplementary-duplookup-docu": "На этой странице перечислены сущности из выбранных таблиц, которые были категоризированы как [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities дубликаты]. \nДубликаты изредка (если вообще) появляются только из-за прерванного обновления базы данных или неудавшегося отката транзакции.", "smw-admin-supplementary-operational-statistics-cache-title": "Статистика кэша", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> показывает выбранный набор статистики, относящейся к кэшу", "smw-admin-supplementary-operational-table-statistics-title": "Статистика таблиц", "smw-admin-supplementary-operational-table-statistics-short-title": "статистика таблиц", "smw-admin-supplementary-operational-table-statistics-intro": "Генерирует <u>$1</u> для выбранного набора таблиц", "smw-admin-supplementary-elastic-version-info": "Версия", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> показывает подробно настройки и статистику кэша", "smw-admin-supplementary-elastic-docu": "На этой странице находятся данные о настройках, соответствиях, состоянии и статистике индексов, относящаяся к кластеру ''Elasticsearch'', подключённому к ''Semantic MediaWiki'' и его хранилищу [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Поддерживаемые функции", "smw-admin-supplementary-elastic-settings-title": "Настройки (индексы)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> используется ''Elasticsearch'' для управления индексами ''Semantic MediaWiki''", "smw-admin-supplementary-elastic-mappings-title": "Соответствия", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> — список индексов и соответствий полей", "smw-admin-supplementary-elastic-mappings-docu": "На этой странице находятся детали соответствия полей, используемых текущим индексом. Рекомендуется мониторить соответствия учитывая значение переменной <code>index.mapping.total_fields.limit</code>, хранящей максимальное число разрешённых полей в индексе.", "smw-admin-supplementary-elastic-mappings-summary": "Описание", "smw-admin-supplementary-elastic-mappings-fields": "Соответствия полей", "smw-admin-supplementary-elastic-nodes-title": "Узлы", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> показывает статистику узлов", "smw-admin-supplementary-elastic-indices-title": "Индексы", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> предоставляет обзор доступных индексов и их статистику", "smw-admin-supplementary-elastic-statistics-title": "Статистика", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> показывает статистику уровня индекса", "smw-admin-supplementary-elastic-statistics-docu": "Эта страница даёт представление о статистике индексов для различных операций, выполняемых на уровне индекса, возвращённая статистика агрегируется с первичными и суммарными агрегациями. [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html Страница справки] содержит подробное описание статистики доступных индексов.", "smw-admin-supplementary-elastic-status-replication": "Статус репликации", "smw-admin-supplementary-elastic-status-last-active-replication": "Последняя активная репликация: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Интервал обновления: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Задержка работ по восстановлению: $1 (приблизительно)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Журнал невыполненных работ (файл): $1 (приблизительно)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Репликация заблокирована: $1 (перепостроение в процессе)", "smw-admin-supplementary-elastic-replication-header-title": "Статус копирования", "smw-admin-supplementary-elastic-replication-function-title": "Копирование", "smw-admin-supplementary-elastic-replication-files": "Файлы", "smw-admin-supplementary-elastic-replication-pages": "Страницы", "smw-admin-supplementary-elastic-endpoints": "Конечные точки", "smw-admin-supplementary-elastic-config": "Конфигурации", "smw-list-count": "Список содержит $1 {{PLURAL:$1|запись|записи|записей}}.", "smw-property-label-uniqueness": "Метка \"$1\" была сопоставлена по крайней мере с одним другим представлением свойств. Пожалуйста, проконсультируйтесь со [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness страницей помощи] о том, как устранить эту проблему.", "smw-property-label-similarity-title": "Сообщение о подобности названий свойств", "smw-property-label-similarity-intro": "<u>$1</u> вычисляет подобие существующих названий свойств", "smw-property-label-similarity-threshold": "Порог:", "smw-property-label-similarity-type": "Показать ID типа", "smw-property-label-similarity-noresult": "Для выбранных опций не найдено никаких результатов.", "smw-property-label-similarity-docu": "Эта страница сравнивает [https://www.semantic-mediawiki.org/wiki/Property_similarity синтаксическое подобие] (но не семантическое или лексическое подобия) между двумя названиями свойств и составляет отчёт с названиями свойств в случае превышения некоего порога. Отчёт может помочь отфильтровать свойства, имеющие ошибки в названии или являющиеся эквивалентными и представляющие один и тот же концепт (см. служебную страницу [[Special:Properties|Свойства]], чтобы прояснить концепт и использования свойств в отчёте). Порог можно настроить, чтобы или расширить, или сузить уровень подобия, который следует принимать во внимание.  <code>[[Property:$1|$1]]</code> используется для исключения свойств из анализа.", "smw-admin-operational-statistics": "Эта страница содержит операционную статистику, собранную в или из функций, связанных из Semantic MediaWiki. Расширенный список статистических данных, специфических для вики, можно посмотреть [[Special:Statistics|<b>здесь</b>]].", "smw_adminlinks_datastructure": "Структура данных", "smw_adminlinks_displayingdata": "Отображение данных", "smw_adminlinks_inlinequerieshelp": "Справка по встроенным запросам", "smw-page-indicator-usage-count": "Примерное [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count количество использований]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Свойство, определённое {{PLURAL:$1|участником|системой}}", "smw-property-indicator-last-count-update": "Примерное число использований\nПоследний раз обновлено: $1", "smw-concept-indicator-cache-update": "Счётчик кэша\nОбновлён в $1", "smw-createproperty-isproperty": "Это свойство типа $1.", "smw-createproperty-allowedvals": "Для данного свойства {{PLURAL:$1|1=допускается следующее значение|допускаются следующие значения}}:", "smw-paramdesc-category-delim": "Разделитель", "smw-paramdesc-category-template": "Ша<PERSON><PERSON><PERSON><PERSON>, с помощью которого форматируются элементы", "smw-paramdesc-category-userparam": "Параметр для передачи шаблону", "smw-info-par-message": "Сообщение для отображения.", "smw-info-par-icon": "Значок для отображения («информация» или «предупреждение»).", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Общие параметры", "prefs-extended-search-options": "Расширенный поиск", "prefs-ask-options": "Семантический поиск", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] (и связанные с ней расширения) предоставляют возможности индивидуальной настройки группы функций. Список индивидуальных настроек с их описаниями и характеристиками доступен по [https://www.semantic-mediawiki.org/wiki/Help:User_preferences следующей ссылке].", "smw-prefs-ask-options-tooltip-display": "Отображать параметр «текст» в виде всплывающей подсказки на специальной странице #ask [[Special:Ask|формирования запросов]].", "smw-prefs-ask-options-compact-view-basic": "Включить простой сжатый формат", "smw-prefs-help-ask-options-compact-view-basic": "Если включено, показывает сокращённый набор ссылок на Special:Ask в сжатом формате", "smw-prefs-general-options-time-correction": "Включить корректировку времени для служебных страниц при помощи локальной настройки [[Special:Preferences#mw-prefsection-rendering|временного смещения]]", "smw-prefs-general-options-jobqueue-watchlist": "Показывать список наблюдения за очередью задач на личной панели", "smw-prefs-help-general-options-jobqueue-watchlist": "Включите, чтобы показывать [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist список] ожидающих избранных задач, вместе с приблизительными размерами их очередей.", "smw-prefs-general-options-disable-editpage-info": "Отключить вступительный текст на странице редактирования", "smw-prefs-general-options-disable-search-info": "Отключить информацию о поддержке синтаксиса на стандартной странице поиска", "smw-prefs-general-options-suggester-textinput": "Включить ассистента ввода для предложений семантических объектов", "smw-prefs-help-general-options-suggester-textinput": "Включите, чтобы получать [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance автодополнение] названий свойств, концепций и категорий при вводе", "smw-ui-tooltip-title-property": "Свойство", "smw-ui-tooltip-title-quantity": "Преобразование единиц", "smw-ui-tooltip-title-info": "Информация", "smw-ui-tooltip-title-service": "Служебная ссылка", "smw-ui-tooltip-title-warning": "Предупреждение", "smw-ui-tooltip-title-error": "Ошибка", "smw-ui-tooltip-title-parameter": "Параметр", "smw-ui-tooltip-title-event": "Событие", "smw-ui-tooltip-title-note": "Примечание", "smw-ui-tooltip-title-legend": "Легенда", "smw-ui-tooltip-title-reference": "Примечание", "smw_unknowntype": "Для этого свойства задан неизвестный тип «$1»", "smw-concept-cache-text": "Концепция имеет в общей сложности $1 {{PLURAL:$1|страницу|страницы|страниц}} и последний раз обновлялся $2 в $3.", "smw_concept_header": "Страницы, использующие представление «$1»", "smw_conceptarticlecount": "Ниже {{PLURAL:$1|показана|показаны}} $1 {{PLURAL:$1|страница|страницы|страниц}}.", "smw-qp-empty-data": "Запрошенные данные не удаётся отобразить из-за недостаточности некоторых критериев отбора.", "right-smw-admin": "Доступ к администрированию (Semantic MediaWiki)", "right-smw-patternedit": "Доступ к редактированию разрешенных для обслуживания регулярных выражений и шаблонов (Semantic MediaWiki)", "right-smw-pageedit": "Редактирование страниц с аннотацией <code>Is edit protected</code> (Semantic MediaWiki)", "restriction-level-smw-pageedit": "защищено (только подходящие пользователи)", "action-smw-patternedit": "редактирования регулярных выражений, используемых Semantic MediaWiki", "action-smw-pageedit": "редактирование страниц с аннотацией <code>Is edit protected</code> (''Semantic MediaWiki'')", "group-smwadministrator": "Администраторы (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|администратор (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:Администраторы (Semantic MediaWiki)", "group-smwcurator": "Кураторы (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|куратор (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "action-smw-admin": "доступ к административным функциям Semantic MediaWiki", "action-smw-ruleedit": "редактировать страницы правил (Semantic MediaWiki)", "smw-property-predefined-default": "«$1» — это предопределённое свойство типа $2.", "smw-property-predefined-common": "Это предварительно развернутое свойство (также известное как [https://www.semantic-mediawiki.org/wiki/Help:Special_properties специальное свойство]) и несет дополнительные административные привилегии, но может быть использовано, как любое другое [https://www.semantic-mediawiki.org/wiki/Property определенное пользователем свойство].", "smw-property-predefined-ask": "«$1» — это предопределённое свойство, которое представляет мета-данные (в форме [https://www.semantic-mediawiki.org/wiki/Subobject подобъекта]) об индивидуальных запросах и предоставляется [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "«$1» — это предопределённое свойство, которое собирает количество условий, используемых в запросе и предоставляется [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "«$1» — предварительно определенное свойство, которое информирует о глубине запроса и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "Численное значение, полученное на основании вложенности подзапросов, цепочек свойств и доступных элементов описания и используемой для ограничения выполнения запросов посредством настройки <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>", "smw-property-predefined-askpa": "$1 - это предопределенное свойство, описывающее параметры влияющие на результат запроса. Предоставляется от [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "Это часть набора свойств, который определяет [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler query profile].", "smw-sp-properties-docu": "На этой странице перечислены доступные [https://www.semantic-mediawiki.org/wiki/Property свойства] и количество их использований в этой вики. Для получения актуальной статистики рекомендуется, чтоб сервисный скрипт [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics статистики свойст] запускался на регулярной основе. Для дифференцированного представления см. служебные страницы со списком [[Special:UnusedProperties|неиспользованных]] или [[Special:WantedProperties|требуемых свойств]].", "smw-sp-properties-cache-info": "Перечисленные данные были получены из [https://www.semantic-mediawiki.org/wiki/Caching кэша] и были последний раз обновлены $1.", "smw-sp-properties-header-label": "Список свойств", "smw-admin-settings-docu": "Отображает список всех настроек по-умолчанию и локализованных настроек, которые имеют отношение к окружению Semantic MediaWiki. За сведениями об отдельных параметров обратитесь к странице помощи по [https://www.semantic-mediawiki.org/wiki/Help:Configuration конфигурации].", "smw-sp-admin-settings-button": "Создать список настроек", "smw-admin-idlookup-title": "Поиск", "smw-admin-idlookup-docu": "В этом разделе представлены технические сведения об отдельном объекте (вики-страница, подобъект, свойство и т.д.) в Семантической МедиаВики. Ввод может представлять собой числовой идентификатор или строковое значение для совпадения с соответствующим полем для поиска, при этом любая идентификационная ссылка относится к Семантической МедиаВики, а не к странице MediaWiki или номеру версии.", "smw-admin-iddispose-title": "Удаление", "smw-admin-iddispose-docu": "Обратите внимание, что операция утилизации является неограниченной и удалит внутренний идентификатор объекта от движка хранения при подтверждении. Пожалуйста, выполняйте эту задачу с '''осторожностью''' и только после того, как [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentation] были проведены консультации.", "smw-admin-iddispose-done": "Идентификатор «$1» был удалён из хранилища.", "smw-admin-iddispose-references": "ID «$1» {{PLURAL:$2|не имеет активных ссылок|с по крайней мере одной активной ссылкой}}.", "smw-admin-iddispose-references-multiple": "Список совпадений с по крайней мере одной активной ссылкой.", "smw-admin-iddispose-no-references": "Поиск не смог сопоставить «$1» с элементом таблицы.", "smw-admin-idlookup-input": "Поиск:", "smw-admin-objectid": "Идентификатор:", "smw-admin-tab-general": "Обзор", "smw-admin-tab-notices": "Замечания по запрету", "smw-admin-tab-maintenance": "Техническое обслуживание", "smw-admin-tab-supplement": "Дополнительные функции", "smw-admin-tab-registry": "Реестр", "smw-admin-tab-alerts": "Оповещения", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Оптимизация таблицы", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Настройки", "smw-admin-configutation-tab-namespaces": "Пространства имён", "smw-admin-configutation-tab-schematypes": "Типы схем", "smw-admin-maintenance-tab-tasks": "Зада<PERSON>и", "smw-admin-maintenance-tab-scripts": "Скрипты обслуживания", "smw-admin-maintenance-no-description": "Нет описания.", "smw-admin-maintenance-script-section-title": "Список доступных скриптов обслуживания", "smw-admin-maintenance-script-section-update": "Обновить скрипты", "smw-admin-maintenance-script-section-rebuild": "Перестроить скрипты", "smw-livepreview-loading": "Загружается…", "smw-sp-searchbyproperty-description": "На этой странице предоставлен простой [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces интерфейс просмотра] для нахождения сущностей по описанным свойствам и именованным значениям. Другие доступные интерфейсы поиска включают в себя [[Special:PageProperty|страницу поиск свойств]] и [[Special:Ask|построитель поисковых запросов]].", "smw-sp-searchbyproperty-resultlist-header": "Список результатов", "smw-sp-searchbyproperty-nonvaluequery": "Список значений, которые имеют назначенное свойство «$1».", "smw-sp-searchbyproperty-valuequery": "Список страниц, которые содержат свойство «$1» с объявленным значением «$2».", "smw-datavalue-number-textnotallowed": "«$1» не может быть присвоен заявленному типу число со значением $2.", "smw-datavalue-number-nullnotallowed": "«$1» вернулся с со значением «NULL», что не разрешено для чисел.", "smw-editpage-annotation-enabled": "Эта страница поддерживает семантические аннотации в тексте (например <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) для построения структурированного контента, в который можно делать запросы, обеспечивается Semantic MediaWiki. Для комплексного описания, как использовать аннотации или парсерную функцию ask, пожалуйста, посетите справочные страницы о [https://www.semantic-mediawiki.org/wiki/Help:Getting_started начале работы], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation in-text annotation аннотации в тексте] и [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries строчных запросах].", "smw-editpage-annotation-disabled": "На этой странице невозможны семантические аннотации в тексте из-за ограничений пространства имён. Детали относительно того, как разрешить пространство имён, находятся в справочной странице [https://www.semantic-mediawiki.org/wiki/Help:Configuration конфигурации].", "smw-editpage-property-annotation-enabled": "Это свойство можно расширить с помощью семантических аннотаций для указания типа данных (например <nowiki>\"[[Has type::Page]]\"</nowiki>) или других поддерживаемых утверждений (например, <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). Описание, как расширить эту страницу смотрите на странице справки о [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration заявлении свойства] или [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes список доступных типов данных].", "smw-editpage-property-annotation-disabled": "Это свойство нельзя расширить аннотацией типа данных (например <nowiki>\"[[Has type::Page]]\"</nowiki>), поскольку она уже предварительно определена (см. более подробную информацию на справочной странице о [https://www.semantic-mediawiki.org/wiki/Help:Special_properties специальные свойства]).", "smw-editpage-concept-annotation-enabled": "Этот концепт можно расширить с использованием парсерной функции #concept. Описание, как использовать #concept, см. на справочной странице о [https://www.semantic-mediawiki.org/wiki/Help:Concepts концептах].", "smw-search-syntax-support": "Поле поиска поддерживает [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search синтаксис семантических запросов] ''MediaWiki''", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Помощник по вводу] включён, чтобы упростить выбор всех возможных опций и категорий.", "smw-search-help-intro": "Ввод <code><nowiki>[[ ... ]]</nowiki></code> укажет процессору использовать поисковый механизм ''Semantic MediaWiki''. Обратите внимание, что сочетание <code><nowiki>[[ ... ]]</nowiki></code> с простым текстовым поиском, наподобие <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code>, не поддерживается.", "smw-search-help-structured": "Структурированный поиск:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> — [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context контекст отбора]\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> — с [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context контекстом запроса]", "smw-search-help-ask": "По этим ссылкам находится информация о том, как использовать функцию парсера <code>#ask</code>:\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Выбор страниц] — как выбирать страницы и составлять условия отбора\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Поисковые операторы] — поддерживаемые поисковые операторы, включая отбор по диапазонам и метасимволам", "smw-search-input": "Ввод и поиск", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Автодополение] в поле ввода требует один из следующих префиксов:\n\n*<code>p:</code> для свойств (например, <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> для категорий\n\n*<code>con:</code> для концепций", "smw-search-syntax": "Син<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile": "Дополнительно", "smw-search-profile-tooltip": "Функции поиска, относящиеся к Semantic MediaWiki", "smw-search-profile-sort-best": "Лучшее совпадение", "smw-search-profile-sort-recent": "Самые недавние", "smw-search-profile-sort-title": "Название", "smw-search-profile-extended-help-intro": "[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile Расширенный профиль страницы] ''Служебная:Поиск'' предоставляет доступ к функциям поиска, особым для Semantic MediaWiki и поддерживаемой в ней серверной части запроса.", "smw-search-profile-extended-help-sort": "Порядок сортировки результатов запроса:", "smw-search-profile-extended-help-sort-title": "*«Название» с использованием заголовка страницы (или отображаемого заголовка) в качестве критерия сортировки", "smw-search-profile-extended-help-sort-recent": "* \"Совсем недавние\" для показа страниц в порядке последнего изменения (от недавних к давним), при этом подобъекты не будут выведены, поскольку для них не определено свойство [[Property:Modification date|Дата изменения]]", "smw-search-profile-extended-help-search-syntax": "Поле поиска поддерживает синстаксис функции парсера <code>#ask</code> для семантического поиска. Полезные выражения включают:", "smw-search-profile-extended-help-query-link": "Детальнее см. $1", "smw-search-profile-extended-help-find-forms": "Доступные формы", "smw-search-profile-extended-section-sort": "Сортировать по", "smw-search-profile-extended-section-form": "Формы", "smw-search-profile-extended-section-search-syntax": "Поиск входа", "smw-search-profile-extended-section-namespace": "Пространство имён", "smw-search-profile-extended-section-query": "Запрос", "smw-search-profile-link-caption-query": "конструктор запросов", "smw-search-show": "Показать", "smw-search-hide": "Скрыть", "log-name-smw": "<PERSON><PERSON><PERSON><PERSON><PERSON>tic MediaWiki", "log-show-hide-smw": "$1 журнал Semantic MediaWiki", "logeventslist-smw-log": "<PERSON><PERSON><PERSON><PERSON><PERSON>tic MediaWiki", "log-description-smw": "Деятельность относительно [https://www.semantic-mediawiki.org/wiki/Help:Logging включенных типов событий], которую регистрирует Semantic MediaWiki и её компоненты.", "smw-datavalue-import-missing-namespace-uri": "Не удалось найти URI пространства имён «$1» в [[MediaWiki:Smw import $1|импорте $1]].", "smw-datavalue-import-missing-type": "Не было найдено определение типа для «$1» в [[MediaWiki:Smw import $2|импорте $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Импорт $1]]", "smw-datavalue-import-invalid-value": "«$1» не является действительным форматом и должен состоять из «пространство имён»:«идентификатор» (например «foaf:name»).", "smw-property-predefined-impo": "\"$1\" — это предопределённое свойство, которое описывает отношение к [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary заимствованной лексике] и предоставляется [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "«$1» — предварительно определенное свойство, которое описывает [[Special:Types|тип данных]] свойства, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "«$1» — предопределённое свойство, которое представляет построение [https://www.semantic-mediawiki.org/wiki/Help:Container контейнера], и обеспечиваемое [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "Это хранилище позволяет сохранять значения свойств, как на обычной вики-странице, но в особом пространстве, привязанном к содержащей его странице", "smw-property-predefined-errp": "«$1» — это предварительно определённое свойство для отслеживания неверных значений в аннотациях и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "Обычно вызывается несоответствием типа данных или несоблюдением ограничения [[Property:Allows value|допустимых значений]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value «$1»] — \nпредварительно определенное свойство, которое может определять список разрешенных значений, чтобы ограничивать назначение значений для свойства, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list «$1»] — предопределённое свойство, для хранения ссылки на список допустимых значений свойства. Предоставляется ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]''.", "smw-datavalue-property-restricted-annotation-use": "Свойство «$1» имеет особое назначение, и его значение не может устанавливаться в семантической аннотации.", "smw-datavalue-property-restricted-declarative-use": "Свойство «$1» — декларативное, и должно использоваться только на страницах свойств или категорий.", "smw-datavalue-property-create-restriction": "Свойство «$1» ещё не существует, а у пользователя нет разрешения «$2» (см. [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Режим разграничения доступа]), чтобы создавать новые свойства.", "smw-datavalue-property-invalid-character": "«$1» содержит запрещённый символ «$2» и, следовательно, отмечено, как недопустимое.", "smw-datavalue-property-invalid-chain": "Использование цепочки свойств «$1» недопустимо в семантической аннотации.", "smw-datavalue-restricted-use": "Значение данных «$1» было отмечено для ограниченного использования.", "smw-datavalue-invalid-number": "«$1» не может быть интерпретировано как число.", "smw-query-condition-circular": "Возможное циклическое условие было обнаружено в «$1».", "smw-query-condition-empty": "В запросе есть пустое условие.", "smw-types-list": "Список типов данных", "smw-types-default": "«$1» является встроенным типом данных.", "smw-types-help": "Дальнейшую информацию и примеры можно найти на [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 странице справки].", "smw-type-anu": "«$1» — это вариант типа данных [[Special:Types/URL|URL]] и в основном используется для экспортной декларации \"owl:AnnotationProperty\".", "smw-type-boo": "«$1» — это примитивный тип данных для описания значения истина/ложь.", "smw-type-cod": "«$1» — это вариант типа данных [[Special:Types/Text|Текст]] для использования в технических текстах произвольной длины, таких как списки исходного кода.", "smw-type-geo": "«$1» — это тип данных, который описывает географическое положение и требует установить [https://www.semantic-mediawiki.org/wiki/Extension:Maps расширение «Maps»] для расширенного функционала.", "smw-type-tel": "«$1» — это специальный тип данных для описания международных телефонных номеров согласно RFC 3966.", "smw-type-txt": "«$1» — это примитивный тип данных для описания строк произвольной длины.", "smw-type-dat": "«$1» — это тип данных для представления моментов времени в едином формате.", "smw-type-ema": "«$1» — тип данных для хранения адреса электронной почты.", "smw-type-tem": "«$1» — особый численный тип данных для хранения температуры.", "smw-type-qty": "«$1» — численный тип данных с единицей измерения.", "smw-type-rec": "«$1» — тип данных для хранения упрядоченного списка типизированных свойств.", "smw-type-extra-tem": "Процедура перевода поддерживает температурные шкалы Кельвина, Цельсия, Фаренгейта и Ранкина.", "smw-type-tab-properties": "Свойства", "smw-type-tab-types": "Типы", "smw-type-tab-errors": "Ошибки", "smw-type-primitive": "Скалярные", "smw-type-contextual": "Зависимые", "smw-type-compound": "Составные", "smw-type-container": "Кон<PERSON>ейнер", "smw-special-pageproperty-description": "На этой странице представлен интерфейс просмотра для поиска всех значений свойства и заданной страницы. Другие доступные интерфейсы поиска включают в себя [[Special:SearchByProperty|поиск свойств]] и [[Special:Ask|построитель поисковых запросов]].", "smw-property-predefined-errc": "«$1» — это предварительно определённое свойство, обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] и представляет ошибки, которые возникли в связи с неправильными аннотациями значений или обработкой ввода.", "smw-property-predefined-long-errc": "Ошибки накапливаются в [https://www.semantic-mediawiki.org/wiki/Help:Container контейнере], который также может включать ссылки на свойство, которое и повлекло несоответствие.", "smw-property-predefined-errt": "«$1» — это предварительно определенное свойство, содержит текстовое описание ошибки и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Определенный пользователем подобъект содержал недействительную схему наименования. Точка ($1) в первых пяти символах предназначена для использования исключительно расширениями. Вы можете установить [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier именованный идентификатор].", "smw-datavalue-record-invalid-property-declaration": "Запись содержит свойство «$1», которая сама по себе объявлена типом записи, и это не допускается.", "smw-property-predefined-mdat": "«$1» — это предварительно определенное свойство, что соответствует дате последнего изменения объекта, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "«$1» — это предварительно определенное свойство, что соответствует дате первой версии субъекта, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "«$1» — это предварительно определенное свойство, которое указывает, является ли субъект новым или нет, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "«$1» — это предварительно определенное свойство содержит название страницы того пользователя, который создал первую версию, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "«$1» — это предварительно определенное свойство, которое описывает MIME-тип загруженного файла и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "«$1» — это предварительно определенное свойство, которое описывает тип носителя, загруженного медиафайла, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "«$1» — это предварительно определенное свойство содержит имя конечного формата, использованного в запросе, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "«$1» — это предварительно определенное свойство, описывающее условия запроса в виде строки, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "«$1» — это предварительно определенное свойство, содержащее значение времени (в секундах), которое требуется для завершения выполнения запроса, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "«$1» — предопределённое свойство, предоставленное ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' для хранения альтернативных (т.е. внешник) источников запросов.", "smw-property-predefined-askco": "«$1» — предопределённое свойство, предоставленное ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' для хранения состояния запроса или его частей.", "smw-property-predefined-long-askco": "Число или числа, представляющие код состояния запроса (см. [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler Профилировщик запросов]).", "smw-property-predefined-prec": "«$1» — это предварительно определенное свойство, описывающее [https://www.semantic-mediawiki.org/wiki/Help:Display_precision точность отображения] (в десятичных цифрах) для числовых типов данных.", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps Расширение «Maps»] не было обнаружено, поэтому свойство «$1» ограничено в своей работе.", "smw-datavalue-monolingual-dataitem-missing": "Отсутствует ожидаемый элемент для построения одноязычного значение.", "smw-datavalue-languagecode-missing": "Для аннотации «$1» парсер не смог определить кода языка (например «foo@en»).", "smw-datavalue-languagecode-invalid": "«$1» не был распознан как поддерживаемый код языка.", "smw-property-predefined-lcode": "«$1» — это предварительно определенное свойство, представляющее BCP47-форматированный код языка, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "«$1» — это [https://www.semantic-mediawiki.org/wiki/Help:Container контейнерный] тип данных, который связывает текстовое значение с конкретным [[Property:Language code|кодом языка]].", "smw-types-extra-mlt-lcode": "Этот тип данных {{PLURAL:$2|требует|не требует}} кода языка (то есть {{PLURAL:$2|аннотация значения без кода языка не принимается|аннотация значения без кода языка принимается}}).", "smw-property-predefined-text": "«$1» — это предварительно определенное свойство, представляющее текст вспомогательной длины, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "«$1» — это предварительно определенное свойство, которое дает возможность описать свойство в контексте языка, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "«$1» — это предварительно определенное свойство для определения списка свойств, использованных со свойством типа [[Special:Types/Record|record]], и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Время разбора внутритекстовой аннотации", "smw-limitreport-intext-postproctime": "[SMW] время пост-обработки", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|секунда|секунды|секунд}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|секунда|секунды|секунд}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Время обновления store-базы данных (при обновлении страницы)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|секунда|секунды|секунд}}", "smw_allows_pattern": "Чтобы доступ к странице был открыт с помощью свойства «[[Property:Allows pattern|Allows pattern]]», эта страница должна содержать список ссылок (после которого идут [https://ru.wikipedia.org/wiki/Регулярные_выражения регулярные выражения]). Для редактирования этой страницы необходимы права <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "Регулярное выражение «$2» классифицировало значение «$1» как недопустимое.", "smw-datavalue-allows-pattern-reference-unknown": "Ссылки на шаблон «$1» не удалось сравнить с записью на [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "Строка «$1» не найдена в списке [[MediaWiki:Smw allows list $1]] .", "smw-datavalue-allows-value-list-missing-marker": "Список «$1» не содержит строк с маркёром списка <code>*</code>.", "smw-datavalue-feature-not-supported": "Функция «$1» не поддерживается или была выключена в этой вики.", "smw-property-predefined-pvap": "«$1» — это предварительно определенное свойство, которой можно указать [[MediaWiki:Smw allows pattern|ссылка на шаблон]], чтобы применить сопоставление [https://en.wikipedia.org/wiki/Regular_expression регулярных выражений], и которая обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "«$1» — это предварительно определенное свойство, которой для сущности можно назначить отдельное название для отображения, и которое обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "«$1» — это предварительно определенное свойство, которое обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] для ограничения присвоения значений для каждого экземпляра уникальным значением (или максимум одним).", "smw-property-predefined-long-pvuc": "Уникальность устанавливается, когда два значения являются неодинаковыми в своей символьной репрезентации, и любое нарушение этого ограничения будет квалифицировано как ошибка.", "smw-datavalue-constraint-uniqueness-violation": "Свойство «$1» позволяет только уникальные присвоенные значения, а «$2» уже было аннотировано в теме «$3».", "smw-datavalue-constraint-uniqueness-violation-isknown": "Свойство «$1» предусматривает только уникальные присвоения. На странице «$2» уже есть присвоенное значение. «$3» нарушает требование уникальности.", "smw-property-predefined-boo": "«$1» — [[Special:Types/Boolean|тип данных]] и предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' для хранения булевых значений.", "smw-property-predefined-num": "«$1» — [[Special:Types/Number|тип данных]] и предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' для хранения численных данных.", "smw-property-predefined-dat": "«$1» — [[Special:Types/Date|тип данных]] и предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'', для хранения дат.", "smw-property-predefined-uri": "«$1» — [[Special:Types/Date|тип данных]] и предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'', для хранения URI и URL.", "smw-property-predefined-qty": "«$1» — [[Special:Types/Date|тип данных]] и предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'', для хранения количественных данных с единицами измерения.", "smw-datavalue-time-invalid-offset-zone-usage": "Сдвиг и часовой пояс в «$1» не распознан.", "smw-datavalue-time-invalid-values": "Значение «$1» содержит не распознанную информацию «$2».", "smw-datavalue-time-invalid-date-components-common": "«$1» содержит некоторую не поддающуюся интерпретации информации.", "smw-datavalue-time-invalid-date-components-dash": "«$1» содержит посторонний дефис или другой символ, не допустимый в дате.", "smw-datavalue-time-invalid-date-components-empty": "«$1» содержит некоторые пустые компоненты.", "smw-datavalue-time-invalid-date-components-three": "«$1» содержит более трёх составляющих, требуемых для распознания даты.", "smw-datavalue-time-invalid-date-components-sequence": "«$1» содержит последовательность символов, не поддающуюся интерпретации в соответствии с имеющейся матрицей компонентов дат.", "smw-datavalue-time-invalid-ampm": "«$1» содержит подстроку «$2», которая не может содержаться во времени в двенадцатичасовом формате.", "smw-datavalue-time-invalid-jd": "«$1» не поддаётся разбору в качестве корректного юлианского дня (результат разбора — «$2»)", "smw-datavalue-external-formatter-uri-missing-placeholder": "В маске URI нет символа подстановки «$1».", "smw-datavalue-external-formatter-invalid-uri": "«$1» — недопустимый URL-адрес.", "smw-datavalue-external-identifier-formatter-missing": "Не назначено свойство «[[Property:External formatter uri|External formatter URI]]».", "smw-datavalue-keyword-maximum-length": "Ключевое слово превысило максимальную длину $1 {{PLURAL:$1|символ|символа|символов}}.", "smw-property-predefined-eid": "«$1» — [[Special:Types/External identifier|тип данных]] и предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'', для хранения внешних идентификаторов.", "smw-property-predefined-peid": "«$1» — предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'', для хранения внешнего идентификатора.", "smw-property-predefined-pefu": "«$1» — предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'', для хранения адреса внешнего ресурса с символом подстановки.", "smw-property-predefined-long-pefu": "В URI должен содержаться символ подстановки, который будет заменён на [[Special:Types/External identifier|внешний идентификатор]] для получения правильной ссылки на ресурс", "smw-type-eid": "«$1» — это разновидность типа данных «[[Special:Types/Text|Текст]]» для описания внешних (URI based) ресурсов. Для этого типа данных следует назначить свойство, объявляющее «[[Property:External formatter uri|External formatter URI]]».", "smw-datavalue-parse-error": "Переданное значение «$1» не было понято.", "smw-datavalue-propertylist-invalid-property-key": "Список свойств «$1» содержит недопустимый ключ свойства «$2».", "smw-datavalue-type-invalid-typeuri": "Тип «$1» не может быть преобразован в корректный URI.", "smw-datavalue-wikipage-invalid-title": "Значение «$1» типа «Страница» содержит недопустимые символы или неполно и может привести к неожиданным результатам при семантическом аннотировании или запросе.", "smw-datavalue-wikipage-property-invalid-title": "Свойство «$1» типа «Страница» со значением «$2» содержит недопустимые символы или неполно и может привести к неожиданным результатам при семантическом аннотировании или запросе.", "smw-datavalue-reference-invalid-fields-definition": "Тип «[[Special:Types/Reference|Ссылка]]» требует ввода списка свойств, объявленных с помощью свойства «[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Has fields]».", "smw-parser-invalid-json-format": "JSON-парсер вернул ошибку «$1».", "smw-property-preferred-label-language-combination-exists": "«$1» нельзя использовать в качестве предпочтительной метки, поскольку язык «$2» уже назначен метке «$3».", "smw-clipboard-copy-link": "Скопировать ссылку в буфер обмена", "smw-property-userdefined-fixedtable": "Свойство «$1» сконфигурир<PERSON>а<PERSON><PERSON> как as [https://www.semantic-mediawiki.org/wiki/Fixed_properties фиксированное], и любые изменения его [https://www.semantic-mediawiki.org/wiki/Type_declaration типа] требуют или запустить <code>setupStore.php</code>, или выполнить задачу «Установка и обновление базы данных» на служебной странице ''[[Special:SemanticMediaWiki|SemanticMediaWiki]]''.", "smw-data-lookup": "Извлечение данных…", "smw-data-lookup-with-wait": "Запрос обрабатывается и может занять некоторое время.", "smw-no-data-available": "Нет доступных данных.", "smw-property-req-violation-missing-fields": "Для свойства «$1» типа «$2» не задана обязательная декларация [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>].", "smw-property-req-violation-missing-formatter-uri": "Для свойства «$1» не задано обязательное свойство <code>External formatter URI</code>.", "smw-property-req-violation-predefined-type": "Для предопределённого свойства «$1» задан тип «$2», с которым несовместимо значение свойства по умолчанию.", "smw-property-req-violation-import-type": "Обнаружено объявление типа, которое несовместимо с предопределенным типом импортированного словаря «$1». Как правило, нет необходимости объявлять тип, потому что информация извлекается из определения импорта.", "smw-property-req-violation-change-propagation-locked-error": "Свойство «$1» изменено, и связанные с ним страницы должны быть обновлены путём [https://www.semantic-mediawiki.org/wiki/Change_propagation распространения изменений]. Страница свойства закрыта от изменений пока первичное обновление не будет завершено, чтобы избежать прерывания обновления или противоречивых настроек свойства. Это может занять некоторое время, зависящее от размера [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue очереди задач] и частоты её обработки, по истечении которого страница будет открыта для изменений.", "smw-property-req-violation-change-propagation-locked-warning": "Свойство «$1» изменено, и связанные страницы должны быть обновлены путём [https://www.semantic-mediawiki.org/wiki/Change_propagation распространения изменений]. Обновление может занять некоторое время, зависящее от размера [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue очереди заданий]. Рекомендуется отложить редактирование свойства, чтобы избежать прерывания обновления или противоречивых настроек.", "smw-property-req-violation-change-propagation-pending": "Ожидается [https://www.semantic-mediawiki.org/wiki/Change_propagation Распространение изменений] (около $1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|задание|заданий|задания}}]). Рекомендуется отложить редактирование страницы свойства, пока процесс не будет завершён, чтобы избезать прерываний обновления или противоречивых настроек свойства.", "smw-property-req-violation-missing-maps-extension": "Не удалось обнаружить расширение [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"], необходимое для этого типа данных. Как следствие, невозможно хранить или обрабатывать географические данные.", "smw-property-req-violation-type": "Для этого свойства указаны противоречивые типы данных, что может привести к установке некорректных значений для него. Следует выбрать один подходящий тип.", "smw-change-propagation-protection": "Эта страница закрыта для изменений, чтобы предотвратить случайное изменение, пока идёт [https://www.semantic-mediawiki.org/wiki/Change_propagation распространение изменений]. Это может занять некоторое время, зависящее от размера и частоты обработки [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue очереди заданий], прежде чем страница будет открыта для изменений.", "smw-category-change-propagation-locked-error": "Категория «$1» изменена, и связанные страницы должны быть обновлены путём [https://www.semantic-mediawiki.org/wiki/Change_propagation распространения изменений]. А пока, страница категорий закрыта для изменений, пока обновление не будет завершено, чтобы избежать прерывания обновления или противоречий в настройках. Это может занять некоторое время, зависящее от размера [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue очереди заданий], прежде чем страница будет открыта для изменений.", "smw-category-change-propagation-locked-warning": "Категория «$1» изменена, и связанные страницы должны быть обновлены путём [https://www.semantic-mediawiki.org/wiki/Change_propagation распространения изменений]. Это может занять некоторое время, зависящее от размера [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue очереди заданий], прежде чем страница будет открыта для изменений. Предлагается отложить редактирование страницы, пока обновление не будет завершено, чтобы избежать прерывания обновления или противоречий в настройках.", "smw-category-change-propagation-pending": "Ожидается обновление в результате [https://www.semantic-mediawiki.org/wiki/Change_propagation распространения обновлений] (около $1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|задание|заданий|задания}}]). Рекомендуется отложить редактирование категории, пока обновление не будет завершено, чтобы избежать прерывания обновления или противоречивых настроек.", "smw-category-invalid-value-assignment": "«$1» — не является действительной семантической аннотацией или заданием категории.", "protect-level-smw-pageedit": "Разрешить только участникам, имеющим право на редактирование страниц (Semantic MediaWiki)", "smw-create-protection": "Пока действует [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode режим ограниченного доступа], создание свойства «$1» разрешено только пользователям с правами «$2» (или из [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups группы]).", "smw-create-protection-exists": "Пока действует [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode режим ограниченного доступа], изменение свойства «$1» разрешено только пользователям с правами «$2» (или из такой [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups группы]).", "smw-edit-protection": "Эта страница [[Property:Is edit protected|защищена от изменений]], чтобы предотвратить нежелательное изменение данных, и может редактироваться только пользователями с правами «$1» или из такой [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups группы].", "smw-edit-protection-disabled": "Режим защиты от редактирования выключен, следовательно свойство «$1» не может быть использовано для защиты страницы от редактирования.", "smw-edit-protection-auto-update": "Уровень защиты страницы изменён на основании значения свойства «Is edit protected».", "smw-edit-protection-enabled": "Защищено от редактирования (Semantic MediaWiki)", "smw-patternedit-protection": "Эта страница защищена и может редактироваться только пользователями с правами <code>smw-patternedit</code> (см. ''[https://www.semantic-mediawiki.org/wiki/Help:Permissions разрешения]'').", "smw-property-predefined-edip": "«$1» — предопределённое свойство, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'', хранящее признак защиты от редактирования.", "smw-property-predefined-long-edip": "Хотя любой участник может установить это свойство на страницу, только участники с особыми полномочиями смогут изменить или удалить защиту после того, как она будет установлена.", "smw-query-reference-link-label": "Ссылка на запрос", "smw-format-datatable-emptytable": "Данные отсутствуют в таблице", "smw-format-datatable-info": "Отображение записей от _START_ до _END_ из _TOTAL_", "smw-format-datatable-infoempty": "Показаны от 0 до 0 из 0 записей", "smw-format-datatable-infofiltered": "(отфильтровано из _MAX_ записей)", "smw-format-datatable-infothousands": "&nbsp;", "smw-format-datatable-lengthmenu": "Показать _MENU_ записей", "smw-format-datatable-loadingrecords": "Загружается…", "smw-format-datatable-processing": "Обрабатывается…", "smw-format-datatable-search": "Поиск:", "smw-format-datatable-zerorecords": "Соответствующие записи не найдены", "smw-format-datatable-first": "Первая", "smw-format-datatable-last": "Последняя", "smw-format-datatable-next": "Следующая", "smw-format-datatable-previous": "Предыдущая", "smw-format-datatable-sortascending": ":активировать для сортировки столбца по возрастанию", "smw-format-datatable-sortdescending": ":активировать для сортировки столбца по убыванию", "smw-format-datatable-toolbar-export": "Экспорт", "smw-category-invalid-redirect-target": "Категория «$1» содержит некорректное перенаправление в иное пространство имён.", "apihelp-smwinfo-summary": "Модуль API для получения информации о статистике Semantic MediaWiki и другой метаинформации.", "apihelp-ask-summary": "Модуль API для запроса Semantic MediaWiki с использованием языка запросов.", "apihelp-askargs-summary": "Модуль API для запроса Semantic MediaWiki с использованием языка запросов в качестве списка условий, распечаток и параметров.", "apihelp-browsebyproperty-summary": "Модуль API для получения информации о свойстве или списке свойств.", "apihelp-browsebysubject-summary": "Модуль API для получения информации о предмете.", "apihelp-smwtask-summary": "Модуль API для выполнения задач, связанных с Semantic MediaWiki (только для внутреннего, не для публичного использования).", "apihelp-smwbrowse-summary": "Модуль API для поддержки действий просмотра для различных типов объектов в Semantic MediaWiki.", "smw-api-invalid-parameters": "Недопустимые параметры, \"$1\"", "smw-property-page-list-count": "$1 {{PLURAL:$1|страница|страниц|страницы}}, использующих это свойство.", "smw-property-page-list-search-count": "$1 {{PLURAL:$1|страница|страниц|страницы}}, на которых для свойства установлено значение «$2».", "smw-property-reserved-category": "Категория", "smw-category": "Категория", "smw-datavalue-uri-invalid-scheme": "«$1» не указан, как допустимый протокол URI", "smw-browse-property-group-title": "Группа свойств", "smw-browse-property-group-label": "Метка группы свойств", "smw-browse-property-group-description": "Описание группы свойств", "smw-property-predefined-ppgr": "«$1» — предопределённое свойство, отмечающее сущности (в основном, категории), используемые для группировки свойств, предоставляемое ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]''.", "smw-filter": "Фильтр", "smw-section-expand": "Раскрыть раздел", "smw-section-collapse": "Свернуть раздел", "smw-ask-format-help-link": "Формат [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "Справка", "smw-cheat-sheet": "Шпаргалка", "smw-personal-jobqueue-watchlist": "Список наблюдения очереди заданий", "smw-property-predefined-label-skey": "Ключ сортировки", "smw-processing": "Обработка…", "smw-loading": "Загрузка…", "smw-fetching": "Получение...", "smw-preparing": "Подготовка...", "smw-expand": "Развернуть", "smw-collapse": "Свернуть", "smw-copy": "Копировать", "smw-copy-clipboard-title": "Копирование содержимого в буфер обмена", "smw-jsonview-expand-title": "Развернуть JSON-просмотр", "smw-jsonview-collapse-title": "Свернуть JSON-просмотр", "smw-jsonview-search-label": "Поиск:", "smw-redirect-target-unresolvable": "Цель неразрешима по причине: «$1»", "smw-types-title": "Тип: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Изменение контентной модели [https://www.semantic-mediawiki.org/wiki/Help:Schema страницы схемы] не разрешено.", "smw-schema-namespace-edit-protection": "Эта страница защищена, править её могут только участники с [https://www.semantic-mediawiki.org/wiki/Help:Permissions правом] <code>smw-schemaedit</code>.", "smw-schema-namespace-edit-protection-by-import-performer": "Эта страница была импортирована перечисленным [https://www.semantic-mediawiki.org/wiki/Import_performer импортером] и означает, что изменение содержимого этой страницы разрешено только указанным пользователям.", "smw-schema-error-title": "{{PLURAL:$1|1=Ошибка|Ош<PERSON>б<PERSON>и}} валидации", "smw-schema-error-schema": "Схема валидации '''$1''' обнаружила следующие несоответствия:", "smw-schema-error-miscellaneous": "Разносторонняя ошибка ($1)", "smw-schema-error-validation-json-validator-inaccessible": "Валидато<PERSON> \"<b>$1</b>\" недоступен (или не установлен) и является причиной того, что файл \"$2\" не может быть проверен, это препятствует сохранению или изменению текущей страницы.", "smw-schema-error-violation": "[«$1», «$2»]", "smw-schema-error-json": "Ошибка JSON: \"$1\"", "smw-schema-validation-schema-title": "JSON-схема", "smw-schema-summary-title": "Описание", "smw-schema-title": "Схема", "smw-schema-usage": "Использование", "smw-schema-type": "Тип схемы", "smw-schema-type-description": "Описание типа", "smw-schema-description": "Описание схемы", "smw-schema-description-link-format-schema": "Этот тип схемы поддерживает определение характеристик для создания контекстно-зависимых ссылок в сочетании с назначенным свойством [[Property:Formatter schema|схемы форматера]].", "smw-schema-tag": "{{PLURAL:$1|Тег|Тега|Тегов}}", "smw-property-predefined-schema-desc": "«$1» — это предварительно определенное свойство, содержащее описание схемы, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-def": "«$1» — это предварительно определенное свойство, которое описывает содержимое схемы, и обеспечивается [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-ask-title-keyword-type": "Поиск по ключевым словам", "smw-remote-source-unavailable": "Не получилось подключиться к удалённой цели «$1».", "smw-remote-source-disabled": "Источник '''$1''' отключил поддержку удалённого запроса!", "smw-parameter-missing": "Параметр «$1» отсутствует.", "smw-property-tab-usage": "Использование", "smw-property-tab-profile-schema": "Схема профиля", "smw-property-tab-redirects": "Синонимы", "smw-property-tab-subproperties": "Подсвойства", "smw-property-tab-specification": "… ещё", "smw-concept-tab-list": "Список", "smw-concept-tab-errors": "Ошибки", "smw-ask-tab-result": "Результат", "smw-ask-tab-extra": "Дополнительно", "smw-ask-tab-debug": "Отладка", "smw-ask-tab-code": "<PERSON>од", "smw-install-incomplete-intro": "Имеется $2 неполных или [[Special:PendingTaskList|в ожидании]] {{PLURAL:$2|задача|задач}} для завершения {{PLURAL:$1|установки|обновления}} [https://www.semantic-mediawiki.org Semantic MediaWiki]. Администратор или пользователь с достаточными правами может завершить {{PLURAL:$2|данную задачу|данные задачи}}. Это следует сделать перед добавлением новых данных, чтобы избежать несоответствий.", "smw-pendingtasks-tab-setup": "Настройка", "smw-pendingtasks-setup-tasks": "Зада<PERSON>и", "smw-report": "Отчет", "smw-legend": "Легенда", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "Ограничение", "smw-entity-examiner-deferred-fake": "Не настоящие", "smw-indicator-revision-mismatch": "Редакция", "smw-indicator-revision-mismatch-error": "Проверка [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner связанной ревизии] обнаружила несоответствие между ревизией, на которую ссылается MediaWiki, и ревизией, ассоциированной в Semantic MediaWiki для этой сущности.", "smw-facetedsearch-profile-label-default": "Профиль по умолчанию", "smw-facetedsearch-intro-tab-search": "Поиск", "smw-facetedsearch-format-table": "Таблица", "smw-facetedsearch-input-filter-placeholder": "Фильтр...", "smw-facetedsearch-no-filters": "Нет фильтров.", "smw-facetedsearch-no-filter-range": "Нет диапазона фильтра.", "smw-search-placeholder": "Поиск…", "smw-listingcontinuesabbrev": "(продолжение)", "smw-showingresults": "Ниже {{PLURAL:$1|1=показан <strong>1</strong> результат|показан  <strong>$1</strong> результат|показано <strong>$1</strong> результата|показаны <strong>$1</strong> результатов}}, начиная с № <strong>$2</strong>."}