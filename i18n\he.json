{"@metadata": {"authors": ["Amire80", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ghsuvr", "Guycn2", "Inkbug", "<PERSON><PERSON><PERSON><PERSON>", "Nemo bis", "Or", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Steeve815", "Strayblues", "Tuvalevsku", "<PERSON><PERSON> אודי אורון", "YaronSh", "המ<PERSON><PERSON><PERSON><PERSON>", "חיים", "נדב ס", "ערן", "שני<PERSON><PERSON><PERSON><PERSON>ן", "아라"]}, "smw-desc": "הנגשת הוויקי שלך – למכונות וגם לבני־אדם ([https://www.semantic-mediawiki.org/wiki/Help:User_manual תיעוד מקוון])", "smw-error": "שגיאה", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ מדיה־ויקי סמנטית] הותקנה והופעל, אבל חסר לה [https://www.semantic-mediawiki.org/wiki/Help:Upgrade מפתח שדרוג].", "smw-upgrade-release": "מהדורה", "smw-upgrade-progress": "התקדמות", "smw-upgrade-progress-explain": "הערכה לגבי מתי השדרוג עומד להסתיים קשה לחיזוי כי היא תלויה בגודל מאגר הנתונים והחומרה הזמינה וזה יכול לקחת רגע להשלים אותה באתרי גדולים יותר.\n\nנא ליצור קשר עם מפעיל מקומי כדי לקבל מידע נוסף על ההתקדמות.", "smw-upgrade-progress-create-tables": "יצירה (או ע<PERSON><PERSON><PERSON><PERSON>) של טבלאות ומפתחות...", "smw-upgrade-progress-post-creation": "הפעלת משימות אחרי יצירה...", "smw-upgrade-progress-table-optimization": "הרצת מיטוב טבלאות...", "smw-upgrade-progress-supplement-jobs": "הוספת משימות משלימות...", "smw-upgrade-error-title": "שגיאה » מדיה־ויקי סמנטית", "smw-upgrade-error-why-title": "למה אני רואה את הדף הזה?", "smw-upgrade-error-why-explain": "מבנה מסד הנתונים הפנימי של מדיה־ויקי סמנטית השתנה ודורש כמה התאמות כדי לפעול באופן מלא. יכולות להיות לכך כמה סיבות, כולל:\n* נוספו מאפיינים קבועים נוספים (דורש הגדרת טבלה נוספת)\n* שדרוג מכיל כמה שינויים בטבלאות או מפתחות שמחייבים יירוט לפני גישה לנתונים\n* שינויים במנוע אחסון או במנוע השאילתות", "smw-upgrade-error-how-title": "איך לתקן את השגיאה הזאת?", "smw-upgrade-error-how-explain-admin": "מפעיל (או כל אדם בעל הרשאות מפעיל) צריך להריץ את [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] של מדיה־ויקי או את סקריפט תחזוקה [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] של מדיה־ויקי סמנטית.", "smw-upgrade-error-how-explain-links": "באפשרותך גם לעיין בדפים הבאים לסיוע נוסף:\n* הוראות [https://www.semantic-mediawiki.org/wiki/Help:Installation התקנה]\n* דף העזרה [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting Troubleshooting]", "smw-extensionload-error-why-title": "למה אני רואה את הדף הזה?", "smw-extensionload-error-why-explain": "ההרחבה <b>לא</b> נטענה באמצעות <code>enableSemantics</code> ובמקום זאת הופעלה באמצעים אחרים כגון שימוש ב־<code>wfLoadExtension( 'SemanticMediaWiki' )</code> ישירות.", "smw-extensionload-error-how-title": "איך לתקן את השגיאה הזאת?", "smw-extensionload-error-how-explain": "כדי להפעיל את ההרחבה ולהימנע מבעיות עם הצהרות מרחבי שם ותצורות ממתינות, יש להשתמש ב־<code>enableSemantics</code> וזה יבטיח שהמשתנים הנדרשים מוגדרים לפני טעינת ההרחבה דרך ה־<code>ExtensionRegistry</code>.\n\nנא לעיין בדף העזרה [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] לסיוע נוסף.", "smw-upgrade-maintenance-title": "תחזוקה » מדיה־ויקי סמנטית", "smw-upgrade-maintenance-why-title": "למה אני רואה את הדף הזה?", "smw-upgrade-maintenance-note": "המערכת עוברת כעת [https://www.semantic-mediawiki.org/wiki/Help:Upgrade שדרוג] של ההרחבה [https://www.semantic-mediawiki.org/ Semantic MediaWiki] יחד עם מאגר הנתונים שלה ואנו רוצים לבקש ממך להמתין בסבלנות ולאפשר לתחזוקה להימשך לפני שיהיה ניתן להפוך את הוויקי לנגיש שוב.", "smw-upgrade-maintenance-explain": "ההרחבה מנסה למזער את ההשפעה ואת זמן ההשבתה על־ידי דחיית רוב משימות התחזוקה שלה לביצוע אחרי הרצת <code>update.php</code>, אבל כמה שינויים שקשורים למסד הנתונים חייבים להסתיים לפני־כן כדי למנוע חוסר־עקביות בנתונים. זה יכול לכלול:\n* שינוי מבני טבלאות כגון הוספת שדות חדשים או שינוי שדות קיימים \n* שינוי או הוספה של מפתחות טבלה \n* הפעלת מיטוב טבלאות (כאשר מופעלות)", "smw-semantics-not-enabled": "התכונה \"מדיה־ויקי סמנטית\" לא הופעלה בוויקי הזה.", "smw_viewasrdf": "הזנת RDF", "smw_finallistconjunct": " וגם", "smw-factbox-head": "... מידע נוסף על \"$1\"", "smw-factbox-facts": "עובדות", "smw-factbox-facts-help": "הצגת קביעות ועובדות שנוצרו על־ידי משתמש", "smw-factbox-attachments": "צרופות", "smw-factbox-attachments-value-unknown": "לא זמינה", "smw-factbox-attachments-is-local": "מקומית", "smw-factbox-attachments-help": "הצגת צרופות זמינות", "smw-factbox-facts-derived": "עובדות נגזרות", "smw-factbox-facts-derived-help": "הצגת עובדות שנגזרו מתוך חוקים או עם עזרה משיטות הנמקה אחרות", "smw_isspecprop": "זהו מא<PERSON>יין מיוחד בוויקי הזה.", "smw-concept-cache-header": "שימוש במטמון", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count מטמון הרעיונות] מכיל {{PLURAL:$1|ישות אחת|'''$1''' ישויות}} ($2)", "smw-concept-no-cache": "אין מטמון זמין.", "smw_concept_description": "תיאור הרעיון \"$1\"", "smw_no_concept_namespace": "ניתן להגדיר רעיונות רק בדפים שבמרחב השם Concept:.", "smw_multiple_concepts": "לכל דף רעיון יכולה להיות רק הגדרת רעיון אחת.", "smw_concept_cache_miss": "לא ניתן להשתמש ברעיון \"$1\" כרגע, כיוון שתצורת הוויקי דורשת את עיבודו באופן בלתי־מקוון.\nאם התקלה אינה נעלמת לאחר זמן־מה, יש לבקש ממנהל האתר להפוך את הרעיון הזה לזמין.", "smw_noinvannot": "ערכים אינם יכולים להיות מושמים למאפיינים הפוכים.", "version-semantic": "הרחבות סמנטיות", "smw_baduri": "כתובות מהצורה \"$1\" אינן מורשות.", "smw_printername_count": "ספירת התוצאות", "smw_printername_csv": "יצוא CSV", "smw_printername_dsv": "יצוא DSV", "smw_printername_debug": "<PERSON>י<PERSON><PERSON><PERSON> שגיאות (למומחים)", "smw_printername_embedded": "הטבעת תוכן הדף", "smw_printername_json": "יצוא JSON", "smw_printername_list": "רשימה", "smw_printername_plainlist": "רשימה רגילה", "smw_printername_ol": "רשימה ממוספרת", "smw_printername_ul": "רשימה עם תבליטים", "smw_printername_table": "טבלה", "smw_printername_broadtable": "טבלה רחבה", "smw_printername_template": "תבנית", "smw_printername_templatefile": "קובץ התבנית", "smw_printername_rdf": "יצוא RDF", "smw_printername_category": "קטגוריה", "validator-type-class-SMWParamSource": "<PERSON><PERSON><PERSON><PERSON>", "smw-paramdesc-limit": "מספר התוצאות המרבי להחזרה", "smw-paramdesc-offset": "ההיסט של התוצאה הראשונה", "smw-paramdesc-headers": "הצגת הכותרות או שמות המאפיינים", "smw-paramdesc-mainlabel": "התווית שתינתן לשם העמוד הראשי", "smw-paramdesc-link": "הצגת ערכים כקישורים", "smw-paramdesc-intro": "הטקסט להצגה לפני תוצאות התשאול, אם יש כאלו", "smw-paramdesc-outro": "הטקסט להצגה לאחר תוצאות התשאול, אם יש כאלו", "smw-paramdesc-default": "הטקסט להצגה אם אין תוצאות תשאול", "smw-paramdesc-sep": "המפריד בין התוצאות", "smw-paramdesc-propsep": "המפריד בין המאפיינים של ערך התוצאה", "smw-paramdesc-valuesep": "המפריד בין הערכים במא<PERSON>יין של תוצאה", "smw-paramdesc-showsep": "הצגת מפריד בתחילת קובץ CSV (\"מפריד=<ערך>\")", "smw-paramdesc-distribution": "במקום להציג את כל הערכים, לספור את המופעים שלהם ולהציג אותם.", "smw-paramdesc-distributionsort": "למיין את התפלגות הערכים לפי מניין המופעים.", "smw-paramdesc-distributionlimit": "להגביל את התפלגות הערכים לפי מניין של ערכים מסוימים בלבד.", "smw-paramdesc-aggregation": "נא לציין לאיזה דבר הצבירה צריכה להתייחס", "smw-paramdesc-template": "שם התבנית להצגת התדפיסים", "smw-paramdesc-columns": "מספר העמודות להצגת התוצאות", "smw-paramdesc-userparam": "הערך המועבר לכל קריאה לתבנית, אם נעשה שימוש בתבנית", "smw-paramdesc-class": "הגדרת מחלקת CSS נוספת עבור הרשימה", "smw-paramdesc-introtemplate": "שם התבנית שתוצג לפני תוצאות השאילתה, אם יש תוצאות", "smw-paramdesc-outrotemplate": "שם התבנית שתוצג אחרי תוצאות השאילתה, אם יש תוצאות", "smw-paramdesc-embedformat": "תג ה־HTML המשמש להגדרת כותרות", "smw-paramdesc-embedonly": "לא להציג כותרות", "smw-paramdesc-table-class": "מחלקת CSS נוספת שתוחל על הטבלה", "smw-paramdesc-table-transpose": "הצגת כותרות טבלה אנכית ותוצאות אופקית", "smw-paramdesc-prefix": "שליטה בתצוגת מרחב השמות בתדפיסים", "smw-paramdesc-rdfsyntax": "באיזה תחביר RDF להשתמש", "smw-paramdesc-csv-sep": "מגדיר מפריד טורים", "smw-paramdesc-csv-valuesep": "מגדיר מפריד ערכים", "smw-paramdesc-csv-merge": "מיזוג שורות וערכי עמודות עם מזהה נושא זהה (ידוע גם בשם העמודה הראשונה)", "smw-paramdesc-csv-bom": "הוספת BOM (תו הגדרת סופיּות) בחלק העליון של קובץ הפלט", "smw-paramdesc-dsv-separator": "באיזה תו הפרדה להשתמש", "smw-paramdesc-dsv-filename": "שם קובץ DSV", "smw-paramdesc-filename": "שם קובץ הפלט", "smw-smwdoc-description": "הצגת טבלה של כל הפרמטרים שאפשר להשתמש בהם לתבנית התוצאה הזאת יחד עם ערכי ברירת מחדל ותיאורים.", "smw-smwdoc-default-no-parameter-list": "תסדיר התוצאה הזה אינו מספק פרמטרים ספציפיים לתסדיר.", "smw-smwdoc-par-format": "לאיזה תסדיר תוצאה להציג את תיעוד הפרמטרים.", "smw-smwdoc-par-parameters": "אילו פרמטרים להציג. \"specific\" למה שמוסיף התסדיר, \"base\" למה שזמין בכל התסדירים, ו־\"all\" לשניהם.", "smw-paramdesc-sort": "המא<PERSON>יין שהשאילתה תמוין לפיו", "smw-paramdesc-order": "סדר מיון השאילתה", "smw-paramdesc-searchlabel": "טקסט עבור המשך החיפוש", "smw-paramdesc-named_args": "שמות הפרמטרים שיועברו לתבנית", "smw-paramdesc-template-arguments": "מג<PERSON><PERSON>ר כיצד הארגומנטים בעלי השם מועברים לתבנית", "smw-paramdesc-import-annotation": "נתונים מוערים נוספים עומדים להיות מועתקים בזמן פענוח של נושא", "smw-paramdesc-export": "אפשרות יצוא", "smw-paramdesc-prettyprint": "פלט מעוצב יפה עם יותר עימוד וירידות שורה", "smw-paramdesc-json-unescape": "הפלט יכיל לוכסנים בלתי־מחולפים ותווי יוניקוד מרובי־בתים.", "smw-paramdesc-json-type": "סוג ההסדרה", "smw-paramdesc-source": "מק<PERSON><PERSON> שאילתה חלופי", "smw-paramdesc-jsonsyntax": "באיזה תחביר JSON להשתמש", "smw-printername-feed": "הזנת Atom ו־RSS", "smw-paramdesc-feedtype": "סוג הזנה", "smw-paramdesc-feedtitle": "טקסט שישמש לכותרת ההזנה", "smw-paramdesc-feeddescription": "טק<PERSON>ט שישמש לתיאור ההזנה", "smw-paramdesc-feedpagecontent": "תוכן דף שיוצג עם ההזנה", "smw-label-feed-description": "הזנת $2 של $1", "smw-paramdesc-mimetype": "סוג המדיה (סוג MIME) עבור קובץ הפלט", "smw_iq_disabled": "שאילתות סמנטיות הושבטו בויקי הזה.", "smw_iq_moreresults": "... תוצאות נוספות", "smw_parseerror": "הערך שניתן לא הובן.", "smw_notitle": "\"$1\" אינו יכול לשמש שם דף בןויקי הזה.", "smw_noproperty": "המחרוזת \"$1\" לא יכולה להיות שם של מאפיין בוויקי הזה.", "smw_wrong_namespace": "רק דפים ממרחב השם \"$1\" מורשים כאן.", "smw_manytypes": "הוגדר יותר מסוג אחד למאפיין הזה.", "smw_emptystring": "מחרוזות ריקות אינן מתקבלות.", "smw_notinenum": "\"$1\" אינו ברשימה ($2) של  [[Property:Allows value|הערכים האפשריים]] למאפיין \"$3\".", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" אינו ברשימה ($2) של  [[Property:Allows value|הערכים האפשריים]] למאפיין \"$3\".", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" אינו בט<PERSON><PERSON><PERSON> ההוא של \"$2\" שמוגדר על־ידי אילוץ [[Property:Allows value|הערכים האפשריים]] למאפיין \"$3\".", "smw-constraint-error": "בעיית אילוץ", "smw-constraint-error-suggestions": "נא לבדוק את ההפרות והמאפיינים המפורטים יחד עם הערכים המוערים שלהם כדי לוודא שכל דרישות האילוץ מתקיימות.", "smw-constraint-error-limit": "הרשי<PERSON>ה תכיל לכל היותר $1 הפרות.", "smw_noboolean": "[אופס! \"$1\" אינו מאפיין מסוג נכון-לאנכון]", "smw_true_words": "true,t,yes,y,כן,נכון,אמת,חיובי,כ", "smw_false_words": "false,f,no,n,לא,לא נכון,לא-נכון,ש,שקר,שלילי,ל", "smw_nofloat": "\"$1\" אינו מספר.", "smw_infinite": "מספרים גדולים כמו \"$1\" אינם נתמכים.", "smw_unitnotallowed": "\"$1\" אינה יחידה תקינה למדידת המאפיין הזה", "smw_nounitsdeclared": "לא הוגדרו יחידות מידה למאפיין הזה.", "smw_novalues": "לא צוינו ערכים.", "smw_nodatetime": "התאריך \"$1\" אינו מובן.", "smw_toomanyclosing": "נראה שיש מופעים רבים מדי של \"$1\" בשאילתה.", "smw_noclosingbrackets": "בכמה מהפעמים שבהם השתמשת ב־\"<nowiki>[[</nowiki>\" בשאילתה לא דאגת להציב \"]]\" תואם לסגירה.", "smw_misplacedsymbol": "נעשה שימוש בסימן \"$1\" במקום שהוא לא מועיל בו.", "smw_unexpectedpart": "החלק \"$1\" מהשאילתה לא הובן.\nייתכן שהתוצאות לא יתאימו למצופה.", "smw_emptysubquery": "לאחת משאילתות־המשנה אין תנאים תקינים.", "smw_misplacedsubquery": "נעשה שימוש בשאילתת־משנה כלשהי במקום שבו שאילתות־משנה אינן מורשות.", "smw_valuesubquery": "שאילתות־משנה אינן נתמכות עבור ערכי המאפיין \"$1\".", "smw_badqueryatom": "כמה חלקים \"<nowiki>[[…]]</nowiki>\" מהשאילתה לא הובנו.", "smw_propvalueproblem": "ערך המאפיין \"$1\" לא הובן.", "smw_noqueryfeature": "חלק מהתכונות שבשאילתה אינן נתמכות בוויקי הזה וחלקים מהשאילתה הושמטו ($1).", "smw_noconjunctions": "מילות חיבור בשאילתות אינן נתמכות בוויקי הזה וחלק מהשאילתה נזרק ($1).", "smw_nodisjunctions": "מילות הפרדה (disjunctions) בשאילתות אינן נתמכות בוויקי הזה וחלק מהשאילתה נזרק ($1).", "smw_querytoolarge": "לא היה אפשר לשקול את תנאי השאילתה {{PLURAL:$2|הבא|הבאים}} בשל המגבלות שאתר הוויקי הזה מטיל על עומק או גודל של שאילתה: <code>$1</code>.", "smw_notemplategiven": "חובה לתת ערך לפרמטר \"template\" כדי שתסדיר השאילתה הזה יעבוד.", "smw_db_sparqlqueryproblem": "לא ניתן היה לקבל את תוצאת השאילתה ממסד הנתונים SPARQL. השגיאה הזאת יכולה להיות זמנית או להצביע על באג בתכנת מסד הנתונים.", "smw_db_sparqlqueryincomplete": "הסתבר שמענה לשאילתה הזאת קשה מדי והוא בוטל. ייתכן שתוצאות אחדות חסרות. אם זה אפשרי, יש לנסות שאילתה פשוטה יותר.", "smw_type_header": "מאפיינים מסוג \"$1\"", "smw_typearticlecount": "{{PLURAL:$1|מוצג מאפיין אחד המשתמש|מוצגים $1 מאפיינים המשתמשים}} בסוג הזה.", "smw_attribute_header": "דפים המשתמשים במאפיין \"$1\"", "smw_attributearticlecount": "הצגת {{PLURAL:$1|דף אחד שמשתמש|$1 דפים שמשתמשים}} במאפיין הזה.", "smw-propertylist-subproperty-header": "מאפייני־משנה", "smw-propertylist-redirect-header": "שמות נרדפים", "smw-propertylist-error-header": "דפים עם השמות לא הולמות", "smw-propertylist-count": "{{PLURAL:$1|מוצגת ישות קשורה אחת|מוצגות $1 ישויות קשורות}}.", "smw-propertylist-count-with-restricted-note": "{{PLURAL:$1|מוצגת ישות קשורה אחת|מוצגות $1 ישויות קשורות}} (ישויות נוספות זמינות, אך התצוגה מוגבלת ל־$2).", "smw-propertylist-count-more-available": "{{PLURAL:$1|מוצגת ישות קשורה אחת|מוצגות $1 ישויות קשורות}} (ישויות נוספות זמינות).", "specialpages-group-smw_group": "מדיה־ויקי סמנטית", "specialpages-group-smw_group-maintenance": "תחזוקה", "specialpages-group-smw_group-properties-concepts-types": "מאפיינים, רעיונות וסוגים", "specialpages-group-smw_group-search": "עיון וחיפוש", "exportrdf": "יצוא דפים ל־RDF", "smw_exportrdf_docu": "הדף הזה מאפשר לך לאחזר נתונים מדף בתסדיר RDF.\nכדי לייצא דפים, יש לכתוב את כותרות הדפים בתיבת הטקסט שלהלן, כותרת אחת בכל שורה.", "smw_exportrdf_recursive": "יצוא רקורסיבי של כל הדפים הקשורים.\nשימו לב שהתוצאה עלולה להיות גדולה!", "smw_exportrdf_backlinks": "לייצא גם את כל הדפים המפנים לדפים המיוצאים.\nייווצר RDF הניתן לעיון.", "smw_exportrdf_lastdate": "אין לייצא דפים שלא שונו מאז נקודת הזמן שצוינה.", "smw_exportrdf_submit": "יצוא", "uriresolver": "פותר הכתובות", "properties": "מאפיינים", "smw-categories": "קטגוריות", "smw_properties_docu": "בווי<PERSON>י הזה נעשה שימוש במאפיינים הבאים.", "smw_property_template": "$1 מסוג $2 ($3 {{PLURAL:$3|פעם אחת|פעמים}})", "smw_propertylackspage": "כל המאפיינים אמורים להיות מתוארים על־ידי דף!", "smw_propertylackstype": "לא צוין סוג עבור המאפיין הזה (בינתיים נניח שהוא מסוג $1).", "smw_propertyhardlyused": "בקו<PERSON>י נעשה שימוש במאפיין הזה בוויקי הזה!", "smw-property-name-invalid": "לא ניתן להשתמש במאפיין $1 (שם מאפיין בלתי־תקין)", "smw-property-name-reserved": "\"$1\" נמצא ברשימת השמות השמורים, ול<PERSON><PERSON> לא יכול לשמש כמאפיין. ייתכן ש[https://www.semantic-mediawiki.org/wiki/Help:Property_naming דף העזרה הזה] מכיל מידע לגבי הסיבה שבגינה השם הזה שמור.", "smw-sp-property-searchform": "להציג מאפיינים שמכילים:", "smw-sp-property-searchform-inputinfo": "הקלט תלוי־רישיות וכאשר הוא משמש לסינון, מוצגים רק מאפיינים שמתאימים לתנאי.", "smw-special-property-searchform": "הצגת מאפיינים שמכילים:", "smw-special-property-searchform-inputinfo": "הקלט תלוי־רישיות וכאשר הוא משמש לסינון, מוצגים רק מאפיינים שמתאימים לתנאי.", "smw-special-property-searchform-options": "אפשרויות", "smw-special-wantedproperties-filter-label": "מסנן:", "smw-special-wantedproperties-filter-none": "כלום", "smw-special-wantedproperties-filter-unapproved": "לא מאושר", "smw-special-wantedproperties-filter-unapproved-desc": "אפשרות סינון בשימוש בקשר למצב הסמכות.", "concepts": "רעיונות", "smw-special-concept-docu": "ניתן להציג [https://www.semantic-mediawiki.org/wiki/Help:Concepts רעיון] בתור \"קטגוריה דינמית\", כלומר בתור אוסף של דפים שאינם מיוצרים אוטומטית, אלא מחושבים על־ידי מדיה־ויקי סמנטית מתיאור בשאילתה נתונה.", "smw-special-concept-header": "רשימת רעיונות", "smw-special-concept-count": "{{PLURAL:$1|רשום הרעיון הבא|רשומים $1 הרעיונות הבאים}}", "smw-special-concept-empty": "לא נמצא שום רעיון.", "unusedproperties": "מאפיינים שאינם בשימוש", "smw-unusedproperties-docu": "הדף הזה רושם [https://www.semantic-mediawiki.org/wiki/Unused_properties מאפיינים שאינם בשימוש] שמוכרזים אף ששום דף אינו משתמש בהם. לתצוגה מובדלת, ר' את הדפים המיוחדים של [[Special:Properties|כל המאפיינים]] או של [[Special:WantedProperties|המאפיינים הרצויים]].", "smw-unusedproperty-template": "$1 מסוג $2", "wantedproperties": "מאפיינים מבוקשים", "smw-wantedproperties-docu": "הדף הזה מציג [https://www.semantic-mediawiki.org/wiki/Unused_properties מאפיינים מבוקשים] שמשמשים באתר הוויקי, אבל אין שום דף שמתאר אותם. לתצוגה מובדלת, ר' את הדפים המיוחדים של [[Special:Properties|כל המאפיינים]] או של [[Special:UnusedProperties|המאפיינים שאינם בשימוש]].", "smw-wantedproperty-template": "$1 (בשימוש {{PLURAL:$2|פעם אחת|$2 פעמים}})", "smw-special-wantedproperties-docu": "הדף הזה מציג [https://www.semantic-mediawiki.org/wiki/Unused_properties מאפיינים מבוקשים] שמשמשים באתר הוויקי, אבל אין שום דף שמתאר אותם. לתצוגה מובדלת, ר' את הדפים המיוחדים של [[Special:Properties|כל המאפיינים]] או של [[Special:UnusedProperties|המאפיינים שאינם בשימוש]].", "smw-special-wantedproperties-template": "$1 (בשימוש {{PLURAL:$2|פעם אחת|$2 פעמים}})", "smw_purge": "רענון", "smw-purge-update-dependencies": "מדיה־ויקי סמנטית מנקה את הדף הנוכחי עקב כמה תלויות מיושנות שהיא זיהתה שדורשת עדכון.", "smw-purge-failed": "מדיה־ויקי סמנטית ניסתה לנקות את מטמון הדף, אבל נכשלה", "types": "סוגים", "smw_types_docu": "רשימה של [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes סוגי נתונים זמינים] שבה כל [https://www.semantic-mediawiki.org/wiki/Help:Datatype סוג] מייצג ערכה ייחודית של תכונות שמתארות ערך במונחים של מאבחני אחסון ותצוגה שיורשים ממאפיין מוחל.", "smw-special-types-no-such-type": "$1 אינו ידוע או שלא הוגדר כנתון תקין", "smw-statistics": "סטטיסטי<PERSON>ה סמנטית", "smw-statistics-cached": "סטטיס<PERSON><PERSON><PERSON><PERSON> סמנטית (במ<PERSON><PERSON><PERSON><PERSON>)", "smw-statistics-entities-total": "ישויות (בסך הכול)", "smw-statistics-entities-total-info": "ספירת שורות משוערת של ישויות. הוא כולל מאפיינים, רעיונות או כל ייצוג אחר של עצם רשום הדורש השמת מזהה.", "smw-statistics-property-instance": "{{PLURAL:$1|ערך של מאפיין|ערכים של מאפיינים}} (סה\"כ)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|מאפיין|מאפיינים}}]] (סה\"כ)", "smw-statistics-property-total-info": "סך המאפיינים הרשומים.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|מאפיין|מאפיינים}} (סה\"כ)", "smw-statistics-property-used": "{{PLURAL:$1|מאפיין|מאפיינים}} (שמשמשים לפחות עם ערך אחד)", "smw-statistics-property-page": "{{PLURAL:$1|מאפיין (רשום עם דף)|מאפיינים (רשומים עם דף)}}", "smw-statistics-property-page-info": "מנייןהמאפיינים שיש להם דף ותיאור ייעודיים.", "smw-statistics-property-type": "{{PLURAL:$1|מאפיין (משויך לסוג נתונים)|מאפיינים (משויכים לסוג נתונים)}}", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|שאילתה|שאילתות}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|שאילתה|שאילתות}}]] (מוטבעת, סה\"כ)", "smw-statistics-query-format": "תסדיר <code>$1</code>", "smw-statistics-query-size": "גודל השאילתה", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|רעיון|רעיונות}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|רעיון|רעיונות}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|עצם־משנה|עצמי־משנה}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|עצם־משנה|עצמי־משנה}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|סוג נתונים|סוגי נתונים}}]]", "smw-statistics-error-count": "{{PLURAL:$1|ערך מאפיין|ערכ<PERSON> מאפיין}} ([[Special:ProcessingErrorList|{{PLURAL:$1|הערה לא הולמת|הערות לא הולמות}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|ערך מאפיין|ערכ<PERSON> מאפיין}} ({{PLURAL:$1|הערה לא מתאימה|הערות לא מתאימות}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|ישות מיושנת (מסומנת להסרה)|ישויות מיושנות (מסומנות להסרה)}}]", "smw-statistics-delete-count-info": "יש להשליך באו<PERSON><PERSON> קבוע ישויות שסומנו להסרה באמצעות סקריפטי התחזוקה שסופקו.", "smw_uri_doc": "פותרן הכתובות מיישם את [$1 חיפוש W3C TAG ב־httpRange-14].\nהוא מבטיח שייצוג RDF (למכונות) של דף ויקי (לאנשים) נמסר בהתאם לבקשה.", "ask": "<PERSON><PERSON><PERSON><PERSON><PERSON> סמנטי", "smw-ask-help": "הפרק הזה מכיל כמה קישורים שיעזרו להסביר כיצד להשתמש בתחביר <code>#ask</code>.\n\n* הדף [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages בחירת דפים] מתאר כיצד לבחור דפים ולבנות תנאים\n\n* הדף [https://www.semantic-mediawiki.org/wiki/Help:Search_operators אופרטורי חיפוש] מפרט אופרטורי חיפוש זמינים, כולל אלה עבור שאילתות טווח ושאילתות תווים כלליים\n\n* הדף [https ://www.semantic-mediawiki.org/wiki/Help:Displaying_information הצגת מידע] מתאר את השימוש בהצהרות תדפיס ואפשרויות עיצוב", "smw_ask_sortby": "<PERSON>יון לפי טור (לא חו<PERSON>ה)", "smw_ask_ascorder": "בסדר עולה", "smw_ask_descorder": "ב<PERSON><PERSON><PERSON> יורד", "smw-ask-order-rand": "א<PERSON><PERSON><PERSON>י", "smw_ask_submit": "<PERSON><PERSON><PERSON> תוצאות", "smw_ask_editquery": "עריכת השאילתה", "smw_add_sortcondition": "[הוספת הגדרת מיון]", "smw-ask-sort-add-action": "הוסף תנאי מיון", "smw_ask_hidequery": "הסתרת השאילתה (תצוג<PERSON> קטנה)", "smw_ask_help": "עזרה בכתיבת שאילתות", "smw_ask_queryhead": "תנאי", "smw_ask_printhead": "בחירת הדפסה", "smw_ask_printdesc": "(יש להוסיף כל שם מאפיין בשורה נפרדת)", "smw_ask_format_as": "תסדיר:", "smw_ask_defaultformat": "בריר<PERSON> המחדל", "smw_ask_otheroptions": "אפשרויות אחרות", "smw-ask-otheroptions-info": "הפרק הזה מכיל אפשרויות שמשנות קביעות להדפסה. אפשר לראות תיאורי פרמטרים באמצעות מעבר בעכבר.", "smw-ask-otheroptions-collapsed-info": "נא להשתמש בסמל הפלוס כדי להציג את כל האפשרויות הזמינות", "smw_ask_show_embed": "הצגת קוד הטבעה", "smw_ask_hide_embed": "הסתרת קוד הטבעה", "smw_ask_embed_instr": "כדי להטביע את השאילתה הזאת אל תוך דף ויקי, השתמשו בקוד להלן.", "smw-ask-delete": "מחי<PERSON>ה", "smw-ask-sorting": "<PERSON>י<PERSON>ן", "smw-ask-options": "אפשרויות", "smw-ask-options-sort": "מיין אפשרויות", "smw-ask-format-options": "סוג ואפשרויות", "smw-ask-parameters": "פרמטרים", "smw-ask-search": "חיפוש", "smw-ask-debug": "נתח", "smw-ask-debug-desc": "יצירת מידע על ניפוי באגים של שאילתה", "smw-ask-no-cache": "כיבוי מטמון שאילתה", "smw-ask-no-cache-desc": "תוצאות ללא מטמון שאילתה", "smw-ask-result": "תוצאה", "smw-ask-empty": "ניקוי כל הישויות", "smw-ask-download-link-desc": "הורדת תוצאות שאילתות בתסדיר $1", "smw-ask-format": "סוג", "smw-ask-format-selection-help": "עזרה עם הסוגים הנבחרים:$1", "smw-ask-condition-change-info": "התנאי שוּנה, ומנוע החיפוש צריך להריץ מחדש את השאילתה כדי להפיק תוצאות שתואמות את הדרישות החדשות.", "smw-ask-input-assistance": "סיוע קלט", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance סיוע בקלט] מסופק עבור שדה התדפיס, המיון והתאי. שדה התנאי מחייב שימוש באחת מהתחיליות הבאות:", "smw-ask-condition-input-assistance-property": "<code>p:</code> כדי לאחזר הצעות מאפיינים (למשל <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> כדי לאחזר הצעות קטגוריות", "smw-ask-condition-input-assistance-concept": "<code>con:</code> כדי לאחזר הצעות רעיונות", "smw-ask-format-change-info": "התסדיר השתנה ויש להריץ את השאילתה שוב כדי להתאים פרמטרים חדשים ואפשרויות המחזה חדשות.", "smw-ask-format-export-info": "התסדיר שנב<PERSON>ר הוא תסדיר יצוא שאין לו ייצוג חזותי ולכן התוצאות מסופקות כהורדה בלבד.", "smw-ask-query-search-info": "השאילתה <code><nowiki>$1</nowiki></code> נענתה על־ידי {{PLURAL:$3|1=<code>$2</code> (ממטמון)|<code>$2</code> (ממטמון)|<code>$2</code>}} {{PLURAL:$4|בשנייה אחת|ב־$4 שניות}}.", "smw-ask-extra-query-log": "יו<PERSON><PERSON> השאילתה", "smw-ask-extra-other": "<PERSON><PERSON><PERSON>", "searchbyproperty": "חי<PERSON><PERSON><PERSON> לפי מאפיין", "processingerrorlist": "רשימת שגיאות עיבוד", "constrainterrorlist": "רשימת שגיאות אילוצים", "propertylabelsimilarity": "דו\"ח דמיון בין תוויות מאפיינים", "missingredirectannotations": "חסרות הערות להפניה", "smw-processingerrorlist-intro": "הרשימה הבאה מספקת סקירה כללית של [https://www.semantic-mediawiki.org/wiki/Processing_errors שגיאות עיבוד] שהופיעו בהקשר של [https://www.semantic-mediawiki.org/ מדיה־ויקי סמנטית]. מומלץ לנטר את הרשימה הזאת באופן קבוע ולתקן הערות ערכים בלתי־תקינות.", "smw-constrainterrorlist-intro": "הרשימה הבאה מספקת סקירה כללית של [https://www.semantic-mediawiki.org/wiki/Constraint_errors שגיאות אילוצים] שהופיעו בהקשר של [https://www.semantic-mediawiki.org/ מדיה־ויקי סמנטית]. מומלץ לנטר את הרשימה הזאת באופן קבוע ולתקן הערות ערכים בלתי־תקינות.", "smw-missingredirects-intro": "הפרק הבא יפרט דפים שחסרים בהם הערות [https://www.semantic-mediawiki.org/wiki/Redirects הפניה] במדיה־ויקי סמנטית (על־ידי השוואה למידע המאוחסן במדיה־ויקי) וכדי לשחזר את ההערות הללו באופן ידני, יש [https: //www.semantic-mediawiki.org/wiki/Help:Purge לנקות] את הדף או להפעיל את סקריפט התחזוקה <code>rebuildData.php</code> (עם אפשרות <code dir=\"ltr\">--redirects</code>).", "smw-missingredirects-list": "דפים עם הערות חסרות", "smw-missingredirects-list-intro": "{{PLURAL:$1|מוצג דף אחד|מוצגים $1 דפים}} עם הערות הפניה חסרות.", "smw-missingredirects-noresult": "לא נמצאו הערות חסרות להפניה.", "smw_sbv_docu": "חיפוש כל הדפים שיש להם מאפיין וערך מסוימים.", "smw_sbv_novalue": "יש לכתוב ערך תקין למאפיין, או שא<PERSON><PERSON><PERSON> לצפות בכל ערכי המאפיינים עבור \"$1\".", "smw_sbv_displayresultfuzzy": "רשימת כל הדפים בעלי המאפיין \"$1\" עם הערך \"$2\".\nמאחר שיש רק מעט תוצאות, יוצגו גם ערכים סמוכים.", "smw_sbv_property": "מאפיין:", "smw_sbv_value": "ערך:", "smw_sbv_submit": "חי<PERSON><PERSON>ש תוצאות", "browse": "עיו<PERSON> בוויקי", "smw_browselink": "עיון במא<PERSON>יינים", "smw_browse_article": "כתבו את שם הדף שהעיון יתחיל ממנו.", "smw_browse_go": "הצגה", "smw_browse_show_incoming": "הצגת מאפיינים נכנסים", "smw_browse_hide_incoming": "הסתרת מאפיינים נכנסים", "smw_browse_no_outgoing": "לדף הזה אין מאפיינים.", "smw_browse_no_incoming": "אין מאפיינים שמקשרים לדף הזה.", "smw-browse-from-backend": "מידע מאוחזר כעת מהשרת.", "smw-browse-intro": "הדף הזה מספק פרטים על מופע של נושא או ישות, נא להזין את שם העצם לבדיקה.", "smw-browse-invalid-subject": "בדיקת תקינות הנושא חזרה עם השגיאה \"$1\".", "smw-browse-api-subject-serialization-invalid": "לנושא יש תסדיר הסדרה בלתי־תקין.", "smw-browse-js-disabled": "ישנו חשד ש־JavaScript כבוי או אינו זמין. אנחנו ממליצים על שימוש בדפדפן שהוא נתמך בו. אפשרויות אחרות נידונות בדף פרמטר ההגדרות [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "הצג קבוצות", "smw-browse-hide-group": "הצג קבוצות", "smw-noscript": "הדף הזה או הפעולה הזאת דורשים JavaScript כדי לעבוד. נא להפעיל את JavaScript בדפדפן שלך או להשתמש בדפדפן שבו הוא נתמך, כדי שיהיה אפשר לספק הפונקציונליות שהתבקשה. לסיוע נוסף, נא לעיין בדף העזרה [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 מתוך", "smw_inverse_label_property": "תוות מאפיין הפוכה.", "pageproperty": "חיפוש במאפייני דף", "pendingtasklist": "רשימת משימות ממתינות", "facetedsearch": "חיפוש רב־פנים", "smw_pp_docu": "נא להזין או דף ומאפיין, או רק מאפיין כדי לאחזר את כל הערכים המשויכים.", "smw_pp_from": "מהדף:", "smw_pp_type": "מא<PERSON><PERSON>ין", "smw_pp_submit": "חי<PERSON><PERSON>ש תוצאות", "smw-prev": "{{PLURAL:$1|הקודם|$1 הקודמים}}", "smw-next": "{{PLURAL:$1|הבא|$1 הבאים}}", "smw_result_prev": "הקודם", "smw_result_next": "הבא", "smw_result_results": "תוצאות", "smw_result_noresults": "אין תוצאות.", "smwadmin": "לוח בקרה של מדיה־ויקי סמנטית", "smw-admin-statistics-job-title": "סטטיסטיקות של משימה", "smw-admin-statistics-job-docu": "סטטיסטיקת המשימות מציגה מידע על משימות מתוזמנות של מדיה־ויקי סמנטית שעדיין לא בוצעו. מספר המשימות עשוי להיות מעט לא מדויק או להכיל ניסיונות כושלים. נא לעיין ב[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue ספר ההוראות] למידע נוסף.", "smw-admin-statistics-querycache-title": "מט<PERSON><PERSON><PERSON> שאילתה", "smw-admin-statistics-querycache-disabled": "[https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] לא הופעל בוויקי הזה ולכן הסטטיסטיקה אינה זמינה.", "smw-admin-statistics-querycache-legend": "סטטיסטיקות המטמון מכילות נתונים מצטברים ונתונים נגזרים זמניים, כולל:\n* \"פספוסים\" (misses): סך הניסיונות לאחזר נתונים מהמטמון עם תגובות בלתי ניתנות להשגה, מה שמאלץ אחזור ישיר של מאגר (בסיס נתונים, מאגר משולש וכו'). \n* \"מחיקות\" (deletes): סך פעולות פינוי מטמון (באמצעות ניקוי או בתלות בשאילתה)\n* \"שליפות\" (hits): סך שליפות המטמון ממקורות מוטבעים (שאילתות שנקראות מתוך דף ויקי) או לא מוטבעים (אם מופעל, כאלה שמתבקשים על־ידי דפים כמו Special:Ask או ה־API)\n* \"זמן תגובת אחזור חציוני\" (medianRetrievalResponseTime): ערך התמצאות של זמן התגובה החציוני (בשניות) עבור בקשות אחזור מוטמנות ולא מוטמנות במהלך טווח הזמן של תהליך האיסוף\n* \"ללא מטמון\" (noCache): מציין את סך הבקשות ללא ניסיון (מגבלה=0 שאילתות, אפשרות \"ללא מטמון\" וכו') כדי לאחזר תוצאות מהמטמון", "smw-admin-statistics-section-explain": "הפרק מספ<PERSON> סטטיסטיקות נוספות למפעילים.", "smw-admin-statistics-semanticdata-overview": "סקירה כללית", "smw-admin-permission-missing": "הגישה לדף הזה נחסמה עקב הרשאות חסרות, נא לעיין בדף העזרה [https://www.semantic-mediawiki.org/wiki/Help:Permissions הרשאות] לפרטים על ההגדרות הנחוצות.", "smw-admin-setupsuccess": "מנוע האחסון הוגדר.", "smw_smwadmin_return": "חזרה אל $1", "smw_smwadmin_updatestarted": "תהליך עדכון חדש לרענון הנתונים הסמנטיים הורץ.\nכל הנתונים המאוחסנים ייבנו מחדש או יתוקנו היכן שיש צורך.\nאפשר לעקוב אחר תהליך העדכון בדף המיוחד הזה.", "smw_smwadmin_updatenotstarted": "יש כבר תהליך עדכון פעיל.\nלא ייווצר אחד נוסף.", "smw_smwadmin_updatestopped": "כל תהליכי העד<PERSON><PERSON>ן הקיימים נעצרו.", "smw_smwadmin_updatenotstopped": "כדי לעצור תהליך עדכון פעיל, יש לסמן את תיבת הסימון כדי לציין שזה באמת מה שרצית.", "smw-admin-docu": "הדף המיוחד הזה יעזור לך במהלך התקנה, שדרוג, תחזוקה ושימוש ב<a href=\"https://semantic-mediawiki.org\">מדיה־ויקי הסמנטית</a>. הוא גם ישמש למטרות ניהוליות נוספות ויספק סטטיסטיקות.\nיש לזכור לגבות את הנתונים החיוניים לך בטרם הפעלת התכונות הניהוליות.", "smw-admin-environment": "סביבת תוכנה", "smw-admin-db": "הגדרות מסד נתונים", "smw-admin-db-preparation": "אתחול הטבלה נמשך ועשוי להימשך קצת עד הצגת התוצאות, וזה תלוי גם בגודל ובמיטוב אפשרי של הטבלה.", "smw-admin-dbdocu": "מדיה־ויקי סמנטית דורשת מבנה מסד הנתונים משלה (ועצמאי ממדיה־ויקי, ולכן לא משפיע על שאר ההתקנה של מדיה־ויקי) כדי לאחסן את הנתונים הסמנטיים.\nניתן להפעיל את פונקציית ההגדרה הזאת כמה פעמים מבלי לגרום לנזק כלשהו, אך היא נדרשת רק פעם אחת עם ההתקנה או שדרוג.", "smw-admin-permissionswarn": "אם הפעולה נכשלת עם שגיאות SQL, למשתמש של בסיס הנתונים של אתר הוויקי שלך (אפשר למצוא אותו בקובץ ה־LocalSettings.php שלך) אין הרשאות מתאימות.\nיש להוסיף למשתמש הזה הרשאות ליצירה ולמחיקה של טבלאות, לכתוב את פרטי הגישה לשורש בסיס הנתונים לקובץ LocalSetting.php באופן זמני, או להשתמש בסקריפט התחזוקה <code>setupStore.php</code> שיכול להשתמש בנתוני האמנה של מפעיל.", "smw-admin-dbbutton": "אתחול או שדרוג של טבלאות", "smw-admin-announce": "פרסום הוויקי שלך", "smw-admin-announce-text": "אם הוויקי שלך ציבורי, אפ<PERSON>ר לרשום אותו ב־<a href=\"https://wikiapiary.com\">WikiApiary</a>, אתר ויקי שעוקב אחרי אתרי ויקי.", "smw-admin-deprecation-notice-title": "הודעות על התיישנות", "smw-admin-deprecation-notice-docu": "הפרק הבא מכיל הגדרות שהוצאו משימוש או הוסרו אך זוהו כפעילות בוויקי הזה. יש לצפות לכך שכל גרסה עתידית תסיר את התמיכה בתצורות האלו.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> הוכרז בתור מיושן והוא יוסר בגרסה $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> יסיר (או יחליף) את {{PLURAL:$2|האפשרות הבאה|האפשרויות הבאות}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> הוכר<PERSON> בתור מיושן והוא יוסר בגרסה $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> הוחלף ב־<code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> הוחלף ב־<code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "{{PLURAL:$2|אפשרויות}} <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> מוחלף ב־<code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> הוסר בגרסה $2", "smw-admin-deprecation-notice-title-notice": "הגדרות מיושנות", "smw-admin-deprecation-notice-title-notice-explanation": "<b>הגדרו<PERSON> מיושנות</b> מציגה הגדרות שזוהו בתור כאלה שנמצאות בשימוש בוויקי הזה ויש תכנון להסיר או לשנות אותן במהדורה עתידית.", "smw-admin-deprecation-notice-title-replacement": "הגדרות שהוחלפו או ששמן שונה", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>הגדרות שהוחלפו או ששמן שונה</b> מכיל הגדרות ששמן שונה או ששונו בדרך אחרת ושמומלץ לעדכן לאלתר את השם או את התסדיר שלהן.", "smw-admin-deprecation-notice-title-removal": "הגדרות מחוקות", "smw-admin-deprecation-notice-title-removal-explanation": "<b>הגדרות מחוקות</b> מכ<PERSON>ל הגדרות שהוסרו במהדורה קודמת אך זוהו בשימוש בוויקי הזה.", "smw-admin-deprecation-notice-section-legend": "מקרא", "smw-smwadmin-refresh-title": "תיקון ועדכון נתונים", "smw_smwadmin_datarefresh": "בנייה מחדש של נתונים", "smw_smwadmin_datarefreshdocu": "ניתן לשחזר את כל הנתונים של מדיה־ויקי הסמנטי לפי התוכן הנוכחי של הוויקי.\nהפעולה הזאת עשויה להיות שימושית לתיקון נתונים שגויים או לעדכון הנתונים אם המבנה הפנימי שונה לאור שדרוג התוכנה.\nהעדכון מופעל דף־דף והוא לא יושלם מייד.\nהמידע להלן מראה האם העדכון מתבצע כעת ומאפשר לך להתחיל או להפסיק את העדכונים (אלא אם כן התכונה הזאת הושבתה על־ידי מנהל האתר).", "smw_smwadmin_datarefreshprogress": "<strong>כבר מתבצע תהליך עדכון.</strong>\nמצב בו העדכון מתקדם לאט הוא מצב נורמלי, מאחר שהנתונים מתעדכנים בחלקים קטנים בכל פעם שמשתמש ניגש לוויקי.\nכדי לסיים את העדכון מהר יותר, תוכלו להפעיל את סקריפט התחזוקה <code>runJobs.php</code> (השתמשו באפשרות <code>--maxjobs 1000</code> כדי להגביל את מספר העדכונים שמתבצעים בבת אחת).\nהתקדמות משוערת של העדכון הנוכחי:", "smw_smwadmin_datarefreshbutton": "קבע בנייה מחדש של נתונים", "smw_smwadmin_datarefreshstop": "עצירת העדכון הזה", "smw_smwadmin_datarefreshstopconfirm": "כן, אני {{GENDER:$1|בטוח|בטוחה}}.", "smw-admin-job-scheduler-note": "משימות (אלה המופעלות) בפרק הזה מבוצעות דרך תור המשימות כדי למנוע מצבי נעילה במהלך ביצוען. [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue תור המשימות] אחראי על העיבוד וזה קריטי שלסקריפט התחזוקה <code>runJobs.php</code> תהיה קיבולת מתאימה (ר' גם את פרמטר התצורה <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "השלכת ישויות מיושנות", "smw-admin-outdateddisposal-intro": "פעילויות מסוימות (שינוי בסוג מאפיין, הסרת דפי ויקי או תיקון ערכי שגיאה) יגרמו להופעת [https://www.semantic-mediawiki.org/wiki/Outdated_entities ישויות מיושנות] ומוצע להסיר אותן מעת לעת כדי לפנות שטח טבלה משויך.", "smw-admin-outdateddisposal-active": "תושמנה משימת השלכת ישויות מיושנות.", "smw-admin-outdateddisposal-button": "תזמון השלכה", "smw-admin-feature-disabled": "התכונה הזאת הושבתה בוויקי הזה, נא לעיין בדף העזרה של <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">ההגדרות</a> או לפנות למפעיל.", "smw-admin-propertystatistics-title": "בנייה מחדש של סטטיסטיקות מאפיינים", "smw-admin-propertystatistics-intro": "בנייה מחדש את כל הנתונים הסטטיסטיים של השימוש במאפיין ותוך כדי זה, עדכ<PERSON><PERSON> ותיקון של [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count ספירת השימוש] במאפיינים.", "smw-admin-propertystatistics-active": "תוזמנה משימת בנייה מחדש של סטטיסטיקת מאפיינים.", "smw-admin-propertystatistics-button": "תזמון בנייה מחדש של סטטיסטיקות", "smw-admin-fulltext-title": "בנייה מחדש של חיפוש בטקסט מלא", "smw-admin-fulltext-intro": "בנייה מחדש של מפתח החיפוש מטבלאות מאפיינים שסוג הנתונים [https://www.semantic-mediawiki.org/wiki/Full-text חיפוש בטקסט מלא] מופעל בהם. השינויים בכללי המפתוח (מילות עצירה שהשתנו, ניתוח צורות חדש וכו') או טבלה חדשה שנוספה או השתנתה מחייבת להפעיל שוב את המשימה הזאת.", "smw-admin-fulltext-active": "תושמנה משימת בנייה מחדש של חיפוש בטקסט מלא.", "smw-admin-fulltext-button": "תזמון בנייה מחדש של טקסט מלא", "smw-admin-support": "קבלת תמיכה", "smw-admin-supportdocu": "מגוון משאבים עומדים לרשותך כדי לעזור במקרה של בעיה:", "smw-admin-installfile": "אם יש לך בעיות בהתקנה, נא להתחיל בבדיקת ההנחיות המופיעות ב<a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">קובץ INSTALL</a> וב<a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">דף ההתקנה</a>.", "smw-admin-smwhomepage": "התיעוד המלא למשתמשי המדיה־ויקי הסמנטית נמצא בכתובת <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "ניתן לדווח על באגים ב<a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">כלי מעקב הבעיות</a> (issue tracker), הדף על <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">דיווח באגים</a> מספק הנחיות איך לכתוב דיווח בעיה יעיל.", "smw-admin-questions": "אם יש לך הצעות או שאלות נוספות, נא להצטרף לדיון שנערך ב<a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">רשימת התפוצה של המשתמשים</a> של Semantic MediaWiki.", "smw-admin-other-functions": "פונקציות אחרות", "smw-admin-statistics-extra": "פונקציות סטטיסטיות", "smw-admin-statistics": "סטטיסטיקות", "smw-admin-supplementary-section-title": "פונקציות משלימות", "smw-admin-supplementary-section-subtitle": "תכונות ליבה נתמכות", "smw-admin-supplementary-section-intro": "הפרק הזה מספק תכונות נוספות מעבר לתחום פעולות התחזוקה, וייתכן שחלק מהתכונות שרשומות (ר' את ה[https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions תיעוד]) מוגבלות או לא זמינות, ולכן אינן נגישות בוויקי הזה.", "smw-admin-supplementary-settings-title": "תצורה והגדרות", "smw-admin-supplementary-settings-intro": "<u>$1</u> מציג פרמטרים המגדירים את ההתנהגות של מדיה־ויקי סמנטית", "smw-admin-main-title": "מדיה־ויקי סמנטית » $1", "smw-admin-supplementary-operational-statistics-title": "סטטיסטיקות תפעוליות", "smw-admin-supplementary-operational-statistics-short-title": "סטטיסטיקות תפעוליות", "smw-admin-supplementary-operational-statistics-intro": "מציג ערכה מורחבת של <u>$1</u>", "smw-admin-supplementary-idlookup-title": "חיפוש וזריקה של ישויות", "smw-admin-supplementary-idlookup-short-title": "חיפוש וזריקה של ישויות", "smw-admin-supplementary-idlookup-intro": "תומך בפונקציה פשוטה של <u>$1</u>", "smw-admin-supplementary-duplookup-title": "חיפוש ישויות כפולות", "smw-admin-supplementary-duplookup-intro": "יש לבצע <u>$1</u> כדי למצוא ישויות שמסווגות ככפולות עבור מטריצת הטבלה שנבחרה", "smw-admin-supplementary-duplookup-docu": "הדף הזה מפרט ערכים מטבלאות נבחרות שסווגו כ[https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities כפולים]. ערכים כפולים אמורים (אם בכלל) להתרחש רק במקרים נדירים שעלולים להיגרם כתוצאה מעדכון שהופסק או תנועת שחזור (rollback) לא מוצלחת.", "smw-admin-supplementary-operational-statistics-cache-title": "סטטיסטיקות מטמון", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> מציג ערכה נבחרת של סטטיסטיות שקשורות למטמון", "smw-admin-supplementary-operational-table-statistics-title": "סטטיסטיקות טבלה", "smw-admin-supplementary-operational-table-statistics-short-title": "סטטיסטיקות טבלה", "smw-admin-supplementary-operational-table-statistics-intro": "מייצר <u>$1</u> עבור ערכה נבחרה של טבלאות", "smw-admin-supplementary-operational-table-statistics-explain": "הפרק הזה מכיל סטטיסטיקות טבלה נבחרות כדי לעזור למפעילי מערכת ולאוצרי נתונים לקבל החלטות מושכלות לגבי המצב הזה של השרת ומנוע האחסון.", "smw-admin-supplementary-operational-table-statistics-legend": "המקרא מתאר חלק מהמפתחות המשמשים לסטטיסטיקה של הטבלה וכולל:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> – המספר הכולל של שורות בטבלה", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> המזהה אחרון שבשימוש כרגע\n* <code>duplicate_count</code> מספר הכפולים שנמצאו ב־id_table (ר' גם [[Special:SemanticMediaWiki/duplicate-lookup|חיפוש ישויות כפולות]])\n* <code>rows.rev_count</code> מספר השורות שמושם להן revision_id שמציין קישור ישיר לדף ויקי\n* <code>rows.smw_namespace_group_by_count</code> מספרים של שורות מצטברות עבור מרחבי שמות המשמשים בטבלה\n* <code>rows.smw_proptable_hash.query_match_count</code> מספר עצמי־המשנה של שאילתות עם הפניה תואמת לטבלה\n* <code>rows.smw_proptable_hash.query_null_count</code> מספר עצמי־המשנה של שאילתות ללא הפניה לטבלה (לא מקושר, הפניה צפה)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> אחוז המונחים הייחודיים (שיעור אחוז נמוך מצביע על כך שמונחים שחוזרים על עצמם תופסים את תוכן הטבלה ואת המפתח)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> מספר מונחים המופיעים פעם אחת בלבד\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> מספר מונחים המופיעים יותר מפעם אחת", "smw-admin-supplementary-elastic-version-info": "גרסה", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> – מציג פרטים על הגדרות וסטטיסטיקות של מפתוח", "smw-admin-supplementary-elastic-docu": "הדף הזה מכיל מידע על סטטיסטיקות של הגדרות, מיפויים, בריאות ומפתוח, הקשורות לאשכול Elasticsearch שמחובר להרחבה \"מדיה־ויקי סמנטית\" ול־[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "פונקציות נתמכות", "smw-admin-supplementary-elastic-settings-title": "הגדרות (מפתחות)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> – הגדרות שנמצאות בשימוש על־ידי החיפוש הגמיש (Elasticsearch) לניהול מדדים של ההרחבה \"מדיה־ויקי סמנטית\"", "smw-admin-supplementary-elastic-mappings-title": "מיפויים", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> – רשימה של מיפויי המדדים והשדות", "smw-admin-supplementary-elastic-mappings-docu": "הדף הזה מכיל פרטי מיפוי שדות שמשמשים את המפתח הנוכחי. מומלץ לנטר את המיפויים בקשר עם <code>index.mapping.total_fields.limit</code> (מגדיר את מספר השדות המרבי המותר במפתח).", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code> מתייחס לספירה של שדות הליבה הממופתחים בעוד ש־<code>nested_fields</code> מתייחס לספירה מצטברת של שדות נוספים שהוקצו לשדה ליבה כדי לתמוך בדפוסי חיפוש מובנים ספציפיים.", "smw-admin-supplementary-elastic-mappings-summary": "סיכום", "smw-admin-supplementary-elastic-mappings-fields": "מיפויי שדות", "smw-admin-supplementary-elastic-nodes-title": "צמתים", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> – הצגת סטטיסטיקות של צמתים", "smw-admin-supplementary-elastic-indices-title": "מדדים", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> – סקירה כללית של המדדים הרלוונטיים ושל הסטטיסטיקות שלהם", "smw-admin-supplementary-elastic-statistics-title": "סטטיסטיקות", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> – הצגת סטטיסטיקות ברמת מפתוח", "smw-admin-supplementary-elastic-statistics-docu": "הדף הזה מספק תובנות לגבי הנתונים הסטטיסטיים של פעולות שונות שמתרחשות ברמת מפתוח. הסטטיסטיקות המוחזרות הן קיבוץ של הבחירה המוקדמת ושל ההצטברות הכוללת. [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html דף העזרה] מכיל תיאור מפורט של הנתונים הסטטיסטיים הזמינים.", "smw-admin-supplementary-elastic-status-replication": "<PERSON><PERSON><PERSON> שכ<PERSON>ול", "smw-admin-supplementary-elastic-status-last-active-replication": "שכ<PERSON>ול פעיל אחרון: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "מרווח רענון: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "רשימת המתנה של משימות שחזור: $1 (הערכה)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "רשימת המתנה של משימות שאיבה (של קבצים): $1 (הערכה)", "smw-admin-supplementary-elastic-status-rebuild-lock": "שכפול הכול: $1 (מתבצעת בנייה מחדש)", "smw-admin-supplementary-elastic-status-replication-monitoring": "ניטור שכפול (פעיל): $1", "smw-admin-supplementary-elastic-replication-header-title": "<PERSON><PERSON><PERSON> שכ<PERSON>ול", "smw-admin-supplementary-elastic-replication-function-title": "שכ<PERSON>ול", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> מציג מידע על שכפולים שנכשלו", "smw-admin-supplementary-elastic-replication-docu": "הדף הזה מספק מידע על [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring מצב השכפול] של ישויות שדווחו בתור כאלה שיש להן בעיות באשכול Elasticsearch. מומלץ לסקור את הישויות הרשומות ולנקות את התוכן כדי לוודא שזאת בעיה זמנית.", "smw-admin-supplementary-elastic-replication-files-docu": "יש לציין כי עבור רשימת הקבצים, משימת ה־[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion file ingest] (שאיבה) חייבת להתבצע קודם ויש לסיים את העיבוד שלה.", "smw-admin-supplementary-elastic-replication-files": "קבצים", "smw-admin-supplementary-elastic-replication-pages": "דפים", "smw-admin-supplementary-elastic-endpoints": "נקודות קצה", "smw-admin-supplementary-elastic-config": "ערכות הגדרה", "smw-admin-supplementary-elastic-no-connection": "כרגע הוויקי '''אינו יכול''' ליצור התחברות לאשכול Elasticsearch, נא ליצור קשר עם מנהל הוויקי כדי לחקור את הבעיה, כיו<PERSON>ן שהוא משביתה את המפתח ואת יכולת השאילתות של המערכת.", "smw-list-count": "הרשי<PERSON><PERSON> מכילה {{PLURAL:$1|פריט אחד|$1 פריטים}}.", "smw-property-label-uniqueness": "התווית \"$1\" התאימה לייצוג מאפיין אחד אחר לפחות. נא לבדוק את [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness דף העזרה] על מידע על פתרון הבעיה הזאת.", "smw-property-label-similarity-title": "דו\"ח דמיון תוויות מאפיינים", "smw-property-label-similarity-intro": "<u>$1</u> מח<PERSON><PERSON> דמיונות בין תוויות מאפיינים קיימות", "smw-property-label-similarity-threshold": "סף:", "smw-property-label-similarity-type": "מזהה סוג תצוגה", "smw-property-label-similarity-noresult": "לא נמצאו תוצאות", "smw-property-label-similarity-docu": "הדף הזה משווה את [https://www.semantic-mediawiki.org/wiki/Property_similarity מרחק הדמיון] (לא להתבלבל עם סמיון סמנטי או מילולי) בין תוויות מאפיינים ומדווח אותם אם הם עוברים את הרף. הדו\"ח יכול לעזור לסנן מאפיינים עם שגיאות כתיב או מאפיינים שווי ערך שמייצגים את אותו הרעיון (ר' את הדף המיוחד [[Special:Properties|properties]] כדי לקבל עזרה בהבהרת הרעיון והשימוש במאפיינים המדווחים). אפשר לכוונן את הרף כדי להרחיב או לצמצם את המרחק המשמש להתאמה מוערכת. המאפיין <code>[[Property:$1|$1]]</code> משמש להוצאת מאפיינים מהכללה בניתוח.", "smw-admin-operational-statistics": "הדף הזה מכיל סטטיסטיקות תפועליות שנאספות בתוך פונקציות של מדיה־ויקי סמנטית או מתוכן. אפשר למצוא רשימה מורחבת של סטטיסטיקות ייחודיות לוויקי [[Special:Statistics|<b>כאן</b>]].", "smw_adminlinks_datastructure": "מבנה הנתונים", "smw_adminlinks_displayingdata": "הצגת נתונים", "smw_adminlinks_inlinequerieshelp": "עזרה על שאילתות משובצות", "smw-page-indicator-usage-count": "הערכת [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count מניין שימוש]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "מאפיין מוגדר על־ידי {{PLURAL:$1|משתמש|מערכת}}", "smw-property-indicator-last-count-update": "מניין שימוש מוערך\nעודכן לאחרונה: $1", "smw-concept-indicator-cache-update": "מניין מטמון\nעודכן לאחרונה: $1", "smw-createproperty-isproperty": "זהו מאפיין מסוג $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|הערך האפשרי למאפיין הזה הוא|הערכים האפשריים למאפיין הזה הם}}:", "smw-paramdesc-category-delim": "מפריד", "smw-paramdesc-category-template": "תסדיר לעיצוב הפריטים", "smw-paramdesc-category-userparam": "פרמטר שיוע<PERSON>ר לתבנית", "smw-info-par-message": "הודעה להצגה.", "smw-info-par-icon": "איזה סמל להראות \"info\" או \"warning\".", "prefs-smw": "מדיה־ויקי סמנטית", "prefs-general-options": "אפשרויות כלליות", "prefs-extended-search-options": "<PERSON><PERSON><PERSON><PERSON><PERSON> מורחב", "prefs-ask-options": "<PERSON><PERSON><PERSON><PERSON><PERSON> סמנטי", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ מדיה־ויקי סמנטית] והרחבות קשורות מספקות הגדרות פרטניות לקבוצה של תכונות ופונקציות נבחרות. רשימת הגדרות פרטניות עם תיאורים ומאפיינים שלהם זמינה ב[https://www.semantic-mediawiki.org/wiki/Help:User_preferences דף העזרה] הבא.", "smw-prefs-ask-options-tooltip-display": "להציג את טקסט הפרמטר בתור רמז צץ עם מידע בדף המיוחד [[Special:Ask|בונה שאילתות]] #ask.", "smw-prefs-ask-options-compact-view-basic": "הפעלת תצוגה בסיסית מצומצמת", "smw-prefs-help-ask-options-compact-view-basic": "אם זה מופעל, תוצג ערכה מצומצמת של קישורים בתצוגה המצומצמת של Special:Ask.", "smw-prefs-general-options-time-correction": "הפעלת תיקון זמן בדפים מיוחדים באמצעות העדפת [[Special:Preferences#mw-prefsection-rendering|היסט זמן]].", "smw-prefs-general-options-jobqueue-watchlist": "הצגת רשימת מעקב בסרגל האישי שלי", "smw-prefs-help-general-options-jobqueue-watchlist": "אם זה מופעל, מוצגת [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist רשימה של] משימות ממתינות נבחרות עם גודל התורים המשוער שלהן.", "smw-prefs-general-options-disable-editpage-info": "להשבית את טקסט המבוא בדף העריכה", "smw-prefs-general-options-disable-search-info": "השבתת המידע על תמיכה בתחביר בדף החיפוש הרגיל", "smw-prefs-general-options-suggester-textinput": "להפעיל סיוע בקלט עבור ישויות סמנטיות", "smw-prefs-help-general-options-suggester-textinput": "אם זה מופעל, מא<PERSON><PERSON>ר להשתמש ב[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance סיוע בקלט] כדי למצוא מאפיינים, מושגים וקטגוריות מהקשר קלט.", "smw-prefs-general-options-show-entity-issue-panel": "הצגת חלונית בעיות הישות", "smw-prefs-help-general-options-show-entity-issue-panel": "אם זה מופעל, זה מריץ בדיקות תקינות בכל דף ומציג את [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel חלונית בעיות הישות].", "smw-prefs-factedsearch-profile": "נא לבחור את ערכת ההגדרות ההתחלתית של [[Special:FacetedSearch|חיפוש רב־פנים]]:", "smw-ui-tooltip-title-property": "מא<PERSON><PERSON>ין", "smw-ui-tooltip-title-quantity": "המרת יחידות", "smw-ui-tooltip-title-info": "מידע", "smw-ui-tooltip-title-service": "קישורי שירות", "smw-ui-tooltip-title-warning": "אזהרה", "smw-ui-tooltip-title-error": "שגיאה", "smw-ui-tooltip-title-parameter": "פרמ<PERSON>ר", "smw-ui-tooltip-title-event": "אירוע", "smw-ui-tooltip-title-note": "הערה", "smw-ui-tooltip-title-legend": "מקרא", "smw-ui-tooltip-title-reference": "<PERSON><PERSON><PERSON><PERSON>", "smw_unknowntype": "הסוג \"$1\" של המאפיין הזה אינו תקין", "smw-concept-cache-text": "לרעיון הזה יש {{PLURAL:$1|דף אחד|$1 דפים}} והוא עודכן ב־$2 ב־$3.", "smw_concept_header": "דפים מהרעיון \"$1\"", "smw_conceptarticlecount": "{{PLURAL:$1|מוצג להלן דף אחד|מוצגים להלן $1 דפים}}.", "smw-qp-empty-data": "לא ניתן להציג את הנתונים המבוקשים בגלל אמות מידה בלתי־מספיקות לבחירה.", "right-smw-admin": "גישה למשימות ניהול (מדיה־ויקי סמנטית)", "right-smw-patternedit": "עריכת גישה כדי לתחזק ביטויים רגולריים ותבניות (מדיה־ויקי סמנטית)", "right-smw-pageedit": "גישת עריכה עבור דפים מוערים ב־<code>Is edit protected</code> (מדיה־ויקי סמנטית)", "right-smw-schemaedit": "עריכת [https://www.semantic-mediawiki.org/wiki/Help:<PERSON><PERSON>a דפי סכֶמה] (מדיה־ויקי סמנטית)", "right-smw-viewjobqueuewatchlist": "גישה לתכונת [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist רשימת מעקב] תור המשימות (מדיה־ויקי סמנטית)", "right-smw-viewentityassociatedrevisionmismatch": "גישה למידע על חוסר התאמה של גרסה הקשורה לישות (מדיה־ויקי סמנטית)", "right-smw-vieweditpageinfo": "הצגת [https://www.semantic-mediawiki.org/wiki/Help:Edit_help עזרה בעריכה] (מדיה־ויקי סמנטית)", "restriction-level-smw-pageedit": "מוגן (רק למשתמשים מורשים)", "action-smw-patternedit": "לערוך ביטויים רגולריים שבשימוש של מדיה־ויקי סמנטית", "action-smw-pageedit": "לערוך דפים מוערים ב־<code>Is edit protected</code> (מדיה־ויקי סמנטית)", "group-smwadministrator": "מנהלים (מדיה־ויקי סמנטית)", "group-smwadministrator-member": "{{GENDER:$1|מנהל|מנהלת}} (מדיה־ויקי סמנטית)", "grouppage-smwadministrator": "{{ns:project}}:מנהלים (מדיה־ויקי סמנטית)", "group-smwcurator": "אוצרים (מדיה־ויקי סמנטית)", "group-smwcurator-member": "{{GENDER:$1|אוצר|אוצרת}} (מדיה־ויקי סמנטית)", "grouppage-smwcurator": "{{ns:project}}:אוצרים (מדיה־ויקי סמנטית)", "group-smweditor": "עורכים (מדיה־ויקי סמנטית)", "group-smweditor-member": "{{GENDER:$1|עורך|עורכת}} (מדיה־ויקי סמנטית)", "grouppage-smweditor": "{{ns:project}}:עורכים (מדיה־ויקי סמנטית)", "action-smw-admin": "לגשת למשימות ניהול של מדיה־ויקי סמנטית", "action-smw-ruleedit": "לערוך דפי חוקים (מדיה־ויקי סמנטית)", "smw-property-namespace-disabled": "המאפיין [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks מרחב שם] כבוי, ניסיון להכריז על סוג או על מאפיינים מסוימים אחרים למאפיין הזה אינו אפשרי.", "smw-property-predefined-default": "\"$1\" הוא מאפיין מוגדר מראש מסוג $2.", "smw-property-predefined-common": "המאפיין הזה מותקן מראש (ידוע גם בתור [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מאפיין מיוחד]) ובא עם הרשאות ניהול נוספות, אבל יכול לשמש כמו כל [https://www.semantic-mediawiki.org/wiki/Property מפאיין שמוגדר על־ידי משתמש].", "smw-property-predefined-ask": "$1 הוא מאפיין מוגדר מראש שמייצג מטא־מידע (בצורת [https://www.semantic-mediawiki.org/wiki/Subobject תת־עצם]) על שאילתות פרטניות והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-asksi": "\"$1\" הוא מאפיין מוגדר מראש שאוסף את מספר התנאים שמשמשים בשאילתה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "\"$1\" הוא מאפיין מוגדר מראש שנותן מידע על עומק של שאילתה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "זהו ערך מספרי המחושב על סמך קינון שאילתות־משנה, שרשראות מאפיינים ורכיבי תיאור זמינים, כאשר ביצוע השאילתה מוגבל בפרמטר התצורה <code dir=\"ltr\">[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "\"$1\" הוא מאפיין מוגדר מראש שמתאר פרמטרים שמשפיעים על תוצאת שאילתה והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-askpa": "זהו חלק מאוסף של מאפיינים המציינים [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler פרופיל שאילתה].", "smw-sp-properties-docu": "הדף הזה מציג רשימת [https://www.semantic-mediawiki.org/wiki/Property מאפיינים] ומניין השימוש בהם כפי שזמין בוויקי הזה. לסטטיסטיקת ספירה מעודכנת מומלץ שתסריט התחזוקה ל[https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics סטטיסטיקות מאפיינים] יורץ באופן קבוע. לתצוגה מבודלת נא לראות את הדפים המיוחדים [[Special:UnusedProperties|מאפיינים שאינם בשימוש]] או [[Special:WantedProperties|מאפיינים מבוקשים]].", "smw-sp-properties-cache-info": "הנתונים האשומים אוחזרו מ[https://www.semantic-mediawiki.org/wiki/Caching מטמון] ועודכנו לאחרונה $1.", "smw-sp-properties-header-label": "רשימת מאפיינים", "smw-admin-settings-docu": "רשימה של כל ההגדרות ההתחלתיות והמקומיות שרלוונטיות לסביבת מדיה־ויקי סמנטית. לפרטים על הגדרות פרטניות נא לקרוא את דף העזרה על [https://www.semantic-mediawiki.org/wiki/Help:Configuration הגדרות].", "smw-sp-admin-settings-button": "יצירת רשימת הגדרות", "smw-admin-idlookup-title": "חיפוש", "smw-admin-idlookup-docu": "הפרק הזה מציג פרטים טכניים על ישות פרטנית (דף ויקי, עצם־משנה, מאפיין, וכו') במדיה־ויקי סמנטית. הקלט יכול להיות מזהה מספרי או מחרוזת המתאימים לשדה החיפוש הרלוונטי, אך עליו להיות מזהה של הערת שוליים כלשהי השייכת למדיה־ויקי סמנטית ולא לדף או לגרסה של מדיה־ויקי.", "smw-admin-iddispose-title": "זריקה", "smw-admin-iddispose-docu": "יש לציין שפעולת הזריקה אינה מוגבלת והיא תסיר את הישות ממנוע האחסון יחד עם כל הערות השוליים שלה בטבלאות הממתינות, אם תאושר. נא לעשות את הפעולה הזאת '''בזהירות''' ורק לאחר בדיקת [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal התיעוד].", "smw-admin-iddispose-done": "המזהה \"$1\" הוסר משרת האחסון.", "smw-admin-iddispose-references": "למזהה \"$1\" {{PLURAL:$2|אין הערת שוליים פעילה|יש לפחות הערת שוליים פעילה אחת}}:", "smw-admin-iddispose-references-multiple": "רשימת התאמות עם רשומת הפניה פעילה אחת לפחות.", "smw-admin-iddispose-no-references": "החיפוש לא יכול היה להתאים את המזהה \"$1\" לישות בטבלה.", "smw-admin-idlookup-input": "חיפוש:", "smw-admin-objectid": "מזהה:", "smw-admin-tab-general": "סקירה כללית", "smw-admin-tab-notices": "הודעות על התיישנות", "smw-admin-tab-maintenance": "תחזוקה", "smw-admin-tab-supplement": "פונקציות משלימות", "smw-admin-tab-registry": "רישום", "smw-admin-tab-alerts": "התראות", "smw-admin-alerts-tab-deprecationnotices": "הודעות על התיישנות", "smw-admin-alerts-tab-maintenancealerts": "התראות תחזוקה", "smw-admin-alerts-section-intro": "הפרק הזה מציג התראות והודעות הקשורות להגדרות, פעולות ופעילויות אחרות שסווגו כך שהן דורשות התייחסות ממנהל מערכת או משתמש עם הרשאות מתאימות.", "smw-admin-maintenancealerts-section-intro": "יש לפתור את ההתראות וההודעות הבאות, ולמרות שאינן חיוניות, הן צפויות לסייע בשיפור התחזוקה המערכתחץ והתפעולית.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "מיטוב טבלה", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "המערכת מצאה ש[https://www.semantic-mediawiki.org/wiki/Table_optimization מיטוב הטבלה] האחרון הופעל {{PLURAL:$2|אתמול|שלשום|לפני $2 ימים|3=ל<PERSON><PERSON>י יומיים}} (שיא מאז $1) וזה חורג מסף התחזוקה של {{PLURAL:$3|יום אחד|יומיים|$3 ימים}}. כפי שהוזכר בתיעוד, הרצת מיטוב תאפשר למתכנן השאילתות לקבל החלטות טובות יותר לגבי שאילתות ולכן מומלץ להפעיל את מיטוב הטבלה על באופן קבוע.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "ישויות לא מעודכנות", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "המערכת ספרה $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities ישויות מיושנות] והגיעה לרמה קריטית של תחזוקה ללא השגחה על־ידי חריגה מהסף של $2. מומלץ להפעיל את סקריפט התחזוקה [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>].", "smw-admin-maintenancealerts-invalidentities-alert-title": "ישויות בלתי־תקינות", "smw-admin-maintenancealerts-invalidentities-alert": "המערכת התאימה [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|ישות אחת|$1 ישויות}}] ל־[https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace מרחב שם לא מתוחזק ומומלץ להפעיל את] סקריפט התחזוקה [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] או [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>].", "smw-admin-deprecation-notice-section": "מדיה־ויקי סמנטית", "smw-admin-configutation-tab-settings": "הגדרות", "smw-admin-configutation-tab-namespaces": "מרח<PERSON>י שם", "smw-admin-configutation-tab-schematypes": "סוגי סכֵמה", "smw-admin-maintenance-tab-tasks": "משימות", "smw-admin-maintenance-tab-scripts": "סקריפטים לתחזוקה", "smw-admin-maintenance-no-description": "<PERSON>י<PERSON> תיאור.", "smw-admin-maintenance-script-section-title": "רשימה של סקריפטים זמינים לתחזוקה", "smw-admin-maintenance-script-section-intro": "סקריפטי התחזוקה הבאים דורשים מפעיל מערכת וגישה לשורת הפקודה כדי שתהיה אפשרות להפעיל סקריפטים ברשימה.", "smw-admin-maintenance-script-description-dumprdf": "יצוא RDF של שלישיות קיימות.", "smw-admin-maintenance-script-description-rebuildconceptcache": "הסקריפט הזה משמש לניהול מטמוני רעיונות עבור מדיה־ויקי סמנטית, שם הוא יכול ליצור, להסיר ולעדכן מטמונים נבחרים.", "smw-admin-maintenance-script-description-rebuilddata": "יוצר מחדש את כל הנתונים הסמנטיים במסד הנתונים, על־ידי מעבר בין כל הדפים שעשויים להכיל נתונים סמנטיים.", "smw-admin-maintenance-script-description-rebuildelasticindex": "בונה מחדש את מפתח Elasticsearch (רק עבור התקנות שמשתמשות ב־<code>ElasticStore</code>), על־ידי מעבר על כל הישויות שיש להן נתונים סמנטיים.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "מציאת ישויות חסרות ב־Elasticsearch (רק עבור התקנות המשתמשות ב־<code>ElasticStore</code>) ותזמון משימות עדכון מתאימות.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "בונה מחדש את מפתח החיפוש המלא של <code>SQLStore</code> (עבור התקנות שבהן ההגדרה הופעלה).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "בונה מחדש את סטטיסטיקת השימוש עבור כל ישויות המאפיין.", "smw-admin-maintenance-script-description-removeduplicateentities": "מסיר ישויות כפולות שנמצאו בטבלאות נבחרות שאין להן הפניות פעילות.", "smw-admin-maintenance-script-description-setupstore": "מגדיר את שרת האחסון והשאילתות כהגדרתו ב־<code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "מעדכן את השדה <code>smw_sort</code> ב־<code>SQLStore</code> (בהתאם להגדרה [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "מאכלס את השדה <code>smw_hash</code> עבור שורות חסרות הערך.", "smw-admin-maintenance-script-description-purgeentitycache": "ניקוי ערכי מטמון עבור ישויות ידועות והנתונים המשויכים אליהן.", "smw-admin-maintenance-script-description-updatequerydependencies": "עדכון שאילתות ותלויות בשאילתה (ר' את ההגדרה [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "השלכת ישויות מיושנות וקישורי שאילתות מיושנים.", "smw-admin-maintenance-script-description-runimport": "אכלוס ויבוא תוכן שהתגלה אוטומטית מ־[https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "סקריפטים לעדכון", "smw-admin-maintenance-script-section-rebuild": "סקריט<PERSON>ים לבנייה מחדש", "smw-livepreview-loading": "בטעינה…", "smw-sp-searchbyproperty-description": "הדף הזה מספק [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces ממשק עיון] פשוט למציאת ישויות שמתוארות על־ידי מאפיין וערך בעל־שם. ממשקי חיפוש זמינים אחרים הם [[Special:PageProperty|חיפוש מאפיין דף]] ו[[Special:Ask|בונה שאילתות]].", "smw-sp-searchbyproperty-resultlist-header": "רשימת תוצאות", "smw-sp-searchbyproperty-nonvaluequery": "רשימה של תוצאות שהוקצה להן המאפיין \"$1\".", "smw-sp-searchbyproperty-valuequery": "רשימת דפים שיש להן המאפיין \"$1\" עם הערך \"$2\" מוער.", "smw-datavalue-number-textnotallowed": "לא ניתן להציב את \"$1\" לסוג מספר מוצהר עם הערך $2.", "smw-datavalue-number-nullnotallowed": "\"$1\" הוחזר עם \"NULL\" שאינו ערך מספרי מותר.", "smw-editpage-annotation-enabled": "הדף הזה תומך בהערות סמנטיות בתוך הטקסט (למשל <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) לבניית תכן מובנה ושאיל שמספקת מדיה־ויקי סמנטית. לתיאור מקיף של איך להשתמש בהערות או בפונקציית המפענח #ask, נא להסתכל בדפי העזרה [https://www.semantic-mediawiki.org/wiki/Help:Getting_started getting started]‏, [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation in-text annotation], או [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-editpage-annotation-disabled": "הדף הזה לא מופעל להערות בתוך הטקסט בשל מגבלות מרחב שם. ניתן למצוא פרטים על איך להפעיל את מרחב השם בדף העזרה [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration].", "smw-editpage-property-annotation-enabled": "אפשר להחיב את המאפיין הזה באמצעות הערות סמנטיות כדי להגדיר סוג נתונים (למשל <nowiki>\"[[Has type::Page]]\"</nowiki>) או הצהרות תומכות אחרות (למשל <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). לתיאור של איך להרחיב את הדף הזה, ר' את דפי העזרה [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaration of a property] או [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes list of available data types].", "smw-editpage-property-annotation-disabled": "לא ניתן להרחיב את המאפיין הזה עם הערת סוג נתונים (למשל <nowiki>\"[[Has type::Page]]\"</nowiki>) משום שהוא כבר מוגדר מראש (ר' את הדף העזרה [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special properties] למידע נוסף).", "smw-editpage-concept-annotation-enabled": "אפשר להרחיב את הרעיון הזה באמצעות פונקציית המפענח #concept. לתיאור של השימוש ב־#concept, ר' את דף העזרה [https://www.semantic-mediawiki.org/wiki/Help:Concepts concept].", "smw-search-syntax-support": "קלט החיפוש תומך בשימוש ב־[https://www.semantic-mediawiki.org/wiki/Help:Semantic_search תחביר שאילתה] סמנטי כדי לסייע בהתאמת תוצאות באמצעות מדיה־ויקי סמנטית.", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance סיוע בקלט] מופעל גם כדי להקל על הבחירה מראש של מאפיינים וקטגוריות זמינות.", "smw-search-help-intro": "קלט <code><nowiki>[[ ... ]]</nowiki></code> יאותת למעבד הקלט להשתמש בשרת החיפוש של מדיה־ויקי סמנטית. יש לציין כי שילוב <code><nowiki>[[ ... ]]</nowiki></code> עם חיפוש טקסט לא מובנה כגון <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> לא נתמך.", "smw-search-help-structured": "חיפושים מובנים:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (כמו [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context הקשר מסונן])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (עם [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context הקשר שאילתה])", "smw-search-help-proximity": "חיפושי קרבה (מא<PERSON><PERSON>ין אינו ידוע, זמין '''רק''' עבור שרתים שמספקים שילוב חיפוש בטקסט מלא):\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code> (לחפש בכל המסמכים \"lorem\" ו-\"ipsum\" שמופתחו)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (להתאים את \"lorem ipsum\" כביטוי)", "smw-search-help-ask": "הקישורים הבאים יסבירו כיצד להשתמש בתחביר <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages בחירת דפים] מתאר כיצד לבחור דפים ולבנות תנאים!\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators אופרטורים של חיפוש] מפרט אופרטורים זמינים של חיפוש, כולל אלו עבור שאילתות טווח ותווים כלליים", "smw-search-input": "קלט וחיפוש", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance סיוע בקלט] מסופק עבור שדה הקלט ודורש להשתמש באחת התחיליות הבאות:\n\n* <code>p:</code> כדי להפעיל הצעות מאפיינים (למשל <code><nowiki>[[p:Has ...</nowiki></code>)\n\n* <code>c:</code> כדי להפעיל הצעות לקטגוריות\n\n* <code>con:</code> כדי להפעיל הצעות רעיות", "smw-search-syntax": "תחב<PERSON>ר", "smw-search-profile": "מו<PERSON><PERSON><PERSON>", "smw-search-profile-tooltip": "אפשרויות חיפוש שמחוברות עם ההרחבה \"מדיה־ויקי סמנטית\"", "smw-search-profile-sort-best": "ההתא<PERSON>ה הטובה ביותר", "smw-search-profile-sort-recent": "אחרונות", "smw-search-profile-sort-title": "כותרת", "smw-search-profile-extended-help-intro": "[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile הפרופיל המורחב] Special:Search מספק גישה לפונקציות חיפוש ספציפיות למדיה־ויקי סמנטית ולשרת השאילתה הנתמך שלה.", "smw-search-profile-extended-help-sort": "מציין העדפת מיון עבור תצוגת התוצאה עם:", "smw-search-profile-extended-help-sort-title": "* \"כותרת\" באמצעות כותרת הדף (או כותרת התצוגה) כקריטריוני מיון", "smw-search-profile-extended-help-sort-recent": "* \"אחרונות\" יציג תחילה את הישויות שהשתנו לאחרונה (ישויות עצם־משנה יידחקו החוצה כי אינן מסומנות ב[[Property:Modification date|תאריך שינוי]])", "smw-search-profile-extended-help-sort-best": "* \"ההתאמה הטובה ביותר\" ימיין ישויות לפי [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy רלוונטיות] על סמך דירוגים שסופקו על־ידי השרת", "smw-search-profile-extended-help-form": "הטפסים מסופקים (אם הם מתוחזקים) כדי להתאים למקרי שימוש מסוימים על־ידי חשיפת שדות מאפיינים וערכים שונים כדי לצמצם את תהליך הקלט ולהקל על המשתמשים להמשיך בבקשת חיפוש. (ר' $1)", "smw-search-profile-extended-help-namespace": "תיבת בחירת מרחב השם תהיה מוסתרת ברגע שנבחר טופס אבל ניתן יהיה להפוך אותה לגלויה בעזרת כפתור \"הצגה/הסתרה\".", "smw-search-profile-extended-help-search-syntax": "שדה קלט החיפוש תומך בשימוש בתחביר <code>#ask</code> כדי להגדיר הקשר חיפוש מסוים למדיה־ויקי סמנטית. ביטויים שימושיים כוללים:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> כדי למצוא כל דבר שמכיל \"...\" והוא שימושי במיוחד כאשר הקשר החיפוש או המאפיינים המעורבים אינם ידועים (למשל <code>in:(lorem && ipsum)</code> שווה ערך ל־<code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> כדי למצוא כל דבר שמכיל \"...\" באותו סדר בדיוק", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code dir=\"ltr\">has:</code> כדי להתאים לכל ישות מאפיין \"...\" (למשל <code dir=\"ltr\">has:(Foo && Bar)</code> שווה ערך ל־<code dir=\"ltr\"><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> כדי לא להתאים לשום ישות שכוללת \"...\"", "smw-search-profile-extended-help-search-syntax-prefix": "*  זמינות ומוגדרות תחיליות מותאמות נוספות, כגון: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* ביטויים מסוימים שמורים כגון: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''חלק מהפעולות הרשומות שימושיות רק בקשר עם מפתוח טקסט מלא מופעל או עם ElasticStore.''", "smw-search-profile-extended-help-query": "השתמש ב־<code><nowiki>$1</nowiki></code> בתור שאילתה.", "smw-search-profile-extended-help-query-link": "לפרטים נוספים, יש להשתמש ב$1", "smw-search-profile-extended-help-find-forms": "מסמכים זמינים", "smw-search-profile-extended-section-sort": "<PERSON><PERSON><PERSON><PERSON> לפי", "smw-search-profile-extended-section-form": "טפסים", "smw-search-profile-extended-section-search-syntax": "קלט לחיפוש", "smw-search-profile-extended-section-namespace": "מר<PERSON><PERSON> שם", "smw-search-profile-extended-section-query": "שאילתה", "smw-search-profile-link-caption-query": "בונה שאילתה", "smw-search-show": "הצגה", "smw-search-hide": "להסתיר", "log-name-smw": "יומן מדיה־ויקי סמנטית", "log-show-hide-smw": "$1 יומן מדיה־ויקי סמנטית", "logeventslist-smw-log": "יומן מדיה־ויקי סמנטית", "log-description-smw": "פעולות עבור [https://www.semantic-mediawiki.org/wiki/Help:Logging סוגי אירועים מופעלים] שדווחו על־ידי מדיה־ויקי סמנטית והמרכיבים שלה.", "logentry-smw-maintenance": "אירועים של תחזוקה שיוצאים מתוך מדיה־ויקי סמנטית", "smw-datavalue-import-unknown-namespace": "מרחב השמות ליבוא \"$1\" אינו ידוע. נא לוודא שפרטי היבוא של OWL זמינים דרך [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "לא ניתן למצוא URI של  מרחב שמות של \"$1\" ב[[MediaWiki:Smw import $1|יבוא $1]].", "smw-datavalue-import-missing-type": "לא נמצא סוג נתונים עבור \"$1\" ב־[[MediaWiki:Smw import $2|$2 יצוא]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|יבוא $1]]", "smw-datavalue-import-invalid-value": "הפורמט של \"$1\" אינו תקין, והוא אמור להיות מהצורה \"namespace\":\"identifier\" (למשל, \"foaf:name\").", "smw-datavalue-import-invalid-format": "המחרוזת \"$1\" הייתה אמורה להיות מחולקת לארבעה חלקים, אך הפורמט שלה לא היה מובן", "smw-property-predefined-impo": "\"$1\" הוא מאפיין מוגדר מראש שמתאר יחס ל[https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary מילון מיובא] ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "\"$1\" הוא מאפיין מוגדר מראש שמתאר את [[Special:Types|סוג הנתונים]] של מאפיין ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "\"$1\" הוא מאפיין מוגדר מראש שמייצג מבנה [https://www.semantic-mediawiki.org/wiki/Help:Container מכל] והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "המכל מאפשר לאגור השמות מאפיין־ערך בדומה לדף ויקי רגיל, אבל בתוך מרחב ישויות נפרד עם קישור לנושא המטביע.", "smw-property-predefined-errp": "\"$1\" הוא מאפיין מוגדר מראש שעוקב אחרי שגיאות קלט להערות ערכים חריגות ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "ברוב המקרים זה נגרם מחוסר־התאמה בסוג או במגבלת [[Property:Allows value|ערך]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] הוא מאפיין מוגדר מראש שיכול להגדיר רשימת ערכים שאפשר להתיר כדי להגביל את השמת הערכים למאפיין ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list \"$1\"] הוא מאפיין מוגדר מראש שיכול להגדיר הפניה לרשימת ערכים שאפשר להתיר כדי להגביל את השמת הערכים למאפיין ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "למא<PERSON>יין \"$1\" יש אזור יישום מוגבל והוא לא יכול לשמש כמאפיין הערות על־ידי משתמש.", "smw-datavalue-property-restricted-declarative-use": "המא<PERSON>יין \" $1 \" הוא מאפיין הצהרתי וניתן להשתמש בו רק בדף מאפיין או בדף קטגוריה.", "smw-datavalue-property-create-restriction": "המאפיין \"$1\" אינו קיים ולמשתמש חסרה ההרשאה \"$2\" (ר' [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode מצב סמכות]) ליצור או להעיר ערכים עם מאפיין לא מאושר.", "smw-datavalue-property-invalid-character": "המא<PERSON>יין \"$1\" מכיל את התו הרשום \"$2  כחלק מתווית המאפיין ולכן סווג כבלתי־תקין.", "smw-datavalue-property-invalid-chain": "השימוש ב{{GRAMMAR:תחילית|$1}} כשרשרת מאפיינים אינו מותר במהלך תהליך ההערה.", "smw-datavalue-restricted-use": "ערך הנתונים \"$1\" סומן לשימוש מוגבל.", "smw-datavalue-invalid-number": "לא ניתן לפרש את \"$1\" בתור מספר.", "smw-query-condition-circular": "תנאי מעגלי אפשרי זוהה בתבנית \"$1\".", "smw-query-condition-empty": "בתיאור השאילתה יש תנאי ריק.", "smw-types-list": "רשימת סוגי נתונים", "smw-types-default": "\"$1\" הוא סוג נתונים מובנה.", "smw-types-help": "מידע נוסף ודוגמאות אפשר למצוא ב[https://www.semantic-mediawiki.org/wiki/Help:Type_$1 דף העזרה הזה].", "smw-type-anu": "\"$1\" הוא הגוון של סוג הנתונים [[Special:Types/URL|URL]] והוא משמש להצהרת היצוא ''owl:AnnotationProperty''.", "smw-type-boo": "\"$1\" הוא סוג נתונים בסיסי לתיאור ערך אמת/שקר.", "smw-type-cod": "\"$1\" הוא הגוון של סוג הנתונים [[Special:Types/Text|Text]] לשימוש בטקסטים טכניים באורך כלשהו, כגון קוד מקור.", "smw-type-geo": "\"$1\" הוא סוג נתונים שמתאר מיקומים גאוגרפיים ודורש את ההרחבה [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] בשביל לספק פונקצינליות מורחבת.", "smw-type-tel": "\"$1\" הוא סוג נתונים מיוחד לתיאור מספר טלפון בין־לאומי בהתאם ל־RFC 3966.", "smw-type-txt": "\"$1\" הוא סוג נתונים בסיסי לתיאור מחרוזות באורך כלשהו.", "smw-type-dat": "\"$1\" הוא סוג נתונים בסיסי לייצוג נקודות בזמן בתסדיר מאוחד.", "smw-type-ema": " $1\" הוא סוג נתונים מיוחד לייצוג דוא\"ל.", "smw-type-tem": "\"$1\" הוא סוג נתונים מספרי מיוחד לייצוג טמפרטורה.", "smw-type-qty": " $1\" הוא סוג נתונים לתיאור כמויות עם ייצוג מספרי ויחידת מדידה.", "smw-type-rec": "\"$1\" הוא סוג נתונים מכלי המציין רשימה של מאפיינים בעלי־סוג בסדר קבוע.", "smw-type-extra-tem": "סכֵמת ההמרה כוללת יחידות נתמכות כגון קלווין, צלזיוס, פרנהייט ורנקין.", "smw-type-tab-properties": "מאפיינים", "smw-type-tab-types": "סוגים", "smw-type-tab-type-ids": "מזהי סוגים", "smw-type-tab-errors": "שגיאות", "smw-type-primitive": "בסיסיים", "smw-type-contextual": "הקשריים", "smw-type-compound": "מורכבים", "smw-type-container": "מכלים", "smw-type-no-group": "לא מסווגים", "smw-special-pageproperty-description": "הדף הזה מספק ממשק עיון למציאת כל הערכים של מאפיין ודף נתון. ממשקי חיפוש זמינים אחרים כוללים את [[Special:SearchByProperty|חיפו<PERSON> מאפיין]], ואת [[Special:Ask|בונה השאילתות ask]].", "smw-property-predefined-errc": "\"$1\" הוא מאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] ומייצג שגיאות שמופיעות בהקשר עם ערך או עיבוד קלט לא הולם.", "smw-property-predefined-long-errc": "שגיאות נאספות ב[https://www.semantic-mediawiki.org/wiki/Help:Container מכל] שיכול לכלול הפניה למאפיין שגרם לחוסר העקביות.", "smw-property-predefined-errt": "\"$1\" הוא מאפיין מוגדר מראש שמכיל תיאור בטקסט של שגיאה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "תת־עצם שהוגדר על־ידי המשתמש הכיל שיטת מתן שמות בלתי־תקינה. כתיבת נקודה ($1) בחמשת התווים הראשונים ($1) שמורה להרחבות. באפשרותך להגדיר [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier מזהה בעל שם].", "smw-datavalue-record-invalid-property-declaration": "הגדרת הרשומה כוללת את המאפיין \"$1\" שבעצמה מוגדרת בתור סוג רשומה ואינה מותרת.", "smw-property-predefined-mdat": "\"$1\" הוא מאפיין מוגדר מראש שמתאים לתאריך השינוי האחרון של הנושא ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "\"$1\" הוא מאפיין מוגדר מראש שמתאים לתאריך השינוי הראשון של הנושא ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "\"$1\" הוא מאפיין מוגדר מראש שמציין אם הנושא חדש או לא ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "\"$1\" הוא מאפיין מוגדר מראש שמכיל את שם הדף של הדף שיצר את הגרסה האחרונה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "\"$1\" הוא מאפיין מוגדר מראש שמציין את סוג ה־MIME של קובץ שהועלה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "\"$1\" הוא מאפיין מוגדר מראש שמתאר את סוג המדיה של קובץ שהועלה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "\"$1\" הוא מאפיין מוגדר מראש שמחזיק את שם תסדיר התוצאה שמשמש בשאילתה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "\"$1\" הוא מאפיין מוגדר מראש שמציין את התנאים של השאילתה כמחרוזת ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "\"$1\" הוא מאפיין מוגדר מראש שמכיל ערך זמן (בשניות) שנדרש כדי להשלים את ביצוע השאילתה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "\"$1\" הוא מאפיין מוגדר מראש המסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] שמזהה מקורות שאילתות חלופיים (למשל מרוחקים, בפדרציה).", "smw-property-predefined-askco": "\"$1\" הוא מאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לתאר את המצב של שאילתה או של רכיבים שלה.", "smw-property-predefined-long-askco": "המספר או המספרים שהוקצו מייצגים מצב מקודד פנימי המוסבר ב[https://www.semantic-mediawiki.org/wiki/Help:Query_profiler דף העזרה].", "smw-property-predefined-prec": "\"$1\" הוא מאפיין מוגדר מראש שמתאר [https://www.semantic-mediawiki.org/wiki/Help:Display_precision דיוק תצוגה] (בספרות עשרוניות) לסוגי נתונים מספריים.", "smw-property-predefined-attch-link": "\"$1\" הוא מאפיין מוגדר מראש שאוסף קישורים לקבצים ותמונות שנמצאים בדף והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-inst": "\"$1\" הוא מאפיין פנימי מוגדר מראש שמאחסן מידע על קטגוריה עצמאית ממדיה־ויקי והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-unit": "\"$1\" הוא מאפיין הצהרתי מוגדר מראש להגדרת יחידות למאפיינים מספריים בעלי סוג והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-unit": "רשימה מופרדת בפסיקים מאפשרת לתאר יחידות או תסדירים לשימוש עבור התצוגה.", "smw-property-predefined-conv": "\"$1\" הוא מאפיין הצהרתי מוגדר מראש להגדרת גורם ההמרה ליחידה מסוימת של כמות פיזית והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-serv": "\"$1\" הוא מאפיין הצהרתי מוגדר מראש להוספת קישורי שירות למאפיין והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-redi": "\"$1\" הוא מאפיין פנימי מוגדר מראש לרישום הפניות והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-subp": "\"$1\" הוא מאפיין הצהרתי מוגדר מראש להגדרה שמאפיין הוא [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of מאפיין־משנה] של מאפיין אחר והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-subc": "\"$1\" הוא מאפיין מוגדר מראש להגדרה שקטגוריה היא [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of קטגוריית־משנה] של קטגוריה אחרת והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-conc": "\"$1\" הוא מאפיין פנימי מוגדר מראש להגדרת רעיון משויך והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-err-type": "\"$1\" הוא מאפיין מוגדר מראש לזיהוי קבוצה או מחלקה של [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors שגיאות עיבוד] והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-skey": "\"$1\" הוא מאפיין פנימי מוגדר מראש שמחזיק הפניית מיון והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-pplb": "\"$1\" הוא מאפיין הצהרתי מוגדר מראש לציון [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label תווית מאפיין מועדף] והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-chgpro": "\"$1\" הוא מאפיין מוגדר מראש שמחזיק מידע על [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation הפצת שינויים] והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-schema-link": " ומסופק על־ידי [מדיה־ויקי סמנטית].", "smw-property-predefined-format-schema": " ומסופק על־ידי [מדיה־ויקי סמנטית].", "smw-property-predefined-profile-schema": " ומסופק על־ידי [מדיה־ויקי סמנטית].", "smw-property-predefined-trans": " ומסופק על־ידי [מדיה־ויקי סמנטית].", "smw-property-predefined-trans-source": " ומסופק על־ידי [מדיה־ויקי סמנטית].", "smw-property-predefined-trans-group": " ומסופק על־ידי [מדיה־ויקי סמנטית].", "smw-property-predefined-cont-len": "\"$1\" הוא מאפיין מוגדר מראש שמאחסן מידע על אורך והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-cont-len": "הוא משמש עם [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ועם [https://www.semantic-mediawiki.org/Attachment_processor מעבד הצרופות]) כדי לאסוף ולאחסן מידע על אורך שמאוחזר מקובץ שנשאב (אם מסופק).", "smw-property-predefined-cont-lang": "\"$1\" הוא מאפיין מוגדר מראש שמאסחן מידע על שפה והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-cont-lang": "הוא משמש עם [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ועם [https://www.semantic-mediawiki.org/Attachment_processor מעבד הצרופות]) כדי לאסוף ולאחסן מידע על שפה שמאוחזר מקובץ שנשאב (אם מסופק).", "smw-property-predefined-cont-title": "\"$1\" הוא מאפיין מוגדר מראש שמאחסן מידע על כותרת והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-cont-title": "הוא משמש עם [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ועם [https://www.semantic-mediawiki.org/Attachment_processor מעבד הצרופות]) כדי לאסוף ולאחסן מידע על כותרות שמאוחזר מקובץ שנשאב (אם מסופק).", "smw-property-predefined-cont-author": "\"$1\" הוא מאפיין מוגדר מראש שמאחסן מידע על מחבר והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-cont-author": "הוא משמש עם [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ועם [https://www.semantic-mediawiki.org/Attachment_processor מעבד הצרופות]) כדי לאסוף ולאחסן מידע על מחברים שמאוחזר מקובץ שנשאב (אם מסופק).", "smw-property-predefined-cont-date": "\"$1\" הוא מאפיין מוגדר מראש שמאחסן מידע על תאריך והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-cont-date": "הוא משמש עם [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ועם [https://www.semantic-mediawiki.org/Attachment_processor מעבד הצרופות]) כדי לאסוף ולאחסן מידע על תאריכים שמאוחזר מקובץ שנשאב (אם מסופק).", "smw-property-predefined-cont-type": "\"$1\" הוא מאפיין מוגדר מראש שמאחסן מידע על סוג קובץ והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-cont-type": "הוא משמש עם [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ועם [https://www.semantic-mediawiki.org/Attachment_processor מעבד הצרופות]) כדי לאסוף ולאחסן מידע על סוגים שמאוחזר מקובץ שנשאב (אם מסופק).", "smw-property-predefined-cont-keyw": "\"$1\" הוא מאפיין מוגדר מראש שמייצג מילות מפתח והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-cont-keyw": "הוא משמש עם [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ועם [https://www.semantic-mediawiki.org/Attachment_processor מעבד הצרופות]) כדי לאסוף ולאחסן מידע על מילות מפתח שמאוחזר מקובץ שנשאב (אם מסופק).", "smw-property-predefined-file-attch": "\"$1\" הוא מאפיין מוגדר מראש שמייצג מכל שמאחסן מידע על צרופות והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-long-file-attch": "הוא משמש עם [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ועם [https://www.semantic-mediawiki.org/Attachment_processor מעבד הצרופות]) כדי לאסוף את המידע הייחודי לתוכן שאפשר לאחזר מקובץ שנשאב (אם מסופק).", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps Extension \"Maps\"] לא נמצא ולכן \"$1\" מוגבל ביכולת פעילותו.", "smw-datavalue-monolingual-dataitem-missing": "ח<PERSON><PERSON> פריט דרוש לבניית ערך חד-לשוני.", "smw-datavalue-languagecode-missing": "בשביל הערת \"$1\" המפענח לא הצליח להבין מה קוד השפה (למשל \"foo@en\").", "smw-datavalue-languagecode-invalid": "\"$1\" לא זוהה בתור קוד שפה נתמך.", "smw-property-predefined-lcode": "\"$1\" הוא מאפיין מוגדר מראש שמייצג קוד שפה בתסדיר BCP47 ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "\"$1\" הוא סוג נתונים [https://www.semantic-mediawiki.org/wiki/Help:Container מכיל] שמשייך ערך טקסט עם [[Property:Language code|קוד שפה]] מסוים.", "smw-types-extra-mlt-lcode": "סוג הנתונים {{PLURAL:$2|דורש|אינו דורש}} קוד שפה (כלומר, הערת ערך ללא קוד שפה {{PLURAL:$2|לא תתקבל|תתקבל}}).", "smw-property-predefined-text": "\"$1\" הוא מאפיין מוגדר מראש שמייצג טקסט באורך שרירותי ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "\"$1\" הוא מאפיין מוגדר מראש שמאפשר לתאר מאפיין בהקשר של שפה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "\"$1\" הוא מאפיין מוגדר מראש כדי להגדיר רשימת מאפיינים שמשמשים עם מאפיין מסוג [[Special:Types/Record|רשומה]] (record) ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] זמן פענוח בתוך הטקסט", "smw-limitreport-intext-postproctime": "[SMW] ז<PERSON>ן אחרי עיבוד", "smw-limitreport-intext-parsertime-value": "{{PLURAL:$1|שנייה אחת|$1 שניות}}", "smw-limitreport-intext-postproctime-value": "{{PLURAL:$1|שנייה אחת|$1 שניות}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] ז<PERSON>ן עדכון אחסון (בניקוי דף)", "smw-limitreport-pagepurge-storeupdatetime-value": "{{PLURAL:$1|שנייה אחת|$1 שניות}}", "smw_allows_pattern": "הדף הזה אמור להכיל רשימת הפניות (שאחריהן [https://en.wikipedia.org/wiki/Regular_expression ביטויים רגולריים]) שתהיינה זמינות באמצעות המאפיין [[Property:Allows pattern|Allows pattern]]. כדי לערוך את הדף הזה, דרושה ההרשאה <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "\"$1\" מוין בתור בלתי־תקין באמצעות הביטוי הרגולרי \"$2\".", "smw-datavalue-allows-pattern-reference-unknown": "ההפניה לתבנית \"$1\" לא התאימה לרשומה בדף [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "ההפניה לרשימה \"$1\" לא יכולה להיות מותאמת לדף [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "בתוכן הרשימה \"$1\" חסרים פריטים עם סמן הרשימה *.", "smw-datavalue-feature-not-supported": "התכונה \"$1\" אינה נתמכת או שהיא כובתה בוויקי הזה.", "smw-property-predefined-pvap": "\"$1\" הוא מאפיין מוגדר מראש שיכול להגדיר [[MediaWiki:Smw allows pattern|הפניה לתבנית]] כדי להחיל התאמת [https://en.wikipedia.org/wiki/Regular_expression ביטוי רגולרי] והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "\"$1\" הוא מאפיין מוגדר מראש שיכול לשייך כותרת תצוגה ייחודית לישות והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "\"$1\" הוא מאפיין מוגדר מראש שמראה ששיוך ערכים למאפיינים אמור להיות ייחודי והוא מסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] כדי להגביל השמת ערכים לכל מופע למשהו ייחודי (או אחד לכל היותר).", "smw-property-predefined-long-pvuc": "ייחודיות מתקיימת כאשר שני ערכים אינם שווים בייצוג המפורש שלהם. כל הפרה של אילוץ הייחודיות תסווג בתור שגיאה.", "smw-datavalue-constraint-uniqueness-violation": "המא<PERSON>יין \"$1\" מאפשר רק השמת ערכים ייחודיים והערך ''$2'' כבר הוער בנושא \"$3\".", "smw-datavalue-constraint-uniqueness-violation-isknown": "המאפיין \"$1\" מאפשר רק הערות ערך ייחודי, ''$2'' כבר מכיל ערך מושם. \"$3\" מפר את מגבלת הייחודיות.", "smw-datavalue-constraint-violation-non-negative-integer": "על המאפיין \"$1\" יש אילוץ \"מספר שלם לא שלילי\" והערך ''$2'' מפר את הדרישה הזאת.", "smw-datavalue-constraint-violation-must-exists": "על המאפיין \"$1\" יש אילוץ <code>must_exists</code> והערך ''$2'' מפר את הדרישה הזאת.", "smw-datavalue-constraint-violation-single-value": "על המאפיין \"[[Property:$1|$1]]\" יש אילוץ <code>single_value</code> והערך \"$2\" מפר את הדרישה הזאת.", "smw-constraint-violation-uniqueness": "אילוץ <code>unique_value_constraint</code> מושם למאפיין \"[[Property:$1|$1]]\", המאפשר רק השמות ערך ייחודיות והערת הערך ''$2'' כבר נמצאה כמצוינת בנושא \"$3\".", "smw-constraint-violation-uniqueness-isknown": "אילוץ <code>unique_value_constraint</code> מושם למאפיין \"[[Property:$1|$1]]\", ולכן מותרות רק הערות על ערך יחיד. ''$2'' כבר מכיל ערך מוער עם \"$3\", וזה מפר את אילוץ הייחודיות לנושא הנוכחי.", "smw-constraint-violation-non-negative-integer": "אילוץ <code>non_negative_integer</code> מושם למאפיין \"[[Property:$1|$1]]\" והערת הערך ''$2'' מפרה את דרישת האילוץ.", "smw-constraint-violation-must-exists": "אילוץ <code>must_exists</code> מושם למאפיין \"[[Property:$1|$1]]\" והערת הערך ''$2'' מפרה את דרישת האילוץ.", "smw-constraint-violation-single-value": "אילוץ <code>single_value</code> מושם למאפיין \"[[Property:$1|$1]]\" והערת הערך \"$2\" מפרה את דרישת האילוץ.", "smw-constraint-violation-class-shape-constraint-missing-property": "<code>shape_constraint</code> מושם לקטגוריה \"[[:$1]]\" עם מפתח <code>property</code>, והמאפיין הנדרש  $2\" חסר.", "smw-constraint-violation-class-shape-constraint-wrong-type": "<code>shape_constraint</code> מושם לקטגוריה \"[[:$1]]\" עם מפתח <code>property_type</code>, המאפיין \"$2\" לא תואם את הסוג של \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "<code>shape_constraint</code> מושם לקטגוריה \"[[:$1]]\" עם מפתח <code>max_cardinality</code>, המאפיין \"$2\" לא תואם את העוצמה של \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "<code>shape_constraint</code> מושם לקטגוריה \"[[:$1]]\" עם מפתח <code>min_textlength</code>, המאפיין \"$2\" לא תואם את דרישת האורך של \"$3\".", "smw-constraint-violation-class-mandatory-properties-constraint": "אילוץ <code>mandatory_properties</code> מושם לקטגוריה \"[[:$1]]\" ודורש את מאפייני החובה הבאים: $2", "smw-constraint-violation-allowed-namespace-no-match": "אילוץ <code>allowed_namespaces</code> מושם למאפיין \"[[Property:$1| $1]]\" והערך \"$2\" מפר את דרישת מרחב השם, רק מרחבי השם \"$3\" הבאים מותרים.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "אילוץ <code>allowed_namespaces</code> דורש סוג דף.", "smw-constraint-schema-category-invalid-type": "הסכֵמה המוערת \"$1\" אינה תקינה עבור הקטגוריה, היא דורשת את הסוג \"$2\".", "smw-constraint-schema-property-invalid-type": "הסכֵמה המוערת \"$1\" אינה תקינה עבור המאפיין, היא דורשת את הסוג \"$2\".", "smw-constraint-error-allows-value-list": "\"$1\" אינו ברשימה ($2) של  [[Property:Allows value|הערכים האפשריים]] למאפיין \"$3\".", "smw-constraint-error-allows-value-range": "\"$1\" אינו בט<PERSON><PERSON><PERSON> ההוא של \"$2\" שמוגדר על־ידי אילוץ [[Property:Allows value|הערכים האפשריים]] למאפיין \"$3\".", "smw-property-predefined-boo": "\"$1\" הוא [[Special:Types/Boolean|סוג]] ומאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לייצג ערכים בוליאניים.", "smw-property-predefined-num": "\"$1\" הוא [[Special:Types/Number|סוג]] ומאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לייצג ערכים מספריים.", "smw-property-predefined-dat": "\"$1\" הוא [[Special:Types/Date|סוג]] ומאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לייצג ערכי תאריכים.", "smw-property-predefined-uri": "\"$1\" הוא [[Special:Types/URL|סוג]] ומאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לייצג ערכי URI/URL.", "smw-property-predefined-qty": "\"$1\" הוא [[Special:Types/Quantity|סוג]] ומאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לייצג ערכי כמות.", "smw-datavalue-time-invalid-offset-zone-usage": "\"$1\" מכיל מזהה היסט ואזור שאינו נתמך.", "smw-datavalue-time-invalid-values": "הערך \"$1\" מכיל מידע בלתי־אפשרי לפירוש בצורה של \"$2\".", "smw-datavalue-time-invalid-date-components-common": "\"$1\" מכיל מידע בלתי־אפשרי לפירוש.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\" מ<PERSON><PERSON>ל קו מפריד חיצוני או תווים אחרים שאינם תקינים לפירוש תאריך.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" מכיל רכיבית ריקים מסוימים.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" מכיל יותר משלישה רכיבים שדרושים לפענוח תאריך.", "smw-datavalue-time-invalid-date-components-sequence": "\"$1\" מכיל רצף שלא ניתן לפרש אל מול מטריצת התאמה זמינה עבור רכיבי תאריך.", "smw-datavalue-time-invalid-ampm": "\"$1\" מכיל את \"$2\" בתור רכיב שעה שאינו תקין כשמקובל לכתוב לפי 12 שעות.", "smw-datavalue-time-invalid-jd": "לא ניתן לפענח את ערך הקלט \"$1\" בתור מספר JD (יום יוליאני) תקין כשמדווח \"$2\".", "smw-datavalue-time-invalid-prehistoric": "לא ניתן לפענח ערך קלט \"$1\" פרה־היסטורי. למשל, ציון של יותר שנים או של מודל לוח שנה יכול להחזיר תוצאות בלתי־צפויות בהקשר פרה־היסטורי.", "smw-datavalue-time-invalid": "לא ניתן לפענח את ערך הקלט \"$1\" בתור תאריך תקין או בתור רכיב זמן כשמדווח \"$2\".", "smw-datavalue-external-formatter-uri-missing-placeholder": "ב־URI לעיצוב חסר ממלא המקום ''$1''.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" אינה כתובת URL תקינה.", "smw-datavalue-external-identifier-formatter-missing": "במא<PERSON>יין חסרה השמת [[Property:External formatter uri|\"URI חיצוני לעיצוב\"]] (\"External formatter URI\").", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "המזהה החיצוני \"$1\" מצפה להחלפה מרובת שדות אבל בערך \"$2\" הנוכחי חסר לפחות פרמטר ערך אחד שיתאים לדרישה.", "smw-datavalue-keyword-maximum-length": "מילת המפתח חרגה מהאורך המרבי של {{PLURAL:$1|תו אחד|$1 תווים}}.", "smw-property-predefined-eid": "\"$1\" הוא [[Special:Types/Date|סוג]] ומאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לייצג מזהים חיצוניים.", "smw-property-predefined-peid": "\"$1\" הוא מאפיין מוגדר מראש שמגדיר מזהה חיצוני ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pefu": "\"$1\" הוא מאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי להגדיר משאב חיצוני עם ממלא מקום.", "smw-property-predefined-long-pefu": "ה־URI צפוי להכיל ממלא מקום שיותאם עם ערך [[Special:Types/External ID|מזהה חיצוני]] כדי ליצור הפניה תקינה למשאב.", "smw-type-eid": "\"$1\" הוא הגוון של סוג הנתונים [[Special:Types/Text|Text]] לתיאור משאבים חיצוניים (מבוססי URI) והוא דורש מאפיינים מושמים כדי להכריז על [[Property:External formatter uri|External formatter URI]].", "smw-property-predefined-keyw": "\"$1\" הוא מאפיין ו[[Special:Types/Date|סוג]] מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] שמנרמל טקסט ויש לו מגבלה של אורך בתווים.", "smw-type-keyw": "\"$1\" הוא הגוון של סוג הנתונים [[Special:Types/Text|טקסט]] שיש לו אורך מוגבל בתווים עם ייצוג תוכן מנורמל.", "smw-datavalue-stripmarker-parse-error": "הערך הנתון \"$1\" מכיל [https://en.wikipedia.org/wiki/Help:Strip_markers סמני strip] ולכן לא ניתן לפענח אותו באופן מספק.", "smw-datavalue-parse-error": "הערך הנתון \"$1\" לא הובן.", "smw-datavalue-propertylist-invalid-property-key": "רשימת המאפיינים \"$1\" הכילה מפתח מאפיין בלתי־תקין \"$2\".", "smw-datavalue-type-invalid-typeuri": "לא היה אפשר להפוך את הסוג \"$1\" לייצוג URI תקין.", "smw-datavalue-wikipage-missing-fragment-context": "לא ניתן להשתמש בערך הקלט של דף הוויקי \"$1\" ללא דף הקשר.", "smw-datavalue-wikipage-invalid-title": "ערך הקלט של סוג הדף \"$1\" מכיל תווים בלתי־תקינים או שאינו שלם ולכן עלול לגרום לתוצאות בלתי־צפויות במהלך שאילתה או תהליך הערה.", "smw-datavalue-wikipage-property-invalid-title": "המאפיין \"$1\" (כסוג דף) עם ערך הקלט \"$2\" מכיל תווים בלתי־תקינים או שאינו שלם ולכן עלול לגרום לתוצאות בלתי־צפויות במהלך תהליך שאילתה או הערה.", "smw-datavalue-wikipage-empty": "ערך הקלט דף הוויקי ריק (למ<PERSON><PERSON> <code>[[SomeProperty::]], [[]]</code>) ולכן לא ניתן להשתמש בו כשם או כחלק מתנאי שאילתה.", "smw-type-ref-rec": "\"$1\" הוא סוג [https://www.semantic-mediawiki.org/wiki/Container מכל] שמאפשר לרשום מידע נוסף (למשל נתוני מוצא) על השמת ערך.", "smw-datavalue-reference-invalid-fields-definition": "הסוג [[Special:Types/Reference|Reference]] מצפה שרשימת מאפיינים תוכרז באמצעות המאפיין [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Has fields].", "smw-parser-invalid-json-format": "מפענח ה־JSON חזר עם \"$1\".", "smw-property-preferred-label-language-combination-exists": "לא ניתן להשתמש ב תווית המאפיין \"$1  כתווית מועדפת כי בשפה \"$2\" כבר הושמה לתווית \"$3\".", "smw-clipboard-copy-link": "העתקת קישור ללוח", "smw-property-userdefined-fixedtable": "\"$1\" הוגדר כ[https://www.semantic-mediawiki.org/wiki/Fixed_properties מאפיין קבוע] וכל שינוי ב[https://www.semantic-mediawiki.org/wiki/Type_declaration הצהרת  הסוג] שלו מחייב הרצה של <code>setupStore.php</code> או השלמה של המשימה המיוחדת [[Special:SemanticMediaWiki|\"התקנת ושדרוג של מסד נתונים\"]].", "smw-data-lookup": "אחזור נתונים...", "smw-data-lookup-with-wait": "הבקשה בעיבוד ועשויה לקחת רגע.", "smw-no-data-available": "אין מידע זמין.", "smw-property-req-violation-missing-fields": "במא<PERSON><PERSON>ין \"$1\" חסרה הצהרת [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] נדרשת עבור סוג \"$2\".", "smw-property-req-violation-multiple-fields": "המאפיין \"$1\" מכיל הצהרות [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] מרובות (ולכן מתחרות), מצופה רק אחת עבור סוג \"$2\" הזה.", "smw-property-req-violation-missing-formatter-uri": "במ<PERSON><PERSON>יין \"$1\" חסרים פרטי הצהרה עבור הסוג המוער על־ידי כי חסרה בו הגדרת המאפיין <code>External formatter URI</code>.", "smw-property-req-violation-predefined-type": "המא<PERSON>יין \"$1\", בהיותו מאפיין מוגדר מראש, מכיל הצהרת סוג \"$2\" שאינה תואמת את סוג ברירת המחדל של המאפיין הזה.", "smw-property-req-violation-import-type": "זוהתה הצהרת סוג שאינה תואמת לסוג המוגדר מראש של המילון המיובא \"$1\". ב<PERSON><PERSON><PERSON><PERSON> כללי, אין צורך להצהיר על סוג כי המידע נשלף מהגדרת היבוא.", "smw-property-req-violation-change-propagation-locked-error": "המאפיין \"$1\" השתנה והוא דורש הערכה חוזרת של ישויות מושמות באמצעות תהליך [https://www.semantic-mediawiki.org/wiki/Change_propagation הפצת שינויים]. דף המאפיין ננעל עד להשלמת עדכון המפרט הראשי כדי למנוע הפרעות מתווכות או מפרטים סותרים. התהליך עשוי לקחת רגע לפני שיהיה ניתן לבטל את נעילת הדף מכיוון שהוא תלוי בגודל ובתדירות של מתזמן [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue תור המשימות].", "smw-property-req-violation-change-propagation-locked-warning": "המאפיין \"$1\" שונה והוא דורש הערכה חוזרת של ישויות מושמות באמצעות תהליך [https://www.semantic-mediawiki.org/wiki/Change_propagation הפצת שינויים]. התהליך עשוי לקחת רגע לפני שיהיה ניתן לבטל את נעילת הדף מכיוון שהוא תלוי בגודל ובתדירות של מתזמן [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue תור המשימות] ומומלץ להמתין עם שינויים במאפיין עד לסיום התהליך כדי למנוע הפרעות ביניים או מפרטים סותרים.", "smw-property-req-violation-change-propagation-pending": "עדכוני [https://www.semantic-mediawiki.org/wiki/Change_propagation הפצת שינויים] ממתינים ([https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue בערך {{PLURAL:$1|משימה אחת|$1 משימות}}]) ומומלץ להמתין עם שינויים במאפיין עד לסיום התהליך כדי למנוע הפרעות ביניים או מפרטים סותרים.", "smw-property-req-violation-missing-maps-extension": "מדיה־ויקי סמנטית לא הצליחה לזהות את ההרחבה [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] שהיא תנאי מוקדם וכתוצאה מכך מגבילה את הפונקציונליות (כלומר אין אפשרות לאחסן או לעבד נתונים גיאוגרפיים) של המאפיין הזה.", "smw-property-req-violation-type": "המאפיין מכיל מפרטי סוגים מתחרים שעלולים לגרום להערות ערך בלתי־תקינות ולכן יש לצפות שמשתמש ישים סוג אחד מתאים.", "smw-property-req-error-list": "המא<PERSON>יין מכיל את השגיאות או האזהרות הבאות:", "smw-property-req-violation-parent-type": "למא<PERSON>יין \"$1\" ולמא<PERSON>יין ההורה שהוקצה \"$2\" יש הערות מסוג שונה.", "smw-property-req-violation-forced-removal-annotated-type": "האכיפה של [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance ירושת סוג הורה מחויבת] הופעלה, סוג ההערה עבור המאפיין \"$1\" אינו תואם לסוג המאפיין ההורה שלו \"$2\" ושונתה כדי לשקף את הדרישה הזאת. מומלץ להתאים את הגדרת הסוג בתוך דף כך שהודעת השגיאה והאכיפה החובה תוסר עבור המאפיין הזה.", "smw-change-propagation-protection": "הדף הזה נעול כדי למנוע שינוי לא מכוון בנתונים בזמן הפעלה של עדכון [https://www.semantic-mediawiki.org/wiki/Change_propagation הפצת שינויים]. התהליך עשוי להימשך רגע לפני פתיחת הדף כי הוא תלוי בגודל ובתדירות של מתזמן [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue תור המשימות].", "smw-category-change-propagation-locked-error": "הקטגוריה \"$1\" שונתה והיא דורשת הערכה חוזרת של ישויות מושמות באמצעות תהליך [https://www.semantic-mediawiki.org/wiki/Change_propagation הפצת שינויים]. דף המאפיין ננעל עד להשלמת עדכון המפרט הראשי כדי למנוע הפרעות מתווכות או מפרטים סותרים. התהליך עשוי לקחת רגע לפני שיהיה ניתן לבטל את נעילת הדף מכיוון שהוא תלוי בגודל ובתדירות של מתזמן [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue תור המשימות].", "smw-category-change-propagation-locked-warning": "הקטגוריה \"$1\" שונתה והיא דורשת הערכה חוזרת של ישויות מושמות באמצעות תהליך [https://www.semantic-mediawiki.org/wiki/Change_propagation הפצת שינויים]. התהליך עשוי לקחת רגע מכיוון שהוא תלוי בגודל ובתדירות של מתזמן [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue תור המשימות] ומומלץ להמתין עם שינויים בקטגוריה עד לסיום התהליך כדי למנוע הפרעות ביניים או מפרטים סותרים.", "smw-category-change-propagation-pending": "עדכוני [https://www.semantic-mediawiki.org/wiki/Change_propagation הפצת שינויים] ממתינים ([https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue בערך {{PLURAL:$1|משימה אחת|$1 משימות}}]) ומומלץ להמתין עם שינויים בקטגוריה עד לסיום התהליך כדי למנוע הפרעות ביניים או מפרטים סותרים.", "smw-category-invalid-value-assignment": "הערך \"$1\" אינו מזוהה כהערת קטגוריה או ערך תקינה.", "protect-level-smw-pageedit": "לאפשר רק למשתמשים עם הרשאת עריכת דף (מדיה־ויקי סמנטית)", "smw-create-protection": "יצירת המאפיין \"$1\" מוגבלת למשתמשים בעלי ההרשאה (או [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups group קבוצת המשתמשים]) המתאימה \"$2\" כשמופעל [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode מצב סמכות].", "smw-create-protection-exists": "שינויים למאפיין \"$1\" מוגבלים למשתמשים בעלי ההרשאה (או [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups group קבוצת המשתמשים]) המתאימה \"$2\" כשמופעל [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode מצב סמכות].", "smw-edit-protection": "הדף הזה [[Property:Is edit protected|מוגן]] כדי למנוע שינוי מקרי בנתונים ורק משתמשים שמחזיקים בהרשאת העריכה המתאימה (\"$1\") או ששייכים ל[https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups קבוצת המשתמשים] המתאימה יכולים לערוך אותו.", "smw-edit-protection-disabled": "הגנת העריכה הושבתה ולכן לא ניתן להשתמש במאפיין \"$1\" כדי להגן על דפי ישויות מפני עריכה לא מורשית.", "smw-edit-protection-auto-update": "מדיה־ויקי סמנטית עדכנה את מצב ההגנה בהתאם למאפיין \"האם עריכה מוגנת\".", "smw-edit-protection-enabled": "מוגן מפני עריכה (מדיה־ויקי סמנטית)", "smw-patternedit-protection": "הדף הזה מוגן ורק משתמשים שמחזיקים ב[https://www.semantic-mediawiki.org/wiki/Help:Permissions הרשאה] המתאימה <code>smw-patternedit</code> יכולים לערוך אותו.", "smw-property-predefined-edip": "\"$1\" הוא מאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לציין האם העריכה מוגנת או לא.", "smw-property-predefined-long-edip": "בעוד שכל משתמש מוסמך להוסיף את המאפיין הזה לנושא, רק משתמש בעל הרשאה ייעודית יכול לערוך או לבטל את ההגנה לישות לאחר הוספתה.", "smw-query-reference-link-label": "הפניה לשאילתה", "smw-format-datatable-emptytable": "אין נתונים זמינים בטבלה", "smw-format-datatable-info": "מוצגות רשומות מ־_START_ עד _END_ מתוך _TOTAL_", "smw-format-datatable-infoempty": "הצגת 0 עד 0 מתוך 0 ערכים", "smw-format-datatable-infofiltered": "(מסונן מתוך _MAX_ רשומות סה\"כ)", "smw-format-datatable-lengthmenu": "להציג רשומות _MENU_", "smw-format-datatable-loadingrecords": "טוען...", "smw-format-datatable-processing": "מעבד...", "smw-format-datatable-search": "חיפוש:", "smw-format-datatable-zerorecords": "לא נמצאו רשומות תואמות", "smw-format-datatable-first": "רא<PERSON><PERSON>ן", "smw-format-datatable-last": "א<PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-next": "הבא", "smw-format-datatable-previous": "הקודם", "smw-format-datatable-sortascending": ": להפעיל כדי למיין את העמודה בסדר עולה", "smw-format-datatable-sortdescending": ": לה<PERSON>עיל כדי למיין את העמודה בסדר יורד", "smw-format-datatable-toolbar-export": "יצוא", "smw-category-invalid-redirect-target": "הקטגוריה \"$1\" מכילה יעד הפניה בלתי־תקין למרחב שם שאינו קטגוריה.", "smw-parser-function-expensive-execution-limit": "פונקציית המפענח הגיעה למגבלה של ביצועים יקרים (ר' את פרמטר התצורה [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "מדיה־ויקי סמנטית מרעננת את הדף הנוכחי בתנאי של עיבוד מסוים שנדרש לאחר שאילתה.", "apihelp-smwinfo-summary": "מודול API לאחזור מידע על סטטיסטיקות של מדיה־ויקי סמנטית ומטא־מידע אחר.", "apihelp-ask-summary": "מודול API לביצוע שאילתות אל מול מדיה־ויקי סמנטית באמצעות שפת ask.", "apihelp-askargs-summary": "מודול API לביצוע שאילתות אל מול מדיה־ויקי סמנטית באמצעות שפת ask בתור רשימת תנאים, הדפסות, ופרמטרים.", "apihelp-browsebyproperty-summary": "מודול API לאחזור מידע על מאפיין או רשימת מאפיינים.", "apihelp-browsebysubject-summary": "מודול API לאחזור מידע על נושא.", "apihelp-smwtask-summary": "מודול API לביצוע משימות הקשורות למדיה־ויקי סמנטית (לשימוש פנימי בלבד, לא לשימוש ציבורי).", "apihelp-smwbrowse-summary": "מודול API לתמיכה בפעילויות עיון בסוגי ישויות שונים במדיה־ויקי סמנטית.", "apihelp-ask-parameter-api-version": "עיצוב פלט:\n;2:תסדיר תואם לאחור באמצעות {} עבור רשימת התוצאות.\n;3:תסדיר ניסיוני באמצעות [] בתור רשימת תוצאות.", "apihelp-smwtask-param-task": "מגדיר את סוג המשימה", "apihelp-smwtask-param-params": "פרמטרים מקודדים ב־JSON התואמים לדרישת סוג המשימה שנבחרה", "smw-apihelp-smwtask-example-update": "דוגמה להפעלת משימת עדכון עבור נושא מסוים:", "smw-api-invalid-parameters": "פרמטרים בלתי־תקינים, \"$1\".", "smw-parser-recursion-level-exceeded": "חריגה מרמת הרקורסיות של $1 במהלך תהליך פענוח. מומלץ לבדוק את התקינות של מבנה התבנית, או במידת הצורך להתאים את פרמטר התצורה <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "הצגת {{PLURAL:$1|דף אחד שמשתמש|$1 דפים שמשתמשים}} במאפיין הזה.", "smw-property-page-list-search-count": "הצגת {{PLURAL:$1|דף אחד שמשתמש|$1 דפים שמשתמשים}} במאפיין הזה עם התאמת ערך \"$2\".", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter מסנן החיפוש] מאפשר הכללה של [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions ביטויי שאילתה] כגון <code>~</code> או <code>!</code>. [https://www.semantic-mediawiki.org/wiki/Query_engine מנוע השאילתות] שנבחר עשוי לתמוך גם בהתאמה ללא תלות ברישיות או ביטויים קצרים אחרים כגון:\n\n* <code dir=\"ltr\">in:</code> התוצאה חייבת לכלול את המונח, למשל '<code>in:Foo</code>'\n\n* <code dir=\"ltr\">not:</code> התוצאה חייבת לא לכלול את המונח, למשל '<code>in:Bar</code>'", "smw-property-reserved-category": "קטגוריה", "smw-category": "קטגוריה", "smw-datavalue-uri-invalid-scheme": "\"$1\" לא רשום כסכֵמת URI תקינה.", "smw-datavalue-uri-invalid-authority-path-component": "\"$1\" זוהה בתור מכיל סמכות או רכיב נתיב בשם \"$2\" וזה לא תקין.", "smw-browse-property-group-title": "קבוצת מאפיינים", "smw-browse-property-group-label": "תווית קבוצת מאפיינים", "smw-browse-property-group-description": "תיא<PERSON>ר קבוצת מאפיינים", "smw-property-predefined-ppgr": "\"$1\" הוא מאפיין מוגדר מראש שמזהה ישויות (בעיקר קטגוריות) שמשמשות כמופע קיבוץ עבור מאפיינים ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-filter": "מסנן", "smw-section-expand": "הרחבת פרק", "smw-section-collapse": "צמצום פרק", "smw-ask-format-help-link": "תסדיר [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "עזרה", "smw-cheat-sheet": "שליף", "smw-personal-jobqueue-watchlist": "רשימת מעקב של תור עבודה", "smw-personal-jobqueue-watchlist-explain": "המספרים מצביעים על הערכה של רשומות בתור עבודה שממתינות לביצוע.", "smw-property-predefined-label-skey": "מפתח מיון", "smw-processing": "מתבצע עיבוד...", "smw-loading": "בטעינה...", "smw-fetching": "מתקבל...", "smw-preparing": "בהכנה...", "smw-expand": "הרחבה", "smw-collapse": "צמצום", "smw-copy": "העתקה", "smw-copy-clipboard-title": "מעתיק את התוכן ללוח", "smw-jsonview-expand-title": "הרחבת תצוגת ה־JSON", "smw-jsonview-collapse-title": "צמצום תצוגת ה־JSON", "smw-jsonview-search-label": "חיפוש:", "smw-redirect-target-unresolvable": "היעד אינו ניתן למציאת בגלל \"$1\"", "smw-types-title": "סוג: $1", "smw-schema-namespace-editcontentmodel-disallowed": "אסור לשנות את מודל התוכן של [https://www.semantic-mediawiki.org/wiki/Help:Schema דף סכֵמה].", "smw-schema-namespace-edit-protection": "הדף הזה מוגן ורק משתמשים שמחזיקים ב[https://www.semantic-mediawiki.org/wiki/Help:Permissions הרשאה] המתאימה <code>smw-schemaedit</code> יכולים לערוך אותו.", "smw-schema-namespace-edit-protection-by-import-performer": "הדף הזה יובא על־ידי [https://www.semantic-mediawiki.org/wiki/Import_performer מבצע יבוא]. המשמעות היא ששינוי התוכן של הדף הזה מוגבל רק לאותם משתמשים הרשומים.", "smw-schema-error-title": "{{PLURAL:$1|שגיאת|שגיאות}} בדיקת תקינות", "smw-schema-error-schema": "סכֵמת בדיקת התקינות '''$1''' מצאה את המקרים הבאים של חוסר עקביות:", "smw-schema-error-miscellaneous": "שגיאה אחרת ($1)", "smw-schema-error-validation-json-validator-inaccessible": "בודק תקינות ה־JSON \"<b dir=\"ltr\">$1</b>\" אינו נגיש (או אינו מותקן) וזאת הסיבה לכך שלא ניתן לבחון את הקובץ \"$2\", וזה מונע את השמירה או את השינוי של הדף הנוכחי.", "smw-schema-error-validation-file-inaccessible": "קובץ בדיקת התקינות \"$1\" אינו נגיש.", "smw-schema-error-type-missing": "בתוכן חסר סוג שהיה מאפשר לזהות אותו ולהשתמש ב[https://www.semantic-mediawiki.org/wiki/Help:Schema מרחב שם של סכֵמה].", "smw-schema-error-type-unknown": "הסוג \"$1\" אינו רשום ולא ניתן להשתמש בו עבור תוכן במרחב השם [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-error-json": "שגיאת JSON‏: \"$1\"", "smw-schema-error-input": "בדיקת תקינות הקלט מצאה את הבעיות הבאות, יש לטפל בהן לפני שניתן לשמור את התוכן. הדף [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling העזרה על סכֵמה] עשוי לספק עצות כיצד להסיר מקרים של חוסר עקביות או לפתור בעיות עם קלט הסכֵמה.", "smw-schema-error-input-schema": "סכֵמת בדיקת התקינות '''$1''' מצאה את המקרים הבאים של חוסר העקביות ויש לטפל בהם לפני שיהיה אפשר לשמור את התוכן. דף [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling העזרה על סכֵמה] עשוי לספק עצות כיצד לפתור את הבעיות האלו.", "smw-schema-error-title-prefix": "סוג הסכֵמה הזה מחייב שהכותרת של הסכֵמה תתחיל ב־\"$1\".", "smw-schema-validation-error": "הסוג \"$1\" אינו רשום ולא ניתן להשתמש בו עבור תוכן במרחב השם [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-validation-schema-title": "סכֵמת JSON", "smw-schema-summary-title": "תק<PERSON><PERSON>ר", "smw-schema-title": "סכֵמה", "smw-schema-usage": "שימוש", "smw-schema-type": "סוג סכֵמה", "smw-schema-type-description": "תיאור הסוג", "smw-schema-description": "תיאור סכֵמה", "smw-schema-description-link-format-schema": "סוג הסכֵמה הזה תומך בהגדרה של מאפיינים ליצירת קישורים רגישים להקשר בקשר למאפיין שהושמה לו [[Property:Formatter schema|סכֵמת עיצוב]].", "smw-schema-description-search-form-schema": "סוג הסכֵמה הזה תומך בהגדרה של צורות קלט ותכונות עבור פרופיל [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch חיפוש מורחב] שבו הוא מכיל הוראות איך ליצור שדות קלט, להגדיר מרחבי שמות ברירת מחדל, או להצהיר על ביטויי תחילית עבור בקשת חיפוש.", "smw-schema-description-property-profile-schema": "סוג הסכֵמה הזה תומך בהגדרה של פרופיל להכרזה על תכונות למאפיין שהוקצה וערכי ההערות שלו.", "smw-schema-description-facetedsearch-profile-schema": "סוג הסכֵמה הזה תומך בהגדרה של ערכות הגדרות שמשמשות כחלק מסביבת [[Special:FacetedSearch|חיפוש רב־פנים]].", "smw-schema-description-property-group-schema": "סוג הסכֵמה הזה תומך בהגדרה של [https://www.semantic-mediawiki.org/wiki/Help:Property_group קבוצות מאפיינים] כדי לעזור לתת מבנה לממשק ה[https://www.semantic-mediawiki.org/wiki/Help:Special:<PERSON><PERSON><PERSON> עיון].", "smw-schema-description-property-constraint-schema": "זה תומך בהגדרה של כללי אילוץ עבור מופע מאפיין, כמו גם בערכים שמושמים לו.", "smw-schema-description-class-constraint-schema": "סוג הסכֵמה הזה תומך בהגדרה של כללי אילוץ עבור מופע מחלקה (המכונה גם קטגוריה).", "smw-schema-tag": "{{PLURAL:$1|תג|תגים}}", "smw-property-predefined-constraint-schema": "\"$1\" הוא מאפיין מוגדר מראש שמגדיר סכֵמת אילוץ ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית].", "smw-property-predefined-schema-desc": "\"$1\" הוא מאפיין מוגדר מראש שמאחסן תיאור סכֵמה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-def": "\"$1\" הוא מאפיין מוגדר מראש שמאחסן את תוכן הסכֵמה ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-tag": "\"$1\" הוא מאפיין מוגדר מראש שמסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties מדיה־ויקי סמנטית] כדי לזהות אוסף של סכֵמות.", "smw-property-predefined-long-schema-tag": "תווית המזהה סכֵמות של תוכן דומה או תכונות דומות.", "smw-property-predefined-schema-type": "\"$1\" הוא מאפיין מוגדר מראש שמתאר סוג לבידול של קבוצה של סכֵמות ומסופק על־ידי [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-schema-type": "כל [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type סוג] מספק פרשנות משלו לרכיבי תחביר וכללי יישום וניתן לבטא אותו בעזרת [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation סכֵמת בדיקת התקינות].", "smw-ask-title-keyword-type": "חיפוש לפי מילת מפתח", "smw-ask-message-keyword-type": "החיפוש הזה תואם את התנאי <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "לא ניתן להתחבר ליעד החיצוני \"$1\".", "smw-remote-source-disabled": "המקור '''$1''' ביטל את התמיכה בבקשות חיצוניות!", "smw-remote-source-unmatched-id": "המקור '''$1''' אינו תואם לגרסה של ההרחבה \"מדיה־ויקי סמנטית\" שתומכת בבקשות חיצוניות.", "smw-remote-request-note": "התוצאה מאוחזרת מהמקור החיצוני '''$1''', והתוכן המיוצר עלול להכיל מידע שאינו זמין מתוך אתר הוויקי הנוכחי.", "smw-remote-request-note-cached": "התוצאה '''מוטמנת''' מהמקור החיצוני '''$1''', והתוכן המיוצר עלול להכיל מידע שאינו זמין מתוך אתר הוויקי הנוכחי.", "smw-parameter-missing": "הפרמ<PERSON><PERSON> \"$1\" חסר.", "smw-property-tab-usage": "שימוש", "smw-property-tab-profile-schema": "סכ<PERSON><PERSON><PERSON> פרופיל", "smw-property-tab-redirects": "שמות נרדפים", "smw-property-tab-subproperties": "מאפייני־משנה", "smw-property-tab-errors": "השמות לא הולמות", "smw-property-tab-constraint-schema": "סכֵמת אילוצים", "smw-property-tab-constraint-schema-title": "סכֵמת אילוצים מהודרת", "smw-property-tab-specification": "... עוד", "smw-concept-tab-list": "רשימה", "smw-concept-tab-errors": "שגיאות", "smw-ask-tab-result": "תוצאה", "smw-ask-tab-extra": "תוספת", "smw-ask-tab-debug": "ניפ<PERSON>י שגיאות", "smw-ask-tab-code": "קוד", "smw-install-incomplete-tasks-title": "משימות ניהול שלא הושלמו", "smw-install-incomplete-intro": "יש {{PLURAL:$2|משימה לא גמורה או [[Special:PendingTaskList|ממתינה]]|$2 משימות לא גמורות או [[Special:PendingTaskList|ממתינות]]}} עד סיום {{PLURAL:$1|ההתקנה|השדרוג}} של [https://www.semantic-mediawiki.org מדיה־ויקי סמנטית]. מפעיל מערכת או משתמש עם הרשאות מספיקות יכולים להשלים {{PLURAL:$2|אותה|אותן}}. יש לעשות את זה לפני הוספת נתונים חדשים כדי למנוע חוסר עקביות.", "smw-install-incomplete-intro-note": "ההודעה הזאת תיעלם לאחר פתרון כל המשימות הרלוונטיות.", "smw-pendingtasks-intro-empty": "שום משימה לא סווגה כממתינה, לא גמורה או יוצאת דופן בקשר למדיה־ויקי סמנטית.", "smw-pendingtasks-intro": "הדף הזה מספק מידע על משימות שסווגו כממתינות, לא גמורות או יוצאות דופן בקשר למדיה־ויקי סמנטית.", "smw-pendingtasks-setup-no-tasks-intro": "ההתקנה (או <PERSON><PERSON><PERSON>רו<PERSON>) הושלמה, כרגע אין משימות ממתינות או יוצאות דופן.", "smw-pendingtasks-tab-setup": "התקנה", "smw-updateentitycollation-incomplete": "ההגדרה <code dir=\"ltr\">[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> שונתה לאחרונה וזה דורש הרצה של הסקריפט <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> כדי שישויות יעודכנו ויכילו את ערך שדה המיון הנכון.", "smw-updateentitycountmap-incomplete": "השדה <code>smw_countmap</code> נוסף במהדורה אחרונה וזה דורש הרצה של הסקריפט <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> כדי שפונקציות יוכלו לגשת לתוכן של השדה הזה.", "smw-populatehashfield-incomplete": "אכלו<PERSON> השדה <code>smw_hash</code> נפסח במהלך התקנה, חובה להריץ את הסקריפט <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-populate-hash-field": "אכלו<PERSON> השדה <code>smw_hash</code> נפסח במהלך התקנה, חובה להריץ את הסקריפט <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code> נבחר להיות [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore אחסון ברירת המחדל], אך התוסף לא הצליח למצוא שום עדות לכך ש־<code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> הופעל, נא להפעיל את הסקריפט לפי ההוראות.", "smw-elastic-rebuildelasticindex-run-incomplete": "<code>ElasticStore</code> נבחר להיות [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore אחסון ברירת המחדל], אך התוסף לא הצליח למצוא שום עדות לכך ש־<code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> הופעל, נא להפעיל את הסקריפט לפי ההוראות.", "smw-pendingtasks-setup-intro": "{{PLURAL:$1|ההתקנה|השדרוג}} של <b>מדיה־ויקי סמנטית</b> {{PLURAL:$1|סיווג|סיווגה}} את המשימות הבאות כ[https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade לא שלמות] ומצופה ממנהל מערכת (או משתמש בעל הרשאות מספיקות) לפתור את המשימות האלו לפני שמשתמשים ממשיכים ליצור או לשנות תוכן.", "smw-pendingtasks-setup-tasks": "משימות", "smw-filter-count": "ספירת מסננים", "smw-es-replication-check": "בדיק<PERSON> שכפול (Elasticsearch)", "smw-es-replication-error": "בעיית שכפול של Elasticsearch", "smw-es-replication-file-ingest-error": "בעיה בשאיבת קובץ", "smw-es-replication-maintenance-mode": "תחזוקת Elasticsearch", "smw-es-replication-error-missing-id": "ניטור שכפול מצא שהערך \"$1\" (מזהה: $2) חסר משרת Elasticsearch.", "smw-es-replication-error-divergent-date": "ניטור השכפול מצא שעבור הערך \"$1\" (מזהה: $2) <b>הגרסה המשויכת</b> מציגה חוסר־עקביות.", "smw-es-replication-error-divergent-date-short": "המידע הבא על תאריכים שימש להשוואה:", "smw-es-replication-error-divergent-date-detail": "תאריך השינוי המוזכר:\n*Elasticsearch: ב־$1 \n*מסד נתונים: ב־$2", "smw-es-replication-error-divergent-revision": "ניטור שכפול מצא שעבור הערך \"$1\" (מזהה: $2) <b>הגרסה המשויכת</b>מציגה חוסר־עקביות.", "smw-es-replication-error-divergent-revision-short": "נתוני הגרסאות המשויכים הבאים שימשו להשוואה:", "smw-es-replication-error-divergent-revision-detail": "גרסה משויכת מוזכרת:\n*Elasticsearch: ב־$1 \n*מסד נתונים: ב־$2", "smw-es-replication-error-maintenance-mode": "שכפול Elasticsearch מוגבל כעת כי הוא פועל ב[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>מצב תחזוקה</b>], שינויים בישויות ובדפים <b>אינם</b> גלויים מייד ותוצאות השאילתה עשויות להכיל מידע מיושן.", "smw-es-replication-error-no-connection": "ניטור השכפול אינו מסוגל לבצע בדיקות כלשהן כי אינו יכול ליצור חיבור לאשכול Elasticsearch.", "smw-es-replication-error-bad-request-exception": "המטפל בחיבור Elasticsearch זרק חריג בקשה גרועה (\"400 conflict http error\") המצביע על בעיה מתמשכת במהלך בקשות שכפול וחיפוש.", "smw-es-replication-error-other-exception": "המט<PERSON>ל בהתחברות של Elasticsearch זרק חריג: \"$1\".", "smw-es-replication-error-suggestions": "מומלץ לערוך את הדף או לנקות את המטמון שלו כדי להיפטר מחוסר־העקביות. אם הבעיה נמשכת, יש לבדוק את אשכול ה־Elasticsearch עצמו (מקצה [allocator], חריגים, נפח בכונן, וכו').", "smw-es-replication-error-suggestions-maintenance-mode": "מומלץ ליצור קשר עם המפעיל של הוויקי כדי לבדוק האם נעשית עכשיו [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild בנייה מחדש של מפתח] או האם <code>refresh_interval</code> לא הוגדרה לערך ברירת המחדל המצופה.", "smw-es-replication-error-suggestions-no-connection": "מומלץ ליצור קשר עם מפעיל הוויקי ולדווח את בעיית ה\"אין התחברות\".", "smw-es-replication-error-suggestions-exception": "נא לבדוק את היומנים כדי למצוא מידע על המצב של Elasticsearch, המפתחות שלהם, ובעיות אפשריות של הגדרות שגויות.", "smw-es-replication-error-file-ingest-missing-file-attachment": "ניטור השכפול מצא שבדף \"$1\" חסרה הערה של מאפיין [[Property:File attachment|צרופת קובץ]] (File attachment) שמציינת שמעבד קליטת הקובץ לא התחיל או לא סיים.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "נא לוודא שמשימת [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion קליטת קובץ] מתוזמנת ומבוצעת לפני שההערה ומפתח הקובץ נהיים זמינים.", "smw-report": "דו\"ח", "smw-legend": "מקרא", "smw-datavalue-constraint-schema-category-invalid-type": "הסכֵמה המוערת \"$1\" אינה תקינה עבור הקטגוריה, היא דורשת את הסוג \"$2\".", "smw-datavalue-constraint-schema-property-invalid-type": "הסכֵמה המוערת \"$1\" אינה תקינה עבור המאפיין, היא דורשת את הסוג \"$2\".", "smw-entity-examiner-check": "הרצת {{PLURAL:$1|הבוחן|הבוחנים}} ברקע", "smw-entity-examiner-indicator": "חלונית בעיית ישות", "smw-entity-examiner-deferred-check-awaiting-response": "הבוחן \"$1\" ממתין עכשיו לתשובה מהשרת.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "אילוץ", "smw-entity-examiner-associated-revision-mismatch": "גרסה", "smw-entity-examiner-deferred-fake": "מזויף", "smw-entity-examiner-indicator-suggestions": "כחלק מבחינת הישות, {{PLURAL:$1|התגלתה הבעיה הבאה|התגלו הבעיות הבאות}} ומומלץ לסקור {{PLURAL:$1|אותה|אותן}} ונקוט בפעולות מתאימות.", "smw-indicator-constraint-violation": "{{PLURAL:$1|אילוץ|אילוצים}}", "smw-indicator-revision-mismatch": "גרסה", "smw-indicator-revision-mismatch-error": "בדיקת [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner הגרסה המשויכת] מצאה חוסר־התאמה בין הגרסה שכתובה במדיה־ויקי לבין הגרסה שמשויכת במדיה־ויקי סמנטית עבור הישות הזאת.", "smw-indicator-revision-mismatch-comment": "חוסר התאמה בדרך־כלל מצביע על כך שתהליך כלשהו הפסיק פעולת אחסון במדיה־ויקי סמנטית. מומלץ לסקור את יומני השרת ולחפש חריגים וכישלונות אחרים.", "smw-facetedsearch-intro-text": "[https://www.semantic-mediawiki.org/wiki/Faceted_search <b>חיפוש רב־פנים</b>] (Faceted search) של Semantic MediaWiki מספק למשתמשים ממשק פשוט לצמצום מהיר של תוצאות שאילתות מתוך תנאי בעזרת תצוגות מרובות־פנים שנוצרות ממאפיינים וקטגוריות תלויים.", "smw-facetedsearch-intro-tips": "* אפשר להשתמש ב־<code dir=\"ltr\">category:?</code>, ב־<code dir=\"ltr\">property:?</code> , או ב־<code dir=\"ltr\">concept:?</code> כדי למצוא קטגוריות, מאפיינים או רעיונות זמינים לבניית ערכת תנאים\n* אפשר להשתמש בתחביר <span dir=\"ltr\">#ask</span> כדי לתאר תנאי (למשל<nowiki> <code>[[Category:Foo]]</code></nowiki>)\n* אפשר להשתמש ב־\"OR\", ב־\"AND\" או בביטויי שאילתה אחרים כדי ליצור תנאים מורכבים\n* ביטויים כמו <code dir=\"ltr\">in:</code> או <code dir=\"ltr\">phrase:</code> יכולים לשמש עבור התאמות בטקסט מלא או בחיפושים לא מבניים, אם נבחר [https://www.semantic-mediawiki.org/wiki/Query_engine מנוע שאילתות] שתומך בביטויים האלה", "smw-facetedsearch-profile-label-default": "פרופיל בררת מחדל", "smw-facetedsearch-intro-tab-explore": "לח<PERSON><PERSON><PERSON>", "smw-facetedsearch-intro-tab-search": "לחפש", "smw-facetedsearch-explore-intro": "יש לבחור אוסף והתחל לעיין.", "smw-facetedsearch-profile-options": "אפשרויות ערכת הגדרות", "smw-facetedsearch-size-options": "אפשרויות דפדוף", "smw-facetedsearch-order-options": "אפשרויו<PERSON> מיון", "smw-facetedsearch-format-options": "אפשרויות תצוגה", "smw-facetedsearch-format-table": "טבלה", "smw-facetedsearch-input-filter-placeholder": "סינון...", "smw-facetedsearch-no-filters": "אין מסננים.", "smw-facetedsearch-no-filter-range": "אין טווח סינון.", "smw-facetedsearch-no-output": "עבור התסדיר הנבחר \"$1\", לא היה פלט זמין.", "smw-facetedsearch-clear-filters": "ניקוי {{PLURAL:$1|מסנן|מסננים}}", "smw-search-placeholder": "חיפוש...", "smw-listingcontinuesabbrev": "המשך", "smw-showingresults": "{{PLURAL:$1|מוצגת תוצאה <strong>אחת</strong>|מוצגות עד <strong>$1</strong> תוצאות}} החל ממספר <strong>$2</strong>."}