{"@metadata": {"authors": ["Al<PERSON>-Holder", "Ameisenigel", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DaSch", "<PERSON> Schäfchen", "<PERSON>", "DraconicDark", "Druesenfieber", "ElBe", "Ferdinand0101", "FriedhelmW", "Gichi", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Justman10000", "Kghbln", "Killarnee", "Krabina", "MF-Warburg", "<PERSON>", "McDut<PERSON><PERSON>", "Melan<PERSON><PERSON>", "Metalhead64", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "MuratTheTurkish", "Murma174", "<PERSON><PERSON><PERSON><PERSON>", "Nemo bis", "Omnipaedista", "<PERSON>ll", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "TMg", "Talex42", "The Evil IP address", "TheRabbit22", "Tobi 406", "Umher<PERSON>render", "Wnme"]}, "smw-desc": "Ermöglicht es, das Wiki zugänglicher zu gestalten – für Menschen ''und'' Maschinen ([https://www.semantic-mediawiki.org/wiki/Help:User_manual/de Dokumentation])", "smw-error": "<PERSON><PERSON>", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] wurde installiert und aktiviert. Der benötigte [https://www.semantic-mediawiki.org/wiki/Help:Upgrade Aktualisierungsschlüssel] wurde indes noch nicht erzeugt.", "smw-upgrade-release": "Veröffentlichung", "smw-upgrade-progress": "Aktualisierungsfortschritt", "smw-upgrade-progress-explain": "Eine Abschätzung hinsichtlich der Dauer der Aktualisierung ist schwierig. Der Zeitbedarf ist abhäng<PERSON> von der Größe der Datenbank und der Leistungsfähigkeit der Hardware des Servers. Bei größeren Wikis wird mehr Zeit benötigt.\n\nUm nähere Informationen zum Fortschritt bei der Aktualisierung zu erhalten, kann der Serveradministrator kontaktiert werden.", "smw-upgrade-progress-create-tables": "Die Datentabellen und Indizes werden erstellt oder aktualisiert…", "smw-upgrade-progress-post-creation": "Allgemeine Wartungsaufgaben werden ausgeführt…", "smw-upgrade-progress-table-optimization": "Die Datenbanktabellen werden optimiert…", "smw-upgrade-progress-supplement-jobs": "Zusätzliche Wartungsaufgaben werden erstellt…", "smw-upgrade-error-title": "<PERSON><PERSON> – Semantic MediaWiki", "smw-upgrade-error-why-title": "Warum wird diese Fehlermeldung angezeigt?", "smw-upgrade-error-why-explain": "Die interne Struktur der von Semantic MediaWiki genutzten Datenbank hat sich verändert und erfordert Anpassungen, um weiterhin störungsfrei funktionieren zu können. Für die Änderung kann es unter anderem mehrere Gründe geben:\n* ein feststehendes Attribut wurde hinzugefügt, das die Erstellung einer neuen Datenbanktabelle erfordert, bevor wieder auf die Datenbank zugegriffen werden kann\n* eine Softwareanpassung bedingt Änderungen an Tabellen oder Indizes der Datenbank, die eine Schemaaktualisierung bedingen, bevor wieder auf die Datenbank zugegriffen werden kann\n* sonstige Veränderungen an der Datenbank in der die Daten gespeichert, bzw. abgefragt werden", "smw-upgrade-error-how-title": "Wie kann dieser Fehler behoben werden?", "smw-upgrade-error-how-explain-admin": "Ein Administrator oder eine andere Person mit Administratorberechtigung muss entweder das Wartungsskript [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php „update.php“] von MediaWiki oder [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php „setupStore.php“] von Semantic MediaWiki ausführen.", "smw-upgrade-error-how-explain-links": "Weitergehende Informationen sind auf den folgenden Seiten verfügbar:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation Installationsanweisungen]\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting Hilfeseite zu häufigen Problemen]", "smw-extensionload-error-why-title": "Warum wird diese Seite angezeigt?", "smw-extensionload-error-why-explain": "Semantic MediaWiki wurde <b>nicht</b> wie erforderlich mit <code>enableSemantics</code> sondern bspw. mit <code>wfLoadExtension</code> oder auf andere Weise aktiviert.", "smw-extensionload-error-how-title": "Wie kann dieser Fehler behoben werden?", "smw-extensionload-error-how-explain": "Um Semantic MediaWiki zu aktivieren ist es erforderlich <code>enableSemantics</code> zu verwenden. Dies stellt sicher, daß dessen Konfiguration mit dem sogenannten „ExtensionRegistry-Mechanismus“ korrekt geladen wird.\n\nSiehe auch die Hilfeseite zu [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics <code>enableSemantics</code>] für weitere Informationen.", "smw-upgrade-maintenance-title": "Aktualisierungs- und Wartungsarbeiten", "smw-upgrade-maintenance-why-title": "Warum sehe ich diese Seite?", "smw-upgrade-maintenance-note": "Die vom Wiki genutzte Softwareerweiterung [https://www.semantic-mediawiki.org/ „Semantic MediaWiki“] wird momentan mitsamt der zugehörigen Datenbank [https://www.semantic-mediawiki.org/wiki/Help:Upgrade aktualisiert]. Wir bitten daher um Geduld, bis dieser Vorgang abgeschlossen ist und das Wiki wieder zugänglich getan werden kann.", "smw-upgrade-maintenance-explain": "Wir versuchen die Auswirkungen sowie die Nichtverfügbarkeit des Wikis zu minimieren, indem Wartungsaufgaben zeitlich hintangestellt werden. Datenbankbezogene Änderungen müssen indes vollständig ausgeführt werden, um Dateninkonsistenzen zu vermeiden. Damit sind unter anderem folgende Änderungen gemeint: \n* Das Ändern der Tabellenstruktur sowie das Hinzufügen sowie Ändern von Tabellenfeldern,  \n* das Ändern oder Hinzufügen von Tabellenindizes, und\n* sofern aktiviert, das Ausführen von Tabellenoptimierungen.", "smw-semantics-not-enabled": "Moment<PERSON> kann Semantic MediaWiki nicht auf diesem Wiki genutzt werden.", "smw_viewasrdf": "RDF-Feed", "smw_finallistconjunct": " und", "smw-factbox-head": "… weitere Daten zur Seite „$1“", "smw-factbox-facts": "<PERSON>ak<PERSON>", "smw-factbox-facts-help": "<PERSON><PERSON><PERSON> und F<PERSON>ten an, die von Benutzern erstellt wurden.", "smw-factbox-attachments": "<PERSON><PERSON>", "smw-factbox-attachments-value-unknown": "unbekannt", "smw-factbox-attachments-is-local": "<PERSON><PERSON>", "smw-factbox-attachments-help": "Zeigt die auf dieser Seite eingebetteten Dateien an.", "smw-factbox-facts-derived": "Abgeleitete Fakten", "smw-factbox-facts-derived-help": "<PERSON><PERSON><PERSON>, die aufgrund von Regeln oder mithilfe von Folgerungen ermittelt bzw. abgeleitet wurden.", "smw_isspecprop": "Dieses Attribut ist ein Spezialattribut in diesem Wiki.", "smw-concept-cache-header": "Cacheverwendung", "smw-concept-cache-count": "Der [https://www.semantic-mediawiki.org/wiki/Help:Konzeptcaching Konzeptcache] enthält {{PLURAL:$1|'''ein''' Objekt|'''$1''' Objekte}} (Stand: $2).", "smw-concept-no-cache": "<PERSON><PERSON>.", "smw_concept_description": "Beschreibung des Konzepts „$1“", "smw_no_concept_namespace": "Konzepte können nur im Namensraum „Konzept:“ erstellt werden.", "smw_multiple_concepts": "Jede Konzeptseite kann nur eine Konzeptdefinition beinhalten.", "smw_concept_cache_miss": "Das Konzept „$1“ kann im Moment nicht genutzt werden, da es, gemäß der Konfiguration des Wikis, offline zu berechnen ist.\nFalls sich das Problem nicht in angemessener Zeit von selbst erledigt, bitte deinen Websiteadministrator, die Daten dieses Konzepts zu aktualisieren.", "smw_noinvannot": "Inversen Attributen können keine Werte zugewiesen werden.", "version-semantic": "Semantische Erweiterungen", "smw_baduri": "URIs der Form „$1“ sind nicht zulässig.", "smw_printername_count": "Ergebnisse zählen", "smw_printername_csv": "Export (CSV)", "smw_printername_dsv": "Export (DSV)", "smw_printername_debug": "Debug-Abfrage (für Experten)", "smw_printername_embedded": "Einbettung (Seiteninhalt)", "smw_printername_json": "Export (JSON)", "smw_printername_list": "Liste", "smw_printername_plainlist": "Liste (unformatiert)", "smw_printername_ol": "Liste (Aufzählung)", "smw_printername_ul": "Liste (Auflistung)", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON><PERSON> (breit)", "smw_printername_template": "Vorlage", "smw_printername_templatefile": "Vorlagendatei", "smw_printername_rdf": "Export (RDF)", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "Text", "smw-paramdesc-limit": "Legt fest, wie viele Ergebnisse bei der Ausgabe der Abfrageergebnisse maximal angezeigt werden sollen", "smw-paramdesc-offset": "Legt fest, ab dem wievielten Ergebnis mit der Ausgabe der Abfrageergebnisse begonnen werden soll", "smw-paramdesc-headers": "Legt fest, ob Überschriften bzw. Attributbezeichnungen bei der Ausgabe der Abfrageergebnisse verwendet werden sollen", "smw-paramdesc-mainlabel": "Legt fest, welche Überschrift oder Bezeichnung für die Hauptergebnisspalte bei der Ausgabe der Abfrageergebnisse angezeigt werden soll", "smw-paramdesc-link": "Legt fest, ob die Datenwerte der Ergebnisse bei der Ausgabe der Abfrageergebnisse als Link angezeigt werden sollen", "smw-paramdesc-intro": "Legt fest, welcher Text vor der Ausgabe der Abfrageergebnisse angezeigt werden soll", "smw-paramdesc-outro": "Legt fest, welcher Text nach der Ausgabe der Abfrageergebnisse angezeigt werden soll", "smw-paramdesc-default": "Legt fest, welcher Text angezeigt werden soll, sofern keine Abfrageergebnisse vorhanden sind", "smw-paramdesc-sep": "Legt fest, welches Trennzeichen bei der Ausgabe der Abfrageergebnisse genutzt werden soll", "smw-paramdesc-propsep": "Legt fest, welches Trennzeichen zwischen den Attributen der Ergebnisse genutzt werden soll", "smw-paramdesc-valuesep": "Legt fest, welches Trennzeichen zwischen den Attributwerten der Ergebnisse genutzt werden soll", "smw-paramdesc-showsep": "Legt fest, welches Trennzeichen im Kopfbereich der .csv-Datei angezeigt werden soll", "smw-paramdesc-distribution": "Die Anzahl der Vorkommen von Werten zählen und anzeigen, anstatt diese alle anzuzeigen.", "smw-paramdesc-distributionsort": "Die Werteverteilung nach Anzahl der Vorkommen sortieren.", "smw-paramdesc-distributionlimit": "Die Anzahl der Vorkommen von Werten auf bestimmte Werte beschränken.", "smw-paramdesc-aggregation": "Legt fest, auf was sich die Aggregation der Daten beziehen soll", "smw-paramdesc-template": "Legt fest, welche Vorlage zum Anzeigen der Abfrageergebnisse verwendet werden soll", "smw-paramdesc-columns": "Legt fest, wie viele Spalten zur Anzeige der Abfrageergebnisse verwendet werden sollen", "smw-paramdesc-userparam": "Legt fest, welcher Wert jedem Vorlagenaufruf übergeben wird, sofern eine Vorlage genutzt wird", "smw-paramdesc-class": "Legt fest, welche zusätzliche CSS-Klasse genutzt werden soll", "smw-paramdesc-introtemplate": "Legt fest, welche Vorlage vor der Ausgabe von Abfrageergebnissen eingefügt werden soll", "smw-paramdesc-outrotemplate": "Legt fest, welche Vorlage nach der Ausgabe von Abfrageergebnissen eingefügt werden soll", "smw-paramdesc-embedformat": "Legt fest, welches HTML-Tag für die Überschriften bei der Ausgabe der Abfrageergebnisse verwendet werden soll", "smw-paramdesc-embedonly": "Legt fest, ob Überschriften bei der Ausgabe der Abfrageergebnisse verwendet werden sollen", "smw-paramdesc-table-class": "Legt fest, welche zusätzliche CSS-Klasse genutzt werden soll", "smw-paramdesc-table-transpose": "Legt fest, ob der Spalten- und Zeilenkopf bei der Anzeige der Abfrageergebnisse transponiert werden soll", "smw-paramdesc-prefix": "Kontrolliert die Anzeige von Namensräumen in Ausgaben", "smw-paramdesc-rdfsyntax": "Die zu verwendende RDF-Syntax", "smw-paramdesc-csv-sep": "Legt fest, welches Trennzeichen zwischen den Spalten bei der Ausgabe der Abfrageergebnisse genutzt werden soll", "smw-paramdesc-csv-valuesep": "Legt fest, welches Trennzeichen zwischen den Datenwerten bei der Ausgabe der Abfrageergebnisse genutzt werden soll", "smw-paramdesc-csv-merge": "Legt fest, ob die Zeilen- und Spaltenwerte zu einem identischen Seitenidentifikator (erste Spalte) zusammengeführt werden sollen", "smw-paramdesc-csv-bom": "Legt fest, ob eine BOM (Byte-Reihenfolge-Markierung) als Zeichen zur Angabe der Byte-Reihenfolge zum Anfang der ausgegebenen Datei hinzugefügt werden soll", "smw-paramdesc-dsv-separator": "Legt fest, welches Trennzeichen bei der Ausgabe der Abfrageergebnisse genutzt werden soll", "smw-paramdesc-dsv-filename": "Legt fest, welcher Name der Datei bei der Ausgabe der Abfrageergebnisse gegeben werden soll", "smw-paramdesc-filename": "Legt fest, welcher Name für die Ausgabedatei verwendet werden soll", "smw-smwdoc-description": "Zeigt eine Übersicht aller Parameter mitsamt deren Standardwerten einschließlich ihrer Beschreibung, die im Zusammenhang mit dem angegebenen Ergebnisformat genutzt werden können.", "smw-smwdoc-default-no-parameter-list": "Dieses Ergebnisformat bietet keine formatspezifischen Parameter.", "smw-smwdoc-par-format": "Das Ergebnisformat zu dessen Parametern die Dokumentation angezeigt werden soll.", "smw-smwdoc-par-parameters": "Die anzuzeigenden Parameter: „specific“ für die vom jeweiligen Ergebnisformat bereitgestellten, „base“ für die für alle Ergebnisformate verfügbaren und „all“ zur Anzeige aller.", "smw-paramdesc-sort": "Legt fest, nach welchem Attribut sortiert die Ergebnisse bei der Ausgabe der Abfrageergebnisse angezeigt werden sollen", "smw-paramdesc-order": "Legt fest, in welcher Sortierreihenfolge die Ergebnisse bei der Ausgabe der Abfrageergebnisse angezeigt werden sollen", "smw-paramdesc-searchlabel": "Legt fest, welcher Text als Link zur Ausgabe weiterer Abfrageergebnisse angezeigt werden soll", "smw-paramdesc-named_args": "Legt fest, ob Bezeichnungen für die Parameter an die Vorlage bei der Ausgabe der Abfrageergebnisse weitergegeben werden sollen", "smw-paramdesc-template-arguments": "Legt fest, wie die Argumente an die Vorlage übergeben werden sollen", "smw-paramdesc-import-annotation": "Legt fest, ob die zusätzlich annotierten Daten während des Parsens einer Se<PERSON> kopiert werden sollen", "smw-paramdesc-export": "Die Exportoption", "smw-paramdesc-prettyprint": "Legt fest, ob zusätzliche Einzüge und neue Zeilen bei der Ausgabe der Abfrageergebnisse verwendet werden sollen", "smw-paramdesc-json-unescape": "Legt fest, ob maskierte Schrägstriche und aus mehreren Bytes bestehende Unicode-Zeichen bei der Ausgabe der Abfrageergebnisse verwendet werden sollen", "smw-paramdesc-json-type": "Legt fest, welcher Serialisierungstyp bei der Ausgabe der Abfrageergebnisse verwendet werden soll", "smw-paramdesc-source": "Legt fest, welche alternative Datenquelle für die Ermittlung der Abfrageergebnisse genutzt werden soll", "smw-paramdesc-jsonsyntax": "Die zu verwendende JSON-Syntax", "smw-printername-feed": "Feed (RSS/Atom)", "smw-paramdesc-feedtype": "Legt fest, welcher Feedtyp bei der Ausgabe der Abfrageergebnisse verwendet werden soll", "smw-paramdesc-feedtitle": "Legt fest, welcher Text als Titel des Feeds bei der Ausgabe der Abfrageergebnisse verwendet werden soll", "smw-paramdesc-feeddescription": "Legt fest, welcher Text als Beschreibung des Feeds bei der Ausgabe der Abfrageergebnisse verwendet werden soll", "smw-paramdesc-feedpagecontent": "Legt fest, ob der Seiteninhalt zusammen mit dem Feed bei der Ausgabe der Abfrageergebnisse angezeigt werden soll", "smw-label-feed-description": "$2-Feed: $1", "smw-paramdesc-mimetype": "Legt fest, welcher Medientyp (MIME-Typ) für die Ausgabedatei verwendet werden soll", "smw_iq_disabled": "Semantische Abfragen wurden in diesem Wiki deaktiviert.", "smw_iq_moreresults": "… weitere Ergebnisse", "smw_parseerror": "Der eingegebene Wert wurde nicht verstanden.", "smw_decseparator": ",", "smw_kiloseparator": ".", "smw_notitle": "„$1“ kann nicht als Seitenname in diesem Wiki verwendet werden.", "smw_noproperty": "„$1“ kann nicht als Attribut in diesem Wiki verwendet werden.", "smw_wrong_namespace": "Nur Seiten im Namensraum „$1“ sind hier zulässig.", "smw_manytypes": "Dem Attribut wurden mehrere Datentypen zugewiesen.", "smw_emptystring": "<PERSON><PERSON> Zeichenfolgen werden nicht akzeptiert.", "smw_notinenum": "„$1“ gehört nicht zu den [[Property:Allows value|zulässigen Werten]] ($2) für das Attribut „$3“.", "smw-datavalue-constraint-error-allows-value-list": "„$1“ befindet sich nicht in der Liste ($2) [[Property:Allows value|zulässiger Werte]] für das Attribut „$3“.", "smw-datavalue-constraint-error-allows-value-range": "„$1“ liegt nicht innerhalb des zulässigen Bereichs „$2“, der durch die Einschränkung mit „[[Property:Allows value|zulässigen Werten]]“ für das Attribut „$3“ festgelegt wurde.", "smw-constraint-error": "Dateneinschränkungsproblem", "smw-constraint-error-suggestions": "Die aufgelisteten Fehler bei Annotationen müssen geprüft werden. Es ist sicherzustellen, daß alle Datenwerte den Anforderungen an die Dateneinschränkungen genügen.", "smw-constraint-error-limit": "Die Liste wird maximal $1 Fehler bezüglich Dateneinschränkungen enthalten.", "smw_noboolean": "„$1“ ist kein Wahrheitswert (wahr/falsch).", "smw_true_words": "wahr,w,ja,j", "smw_false_words": "falsch,f,nein,n", "smw_nofloat": "„$1“ ist keine Zahl.", "smw_infinite": "Die Zahl „$1“ ist zu lang.", "smw_unitnotallowed": "„$1“ wurde nicht als gültige Maßeinheit für dieses Attribut festgelegt.", "smw_nounitsdeclared": "<PERSON>s wurden keine Maßeinheiten für dieses Attribut angegeben.", "smw_novalues": "<PERSON>s wurden keine Werte angegeben.", "smw_nodatetime": "Das Datum „$1“ wurde nicht verstanden.", "smw_toomanyclosing": "In der Abfrage kommen zu viele „$1“ vor.", "smw_noclosingbrackets": "Ein Vorkommen von „<nowiki>[[</nowiki>“ in der Abfrage wurde nicht durch ein entsprechendes „]]“ abgeschlossen.", "smw_misplacedsymbol": "Das Symbol „$1“ wurde an einer Stelle verwendet, an der es nicht sinnvoll ist.", "smw_unexpectedpart": "Der Teil „$1“ der Abfrage konnte nicht interpretiert werden. Die Ergebnisse entsprechen möglicherweise nicht den Erwartungen.", "smw_emptysubquery": "Eine Unterabfrage enthält eine ungültige Bedingung.", "smw_misplacedsubquery": "Eine Unterabfrage wurde an einer Stelle eingesetzt, an der diese nicht vorkommen darf.", "smw_valuesubquery": "Teilabfragen werden für Werte des Attributs „$1“ nicht unterstützt.", "smw_badqueryatom": "Ein Teil „<nowiki>[[…]]</nowiki>“ der Abfrage konnte nicht interpretiert werden.", "smw_propvalueproblem": "Der Wert des Attributs „$1“ wurde nicht verstanden.", "smw_noqueryfeature": "Einige Abfragebestandteile werden nicht von diesem Wiki unterstützt. Der entsprechende Teil der Abfrage wurde ignoriert ($1).", "smw_noconjunctions": "UND-Verknüpfungen in den Abfragen werden nicht von diesem Wiki unterstützt. Der entsprechende Teil der Abfrage wurde ignoriert ($1).", "smw_nodisjunctions": "Disjunktionen (ODER) in Abfragen werden nicht von diesem Wiki unterstützt. Der entsprechende Teil der Abfrage wurde ignoriert ($1).", "smw_querytoolarge": "Die {{PLURAL:$2|folgende Abfragebedingung konnte|folgenden $2 Abfragebedingungen konnten}} aufgrund der auf diesem Wiki gültigen Beschränkungen bezüglich Größe und Tiefe von Abfragen nicht berücksichtigt werden: <code>$1</code>.", "smw_notemplategiven": "Der Parameter „template“ muss angegeben werden, damit diese Abfrage durchgeführt werden kann.", "smw_db_sparqlqueryproblem": "Das Abfrageergebnis konnte nicht aus der SPARQL-Datenbank abgerufen werden. Dieser Fehler kann vorübergehend sein oder auf einen Fehler Datenbank hinweisen.", "smw_db_sparqlqueryincomplete": "Das Ausführen der Abfrage erwies sich als zu umfangreich und wurde abgebrochen. Einige der möglichen Ergebnisse könnten daher fehlen. <PERSON><PERSON><PERSON> mö<PERSON>, sollte stattdessen eine einfachere Abfrage genutzt werden.", "smw_type_header": "Attribute mit dem Datentyp „$1“", "smw_typearticlecount": "Es {{PLURAL:$1|wird ein Attribut|werden $1 Attribute}} mit diesem Datentyp angezeigt:", "smw_attribute_header": "Seiten mit dem Attribut „$1“", "smw_attributearticlecount": "Es {{PLURAL:$1|wird eine Seiten|werden $1 Seiten}} ange<PERSON><PERSON><PERSON>, die dieses Attribut {{PLURAL:$1|verwendet|verwenden}}:", "smw-propertylist-subproperty-header": "Unterattribute", "smw-propertylist-redirect-header": "Synonyme", "smw-propertylist-error-header": "Seiten mit fehlerhaften Annotationen", "smw-propertylist-count": "Es {{PLURAL:$1|wird ein ähnliches Objekt|werden $1 ähnliche Objekte}} angezeigt.", "smw-propertylist-count-with-restricted-note": "Es {{PLURAL:$1|wird ein ähnliches Objekt|werden $1 ähnliche Objekte}} angezeigt. Weitere sind vorhanden, jedoch ist die Anzeige auf „$2“ beschränkt.", "smw-propertylist-count-more-available": "Es {{PLURAL:$1|wird ein ähnliches Objekt|werden $1 ähnliche Objekte}} angezeigt. Weitere sind vorhanden.", "specialpages-group-smw_group": "Semantic MediaWiki", "specialpages-group-smw_group-maintenance": "Wartung", "specialpages-group-smw_group-properties-concepts-types": "Attribute, Konzepte und Datentypen", "specialpages-group-smw_group-search": "Durchsuchen", "exportrdf": "Seiten als RDF exportieren", "smw_exportrdf_docu": "Hier können Informationen zu einzelnen Seiten im RDF-Format abgerufen werden. Bitte die Namen der gewünschten Seiten <i>zeilenweise</i> angeben.", "smw_exportrdf_recursive": "Exportiere auch alle relevanten Seiten rekursiv. Diese Einstellung kann zu sehr großen Ergebnismengen führen!", "smw_exportrdf_backlinks": "Exportiere auch alle Seiten, die auf zu exportierende Seiten verweisen. Diese Einstellung erzeugt RDF, das leichter durchsucht werden kann.", "smw_exportrdf_lastdate": "Exportiere keine Seiten, die seit dem angegebenen Zeitpunkt nicht mehr verändert wurden.", "smw_exportrdf_submit": "Exportieren", "uriresolver": "URI-Auflöser", "properties": "Attribute", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "In diesem Wiki werden die folgenden Attribute genutzt:", "smw_property_template": "$1 mit Datentyp $2 ($3 {{PLURAL:$3|Vorkommen}})", "smw_propertylackspage": "Dieses Attribut sollte auf seiner Seite mit Datentyp usw. beschrieben werden!", "smw_propertylackstype": "<PERSON><PERSON><PERSON> dieses Attribut wurde kein Datentyp festgelegt ($1 wird daher als Datentyp angenommen).", "smw_propertyhardlyused": "Dieses Attribut wird im Wiki kaum verwendet!", "smw-property-name-invalid": "Das Attribut „$1“ kann nicht verwendet werden (ungültiger Attributname).", "smw-property-name-reserved": "„$1“ ist als reservierter Name gelistet und sollte nicht als Attributname verwendet werden. Die folgende [https://www.semantic-mediawiki.org/wiki/Help:Property_naming Hilfeseite] könnte Informationen darüber enthalten, warum dieser Name reserviert ist.", "smw-sp-property-searchform": "Attribute anzeigen, die Folgendes enthalten:", "smw-sp-property-searchform-inputinfo": "Bei der Eingabe wird zwischen Groß- und Kleinschreibung unterschieden. Bei der Filterung werden nur Attribute angezeigt, die der Bedingung entsprechen.", "smw-special-property-searchform": "Attribute anzeigen, die Folgendes enthalten:", "smw-special-property-searchform-inputinfo": "Bei der Eingabe wird zwischen Groß- und Kleinschreibung unterschieden. Bei der Filterung werden nur Attribute angezeigt, die der Bedingung entsprechen.", "smw-special-property-searchform-options": "Optionen", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Unbestätigte", "smw-special-wantedproperties-filter-unapproved-desc": "In Verbindung mit dem Berechtigungsmodus verwendete Filteroption.", "concepts": "Konzepte", "smw-special-concept-docu": "Ein [https://www.semantic-mediawiki.org/wiki/Help:Konzept Konzept] kann als „dynamische Kategorie“ verstanden werden. Es ist eine Zusammenstellung von Seiten die nicht manuell sondern aufgrund einer vorgegebenen Abfrage von Semantic MediaWiki maschinell erstellt wurde.", "smw-special-concept-header": "Liste der Konzepte", "smw-special-concept-count": "{{PLURAL:$1|Das folgende Konzept ist|Die folgenden Konzepte sind}} vorhanden.", "smw-special-concept-empty": "<PERSON>s wurde kein Konzept gefunden.", "unusedproperties": "Verwaiste Attribute", "smw-unusedproperties-docu": "Diese Seite listet alle [https://www.semantic-mediawiki.org/wiki/Unused_properties verwaisten Attribute] auf. Diese wurden auf einer Seite im Namensraum „Attribut:“ beschrieben, werden indes nicht im Wiki verwendet. Für eine differenzierte Übersicht siehe auch die weiteren Spezialseiten „[[Special:Properties|Attribute]]“ und „[[Special:WantedProperties|Gewünschte Attribute]]“.", "smw-unusedproperty-template": "$1 mit Datentyp $2", "wantedproperties": "Gewünschte Attribute", "smw-wantedproperties-docu": "Diese Seite listet alle [https://www.semantic-mediawiki.org/wiki/Wanted_properties gewünschten Attribute] auf. Diese werden im Wiki verwendet, wurden indes noch nicht auf einer Seite im Namensraum „Attribut:“ beschrieben. Für eine differenzierte Übersicht siehe auch die weiteren Spezialseiten „[[Special:Properties|Attribute]]“ und „[[Special:UnusedProperties|Verwaiste Attribute]]“.", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|Vorkommen}})", "smw-special-wantedproperties-docu": "Diese Seite listet alle [https://www.semantic-mediawiki.org/wiki/Wanted_properties gewünschten Attribute] auf. Diese werden im Wiki verwendet, wurden indes noch nicht auf einer Seite im Namensraum „Attribut:“ beschrieben. Für eine differenzierte Übersicht siehe auch die weiteren Spezialseiten „[[Special:Properties|Attribute]]“ und „[[Special:UnusedProperties|Verwaiste Attribute]]“.", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|Vorkommen}})", "smw_purge": "Neu laden", "smw-purge-update-dependencies": "Die aktuelle Seite wird von Semantic MediaWiki neu geladen, da Annotationen mit veralteten Datenwerten ermittelt wurden. Auf diese Weise werden diese aktualisiert.", "smw-purge-failed": "Die aktuelle Seite konnte von Semantic MediaWiki nicht neu geladen werden.", "types": "Datentypen", "smw_types_docu": "Dies ist eine [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes Liste aller Datentypen], die Attributen zugewiesen werden können. Jeder [https://www.semantic-mediawiki.org/wiki/Help:Datatype Datentyp] beschreibt eindeutige Merkmale zur Datenspeicherung und -anzeige eines Datenwerts für ein Attribut, dem der entsprechende Datentyp zugeordnet wurde.", "smw-special-types-no-such-type": "Der Datentyp „$1“ ist unbekannt oder ungültig.", "smw-statistics": "Datenstatistik", "smw-statistics-cached": "Semantische Statistiken (gecacht)", "smw-statistics-entities-total": "Datenobjekte (insgesamt)", "smw-statistics-entities-total-info": "Die geschätzte Anzahl der Datenobjekte. Dies umfasst Attribute, Konzepte oder andere zulässige Datenobjekte, die eine Kennung erhalten haben.", "smw-statistics-property-instance": "{{PLURAL:$1|Attributwert|Attributwerte}} (gesamt)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Attribut|Attribute}}]] (gesamt)", "smw-statistics-property-total-info": "Die Gesamtzahl vorhandener Attribute.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Attribut|Attribute}} (gesamt)", "smw-statistics-property-used": "{{PLURAL:$1|Attribut|Attribute}} (mit mindestens einem Datenwert)", "smw-statistics-property-page": "{{PLURAL:$1|Attribut|Attribute}} (auf einer Seite beschrieben)", "smw-statistics-property-page-info": "Die Anzahl der Attribute, für die eine zugehörige Seite mitsamt einer Beschreibung erstellt wurde.", "smw-statistics-property-type": "{{PLURAL:$1|Attribut|Attribute}} (einem Datentyp zugewiesen)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Abfrage|Abfragen}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Abfrage|Abfragen}}]] (eingebettet, insgesamt)", "smw-statistics-query-format": "Format „$1“", "smw-statistics-query-size": "Abfragegröße", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Konzept|Konzepte}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Konzept|Konzepte}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Unterobjekt|Unterobjekte}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Unterobjekt|Unterobjekte}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Datentyp|Datentypen}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Attributwert|Attributwerte}} (mit [[Special:ProcessingErrorList|{{PLURAL:$1|fehlerhafter Annotation|fehlerhaften Annotationen}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Attributwert|Attributwerte}} (fehlerhafte {{PLURAL:$1|Annotation|Annotationen}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Veraltetes Datenobjekt|Veraltete Datenobjekte}}]", "smw-statistics-delete-count-info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die zum Entfernen markiert wurden, sollten regelmäßig mithilfe bereitgestellter Wartungsskripte bereinigt werden.", "smw_uri_doc": "Der URI-Resolver implementiert den [ $1 W3C TAG-Befund auf httpRange-14].\n<PERSON><PERSON> stellt sicher, dass je nach Anfrage eine RDF-Darstellung (für Maschinen) oder eine Wiki-Seite (für Menschen) ausgeliefert wird.", "ask": "Semantische Suche", "smw-ask-help": "Dieser Abschnitt enthält einige Links, die erklären, wie man die <code>#ask</code>-Syntax verwendet.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Seiten auswählen] beschre<PERSON><PERSON>, wie man Seiten auswählt und Abfragebedingungen erstellt\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Suchoperatoren] listet die verfügbaren Suchoperatoren auf, einschließlich derjenigen für Bereichs- und Platzhalterabfragen\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Informationen anzeigen] beschreibt das Nutzen von Ausgabeanweisungen und Formatierungsoptionen", "smw_ask_sortby": "Sortiere nach Spalte (optional)", "smw_ask_ascorder": "Aufsteigend", "smw_ask_descorder": "Absteigend", "smw-ask-order-rand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Ergebnisse suchen", "smw_ask_editquery": "Abfrage bearbeiten", "smw_add_sortcondition": "[Sortieranweisung hinzufügen]", "smw-ask-sort-add-action": "Sortierbedingung hinzufügen", "smw_ask_hidequery": "Code der Abfrage ausblenden (kompakte Ansicht)", "smw_ask_help": "<PERSON><PERSON><PERSON> zu A<PERSON>fragen", "smw_ask_queryhead": "Abfrageanweisungen (Bedingungen)", "smw_ask_printhead": "Ausgabeanweisungen (Anzeige)", "smw_ask_printdesc": "(ein Attribut je Zeile angeben)", "smw_ask_format_as": "Formatiert als:", "smw_ask_defaultformat": "Standard", "smw_ask_otheroptions": "Weitere Optionen", "smw-ask-otheroptions-info": "Dieser Abschnitt enthält Optionen, mit denen die Ergebnisausgabe gesteuert werden kann. Die Beschreibungen der Ausgabeparameter werden angezeigt, wenn man die Maus über deren Namen führt.", "smw-ask-otheroptions-collapsed-info": "Bitte auf das kleine Plus-Symbol klicken, um sich die weiteren verfügbaren Optionen anzeigen zu lassen.", "smw_ask_show_embed": "Code der Abfrage anzeigen", "smw_ask_hide_embed": "Code der Abfrage verstecken", "smw_ask_embed_instr": "Zum Einbetten dieser Abfrage in eine Wikiseite kann der unten angegebene Code verwendet werden:", "smw-ask-delete": "Entfernen", "smw-ask-sorting": "Sortierung", "smw-ask-options": "Optionen", "smw-ask-options-sort": "Sortierbedingungen", "smw-ask-format-options": "Ausgabeformate und Optionen", "smw-ask-parameters": "Parameter", "smw-ask-search": "<PERSON><PERSON>", "smw-ask-debug": "Fehleranalyse", "smw-ask-debug-desc": "<PERSON><PERSON><PERSON><PERSON> zur Fehleranalyse von Abfragen", "smw-ask-no-cache": "Abfragecache löschen", "smw-ask-no-cache-desc": "Ergebnisse ohne Verwendung des Abfragecaches anzeigen", "smw-ask-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-empty": "Alle Einträge löschen", "smw-ask-download-link-desc": "Ermittelte Ergebnisse im Format $1 herunterladen", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Hilfe zum Ausgabeformat: $1", "smw-ask-condition-change-info": "Die Abfragebedingung wurden geändert. Es ist daher eine erneute Ausführung der Abfrage notwendig, um die den neuen Abfragebedingungen entsprechenden Ergebnisse zu ermitteln.", "smw-ask-input-assistance": "Eingabeunterstützung", "smw-ask-condition-input-assistance": "Es wird eine [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Eingabeunterstützung] in den Feldern für die Ausgabeanweisungen, Sortieranweisungen und Abfragebedingungen angeboten. Das Feld für Abfragebedingungen erfordert die Nutzung eines der folgenden Präfixe:", "smw-ask-condition-input-assistance-property": "<code>p:</code> zur Aktivierung einer Attributsuche (z.&nbsp;B. <code>[[p:<PERSON> …</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> zur Aktivierung einer Kategoriesuche", "smw-ask-condition-input-assistance-concept": "<code>con:</code> zur Aktivierung einer Konzeptsuche", "smw-ask-format-change-info": "Das Ausgabeformat wurde geändert. Es ist daher eine erneute Ausführung der Abfrage notwendig, um die den neuen Darstellungsoptionen entsprechenden Ergebnisse anzeigen zu können.", "smw-ask-format-export-info": "Das ausgewählte Ergebnisformat dient dem Datenexport und verfügt über keine visuelle Darstellung. Die Ergebnisse werden nur zum Herunterladen bereitgestellt.", "smw-ask-query-search-info": "Die Abfrage <code><nowiki>$1</nowiki></code> wur<PERSON> von der Datenbank {{PLURAL:$3|1=„$2“ (aus dem Cache)|„$2“ (aus dem Cache)|„$2“}} in {{PLURAL:$4|einer Sekunde|$4 Sekunden}} beantwortet.", "smw-ask-extra-query-log": "Abfrage-Logbuch", "smw-ask-extra-other": "<PERSON><PERSON><PERSON>", "searchbyproperty": "Suche mittels Attribut", "processingerrorlist": "Liste der Verarbeitungsfehler", "constrainterrorlist": "Liste der Dateneinschränkungsfehler", "propertylabelsimilarity": "Bericht zur Ähnlichkeit von Attributbezeichungen", "missingredirectannotations": "Fehlende Weiterleitungsannotationen", "smw-processingerrorlist-intro": "Die folgende Liste bietet eine Übersicht über die [https://www.semantic-mediawiki.org/wiki/Processing_errors Verarbeitungsfehler], die beim <PERSON> von [https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki/de Semantic MediaWiki] aufgetreten sind. Es wird empfohlen, diese Liste regelmäßig einzusehen und ungültige Attribut-Datenwert-Zuweisungen (Annotationen) zu korrigieren.", "smw-constrainterrorlist-intro": "Die folgende Liste enthält eine Übersicht über die [https://www.semantic-mediawiki.org/wiki/Help:Constraint_errors Dateneinschränkungsfehler], die beim <PERSON>utzen von [https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki/de Semantic MediaWiki] aufgetreten sind. Es wird empfohlen, diese Liste regelmäßig einzusehen und ungültige Attribut-Datenwert-Zuweisungen (Annotationen) zu korrigieren.", "smw-missingredirects-intro": "Diese Seite listet Seiten, bei denen die Annotationen der [https://www.semantic-mediawiki.org/wiki/Redirects Weiterleitungen] fehlen. Die Annotationen können erstellt werden, indem die betreffenden Seiten einzeln [https://www.semantic-mediawiki.org/wiki/Help:Purge neu geladen] werden, oder indem das Wartungsskript „rebuildData.php“ mit der Option <code>--redirects</code> ausgeführt wird.", "smw-missingredirects-list": "Seiten mit fehlenden Annotationen", "smw-missingredirects-list-intro": "Es ist {{PLURAL:$1|eine Seite|sind $1 Seiten}} mit fehlenden Weiterleitungsannotationen vorhanden:", "smw-missingredirects-noresult": "Es wurden keine fehlenden Weiterleitungsannotationen gefunden.", "smw_sbv_docu": "Diese Spezialseite ermittelt alle Seiten, die einen bestimmten Wert für das angegebene Attribut haben.", "smw_sbv_novalue": "Bitte den gewünschten Wert eingeben oder alle Werte für das Attribut $1 ansehen.", "smw_sbv_displayresultfuzzy": "Eine Liste aller Seiten, die das Attribut „$1“ mit dem Wert „$2“ haben.\nWeil nur wenige Ergebnisse gefunden wurden, werden auch ähnliche Werte aufgelistet.", "smw_sbv_property": "Attribut:", "smw_sbv_value": "Wert:", "smw_sbv_submit": "Ergebnisse suchen", "browse": "Semantisches Browsen", "smw_browselink": "Attribute anzeigen", "smw_browse_article": "Bitte den Namen einer Se<PERSON> angeben, um mit dem Browsen zu beginnen.", "smw_browse_go": "Los", "smw_browse_show_incoming": "Attribute anzeigen, die hierhin verlinken", "smw_browse_hide_incoming": "Attribute ausblenden, die hierhin verlinken", "smw_browse_no_outgoing": "Diese Seite enthält keine Attribute.", "smw_browse_no_incoming": "Keine Attribute verlinken auf diese Seite.", "smw-browse-from-backend": "Die Informationen werden momentan aus der Datenbank abgerufen.", "smw-browse-intro": "<PERSON><PERSON> dieser Spezialseite können die auf Seiten oder mit Subobjekten gespeicherten Daten und Informationen eingesehen werden. Bitte gib den Namen einer Seite ein, die eingesehen werden soll.", "smw-browse-invalid-subject": "Die Validierung der Seite ergab den Fehler „$1“.", "smw-browse-api-subject-serialization-invalid": "Die Seite hat ein ungültiges Serialisierungsformat.", "smw-browse-js-disabled": "Womöglich ist JavaScript deaktiviert oder nicht verfügbar. Es sollte ein Browser verwendet werden, der JavaScript unterstützt. Weitere Möglichkeiten sind auf der Hilfeseite zu [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi Konfigurationsparameter <code>$smwgBrowseByApi</code>] angegeben.", "smw-browse-show-group": "Attributgruppen anzeigen", "smw-browse-hide-group": "Attributgruppen ausblenden", "smw-noscript": "Diese Seite oder Aktion benötigt JavaScript, um zu funktionieren. Bitte aktiviere JavaScript in deinem Browser oder verwende einen Browser, der dies unterstützt, damit die angeforderten Funktionen ausgeführt werden können. Weitere Informationen findest du auf der [https://www.semantic-mediawiki.org/wiki/Help:Noscript Hilfeseite zu „noscript“].", "smw_inverse_label_default": "$1 von", "smw_inverse_label_property": "Bezeichnung des inversen Attributs", "pageproperty": "Attributwerte einer Seite", "pendingtasklist": "Liste ausstehender Wartungsaufgaben", "facetedsearch": "Facettensuche", "smw_pp_docu": "Entweder eine Seite und ein Attribut angeben oder nur ein Attribut, um alle zugewiesenen Werte zu erhalten.", "smw_pp_from": "<PERSON>:", "smw_pp_type": "Attribut:", "smw_pp_submit": "Ergebnisse anzeigen", "smw-prev": "vorherige {{PLURAL:$1|$1}}", "smw-next": "nächste {{PLURAL:$1|$1}}", "smw_result_prev": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_next": "Nächste", "smw_result_results": "Ergebnisse", "smw_result_noresults": "<PERSON>s wurden keine Ergebnisse gefunden.", "smwadmin": "Verwaltung (Semantic MediaWiki)", "smw-admin-statistics-job-title": "Statistiken zu Aufträgen", "smw-admin-statistics-job-docu": "Die Statistik zu Aufträgen zeigt Informationen zu noch nicht ausgeführten Aufträgen von Semantic MediaWiki an. Die Anzahl der Aufträge kann geringfügig ungenau sein oder Fehlversuche enthalten. Weitere Informationen sind in der [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Dokumentation] zu finden.", "smw-admin-statistics-querycache-title": "Abfragencache", "smw-admin-statistics-querycache-disabled": "<PERSON> [https://www.semantic-mediawiki.org/wiki/QueryCache Cachen von Abfragen] wurde für dieses Wiki nicht aktiviert. Daher sind hierzu keine Statistiken verfügbar.", "smw-admin-statistics-querycache-legend": "Die Statistiken zum Abfragencache enthalten veränderliche wie auch abgeleitete Daten, darun<PERSON>:\n* <PERSON><PERSON><PERSON><PERSON> („misses“) – Die Zahl der direkten Abrufe von Abfrageergebnissen aus der Datenbank, da diese nicht im Cache verfügbar waren.\n* Löschungen („deletes“) – Die Zahl der Löschungen des Caches, entweder durch den Nutzer oder durch entsprechende Mechanismen der Software.\n* Treffer („hits“) – Die Zahl der Abrufe von Abfrageergebnissen aus dem Cache, da diese dort verfügbar waren, aufgeteilt nach auf Seiten eingebetteten Abfragen („embedded“) und nicht eingebetteten Abfragen („nonEmbedded“) auf der Spezialseite „Semantische Suche“ oder über die API.\n* Mittlere Antwortzeit für Abfrageergebnisse („medianRetrievalResponseTime“) – Die mittlere Antwortzeit für gecachte und nicht gecachte Abrufe von Abfrageergebnissen in Sekunden während des Erhebungszeitraums der Statistiken zum Abfragencache.\n* <PERSON><PERSON> („noCache“) – Die Zahl der Abrufe von Abfrageergebnissen aus der Datenbank aufgrund entsprechender Vorgaben, d. h. durch die Nutzung der Abfrageoptionen <code>limit=0</code> oder <code>no-cache</code>.", "smw-admin-statistics-section-explain": "Dieser Abschnitt stellt Administratoren zusätzliche Statistiken bereit.", "smw-admin-statistics-semanticdata-overview": "Übersicht", "smw-admin-permission-missing": "Der Zugriff auf diese Seite wurde aufgrund fehlender Berechtigungen verwehrt. Bitte lies die Hilfeseite zu den [https://www.semantic-mediawiki.org/wiki/Help:Permissions „Berechtigungen“], um Informationen bezüglich der hierfür notwendigen Einstellungen zu erhalten.", "smw-admin-setupsuccess": "Die Datenbank wurde eingerichtet.", "smw_smwadmin_return": "Zurück zur Seite $1.", "smw_smwadmin_updatestarted": "Ein Aktualisierungsprozess zur Erneuerung der semantischen Daten wurde gestartet.\nAlle gespeicherten Daten werden, sofern notwen<PERSON>, entweder neu erstellt oder repariert.\nDer Fortschritt der Aktualisierung kann auf dieser Spezialseite eingesehen werden.", "smw_smwadmin_updatenotstarted": "Es läuft bereits ein Aktualisierungsprozess.\nEs wird kein neuer begonnen.", "smw_smwadmin_updatestopped": "Alle laufenden Aktualisierungsprozesse wurden vorzeitig beendet.", "smw_smwadmin_updatenotstopped": "Um den laufenden Aktualisierungsprozess vorzeitig zu beenden, muss das Kontrollkästchen markiert werden, um dadurch anzuzeigen, dass man sich diesbezüglich tatsächlich sicher ist.", "smw-admin-docu": "Diese Spezialseite bietet während Installation, Aktualisierung, Wartung und Nutzung sowie bei weiteren administrativen Funktionen und Aufgaben von <a href=\"https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_–_Startseite\">Semantic MediaWiki</a> Unterstützung. Ebenso werden Statistiken bereitgestellt. Wichtige Daten sollten vor der Ausführung administrativer Funktionen gesichert werden.", "smw-admin-environment": "Softwareumgebung", "smw-admin-db": "Datenbankeinrichtung", "smw-admin-db-preparation": "Die Initialisierung der Datenbanktabellen wird gerade durchgeführt. Es kann etwas dauern, bis die Ergebnisse angezeigt werden. Der Zeitbedarf ist abhängig von der Größe und ggf. dem Optimierungsbedarf der Datenbanktabellen.", "smw-admin-dbdocu": "Semantic MediaWiki benötigt weitere Datentabellen in der von MediaWiki genutzten Datenbank, um die semantischen Daten speichern zu können. Die folgende Sonderaufgabe gewährleistet, dass die Datenbank richtig eingerichtet wird. Die Datenbankänderungen, die in diesem Schritt durchgeführt werden, beeinträchtigen den übrigen Teil der von MediaWiki verwendeten Datenbank nicht. Diese könne<PERSON>, sofern dies gewünscht ist, leicht zurückgesetzt werden.\nDiese Sonderaufgabe zur Einrichtung der Datentabellen kann mehrere Male ausgeführt werden, ohne Schaden zu verursachen. Indes ist es lediglich notwendig, diese einmal während der Installation oder der Aktualisierung von Semantic MediaWiki durchzuführen.", "smw-admin-permissionswarn": "So<PERSON>n diese Aktion mit einem SQL-<PERSON><PERSON> abbricht, k<PERSON><PERSON><PERSON> es sein, dass der Datenbankbenutzer, mit dem das Wiki auf die Datenbank zugreift (siehe die Datei „LocalSettings.php“), über keine ausreichenden Rechte verfügt.\nUm das Problem zu beheben, ist es notwendig, entweder dem Datenbankbenutzer zusätzliche Rechte zur Erstellung und Löschung von Datenbanktabellen einzuräumen, den Datenbankadministrator zeitweilig in die Datei „LocalSettings.php“ einzutragen oder aber das Wartungsskript „setupStore.php“ zu nutzen, das von einem Serveradministrator mit Administrationsberechtigung ausgeführt werden kann.", "smw-admin-dbbutton": "Tabellen initialisieren oder aktualisieren", "smw-admin-announce": "Wiki bekannt geben", "smw-admin-announce-text": "<PERSON><PERSON><PERSON> dieses Wiki öffentlich zugänglich ist, kann es auf <a href=\"https://wikiapiary.com\">WikiApiary</a>, dem Wiki zum Tracken von Wikis, registriert werden.", "smw-admin-deprecation-notice-title": "Änderungshinweise zu Konfigurationsparametern", "smw-admin-deprecation-notice-docu": "In diesem Abschnitt werden veraltete oder gelöschte Konfigurationsparameter angezeigt, von denen festgestellt wurde, dass diese in diesem Wiki noch verwendet werden. Die angegebenen Konfigurationsparameter werden mit künftigen Softwareversionen entfallen.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ist veraltet und wird mit Version $2 entfernt.", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> – Die  {{PLURAL:$2|folgende Option wird|folgenden Optionen werden}} entfernt oder ersetzt:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> ist veraltet und wird mit Version $2 entfernt.", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> wird durch <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code> ersetzt.", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> wurde durch <code>$2</code> ersetzt", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> – Die {{PLURAL:$2|folgende Option wird|folgenden Optionen werden}} ersetzt:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> wird ersetzt durch <code>$2</code>.", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> wurde mit Version $2 entfernt.", "smw-admin-deprecation-notice-title-notice": "Zukünftige Änderungen an Konfigurationsparametern", "smw-admin-deprecation-notice-title-notice-explanation": "Die im folgenden Abschnitt angegebenen Konfigurationsparameter werden in der Konfigurationsdatei des Wikis verwendet, indes mit künftigen Softwareveröffentlichungen entweder verändert oder entfernt.", "smw-admin-deprecation-notice-title-replacement": "Ersetzte oder umbenannte Konfigurationsparameter", "smw-admin-deprecation-notice-title-replacement-explanation": "Die im folgenden Abschnitt angegebenen Konfigurationsparameter wurden entweder umbenannt oder anderweitig verändert. Diese sollten umgehend in der Konfigurationsdatei des Wikis angepasst werden.", "smw-admin-deprecation-notice-title-removal": "Entfallene Konfigurationsparameter", "smw-admin-deprecation-notice-title-removal-explanation": "Die im folgenden Abschnitt angegebenen Konfigurationsparameter wurden entfernt. Diese sollten umgehend aus der Konfigurationsdatei des Wikis entfernt werden.", "smw-admin-deprecation-notice-section-legend": "<PERSON><PERSON>", "smw-smwadmin-refresh-title": "Datenreparatur und -aktualisierung", "smw_smwadmin_datarefresh": "Daten neu erstellen", "smw_smwadmin_datarefreshdocu": "Es ist möglich alle Datenbanken von Semantic MediaWiki auf Basis des aktuellen Inhalts des Wikis wiederherzustellen.\nDies kann hilfreich sein, um eine fehlerhafte Datenbank zu erneuern oder um Daten zu aktualisieren, sofern bspw. aufgrund einer Softwareaktualisierung die Datenbankstruktur geändert wurde.\nDie Datenaktualisierung wird für jede Seite ausgeführt und daher einige Zeit in Anspruch nehmen.\nNachfolgend wird angezeigt, ob eine momentan Aktualisierung läuft bzw. ermöglicht es eine Aktualisierung zu starten oder zu stoppen (sofern diese Funktion nicht vom Websiteadministrator deaktiviert wurde).", "smw_smwadmin_datarefreshprogress": "<strong>Eine Aktualisierung wird bereits durchgeführt.</strong>\nEine Aktualisierung geht normalerweise nur langsam voran, da die Daten lediglich in kleinen Schritten erneuert werden, und zwar jedes Mal, wenn ein Benutzer auf das Wiki zugreift.\nUm diese Aktualisierung schneller zu Ende zu führen, kann man das MediaWiki-Wartungsskript „runJobs.php“ einsetzen. Mit dem Parameter <code>--maxjobs 1000</code> beschränkt man dabei die Anzahl der Aktualisierungsschritte, die in einem Durchgang durchgeführt werden, um Probleme bei der Nutzung des hierfür erforderlichen Arbeitsspeichers zu vermeiden.\nGeschätzter Fortschritt der laufenden Aktualisierung:", "smw_smwadmin_datarefreshbutton": "Datenaktualisierung planen", "smw_smwadmin_datarefreshstop": "Diese Datenaktualisierung beenden", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, ich bin mir {{GENDER:$1|sicher}}.", "smw-admin-job-scheduler-note": "Die meisten in diesem Abschnitt angegebenen Aktivitäten werden als Hintergrundauftrag ausgeführt, um Probleme während deren Ausführung zu vermeiden. Die [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Auftragswarteschlange] ist für deren Verarbeitung verantwortlich und es ist wichtig, dass das hierfür genutzte Wartungsskript „runJobs.php“ (siehe auch Konfigurationsparameter <code>$wgRunJobsAsync</code>) angemessen eingerichtet wird.", "smw-admin-outdateddisposal-title": "Veraltete Objekte bereinigen", "smw-admin-outdateddisposal-intro": "Einige Aktivitäten, wie bspw. das Ändern des Datentyps eines Attributs, das Löschen von Seiten oder das Korrigieren fehlerhafter Datenwerte, führen zu [https://www.semantic-mediawiki.org/wiki/Outdated_entities veralteten Datenobjekten]. Es ist empfehlenswert, diese periodisch in der Datenbank zu bereinigen. Es kann etwas dauern, bis der Auftrag zur Bereinigung ausgeführt und abgeschlossen ist. Dies ist davon abhängig, in welchem zeitlichen Intervall Aufträge ausgeführt werden.", "smw-admin-outdateddisposal-active": "Ein Auftrag zur Bereinigung veralteter Objekte wurde erzeugt und eingeplant.", "smw-admin-outdateddisposal-button": "Auftrag zur Bereinigung erzeugen", "smw-admin-feature-disabled": "Diese Funktion wurde auf diesem Wiki deaktiviert. Hierzu können Informationen auf der <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">entsprechenden Hilfeseite</a> eingesehen werden.", "smw-admin-propertystatistics-title": "Statistiken zu Attributen neu erstellen", "smw-admin-propertystatistics-intro": "Erstellt die gesamten Statistiken zu Attributen neu und aktualisiert sowie korrigiert die [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Anzahl zugeordneter Attributwerte] auf den Seiten der Attribute.", "smw-admin-propertystatistics-active": "Ein Auftrag zur Neuerstellung der Attributstatistik wurde erzeugt und eingeplant.", "smw-admin-propertystatistics-button": "Auftrag zur Neuerstellung der Attributstatistiken erzeugen", "smw-admin-fulltext-title": "Index der Volltextsuche neu erstellen", "smw-admin-fulltext-intro": "Erstellt den Suchindex der Attributtabellen mit einem für die [https://www.semantic-mediawiki.org/wiki/Full-text Volltextsuche] vorgesehenen Datentyp neu. Änderungen an den Indexregeln wie bspw. geänderte Stoppwörter, neuer Stemmer usw., und/oder das Hinzufügen oder Ändern einer Tabelle erfordern das erneute Ausführen dieser Spezialaufgabe.", "smw-admin-fulltext-active": "Ein Auftrag zur Neuerstellung des Suchindexes für die Volltextsuche wurde erzeugt und eingeplant.", "smw-admin-fulltext-button": "Auftrag zur Neuerstellung des Suchindexes erzeugen", "smw-admin-support": "Unterstützung erhalten", "smw-admin-supportdocu": "Verschiedene Hilfen und Hinweise wurden erstellt, um im Fall eines Problems zu helfen:", "smw-admin-installfile": "<PERSON><PERSON><PERSON> beim Installieren Probleme auftreten, könnten die in der Datei <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">„INSTALL“</a> und die auf der Seite mit der <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">Installationsdokumentation</a> enthaltenen Informationen weiterhelfen.", "smw-admin-smwhomepage": "Die Dokumentation für die Nutzer von Semantic MediaWiki ist auf <b><a href=\"https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_%E2%80%93_Startseite\">semantic-mediawiki.org</a></b> verfügbar.", "smw-admin-bugsreport": "Softwarefehler können auf <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a> gemeldet werden. Die Dokumentation zum <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\"><PERSON>den von Softwarefehlern</a> bietet eine Anleitung zum Verfassen eines effektiven Fehlerberichts.", "smw-admin-questions": "<PERSON><PERSON> <PERSON> von Fragen oder Anregungen kann man sich an den Diskussionen auf der <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">Mailingliste für Nutzer</a> beteiligen.", "smw-admin-other-functions": "<PERSON><PERSON>e Statistiken", "smw-admin-statistics-extra": "Statistiken", "smw-admin-statistics": "Statistiken", "smw-admin-supplementary-section-title": "Zusätzliche Funktionen", "smw-admin-supplementary-section-subtitle": "Allgemeine Funktionen", "smw-admin-supplementary-section-intro": "In diesem Abschnitt werden weitere Funktionen zusätzlich zu denen der Wartung bereitgestellt. Es ist möglich, dass einige in der [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions Dokumentation] gelisteten Funktionen entweder zugriffsbeschränkt oder nicht verfügbar sind und deshalb nicht auf diesem Wiki genutzt werden können.", "smw-admin-supplementary-settings-title": "Konfigurationseinstellungen", "smw-admin-supplementary-settings-intro": "$1 – <PERSON><PERSON><PERSON> eine Liste der verfügbaren und von Semantic MediaWiki verwendeten Konfigurationseinstellungen an", "smw-admin-main-title": "Semantic MediaWiki − $1", "smw-admin-supplementary-operational-statistics-title": "Statistiken zu Nutzung und Betrieb", "smw-admin-supplementary-operational-statistics-short-title": "Betriebsstatistiken", "smw-admin-supplementary-operational-statistics-intro": "$1 – <PERSON><PERSON><PERSON> zu Semantic MediaWiki verfügbare Statistiken an", "smw-admin-supplementary-idlookup-title": "Datenobjekte ermitteln und bereinigen", "smw-admin-supplementary-idlookup-short-title": "Objektsuche und -bereinigung", "smw-admin-supplementary-idlookup-intro": "$1 – Bietet Funktionen zum Ermitteln und Bereinigen einzelner Datenobjekte", "smw-admin-supplementary-duplookup-title": "Doppelten Datenobjekte anzeigen", "smw-admin-supplementary-duplookup-intro": "$1 – <PERSON><PERSON><PERSON> doppel<PERSON> Datenobjekte an", "smw-admin-supplementary-duplookup-docu": "Diese Seite listet Datenobjekte, die in der Datenbank als [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities doppelt vorhanden] kategorisiert wurden. Doppelte Datenobjekte sollten, wenn überhaupt, nur in seltenen Fällen auftreten, und entstehen zumeist aufgrund unterbrochener oder gescheiterter Datenaktualisierungsvorgänge.", "smw-admin-supplementary-operational-statistics-cache-title": "Statistiken zum Cache", "smw-admin-supplementary-operational-statistics-cache-intro": "$1 – <PERSON><PERSON><PERSON> zur Nutzung des Caches an", "smw-admin-supplementary-operational-table-statistics-title": "Statist<PERSON><PERSON> zu Tabellen", "smw-admin-supplementary-operational-table-statistics-short-title": "Tabellenstatistiken", "smw-admin-supplementary-operational-table-statistics-intro": "$1 – <PERSON><PERSON><PERSON> zu Datentabellen an", "smw-admin-supplementary-operational-table-statistics-explain": "Dieser Abschnitt enthält ausgewählte Statistiken zu Datentabellen, um Administratoren und Datenkuratoren dabei zu unterstützen, informierte Entscheidungen bezüglich des Status der Datenbank sowie der Suchdatenbank zu treffen.", "smw-admin-supplementary-operational-table-statistics-legend": "Diese Legende erklärt die einzelnen Schlagwörter der Statistiken zu Datentabellen:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> – <PERSON><PERSON><PERSON> der Zeilen, die in einer Datentabelle vorhanden sind", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> – Die letzte Kennung, die aktuell verwendet wird\n* <code>duplicate_count</code> – Die Anzahl der Duplikate, die in der Datentabelle <code>id_table</code> vorhanden sind (siehe auch [[Special:SemanticMediaWiki/duplicate-lookup|„Suche nach doppelten Datenobjekten“]])\n* <code>rows.rev_count</code> – Die Anzahl der Zeilen, die eine Versionskennung aufweisen und somit einen direkten Verweis zu einer Seite haben\n* <code>rows.smw_namespace_group_by_count</code> – Die Anzahl der Zeilen je Namensraum, die verwendet werden\n* <code>rows.smw_proptable_hash.query_match_count</code> – Die Anzahl der Subobjekte zu Abfragen mit einem Verweis in der Datentabelle\n* <code>rows.smw_proptable_hash.query_null_count</code> – <PERSON> Anzahl der Subobjekte zu Abfragen, die über keinen Verweis in der Datentabelle (unverlinkte, fließende Referenz) verfügen", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> – Der Prozentsatz der Begriffe, die einzigartig sind (ein niedriger Prozentsatz deutet darauf hin, dass sich wiederholende Begriffe in der Tabelle und dem Index befinden)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> – <PERSON> Anzahl der Begriffe, die nur einmal vorkommen\n<code>rows.terms_occurrence.multi_occurrence_total_count</code> – <PERSON> Anzahl der Begriffe, die mehr als einmal vorkommen", "smw-admin-supplementary-elastic-version-info": "Version", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "$1 – Informiert detailliert über Einstellungen und Indexstatistiken", "smw-admin-supplementary-elastic-docu": "Diese Seite bietet Informationen zu Einstellungen, <PERSON><PERSON>s, Betriebsstatus sowie Indexstatistiken des Elasticsearch-Clusters, der von Semantic MediaWiki über dessen [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore „ElasticStore“] genutzt wird.", "smw-admin-supplementary-elastic-functions": "Unterstützte Funktionen", "smw-admin-supplementary-elastic-settings-title": "Einstellungen", "smw-admin-supplementary-elastic-settings-intro": "$1 – <PERSON><PERSON><PERSON> eine Übersicht der Einstellungen, die von Elasticsearch verwendet werden, um die von Semantic MediaWiki genutzten Indizes zu verwalten", "smw-admin-supplementary-elastic-mappings-title": "Mappings", "smw-admin-supplementary-elastic-mappings-intro": "$1 – <PERSON>ei<PERSON> die Liste der genutzten Indizes und Feldmappings", "smw-admin-supplementary-elastic-mappings-docu": "Diese Seite enthält Einzelheiten zu den Feldmappings, die vom aktuellen Index verwendet werden. Die Zusammenfassung der Feldmappings sollte in Verbindung mit dem Konfigurationsparameter <code>index.mapping.total_fields.limit</code> überwacht werden, der die maximale Anzahl der zulässigen Felder eines Index angibt.", "smw-admin-supplementary-elastic-mappings-docu-extra": "Der Wert zu <code>property_fields</code> bezieht sich auf die Anzahl indexierter Hauptfelder, während sich der Wert zu <code>nested_fields</code> auf die kumulierte Anzahl zusätzlicher Felder bezieht, die einem Hauptfeld zugewiesen sind, um spezifische strukturierte Suchmuster zu unterstützen.", "smw-admin-supplementary-elastic-mappings-summary": "Zusammenfassung", "smw-admin-supplementary-elastic-mappings-fields": "Feldmappings", "smw-admin-supplementary-elastic-nodes-title": "Knoten", "smw-admin-supplementary-elastic-nodes-intro": "$1 – <PERSON><PERSON><PERSON> des genutzten Knotens", "smw-admin-supplementary-elastic-indices-title": "Indizes", "smw-admin-supplementary-elastic-indices-intro": "$1 – Bietet einen Überblick zu den genutzten Indizes mitsamt zugehöriger Statistiken", "smw-admin-supplementary-elastic-statistics-title": "Statistiken", "smw-admin-supplementary-elastic-statistics-intro": "$1 – <PERSON><PERSON><PERSON> der Indexebenen", "smw-admin-supplementary-elastic-statistics-docu": "Diese Seite bietet Einblick in die Statistiken zu den verschiedenen Betriebsabläufen auf der Betriebsebene der genutzten Indizes. Die Statistiken werden für die Gesamtebene des jeweiligen Index sowie für dessen primäre Unterebenen zusammengefasst ausgegeben. Die [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html Hilfeseite] enthält eine ausführliche Beschreibung der verfügbaren Indexstatistiken.", "smw-admin-supplementary-elastic-status-replication": "Status der Datenreplikation", "smw-admin-supplementary-elastic-status-last-active-replication": "Zuletzt durchgeführte Datenreplikation: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Aktualisierungszeitspanne: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Rückstand an Datenwiederherstellungsaufträgen: $1 (Schätzung)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Rückstand an Aufträgen zum Aufnehmen von Daten aus Dateien: $1 (Schätzung)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replikation gesperrt: $1 (Datenwiederherstellung wird durchgeführt)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Überwachung der Datenreplikation (aktiv): $1", "smw-admin-supplementary-elastic-replication-header-title": "Datenreplikationsstatus", "smw-admin-supplementary-elastic-replication-function-title": "Datenreplikation", "smw-admin-supplementary-elastic-replication-intro": "$1 – <PERSON><PERSON><PERSON> zu fehlgeschlagenen Datenreplikationen an", "smw-admin-supplementary-elastic-replication-docu": "Diese Seite stellt Informationen zum [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring Replikationsstatus] von Datenobjekten bereit, bei denen Probleme in Zusammenhang mit der Elasticsearch-Datenbank bestehen. Es wird em<PERSON>, die aufgelisteten Datenobjekte zu überprüfen und den Cache der betreffenden Seiten zu leeren. Sofern das Problem dadurch gelöst wurde, bestand es nur vorübergehend.", "smw-admin-supplementary-elastic-replication-files-docu": "Die [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion Indexierung von Dateien] erfolgt in einem gesonderten Schritt. Erst nachdem diese Aufgabe vollständig ausgeführt wurde, sind die Inhalte der Dateien im Suchindex verfügbar.", "smw-admin-supplementary-elastic-replication-files": "<PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-pages": "Seiten", "smw-admin-supplementary-elastic-endpoints": "Endpunkte", "smw-admin-supplementary-elastic-config": "Konfiguration", "smw-admin-supplementary-elastic-no-connection": "Das Wiki kann derzeit '''keine''' Verbindung zur Elasticsearch-Datenbank herstellen. Dies verhindert aktuell die Datenabfragen im Wiki. Der Systemadministrator des Wikis muß kontaktiert werden, damit das Problem behoben werden kann und die erforderliche Indexierung des Wikis wieder durchgeführt wird.", "smw-list-count": "Die Liste enthält {{PLURAL:$1|einen Eintrag|$1 Einträge}}.", "smw-property-label-uniqueness": "Die Bezeichnung „$1“ entspricht mindestens der eines weiteren Attributs. Siehe hierzu die [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness Hilfeseite] mit Informationen zur Lösung dieses Problems.", "smw-property-label-similarity-title": "Bericht zur Ähnlichkeit von Attributbezeichungen", "smw-property-label-similarity-intro": "<u>$1</u> ermittelt Ähnlichkeiten zwischen vorhandenen Attributbezeichnungen", "smw-property-label-similarity-threshold": "Schwellenwert:", "smw-property-label-similarity-type": "Bezeichner des Datentyps anzeigen", "smw-property-label-similarity-noresult": "Den Auswahlkriterien entsprechend keine Ergebnisse.", "smw-property-label-similarity-docu": "Diese Spezialseite vergleicht die [https://www.semantic-mediawiki.org/wiki/Property_similarity syntaktische Ähnlichkeit], nicht jedoch die semantische Ähnlichkeit, zwischen zwei Attributbezeichnungen und gibt diese an. Diese Analyse kann beim Ermitteln von orthografisch falsch geschriebenen oder gleichwertigen Attributen helfen, die konzeptionell gleichbedeutend sind. Siehe auch die Spezialseite [[Special:Properties|„Attribute“]], um Konzeption und Nutzung der hier angegebenen Attribute weiter zu klären. Der Schwellenwert für die Analyse kann angepasst werden, um die Ähnlichkeits- und Distanzmaße entweder zu erweitern oder zu verringern. Zudem kann das Attribut [[Property:$1|„$1“]] dazu genutzt werden bestimmte Attribute von der hier durchgeführten Analyse auszuschließen.", "smw-admin-operational-statistics": "Diese Seite zeigt <PERSON>, die bezüglich der Nutzung oder zum Betrieb von Semantic MediaWiki ermittelt wurden. Eine erweiterte Übersicht wikispezifischer Statistiken ist auf der Spezialseite [[Special:Statistics|<b>Statistik</b>]] verfügbar.", "smw_adminlinks_datastructure": "Datenstruktur", "smw_adminlinks_displayingdata": "Datenanzeige", "smw_adminlinks_inlinequerieshelp": "<PERSON><PERSON><PERSON> zu eingebetteten Abfragen", "smw-page-indicator-usage-count": "Ungefähre [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Anzahl zugeordneter Attributwerte]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|Von einem Benutzer festgelegtes Attribut|Vom System vorgegebenes Attribut}}", "smw-property-indicator-last-count-update": "Ungefähre Anzahl der Datenwertzuordnungen zu diesem Attribut\nZuletzt aktualisiert: $1", "smw-concept-indicator-cache-update": "Anzahl der Seiten dieses Konzepts (gecachter Wert)\n\nZuletzt aktualisiert: $1", "smw-createproperty-isproperty": "Dies ist ein Attribut des Datentyps $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Der mögliche Wert für dieses Attribut ist|Die möglichen Werte für dieses Attribut sind}}:", "smw-paramdesc-category-delim": "Legt fest, welches Trennzeichen bei der Ausgabe der Abfrageergebnisse genutzt werden soll", "smw-paramdesc-category-template": "Legt fest, welche Vorlage bei der Ausgabe der Abfrageergebnisse verwendet werden soll", "smw-paramdesc-category-userparam": "Legt fest, welcher Parameter an die Vorlage bei der Ausgabe der Abfrageergebnisse weitergegeben werden soll", "smw-info-par-message": "Die anzuzeigende Nachricht", "smw-info-par-icon": "Das anzuzeigende Symbol, entweder „Info“ (<code>info<code>) oder „Warnung“(<code>warning</code>)", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Allgemeine Optionen", "prefs-extended-search-options": "Erweiterte Suche", "prefs-ask-options": "Semantische Suche", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_–_Startseite Semantic MediaWiki] und zugehörige Erweiterungen bieten die Möglichkeit individueller Einstellungen für ausgewählte Funktionen. Siehe hierzu auch die entsprechende [https://www.semantic-mediawiki.org/wiki/Help:Benutzereinstellungen Hilfeseite] für eine ausführliche Beschreibung.", "smw-prefs-ask-options-tooltip-display": "Beschreibung der möglichen Ausgabeparameter als Tooltip auf der Spezialseite [[Special:Ask|Semantische Suche]] anzeigen.", "smw-prefs-ask-options-compact-view-basic": "Kompakte Ansicht aktivieren", "smw-prefs-help-ask-options-compact-view-basic": "Falls aktiviert, wird auf der Spezialseite „Semantische Suche“ in der kompakten Ansicht eine reduzierte Anzahl von Links angezeigt.", "smw-prefs-general-options-time-correction": "Zeitkorrektur für Spezialseiten mithilfe der lokalen [[Special:Preferences#mw-prefsection-rendering|Einstellung zum Zeitunterschied]] aktivieren", "smw-prefs-general-options-jobqueue-watchlist": "Auftragswarteschlangen-Beobachtungsliste in meiner persönlichen Navigationsleiste anzeigen", "smw-prefs-help-general-options-jobqueue-watchlist": "<PERSON> aktiviert, wird eine [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist Liste ausstehender Softwareaufträge] zusammen mit dem geschätzten Umfang der entsprechenden Auftragswarteschlange angezeigt.", "smw-prefs-general-options-disable-editpage-info": "Hinweise während des Bearbeitens von Seiten deaktivieren", "smw-prefs-general-options-disable-search-info": "Hinweise zur für die Suche nutzbaren Wikisyntax auf der Standard-Suchseite deaktivieren", "smw-prefs-general-options-suggester-textinput": "Eingabehilfen für semantische Objekte aktivieren", "smw-prefs-help-general-options-suggester-textinput": "<PERSON> aktiviert, ist es von Eingabefeldern aus möglich, eine [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Eingabehilfe] zu Attributen, Konzepten und Kategorien aufzurufen.", "smw-prefs-general-options-show-entity-issue-panel": "Problemanzeige zu Daten anzeigen", "smw-prefs-help-general-options-show-entity-issue-panel": "<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON>, werden auf jeder Seite Prüfungen zur Datenintegrität durchgeführt und deren Ergebnisse über die [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel Problemanzeige zu Daten] angezeigt.", "smw-prefs-factedsearch-profile": "Standardprofil für die [[Special:FacetedSearch|Facettensuche]]:", "smw-ui-tooltip-title-property": "Attribut", "smw-ui-tooltip-title-quantity": "Einheitenumrechnung", "smw-ui-tooltip-title-info": "Information", "smw-ui-tooltip-title-service": "Servicelinks", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-error": "<PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-note": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-legend": "<PERSON><PERSON>", "smw-ui-tooltip-title-reference": "Referenzierung", "smw_unknowntype": "Der Datentyp „$1“ dieses Attributs ist ungültig.", "smw-concept-cache-text": "Das Konzept enthält {{PLURAL:$1|eine Seite|$1 Seiten}} und wurde letztmalig am $2 um $3 Uhr aktualisiert.", "smw_concept_header": "Seiten mit dem Konzept „$1“", "smw_conceptarticlecount": "Unterhalb {{PLURAL:$1|wird eine Seite|werden $1 Seiten}} angezeigt.", "smw-qp-empty-data": "Die angeforderten Daten konnten aufgrund unzureichender Auswahlkriterien nicht angezeigt werden.", "right-smw-admin": "Zugriff auf administrative Tätigkeiten (Semantic MediaWiki)", "right-smw-patternedit": "Zulässige Textmuster anhand regulärer Ausdrücke erstellen und bearbeiten", "right-smw-pageedit": "Seiten bearbeiten, die mit einem positiven Wahrheitswert für das Spezialattribut „Ist bearbeitungsgeschützt“ annotiert wurden", "right-smw-schemaedit": "Seiten im Namensraum [https://www.semantic-mediawiki.org/wiki/Help:Schema „Schema“] bearbeiten (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "Zugriff auf die [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist An<PERSON><PERSON> zu Auftragswarteschlangen] (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Auf Informationen über nicht zueinander passende Revisionen von Entitäten zugreifen (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "[https://www.semantic-mediawiki.org/wiki/Help:Edit_help Bearbeitungshilfe] einsehen (Semantic MediaWiki)", "restriction-level-smw-pageedit": "geschützt (nur berechtigte Benutzer)", "action-smw-patternedit": "regul<PERSON>re Ausdrücke zu erstellen und/oder zu bearbeiten, die von Semantic MediaWiki verwendet werden", "action-smw-pageedit": "Seiten zu bearbeiten, die mit einem positiven Wahrheitswert für das Spezialattribut „Ist bearbeitungsgeschützt“ annotiert wurden", "group-smwadministrator": "SMW-<PERSON><PERSON>", "group-smwadministrator-member": "{{GENDER:$1|SMW-Administrator|SMW-Administratorin}}", "grouppage-smwadministrator": "{{ns:project}}:SMW-Administrator<PERSON>", "group-smwcurator": "SMW-<PERSON><PERSON><PERSON>", "group-smwcurator-member": "{{GENDER:$1|SMW-Kurator|SMW-Kuratorin}}", "grouppage-smwcurator": "{{ns:project}}:SMW-<PERSON><PERSON><PERSON>", "group-smweditor": "SMW-Editoren", "group-smweditor-member": "{{GENDER:$1|SMW-Editor|SMW-Editorin}}", "grouppage-smweditor": "{{ns:project}}:SMW-Editoren", "action-smw-admin": "Semantic MediaWiki zu administrieren", "action-smw-ruleedit": "Regeln zu erstellen und zu bearbeiten", "smw-property-namespace-disabled": "Der [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks Namensraum „Attribut“] ist inaktiv. Es ist nicht möglich, einen Datentyp oder andere Eigenschaften zu einem Attribut festzulegen.", "smw-property-predefined-default": "„$1“ ist ein Spezialattribut des Datentyps $2.", "smw-property-predefined-common": "Dieses Attribut ist softwareseitig fest definiert und auch bekannt als [https://www.semantic-mediawiki.org/wiki/Help:Spezialattribute Spezialattribut]. Es erfüllt eine besondere Funktion, kann jedoch wie jedes andere [https://www.semantic-mediawiki.org/wiki/Help:Attribut benutzerdefinierte Attribut] verwendet werden.", "smw-property-predefined-ask": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Metainformationen einer Abfrage als [https://www.semantic-mediawiki.org/wiki/Subobject Subobjekt] speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-asksi": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Anzahl der von einer Abfrage verwendeten Bedingungen speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-askde": "„$1“ ist ein softwareseitig fest definiertes Attribut, das über die Tiefe einer Abfrage informiert. Dieses Attribut wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-askde": "Es handelt sich um einen numerischen Wert, der der Summe verschachtelter Unterabfragen, verarbeiteten Attributketten sowie vorhandenen Elementen zur Beschreibung einer Abfrage entspricht. Er wird durch den mit dem Konfigurationsparameter [https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth <code>$smwgQMaxDepth</code>] angegebenen Wert beschränkt.", "smw-property-predefined-askpa": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Parameter einer Abfrage speichert, die deren Ergebnis beeinflussen. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-askpa": "Es ist Teil einer Sammlung von Spezialattributen, mit denen das [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler Profil einer Abfrage] erstellt wird.", "smw-sp-properties-docu": "Diese Spezialseite listet die [https://www.semantic-mediawiki.org/wiki/Property Attribute] und den Umfang ihrer Nutzung in diesem Wiki auf. Es wird empfohlen, regelmäßig das [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics Wartungsskript zur Aktualisierung der Nutzungsstatistik] auszuführen, um stets eine aktuelle Übersicht zu haben. Für eine differenzierte Ansicht existieren zudem die Spezialseiten für [[Special:UnusedProperties|nicht genutzte]] und [[Special:WantedProperties|gewünschte]] Attribute.", "smw-sp-properties-cache-info": "Die aufgelisteten Daten stammen aus dem [https://www.semantic-mediawiki.org/wiki/Caching Zwischenspeicher]. Letzte Aktualisierung: $1.", "smw-sp-properties-header-label": "Attributliste", "smw-admin-settings-docu": "Diese Seite zeigt eine Liste der Standardeinstellungen sowie der für dieses Wiki angepassten Einstellungen zur Konfiguration von Semantic MediaWiki an. Weitergehende Informationen sind auf der Hilfeseite zu den [https://www.semantic-mediawiki.org/wiki/Help:Configuration Konfigurationseinstellungen] verfügbar.", "smw-sp-admin-settings-button": "Liste erstellen", "smw-admin-idlookup-title": "Ermittlung", "smw-admin-idlookup-docu": "In diesem Abschnitt werden die technischen Einzelheiten zu einem Objekt (Wikiseite, Unterobjekt, Attribut usw.) in Semantic MediaWiki angezeigt. Die Eingabe kann eine zutreffende Kennung oder Zeichenkette sein. Die<PERSON> Kennungen und Zeichenketten sind nicht zu verwechseln mit den von MediaWiki verwendeten Seitenbezeichnungen oder Versionskennungen.", "smw-admin-iddispose-title": "Bereinigung", "smw-admin-iddispose-docu": "Die Bereinigung einer Objektkennung wird nach der Bestätigung unbeschränkt durchgeführt, d. h. das Objekt wird nach erfolgter Bestätigung direkt mitsamt allen Verknüpfungen aus der Datenbank entfernt. Diese Aktion sollte daher nur mit großer '''Vorsicht''' und nur nach Einsicht in die [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal entsprechende Dokumentation] durchgeführt werden.", "smw-admin-iddispose-done": "Die Objektkennung „$1“ wurde aus der Datenbank entfernt.", "smw-admin-iddispose-references": "Die Kennung „$1“ hat {{PLURAL:$2|keinen|mindestens einen}} aktiven Verweis auf eine Datenbanktabelle:", "smw-admin-iddispose-references-multiple": "Liste der Kennungen mit mindestens einem aktiven Verweis auf eine Datenbanktabelle.", "smw-admin-iddispose-no-references": "Der Eintrag „$1“ wurde in keiner Datenbanktabelle gefunden.", "smw-admin-idlookup-input": "Suche:", "smw-admin-objectid": "<PERSON><PERSON><PERSON>:", "smw-admin-tab-general": "Übersicht", "smw-admin-tab-notices": "Hinweise zur Konfiguration", "smw-admin-tab-maintenance": "Wartung", "smw-admin-tab-supplement": "Zusätzliche Funktionen", "smw-admin-tab-registry": "Wikiregistrierung", "smw-admin-tab-alerts": "Meldungen", "smw-admin-alerts-tab-deprecationnotices": "Hinweise zur Konfiguration", "smw-admin-alerts-tab-maintenancealerts": "Meldungen zur Wartung", "smw-admin-alerts-section-intro": "Dieser Abschnitt zeigt <PERSON> und Hinweise zu Konfiguration sowie bezüglich des Betriebs oder anderer Aktivitäten. <PERSON><PERSON> mü<PERSON> von Administratorinnen oder Administratoren mit entsprechenden Rechten geprüft werden.", "smw-admin-maintenancealerts-section-intro": "Die Beachtung folgender Meldungen und Hinweise ist nicht essenziell. Deren Beachtung unterstützt sowie verbessert indes die Wartung und den Betrieb des Systems.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Datentabellenoptimierung", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Die letzte [https://www.semantic-mediawiki.org/wiki/Table_optimization Optimierung der Datenbanktabellen] wurde vor $2 Tagen durchgeführt (Eintrag vom $1). Der Schwellwert von $3 Tagen ist somit überschritten. Eine Optimierung der Datenbanktabellen führt zu effizienteren Ausführungen von Abfragen. Es wird empfohlen regelmäßig eine Optimierung der Datenbanktabellen durchzuführen.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Veraltete Datenobjekte", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Das System hat $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities veraltete Dateiobjekte] ermittelt. Der kritische Wert von $2 nicht durchgeführten Wartungsaufgaben ist daher überschritten. Es wird empfohlen, das Wartungsskript [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php „disposeOutdatedEntities.php“] auszuführen.", "smw-admin-maintenancealerts-invalidentities-alert-title": "Ungültige Datenobjekte", "smw-admin-maintenancealerts-invalidentities-alert": "Das System hat $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|ungültigen Eintrag|ungültige Einträge}}] in einem [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace nicht gewarteten Namensraum] gefunden. Es wird empfohlen, entweder das Wartungsskript [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php „disposeOutdatedEntities.php“] oder [https://www.semantic-mediawiki.org/wiki/rebuildData.php „rebuildData.php“] auszuführen.", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Konfigurationseinstellungen", "smw-admin-configutation-tab-namespaces": "Namensräume", "smw-admin-configutation-tab-schematypes": "Schematypen", "smw-admin-maintenance-tab-tasks": "Wartungsaufgaben", "smw-admin-maintenance-tab-scripts": "Wartungsskripte", "smw-admin-maintenance-no-description": "Es ist keine Beschreibung vorhanden.", "smw-admin-maintenance-script-section-title": "Liste verfügbarer Wartungsskripte", "smw-admin-maintenance-script-section-intro": "Die folgenden Wartungsskripte können nur von einem Systemadministrator über die Kommandozeile des Servers ausgeführt werden:", "smw-admin-maintenance-script-description-dumprdf": "Ermöglicht den RDF-Export der vorhandenen semantische Tripel.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Ermöglicht die Verwaltung (Erstellung, Löschung und Aktualisierung) semantischer Konzepte.", "smw-admin-maintenance-script-description-rebuilddata": "Ermöglicht das Erstellen oder Aktualisieren aller in der Datenbank gespeicherten semantischen Daten.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Ermöglicht das Erstellen oder Aktualisieren aller im Index von Elasticsearch gespeicherten semantischen Daten, sofern Elasticsearch als Datenbank genutzt wird.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Ermöglicht das Ermitteln fehlender Datenobjekte und erstellt diese von Elasticsearch, sofern Elasticsearch als Datenbank genutzt wird.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Ermöglicht das Erstellen oder Aktualisieren aller im Suchindex der Volltextsuche gespeicherten Daten, sofern diese aktiviert wurde (<code>SQLStore</code>).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Ermöglicht das Aktualisieren der Statistik zur Attributnutzung.", "smw-admin-maintenance-script-description-removeduplicateentities": "Ermöglicht das Entfernen von Datenobjektduplikaten, die in der Datenbank über keine aktiven Verknüpfungen mehr verfügen.", "smw-admin-maintenance-script-description-setupstore": "Richtet die für die Speicherung semantischer Daten konfigurierte Datenbank, entsprechend der Festlegung in der Datei „LocalSettings.php“, ein.", "smw-admin-maintenance-script-description-updateentitycollation": "Ermöglicht das Aktualisieren des Datenbankfelds <code>smw_sort</code> in der relationalen Datenbank (in Übereinstimmung mit der [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation Konfiguration des Parameters <code>$smwgEntityCollation</code>]).", "smw-admin-maintenance-script-description-populatehashfield": "Ermöglicht das Befüllen des Datenbankfelds <code>smw_hash</code> in der relationalen Datenbank.", "smw-admin-maintenance-script-description-purgeentitycache": "Ermöglicht das Löschen des Caches für bekannte Objekte und ihre zugehörigen Daten.", "smw-admin-maintenance-script-description-updatequerydependencies": "Ermöglicht das Aktualisieren von Abfragen sowie von <PERSON>, die von Abfragen abhängen. Siehe hierzu auch den [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore Konfigurationsparameter <code>$smwgEnabledQueryDependencyLinksStore</code>.]", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Veraltete Einträge und Abfragelinks entfernen.", "smw-admin-maintenance-script-description-runimport": "Gesondert hinterlegte Inhalte anlegen und importieren. Siehe auch [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs Konfigurationsparameter <code>$smwgImportFileDirs</code>].", "smw-admin-maintenance-script-section-update": "Aktualisierungsskripte", "smw-admin-maintenance-script-section-rebuild": "Neuerstellungsskripte", "smw-livepreview-loading": "<PERSON>de …", "smw-sp-searchbyproperty-description": "Diese Seite stellt eine einfache [https://www.semantic-mediawiki.org/wiki/Help:Semantisches_Browsen Suchoberfläche] zum <PERSON><PERSON> von Objekten bereit, die ein Attribut mit einem bestimmten Datenwert enthalten. Andere verfügbare Suchoberflächen sind die [[Special:PageProperty|Attributsuche]] sowie der [[Special:Ask|Abfragengenerator]].", "smw-sp-searchbyproperty-resultlist-header": "Liste der Ergebnisse", "smw-sp-searchbyproperty-nonvaluequery": "Eine Liste der Datenwerte des Attributs „$1“.", "smw-sp-searchbyproperty-valuequery": "Eine Liste der Seiten, die das Attribut „$1“ mit dem Datenwert „$2“ enthalten.", "smw-datavalue-number-textnotallowed": "Der Datenwert „$1“ kann einem Attribut des Datentyps Zahl nicht zugeordnet werden sondern bspw. der Datenwert „$2“.", "smw-datavalue-number-nullnotallowed": "Der Datenwert „$1“ wurde mit „NULL“ zurückgegeben, was als Wert für eine Zahl nicht zulässig ist.", "smw-editpage-annotation-enabled": "Diese Seite kann mit semantischen Annotationen in Form von bspw. <code><nowiki>[[</nowiki><PERSON><PERSON><PERSON><PERSON> zu::Dokumentation]]</code> versehen werden, um strukturierte wie abfragbare Inhalte zu erfassen. Ausführliche Hinweise zum [https://www.semantic-mediawiki.org/wiki/Help:Attribute_und_Datentypen#Attribute Einfügen von Annotationen] oder [https://www.semantic-mediawiki.org/wiki/Help:Eingebettete_Abfrage Erstellen von Abfragen] sind auf der Website zu Semantic MediaWiki verfügbar.<!--[[Is specified as::World Heritage Site]]-->", "smw-editpage-annotation-disabled": "Diese Seite kann nicht mit semantischen Annotationen versehen werden, da der Namensraum hierfür nicht konfiguriert wurde.", "smw-editpage-property-annotation-enabled": "Dieses Attribut kann noch mit einem Datentyp in Form von bspw. <code><nowiki>[[</nowiki>Datentyp::Seite]]</code> oder weiteren unterstützten Deklarationen, z. B. <code><nowiki>[[</nowiki>Unterattribut von::Dokumentation]]</code>, versehen werden. Ausführliche Hinweise zu [https://www.semantic-mediawiki.org/wiki/Help:Datentyp#Datentypen Datentypen] sowie eine [https://www.semantic-mediawiki.org/wiki/Help:Datentyp#Liste_der_Datentypen Liste der Datentypen] sind auf der Website zu Semantic MediaWiki verfügbar.<!--[[Has type::Page]], [[Subproperty of::dc:date]]-->", "smw-editpage-property-annotation-disabled": "Dieses Attribut kann nicht mit einem Datentyp versehen werden, da dieser bereits systemseitig festgelegt ist (Spezialattribut). Ausführliche Hinweise zu [https://www.semantic-mediawiki.org/wiki/Help:Spezialattribute Spezialattributen] sind auf der Website zu Semantic MediaWiki verfügbar.<!--[[Has type::Page]]-->", "smw-editpage-concept-annotation-enabled": "Dieses Konzept kann mithilfe der Parserfunktion #concept erweitert werden. Für eine Beschreibung zur Verwendung von #concept, siehe die Hilfeseite zu [https://www.semantic-mediawiki.org/wiki/Help:Concepts Konzepten].", "smw-search-syntax-support": "Die Sucheingabe unterstützt die Nutzung der von Semantic MediaWiki bereitgestellten [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search Abfragesyntax] (<code>#ask:</code>-Syntax) zum Ermitteln der Suchergebnisse.", "smw-search-input-assistance": "Die [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Eingabehilfe] ist ebenfalls aktiviert, um die Auswahl von Attributen und Kategorien zu vereinfachen.", "smw-search-help-intro": "Eine Eingabe von <code><nowiki>[[ ... ]]</nowiki></code> bewirk<PERSON>, dass die strukturierte Suche von Semantic MediaWiki genutzt wird. Die Kombination von <code><nowiki>[[ ... ]]</nowiki></code> mit einer unstrukturierten Textsuche wie bspw. <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> wird nicht unterstützt.", "smw-search-help-structured": "Strukturierte Suchanfragen:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (als [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context Filterung])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (mit einer [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context Abfrage])", "smw-search-help-proximity": "Näherungssuchanfragen (ein Attribut ist unbekannt, '''nur''' verfügbar für solche Datenbanken, die eine Volltextsuche bereitstellen):\n\n* <code><nowiki>[[in:lorem ipsum]]</nowiki></code> (sucht in allen indexierten Dokumenten nach „lorem“ und „ipsum“)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (sucht nach „lorem ipsum“ als Ausdruck)", "smw-search-help-ask": "Die folgenden Links erklären, wie die Abfragesyntax (<code>#ask</code>-Syntax) verwendet werden kann:\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Seiten auswählen] be<PERSON><PERSON><PERSON><PERSON>, wie man Seiten auswählt und Abfragebedingungen erstellt\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Suchoperatoren] listet die verfügbaren Suchoperatoren auf, einschließlich derer für Bereichs- und Platzhalterabfragen", "smw-search-input": "Eingabe und Suche", "smw-search-help-input-assistance": "<PERSON>ü<PERSON> das Eingabefeld wird eine [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Eingabeunterstützung] angeboten. Diese erfordert die Nutzung eines der folgenden Präfixe:\n\n* <code>p:</code> zur Aktivierung einer Attributsuche (z.&nbsp;B. <code><nowiki>[[p:Has ...</nowiki></code>)\n\n* <code>c:</code> zur Aktivierung einer Kategoriesuche\n\n* <code>con:</code> zur Aktivierung einer Konzeptsuche", "smw-search-syntax": "Syntax", "smw-search-profile": "Extra", "smw-search-profile-tooltip": "Suchfunktionen in Verbindung mit Semantic MediaWiki", "smw-search-profile-sort-best": "<PERSON><PERSON>", "smw-search-profile-sort-recent": "Aktuellste Treffer", "smw-search-profile-sort-title": "Titel", "smw-search-profile-extended-help-intro": "<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile erweiterte Suchprofil] bietet auf der Seite „Spezial:Suche“ Zugriff auf Suchfunktionen, die spezifisch für die Erweiterung „Semantic MediaWiki“ sind. Die Suchfunktionen bestehen aus:", "smw-search-profile-extended-help-sort": "Ermöglicht die Anpassung der Anzeige von Suchergebnissen mit den folgenden Auswahlmöglichkeiten:", "smw-search-profile-extended-help-sort-title": "* „Titel“ – verwendet den Seiten- oder Anzeigetitel als Sortierkriterium", "smw-search-profile-extended-help-sort-recent": "* „Aktuellste Treffer“ – verwendet den Zeitpunkt der letzten Änderung als Sortierkriterium. Ergebnisse aus Unterobjekten werden indes nicht angezeigt, da diese generell nicht mit dem notwendigen Attribut „[[Property:Modification date|Zuletzt geändert]]“ annotiert werden.", "smw-search-profile-extended-help-sort-best": "* „Beste Treffer“ – verwendet die [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy Relevanz von Seiten] als Sortierkriterium. Die Relevanz stützt sich auf sogenannte „Scores“, die von Elasticsearch ermittelt und bereitgestellt werden.", "smw-search-profile-extended-help-form": "Formulare werden Benutzern zur Verfügung gestellt, um spezielle Nutzungsfälle bei der Suche abzudecken. Formulare können unterschiedliche Attribut- und Werteingabefelder enthalten, um es Benutzern zu erleichtern, eine Suche durchzuführen. (siehe $1)", "smw-search-profile-extended-help-namespace": "Der Auswahlkasten für Namensräume wird ausgeblendet, sobald ein Formular ausgewählt wurde. Dieser kann jedoch mithilfe der Schaltflächen „Anzeigen“ wieder sichtbar geschaltet werden.", "smw-search-profile-extended-help-search-syntax": "Das Eingabefeld der Suche unterstützt die Nutzung der von Semantic MediaWiki bereitgestellten Abfragesyntax (<code>#ask:</code>-Syntax) zur Durchführung einer semantischen Suche. Nützliche Suchausdrücke sind:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code>, um alles zu finden, was die Eingabe \"…\" enthält. Dies ist insbesondere nützlich, wenn die gesuchten Begriffe nicht eindeutig und/oder die Attribute unbekannt sind (z.&nbsp;B.:<code>in:(lorem && ipsum)</code> entspricht <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code>, um alles zu finden, was exakt der Eingabe \"…\" entspricht", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code>, um alle Objekte eines angegebenen Attributs \"…\" zu finden (z.&nbsp;B.: <code>has:(Foo && Bar)</code> entspricht <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code>, um keines der Objekte zu finden, das die Eingabe \"…\" enthält", "smw-search-profile-extended-help-search-syntax-prefix": "* Zusätzliche benutzerdefinierte Suchausdrücke sind verfügbar und definiert wie beispielsweise: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Einige Suchausdrücke sind reserviert wie beispielsweise: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Einige der gelisteten Suchausdrücke sind nur in Verbindung mit einem aktivierten Volltextsuchindex oder einer ElasticSearch-Datenbank nützlich.''", "smw-search-profile-extended-help-query": "<code><nowiki>$1</nowiki></code> wurde für die Abfrage verwendet.", "smw-search-profile-extended-help-query-link": "Weitere Einzelheiten: $1.", "smw-search-profile-extended-help-find-forms": "verfügbare Formulare", "smw-search-profile-extended-section-sort": "Sortieren nach", "smw-search-profile-extended-section-form": "Formulare", "smw-search-profile-extended-section-search-syntax": "Sucheingabe", "smw-search-profile-extended-section-namespace": "Namensraum", "smw-search-profile-extended-section-query": "Abfrage", "smw-search-profile-link-caption-query": "Abfragengenerator", "smw-search-show": "Anzeigen", "smw-search-hide": "Ausblenden", "log-name-smw": "Semantic-MediaWiki-Logbuch", "log-show-hide-smw": "Semantic-MediaWiki-Logbuch $1", "logeventslist-smw-log": "Semantic-MediaWiki-Logbuch", "log-description-smw": "Dies ist das Logbuch, mit dem die Aktivitäten von Semantic MediaWiki bezüglich der hierfür [https://www.semantic-mediawiki.org/wiki/Help:Logging aktivierten Ereignisarten] protokolliert werden.", "logentry-smw-maintenance": "Von Semantic MediaWiki durchgeführte wartungsbezogene Ereignisse", "smw-datavalue-import-unknown-namespace": "Der für den Import vorgesehene Namensraum „$1“ ist unbekannt. Es muss sichergestellt werden, dass die Einzelheiten zum OWL-Import auf der Seite [[MediaWiki:Smw import $1]] angegeben sind.", "smw-datavalue-import-missing-namespace-uri": "Es wurde keine URI für den Namensraum „$1“ auf der Seite mit den Definitionen für den [[MediaWiki:Smw import $1|$1-Import]] gefunden.", "smw-datavalue-import-missing-type": "Es wurde keine Definition des Datentyps für „$1“ im [[MediaWiki:Smw import $2|$2-Import]] gefunden.", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1-Import]]", "smw-datavalue-import-invalid-value": "„$1“ ist kein gültiges Format. Es muss dem Schema „Namensraum“:„Kennung“ (z.&nbsp;B. „foaf:name“) entsprechen.", "smw-datavalue-import-invalid-format": "„$1“ ist keine gültige Zeichenfolge für dieses Schema. Diese muss in vier Teile gegliedert sein.", "smw-property-predefined-impo": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den Zusammenhang mit einem [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary importierten Vokabular] beschreibt. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-type": "„$1“ ist ein softwareseitig fest definiertes Attribut, mit dem der [[Special:Types|Datentyp]] eines Attributs festgelegt wird. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-sobj": "„$1“ ist ein softwareseitig fest definiertes Attribut und stellt einen [https://www.semantic-mediawiki.org/wiki/Help:Container Datenverbund] dar. <PERSON><PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-sobj": "Ein Subobjekt erlaubt die Speicherung mehrerer Attribut-Datenwert-Zuweisungen (Annotationen), <PERSON><PERSON><PERSON> dem Vorgehen auf einer regulären Wikiseite. Die Speicherung erfolgt dabei in einem anderen Objektraum als dem der Wikiseite selbst. Dieser Objektraum ist indes mit der entsprechenden Wikiseite, auf der sich das Subobjekt befindet, verknüpft.", "smw-property-predefined-errp": "„$1“ ist ein softwareseitig fest definiertes Attribut, das fehlerhafte Attributwerte speichert. Fehlerhafte Attributwerte entsprechen nicht dem [https://semantic-mediawiki.org/wiki/Help:Datentyp Datentyp] des Attributs. Das Attribut wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-errp": "In den meisten Fällen wird dieses Problem aufgrund nicht übereinstimmender Datentypangaben oder durch Einschränkungen aufgrund ausschließlich [[Property:Allows value|zulässiger Werte]] verursacht.", "smw-property-predefined-pval": "„$1“ ist ein softwareseitig fest definiertes Attribut, mit dem die für ein Attribut zulässigen Datenwerte festgelegt werden. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-pvali": "„$1“ ist ein softwareseitig fest definiertes Attribut, mit dem ein Verweis auf eine Liste mit für ein Attribut zulässigen Datenwerten festgelegt wird. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-datavalue-property-restricted-annotation-use": "Das Attribut „$1“ hat einen eingeschränkten Anwendungsbereich und kann nicht als Attribut zum Annotieren von Daten verwendet werden.", "smw-datavalue-property-restricted-declarative-use": "Das Attribut „$1“ dient dem Festlegen von Eigenschaften (Definition derselben) und kann nur auf der Seite eines Attributs oder einer Kategorie verwendet werden.", "smw-datavalue-property-create-restriction": "Das Attribut „$1“ ist nicht vorhanden und kann nur von Benutzern der hierfür vorgesehenen [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups Benutzergruppe] oder mit der hierfür benötigten Berechtigung („$2“) erstellt werden, während der [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Berechtigungsmodus] aktiviert ist. Erst wenn dieses unbestätigte Attribut erstellt wurde, kann es für Annotationen genutzt werden.", "smw-datavalue-property-invalid-character": "Der Name des Attributs „$1“ enthält das ungültige Zeichen „$2“, das nicht hierfür verwendet werden kann.", "smw-datavalue-property-invalid-chain": "Die Verwendung von „$1“ als Attributkette ist während des Hinzufügens von Annotationen nicht möglich.", "smw-datavalue-restricted-use": "Der Datenwert „$1“ ist nicht uneingeschränkt nutzbar und kann daher nicht gespeichert werden.", "smw-datavalue-invalid-number": "„$1“ ist keine gültige Zahl.", "smw-query-condition-circular": "In Vorlage „$1“ wurde ein möglicher Zirkelbezug festgestellt.", "smw-query-condition-empty": "Die Abfrage enthält eine leere Bedingung.", "smw-types-list": "Liste der Datentypen", "smw-types-default": "„$1“ ist ein softwareseitig fest definierter Datentyp.", "smw-types-help": "Weitere Informationen sowie Beispiele sind auf der zugehörigen [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 Hilfeseite] vorhanden.", "smw-type-anu": "„$1“ ist eine Variante des Datentyps [[Special:Types/URL|„URL“]], der zumeist für Exportdeklarationen zu ''owl:AnnotationProperty'' genutzt wird.", "smw-type-boo": "„$1“ ist ein Datentyp, der für Wahrheitswerte („wahr“ oder „falsch“) genutzt wird.", "smw-type-cod": "„$1“ ist eine Variante des Datentyps [[Special:Types/Text|„Text“]], der für technische Texte beliebiger Länge wie bspw. Softwarecode genutzt wird.", "smw-type-geo": "„$1“ ist ein Datentyp, der zum Speichern der Lage eines Punktes in einer Ebene oder in einem Raum genutzt wird. Er wird von der Softwareerweiterung [https://www.semantic-mediawiki.org/wiki/Extension:Maps „Maps“] bereitgestellt.", "smw-type-tel": "„$1“ ist ein Datentyp, der für die Ziffernfolge zur Anwahl eines Teilnehmers (Rufnummer) gemäß RFC 3966 genutzt wird.", "smw-type-txt": "„$1“ ist ein Datentyp, der für Zeichenketten beliebiger Länge genutzt wird.", "smw-type-dat": "„$1“ ist ein Datentyp, der für genau bestimmte Momente in einem zeitlichen Bezugssystem (Zeitskala) genutzt wird.", "smw-type-ema": "„$1“ ist ein besonderer Datentyp für E-Mail-Adressen.", "smw-type-tem": "„$1“ ist ein besonderer numerischer Datentyp für Temperaturen.", "smw-type-qty": "„$1“ ist ein besonderer numerischer Datentyp für Mengenangaben mit Maßeinheiten.", "smw-type-rec": "„$1“ ist ein besonderer Datentyp für Attributsequenzen in einer festen Reihenfolge.", "smw-type-extra-tem": "Das Konvertierungsschema unterstützt Temperatureinheiten wie Kelvin, <PERSON><PERSON><PERSON>, Fahrenheit und Rankine.", "smw-type-tab-properties": "Attribute", "smw-type-tab-types": "Datentypen", "smw-type-tab-type-ids": "Datentypenkennungen", "smw-type-tab-errors": "<PERSON><PERSON>", "smw-type-primitive": "<PERSON><PERSON><PERSON>", "smw-type-contextual": "Kontextbezogen", "smw-type-compound": "Komplex", "smw-type-container": "Verbundbezogen", "smw-type-no-group": "Nicht klassifiziert", "smw-special-pageproperty-description": "Diese Seite stellt eine einfache Suchoberfläche zum Ermitteln aller Attribute auf einer bestimmten Seite bereit. Andere verfügbare Suchoberflächen sind die [[Special:SearchByProperty|Attributsuche]] sowie der [[Special:Ask|Abfragengenerator]].", "smw-property-predefined-errc": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Fehler im Zusammenhang mit der Verarbeitung von Eingaben sowie bei fehlerhaften Attribut-Datenwert-Zuweisungen (Annotationen) speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-errc": "Fehler werden in einem [https://www.semantic-mediawiki.org/wiki/Help:Container Container] g<PERSON><PERSON><PERSON><PERSON>, der eine Referenz zu dem Attribut beinhalten kann, das die Diskrepanz verursacht hat.", "smw-property-predefined-errt": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Beschreibungen zu Fehlern enthält, die in Verbindung mit fehlerhaften Attribut-Datenwert-Zuweisungen (Annotationen) oder bei deren Verarbeitung aufgetreten sind. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-subobject-parser-invalid-naming-scheme": "Ein benutzerdefiniertes Unterobjekt verwendet ein ungültiges Namensschema. Die Verwendung eines Punkts innerhalb der ersten fünf Zeichen ($1) ist ausschließlich Softwareerweiterungen vorbehalten. Es kann manuell ein [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier Bezeichner] festgelegt werden.", "smw-datavalue-record-invalid-property-declaration": "Die Definition des Datensatzes enthält das Attribut „$1“, das selbst wiederum dem Datentyp „Verbund“ zugeordnet wurde. Dies ist nicht zulässig.", "smw-property-predefined-mdat": "„$1“ ist ein softwareseitig fest definiertes Attribut, das das Datum der letzten Änderung einer Seite speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-cdat": "„$1“ ist ein softwareseitig fest definiertes Attribut, das das Datum der ersten Version einer Seite speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-newp": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den Status speichert, ob eine Seite eine neue Seite ist. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-ledt": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Benutzerseite des Benutzers speichert, der die letzte Version einer Seite erstellt hat. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-mime": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den MIME-Typ einer hochgeladenen Datei speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-media": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den Medientyp einer hochgeladenen Datei speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-askfo": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den Namen des in einer Abfrage verwendeten Ergebnisformats speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-askst": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Bedingungen der Abfrage als Zeichenfolge speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-askdu": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Dauer in Sekunden speichert, die die Abfrage zur Ausführung benötigt hat. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-asksc": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die alternative Quelle einer Abfrage enthält, bspw. die einer wikifremden Fernabfrage. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-askco": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Query_status_code Statuscode von Abragen] speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-askco": "Die gespeicherte Zahl oder gespeicherten Zahlen stehen für den softwareintern genutzten Code des Status, in dem sich die jeweilige Abfrage befindet. Weitere Informationen sind auf der entsprechenden [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler Hilfeseite] vorhanden.", "smw-property-predefined-prec": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die [https://www.semantic-mediawiki.org/wiki/Help:Display_precision Anzeigegenauigkeit] für numerische Datentypen dezimal speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-attch-link": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Links zu den auf einer Seite eingebetteten Dateien speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-inst": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Informationen zu Kategorien unabhängig von MediaWiki speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-unit": "„$1” ist ein softwareseitig festgelegtes Attribut, um Einheiten numerischer Attributwerte festzulegen. Es wird von  [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-unit": "Eine durch Kommata getrennte Liste ermöglicht die Definition von Einheiten oder Formaten für die Anzeige von Datenwerten.", "smw-property-predefined-conv": "„$1“ ist ein softwareseitig fest definiertes Attribut, um Konvertierungsfaktoren für Maße oder physikalische Einheiten festzulegen. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestell.", "smw-property-predefined-serv": "„$1“ ist ein softwareseitig festgelegtes Attribut, um Service-Links zu Attributen festzulegen. Es wird von  [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-redi": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Weiterleitungen aufzeichnet. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-subp": "„$1“ ist ein softwareseitig fest definiertes Attribut, um festzulegen, dass ein Attribut ein [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of Unterattribut] ist. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-subc": "„$1“ ist ein softwareseitig fest definiertes Attribut, um festzulegen, dass eine Kategorie eine [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of Unterkategorie] ist. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-conc": "„$1“ ist ein softwareseitig fest definiertes Attribut, das zugehörige Konzepte aufzeichnet. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-err-type": "„$1“ ist ein softwareseitig fest definiertes Attribut und speichert Informationen zu [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors Verarbeitungsfehler]. E<PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-skey": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Sortierinformationen speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-pplb": "\"$1\" ist ein softwareseitig fest definiertes Attribut, das die [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label bevorzugte Beschriftung eines Attributs] speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-chgpro": "„$1“ ist ein softwareseitig fest definiertes Attribut und speichert [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation Informationen zur Verarbeitung von Datenänderungen]. E<PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-schema-link": "<PERSON><PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-format-schema": "<PERSON><PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-profile-schema": "<PERSON><PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-trans": "<PERSON><PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-trans-source": "<PERSON><PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-trans-group": "<PERSON><PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-cont-len": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den Umfang des Inhalts einer Datei speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-cont-len": "Es wird in Verbindung mit [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] als Datenbank verwendet. Sofern vorhanden, wird der Umfang einer in die Datenbank aufgenommenen Datei ermittelt und gespeichert.", "smw-property-predefined-cont-lang": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Sprache des Inhalts einer Datei speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-cont-lang": "Es wird in Verbindung mit [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] als Datenbank verwendet. Sofern vorhanden, wird die Sprache des Inhalts einer in die Datenbank aufgenommenen Datei ermittelt und gespeichert.", "smw-property-predefined-cont-title": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den Titel einer Datei speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-cont-title": "Es wird in Verbindung mit [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] als Datenbank verwendet. Sofern vorhanden, wird der Titel einer in die Datenbank aufgenommenen Datei ermittelt und gespeichert.", "smw-property-predefined-cont-author": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Autoren einer Datei speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-cont-author": "Es wird in Verbindung mit [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] als Datenbank verwendet. Sofern vorhanden, werden die Autoren einer in die Datenbank aufgenommenen Datei ermittelt und gespeichert.", "smw-property-predefined-cont-date": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Datum einer Datei speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-cont-date": "Es wird in Verbindung mit [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] als Datenbank verwendet. Sofern vorhanden, wird das Datum einer in die Datenbank aufgenommenen Datei ermittelt und gespeichert.", "smw-property-predefined-cont-type": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den Typ einer Datei speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-cont-type": "Es wird in Verbindung mit [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] als Datenbank verwendet. Sofern vorhanden, wird der Typ einer in die Datenbank aufgenommenen Datei ermittelt und gespeichert.", "smw-property-predefined-cont-keyw": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Schlagwörter zum Inhalt einer Seite speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-cont-keyw": "Es wird in Verbindung mit [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] als Datenbank verwendet. Sofern vorhanden, werden die Schlagwörter einer in die Datenbank aufgenommenen Datei ermittelt und gespeichert.", "smw-property-predefined-file-attch": "„$1“ ist ein softwareseitig fest definiertes Attribut in Form eines Datencontainers, das Informationen zu den auf einer Seite eingebetteten Dateien speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-file-attch": "Es wird in Verbindung mit [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] als Datenbank verwendet. Sofern vorhanden, werden alle abrufbaren Informationen zum Inhalt einer in die Datenbank aufgenommenen Datei ermittelt und gespeichert.", "smw-types-extra-geo-not-available": "Die Softwareerweiterung [https://www.semantic-mediawiki.org/wiki/Extension:Maps „Maps“] ist nicht installiert. Daher ist das Attribut „$1“ in seiner Nutzbarkeit eingeschränkt.", "smw-datavalue-monolingual-dataitem-missing": "Ein erwartetes Element zum Erstellen eines einsprachigen zusammengesetzten Wertes fehlt.", "smw-datavalue-languagecode-missing": "Zur Annotation „$1“ konnte kein Sprachcode festgestellt werden (z.&nbsp;B. „xyz@de“).", "smw-datavalue-languagecode-invalid": "„$1“ ist kein zulässiger Sprachcode.", "smw-property-predefined-lcode": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den einem Datenwert zugeordneten „BCP47“-formatierten Sprachcode speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-type-mlt-rec": "„$1“ ist der Datentyp für einen [https://www.semantic-mediawiki.org/wiki/Help:Container Datenverbund], der einen Text mit dem [[Property:Language code|Sprachcode]] verknüpft und so dessen Sprache festlegt.", "smw-types-extra-mlt-lcode": "Der Datentyp erfordert {{PLURAL:$2|einen|keinen}} Sprachcode (bspw. wird eine Attribut-Datenwert-Zuweisung (Annotation) ohne einen Sprachcode {{PLURAL:$2|nicht akzeptiert|akzeptiert}}).", "smw-property-predefined-text": "„$1“ ist ein softwareseitig fest definiertes Attribut, das einen Text mit einer beliebigen Länge speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-pdesc": "„$1“ ist ein softwareseitig fest definiertes Attribut, das es erlaubt, ein Attribut im Kontext einer Sprache zu beschreiben. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-list": "„$1“ ist ein softwareseitig fest definiertes Attribut zur Definition einer Attributliste in einem [[Special:Types/Record|Attributverbund]]. <PERSON><PERSON> wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-limitreport-intext-parsertime": "[SMW] Zeitverbrauch des Parsers für die direkten Annotationen", "smw-limitreport-intext-postproctime": "[SMW] Datennachverarbeitungzeit", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|Sekunde|Sekunden}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|Sekunde|Sekunden}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Dauer der Datenbankaktualisierung (beim Leeren des Seitencaches)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|Sekunde|Sekunden}}", "smw_allows_pattern": "Auf dieser Seite werden Referenzobjekte mit entsprechenden auf [https://de.wikipedia.org/wiki/Regulärer_Ausdruck regulären Ausdrücken] gestützten Musterreferenzen gespeichert (<code>Referenzobjekt|Musterreferenz</code>) und durch das Spezialattribut „[[Property:Allows pattern|Erlaubt Muster]]“ zur Verfügung gestellt. Um diese Seite bearbeiten zu können, ist das Benutzerrecht <code>smw-patternedit</code> erford<PERSON><PERSON>.", "smw-datavalue-allows-pattern-mismatch": "„$1“ wurde vom regulären Ausdruck „$2“ als ungültig klassifiziert.", "smw-datavalue-allows-pattern-reference-unknown": "Die Musterreferenz „$1“ entspricht keinem Eintrag in [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "Die der Referenz „$1“ zugehörige Seite [[MediaWiki:Smw allows list $1]] für das Speichern von Datenwerten ist nicht vorhanden.", "smw-datavalue-allows-value-list-missing-marker": "In der Liste „$1“ fehlen Einträge mit der Markierung „*“.", "smw-datavalue-feature-not-supported": "Die Funktion „$1“ ist auf diesem Wiki deaktiviert oder wird nicht unterstützt.", "smw-property-predefined-pvap": "„$1“ ist ein softwareseitig fest definiertes Attribut, das eine [[MediaWiki:Smw allows pattern|Musterreferenz]] speichert anhand derer Attributwertzuweisungen mit Hilfe [https://de.wikipedia.org/wiki/Regulärer_Ausdruck regulärer Ausdrücke] überprüft werden. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-dtitle": "„$1“ ist ein softwareseitig fest definiertes Attribut, das einen eindeutigen Anzeigetitel zu einem Objekt speichert und ihm zuweist. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-pvuc": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Information speichert, ob eine bestimmte Attribut-Datenwert-Zuweisung (Annotation) im Wiki einzigartig sein muss oder gar nur einmalig erfolgen darf. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-pvuc": "Eindeutigkeit bedeutet, dass zwei Datenwerte für ein bestimmtes Attribut bei durchgeführten Attribut-Datenwert-Zuweisungen (Annotationen) nicht identisch sein dürfen. Jed<PERSON> Verstoß dieser Anforderung wird als Fehler eingestuft.", "smw-datavalue-constraint-uniqueness-violation": "Dem Attribut „$1“ kann der Datenwert ''$2'' nur einmal zugeordnet werden. Der Datenwert ist bereits auf der Seite „$3“ vorhanden, so daß er nicht gespeichert werden kann.", "smw-datavalue-constraint-uniqueness-violation-isknown": "Das Attribut „$1“ kann nur einmal einer bestimmten Seite zugeordnet werden. Die Seite „$2“ enthält bereits einen Datenwert für dieses Attribut, so daß der Datenwert „$3“ nicht gespeichert werden kann.", "smw-datavalue-constraint-violation-non-negative-integer": "Dem Attribut „$1“ kann nur eine positive G<PERSON><PERSON><PERSON> zugeordnet werden. Der Datenwert ''$2'' ist keine positive <PERSON><PERSON><PERSON><PERSON>, so daß er nicht gespeichert werden kann.", "smw-datavalue-constraint-violation-must-exists": "Dem Attribut „$1“ kann nur ein Datenwert zugeordnet werden, der bereits vorhanden ist (<code>must_exists</code>). Der Datenwert ''$2'' ist nicht vorhanden, so daß er nicht gespeichert werden kann.", "smw-datavalue-constraint-violation-single-value": "Dem Attribut „[[Property:$1|$1]]“ kann auf einer Seite nur ein Datenwert zugeordnet werden (<code>single_value</code>). Der Datenwert ''$2'' ist der zweite, so daß er nicht gespeichert werden kann.", "smw-constraint-violation-uniqueness": "Die Beschränkung <code>unique_value_constraint</code> wurde für das Attribut „[[Property:$1|$1]]“ festgelegt. Dies bedeutet, dass ein Datenwert „$2“ nur einmal diesem Attribut zugeordnet werden kann. Er wurde indes schon dem Datenobjekt „$3“ zugeordnet.", "smw-constraint-violation-uniqueness-isknown": "Die Beschränkung auf einzigartige Werte (<code>unique_value_constraint</code>) wurde für das Attribut „[[Property:$1|$1]]“ festgelegt. Dies bedeutet, dass ein bestimmter Datenwert nur einmal diesem Attribut zugeordnet werden kann. Dem Datenobjekt „$2“ wurde bereits der Datenwert „$3“ zugeordnet und verletzt damit gegen die festgelegte Beschränkung.", "smw-constraint-violation-non-negative-integer": "Die Beschränkung <code>non_negative_integer</code> wurde für das Attribut „[[Property:$1|$1]]“ festgelegt. Dies bedeutet, dass ein Datenwert eine positive ganze Zahl sein muss. Der zugeordnete Datenwert „$2“ verstößt somit gegen die festgelegt Beschränkung.", "smw-constraint-violation-must-exists": "Die Beschränkung <code>must_exists</code> wurde für das Attribut „[[Property:$1|$1]]“ festgelegt. Dies bedeutet, dass ein Datenwert als Seite bereits vorhanden sein muss. Der zugeordnete Datenwert „$2“ verstößt somit gegen die festgelegt Beschränkung.", "smw-constraint-violation-single-value": "Die Beschränkung <code>single_value_constraint</code> wurde für das Attribut „[[Property:$1|$1]]“ festgelegt. Dies bedeutet, dass dem Datenobjekt nur ein Datenwert zugeordnet werden darf. Der zugeordnete Datenwert „$2“ verstößt somit gegen die festgelegt Beschränkung.", "smw-constraint-violation-class-shape-constraint-missing-property": "<PERSON><PERSON><PERSON> die Kategorie \"[[:$1]]\" wurde mit einem Attribut (<code>property</code>) eine Einschränkung (<code>shape_constraint</code>) festgelegt. Das erforderliche Attribut „$2“ ist nicht vorhanden.", "smw-constraint-violation-class-shape-constraint-wrong-type": "<PERSON><PERSON><PERSON> die Kategorie \"[[:$1]]\" wurde mit einen festgelegten Datentyp (<code>property_type</code>) eine Einschränkung (<code>shape_constraint</code>) festgelegt. Der Datentyp des Attributs „$2“ entspricht indes nicht der Anforderung an den Datentyp: „$3“.", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "<PERSON><PERSON><PERSON> die Kategorie \"[[:$1]]\" wurde mit einer Kardinalität (<code>max_cardinality</code>) eine Einschränkung (<code>shape_constraint</code>) festgelegt. Das Attribut „$2“ entspricht indes nicht der Anforderung an die Kardinalität: „$3“.", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "<PERSON><PERSON><PERSON> die Kategorie \"[[:$1]]\" wurde mit einer Mindestlänge (<code>min_textlength</code>) eine Einschränkung (<code>shape_constraint</code>) festgelegt. Das Attribut „$2“ entspricht indes nicht der Anforderung an die Mindestlänge: „$3“.", "smw-constraint-violation-class-mandatory-properties-constraint": "Der Kategorie \"[[:$1]]\" wurde mit erforderlichen Attributen (<code>mandatory_propertiesh</code>) eine Beschränkung (<code>shape_constraint</code>) festgelegt. Die folgenden Attribute müssen vorhanden sein: „$2“.", "smw-constraint-violation-allowed-namespace-no-match": "Die Beschränkung <code>allowed_namespaces</code> wurde für das Attribut „[[Property:$1|$1]]“ festgelegt. Dies bedeutet, dass dem Datenobjekt nur ein Datenwert als Seite aus bestimmten Namensräumen zugeordnet werden darf. Die Seite „$2“ befindet sich nicht im festgelegten Namensraum und verstößt somit gegen die festgelegt Beschränkung. Nur Seiten in den Namensräumen „$3“ sind gemäß der festgelegten Beschränkung zulässig.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "Die Beschränkung <code>allowed_namespaces</code> er<PERSON><PERSON> ein Attribut mit dem Datentyp „Seite“.", "smw-constraint-schema-category-invalid-type": "Das annotierte Schema „$1“ kann nicht für Kategorien genutzt werden. Es muss der Schematyp „$2“ genutzt werden.", "smw-constraint-schema-property-invalid-type": "Das annotierte Schema „$1“ kann nicht für Kategorien genutzt werden. Es muss der Schematyp „$2“ genutzt werden.", "smw-constraint-error-allows-value-list": "„$1“ befindet sich nicht in der Liste ($2) [[Property:Allows value|zulässiger Werte]] für das Attribut „$3“.", "smw-constraint-error-allows-value-range": "„$1“ liegt nicht innerhalb des zulässigen Bereichs „$2“, der durch die Einschränkung mit „[[Property:Allows value|zulässigen Werten]]“ für das Attribut „$3“ festgelegt wurde.", "smw-property-predefined-boo": "„$1“ ist ein [[Special:Types/Boolean|Datentyp]] für Wahrheitswerte („wahr“ oder „falsch“). Er wird Attributen mit Hilfe eines von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] bereitgestellten, softwareseitig fest definierten Attributs (Spezialattribut), zugeordnet.", "smw-property-predefined-num": "„$1“ ist ein [[Special:Types/Number|Datentyp]] für Zahlenwerte. Er wird Attributen mit Hilfe eines von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] bereitgestellten, softwareseitig fest definierten Attributs (Spezialattribut), zugeordnet.", "smw-property-predefined-dat": "„$1“ ist ein [[Special:Types/Date|Datentyp]] für Datumswerte. Er wird Attributen mit Hilfe eines von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] bereitgestellten, softwareseitig fest definierten Attributs (Spezialattribut), zugeordnet.", "smw-property-predefined-uri": "„$1“ ist ein [[Special:Types/URL|Datentyp]] für URI-/URL-Werte. Er wird Attributen mit Hilfe eines von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] bereitgestellten, softwareseitig fest definierten Attributs (Spezialattribut), zugeordnet.", "smw-property-predefined-qty": "„$1“ ist ein [[Special:Types/Quantity|Datentyp]] für Datenwerte mit Maßangaben. Er wird Attributen mit Hilfe eines von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] bereitgestellten, softwareseitig fest definierten Attributs (Spezialattribut), zugeordnet.", "smw-datavalue-time-invalid-offset-zone-usage": "Der Datenwert „$1“ enthält eine Angabe zur Zeitverschiebung und einen Zeitzonenidentifikator, die nicht unterstützt werden.", "smw-datavalue-time-invalid-values": "Der Datenwert „$1“ enthält nicht interpretierbare Informationen in Form von „$2“.", "smw-datavalue-time-invalid-date-components-common": "Der Datenwert „$1“ enthält einige nicht interpretierbare Informationen.", "smw-datavalue-time-invalid-date-components-dash": "Der Datenwert „$1“ enthält einen Gedankenstrich oder andere für die Interpretation der Datumsangabe ungültige Zeichen.", "smw-datavalue-time-invalid-date-components-empty": "Der Datenwert „$1“ enthält nicht alle Bestandteile.", "smw-datavalue-time-invalid-date-components-three": "Der Datenwert „$1“ besteht aus mehr als den drei für die Interpretation der Datumsangabe erforderlichen Bestandteilen.", "smw-datavalue-time-invalid-date-components-sequence": "Der Datenwert „$1“ enthält eine für die Interpretation einer Datumsangabe ungültige Sequenz.", "smw-datavalue-time-invalid-ampm": "Der Datenwert „$1“ enthält „$2“ als Stundenangabe, die für die 2-mal-12-Stunden-Zählung ungültig ist.", "smw-datavalue-time-invalid-jd": "Der Datenwert „$1“ entspricht keiner gültigen Zahl für ein julianisches Datum. „$2“ wurde ausgegeben.", "smw-datavalue-time-invalid-prehistoric": "Der Datenwert „$1“ entspricht keiner gültigen Angabe für ein prähistorisches Datum. Es könnte bspw. mehr als nur eine Jahreszahl oder ein unzutreffendes Kalendermodell angegeben worden sein.", "smw-datavalue-time-invalid": "Der Datenwert „$1“ entspricht keiner gültigen Angabe für ein Datum oder einen Zeitpunkt. „$2“ wurde ausgegeben.", "smw-datavalue-external-formatter-uri-missing-placeholder": "Der für das Erstellen der URI notwendige Datenwert für den Platzhalter „$1“ ist nicht vorhanden.", "smw-datavalue-external-formatter-invalid-uri": "Der Datenwert „$1“ bescheibt eine ungültige URL.", "smw-datavalue-external-identifier-formatter-missing": "Dem Attribut wurde keine [[Property:External formatter uri|Anweisung zur Generierung einer URI]] zugewiesen.", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "Die Kennung der externen URI „$1“ besteht aus mehreren zusammengesetzten Feldern. Dem aktuell hinterlegten Wert „$2“ fehlt mindestens ein Parameter, um die Anforderung der Kennung zu erfüllen.", "smw-datavalue-keyword-maximum-length": "Das Stichwort hat die maximale Länge von {{PLURAL:$1|einem|$1}} Zeichen überschritten.", "smw-property-predefined-eid": "„$1“ ist ein [[Special:Types/External identifier|Datentyp]] für Anweisungen zum Generieren einer URI sowie ein softwareseitig fest definiertes Attribut. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-peid": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die externe Kennung zum Generieren einer URI speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-pefu": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Anweisungen zum Generieren einer URI zusammen mit einem Platzhalter speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-pefu": "Die URI muss einen Platzhalter enthalten, damit [[Special:Types/External identifier|externe Identifikatoren]] korrekt zur Erstellung einer gültigen Referenzierung positioniert werden können.", "smw-type-eid": "„$1“ ist eine Variante des Datentyps [[Special:Types/Text|„Text“]]. Die zugewiesenen Attribute müssen [[Property:External formatter uri|Anweisungen zum Generieren einer URI]] enthalten.", "smw-property-predefined-keyw": "„$1“ ist ein [[Special:Types/Keyword|Datentyp]], der einen in seiner maximalen Zeichenlänge beschränkten Text normalisiert. Es ist zudem ein softwareseitig fest definiertes Attribut. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-type-keyw": "„$1“ ist eine Variante des Datentyps [[Special:Types/Text|„Text“]], der einen in seiner maximalen Zeichenlänge beschränkten Text normalisiert.", "smw-datavalue-stripmarker-parse-error": "Der angegebene Wert „$1“ enthält [https://en.wikipedia.org/wiki/Help:Strip_markers „Strip markers“ (Markierungen für vom Softwareparser genutzte Platzhalter)] und kann deshalb nicht ausreichend verarbeitet werden.", "smw-datavalue-parse-error": "Der angegebene Wert „$1“ wurde nicht verstanden.", "smw-datavalue-propertylist-invalid-property-key": "Die Attributfolge „$1“ enthielt die ungültige Attributkennung „$2“.", "smw-datavalue-type-invalid-typeuri": "<PERSON><PERSON> dem <PERSON> „$1“ konnte keine gültige URI generiert werden.", "smw-datavalue-wikipage-missing-fragment-context": "„$1“ kann nicht als Wert für eine Seite ohne eine im Kontext vorhandene Seite verwendet werden.", "smw-datavalue-wikipage-invalid-title": "Der angegebene Wert „$1“ enthält für den Datentyp Seite ungültige Zeichen oder ist unvollständig. Er kann deshalb während einer Abfrage oder bei einer Annotation unerwartete Ergebnisse verursachen.", "smw-datavalue-wikipage-property-invalid-title": "Der für das Attribut „$1“ des Datentyps Seite angegebene Wert „$2“ enthält ungültige Zeichen oder ist unvollständig. Er kann deshalb während einer Abfrage oder bei einer Annotation unerwartete Ergebnisse verursachen.", "smw-datavalue-wikipage-empty": "Es ist kein Wert für den Titel einer Seite vorhanden (z.&nbsp;B. <code>[[SomeProperty::]], [[]]</code>) und kann somit nicht als Name oder Teil einer Abfragebedingung verwendet werden.", "smw-type-ref-rec": "„$1“ ist der Datentyp für einen [https://www.semantic-mediawiki.org/wiki/Help:Container Datenverbund], mit dem zusätzliche Informationen, bspw. Quellenangaben, zu einer Attribut-Datenwert-Zuweisung (Annotation) gespeichert werden können.", "smw-datavalue-reference-invalid-fields-definition": "Der Datentyp [[Special:Types/Reference|Referenzierung]] muss aus einer Folge von Attributen bestehen, die das Spezialattribut [https://www.semantic-mediawiki.org/wiki/Help:Spezialattribut_Hat_Komponenten „Hat Komponenten“] verwenden.", "smw-parser-invalid-json-format": "Der JSON-Parse<PERSON> hat den Fehler „$1“ ausgegeben.", "smw-property-preferred-label-language-combination-exists": "„$1“ kann nicht als bevorzugte Bezeichnung verwendet werden, da der Sprache „$2“ bereits die Bezeichnung „$3“ zugewiesen wurde.", "smw-clipboard-copy-link": "Link in die Zwischenablage kopieren", "smw-property-userdefined-fixedtable": "„$1“ wurde als [https://www.semantic-mediawiki.org/wiki/Fixed_properties fest definiertes Attribut] erstellt und konfiguriert. Jede Änderung des zugewiesenen [https://www.semantic-mediawiki.org/wiki/Type_declaration Datentyps] erfordert zwingend entweder die Ausführung des Wartungsskripts \"setupStore.php\" oder die Durchführung der Spezialaufgabe [[Special:SemanticMediaWiki|„Daten neu erstellen“]].", "smw-data-lookup": "<PERSON><PERSON><PERSON> ab…", "smw-data-lookup-with-wait": "<PERSON>s kann einen Moment dauern, bis die Abfrage ausgeführt wurde.", "smw-no-data-available": "<PERSON>s sind keine Daten verfügbar.", "smw-property-req-violation-missing-fields": "Dem Attribut „$1“ fehlt eine erforderliche Deklaration mit Spezialattribut [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields „Hat Komponenten“] für Datentyp „$2“.", "smw-property-req-violation-multiple-fields": "Das Attribut „$1“ enthält mehrere und somit konkurrierende Deklarationen mit dem Spezialattribut [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields „Hat Komponenten“]. Es ist für den Datentyp „$2“ nur eine Deklaration zulässig.", "smw-property-req-violation-missing-formatter-uri": "Die Annotation des Attributs „$1“ ist aufgrund des zugewiesenen Datentyps unvollständig. Das Spezialattribut „Formatierungsanweisung zur externen URI“ mit einem entsprechenden Wert fehlt.", "smw-property-req-violation-predefined-type": "„$1“ ist ein softwareseitig fest definiertes Attribut. Ihm wurde der Datentyp „$2“ zugewiesen, der nicht mit dem Standardtyp dieses Attributs vereinbar ist.", "smw-property-req-violation-import-type": "Es wurde ein Datent<PERSON><PERSON> zu<PERSON>, der mit dem vom importierten Vokabular „$1“ für dieses Attribut definierten Datentyp nicht kompatibel ist. Es ist allgemein nicht erford<PERSON>lich, einen Datentyp festzulegen, da diese Information aus der importierten Definition des Attributs abgerufen wird.", "smw-property-req-violation-change-propagation-locked-error": "Das Attribut „$1“ wurde solcherart verändert, dass die zugeordneten Datenobjekte mit einem [https://www.semantic-mediawiki.org/wiki/Change_propagation Datenänderungsvorgang] neu verarbeitet werden müssen. Daher ist die Seite dieses Attributs bis zu dem Zeitpunkt für Bearbeitungen gesperrt, an dem dieser Vorgang abgeschlossen wurde. Hierdurch werden ggf. mögliche Funktionsstörungen oder widersprüchliche Datenanzeigen verhindert. Da der Datenänderungsvorgang abhängig vom Umfang sowie der Abarbeitungsfrequenz der [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Auftragswarteschlange] ist, kann es einen Moment dauern, bis die Seite freigegeben wird und wieder bearbeitet werden kann.", "smw-property-req-violation-change-propagation-locked-warning": "Das Attribut „$1“ wurde solcherart verändert, dass die zugeordneten Datenobjekte mit einem [https://www.semantic-mediawiki.org/wiki/Change_propagation Datenänderungsvorgang] neu verarbeitet werden müssen. Daher sollte die Seite dieses Attributs nicht bearbeitet werden, bis dieser Vorgang abgeschlossen wurde. Hierdurch werden ggf. mögliche Funktionsstörungen oder widersprüchliche Datenanzeigen verhindert. Da der Datenänderungsvorgang abhängig vom Umfang sowie der Abarbeitungsfrequenz der [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Auftragswarteschlange] ist, kann es einen Moment dauern, bis die Seite wieder bearbeitet werden kann.", "smw-property-req-violation-change-propagation-pending": "Es {{PLURAL:$1|muss|müssen}} noch ungefähr [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|ein Datenänderungsauftrag|$1 Datenänderungsaufträge}}] durchgeführt werden. Daher sollte die Seite dieses Attributs so lange nicht bearbeitet werden, bis kein Datenänderungsauftrag mehr vorhanden ist. Hierdurch werden ggf. mögliche Funktionsstörungen oder widersprüchliche Datenanzeigen verhindert.", "smw-property-req-violation-missing-maps-extension": "Semantic MediaWiki konnte nicht erkennen, ob die Softwareerweiterung [https://www.semantic-mediawiki.org/wiki/Extension:Maps „Maps“] installiert ist. Da diese eine Voraussetzung für das Funktionieren von Attributen mit diesem Datentyp ist, ist dieses Attribut in seiner Nutzbarkeit eingeschränkt.", "smw-property-req-violation-type": "Das Attribut enthält konkurrierende Datentypangaben, die zu ungültigen Annotationen von Datenwerten führen könnten. Es muss sichergestellt werden, dass dem Attribut nur ein zutreffender Datentyp zugewiesen wird.", "smw-property-req-error-list": "Zu diesem Attribut existieren die folgenden Fehlermeldungen oder Warnhinweise:", "smw-property-req-violation-parent-type": "Das Attribut „$1“ und das zugehörige übergeordnete Attribut „$2“ haben unterschiedliche Datentypen.", "smw-property-req-violation-forced-removal-annotated-type": "Die Funktion der [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance verpflichtenden Vererbung des übergeordneten Datentyps] ist aktiv. Der festgelegte Datentyp des Attributs „$1“ stimmt nicht nicht mit dem Datentyp „$2“ des übergeordneten Attributs überein. Er wurde daher softwareintern so angepasst, dass beide Datentypen übereinstimmen. Es wird empfohlen, den Datentyp des Attributs korrekt festzulegen, damit diese Fehlermeldung nicht mehr angezeigt wird und der Datentype nicht mehr softwareintern angepasst werden muss.", "smw-change-propagation-protection": "Diese Seite ist gesperrt, um unbeabsichtigte Änderungen an Daten zu verhindern, während ein [https://www.semantic-mediawiki.org/wiki/Change_propagation Datenänderungsvorgang] ausgeführt wird. Da der Datenänderungsvorgang abhängig vom Umfang sowie der Abarbeitungsfrequenz der [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Auftragswarteschlange] ist, kann es einen Moment dauern, bis die Seite freigegeben wird und wieder bearbeitet werden kann.", "smw-category-change-propagation-locked-error": "Die Kategorie „$1“ wurde solcherart verändert, dass die zugeordneten Datenobjekte mit einem [https://www.semantic-mediawiki.org/wiki/Change_propagation Datenänderungsvorgang] neu verarbeitet werden müssen. Daher ist die Seite der Kategorie bis zu dem Zeitpunkt für Bearbeitungen gesperrt, an dem dieser Vorgang abgeschlossen wurde. Hierdurch werden ggf. mögliche Funktionsstörungen oder widersprüchliche Datenanzeigen verhindert. Da der Datenänderungsvorgang abhängig vom Umfang sowie der Abarbeitungsfrequenz der [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Auftragswarteschlange] ist, kann es einen Moment dauern, bis die Seite freigegeben wird und wieder bearbeitet werden kann.", "smw-category-change-propagation-locked-warning": "Die Kategorie „$1“ wurde solcherart verändert, dass die zugeordneten Datenobjekte mit einem [https://www.semantic-mediawiki.org/wiki/Change_propagation Datenänderungsvorgang] neu verarbeitet werden müssen. Daher sollte die Seite dieser Kategorie nicht bearbeitet werden, bis dieser Vorgang abgeschlossen wurde. Hierdurch werden ggf. mögliche Funktionsstörungen oder widersprüchliche Datenanzeigen verhindert. Da der Datenänderungsvorgang abhängig vom Umfang sowie der Abarbeitungsfrequenz der [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Auftragswarteschlange] ist, kann es einen Moment dauern, bis die Seite wieder bearbeitet werden kann.", "smw-category-change-propagation-pending": "Es {{PLURAL:$1|muss|müssen}} noch ungefähr [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|ein Datenänderungsauftrag|$1 Datenänderungsaufträge}}] durchgeführt werden. Daher sollte die Seite dieser Kategorie so lange nicht bearbeitet werden, bis kein Datenänderungsauftrag mehr vorhanden ist. Hierdurch werden ggf. mögliche Funktionsstörungen oder widersprüchliche Datenanzeigen verhindert.", "smw-category-invalid-value-assignment": "„$1“ wurde nicht als gültige Kategorie oder als Annotation eines gültigen Wertes erkannt.", "protect-level-smw-pageedit": "<PERSON>ur Benutzern mit der Berechtigung zum Bearbeiten von Seiten erlauben (Semantic MediaWiki)", "smw-create-protection": "Das Erstellen des Attributs „$1“ ist auf Benutzer der hierfür vorgesehenen [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups Benutzergruppe] oder mit der hierfür benötigten Berechtigung „$2“ beschränkt, während der [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Berechtigungsmodus] aktiviert ist.", "smw-create-protection-exists": "Das Ändern des Attributs „$1“ ist auf Benutzer der hierfür vorgesehenen [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups Benutzergruppe] oder mit der hierfür benötigten Berechtigung „$2“ beschränkt, während der [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Berechtigungsmodus] aktiviert ist.", "smw-edit-protection": "Diese Seite ist [[Property:Is edit protected|bearbeitungsgeschützt]], um unbeabsichtigte Veränderungen an Inhalten und Daten zu verhindern. Diese kann nur von Benutzern der hierfür vorgesehenen [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups Benutzergruppe] oder mit der hierfür benötigten Berechtigung („$1“) bearbeitet werden.", "smw-edit-protection-disabled": "Der Bearbeitungsschutz mit Hilfe einer Annotation wurde deaktiviert. Da<PERSON> kann das Spezialattribut „$1“ nicht verwendet werden, um diese Seite vor unerwünschten Bearbeitungen zu schützen.", "smw-edit-protection-auto-update": "Semantic MediaWiki hat den Schutzstatus der Seite übereinstimmend mit dem Wert des Attributs „Ist bearbeitungsgeschützt“ aktualisiert.", "smw-edit-protection-enabled": "Diese Seite wurde mit Semantic MediaWiki vor Bearbeitungen geschützt.", "smw-patternedit-protection": "Diese Seite ist bearbeitungsgeschützt, um unbeabsichtigte Veränderungen an Inhalten zu verhindern. Diese kann deshalb nur von Benutzern mit der hierfür notwendigen Berechtigung „smw-patternedit“ bearbeitet werden.", "smw-property-predefined-edip": "„$1“ ist ein softwareseitig fest definiertes Attribut, das den Status des Bearbeitungsschutzes einer Seite speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-edip": "<PERSON><PERSON> darf dieses Attribut zu einer Seite hinzufügen und so den Bearbeitungsschutz festlegen. Indes dürfen hernach nur Benutzer mit einer entsprechenden Berechtigung die auf diese Weise geschützte Seite bearbeiten und den Bearbeitungsschutz ggf. aufheben.", "smw-query-reference-link-label": "Abfragereferenz", "smw-format-datatable-emptytable": "Es sind keine Ergebnisse vorhanden.", "smw-format-datatable-info": "Es werden die Ergebnisse _START_ bis _END_ von insgesamt _TOTAL_ Ergebnissen angezeigt.", "smw-format-datatable-infoempty": "Es werden keine Ergebnisse angezeigt.", "smw-format-datatable-infofiltered": "(aus insgesamt _MAX_ Ergebnissen gefiltert)", "smw-format-datatable-infothousands": ".", "smw-format-datatable-lengthmenu": "Zeige _MENU_ Einträge", "smw-format-datatable-loadingrecords": "<PERSON>de …", "smw-format-datatable-processing": "Verarbeite …", "smw-format-datatable-search": "Suche:", "smw-format-datatable-zerorecords": "Es wurden keine übereinstimmenden Ergebnisse gefunden.", "smw-format-datatable-first": "<PERSON><PERSON><PERSON>", "smw-format-datatable-last": "Letzte", "smw-format-datatable-next": "Nächste", "smw-format-datatable-previous": "<PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-sortascending": ": aktiv<PERSON><PERSON>, um die Spalte aufsteigend zu sortieren", "smw-format-datatable-sortdescending": ": aktiv<PERSON><PERSON>, um die Spalte absteigend zu sortieren", "smw-format-datatable-toolbar-export": "Exportieren", "smw-format-list-other-fields-open": "&#32;(", "smw-category-invalid-redirect-target": "Die Kategorie „$1“ enthält ein Weiterleitungsziel, das sich nicht im Namensraum Kategorie befindet und daher ungültig ist.", "smw-parser-function-expensive-execution-limit": "Die Parserfunktion hat die Grenze für aufwendige Ausführungen erreicht. Siehe hierzu die Seite zum [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit Konfigurationsparameter <code>$smwgQExpensiveExecutionLimit</code>].", "smw-postproc-queryref": "Die Software Semantic MediaWiki wird diese Seite neu laden und so eine Aktualisierung der Daten bewirken.", "apihelp-smwinfo-summary": "API-Modul zum Abrufen statistischer Daten und weiterer Metainformationen bezüglich der Nutzung von Semantic MediaWiki.", "apihelp-ask-summary": "API-Modul zum Abfragen eines Wikis mit der Abfragesprache von Semantic MediaWiki.", "apihelp-askargs-summary": "API-Modul zum Abfragen eines Wikis mit der Abfragesprache von Semantic MediaWiki als Liste von Bedingungen, Ausgabeanweisungen und Parametern.", "apihelp-browsebyproperty-summary": "API-Modul zum Abrufen von Informationen zu einem Attribut oder einer Liste von Attributen.", "apihelp-browsebysubject-summary": "API-Modul zum Abrufen von Informationen zu einem Objekt (Seite oder Unterobjekt).", "apihelp-smwtask-summary": "API-Modul zum Ausführen Semantic MediaWiki betreffender Aufgaben, das nur von der Software selbst und nicht von Außen genutzt wird.", "apihelp-smwbrowse-summary": "API-Modul zur Unterstützung von Aktivitäten beim Browsen für unterschiedliche Objekttypen in Semantic MediaWiki.", "apihelp-ask-parameter-api-version": "Ausgabeformat:\n;2:Rückwärtskompatibles Format, das {} für Ergebnislisten verwendet.\n;3:Experimentelles Format, das [] für Ergebnislisten verwendet.", "apihelp-smwtask-param-task": "Definiert den Aufgabentyp", "apihelp-smwtask-param-params": "Im JSON-Format codierte Parameter, die mit den Anforderungen des ausgewählten Aufgabentyps übereinstimmen", "smw-apihelp-smwtask-example-update": "Beispiel zur Ausführung einer Aktualisierungsaufgabe bezüglich eines bestimmten Datensubjekts:", "smw-api-invalid-parameters": "Ungültige Parameter: „$1“", "smw-parser-recursion-level-exceeded": "Die Obergrenze von $1 Rekursionen wurde während des Parsens überschritten. Es wird empfohlen, die Struktur der Vorlage zu überprüfen oder ggf. den Konfigurationsparameter <code>$maxRecursionDepth</code> anzupassen.", "smw-property-page-list-count": "Unterhalb {{PLURAL:$1|wird eine Seite|werden $1 Seiten}} ange<PERSON><PERSON><PERSON>, auf denen für dieses Attribut ein Datenwert gespeichert wurde.", "smw-property-page-list-search-count": "Es {{PLURAL:$1|wird eine Seite|werden $1 Seiten}} ange<PERSON><PERSON><PERSON>, auf denen für dieses Attribut der Datenwert „$2“ gespeichert wurde.", "smw-property-page-filter-note": "Der [https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter Filter für die Suche nach Datenwerten zu Attributen] unterstützt die Nutzung von [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions Abfrageausdrücken] wie bpsw. <code>~</code> oder <code>!</code>. Je nach genutzter [https://www.semantic-mediawiki.org/wiki/Query_engine Abfragedatenbank] werden auch die groß- und kleinschreibungsunabhängige Suche oder auch folgende weitere Abfrageausdrücke unterstützt:\n\n* <code>in:</code>: Das Ergebnis soll den angegebenen Begriff enthalten, wie bspw. <code>in:Foo</code>\n\n* <code>not:</code>: Das Ergebnis soll den angegebenen Begriff nicht enthalten, wie bpsw. <code>not:Bar</code>", "smw-property-reserved-category": "<PERSON><PERSON><PERSON>", "smw-category": "<PERSON><PERSON><PERSON>", "smw-datavalue-uri-invalid-scheme": "„$1“ ist nicht als zulässiges URI-Schema gelistet.", "smw-datavalue-uri-invalid-authority-path-component": "„$1“ enthält mit „$2“ eine ungültige Zuständigkeits- oder Pfadkomponente.", "smw-browse-property-group-title": "Attributgruppe", "smw-browse-property-group-label": "Attributgruppenbezeichnung", "smw-browse-property-group-description": "Attributgruppenbeschreibung", "smw-property-predefined-ppgr": "„$1“ ist ein softwareseitig fest definiertes Attribut, das Seiten (hauptsächlich Kategorien) bestimmt, die zum Gruppieren von Attributen verwendet werden. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-filter": "Filter", "smw-section-expand": "Abschnitt ausklappen", "smw-section-collapse": "Abschnitt einklappen", "smw-ask-format-help-link": "Ausgabeformat [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-personal-jobqueue-watchlist": "Beobachtungsliste zur Auftragswarteschlange", "smw-personal-jobqueue-watchlist-explain": "Bei den Zahlen handelt es sich um ungefähre Angaben zu den noch offenen Einträgen in der Auftragswarteschlange, die noch ausgeführt werden müssen.", "smw-property-predefined-label-skey": "Sortierschlüssel", "smw-processing": "Verarbeite Daten…", "smw-loading": "Wird geladen…", "smw-fetching": "Abrufen…", "smw-preparing": "Vorbereiten…", "smw-expand": "Ausklappen", "smw-collapse": "Einklappen", "smw-copy": "<PERSON><PERSON><PERSON>", "smw-copy-clipboard-title": "Ko<PERSON>rt den Inhalt in die Zwischenablage", "smw-jsonview-expand-title": "K<PERSON><PERSON> die JSON-Ansicht aus", "smw-jsonview-collapse-title": "<PERSON><PERSON><PERSON> die JSON-Ansicht ein", "smw-jsonview-search-label": "Suche:", "smw-redirect-target-unresolvable": "Das Weiterleitungsziel ist nicht auflösbar. Grund: „$1“", "smw-types-title": "Typ: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Das Ändern des Inhaltsmodells der [https://www.semantic-mediawiki.org/wiki/Help:Schema Seite zu einem Schema] ist nicht möglich.", "smw-schema-namespace-edit-protection": "Diese Seite ist bearbeitungsgeschützt, um unbeabsichtigte Veränderungen an Inhalten zu verhindern. Diese kann deshalb nur von Benutzern mit der hierfür notwendigen Berechtigung „smw-schemaedit“ bearbeitet werden.", "smw-schema-namespace-edit-protection-by-import-performer": "Diese Seite wurde mit einem [https://www.semantic-mediawiki.org/wiki/Import_performer Importwerkzeug] importiert. Der Inhalt dieser Seite kann daher nur von den hier angegebenen Benutzern verändert werden.", "smw-schema-error-title": "{{PLURAL:$1|Validierungsfehler}}", "smw-schema-error-schema": "Die Spezifikation '''$1''' und dessen Validierung für das aktuelle Schema hat die folgenden Widersprüche ergeben:", "smw-schema-error-miscellaneous": "<PERSON><PERSON><PERSON> ($1)", "smw-schema-error-validation-json-validator-inaccessible": "Der JSON-Validator „<b>$1</b>“ ist nicht aufrufbar oder wurde nicht installiert. Dies ist der Grund, warum die Datei „$2“ nicht verarbeitet werden kann. Dies verhindert, daß die aktuelle Seite verändert oder gespeichert werden kann.", "smw-schema-error-validation-file-inaccessible": "Auf die Validierungsdatei „$1“ kann nicht zugegriffen werden.", "smw-schema-error-violation": "[„$1“, „$2“]", "smw-schema-error-type-missing": "Dem Inhalt fehlt die Angabe eines Typs, um im [https://www.semantic-mediawiki.org/wiki/Help:Schema Namensraum für Schemata] erkannt und verwendet werden zu können.", "smw-schema-error-type-unknown": "<PERSON> Typ „$1“ ist unbekannt und kann daher nicht im [https://www.semantic-mediawiki.org/wiki/Help:Schema Namensraum für Schemata] verwendet werden.", "smw-schema-error-json": "JSON-Fehler: „$1“", "smw-schema-error-input": "Das System der Eingabevalidierung hat Probleme festgestellt. Diese müssen behoben werden, bevor das Schema gespeichert werden kann. Die [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling Hilfeseite über die Handhabung von Fehlern bei der Erstellung von Schemata] bietet Hinweise und Ratschläge zur Behebung der Probleme bei deren Erfassung.", "smw-schema-error-input-schema": "Entsprechend dem Validierungsschema '''$1''' wurden Inkonsistenzen festgestellt. Diese müssen behoben werden, bevor das Schema gespeichert werden kann. Die [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling Hilfeseite über die Handhabung von Fehlern bei der Erstellung von Schemata] bietet Hinweise und Ratschläge zur Behebung der Probleme bei deren Erfassung.", "smw-schema-error-title-prefix": "<PERSON><PERSON> Sc<PERSON>aty<PERSON>, daß der Titel des Schemas das Präfix „$1“ nutzt.", "smw-schema-validation-error": "Der Datentype „$1“ ist unbekannt und kann nicht für Inhalte im [https://www.semantic-mediawiki.org/wiki/Help:Schema Namensraum „smw/schema“] verwendet werden.", "smw-schema-validation-schema-title": "JSON-Schema", "smw-schema-summary-title": "Zusammenfassung", "smw-schema-title": "<PERSON><PERSON><PERSON>", "smw-schema-usage": "Annotationen", "smw-schema-type": "Schematyp", "smw-schema-type-description": "Beschreibung des Schematyps", "smw-schema-description": "Beschreibung des Schemas", "smw-schema-description-link-format-schema": "Dieser Schematyp ermöglicht die Definition kontextsensitiver Links, entsprechend der Festlegung mit dem Attribut [[Property:Formatter schema|Formatierungsregel]].", "smw-schema-description-search-form-schema": "Dieser Schematyp ermöglicht die Definition von Eingabeformularen und Inhaltsausprägungen für das [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch erweiterte Suchprofil]. Es enthält Festlegungen zur Erstellung von Eingabefeldern, zu Standardnamensräumen oder zu Suchausdrücken für Suchanfragen.", "smw-schema-description-property-profile-schema": "Dieser Schematyp ermöglicht die Definition von Profilen, die Merkmale für Attribute und deren Datenwertzuordnungen festlegen.", "smw-schema-description-facetedsearch-profile-schema": "Dieser Schematyp unterstützt die Definition von Profilen, die als Teil der Umgebung für die [[Special:FacetedSearch|Facettensuche]] verwendet werden.", "smw-schema-description-property-group-schema": "Dieser Schematyp ermöglicht die Definition von [https://www.semantic-mediawiki.org/wiki/Help:Property_group Attributgruppen], mit denen die Anzeige der [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse Spezialseite zum Durchsuchen von Annotationen] strukturiert und somit übersichtlicher gestaltet werden kann.", "smw-schema-description-property-constraint-schema": "Dieser Schematyp ermöglicht die Definition von Einschränkungsregeln für Attribute sowie ihnen zugewiesenen Datenwerten.", "smw-schema-description-class-constraint-schema": "Dieser Schematyp ermöglicht die Definition von Einschränkungsregeln für Objekttypen (Klassen, d.h. Kategorien).", "smw-schema-tag": "{{PLURAL:$1|Schemakennzeichen}}", "smw-property-predefined-constraint-schema": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Definition des Schemas zur Einschränkung zulässiger Datenwerte speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-schema-desc": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Schemabeschreibung speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-schema-def": "„$1“ ist ein softwareseitig fest definiertes Attribut, das die Definition des Schemas speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-schema-tag": "„$1“ ist ein softwareseitig fest definiertes Attribut, das das Kennzeichen des Schemas speichert. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-schema-tag": "<PERSON><PERSON> Bezeichnung, die Schemata ähnlicher Inhalte oder Ausprägungen kennzeichnet.", "smw-property-predefined-schema-type": "„$1“ ist ein softwareseitig fest definiertes Attribut, das einen Schematyp beschreibt, mit dem Gruppen von Schemata unterschieden werden können. Es wird von [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] zur Verfügung gestellt.", "smw-property-predefined-long-schema-type": "<PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type Schematyp] bietet eine eigene Struktur von Syntaxelementen und zugehörigen Anwendungsregeln. Er kann mit einem [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation Validierungmodell] beschrieben werden.", "smw-ask-title-keyword-type": "Stichwortsuche", "smw-ask-message-keyword-type": "Diese Suche entspricht der Abfragebedingung <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Es konnte keine Verbindung zur externen Datenquelle „$1“ hergestellt werden.", "smw-remote-source-disabled": "Die externe Datenquelle „$1“ gestattet keine Fernabfrage.", "smw-remote-source-unmatched-id": "Die externe Datenquelle „$1“ nutzt keine Version von Semantic MediaWiki, die eine Fernabfrage unterstützt.", "smw-remote-request-note": "Das Ergebnis wird von der externen Datenquelle „$1“ abgefragt. Es ist wahrscheinlich, dass der erstellte Inhalt Informationen enthält, die nicht innerhalb des aktuellen Wikis verfügbar sind.", "smw-remote-request-note-cached": "Das aus der externen Datenquelle „$1“ abgefragte Ergebnis ist '''gecacht'''. Es ist wahr<PERSON><PERSON>, dass der erstellte Inhalt Informationen enthält, die nicht innerhalb des aktuellen Wikis verfügbar sind.", "smw-parameter-missing": "Der Parameter „$1“ fehlt.", "smw-property-tab-usage": "Annotationen", "smw-property-tab-profile-schema": "Profilschema", "smw-property-tab-redirects": "Synonyme", "smw-property-tab-subproperties": "Unterattribute", "smw-property-tab-errors": "Fehlerhafte Annotationen", "smw-property-tab-constraint-schema": "Dateneinschränkungsschema", "smw-property-tab-constraint-schema-title": "Erstelltes Dateneinschränkungsschema", "smw-property-tab-specification": "… mehr", "smw-concept-tab-list": "Liste", "smw-concept-tab-errors": "<PERSON><PERSON>", "smw-ask-tab-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-extra": "Extra", "smw-ask-tab-debug": "Fehleranalyse", "smw-ask-tab-code": "Code", "smw-install-incomplete-tasks-title": "Unvollständig erledigte administrative Aufgaben", "smw-install-incomplete-intro": "Es existieren $2 unvollständige oder [[Special:PendingTaskList|ausstehende]] {{PLURAL:$2|Wartungsaufgabe|Wartungsaufgaben}} zur {{PLURAL:$1|Installation|Aktualisierung}} von [https://www.semantic-mediawiki.org Semantic MediaWiki]. Ein Systemadministrator oder Benutzer mit entsprechenden Berechtigungen kann diese ausführen. Zum Verhindern von Dateninkonsistenzen sollte dies erfolgen, bevor dem Wiki neue Daten hinzugefügt werden.", "smw-install-incomplete-intro-note": "<PERSON><PERSON> Na<PERSON> wird nicht mehr angezeigt werden, sobald all notwendigen Aufgaben ausgeführt wurden.", "smw-pendingtasks-intro-empty": "Im <PERSON> mit Semantic MediaWiki wurden keine Wartungsaufgaben als unvollständig oder ausstehend  eingestuft.", "smw-pendingtasks-intro": "Diese Seite zeigt Informationen zu Wartungsaufgaben an, die im Zusammenhang mit Semantic MediaWiki entweder aus ausstehend oder unvollständig eingestuft wurden.", "smw-pendingtasks-setup-no-tasks-intro": "Die Installation oder Aktualisierung wurde beendet. Aktuell sind keine ausstehenden Wartungsaufgaben vorhanden.", "smw-pendingtasks-tab-setup": "Installation", "smw-updateentitycollation-incomplete": "Der Konfigurationsparameter <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> wurde vor kurzem angepasst. In der Folge erfordert dies das Ausführen des Wartungsskripts [https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php „updateEntityCollation.php“], damit die vorhanden Datensätze aktualisiert werden und somit den korrekten Wert zum Sortieren erhalten.", "smw-updateentitycountmap-incomplete": "<PERSON>feld <code>smw_countmap</code> wurde mit einer neuen Version von Semantic MediaWiki eingeführt. In der Folge erfordert dies das Ausführen des Wartungsskripts [https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php „updateEntityCountMap.php“], damit die vorhanden Datensätze aktualisiert und somit weiterhin korrekt genutzt werden können.", "smw-populatehashfield-incomplete": "Das Befüllen des Datentabellenfelds <code>smw_hash</code> wurde während der Installation oder Aktualisierung von Semantic MediaWiki übersprungen. Das Wartungsskript [https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php „populateHashField.php“] muss daher ausgeführt werden.", "smw-install-incomplete-populate-hash-field": "Das Befüllen des Datentabellenfelds <code>smw_hash</code> wurde während der Installation oder Aktualisierung von Semantic MediaWiki abgebrochen. Das Wartungsskript [https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php „populateHashField.php“] muss daher ausgeführt werden.", "smw-install-incomplete-elasticstore-indexrebuild": "Elasticsearch wurde als [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore Datenbank] festgelegt. In der Folge erfordert dies das Ausführen des Wartungsskripts [https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php „rebuildElasticIndex.php“], damit Daten im Wiki genutzt werden können.", "smw-elastic-rebuildelasticindex-run-incomplete": "Elasticsearch wurde als [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore Datenbank] festgelegt. In der Folge erfordert dies das Ausführen des Wartungsskripts [https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php „rebuildElasticIndex.php“], damit Daten im Wiki genutzt werden können.", "smw-pendingtasks-setup-intro": "Während der {{PLURAL:$1|Installation|Aktualisierung}} von Semantic MediaWiki wurden die folgenden Wartungsaufgaben [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade nicht vollständig ausgeführt]. Ein Systemadministrator oder ein Nutzer mit entsprechenden Berechtigungen sollte diese Wartungsaufgaben nun ausführen, damit die Nutzer des Wikis weiterhin neue Inhalte erstellen und bestehende Inhalte verändern können.", "smw-pendingtasks-setup-tasks": "Wartungsaufgaben", "smw-filter-count": "Anzahl gefilterter Annotationen", "smw-es-replication-check": "Datenreplikationsprüfung (Elasticsearch)", "smw-es-replication-error": "Datenreplikationsproblem", "smw-es-replication-file-ingest-error": "Dateiindexierungsproblem", "smw-es-replication-maintenance-mode": "Elasticsearch Wartungsmodus", "smw-es-replication-error-missing-id": "Während der Überwachung der Datenreplikation wurde festgestellt, dass die Seite „$1“ (Kennung: <code>$2</code>) nicht in der Elasticsearch-Datenbank enthalten ist.", "smw-es-replication-error-divergent-date": "Während der Überwachung der Datenreplikation wurde festgestellt, dass die Seite „$1“ (Kennung: $2) in der Elasticsearch-Datenbank ein abweichendes <b>Änderungsdatum</b> aufweist.", "smw-es-replication-error-divergent-date-short": "Die folgenden Datumsinformationen wurden für den Vergleich verwendet:", "smw-es-replication-error-divergent-date-detail": "Änderungsdatum:\n* Elasticsearch: $1 \n* Datenbank: $2", "smw-es-replication-error-divergent-revision": "Mit Hilfe der Replikationsüberwachung wurde festgestellt, dass die <b>zugehörige Version</b> der Seite $1 (Kennung: $2) eine Abweichung aufweist.", "smw-es-replication-error-divergent-revision-short": "Die folgenden verbundenen Revisionen wurden für den Vergleich verwendet:", "smw-es-replication-error-divergent-revision-detail": "Seitenversion:\n* Elasticsearch: $1 \n* Datenbank: $2", "smw-es-replication-error-maintenance-mode": "Die Replikation von Elasticsearch ist derzeit eingeschränkt, da diese im [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>Wartungsmodus</b>] läuft. Änderungen an Datenobjekten und Seiten sind daher <b>nicht</b> sofort sichtbar. Zudem könnten Abfragen veraltete Informationen ausgeben.", "smw-es-replication-error-no-connection": "Die Replikationsüberwachung kann keine Prüfungen durchführen, da keine Verbindung mit der Elasticsearch-Datenbank hergestellt werden kann.", "smw-es-replication-error-bad-request-exception": "Der Verbindungshandler für Elasticsearch hat einen Fehler ausgegeben (HTTP-Status 400: „Fehlerhafte Anfrage“). Dies deutet auf ein Weitergabeproblem während der Datenreplikation sowie bei Suchanfragen hin.", "smw-es-replication-error-other-exception": "Der Elasticserach-Handler hat den folgenden Fehler ausgegeben: „$1“.", "smw-es-replication-error-suggestions": "<PERSON><PERSON> wird em<PERSON><PERSON><PERSON>, die Seite zu bearbeiten oder deren Cache zu leeren, um die Unstimmigkeit zu beseitigen. Sofern das Problem weiterhin besteht, ist der Elasticsearch-Cluster selbst (Datenzuteilung, Fehlermeldungen, Speicherplatz usw.) zu prüfen.", "smw-es-replication-error-suggestions-maintenance-mode": "Der Systemadministrator des Wikis muß kontaktiert werden, damit geprüft wird, ob derzeit entweder eine [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild Neuerstellung des Index] ausgeführt wird oder der Indexierungsintervall (<code>refresh_interval</code>) ggf. nicht mit dem benötigten Standardwert festgelegt wurde.", "smw-es-replication-error-suggestions-no-connection": "Der Systemadministrator des Wikis muß kontaktiert werden, damit das Problem der unterbrochenen Verbindung zur Elasticsearch-Datenbank behoben werden kann.", "smw-es-replication-error-suggestions-exception": "Die Logbücher von Elasticsearch müssen eingesehen werden, um Informationen zu dessen Status, dessen Indizes und möglichen Problemen wie einer Fehlkonfiguration zu erhalten.", "smw-es-replication-error-file-ingest-missing-file-attachment": "Mit <PERSON><PERSON><PERSON> der Replikationsüberwachung wurde festgestellt, dass der Seite „$1“ die Annotation des [[Property:File attachment|Dateianhangs]] fehlt. Mit dieser Annotation wird angezeigt, dass die Dateiindexierung noch nicht gestartet wurde oder noch nicht beendet ist.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Es muss sichergestellt sein, dass der Auftrag zur [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion Dateiindexierung] erzeugt und ausgeführt wurde, bevor Annotation und Dateiindex zur Verfügung gestellt werden.", "smw-report": "Bericht", "smw-legend": "<PERSON><PERSON>", "smw-datavalue-constraint-schema-category-invalid-type": "Das annotierte Schema „$1“ kann nicht für Kategorien genutzt werden. Es muss der Schematyp „$2“ genutzt werden.", "smw-datavalue-constraint-schema-property-invalid-type": "Das annotierte Schema „$1“ kann nicht für Kategorien genutzt werden. Es muss der Schematyp „$2“ genutzt werden.", "smw-entity-examiner-check": "{{PLURAL:$1|Eine Datenprüfung wird|Datenprüfungen werden}} im Hintergrund ausgeführt.", "smw-entity-examiner-indicator": "Problemanzeige zu Daten", "smw-entity-examiner-deferred-check-awaiting-response": "Die Datenprüfung zu „$1“ warten auf eine Antwort von der Datenbank.", "smw-entity-examiner-deferred-elastic-replication": "Elasticsearch", "smw-entity-examiner-deferred-constraint-error": "Einschränkung", "smw-entity-examiner-associated-revision-mismatch": "Version", "smw-entity-examiner-deferred-fake": "Falschanzeige", "smw-entity-examiner-indicator-suggestions": "<PERSON><PERSON> mit der Datenprüfung {{PLURAL:$1|wurde das folgende Problem|wurden die folgenden Probleme}} ermittelt. Es wird empfohlen {{PLURAL:$1|das Problem|die Probleme}} zu überprüfen und {{PLURAL:$1|eine Aktion|Aktionen}} zu Behebung {{PLURAL:$1|desselben|derselben}} auszuführen.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Einschränkung|Einschränkungen}}", "smw-indicator-revision-mismatch": "Revision", "smw-indicator-revision-mismatch-error": "Der [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner Seitenversionsprüfung] hat beim Vergleich eine Abweichung der Seitenversionen zwischen den Datenbanken von MediaWiki und Semantic MediaWiki festgestellt.", "smw-indicator-revision-mismatch-comment": "Eine Abweichung deutet normalerweise darauf hin, daß eine Softwareaufgabe zur Aktualisierung der Seitenversion, in der Datenbank von Semantic MediaWiki, unterbrochen wurde. Daher sollten die entsprechenden Logdateien des Servers auf Informationen zu möglichen Fehlern geprüft werden.", "smw-facetedsearch-intro-text": "Die [https://www.semantic-mediawiki.org/wiki/Faceted_search <b>Facettensuche</b>] von Semantic MediaWiki bietet Benutzern eine einfache Schnittstelle, um Abfrageergebnisse aus einer Bedingung mit Hilf<PERSON> von Face<PERSON>ichten, die aus abhängigen Eigenschaften und Kategorien erstellt werden, schnell einzugrenzen.", "smw-facetedsearch-intro-tips": "* Verwende <code>category:?</code>, <code>property:?</code> oder <code>concept:?</code>, um verfügbare Kategorien, Eigenschaften oder Konzepte zu finden und ein Bedingungssystem zu erstellen\n* Verwende die #ask-Syntax, um eine Bedingung zu beschreiben (z. B. <code><nowiki>[[Category:Foo]]</nowiki></code>)\n* Verwende \"OR\", \"AND\" oder andere Abfragen-Ausdrücke, um komplexe Bedingungen zu erstellen\n* Ausdrücke wie <code>in:</code> oder <code>phrase:</code> können für die Volltextsuche oder unstrukturierte Suchen verwendet werden, falls das ausgewählte [https://www.semantic-mediawiki.org/wiki/Query_engine Abfragen-System] solche Ausdrücke unterstützt", "smw-facetedsearch-profile-label-default": "Standardprofil", "smw-facetedsearch-intro-tab-explore": "Entdecken", "smw-facetedsearch-intro-tab-search": "<PERSON><PERSON>", "smw-facetedsearch-explore-intro": "<PERSON><PERSON>hle eine Sammlung aus und beginne zu stöbern.", "smw-facetedsearch-profile-options": "Profiloptionen", "smw-facetedsearch-size-options": "Seitenoptionen", "smw-facetedsearch-order-options": "Optionen anordnen", "smw-facetedsearch-format-options": "Anzeigeoptionen", "smw-facetedsearch-format-table": "<PERSON><PERSON><PERSON>", "smw-facetedsearch-input-filter-placeholder": "Filter…", "smw-facetedsearch-no-filters": "<PERSON><PERSON>.", "smw-facetedsearch-no-filter-range": "<PERSON><PERSON>.", "smw-facetedsearch-no-output": "<PERSON>ür das ausgewählte Format \"$1\" war keine Ausgabe verfügbar.", "smw-facetedsearch-clear-filters": "{{PLURAL:$1|Filter}} löschen", "smw-search-placeholder": "<PERSON>e…", "smw-listingcontinuesabbrev": "Fortsetzung", "smw-showingresults": "{{PLURAL:$1|<strong>1</strong> <PERSON>rgebnis|<strong>$1</strong> E<PERSON>bnisse}}, beginnend mit Nummer <strong>$2</strong>."}