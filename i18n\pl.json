{"@metadata": {"authors": ["Alan ffm", "BeginaFelicysym", "Chrumps", "CiaPan", "<PERSON><PERSON><PERSON><PERSON>", "InternerowyGołąb", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Maikking", "Nemo bis", "Odie2", "Ostrzyciel", "PanWor", "Rail", "Railfail536", "<PERSON><PERSON><PERSON>", "SemanticPioneer", "Sociologist", "Sovq", "Sp5uhe", "ToSter", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "WTM", "WaldiSt", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "아라"]}, "smw-desc": "Poprawia dostępność wiki zarówno dla automatów jak i ludzi ([https://www.semantic-mediawiki.org/wiki/Help:User_manual dokumentacja online])", "smw-error": "Błąd", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] zostało zainstalowane i włączone, ale brakuje odpowiedniego [https://www.semantic-mediawiki.org/wiki/Help:Upgrade klucza aktualizacji].", "smw-upgrade-release": "<PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-progress": "Postęp", "smw-upgrade-error-title": "Błąd » Semantic MediaWiki", "smw-upgrade-error-why-title": "Dlaczego widzę tę stronę?", "smw-upgrade-error-how-title": "<PERSON><PERSON> mog<PERSON> ten problem?", "smw-extensionload-error-why-title": "Dlaczego widzę tę stronę?", "smw-extensionload-error-how-title": "<PERSON><PERSON> mog<PERSON> ten problem?", "smw-upgrade-maintenance-why-title": "Dlaczego widzę tę stronę?", "smw-semantics-not-enabled": "Funkcjonalność Semantycznej MediaWiki dla tej wiki nie została włączona.", "smw_viewasrdf": "Kanał RDF", "smw_finallistconjunct": " i", "smw-factbox-head": "... więcej o „$1”", "smw-factbox-facts": "Fak<PERSON>", "smw-factbox-facts-help": "Wyświetla deklaracje i fakty, które zostały utworzone przez użytkownika", "smw-factbox-attachments": "Załączniki", "smw-factbox-attachments-value-unknown": "nd.", "smw-factbox-facts-derived": "Pochodne fakty", "smw-factbox-facts-derived-help": "Przedstawia fakty, które zostały wyprowadzone z zasad lub za pomocą innych technik wnioskowania", "smw_isspecprop": "Ta właś<PERSON><PERSON><PERSON>ć jest unikalna dla tej wiki.", "smw-concept-cache-header": "Użycie pamięci podręcznej", "smw-concept-no-cache": "Brak dostępnej pamięci podręcznej.", "smw_concept_description": "Opis koncepcji „$1”", "smw_no_concept_namespace": "Koncepcje można definiować tylko w przestrzeni nazw Koncepcja:", "smw_multiple_concepts": "Każda strona konceptu może mieć tylko jedną definicję konceptu.", "smw_concept_cache_miss": "Koncepcja „$1” w chwili obecnej nie może zostać użyta, ponieważ konfiguracja wiki wymaga aby została ona przeliczona offline. Jeśli problem nie ustąpi po jaki<PERSON>ś <PERSON>ie, skontaktuj się z <PERSON>em witryny.", "smw_noinvannot": "Nie można przypisać wartości właściwościom odwrotnym.", "version-semantic": "Rozszerzenia semantyczne", "smw_baduri": "<PERSON><PERSON><PERSON>, URI z przestrzeni „$1” nie są w tym miejscu dostępne.", "smw_printername_count": "Wyniki obliczeń", "smw_printername_csv": "eksport CSV", "smw_printername_dsv": "Eksport DSV", "smw_printername_debug": "Śledzenie zap<PERSON>ania (dla specjalistów)", "smw_printername_embedded": "Zagnieżdżone fragmenty strony", "smw_printername_json": "eksport JSON", "smw_printername_list": "Lista", "smw_printername_plainlist": "Zwykła lista", "smw_printername_ol": "Lista numerowana", "smw_printername_ul": "Lista punktowana", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "Szeroka tabela", "smw_printername_template": "Szablon", "smw_printername_templatefile": "Plik szablonu", "smw_printername_rdf": "Eksport RDF", "smw_printername_category": "Kategoria", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-limit": "Maksymalna liczba zwracanych wyników", "smw-paramdesc-offset": "Przesunięcie pierwszego wyniku", "smw-paramdesc-headers": "Wyświetlanie nazw nagłówków i właściwości", "smw-paramdesc-mainlabel": "Etykieta dająca nazwę stronie głównej", "smw-paramdesc-link": "Po<PERSON>ż wartości jako linki", "smw-paramdesc-intro": "Tekst wyświetlany przed wynikami zapytania, je<PERSON><PERSON> są jakie<PERSON>", "smw-paramdesc-outro": "Tekst wyświetlany poniżej wyników zapytania, jeś<PERSON> są jakie<PERSON>", "smw-paramdesc-default": "Tekst wyświetlany jeśli brak jest wyników zapytania", "smw-paramdesc-sep": "Separator oddzielający wyniki", "smw-paramdesc-valuesep": "Separator mię<PERSON><PERSON> wartościami właściwości wyniku", "smw-paramdesc-showsep": "Pokaż separator w górnej części pliku CSV (\"sep=<value>\")", "smw-paramdesc-distribution": "Zamiast wyświetlania wszystkich wartości, policz ich wystąpienia i pokaż to.", "smw-paramdesc-distributionsort": "Sortuj rozkład wartości według liczby wystąpień.", "smw-paramdesc-aggregation": "<PERSON><PERSON><PERSON><PERSON>, do czego powinna się odnosić agregacja", "smw-paramdesc-template": "Na<PERSON><PERSON>, który zostanie wyświetlony na wydrukach", "smw-paramdesc-columns": "Liczba kolumn, w których zostaną wyświetlone wyniki", "smw-paramdesc-userparam": "Wartość przekazywana do szablonu przy każdym jego użyciu", "smw-paramdesc-class": "Dodatkowa klasa CSS do ustawienia dla listy", "smw-paramdesc-introtemplate": "Nazwa szablonu wyświetlana przed wynikami zapytania, jeś<PERSON> są jakieś", "smw-paramdesc-outrotemplate": "Nazwa szablonu wyświetlana poniżej wyników zapytania, jeś<PERSON> są jakieś", "smw-paramdesc-embedformat": "Znacznik HTML używany do definiowania nagłówków", "smw-paramdesc-embedonly": "Nie wyświetlaj nagłówków", "smw-paramdesc-table-class": "Dodatkowa klasa CSS do ustawienia dla tabeli", "smw-paramdesc-table-transpose": "Wyświetlaj nagłówki tabeli pionowo, a wyniki poziomo", "smw-paramdesc-rdfsyntax": "Wykorzystywana składnia RDF", "smw-paramdesc-csv-sep": "Określa separator kolumn", "smw-paramdesc-csv-valuesep": "Określa separator <PERSON><PERSON><PERSON><PERSON>", "smw-paramdesc-csv-bom": "Dodaj BOM (znacznik kolejności bajtów) na początku pliku wynikowego", "smw-paramdesc-dsv-separator": "Użyty separator", "smw-paramdesc-dsv-filename": "Nazwa pliku DSV", "smw-paramdesc-filename": "Nazwa pliku wyjściowego", "smw-smwdoc-description": "Pokazuje tabelę wszystkich parametrów, które mogą być użyte do formatowania określonego wyniku wraz z wartościami domyślnymi i opisami.", "smw-smwdoc-par-format": "Formatuj wynik, do wyświetlenia sparametryzowanej dokumentacji.", "smw-smwdoc-par-parameters": "Które parametry wyświetlić. \"specific\" dla dodanych przez format, \"base\" dla dostępnych we wszystkich formatach oraz \"all\" do obu.", "smw-paramdesc-sort": "Właściwość do sortowania tabeli przez", "smw-paramdesc-order": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sortowania zapytania", "smw-paramdesc-searchlabel": "Treść linku do kolejnych wyników", "smw-paramdesc-named_args": "Nazwa argumentu przekazana do szablonu", "smw-paramdesc-template-arguments": "Ustawia sposób przekazywania nazwanych argumentów do szablonu", "smw-paramdesc-export": "Opcje eksportu", "smw-paramdesc-json-type": "Typ serializacji", "smw-paramdesc-source": "Alternatywne źródło zapytania", "smw-paramdesc-jsonsyntax": "Składnia JSON, która będzie używana", "smw-printername-feed": "kanał RSS i Atom", "smw_iq_disabled": "<PERSON><PERSON><PERSON>, w tym wiki wyłączono możliwość tworzenia zapytań w artykułach.", "smw_iq_moreresults": "&hellip; dalsze wyniki", "smw_parseerror": "<PERSON><PERSON><PERSON> jest niezrozumiała.", "smw_decseparator": ",", "smw_kiloseparator": ".", "smw_notitle": "„$1” nie może być użyte jako nazwa strony.", "smw_noproperty": "„$1” nie może być użyte jako nazwa właściwości w tej wiki.", "smw_wrong_namespace": "Dozwolone są tutaj tylko strony z przestrzeni nazw „$1”.", "smw_manytypes": "Zdefiniowano więcej niż jeden typ dla atrybutu.", "smw_emptystring": "Puste łańcuchy znakowe są niedozwolone.", "smw_notinenum": "„$1” nie znajduje się na liście ($2) [[Property:Allows value|dozwolonych wartości]] dla właściwości „$3”.", "smw_noboolean": "“$1” nie zostało rozpoznane jako war<PERSON> (prawda/fałsz).", "smw_true_words": "prawda,t,yes,y,tak,true", "smw_false_words": "fałsz,f,no,n,nie,false", "smw_nofloat": "„$1” nie jest licz<PERSON>.", "smw_infinite": "Liczby tak duże jak „$1” nie są obsługiwane.", "smw_unitnotallowed": "„$1” nie została zadeklarowana jako dopuszczalna jednostka miary dla tej właściwości.", "smw_nounitsdeclared": "Dla tej właściwości nie zadeklarowano żadnych jednostek miary.", "smw_novalues": "<PERSON><PERSON><PERSON>", "smw_nodatetime": "Data „$1” nie została zrozumiana.", "smw_toomanyclosing": "W zapytaniu jest zbyt wiele wystąpień „$1”.", "smw_noclosingbrackets": "W zapytaniu któryś z podwójnych nawiasów „<nowiki>[[</nowiki>” nie został zamknięty nawiasem „]]”.", "smw_misplacedsymbol": "Symbolu „$1” użyto w niewłaściwym miejscu.", "smw_unexpectedpart": "<PERSON><PERSON><PERSON><PERSON><PERSON> „$1” zapytania jest niezrozumiała.\nWyniki mogą być inne od oczekiwanych.", "smw_emptysubquery": "Podzapytanie nie ma prawidłowych warunków do wykonania.", "smw_misplacedsubquery": "Podzapytanie zostało użyte w niedozwolonym miejscu.", "smw_valuesubquery": "Podzapytania nie są dozwolone jako wartości właściwości „$1”.", "smw_badqueryatom": "<PERSON><PERSON><PERSON><PERSON><PERSON> zapyt<PERSON> „<nowiki>[[…]]</nowiki>” nie została zrozumiana.", "smw_propvalueproblem": "<PERSON><PERSON><PERSON><PERSON> „$1” nie została rozpoznana.", "smw_noqueryfeature": "Niektóre elementy zapytania nie są obsługiwane na tej wiki, pominięto część zapytania ($1).", "smw_noconjunctions": "Koniunkcje w zapytaniach nie są obsługiwane na tej wiki, pominięto część zapytania ($1).", "smw_nodisjunctions": "Alternatywy w zapytaniach nie są obsługiwane na tej wiki, pominięto część zapytania ($1).", "smw_querytoolarge": "{{PLURAL:$2|Następujący warunek zapytania nie mógł zostać uwzględniony|Następujące warunki zapytania nie mogły zostać uwzględnione}} ze względu na ograniczenia wiki w wielkości lub głębokości zapytania: <code>$1</code>.", "smw_notemplategiven": "Na<PERSON>ży podać parametr „template”, aby to zapytanie zadziałało.", "smw_db_sparqlqueryproblem": "Nie można uzyskać wyniku na zapytanie z bazy danych SPARQL. Ten błąd może być tymczasowy lub wskazywać na błąd w oprogramowaniu bazy danych.", "smw_db_sparqlqueryincomplete": "Odpowiedź na zapytanie okazała się zbyt trudna i została przerwana. Niektórych wyników może brakować. <PERSON><PERSON><PERSON> to możliwe, spróbuj użyć prostszego zapytania.", "smw_type_header": "Atrybuty typu “$1”", "smw_typearticlecount": "Pokazano $1 {{PLURAL:$1|atrybut używający|atrybuty używające|atrybutów używających}} tego typu.", "smw_attribute_header": "Strony używające atrybutu “$1”", "smw_attributearticlecount": "Pokazano $1 {{PLURAL:$1|stronę używającą|strony używające|stron używających}} tego atrybutu.", "smw-propertylist-subproperty-header": "Podwłaściwości", "smw-propertylist-redirect-header": "<PERSON><PERSON><PERSON><PERSON>", "smw-propertylist-error-header": "Strony z nieprawidłowymi przypisaniami", "smw-propertylist-count": "Wyświetlanie $1 {{PLURAL:$1|powiązanego|powiązanych}} {{PLURAL:$1|obiektu|obiektów}}.", "specialpages-group-smw_group": "Semantyczna MediaWiki", "specialpages-group-smw_group-maintenance": "Ko<PERSON>r<PERSON><PERSON><PERSON>", "specialpages-group-smw_group-properties-concepts-types": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ci, koncepty i typy", "specialpages-group-smw_group-search": "Przeglądanie i wyszukiwanie", "exportrdf": "Eksport stron do RDF", "smw_exportrdf_docu": "Ta strona pozwala eksportować fragmenty artykułu w formacie RDF.  Aby wyeksportować artykuły, wpisz ich tytuły w poniższym polu tekstowym, po jednym tytule w wierszu.", "smw_exportrdf_recursive": "Rekursywny eksport wszystkich powiązanych stron.  Zwróć uwagę, że wynik może być olbrzymi!", "smw_exportrdf_backlinks": "Eksportuj także wszystkie strony, które odwołują się do eksportowanych stron.  Tworzy przeglądalny RDF.", "smw_exportrdf_lastdate": "Nie eksportuj stron, które nie były zmieniane od podanego czasu.", "smw_exportrdf_submit": "Eksport", "uriresolver": "Przekształcanie URI", "properties": "<PERSON><PERSON><PERSON><PERSON><PERSON>ś<PERSON>", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "Następujące właściwości są wykorzystywane w wiki.", "smw_property_template": "$1 typu $2 ($3 {{PLURAL:$3|użycie|użycia}})", "smw_propertylackspage": "Wszystkie właściwości powinny być opisane stroną!", "smw_propertylackstype": "Nie określono żadnego typu dla tej wła<PERSON><PERSON>ci (tymczasowo przypisano typ $1).", "smw_propertyhardlyused": "Ta właściwość jest rzadko używana w przestrzeni wiki!", "smw-property-name-invalid": "Właściwość $1 nie może być użyta (nieprawidłowa nazwa właściwości).", "smw-sp-property-searchform": "Pokaż w<PERSON><PERSON><PERSON>, które zawierają:", "smw-special-property-searchform": "Wyświetl właściwości zawierające:", "smw-special-property-searchform-options": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Filtr:", "smw-special-wantedproperties-filter-none": "Brak", "smw-special-wantedproperties-filter-unapproved": "Niezatwierdzony", "concepts": "Koncepty", "smw-special-concept-header": "Lista konceptów", "smw-special-concept-count": "Znaleziono {{PLURAL:$1|1 koncept|$1 koncepty|$1 konceptów}}.", "smw-special-concept-empty": "Nie znaleziono konceptów.", "unusedproperties": "Niewykorzystywane właściwości", "smw-unusedproperties-docu": "Ta strona wymienia [https://www.semantic-mediawiki.org/wiki/Unused_properties niewykorzystywane właściwości], które są z<PERSON>, choć żadna inna strona z nich nie korzysta. Odmiennego spojrzenia dostarczają wykazy na stronach specjalnych [[Special:Properties|wszystkich]] oraz [[Special:WantedProperties|pożądanych właściwości]].", "smw-unusedproperty-template": "$1 typu $2", "wantedproperties": "Pożądane właściwości", "smw-wantedproperties-docu": "Ta strona wymienia [https://www.semantic-mediawiki.org/wiki/Wanted_properties pożądane właściwości], które są używane w wiki, ale nie mają strony opisującej je. Odmiennego spojrzenia dostarczają wykazy na stronach specjalnych [[Special:Properties|wszystkich]] oraz [[Special:UnusedProperties|niewykorzystywanych właściwości]].", "smw-wantedproperty-template": "$1 (wyk<PERSON><PERSON><PERSON>e {{PLURAL:$2|raz|$2 razy}})", "smw-special-wantedproperties-docu": "Ta strona wymienia [https://www.semantic-mediawiki.org/wiki/Wanted_properties pożądane właściwości], które są używane w wiki, ale nie mają strony opisującej je. Odmiennego spojrzenia dostarczają wykazy na stronach specjalnych [[Special:Properties|wszystkich]] oraz [[Special:UnusedProperties|niewykorzystywanych właściwości]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|użycie|użycia|użyć}})", "smw_purge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-purge-failed": "Semantyczna MediaWiki próbowała odświeżyć tę stronę, jednak nie powiodło się", "types": "<PERSON><PERSON>", "smw_types_docu": "W<PERSON>az [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes dostępnych typów danych]. Każdy [https://www.semantic-mediawiki.org/wiki/Help:Datatype typ] reprezentuje unikatowy zbiór atrybutów charakteryzujących wartości poprzez sposób ich składowania w pamięci i wyświetlania. Charakterystyki te są dziedziczone w przypisanych właściwościach.", "smw-special-types-no-such-type": "\"$1\" jest nieznany lub nie został określony jako poprawny typ danych.", "smw-statistics": "Statystyka semantyczna", "smw-statistics-entities-total": "Encje (wszystkie)", "smw-statistics-property-instance": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|Wartości}} w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (łącznie)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}]] (łącznie)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}} (łącznie)", "smw-statistics-property-used": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}} (użycie z co najmniej jedną wartością)", "smw-statistics-property-page": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}} (zarejestrowane jako strona)", "smw-statistics-property-type": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (przypisana do typu danych)|<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (przypisane do typu danych)}}", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Zapytanie|Zapytania}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Zapytanie|Zapytania}}]] (osadzone, wszystkie)", "smw-statistics-query-format": "format <code>$1</code>", "smw-statistics-query-size": "Wielkość zapytania", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Koncept|Koncepty}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Koncept|Koncepty}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Podobiekt|Podobiekty}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Podobiekt|Podobiekty}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Typ danych|Typy danych}}]]", "smw-statistics-error-count": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON>}} w<PERSON><PERSON><PERSON>wości ([[Special:ProcessingErrorList|{{PLURAL:$1|niewłaściwa adnotacja|niew<PERSON>ści<PERSON> adnotacje}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON>}} właś<PERSON>wości ({{PLURAL:$1|niewłaściwa adnotacja|niew<PERSON>ści<PERSON> adnotacje}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Nieaktualna encja|Nieaktualne encje}}]", "smw_uri_doc": "Resolver URI implementuje [$1 W3C TAG finding on httpRange-14]. <PERSON><PERSON>ęki temu ludzie nie zamieniają się w strony WWW.", "ask": "Wyszukiwanie se<PERSON>tyczne", "smw_ask_sortby": "Sort<PERSON>j <PERSON> kolumny (opcjonalnie)", "smw_ask_ascorder": "Rosnąco", "smw_ask_descorder": "Malejąco", "smw-ask-order-rand": "Losowy", "smw_ask_submit": "Szukaj wyników", "smw_ask_editquery": "<PERSON><PERSON><PERSON><PERSON>", "smw_add_sortcondition": "[Dodaj warunki sortowania]", "smw-ask-sort-add-action": "Dodaj warunek sortowania", "smw_ask_hidequery": "<PERSON><PERSON><PERSON><PERSON> (widok skrócony)", "smw_ask_help": "Pomoc dla tworzenia zapytań", "smw_ask_queryhead": "Warunek", "smw_ask_printhead": "Instrukcje wydruku", "smw_ask_printdesc": "(dodaj w linii jedną nazwę właściwości)", "smw_ask_format_as": "Format<PERSON>j jako", "smw_ask_defaultformat": "domyślny", "smw_ask_otheroptions": "Inne opcje", "smw-ask-otheroptions-collapsed-info": "<PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON>ć wszystkie dostępne opcje użyj ikony plusa", "smw_ask_show_embed": "Pokaż kod zagnieżdżony", "smw_ask_hide_embed": "Ukryj zagnieżdżony kod", "smw_ask_embed_instr": "<PERSON><PERSON> to zapytanie na stronie wiki, użyj poniższego kodu.", "smw-ask-delete": "Usuń", "smw-ask-sorting": "Sort<PERSON>nie", "smw-ask-options": "<PERSON><PERSON><PERSON>", "smw-ask-options-sort": "<PERSON><PERSON>je <PERSON>nia", "smw-ask-format-options": "Format i opcje", "smw-ask-parameters": "Parametry", "smw-ask-search": "Szukaj", "smw-ask-debug": "Debugowanie", "smw-ask-debug-desc": "Tworzy informacje o debugowaniu zapytań", "smw-ask-no-cache": "Wyłącz pamięć podręczną", "smw-ask-no-cache-desc": "Wyniki bez pamięci podręcznej zapytań", "smw-ask-result": "<PERSON><PERSON><PERSON>", "smw-ask-empty": "Wymaż wszystkie wpisy", "smw-ask-download-link-desc": "Pobieranie wyników zapytań w formacie $1", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Pomoc w wybranym formacie: $1", "smw-ask-condition-change-info": "Warunek został zmieniony i wyszukiwarka wymaga ponownego uruchomienia zapytania w celu uzyskania wyników odpowiadających nowym wymaganiom.", "smw-ask-extra-query-log": "Rejestr zapytań", "smw-ask-extra-other": "<PERSON><PERSON>", "searchbyproperty": "Wyszukiwanie po atrybucie", "processingerrorlist": "Lista błędów przetwarzania", "constrainterrorlist": "Lista błędów ograniczeń", "propertylabelsimilarity": "Raport podobieństwa etykiet właściwości", "smw-missingredirects-list": "Strony z brakującymi adnotacjami", "smw_sbv_docu": "Wyszukiwanie wszystkich stron, które mają dany atrybut i wartość.", "smw_sbv_novalue": "<PERSON><PERSON><PERSON>, lub zobacz wszystkie wartości atrybutów dla $1.", "smw_sbv_displayresultfuzzy": "Lista wszystkich stron, które posiadają wła<PERSON><PERSON><PERSON><PERSON><PERSON> „$1” o wartości „$2”.\nPonieważ znaleziono tylko kilka wyników, pokazano również wyniki podobne.", "smw_sbv_property": "<PERSON>ry<PERSON>", "smw_sbv_value": "Wartość:", "smw_sbv_submit": "Znajdź wyniki", "browse": "Przeglądanie artykułów", "smw_browselink": "Przeglądaj właściwości", "smw_browse_article": "Wpisz nazwę artykułu, od którego chcesz rozpocząć przeglądanie.", "smw_browse_go": "<PERSON><PERSON><PERSON>", "smw_browse_show_incoming": "Pokaż właściwości linkujące tutaj", "smw_browse_hide_incoming": "Ukryj właściwości linkujące tutaj", "smw_browse_no_outgoing": "Ta strona nie ma żadnych właściwości.", "smw_browse_no_incoming": "Żadne właściwości nie linkują do tej strony.", "smw-browse-from-backend": "Informacje są obecnie pobierane z zaplecza.", "smw-browse-api-subject-serialization-invalid": "Podmiot ma nieprawidłowy format serializacji.", "smw-browse-js-disabled": "Prawdopodobnie JavaScript jest wyłączony lub niedostępny. Zaleca się korzystanie z przeglądarki, w której jest on obsługiwany. Inne opcje omówione są na  stronie parametrów konfiguracyjnych [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Pokaż grupy", "smw-browse-hide-group": "<PERSON><PERSON><PERSON><PERSON>", "smw_inverse_label_default": "$1 z", "smw_inverse_label_property": "Etykieta odwrotnej właściwości", "pageproperty": "Szukanie właściwości stron", "pendingtasklist": "Lista oczekujących zadań", "smw_pp_docu": "Wprowadź stronę i właściwość lub po prostu właś<PERSON>ść, aby pobrać wszystkie przypisane wartości.", "smw_pp_from": "O<PERSON> strony:", "smw_pp_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć:", "smw_pp_submit": "Znajdź wyniki", "smw-prev": "{{PLURAL:$1|poprzedni|poprzednie $1}}", "smw-next": "{{PLURAL:$1|następny|następne $1}}", "smw_result_prev": "Poprzednia", "smw_result_next": "Następne", "smw_result_results": "Wyniki", "smw_result_noresults": "Niestety, brak wyników.", "smwadmin": "Panel Semantycznej MediaWiki", "smw-admin-statistics-job-title": "Statystyki zadań", "smw-admin-statistics-job-docu": "Statystyka zadań wyświetla informacje o zaplanowanych zadaniach Semantic MediaWiki, które nie zostały jeszcze wykonane. Liczba zadań może być trochę niedokładna lub zawierać nieudane próby. Więcej informacji można znaleźć na stronie [Manual:Job_queue/pl podręcznika].", "smw-admin-statistics-querycache-title": "<PERSON><PERSON><PERSON>ć podręczna zapytań", "smw-admin-statistics-querycache-disabled": "Usługa [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] nie jest włączona na tej wiki i żadne statystyki nie są dostępne.", "smw-admin-statistics-semanticdata-overview": "Przegląd", "smw-admin-permission-missing": "Dostęp do tej strony został zablokowany z powodu braku uprawnień. Na stronie pomocy na temat [https://www.semantic-mediawiki.org/wiki/Help:Permissions uprawnień] znajdziesz szczegółowe informacje o niezbędnych ustawieniach.", "smw-admin-setupsuccess": "Silnik przestrzeni dyskowej został ustawiony.", "smw_smwadmin_return": "Powrót do $1", "smw_smwadmin_updatestarted": "Uruchomiono nowy proces aktualizacji danych semantycznych.\nWszystkie zapisane dane zostaną uporządkowane oraz naprawione w razie potrzeby.\nMoż<PERSON>z śledzić stan procesu aktualizacji na tej stronie specjalnej.", "smw_smwadmin_updatenotstarted": "Jest już uruchomiony proces aktualizacji.\nKolejny nie zostanie utworzony.", "smw_smwadmin_updatestopped": "Wszystkie istniejące procesy aktualizacji zostały zatrzymane.", "smw_smwadmin_updatenotstopped": "W celu zatrzymania uruchomionego procesu aktualizacji należy zaznaczyć pole wyboru, aby <PERSON><PERSON><PERSON><PERSON> decyzję.", "smw-admin-docu": "Ta strona specjalna pomoże Ci w instalacji, modernizacji, utrzymaniu i użytkowaniu <a href=\"https://www.semantic-mediawiki.org\">Semantycznego MediaWiki</a> a także udostępnia dalsze funkcje i zadania administracyjne oraz statystyki.\nPamiętaj przed wykonaniem funkcji administracyjnych o utworzeniu kopii zapasowej ważnych danych.", "smw-admin-db": "Konfiguracja bazy danych", "smw-admin-db-preparation": "Inicjalizacja tabeli trwa i może chwilę potrwać, zanim wyniki zostaną wyświetlone w oczekiwaniu na rozmiar i możliwe optymalizacje tabeli.", "smw-admin-dbdocu": "Semantyczne MediaWiki wymaga rozszerzeń do bazy danych MediaWiki, które umożliwiają przechowywanie danych semantycznych.\nPoniższa funkcja zapewnia, że baza danych zostanie poprawnie przygotowana.\nZmiany wykonane w tym kroku nie mają wpływu na pozostałą część bazy danych MediaWiki i mogą łatwo zostać cofnięte w razie potrzeby.\nTa operacja może zostać wykonana wielokrotnie bez wyrządzenia szkód ale konieczna jest tylko raz w trakcie instalacji lub aktualizacji.", "smw-admin-permissionswarn": "Jeśli operacja nie powiedzie się i wystąpią błędy SQL, najprawdopodobniej użytkownik bazy danych wykorzystywany przez Twoją wiki (sprawdź plik LocalSettings.php) nie ma wystarczających uprawnień.\nNadaj użytkownikowi dodatkowe uprawnienia do tworzenia i usuwania tabel, tymczasowo użyj konta root bazy danych w pliku LocalSettings.php lub użyj skryptu konserwacyjnego <code>setupStore.php</code>, który ma uprawnienia administratora.", "smw-admin-dbbutton": "Inicjuj lub aktualiz<PERSON>j tabele", "smw-admin-announce": "Ogłoś swoją wiki", "smw-admin-deprecation-notice-docu": "Poniższa sekcja zawiera ustawienia, które zostały wycofane lub usunięte, ale zostały wykryte jako aktywne na tej wiki. Oczekuje się, że każda kolejna wersja usunie obsługę tych konfiguracji.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> jest przestarzały i zostanie usunięty w wersji $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> usunie (lub zast<PERSON>) {{PLURAL:$2|następującą opcję|następuj<PERSON>ce opcje}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> jest przestarzały i zostanie usunięte w $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> został zastąpiony przez <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|opcja|opcje}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> zostaje zastąpiony przez <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> został usunięty w $2", "smw-admin-deprecation-notice-title-notice": "Nadchodzące zmiany", "smw-admin-deprecation-notice-title-notice-explanation": "Wykryto w tej wiki następujące ustawienia, które planuje się usunąć lub zmienić w przys<PERSON>ł<PERSON> wersji.", "smw-admin-deprecation-notice-title-replacement": "Zastąpione lub zmienione ustawienia", "smw-admin-deprecation-notice-title-replacement-explanation": "Poniższa sekcja zawiera ustawienia, które zostały zmienione lub zmodyfikowane i zaleca się natychmiast zaktualizować ich nazwy lub format.", "smw-admin-deprecation-notice-title-removal": "Usunięte ustawienia", "smw-admin-deprecation-notice-title-removal-explanation": "Wymienione ustawienia zostały usunięte w poprzedniej wersji, ale zostały wykryte na tej wiki.", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw-smwadmin-refresh-title": "Naprawa i aktualizacja danych", "smw_smwadmin_datarefresh": "Odbudowa danych", "smw_smwadmin_datarefreshdocu": "Istnieje możliwość przywrócenia wszystkich danych Semantic MediaWiki w oparciu o aktualną zawartość wiki.\nMoże to być przydatne do naprawy uszkodzonych danych lub odświeżenia danych, jeśli wewnętrzny format zmienił się z powodu jakichś aktualizacji oprogramowania.\nAktualizacja jest wykonywana strona po stronie i nie zostanie ukończona natychmiast.\n<PERSON><PERSON><PERSON><PERSON> prz<PERSON>, czy aktualizacja jest w toku. Można również uruchomić lub zatrzymać aktualizację (chyba, że ta funkcja została wyłączona przez administratora).", "smw_smwadmin_datarefreshprogress": "<strong>Aktualizacja trwa.</strong>\nTo normalne, że aktualizacja postępuje powoli, g<PERSON><PERSON>dświeżanie danych wykonywane jest w małymi porcjami za każdym razem, gdy użytkownik korzysta z wiki.\nAby szybciej zakończyć aktualizację można uruchomić skrypt MediaWiki <code>runJobs.php</code> (użyj opcji <code>--maxjobs 1000</code>, aby ograni<PERSON><PERSON><PERSON> liczbę aktualizacji, które wykonywane są podczas jednego uruchomienia).\nPrzewidywany postęp bieżącej aktualizacji:", "smw_smwadmin_datarefreshbutton": "Zaplanuj przebudowę danych", "smw_smwadmin_datarefreshstop": "Zatrzymaj aktualizację danych", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, jestem {{GENDER:$1|pewny|pewna}}.", "smw-admin-outdateddisposal-button": "<PERSON><PERSON><PERSON><PERSON><PERSON>u<PERSON>", "smw-admin-feature-disabled": "Ta funkcja została wyłączona w tej wiki. Aby uzyskać więcej szczegółów, przeczytaj <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">powiązaną</a> stronę pomocy.", "smw-admin-support": "Uzyskiwanie wsparcia", "smw-admin-supportdocu": "Różnorodne źródła informacji mogą okazać się przydatne, jeśli wystąpią problemy:", "smw-admin-installfile": "<PERSON><PERSON><PERSON> trudności z instalacją, zac<PERSON><PERSON>j od sprawdzenia wskazówek w <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">pliku INSTALL</a> /a> i <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">na stronie instalacji</a>.", "smw-admin-smwhomepage": "Kompletna dokumentacja Semantic MediaWiki znajduje się na stronie <strong><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></strong>.", "smw-admin-bugsreport": "Bł<PERSON>dy możesz zgłasz<PERSON> w <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>.", "smw-admin-questions": "<PERSON><PERSON><PERSON> nadal masz pytania lub sugestie, prz<PERSON>łącz się do dyskusji na <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">forum użytkowników Semantic MediaWiki</a>.", "smw-admin-other-functions": "<PERSON><PERSON>", "smw-admin-statistics-extra": "Funkcje statystyczne", "smw-admin-statistics": "Statystyki", "smw-admin-supplementary-section-title": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-section-subtitle": "Wspierane <PERSON>", "smw-admin-supplementary-section-intro": "Niektóre z wymienionych funkcji w tej sekcji mogą być ograniczone i dlatego są niedostępne na tej wiki.", "smw-admin-supplementary-settings-title": "Ustawienia konfiguracji", "smw-admin-supplementary-settings-intro": "<u>$1</u> wyprowadza zbiorczą listę dostępnych ustawień używanych w Semantic MediaWiki", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Statystyki operacyjne", "smw-admin-supplementary-operational-statistics-short-title": "statystyki operacyjne", "smw-admin-supplementary-operational-statistics-intro": "<u>$1</u> pokazuje rozszerzony zestaw statystyk", "smw-admin-supplementary-operational-statistics-cache-title": "Statystyki pamięci cache", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> wyświetla statystyki dotyczące pamięci cache", "smw-admin-supplementary-elastic-version-info": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-intro": "<u>$1</u> informuje o ustawieniach i indeksie statystyk", "smw-admin-supplementary-elastic-functions": "Wspierane <PERSON>", "smw-admin-supplementary-elastic-settings-title": "Ustawienia (indeksy)", "smw-admin-supplementary-elastic-mappings-title": "Mapowania", "smw-admin-supplementary-elastic-mappings-summary": "Podsumowanie", "smw-admin-supplementary-elastic-nodes-title": "Węzły", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> pokazuje statystyki węzła", "smw-admin-supplementary-elastic-indices-title": "Wskaźniki", "smw-admin-supplementary-elastic-statistics-title": "Statystyki", "smw-admin-supplementary-elastic-status-replication": "Status replikacji", "smw-admin-supplementary-elastic-status-last-active-replication": "Ostatnia aktywna replikacja: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Interwał odświeżania: $1", "smw-admin-supplementary-elastic-replication-header-title": "<PERSON>", "smw-admin-supplementary-elastic-replication-function-title": "Replikacja", "smw-admin-supplementary-elastic-replication-files": "Pliki", "smw-admin-supplementary-elastic-replication-pages": "Strony", "smw-admin-supplementary-elastic-endpoints": "Punkty końcowe", "smw-admin-supplementary-elastic-config": "Konfiguracje", "smw-list-count": "Lista zawiera $1 {{PLURAL:$1|pozycję|pozycje}}.", "smw-property-label-similarity-title": "Raport podobieństwa etykiet właściwości", "smw-property-label-similarity-intro": "<u>$1</u> oblicza podobieństwa dla istniejących etykiet właściwości", "smw-property-label-similarity-threshold": "Próg:", "smw-property-label-similarity-type": "Wyświetl ID typu", "smw-property-label-similarity-noresult": "Nie znaleziono wyników dla wybranych opcji.", "smw_adminlinks_datastructure": "Struktura danych", "smw_adminlinks_displayingdata": "<PERSON><PERSON><PERSON> dane", "smw_adminlinks_inlinequerieshelp": "Pomoc dotycząca wewnętrznych zapytań", "smw-property-indicator-type-info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zdefiniowana przez {{PLURAL:$1|użytkownika|system}}", "smw-createproperty-isproperty": "To jest w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> typu $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Dopuszczalna wartość dla tej wła<PERSON><PERSON><PERSON> to|Dopuszczalne wartości dla tej własności to:}}", "smw-paramdesc-category-delim": "Ogranicznik", "smw-paramdesc-category-template": "Szablon do formatowania elementów z", "smw-paramdesc-category-userparam": "Parametr do przekazania do szablonu", "smw-info-par-message": "Wiadomość do wyświetlenia.", "prefs-smw": "Semantyczna MediaWiki", "prefs-general-options": "<PERSON><PERSON><PERSON>", "prefs-extended-search-options": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefs-ask-options": "Wyszukiwanie se<PERSON>tyczne", "smw-prefs-intro-text": "Opcje poniżej są dostarczane przez [https://www.semantic-mediawiki.org/ semantyczną MediaWiki] (lub powiązane rozszerzenia), aby umo<PERSON><PERSON>wić indywidualne dostosowanie do wybranych funkcji. Więcej informacji na ten temat można uzyskać w [https://www.semantic-mediawiki.org/wiki/Help:User_preferences sekcji pomocy].", "smw-prefs-ask-options-tooltip-display": "Wyświetlaj opis parametru za pomocą „dymku” z informacją", "smw-prefs-general-options-time-correction": "Włącz korekcję czasu dla stron specjalnych, korzystając z lokalnego przesunięcia czasu w preferencjach", "smw-prefs-general-options-disable-editpage-info": "Wyłącz tekst wprowadzający na stronie edycji", "smw-prefs-general-options-disable-search-info": "Wyłącz informacje dotyczące obsługi składni na standardowej stronie wyszukiwania", "smw-prefs-general-options-suggester-textinput": "Włącz wspomaganie wprowadzania dla encji semantycznych", "smw-prefs-help-general-options-suggester-textinput": "Włączenie tej opcji umożliwia użycie [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance pomocy wejściowej] w celu znalezienia właściwości, poj<PERSON><PERSON> i kategorii z kontekstu wejściowego.", "smw-ui-tooltip-title-property": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-quantity": "Konwersja jednostek", "smw-ui-tooltip-title-info": "Informacja", "smw-ui-tooltip-title-service": "Linki usługi", "smw-ui-tooltip-title-warning": "Ostrzeżenie", "smw-ui-tooltip-title-error": "Błąd", "smw-ui-tooltip-title-parameter": "Parametr", "smw-ui-tooltip-title-event": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-note": "Notatka", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "<PERSON><PERSON><PERSON><PERSON>", "smw_unknowntype": "Typ \"$1\" tego atrybutu jest nieprawidłowy.", "smw_concept_header": "Strony koncepcji „$1”", "smw_conceptarticlecount": "{{PLURAL:$1|W<PERSON>świetlona jest jedna strona |Wyświetlone zostały $1 strony|Wyświetlonych zostało $1 stron}}.", "smw-qp-empty-data": "Żądane dane nie mogły zostać wyświetlone ze względu na niewystarczające kryteria wyboru.", "right-smw-admin": "Dostęp do zarządzania (Semantyczna MediaWiki)", "restriction-level-smw-pageedit": "zabezpieczono (tylko użytkownicy z odpowiednimi uprawnieniami)", "action-smw-patternedit": "edycji wyrażeń regularnych, używanych w Semantic MediaWiki", "group-smwadministrator": "<PERSON><PERSON> (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrator|administratorka}} (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:<PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator": "<PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|kurator (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON> (Semantic MediaWiki)", "action-smw-admin": "dostęp do zarządzania semantycznej MediaWiki", "action-smw-ruleedit": "edytowania strony reguł (Semantic MediaWiki)", "smw-property-predefined-default": "„$1” jest w<PERSON><PERSON><PERSON><PERSON>ś<PERSON>ą predefiniowaną.", "smw-property-predefined-common": "Ta właściwość jest wdrożona automatycznie (znana jest także jako [https://www.semantic-mediawiki.org/wiki/Help:Special_properties specjalna właściwość]) i zawiera dodatkowe uprawnienia administracyjne, ale można jej używać tak jak innych [https://www.semantic-mediawiki.org/wiki/Property właściwości zdefiniowanych przez użytkownika].", "smw-property-predefined-long-askde": "Jest to wartość numeryczna obliczana na podstawie zagnieżdżenia podkwerendy, łańcuchów własności i dostępnych elementów opisu z wykonaniem zapytania ograniczonego przez parametr konfiguracyjny <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-sp-properties-docu": "Ta strona wymienia [https://www.semantic-mediawiki.org/wiki/Property właściwości] dostępne dla tej wiki oraz liczniki ich użycia. Dla utrzymania aktualności statystyk liczników zaleca się regularne uruchamianie skryptu administracyjnego [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics statystyk właściwości]. Odmiennego spojrzenia dostarczają wykazy na stronach specjalnych [[Special:UnusedProperties|właściwości niewykorzystywanych]] oraz [[Special:WantedProperties|pożądanych]].", "smw-sp-properties-cache-info": "Wymienione dane zostały pobrane z [https://www.semantic-mediawiki.org/wiki/Caching cache] i były ostatnio aktualizowane $1.", "smw-sp-properties-header-label": "Lista właściwości", "smw-sp-admin-settings-button": "Utwórz listę ustawień", "smw-admin-idlookup-title": "Szukaj", "smw-admin-idlookup-input": "Wyszukiwanie:", "smw-admin-objectid": "Identyfikator:", "smw-admin-tab-general": "Przegląd", "smw-admin-tab-supplement": "<PERSON><PERSON><PERSON>", "smw-admin-tab-registry": "Reje<PERSON><PERSON>", "smw-admin-tab-alerts": "Powiadomienia", "smw-admin-configutation-tab-settings": "Ustawienia", "smw-admin-configutation-tab-namespaces": "Przestrzenie nazw", "smw-admin-configutation-tab-schematypes": "Typy schematów", "smw-admin-maintenance-tab-tasks": "Zadania", "smw-admin-maintenance-no-description": "Brak opisu.", "smw-livepreview-loading": "Trwa ładowanie…", "smw-sp-searchbyproperty-resultlist-header": "Lista wyników", "smw-sp-searchbyproperty-nonvaluequery": "<PERSON><PERSON>, które mają przypisaną wła<PERSON><PERSON><PERSON><PERSON><PERSON> „$1”.", "smw-sp-searchbyproperty-valuequery": "<PERSON>a stron, które mają wła<PERSON><PERSON><PERSON><PERSON> „$1” z wartością „$2”.", "smw-datavalue-number-textnotallowed": "„$1” nie może być przypisane do zadeklarowanego typu liczbowego o wartości $2.", "smw-datavalue-number-nullnotallowed": "„$1” <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „NULL”, która nie jest dozwolona jako liczba.", "smw-search-input": "Wprowadź i wyszukaj", "smw-search-syntax": "Składnia", "smw-search-profile": "Rozszerzone", "smw-search-profile-tooltip": "Funkcje wyszukiwania w połączeniu z Semantyczną MediaWiki", "smw-search-profile-sort-best": "Najlepsze dopasowanie", "smw-search-profile-sort-recent": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-namespace": "Pole wyboru przestrzeni nazw zostanie ukryte, gdy tylko zostanie wybrany formularz, ale można je pokazać za pomocą przycisku „pokaż/ukryj”.", "smw-search-profile-extended-help-query-link": "(Aby uzyskać więcej szczegółów $1).", "smw-search-profile-extended-section-sort": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-form": "Formularze", "smw-search-profile-extended-section-namespace": "Przestrzeń nazw", "smw-search-profile-extended-section-query": "Zapytanie", "smw-search-profile-link-caption-query": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-show": "Po<PERSON><PERSON>", "smw-search-hide": "<PERSON><PERSON><PERSON><PERSON>", "log-name-smw": "Rejestr semantycznej MediaWiki", "log-show-hide-smw": "$1 rejestr semantycznej MediaWiki", "logeventslist-smw-log": "Rejestr Semantycznej MediaWiki", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Import $1]]", "smw-property-predefined-long-errp": "W większości przypadków jest to spowodowane niedopasowaniem lub ograniczeniem  [[Property:Allows value|wartości]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value „$1”] jest predefiniowan<PERSON> wła<PERSON>cią, która może zdefiniować listę dopuszczalnych wartości ograniczających przyporządkowania wartości dla właściwości.", "smw-datavalue-property-restricted-annotation-use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „$1” ma ograniczony obszar zastosowania i nie może być używana przez użytkownika jako w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adnotacji.", "smw-datavalue-property-restricted-declarative-use": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „$1” jest właściwością deklaratywną i może być używana tylko na stronie właściwości lub kategorii.", "smw-datavalue-property-invalid-character": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „$1” zawiera znak „$2”, b<PERSON><PERSON><PERSON><PERSON> część etykiety właściwości i dlatego została zaklasyfikowana jako nieprawidłowa.", "smw-datavalue-restricted-use": "Wartość danych „$1” została oznaczona do ograniczonego użytku.", "smw-datavalue-invalid-number": "„$1” nie może być interpretowane jako liczba.", "smw-types-list": "Lista typów danych", "smw-types-default": "„$1” jest wbu<PERSON>wanym typem danych.", "smw-types-help": "Więcej informacji i przykładów można znaleźć na tej [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 stronie pomocy].", "smw-type-boo": "„$1” jest podstawowym typem danych opisującym wartość prawda/fałsz.", "smw-type-tel": "„$1” to specjalny typ danych do opisywania międzynarodowych numerów telefonicznych zgodnie z RFC 3966.", "smw-type-dat": "„$1” to podstawowy typ danych, reprezentujący punkty w czasie w ujednoliconym formacie.", "smw-type-ema": "„$1” jest specjalnym typem danych reprezentującym adres e-mail.", "smw-type-tem": "„$1” jest specjalnym typem danych reprezentującym temperaturę.", "smw-type-qty": "„$1” jest typem danych opisującym wielkości z reprezentacją numeryczną i jednostką miary.", "smw-type-tab-properties": "<PERSON><PERSON><PERSON><PERSON><PERSON>ś<PERSON>", "smw-type-tab-types": "<PERSON><PERSON>", "smw-type-tab-errors": "Błędy", "smw-type-primitive": "Podstawowe", "smw-type-contextual": "Zależne od kontekstu", "smw-type-compound": "Złożone", "smw-type-container": "Kontenery", "smw-property-predefined-errt": "„$1” jest predefinio<PERSON><PERSON> właś<PERSON>ścią, zawierają<PERSON>ą tekstowy opis błędu i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-mdat": "„$1” jest predefinio<PERSON>ą wła<PERSON><PERSON>ścią, która odpowiada dacie ostatniej modyfikacji strony i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-cdat": "„$1” jest predefinio<PERSON>ą w<PERSON><PERSON><PERSON><PERSON>cią, która odpowiada dacie pierwszej wersji tematu i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-newp": "„$1” jest predefinio<PERSON><PERSON> wła<PERSON><PERSON><PERSON>cią, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, czy temat jest nowy, czy nie, i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-mime": "„$1” jest predefinio<PERSON>ą w<PERSON>ścią, opisującą typ MIME przesłanego pliku i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-prec": "„$1” jest predefiniowaną właś<PERSON>wością, opisuj<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Display_precision precyzję wyświetlania] (w cyfrach dziesiętnych) dla liczbowych typów danych.", "smw-datavalue-monolingual-dataitem-missing": "Brak oczekiwanego elementu do budowania jednojęzycznej wartości złożonej.", "smw-datavalue-languagecode-missing": "W przypadku adnotacji „$1” parser nie był w stanie określić kodu języka (np. „foo@en”).", "smw-datavalue-languagecode-invalid": "„$1” nie został rozpoznany jako obsługiwany kod języka.", "smw-property-predefined-lcode": "„$1” to predefiniowana w<PERSON><PERSON><PERSON><PERSON><PERSON>, reprezentująca kod języka sformatowany w BCP47 i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "„$1” to typ danych [https://www.semantic-mediawiki.org/wiki/Help:Container kontener], który wiąże wartość tekstową z określonym [[Property:Language code|kodem języka]].", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|sekunda|sekundy|sekund}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekunda|sekundy|sekund}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|sekunda|sekundy|sekund}}", "smw-datavalue-allows-pattern-mismatch": "Wyrażenie regularne „$2” zaklasyfikowało war<PERSON> „$1” jako niepoprawną.", "smw-datavalue-feature-not-supported": "Funkcja „$1” jest nieobsługiwana lub została wyłączona w tej wiki.", "smw-property-predefined-boo": "„$1” jest [[Special:Types/Boolean|typem danych]] i predefiniowaną właściwością do reprezentowania wartości logicznych i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-num": "„$1” jest [[Special:Types/Number|typem danych]] i predefiniowaną właściwością do reprezentowania wartości liczbowych i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-dat": "„$1” jest [[Special:Types/Date|typem danych]] i predefiniowaną właściwością do reprezentowania wartości daty i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-uri": "„$1” jest [[Special:Types/URL|typem danych]] i predefiniowaną właściwością do reprezentowania wartości URI/URL i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-property-predefined-qty": "„$1” jest [[Special:Types/Quantity|typem danych]] i predefiniowaną właściwością do reprezentowania wartości ilościowych i pochodzi z [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantycznej MediaWiki].", "smw-datavalue-time-invalid-date-components-common": "„$1” zawiera informacje, które nie jest interpretowalne.", "smw-datavalue-time-invalid-date-components-dash": "„$1” zawiera zbędny myślnik lub inne znaki, które nie powinny się znaleźć w dacie.", "smw-datavalue-time-invalid-date-components-empty": "„$1” zawiera puste elementy.", "smw-datavalue-time-invalid-date-components-three": "„$1” zawiera więcej niż trzy komponenty potrzebne do interpretacji daty.", "smw-datavalue-time-invalid-date-components-sequence": "„$1” zawiera ciąg, kt<PERSON>ry nie mógł zostać zinterpretowany jako prawidłowy element daty.", "smw-datavalue-time-invalid-ampm": "„$1” zawiera „$2” jako element godziny, kt<PERSON>ry jest nieprawidłowy dla konwencji 12-god<PERSON><PERSON><PERSON>.", "smw-datavalue-external-formatter-invalid-uri": "„$1” jest nieprawidłowym adresem URL.", "smw-datavalue-keyword-maximum-length": "Słowo kluczowe przekroczyło maksymalną długość $1 {{PLURAL:$1|znaku|znaków}}.", "smw-datavalue-parse-error": "<PERSON><PERSON><PERSON> „$1” nie została niezrozumiała.", "smw-datavalue-propertylist-invalid-property-key": "Lista właściwości „$1” zawiera nieprawidłowy klucz właściwości „$2”.", "smw-datavalue-type-invalid-typeuri": "Typu „$1” nie można przekształcić w prawidłową reprezentację URI.", "smw-parser-invalid-json-format": "Parser JSON zwrócił błąd „$1”.", "smw-property-preferred-label-language-combination-exists": "„$1” nie może być użyty jako preferowana etykieta, ponieważ język „$2” jest już przypisany do etykiety „$3”.", "smw-clipboard-copy-link": "Kopiuj link do schowka", "smw-data-lookup": "<PERSON><PERSON><PERSON><PERSON> danych...", "smw-data-lookup-with-wait": "Żądanie jest przetwarzane i może zająć chwilę.", "smw-no-data-available": "<PERSON><PERSON> danych.", "smw-property-req-violation-predefined-type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „$1” jako predefiniowana wła<PERSON><PERSON><PERSON><PERSON>ć zawiera deklarację typu „$2”, która jest niezgodna z domyślnym typem tej właściwości.", "smw-property-req-violation-type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zawiera konkurencyjne specyfikacje typów, które mogą skutkować adnotacjami o nieprawidłowej warto<PERSON>, dlatego oczekuje się, że użytkownik przydzieli jeden odpowiedni typ.", "protect-level-smw-pageedit": "Zezwalaj tylko użytkownikom z uprawnieniami do edycji stron (Semantic MediaWiki)", "smw-edit-protection": "Ta strona jest [[Property:Is edit protected|zabezpieczona]], aby zapobiec przypadkowej modyfikacji danych i może być edytowana tylko przez użytkowników z odpowiednimi uprawnieniami do edycji („$1”) lub [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupę użytkowników].", "smw-format-datatable-emptytable": "Brak danych dostępnych w tabeli", "smw-format-datatable-infothousands": "&nbsp;", "smw-format-datatable-loadingrecords": "Ładowanie...", "smw-format-datatable-processing": "Przetwarzanie...", "smw-format-datatable-search": "Wyszukiwanie:", "smw-format-datatable-zerorecords": "Nie znaleziono pasujących pozycji", "smw-format-datatable-first": "<PERSON><PERSON><PERSON>", "smw-format-datatable-last": "Ostatni", "smw-format-datatable-next": "Następny", "smw-format-datatable-previous": "Poprzedni", "smw-format-datatable-toolbar-export": "Eksportuj", "smw-category-invalid-redirect-target": "Kategoria „$1” zawiera nieprawidłowy cel przekierowania do przestrzeni nazw innej niż kategoria.", "smw-postproc-queryref": "Semantyczna MediaWiki zainicjuje odświeżanie strony z powodu wymaganego przetwarzania zapytania.", "apihelp-smwinfo-summary": "Moduł API do pobierania informacji o statystykach Semantic MediaWiki i innych metainformacjach.", "apihelp-browsebyproperty-summary": "Moduł API do pobierania informacji o właściwości lub liście właściwości.", "apihelp-browsebysubject-summary": "Moduł API do pobierania informacji o temacie.", "apihelp-smwtask-summary": "Moduł API do wykonywania zadań związanych z Semantic MediaWiki.", "smw-api-invalid-parameters": "Nieprawidłowe parametry, „$1”", "smw-property-page-list-count": "Wyświetlanie $1 {{PLURAL:$1|strony|stron}} przy użyciu tej właściwości.", "smw-property-page-list-search-count": "Wyświetlanie $1 {{PLURAL:$1|strony|stron}} przy użyciu tej właściwości z dopasowaniem wartości „$2”.", "smw-property-reserved-category": "Kategoria", "smw-category": "Kategoria", "smw-browse-property-group-title": "Grupa właściwości", "smw-browse-property-group-label": "Etykieta grypy właściwości", "smw-browse-property-group-description": "Opis grupy właściwości", "smw-filter": "Filtr", "smw-section-expand": "Rozwiń sekcję", "smw-section-collapse": "<PERSON><PERSON><PERSON> sekcję", "smw-ask-format-help-link": "format [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "Pomoc", "smw-cheat-sheet": "Ściągawka", "smw-property-predefined-label-skey": "Klucz sortowania", "smw-processing": "Przetwarzanie...", "smw-loading": "Ładowanie...", "smw-preparing": "Przygotowuję ...", "smw-copy": "<PERSON><PERSON><PERSON><PERSON>", "smw-copy-clipboard-title": "Kopiuje zawartość do schowka", "smw-jsonview-search-label": "Szukaj:", "smw-redirect-target-unresolvable": "Cel jest nierozwiązalny z powodu „$1”", "smw-types-title": "Typ: $1", "smw-schema-error-json": "Błąd JSON: „$1”", "smw-schema-summary-title": "Podsumowanie", "smw-schema-title": "Schemat", "smw-schema-usage": "Wykorzystanie", "smw-schema-type": "<PERSON><PERSON> schematu", "smw-schema-tag": "{{PLURAL:$1|Znacznik|Znaczniki}}", "smw-ask-title-keyword-type": "Wyszukiwanie słów kluczowych", "smw-ask-message-keyword-type": "To wyszukiwanie pasuje do warunku <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Nie można połączyć się ze zdalnym celem „$1”.", "smw-parameter-missing": "Brak parametru „$1”.", "smw-property-tab-redirects": "<PERSON><PERSON><PERSON><PERSON>", "smw-property-tab-subproperties": "Podwłaściwości", "smw-property-tab-errors": "Niewłaściwe przypisania", "smw-property-tab-specification": "... wi<PERSON>cej", "smw-concept-tab-errors": "Błędy", "smw-ask-tab-result": "<PERSON><PERSON><PERSON>", "smw-ask-tab-code": "Kod", "smw-pendingtasks-setup-tasks": "Zadania", "smw-report": "<PERSON><PERSON>", "smw-legend": "<PERSON>a", "smw-facetedsearch-intro-tab-explore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-facetedsearch-intro-tab-search": "Szukaj", "smw-facetedsearch-format-table": "<PERSON><PERSON><PERSON>", "smw-listingcontinuesabbrev": "cd.", "smw-showingresults": "Poniżej znajduje się lista {{PLURAL:$1|z '''1''' wynikiem|'''$1''' wyników}}, rozpoczynając od wyniku numer '''$2'''."}