{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Sasan700", "Исмаи<PERSON>", "Умар"]}, "smw-desc": "<PERSON>ьан вики атта тӀекхочуш йо - маше<PERSON><PERSON>на а, адамашна а ([https://www.semantic-mediawiki.org/wiki/Help:User_manual онлайн документаци])", "smw-error": "ГӀалат", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] дӀахӀоттийна а йу, болхбеш а йу, амма цуьнца догӀуш долу [https://www.semantic-mediawiki.org/wiki/Help:Upgrade карладаккхаран догӀа дац].", "smw-upgrade-release": "Верси", "smw-upgrade-progress": "Прогресс", "smw-upgrade-progress-explain": "Маца чекхдер ду карлайаккхар хала ду ала, хӀунда аьлча иза дозуш ду хаамаш латточу меттиган барамца а, лелочу аппаратан гӀирсашца а, ткъа йаккхий викеш чекхйаха цхьа хан йала тарло.\n\nДехар до, локалан администраторе йазде, кхиамех лаьцна кхин дӀа хаа лууш.", "smw-upgrade-progress-create-tables": "Таблиц<PERSON><PERSON> а, индек<PERSON><PERSON><PERSON> а кхоллар (йа карладаккхар)...", "smw-upgrade-progress-post-creation": "ДӀайаздарш кхоллар кхочушдар...", "smw-upgrade-progress-table-optimization": "Таблицин оптимизаци йар кхочушдар...", "smw-upgrade-progress-supplement-jobs": "Кхин а белхан меттигаш тӀетохар...", "smw-upgrade-error-title": "ГӀалат » Semantic MediaWiki", "smw-upgrade-error-why-title": "ХӀунда го суна хӀара агӀо?", "smw-upgrade-error-why-explain": "Semantic MediaWiki чоьхьара базин структура хийцаелла, цхьацца нисдарш дан деза, иза дуьззина болх бан. Цуьнан масех бахьана хила тарло, шайна йукъахь:\n* Кхин а дӀахӀиттийна хьашташ (кхин а таблица дӀахӀоттор оьшу) тӀетоьхна\n* Кхиорехь цхьацца хийцамаш бу таблицашкахь йа индексашкахь, цуо дӀалацар декхаре до хаамашка кхачале\n* Хийцамаш лармехь йа дехарийн механизмехь", "smw-upgrade-error-how-title": "ХӀара гӀалат муха нисдан деза?", "smw-upgrade-error-how-explain-admin": "Администра<PERSON><PERSON><PERSON><PERSON> (йа администраторан бакъонаш йолчу муьлххачу а стага) лело деза йа MediaWiki [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] йа Semantic MediaWiki [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] латторан скрипт.", "smw-upgrade-error-how-explain-links": "Иштта хьажа мегар ду хӀокху агӀонаш тӀе, кхин а информаци йовза:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation ДӀахӀотторан] инструкцеш\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting ГӀалаташ нисдар] гӀоьнан агӀо", "smw-extensionload-error-why-title": "ХӀунда го суна хӀара агӀо?", "smw-extensionload-error-why-explain": "Шорд<PERSON><PERSON><PERSON> <b>дацара</b> чуда<PERSON>хна <code>enableSemantics</code> лелош, цуьнан метта йукъатоьхна кхечу гӀирсашца масала <code>wfLoadExtension( 'SemanticMediaWiki' )</code> ма-дарра лелош.", "smw-extensionload-error-how-title": "ХӀара гӀалат муха нисдан деза?", "smw-extensionload-error-how-explain": "Шордар дӀахӀотто а, цӀерийн меттиган декларацешца а, хьоьжуш йолчу конфигурацешца а проблемаш ца хилийта а лело деза <code>enableSemantics</code>, цо тешалла дийр ду оьшу хийцамаш дӀахӀитторан хьалха шордар дӀахӀоттале <code>ExtensionRegistry</code> чухула.\n\nДехар до, хьажа [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] гӀоьнан агӀоне, кхин а информаци йовза.", "smw-upgrade-maintenance-title": "Техникин гӀо » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "ХӀунда го суна хӀара агӀо?", "smw-upgrade-maintenance-note": "Система карарчу хенахь дӀахьош ду [https://www.semantic-mediawiki.org/wiki/Help:Upgrade карлайаккхар] [https://www.semantic-mediawiki.org/ Semantic MediaWiki] шордарца цхьаьна шен хаамийн лармица цхьаьна, ткъа тхуна лаьа шуьга деха собар дан а, викин тӀекхача йиш йуха а хиллалц.", "smw-upgrade-maintenance-explain": "Шордар гӀерта Ӏаткъам а, дӀасацаран хан а лахйапран, шен дукхах долу Ӏалашдаран гӀуллакхаш <code>update.php</code> дӀадаьккхинчул тӀаьхьа дӀататтарца амма цхьаболу базица боьзна хийцамаш хьалха чекхбаха беза, хаа<PERSON><PERSON>ш нийса ца хилийта. Цу йукъа догӀуш хила тарло:\n* Таблицин структураш хийцар масала керла тӀетохар йа лелаш долу меттигаш хийцар;\n* Таблицин индексаш хийцар йа тӀетохар\n* Таблицин оптимизаци йеш (дӀахӀоттийча).", "smw-semantics-not-enabled": "Semantic MediaWiki функци дӀахӀоттийна йац хӀокху вики тӀехь.", "smw_viewasrdf": "RDF хьост", "smw_finallistconjunct": ",", "smw-factbox-head": "... кхин дӀа а \"$1\" лаьцна.", "smw-factbox-facts": "<PERSON>а<PERSON><PERSON><PERSON><PERSON>", "smw-factbox-facts-help": "Гойту дӀахьедарш а, бакъдерш а, декъа<PERSON>хочо кхоьллина долу", "smw-factbox-attachments": "ТӀетохарш", "smw-factbox-attachments-value-unknown": "N/A", "smw-factbox-attachments-is-local": "Ло<PERSON><PERSON><PERSON><PERSON><PERSON> йу", "smw-factbox-attachments-help": "Гойту лелаш долу тӀетохарш", "smw-factbox-facts-derived": "Схьаэцна факташ", "smw-factbox-facts-derived-help": "Гойту бакъонашкара схьадаьлла бакъдерш, йа кхечу хьесапдаран кепех", "smw_isspecprop": "ХӀара башхалла хӀокху вики-сайтан къаьсттина башхаллонах цхьаъ йу.", "smw-concept-cache-header": "Кэш лелор", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count concept cache] чохь йу {{PLURAL:$1|'''one''' entity|'''$1''' entities}} ($2).", "smw-concept-no-cache": "Кэш йац.", "smw_concept_description": "Концептах лаьцна «$1»", "smw_no_concept_namespace": "Концепташ билгалйаха йиш йац, «Концепт:» («Concept:») цӀерийн меттигашкахь бен.", "smw_multiple_concepts": "ХӀора концепт агӀонан цхьа концептан билгалдаккхар бен хила йиш йац.", "smw_concept_cache_miss": "«$1» концепт карарчу хенахь лело йиш йац, хӀунда аьлча вики хӀоттош цуьнан фонан рожехь къасто деза. Нагахь санна и хаам жимма хан йаьлча дӀа ца балахь, хьайн вики-сайтан администраторе йазде, хӀара концепт дӀахӀотто.", "smw_noinvannot": "Йуханехьа йолу билгалонашна маьӀна хӀотто йиш йац.", "version-semantic": "Семантикин шордар", "smw_baduri": "Бехк ма билла, амма \"$1\" диапазонера хьажоргаш кхузахь тӀекхочуш йац.", "smw_printername_count": "Даг<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н жамӀаш", "smw_printername_csv": "CSV экспорт", "smw_printername_dsv": "DSV экспорт", "smw_printername_debug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> нисдар (говзанчашна)", "smw_printername_embedded": "АгӀонашна чубуьллу чулацам", "smw_printername_json": "JSON экспорт", "smw_printername_list": "МогӀа", "smw_printername_plainlist": "РогӀера могӀам", "smw_printername_ol": "Терахьца болу могӀам", "smw_printername_ul": "Билгалбина могӀам", "smw_printername_table": "Таблица", "smw_printername_broadtable": "Шу<PERSON>ра таблица", "smw_printername_template": "<PERSON>е<PERSON>", "smw_printername_templatefile": "Кепан файл", "smw_printername_rdf": "RDF-экспорт", "smw_printername_category": "Категори", "validator-type-class-SMWParamSource": "текст", "smw-paramdesc-limit": "Йухадалийна жамӀийн максимум дукхалла", "smw-paramdesc-offset": "Хьалхара жамӀан офсет", "smw-paramdesc-headers": "Бил<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> кортош/цӀераш гайта", "smw-paramdesc-mainlabel": "Коьрта агӀонан цӀарна луш йолу билгало", "smw-paramdesc-link": "Х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> санна маьӀна гайта", "smw-paramdesc-intro": "Дехаран жамӀашна хьалха гойтуш йолу текст, нагахь санна йелахь.", "smw-paramdesc-outro": "Дехаран жамӀашна тӀаьхьа гойтуш йолу текст, нагахь санна иза йелахь.", "smw-paramdesc-default": "Текст гойтуш йу, нагахь санна хаттаран жамӀаш дацахь", "smw-paramdesc-sep": "МаьӀнаш доькъург", "smw-paramdesc-propsep": "ТӀаьххьара дӀайаздарехь билга<PERSON><PERSON><PERSON>ийн йукъахь доькъург", "smw-paramdesc-valuesep": "ХӀокху билга<PERSON><PERSON><PERSON><PERSON> хийцалуш долчу маьӀнашна йукъахь докъург", "smw-paramdesc-showsep": "CSV файлан лакхахь къастош гайта (\"sep=<value>\")", "smw-paramdesc-distribution": "Дерриге а маьӀнийн метта, церан нисдаларш дагарде, гайта.", "smw-paramdesc-distributionsort": "МаьӀна дӀасадаржор нисдаларан кесталлаца дӀасакъастаде.", "smw-paramdesc-distributionlimit": "Дагардаран маьӀнаш дӀасабаржор цхьацца мах хадорца бен дозатохар.", "smw-paramdesc-aggregation": "Агрегаци хьанна йукъайало йеза билгалдаккха", "smw-paramdesc-template": "Хаа<PERSON><PERSON><PERSON> гойтуш лелор йолчу кепан цӀе", "smw-paramdesc-columns": "Лехаман жамӀаш гойтуш долу бӀогӀамалгийн дукхалла", "smw-paramdesc-userparam": "ХӀора кепан кхайкхамна дӀалуш долу маьӀна ду, нагахь санна кеп лелош йелахь", "smw-paramdesc-class": "К<PERSON><PERSON>н а CSS класс могӀамна", "smw-paramdesc-introtemplate": "Дехаран жамӀашна хьалха гойтуш йолу кепан цӀе, нагахь санна иза йелахь", "smw-paramdesc-outrotemplate": "Дехаран жамӀашна тӀаьхьа гойтуш йолу кепан цӀе, нагахь санна иза йелахь", "smw-paramdesc-embedformat": "HTML тег лелайо кортош гойтуш", "smw-paramdesc-embedonly": "ЦӀераш ма гайта", "smw-paramdesc-table-class": "<PERSON><PERSON><PERSON>н а CSS класс таблицашна", "smw-paramdesc-table-transpose": "Таблици<PERSON> кортош вертикалехь гойту, жамӀаш горизонталехь гойту", "smw-paramdesc-prefix": "ЦӀерийн меттиг зорбане йаккхарехь гайтаран урхалла дар", "smw-paramdesc-rdfsyntax": "Муьлха RDF синтаксис лело йеза", "smw-paramdesc-csv-sep": "Лелайе доькъург", "smw-paramdesc-csv-valuesep": "Лелайе маьӀнан доькъург", "smw-paramdesc-csv-merge": "Цхьаьнатоха бӀогӀамалгийн а, колонкийн а маьӀнаш цхьатерра субъектан ID йолуш (aka хьалхара колонка)", "smw-paramdesc-csv-bom": "ТӀетоха BOM (сигналан духан билгало) арахоьцучу файлан лакхахь", "smw-paramdesc-dsv-separator": "Лелайе доькъург", "smw-paramdesc-dsv-filename": "DSV файлан цӀе", "smw-paramdesc-filename": "Арахе<PERSON>начу файлан цӀе", "smw-smwdoc-description": "Гойту массо а параметрийн таблица, лело мегар долу билгалйаьккхинчу арахецаран форматан, Ӏад йитаран кепан маьӀнаца а, цуьнах лаьцна хаамца а цхьаьна.", "smw-smwdoc-default-no-parameter-list": "ХӀокху жамӀо форматан спецификин параметраш ца ло.", "smw-smwdoc-par-format": "Параметрийн документаци гайтаран жамӀан формат.", "smw-smwdoc-par-parameters": "Муьлха параметр<PERSON>ш гайта йеза: «specific» — формато тӀетоьхначарна, «base» — массо а форматехь йолчунна, ткъа «all» — шинна а.", "smw-paramdesc-sort": "Дехар дӀасакъастош йолу башхалла", "smw-paramdesc-order": "Ха<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> рогӀалла", "smw-paramdesc-searchlabel": "Текст кхидӀа а леха", "smw-paramdesc-named_args": "Кеп чу дӀайеллачу аргументийн цӀераш", "smw-paramdesc-template-arguments": "ЦӀе йолу аргументаш кеп чу муха дӀакхачайо билгалдоккху", "smw-paramdesc-import-annotation": "Кхин а аннотаци йина хаамаш, субъектан парсинг йеш копеш йийр йолуш.", "smw-paramdesc-export": "Экспортан параметраш", "smw-paramdesc-prettyprint": "Хаза зор<PERSON><PERSON><PERSON>н арахецар, цо гойту кхин а дӀатоьхна дӀахилорш а, могӀанийн дӀасакъастарш а", "smw-paramdesc-json-unescape": "Экран йан<PERSON><PERSON> хьаьркаш а, дуккха а байтийн Юникодан хьаьркаш а арахоьцу", "smw-paramdesc-json-type": "Сериализацин тайпа", "smw-paramdesc-source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> альтер<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> хьост", "smw-paramdesc-jsonsyntax": "Лелор йолу JSON синтаксис", "smw-printername-feed": "RSS а, Atom а лента", "smw-paramdesc-feedtype": "Кана<PERSON>ан тайпа", "smw-paramdesc-feedtitle": "Каналан цӀе санна лело текст", "smw-paramdesc-feeddescription": "Кана<PERSON><PERSON>х лаьцна хаамехь лелор йолу текст", "smw-paramdesc-feedpagecontent": "Канал тӀехь гойтур болу агӀонан чулацам", "smw-label-feed-description": "$2-канал $1", "smw-paramdesc-mimetype": "Медиа-файлан тайпа (MIME тайпа) арахоьцучу файлан", "smw_iq_disabled": "Бехк ма билла, амма чудих<PERSON>ина хаттарш дӀадаьхна ду оцу сайтана.", "smw_iq_moreresults": "… тӀаьхьара хиламаш", "smw_parseerror": "ДӀадаханчу маьӀнех кхеташ дацара.", "smw_notitle": "«$1» лело йиш йац хӀокху сайта тӀехь йаззаман цӀе санна.", "smw_noproperty": "«$1» лело йиш йац вики чохь билгалонан цӀе санна.", "smw_wrong_namespace": "«$1» цӀерийн меттигера агӀонаш бен кхузахь лело бакъо йац.", "smw_manytypes": "Цхьана тайпанал сов билгалдаьккхина билгалонан.", "smw_emptystring": "Баьсса могӀамаш магийна бац.", "smw_notinenum": "«$1» йукъа ца йогӀу могӀанан ($2) [[Property:Allows value|магийна маьӀнаш]] «$3» билгалонан.", "smw-datavalue-constraint-error-allows-value-list": "«$1» йукъа ца йогӀу могӀанан ($2) [[Property:Allows value|магийна маьӀнаш]] «$3» билгалонан.", "smw-datavalue-constraint-error-allows-value-range": "«$1» йукъа ца йогӀу дехкарийн  могӀанан ($2) [[Property:Allows value|магийна маьӀнаш]] «$3» билгалонан.", "smw-constraint-error": "Дехкаршца доьзна хаттар", "smw-constraint-error-suggestions": "Дехар до, дагардар дохор а, башхаллаш а церан аннотацица цхьаьна хьажа, массо а декхар<PERSON> кхочушдарх тешарна.", "smw-constraint-error-limit": "МогӀамехь хир ду максимум $1 дохийнарг.", "smw_noboolean": "«$1» — булан маьӀна дац  (хӀаъ/хӀан-хӀа).", "smw_true_words": "бакъ, т, хӀаъ, й", "smw_false_words": "харц,ф,хӀан-хӀа,н", "smw_nofloat": "«$1» — терахь дац.", "smw_infinite": "$1 кхаччалц терахьаш лелош дац.", "smw_unitnotallowed": "«$1» дӀакхайкхийна дац хӀокху билгалонан нийса барамийн цхьаьнакхетаралла санна.", "smw_nounitsdeclared": "ХӀокху билгалонах лаьцна дӀакхайкхийна дакъош дац.", "smw_novalues": "Цхьа а маьӀна дац.", "smw_nodatetime": "Дозушдоцу терахь «$1».", "smw_toomanyclosing": "ГӀалат: ХӀокху хаттарехь «$1» дукха нисло.", "smw_noclosingbrackets": "Схьайоьллуш йолу къовларгаш «<nowiki>[[</nowiki>» дӀакъевлина йацара цуьнца йогӀуш йолу дӀакъовларан параца «]]» хӀокху дехарехь.", "smw_misplacedsymbol": "ГӀалат: ХӀокху меттехь «$1» хьаьрк лелор цхьа а маьӀна долуш дац.", "smw_unexpectedpart": "ГӀалат: «$1» дехаран дакъа ца девзаш ду. ЖамӀаш хиладезачух къаьсташ хила тарло.", "smw_emptysubquery": "ГӀалат: Цхьана бухара дехарех нийса билламан билгало билгалйаьккхина йац.", "smw_misplacedsubquery": "ГӀалат: Бухара дехар леладо бухара дехарш дан бакъо йоцчу меттехь.", "smw_valuesubquery": "Бухара дехарш «$1» билгалонан маьӀнашна ловш дац.", "smw_badqueryatom": "«<nowiki>[[...]]</nowiki>» дехаран дакъа теллина дац.", "smw_propvalueproblem": "ГӀалат: «$1» билгалонан маьӀна теллина дац.", "smw_noqueryfeature": "Дехаран цхьа дакъа дӀадаьккхина, хӀунда аьлча дехарийн меттан цхьайолу таронаш хӀокху ($1) вики тӀехь болх беш йац.", "smw_noconjunctions": "Дехаран цхьа дакъа дӀадаьккхина, хӀунда аьлча «Логикин И» операци хӀокху ($1) вики тӀехь лелаш йац.", "smw_nodisjunctions": "ГӀалат: Ди<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (логикин ЙА) хӀокху сайто лелош йац, цундела уьш лелош долу дехаран дакъа тидамза дитина ($1).", "smw_querytoolarge": "{{PLURAL:$2|1=Билгалбаьккхина биллам|$2 билгалбаьхна билламаш}} дехарш кхочушдан аьтто ца баьлла дехаран кӀоргалла йа барам бахьана долуш: <code>$1</code>.", "smw_notemplategiven": "ХӀара дехар кхочушдархьама, «template» параметран маьӀна йаздан деза.", "smw_db_sparqlqueryproblem": "SPARQL базин дехаран жамӀ схьаэца аьтто ца баьлла. Иза цхьана ханна гӀалат хила тарло, йа базин программица проблема хила тарло.", "smw_db_sparqlqueryincomplete": "ХӀокху дехарна жоп лахар тӀех чолхе нисделла, йукъахделира. Цхьадолу жамӀаш ца гайта а тарло. Хьайн хаттар аттачу даккха хьажа, аьтто балахь.", "smw_type_header": "«$1» тайпанан башхаллаш", "smw_typearticlecount": "{{PLURAL:$1|Гайтина}} $1 {{PLURAL:$1|башхалла}} хӀокху тайпана.", "smw_attribute_header": "“$1” башхалла лелош йолу агӀонаш", "smw_attributearticlecount": "{{PLURAL:$1|Гуш йу}} $1 {{PLURAL:$1|хӀара билгало лелош йолу агӀо|хӀара билгало лелош йолу агӀонаш}}.", "smw-propertylist-subproperty-header": "Бухара башхаллаш", "smw-propertylist-redirect-header": "Синони<PERSON><PERSON>ш", "smw-propertylist-error-header": "Нийса доцу тӀедилларш долу агӀонаш", "smw-propertylist-count": "Гойту $1 {{PLURAL:$1|доьзна дух}}.", "smw-propertylist-count-with-restricted-note": "$1 {{PLURAL:$1|уьйр йолу дух}} гайтар (кхин а алсама ду, амма «$2» бен гайта йиш йац).", "smw-propertylist-count-more-available": "$1 {{PLURAL:$1|уьйр йолу дух}} гайтар (кхин а лела).", "specialpages-group-smw_group": "Семантикин МедиаВики", "specialpages-group-smw_group-maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> кхочушдар", "specialpages-group-smw_group-properties-concepts-types": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, кон<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, та<PERSON><PERSON><PERSON><PERSON>ш", "specialpages-group-smw_group-search": "Хь<PERSON><PERSON><PERSON><PERSON> а, лахар а", "exportrdf": "АгӀонаш RDF тӀе экспорт йар", "smw_exportrdf_docu": "ХӀокху агӀоно аьтто бо йаззаман дакъош RDF форматехь экспорт йан. Хьайна луучу йаззамашна цӀераш йазйе, хӀора могӀанехь цхьацца.", "smw_exportrdf_recursive": "Массо йихкина агӀонийн рекурсиван экспорт. Хилам боккха хила мега!", "smw_exportrdf_backlinks": "Иштта экспорт йе массо агӀонаш, экспорт йинчу агӀонашца хьажийна йолу. Генераци йо RDF йуьззина навигацин гӀоьнца.", "smw_exportrdf_lastdate": "Бил<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у хенахь хийцам бина боцу, агӀонаш экспорт мае.", "smw_exportrdf_submit": "Экспорт", "uriresolver": "URI хуьйцург", "properties": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-categories": "Катего<PERSON><PERSON>ш", "smw_properties_docu": "ХӀокху сайтан тӀехь лелош йу хӀара башхаллаш.", "smw_property_template": "$1 йу тайпа $2, ($3 {{PLURAL:$3|лелор}})", "smw_propertylackspage": "ХӀора башхалла шен-шен хаамийн агӀо хила йеза!", "smw_propertylackstype": "ХӀокху бвшха<PERSON><PERSON>ин хаамийн тайпа дац цуьнца доьзна (тайпа стандартан хир ду $1).", "smw_propertyhardlyused": "ХӀара башхалла наггахь бен лелош йац сайтан техь.", "smw-property-name-invalid": "$1 башхалла лело йиш йац (башхаллин цӀе нийса йац).", "smw-property-name-reserved": "«$1» ларйина цӀе йу, лело ца йеза билгало санна. ХӀара цӀе хӀунда ларйина йу бохучух лаьцна хаамаш хила тарло [https://www.semantic-mediawiki.org/wiki/Help:Property_naming гӀоьнан агӀонгахь].", "smw-sp-property-searchform": "Гайта билгал<PERSON><PERSON><PERSON>, чулацам болу:", "smw-sp-property-searchform-inputinfo": "Чулацам регистр экаме бу, фильтраци йеш лелош хилча, билламца йогӀуш йолу билгало бен ца гойту.", "smw-special-property-searchform": "Гайта билгал<PERSON><PERSON><PERSON>, чулацам болу:", "smw-special-property-searchform-inputinfo": "Чулацам регистр экаме бу, фильтраци йеш лелош хилча, билламца йогӀуш йолу билгало бен ца гойту.", "smw-special-property-searchform-options": "Пара<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Фильтр:", "smw-special-wantedproperties-filter-none": "ХӀумма а", "smw-special-wantedproperties-filter-unapproved": "тӀечӀагӀданза", "smw-special-wantedproperties-filter-unapproved-desc": "Фильтран опци лелайо бакъонан рожца йоьзна", "concepts": "Концепташ", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Концепт<PERSON>ш Концепт] лара мегар ду «динамикин категори» санна, и.дӀ.кх.а. агӀонийн гулам санна, куьйга кхоьллина йоцу, амма Semantic MediaWiki-с ​​гулйина йеллачу дехаран дийцарера.", "smw-special-concept-header": "Концептийн могӀам", "smw-special-concept-count": "МогӀам чохь йу {{PLURAL:$1|концепт|$1 концепташ}}", "smw-special-concept-empty": "Концепташ цакарий.", "unusedproperties": "Лел<PERSON>ш йоцу билгало<PERSON>ш", "smw-unusedproperties-docu": "ХӀокху агӀорахь гайтина [https://www.semantic-mediawiki.org/wiki/Wanted_properties лелош йоцу билгалонаш]. Хьажа: [[Special:Properties|лелош ерш]] йа [[Special:WantedProperties|оьшуш йолу]].", "smw-unusedproperty-template": "$1 йу $2 тайпа", "wantedproperties": "Хаам боца билгалонаш", "smw-wantedproperties-docu": "ХӀокху агӀорахь гойтуш йу [https://www.semantic-mediawiki.org/wiki/Wanted_properties оьшуш йолу билгалонаш], викехь лелош йолу, амма царах лаьцна хаам болу агӀонаш йоцу. Хьажа белхан агӀонаш [[Special:Properties|юьзина]] йа [[Special:UnusedProperties|лелош йоцу билгалонаш йолу]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|лелор}})", "smw-special-wantedproperties-docu": "ХӀокху агӀоно вики чохь лелош йолу [https://www.semantic-mediawiki.org/wiki/Wanted_properties оьшуш йолу башхаллаш] йу, амма шен дийцаран агӀо йац. Къаьстина хьажа белхан агӀонаш [[Special:Properties|тӀекхочуш йолу]] йа [[Special:UnusedProperties|лелош йоцу]] билгалонийн могӀамца.", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|лелор}})", "smw_purge": "Карлайаккха", "smw-purge-update-dependencies": "Semantic MediaWiki-но дӀайоккхуш йу карарчу хенахь йолу агӀонан кэш, шена гучудаьлла цхьадолу ширделла дозуш хилар бахьана долуш, уьш карладаха деза.", "smw-purge-failed": "Semantic MediaWiki гӀоьртира агӀо карлайаккха, амма аьтто ца баьлла", "types": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_types_docu": "[https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes ТӀекхочучу хаамийн тайпанийн] могӀам, цу чохь хӀора [https://www.semantic-mediawiki.org/wiki/Help:Datatype хааман тайпана] атрибутийн шатайпа гулдар ду, маьӀна Ӏалашдаран а, гайтаран а агӀор, уьш хӀитто йиш йу билгалонашна.", "smw-special-types-no-such-type": "\"$1\" хууш дац йа нийса хаамийн тайпа санна билгалдаьккхина дацара.", "smw-statistics": "Семан<PERSON>и<PERSON><PERSON>н статистика", "smw-statistics-cached": "Семан<PERSON>и<PERSON><PERSON>н статистика (кэш йина)", "smw-statistics-entities-total": "ХӀуманаш (йерриге а)", "smw-statistics-entities-total-info": "Гергарчу хьесапехь хӀуманийн могӀанаш дагарбар. Цу йукъайогӀу билгалонаш, кхетамаш, муьлхха а кхин дӀайазйина объектийн репрезентацеш, шайна тӀейилла йезаш йолу идентификатор.", "smw-statistics-property-instance": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{PLURAL:$1|маьӀна}} (шад<PERSON><PERSON><PERSON>)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Билгало}}]] (ша<PERSON><PERSON><PERSON>ш)", "smw-statistics-property-total-info": "Берриге а дӀайазбина билгалонаш.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Башхалла}} (ша<PERSON><PERSON><PERSON><PERSON>)", "smw-statistics-property-used": "{{PLURAL:$1|Башхалла}} (цхьана маьӀнехь лелина а лелош йу)", "smw-statistics-property-page": "{{PLURAL:$1|Башхалла}} (дӀайазйина йу агӀонгахь)", "smw-statistics-property-page-info": "Дагарйе объекташ шен агӀо а йолуш, хаам а.", "smw-statistics-property-type": "{{PLURAL:$1|Билгало}} (хаа<PERSON>ийн тайпанашна хӀоттина йу)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Дехар}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Дехар}}]] (дӀахӀоттийна, шайерш)", "smw-statistics-query-format": "<code>$1</code> формат", "smw-statistics-query-size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> барам", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|1=Концепт|Концепташ}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|1=Концепт|Концепташ}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Бухара объект}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Бухара объект}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|1=Хаа<PERSON>ийн тайпа|Хаа<PERSON>ийн тайпанаш}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Билгалонан маьӀна|Билгалонийн маьӀнаш}} ([[Special:ProcessingErrorList|{{PLURAL:$1|нийса йоцучу аннотацин|нийса йоцучу аннотацийн}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Билгалонан маьӀна|Билгалонийн маьӀнаш}} ({{PLURAL:$1|нийса йоцу аннотацин|нийса йоцу аннотацийн}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Ширйелла объект|Ширйелла объекташ}}]", "smw-statistics-delete-count-info": "ДӀайаккха билгалйаьхна хӀуманаш дӀайаха йеза кест-кеста Ӏалашдаран скрипташ лелош.", "smw_uri_doc": "URI ресолверо кхочушдо [$1 W3C лехар http тегашна Range-14 лелош].\nЦо дӀакхачадо RDF гайтам (машинашна) йа вики-агӀо (ад<PERSON><PERSON><PERSON><PERSON><PERSON>) дехаре хьаьжжина.", "ask": "Сема<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> лахар", "smw-ask-help": "ХӀокху декъехь йу <code>#ask</code> синтаксис лелорца гӀо деш йолу хьажоргаш.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecting pages] агӀонаш муха къасто йеза а, балламаш муха хӀиттабо а дуьйцу\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Search operators] лехаман операторийн могӀам, шайна йукъахь диапазонан лехаман операторш а, хьаьркаш а йолуш\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Displaying information] зорба тохаран хьелаш а, форматан опцеш а лелор билгалдоккху", "smw_ask_sortby": "БӀогӀамца дӀасакъастаде (лаамехь)", "smw_ask_ascorder": "Йоккха хиларца", "smw_ask_descorder": "КӀезиг хиларца", "smw-ask-order-rand": "нисйелларг", "smw_ask_submit": "Лаха", "smw_ask_editquery": "<PERSON>е<PERSON><PERSON><PERSON> тадан", "smw_add_sortcondition": "[ДӀасакъасторан хьал тӀетоха]", "smw-ask-sort-add-action": "ДӀасакъасторан хьал тӀетоха", "smw_ask_hidequery": "Дех<PERSON>р къайладаккха (компактан хатӀ)", "smw_ask_help": "Хаттаршца гӀо дар", "smw_ask_queryhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_printhead": "З<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> харжар", "smw_ask_printdesc": "(хӀора могӀанехь цхьа билгалонан цӀе тӀетоха)", "smw_ask_format_as": "Формат иштта:", "smw_ask_defaultformat": "Ӏадйитаран кеп", "smw_ask_otheroptions": "К<PERSON>ин болу нисдаран гӀирс", "smw-ask-otheroptions-info": "ХӀокху декъехь йу арахецаран низам хийцалуш йолу опцеш. Параметрийн дийцарш хьажа йиш йу, царна тӀе дехкан курсор хьажийна.", "smw-ask-otheroptions-collapsed-info": "Дехар до, лелайе плюсан веназ, массо а лелаш йолу параметраш хьажа", "smw_ask_show_embed": "Гойта чуйиллина код", "smw_ask_hide_embed": "Къайлайаккха чуйиллина код", "smw_ask_embed_instr": "ХӀара дехар могӀанан кепара вики агӀонна чу дилла, лелайе лахахь йолу код.", "smw-ask-delete": "ДӀайаккха", "smw-ask-sorting": "Сорташ хӀиттор", "smw-ask-options": "Нисдаран гӀирс", "smw-ask-options-sort": "Сорташ хӀитторан нисдаран гӀирс", "smw-ask-format-options": "Формат а, опцеш а", "smw-ask-parameters": "Пара<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-search": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-debug": "Нис<PERSON><PERSON><PERSON>", "smw-ask-debug-desc": "Деха<PERSON>ан нисдарах информацин генераци йо", "smw-ask-no-cache": "Кэш дӀацӀанйан", "smw-ask-no-cache-desc": "Деха<PERSON>ан кэш йоцуш жамӀаш", "smw-ask-result": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-empty": "Дерриге дӀайаздар дӀацӀандан", "smw-ask-download-link-desc": "Дехар дина жамӀаш чудаха $1 форматехь", "smw-ask-format": "Формат", "smw-ask-format-selection-help": "Ха<PERSON><PERSON><PERSON>инчу форматан гӀо: $1.", "smw-ask-condition-change-info": "Биллам хийцабелла, лехаман системо лехамаш карлабаха беза, керлачу лехамашца догӀуш долу жамӀаш йухадерзо.", "smw-ask-input-assistance": "Чудахарца гӀо дар", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Йаздаран гӀо] луш ду зорба<PERSON><PERSON>, дІасакъасторан а, билламан а меттигашна. Билламан меттигехь хӀокху дешхьалхенех цхьаъ хила йеза:", "smw-ask-condition-input-assistance-property": "<code>p:</code> билга<PERSON><PERSON>нашна лерина хьехам йукъабаккха (масала <code>[[p:Ду ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> категорешца хьехам схьаэца", "smw-ask-condition-input-assistance-concept": "<code>con:</code> концепцин хьехам схьаэца", "smw-ask-format-change-info": "Форма<PERSON> хийцина, дехар йуха а дан деза, керлачу параметрашца а, визуализацин параметрашца а догӀуш.", "smw-ask-format-export-info": "Ха<PERSON><PERSON><PERSON><PERSON>на формат экспортан формат йу, бӀаьран репрезентаци йоцуш, цундела жамӀаш дӀадаьхначул тӀаьхьа бен ца хуьлу.", "smw-ask-query-search-info": "<code><nowiki>$1</nowiki></code> дехарна жоп делла {{PLURAL:$3|1=<code>$2</code> (кэш чуьра)|<code>$2</code> (кэш чуьра)|<code>$2</code>}} $4 {{PLURAL:$4|секунд}} чохь.", "smw-ask-extra-query-log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> жур<PERSON>л", "smw-ask-extra-other": "К<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchbyproperty": "Билгалонца лахар", "processingerrorlist": "Кечдарийн гӀалатийн могӀам", "constrainterrorlist": "Дехкарийн гӀалатийн могӀам", "propertylabelsimilarity": "Бил<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н билгало цхьатера хиларан хаам", "missingredirectannotations": "ДӀасахьажоргийн аннотаци йац", "smw-processingerrorlist-intro": "ХӀокху могӀамехь гойту [https://www.semantic-mediawiki.org/ Semantic MediaWiki] уьйрца доьзна хиллачу [https://www.semantic-mediawiki.org/wiki/Processing_errors кечйаран гӀалаташ]. Рекомендаци йо хаддаза оцу могӀамна тӀехь терго латтор а, нийса йоцу мехаллин аннотацеш нисйар а.", "smw-constrainterrorlist-intro": "ХӀокху могӀамехь дуьйцу [https://www.semantic-mediawiki.org/wiki/Constraint_errors доза тохаран гӀалаташ] хьокъехь, иза гучудаьлла [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. И могӀам хаддаза тергонехь латто а, маьӀнийн нийса йоцу аннотацеш нисйан а дика ду.", "smw-missingredirects-intro": "ХӀокху декъехь дӀайазйийр йу [https://www.semantic-mediawiki.org/wiki/Redirects redirect] аннотацеш йоцуш йолу агӀонаш Semantic MediaWiki тӀехь (MediaWiki тӀехь Ӏалашбина информацица йуьстича) а, и аннотацеш меттахӀитто а йа куьйга [https://www.semantic-mediawiki.org/wiki/Help:Purge purge]. <code>rebuildData.php</code> Ӏалашйаран скрипт (<code>--redirects</code> опцица).", "smw-missingredirects-list": "Аннота<PERSON><PERSON>ш йоцуш йолу агӀонаш", "smw-missingredirects-list-intro": "Гайтина $1 {{PLURAL:$1|агӀо}} ца тоьуш дӀасахьажоргийн аннотацеш йолуш.", "smw-missingredirects-noresult": "Цхьа а аннотаци ца карийна дӀасахьажоргна.", "smw_sbv_docu": "Билгалдаьккхина билгало а йолуш, маьӀна а долуш йолу массо а агӀонаш леха.", "smw_sbv_novalue": "$1 билгалонан маьӀна билгалдаккха йа массо а маьӀна хьажа.", "smw_sbv_displayresultfuzzy": "«$2» маьӀна долуш, чохь «$1» билгало а йолуш массо а агӀонийн могӀам.\nНийса жамӀийн терахь жима хиларна, гойту агӀонаш а, шайна чохь дӀайеллачу билгалонан цхьатерра маьӀна долуш.", "smw_sbv_property": "Башхалла:", "smw_sbv_value": "МаьӀна:", "smw_sbv_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "browse": "Сайте хьажа", "smw_browselink": "Хьажа агӀонах дерг", "smw_browse_article": "Таллам дӀаболорхьама агӀонан цӀе йазйе.", "smw_browse_go": "Дехьа гӀо", "smw_browse_show_incoming": "ЧуйогӀу билга<PERSON><PERSON><PERSON><PERSON> гайта", "smw_browse_hide_incoming": "Кхузахь хьажорг долу билг<PERSON><PERSON><PERSON><PERSON><PERSON> къайлайаха", "smw_browse_no_outgoing": "ХӀокху агӀонан чохь билгало<PERSON><PERSON> йац.", "smw_browse_no_incoming": "ХӀокху агӀонна тӀе хьажорг йолуш цхьа а башхалла йац.", "smw-browse-from-backend": "Карарчу хенахь информаци схьаоьцуш йу серверан программин чуьра.", "smw-browse-intro": "ХӀокху агӀонан тӀехь дуьйцу субъектах йа субъектан инстанцех лаьцна, дехар ду йазйе таллам бан безачу субъектан цӀе.", "smw-browse-invalid-subject": "Субъектан валидацино йухадерзийна \"$1\" гӀалат.", "smw-browse-api-subject-serialization-invalid": "Субъектан нийса йоцу сериализацин формат йу.", "smw-browse-js-disabled": "JavaScript дӀайаьккхина йа лело йиш йоцуш санна хетало. И ловш йолу браузер лело рекомендаци йо оха. Кхин варианташ йийцаре йина конфигурацин параметран агӀонан тӀехь [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Гайта тобанаш", "smw-browse-hide-group": "То<PERSON><PERSON><PERSON><PERSON> къайлайаха", "smw-noscript": "ХӀокху агӀонан йа ардаман JavaScript оьшу болх бан. Дехар до, хьайн браузерехь JavaScript дӀахӀоттайе, йа леладе иза лелош йолу браузер, и функционал дӀайала йиш хилийта дехарца. Нагахь санна хьайна кхин а гӀо оьшуш делахь, хьажа гӀоьнан агӀоне [https://www.semantic-mediawiki.org/wiki/Help:Noscript «Noscript»].", "smw_inverse_label_default": "$1 чуьра", "smw_inverse_label_property": "ЙухайогӀу билга<PERSON><PERSON><PERSON><PERSON> башхаллан", "pageproperty": "Башхалла лехаран агӀо", "pendingtasklist": "ТӀаьхьататтийна Ӏалашонийн могӀам", "facetedsearch": "АгӀонаш лехар", "smw_pp_docu": "АгӀо а, башхалла а, йа йеккъа башхалла а йазде, массо а тӀедиллина маьӀна схьаэцархьама.", "smw_pp_from": "АгӀонан тӀера:", "smw_pp_type": "Башхалла:", "smw_pp_submit": "ЖамӀаш лехар", "smw-prev": "хьалхара {{PLURAL:$1|$1}}", "smw-next": "рогӀера {{PLURAL:$1|$1}}", "smw_result_prev": "Хь<PERSON><PERSON><PERSON><PERSON><PERSON>а", "smw_result_next": "РогӀера", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "Бехк ма биллалахь, амма хӀумма а ца карийна.", "smwadmin": "Semantic MediaWiki гӀирсийн панель", "smw-admin-statistics-job-title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> статистика", "smw-admin-statistics-job-docu": "Белхан статистико гойту хӀинца а кхочуш ца дина дӀахӀиттийначу Semantic MediaWiki белхех лаьцна информаци. Белхан лоруш хилар нийса ца хила тарло йа уьш кхочушдан гӀертарш хила тарло. Кхин дӀа хаа [https://www.mediawiki.org/wiki/Special:MyLanguage/manual:Job_queue куьйгаллин агӀонехь] информаци хьажа.", "smw-admin-statistics-querycache-title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> кэш", "smw-admin-statistics-querycache-disabled": "[https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] хӀокху вики тӀехь дӀахӀоттийна йац, цундела статистика тӀекхочуш йац.", "smw-admin-statistics-querycache-legend": "Кэш статистика ханна гулбина а, иштта схьабевлла а хаамаш хила беза, шайна йукъахь:\n* «misses» санна йерриг гӀертарш схьаэца хаамаш кэш чуьра кхача йиш йоцучу жоьпашца, ницкъ беш ма-дарра Ӏалашдаран (БД, трипл-сторан и. дӀ. кх.) схьаэца\n* «deletes» кэш дӀайаккхаран операцин йерриг барам санна (йа цӀанйарца, йа хаттаран дозуш хиларца)\n* «hits» чохь бу кэш схьаэцаран барам йа чуйиллина (вики агӀонна чуьра кхайкхина хаттарш) йа чуйиллина йоцу (нагахь санна дӀахӀоттийна йелахь, Special:Ask йа API санна йолчу агӀонаша дехар дина) хьостанашкара\n* «medianRetrievalResponseTime» — ориентацин маьӀна йуккъера жоьпан хенан (сек.) кэшехь а, кэшехь доцучу а лехаман дехаршна гулдаран процессан хенан йукъахь\n* «noCache» гойту кэш чуьра жамӀаш схьаэца гӀертаран дехарийн барам (limit=0 дехарш, 'no-cache' опци и. дӀ. кх. а)", "smw-admin-statistics-section-explain": "Дакъа тӀехь дӀало кхин а статистика администраторшна.", "smw-admin-statistics-semanticdata-overview": "<PERSON>у<PERSON>е", "smw-admin-permission-missing": "ХӀокху агӀонна тӀекхача йиш йац. Дехар до, хьажа [https://www.semantic-mediawiki.org/wiki/Help:Permissions гӀоьнан] агӀоне, кхин дӀа информаци хьажархьама.", "smw-admin-setupsuccess": "ХӀума латторан система дӀахӀоттийна.", "smw_smwadmin_return": "ЙухагӀо агӀоне $1.", "smw_smwadmin_updatestarted": "Семантикин хаамаш карлабахаран керла процесс йолийна.\nДерриге а лардина хаамаш йуха а дӀахӀиттор бу, оьшучу меттехь меттахӀиттор бу.\nКарладаккхаран дӀадахарна тӀехь терго латто йиш йу хьан хӀокху белхан агӀонан тӀехь.", "smw_smwadmin_updatenotstarted": "Цхьа карлайаккхаран процесс хӀинцале а дӀахьош йу.\nКхин цхьаъ а ца кхоьллина.", "smw_smwadmin_updatestopped": "Дерриге а лелаш йолу карладаккхаран процессаш сецна.", "smw_smwadmin_updatenotstopped": "ДӀайоьдуш йолу карладаккхаран процесс сацорхьама, ахьа билгало хӀотто йеза цу сацамах хьо баккъалла а тешна хилар гойтуш.", "smw-admin-docu": "ХӀокху белхан агӀоно гӀодийр ду шуна <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a> шордар дӀахӀоттош а, карлайаккхуш а, Ӏалашйеш а, лелош а процессехь.\nАдминистративан ардамаш беш хьалха мехала хаамаш копий йан дагахь латтаде.", "smw-admin-environment": "Програм<PERSON>ин чкъор", "smw-admin-db": "<PERSON><PERSON><PERSON><PERSON><PERSON>н база нисйар", "smw-admin-db-preparation": "Таблица дӀахӀоттош йу. Цхьа хан йала тарло жамӀаш а, хила тарлуш йолу таблицан оптимизаци а гайтале.", "smw-admin-dbdocu": "Semantic MediaWiki-на оьшу шен базийн структура, маьӀнин хаамаш Ӏалашбан. Иза MediaWiki-х йозуш йац, цундела кхечу MediaWiki-н инсталляцешна Ӏаткъам ца бо.\n\nИ процедура масийттаза йан йиш йелахь а, цхьа а тӀаьхьало йоцуш, иза цкъа бен дӀайахьа ца оьшу — Semantic MediaWiki дӀахӀотточу хенахь йа карлайаьккхинчу хенахь.", "smw-admin-permissionswarn": "SQL-коман<PERSON><PERSON><PERSON> кхочушйеш кхачамбацарш хиларан бахьана хила тарло ахьа вики-базаца зӀе латточу лелоран цӀарах оьшуш йолу бакъонаш цахилар (хьажа LocalSettings.php файле).\nХӀокху декъашхочунна кхин а бакъонаш йала хьажа таблицаш кхолла а, дӀайаха а; ханна логин \"root\" йазде LocalSettings.php файлехь, йа леладе <code>setupStore.php</code> сервисан скрипт, цуо лело мегар ду администраторан параметраш.", "smw-admin-dbbutton": "Таблиц<PERSON>шна инициализаци йар йа карлайаккхар", "smw-admin-announce": "Хьан вики чохь кхайкхам", "smw-admin-announce-text": "Нагахь санна, хьан вики йукъара йелахь, ахьа иза дӀайазйан мегар ду <a href=\"https://wikiapiary.com\">WikiApiary</a> тӀехь.", "smw-admin-deprecation-notice-title": "Дехкарх лаьцна билгалдах<PERSON><PERSON>ш", "smw-admin-deprecation-notice-docu": "РогӀерчу декъехь ширйелла йа дӀайаьхна параметраш, амма хӀинца а жигара йу хӀокху вики тӀехь. ХӀора карладаккхаро дӀадаккха деза оцу конфигурацешна гӀортор.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ширйелла йу, $2 чохь дӀайоккхуш йу", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> дӀайоккхур йу (йа заменит) {{PLURAL:$2|рогӀера опци|рогӀера опцеш}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> ширйелла, $2 чохь дӀайоккхур йу", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> хийцина → <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> хийцина → <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|опци|опцеш}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> хийцина → <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> дӀайаьккхина $2 чохь", "smw-admin-deprecation-notice-title-notice": "Нисдаран гӀирс ширбелла", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Ширб<PERSON><PERSON>ла гӀирс</b> гойту хӀокху вики тӀехь лелош гучудаьлла, тӀейогӀучу релизехь дӀайаха йа хийца лерина йолу параметраш.", "smw-admin-deprecation-notice-title-replacement": "Хийцина йа цӀе хийцина нисдаран гӀирс", "smw-admin-deprecation-notice-title-replacement-explanation": "<b><PERSON><PERSON><PERSON>ина йа цӀе хийцина параметраш</b> ― чохь йу цӀераш хийцина йа кхечу кепара хийцина параметраш, ахьа сихонца церан цӀераш йа формат карлайаккхар дика ду.", "smw-admin-deprecation-notice-title-removal": "ДӀабаьхна нисдаран гӀирсаш", "smw-admin-deprecation-notice-title-removal-explanation": "<b>ДӀабьхна нисдаран гӀирсаш</b> ― билгалйоху хьалха арайаьллачу релизехь дӀабаьхна болу, амма хӀинца а хӀокху вики тӀехь лелош болу нисдаран гӀирсаш.", "smw-admin-deprecation-notice-section-legend": "Легенда", "smw-smwadmin-refresh-title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> нисбар а, кар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р а", "smw_smwadmin_datarefresh": "Хаа<PERSON>аш йухаметтахӀиттор", "smw_smwadmin_datarefreshdocu": "Ахьа меттахӀотто йиш йу массо а Semantic MediaWiki-н хаамаш карарчу хенахь болчу вики чулацамна тӀе а тевжаш.\nИза пайде хила тарло боьхна хаамаш меттахӀитторехь, цхьацца программаш карлайахар бахьана долуш чоьхьара формат хийцаеллехь хаамаш карлабахарехь.\nКарлайаккхар дӀахьур ду агӀо агӀонца, цхьа хан оьшур йу.\nБухахь хьажа йиш йу, муха дӀайоьду карладаккхаран процесс, карладаккхар дӀадоло йа сацо аьтто луш (нагахь санна и функци сайтан администраторо дӀайаьккхина йацахь).", "smw_smwadmin_datarefreshprogress": "<strong>Керладаккхар хӀинцале а дӀахьош ду.</strong>\nКарладаккхаран процесс дика меллаша хилар нийса ду, хӀунда аьлча цо декъашхо вики чу кхаьчча, кегийчу барамашкахь бен хаамаш ца карлабоху.\nИ карладаккхар сиха чекхдаккха, ахьа кхайкха мегар ду MediaWiki <code>runJobs.php</code> Ӏалашйаран скрипт (лелайе <code>- maxjobs 1000</code> параметр, цхьана парти чохь карладаккхаран дукхалла лахдан).\nХӀинцалерчу карладаккхаран гергарчу хьесапехь кхиамаш:", "smw_smwadmin_datarefreshbutton": "График<PERSON>а хаамаш меттахӀиттор", "smw_smwadmin_datarefreshstop": "ХӀара карладаккхар сацаде", "smw_smwadmin_datarefreshstopconfirm": "ХӀаъ, {{GENDER:$1|со тешна ву|со тешна йу}}.", "smw-admin-job-scheduler-note": "ХӀокху декъехь жигара Ӏалашонаш кхочушдо Task Scheduler рогӀехь, уьш лелачу хенахь блоктохарш ца хилийта. [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue ПланхӀотторго] дӀайахьарна жоп дала деза, цундела чӀогӀа мехала ду <code>runJobs.php</code> сценарин кхачам боллуш ресурсаш хилар (хьажа иштта конфигурацин параметр <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Ширйелла хӀуманаш дӀайахар", "smw-admin-outdateddisposal-intro": "Цхьаболу ардамаш (бил<PERSON><PERSON><PERSON><PERSON><PERSON>н тайпа хийцар, вики-агӀонаш дӀайахар, йа гӀалат долу маьӀнаш нисдар) [https://www.semantic-mediawiki.org/wiki/Outdated_entities ширйелла хӀуманаш] кхойкхур йу, цундела рекомендаци йо уьш хан-зама йаларца дӀадаха цуьнах йоьзна йолчу таблицехь меттиг мукъайаккхар.", "smw-admin-outdateddisposal-active": "Ширйелла хӀуманаш дӀайаха болх тӀебиллина.", "smw-admin-outdateddisposal-button": "ДӀайаккха план хӀотто", "smw-admin-feature-disabled": "ХӀара функци дӀайаьккхина йу хӀокху вики тӀехь. Дехар до, хьажа гӀоьнан агӀо <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">нисдаран гӀирсах лаьцна болу</a>, йа системин администраторе йазде.", "smw-admin-propertystatistics-title": "Бил<PERSON><PERSON><PERSON><PERSON><PERSON>н статистика йухадӀахӀоттор", "smw-admin-propertystatistics-intro": "Йерриге а билгало лелоран статистика йухахӀиттайо, цул тӀаьхьа карлайоккху, нисйо билгало [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count лелоран лорург].", "smw-admin-propertystatistics-active": "Бил<PERSON><PERSON><PERSON><PERSON><PERSON>н статистика йухахӀотторехь болх бан лерина.", "smw-admin-propertystatistics-button": "Башхалла йухахӀотторан график хӀоттайе", "smw-admin-fulltext-title": "Йуьззина текст лехамаш йухахӀиттор", "smw-admin-fulltext-intro": "ЙухахӀоттадо лехаман индекс билгалонан таблицашкара [https://www.semantic-mediawiki.org/wiki/Full-text йуьззина текстан лехар] дӀахӀоттийначу хаамийн тайпанца. Индексацин бакъонашкахь хийцамаш баро (хийцаделла гӀовгӀанан дешнаш, керла стемер, и. дӀ. кх.) а,/йа керла тӀетоьхна йа хийцина таблица а и болх йуха а дӀабахьа беза.", "smw-admin-fulltext-active": "Йуьззина текстан лехамаш йухахӀитторехь болх бан лерина бара.", "smw-admin-fulltext-button": "Йуьззина текст йухахӀотторан график хӀоттайе", "smw-admin-support": "ГӀо схьаэцар", "smw-admin-supportdocu": "Тайп-тайпана ресурсаш йу хьуна гӀо дан, нагахь санна хьан проблемаш йелахь:", "smw-admin-installfile": "Нагахь санна хьайна хало йелахь дӀахӀоттош, дӀадоладе <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">файлехь INSTALL</a> а, <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">дӀахӀотторан агӀонгахь</a> болу дӀахӀотторан хьехам бешна.", "smw-admin-smwhomepage": "Йуьззина Semantic MediaWiki лелоран документаци йу<b><a href=\"https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_-_%D0%B7%D0%B0%D0%B3%D0%BB%D0%B0%D0%B2%D0%BD%D0%B0%D1%8F_%D1%81%D1%82%D1%80%D0%B0%D0%BD%D0%B8%D1%86%D0%B0\">semantic-mediawiki.org</a></b> тӀехь.", "smw-admin-bugsreport": "ГӀалатех лаьцна хаам бан йиш йу <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>, ткъа <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">догӀуш долу гӀоьнан декъан агӀонан</a> тӀехь цхьацца хьехам бу проблемех лаьцна эффективан хаам муха йазбан беза.", "smw-admin-questions": "<PERSON><PERSON><PERSON><PERSON> кхин а хаттарш йа хьехамаш белахь, дийцаре дӀакхета <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">Semantic MediaWiki декъашхойн дIасатасаран могӀам</a>. тӀехь.", "smw-admin-other-functions": "Кхийолу функцеш", "smw-admin-statistics-extra": "Статисти<PERSON><PERSON><PERSON> функцеш", "smw-admin-statistics": "Статистика", "smw-admin-supplementary-section-title": "<PERSON><PERSON>ин а функцеш", "smw-admin-supplementary-section-subtitle": "ТӀамаран ловш йолу функцеш", "smw-admin-supplementary-section-intro": "ХӀокху секцехь кхин а функцеш йу, гӀоьнан йолчийл сов. Хила тарло цхьайолу функцеш (хьажа [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions документаци]) дозатоьхна, цундела хӀокху вики тӀехь йац.", "smw-admin-supplementary-settings-title": "Конфигураци а, нисдаран гӀирс а", "smw-admin-supplementary-settings-intro": "<u>$1</u> гойту Semantic MediaWiki лелар къастош йолу параметраш", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Оопера<PERSON><PERSON><PERSON> статистика", "smw-admin-supplementary-operational-statistics-short-title": "операцин статистика", "smw-admin-supplementary-operational-statistics-intro": "Гойту шуьйра <u>$1</u> гулам", "smw-admin-supplementary-idlookup-title": "ХӀуман лахар а, дӀайаккхар а", "smw-admin-supplementary-idlookup-short-title": "духа<PERSON> лехар а, дӀайахар а", "smw-admin-supplementary-idlookup-intro": "«<u>$1</u>» цхьалха функци ловш йу", "smw-admin-supplementary-duplookup-title": "ХӀуманаш-дубли<PERSON><PERSON><PERSON><PERSON><PERSON> хьажар", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> — ха<PERSON>р<PERSON>инчу таблицин матрицехь дубликаташ санна классификаци йина хӀуманаш лаха", "smw-admin-supplementary-duplookup-docu": "ХӀокху агӀонан тӀехь йу [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities дубликаташ] санна категорешкахь йолу хаьржина таблицаш тӀера хӀуманаш.\nДубликаташ наггахь бен (нагахь санна цкъа а) гучуйовлу базийн карладаккхар дӀадаьккхина йа транзакци йухайерзор бахьана долуш бен.", "smw-admin-supplementary-operational-statistics-cache-title": "Кэ<PERSON>ан статистика", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> гойту кэшца йоьзна статистикин хаьржина тоба", "smw-admin-supplementary-operational-table-statistics-title": "Табли<PERSON><PERSON><PERSON> статистика", "smw-admin-supplementary-operational-table-statistics-short-title": "таблицийн статистика", "smw-admin-supplementary-operational-table-statistics-intro": "Генера<PERSON>и йо <u>$1</u> хаьржинчу таблицийн тобанна", "smw-admin-supplementary-operational-table-statistics-explain": "ХӀокху декъехь йу хаьржина таблицин статистика, цо гӀо дийр ду администраторшна а, хаамийн кураторшна а серверан дакъош а, хаамаш  а механизмех лаьцна нийса сацамаш бан.", "smw-admin-supplementary-operational-table-statistics-legend": "Легендано дуьйцу таблицин статистикан лелош йолу цхьайолу догӀанаш, цу йукъахь йу:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> таблицехь могӀанийн массо а дукхалла", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> карарчу хенахь лелош йолу тӀаьххьара идентификатор\n* <code>duplicate_count</code> id_table таблицехь карийна дубликатийн дукхалла (хьажа иштта [[Special:SemanticMediaWiki/duplicate-lookup|Духийн дубликаташ лахар]])\n* <code>rows.rev_count</code> могӀанийн терахь, тӀедиллина revision_id йолуш, вики агӀонна тӀе ма-дарра хьажорг гойтуш\n* <code>rows.smw_namespace_group_by_count</code> таблицехь лелош йолчу цӀерийн меттигийн гулдина могӀанийн дукхалла\n* <code>rows.smw_proptable_hash.query_match_count</code> нийса догӀу таблицин хьажоргца дехаран бухара объектийн дукхалла\n* <code>rows.smw_proptable_hash.query_null_count</code> дехаран бухара объектийн дукхалла, таблицин хьажорг йоцуш (зӀе йоцуш, лела хьажоргаш)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> шатайпа терминийн процент (лахара процент гойту шалха терминаш таблицин чулацам а, ин<PERSON><PERSON><PERSON><PERSON> а дӀалоцуш хилар)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> цкъа бен ца нислуш долу терминийн дукхалла\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> цкъа сов нислуш долу терминийн дукхалла", "smw-admin-supplementary-elastic-version-info": "Верси", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> гойту ма-йарра кэш-хӀоттамаш а, статистика а", "smw-admin-supplementary-elastic-docu": "ХӀокху агӀонехь йу ''Semantic MediaWiki''ца а, цуьнан Ӏалашдарца а йоьзна йолу ''ElasticSearch'' кластерца йоьзна йолу нисдаран гӀирсаш, индексан хьолан а, статистика а [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Ловш йолу функцеш", "smw-admin-supplementary-elastic-settings-title": "Нисдаран гӀирсаш (индек<PERSON><PERSON><PERSON>)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> лелайо ''ElasticSearch'' ''Semantic MediaWiki'' индексашна урхалла дан", "smw-admin-supplementary-elastic-mappings-title": "ДогӀурш", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> — индексийн а меттиган йогӀуш хилар а", "smw-admin-supplementary-elastic-mappings-docu": "ХӀокху агӀонан тӀехь йу карарчу хенахь индексо лелош йолу меттигийн картин детальш. Рекомендаци йу догӀуш хиларан тӀехь терго латто, <code>index.mapping.total_fields.limit</code> хийцаман маьӀнан тидаме а оьцуш, цуо Ӏалашдо индексехь уггаре дукха бакъо йелла меттигаш.", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code> гойту индексаци йина коьрта меттигийн дукхаллин терахь, ткъа <code>nested_fields</code> гойту гулдина дукхаллин терахь кхин а коьртачу меттигна тӀедиллина, билггал йолу структурехь лехаман кепашна гӀо дан.", "smw-admin-supplementary-elastic-mappings-summary": "<PERSON>уьнах лаьцна", "smw-admin-supplementary-elastic-mappings-fields": "Мет<PERSON><PERSON><PERSON><PERSON><PERSON> йогӀуш хилар", "smw-admin-supplementary-elastic-nodes-title": "<PERSON>е<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> гойту шедийн статистика", "smw-admin-supplementary-elastic-indices-title": "Индек<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> дӀахьо лелаш йолчу индексийн а, церан статистикин а йукъара гайтам", "smw-admin-supplementary-elastic-statistics-title": "Статистика", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> гойту индексан тӀегӀан статистика", "smw-admin-supplementary-elastic-statistics-docu": "ХӀокху агӀоно индексан тӀегӀанехь дӀахьочу тайп-тайпанчу операцешна индексан статистиках кхетам ло, йухайерзош йолу статистика гулйо йуьхьанцара а, чулацаман а гулдаршца. [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html ГӀоьнан агӀо] тӀехь дӀайаздина ду лелаш йолчу индексийн статистикин хьокъехь.", "smw-admin-supplementary-elastic-status-replication": "Репликаци статус", "smw-admin-supplementary-elastic-status-last-active-replication": "ТӀаьххьара жигара репликаци: $1.", "smw-admin-supplementary-elastic-status-refresh-interval": "Карладаккхаран интервал: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "МеттахӀотторан хан: $1 (гергарчу хьесапехь)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Чекхбахаза белхийн журнал (файл): $1 (герггарчу хьесапехь)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Репликацин блоктоьхна: $1 (йухадӀахӀиттор процессехь)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Репликацин мониторинг (жигара): $1", "smw-admin-supplementary-elastic-replication-header-title": "Копи йа<PERSON>ан статус", "smw-admin-supplementary-elastic-replication-function-title": "Копи йар", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> гойту кхачамбацарш хилла репликацех лаьцна информаци", "smw-admin-supplementary-elastic-replication-docu": "ХӀокху агӀонан тӀехь информаци йу [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring репликацин статусехь] духех лаьцна, хьан Elasticsearch кластерца проблемаш хиларх лаьцна хаам бина. Духийн могӀам хьажа а, церан чулацам дӀабаккха а дика ду, и проблема цхьана ханна хилла хиларх тешарна.", "smw-admin-supplementary-elastic-replication-files-docu": "Билгалдаккха деза, файлийн могӀам схьаэца хьалха [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion файлаш тӀеоьцуш] гӀуллакх кхочушдан деза, цуьнан болх чекхбаккха беза.", "smw-admin-supplementary-elastic-replication-files": "<PERSON>а<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-pages": "АгӀонаш", "smw-admin-supplementary-elastic-endpoints": "Чаккхенан меттигаш", "smw-admin-supplementary-elastic-config": "Конфиг<PERSON><PERSON><PERSON><PERSON><PERSON>ш", "smw-admin-supplementary-elastic-no-connection": "Карарчу хенахь вики «таро йац» Elasticsearch кластерца зӀе латто. Дехар до, вики-администраторе йазде, проблема талла, хӀунда аьлча цо новкъарло йо системина индексаци йан йа лехамаш бан.", "smw-list-count": "МогӀаман чохь ду $1 {{PLURAL:$1|дӀайаздар}}.", "smw-property-label-uniqueness": "\"$1\" билгало йина лаххара а цхьана кхечу билгалонан хьажарца. Дехар до, [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness гӀоьнан агӀо] хьажа, хӀара проблема муха дӀанисйан мегар ду хаа.", "smw-property-label-similarity-title": "Бил<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н билгало цхьатера хиларан хаам", "smw-property-label-similarity-intro": "<u>$1</u> хьесап до лелаш йолчу билгалонийн цӀерийн цхьатера хилар", "smw-property-label-similarity-threshold": "Чаккхе:", "smw-property-label-similarity-type": "Гойта тайпа ID", "smw-property-label-similarity-noresult": "Х<PERSON><PERSON><PERSON><PERSON><PERSON>нчу опцешна цхьа а жамӀ ца карийна.", "smw-property-label-similarity-docu": "ХӀокху агӀоно дусту [https://www.semantic-mediawiki.org/wiki/Property_similarity синтаксин цхьатера хилар] (амма маьӀнин йа лексикин цхьатера хилар дац) шина билгалонан цӀерашна йукъахь, хаам бо билгалонан цӀерашна, нагахь санна цхьа билггала барам тӀехбаьллехь. Отчето гӀо дан мега нийсайаздар нийса доцу йа эквивалент йолу, цхьа кхетам гойтуш йолу билгалонашна фильтраци йан (хьажа [[Special:Properties|Билгалонийн]] белхан агӀоне, рапортехь билгалонаш кхочушдаран кхетам а, лелор а къасто). Порог нисйан йиш йу тидаме эца дезаш долу цхьатера хиларан тӀегӀа шуьйра йа готта хилийта. <code>[[Property:$1|$1]]</code> лелайо анализах билгалонаш дӀадаха.", "smw-admin-operational-statistics": "ХӀокху агӀонехь йу Semantic MediaWiki-ца уьйр йолчу функцишкахь йа цуьнах гулйина операцин статистика. Вики-къаьсттина статистикин шорбина могӀам хьажа [[Special:Statistics|<b>кхузахь</b>]].", "smw_adminlinks_datastructure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> структура", "smw_adminlinks_displayingdata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> гайтар", "smw_adminlinks_inlinequerieshelp": "МогӀанехь долчу хаттарша гӀо до", "smw-page-indicator-usage-count": "Гергар<PERSON>у хьесапехь [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count лелоран дукхалла]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|декъашхочо|системо}} билгалйаьккхина башхалла", "smw-property-indicator-last-count-update": "Гергарчу хьесапехь лелоран дукхалла\nТӀаьххьара карлайаьккхина: $1", "smw-concept-indicator-cache-update": "Кэш лорург\nКарлайаьккхина: $1", "smw-createproperty-isproperty": "ХӀара $1 тайпанан билгало йу.", "smw-createproperty-allowedvals": "ХӀокху билгалонан {{PLURAL:$1|1=хӀара маьӀна мегаш ду|хӀара маьӀнаш мегаш ду}}:", "smw-paramdesc-category-delim": "ДӀасакъастораг", "smw-paramdesc-category-template": "Элементаш форматашка йерзош гӀо деш йолу кеп", "smw-paramdesc-category-userparam": "Кепе дӀакхачо параметр", "smw-info-par-message": "<PERSON><PERSON><PERSON><PERSON> гайтархьама.", "smw-info-par-icon": "Гойтуш йолу веназ («информаци» йа «дӀахьедар»).", "prefs-smw": "Семантикин МедиаВики", "prefs-general-options": "Йукъара параметраш", "prefs-extended-search-options": "Кхиаме лехамаш", "prefs-ask-options": "Сема<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> лахар", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] (а, цуьнца доьзна долу шордарш а) таро ло функцийн тобанаш хийца. ХӀора меттигийн могӀам церан дийцаршца а, билгалонашца а леладо [https://www.semantic-mediawiki.org/wiki/Help:User_preferences рогӀерчу хьажорган] тӀехь.", "smw-prefs-ask-options-tooltip-display": "Параметран текст гайта инфо гӀирсан хьехам санна #хаттар [[Special:Ask|хаттар кхуллуш]] леррина агӀонан тӀехь.", "smw-prefs-ask-options-compact-view-basic": "Цхьалхе Ӏаьвдина формат дӀахӀоттайе", "smw-prefs-help-ask-options-compact-view-basic": "Нагахь санна дӀахӀоттийна йелахь, гойту Special:Ask хьажоргийн йоца тоба Ӏаьвдина форматехь", "smw-prefs-general-options-time-correction": "Белхан агӀонашна хан нисйар дӀахӀоттаде локалерчу параметрца [[Special:Preferences#mw-prefsection-rendering|хенан меттахӀоттам]]", "smw-prefs-general-options-jobqueue-watchlist": "Гайта белхан рогӀехь терго латторан могӀам долара панелехь", "smw-prefs-help-general-options-jobqueue-watchlist": "ДӀахӀоттайе, [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist могӀам] лаьтташ долу хаьржина гӀуллакхаш, гергарчу барамехь церан рогӀийн барамашца цхьаьна гайтархьама.", "smw-prefs-general-options-disable-editpage-info": "Хийцаман агӀонан тӀехь йуьхьанцара текст дӀайаккха", "smw-prefs-general-options-disable-search-info": "Станд<PERSON><PERSON><PERSON><PERSON>н лехаман агӀонан тӀехь синтаксин гӀоьналлин хаамаш дӀабаха", "smw-prefs-general-options-suggester-textinput": "МаьӀнин объектийн предложенишна чудаларан ассистент дӀахӀоттайе", "smw-prefs-help-general-options-suggester-textinput": "Ахьа йаздечу хенахь [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance автотӀейузар] билга<PERSON><PERSON><PERSON><PERSON>, концепцин, категорин цӀераш схьаэца йиш ло", "smw-prefs-general-options-show-entity-issue-panel": "Гойту субъектан хаттаран панель", "smw-prefs-help-general-options-show-entity-issue-panel": "Нагахь санна дӀахӀоттийна йелахь, хӀора агӀонан тӀехь нийсо толлуш дӀахьо, гойту [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel Духийн проблемийн панель].", "smw-prefs-factedsearch-profile": "[[Special:FacetedSearch|Фасетан лехаран]] хьалха санна профиль харжа:", "smw-ui-tooltip-title-property": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-quantity": "<PERSON>а<PERSON><PERSON><PERSON><PERSON> хийцар", "smw-ui-tooltip-title-info": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-service": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> хьажорг", "smw-ui-tooltip-title-warning": "ДӀахьедар", "smw-ui-tooltip-title-error": "ГӀалат", "smw-ui-tooltip-title-parameter": "Параметр", "smw-ui-tooltip-title-event": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-note": "Билг<PERSON><PERSON><PERSON><PERSON>ккхар", "smw-ui-tooltip-title-legend": "Легенда", "smw-ui-tooltip-title-reference": "Билг<PERSON><PERSON><PERSON><PERSON>ккхар", "smw_unknowntype": "ХӀокху билгалонан хӀоттийна ду ца девза тайпа \"$1\".", "smw-concept-cache-text": "Концепцин барам бу $1 {{PLURAL:$1|агӀо}} тӀаьххьара карладаьккхина $2 чуьра $3.", "smw_concept_header": "«$1» хьажар лелош йолу агӀонаш", "smw_conceptarticlecount": "Лахахь {{PLURAL:$1|гайтина}} $1 {{PLURAL:$1|агӀо}}.", "smw-qp-empty-data": "Дехар дина хаамаш гайта йиш йац, хӀунда аьлча цхьайолу талламан критереш кхачам боллуш йац.", "right-smw-admin": "Администрацин тӀекхача аьтто (Semantic MediaWiki)", "right-smw-patternedit": "Сервисо тӀеэцна рогӀера аларш а, кепаш а хийца аьтто (Semantic MediaWiki)", "right-smw-pageedit": "АгӀонаш хийцар аннотацица <code>Is edit protected</code> (Semantic MediaWiki)", "right-smw-schemaedit": "Хийца [https://www.semantic-mediawiki.org/wiki/Help:Schema схемин агӀонаш] (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "Белхан рогӀехь [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist тергаман могӀаман] функцина тӀекхачар (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Духашца йоьзна йолу ревизи нийса ца хиларх лаьцна информацин тӀекхачар (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "[https://www.semantic-mediawiki.org/wiki/Help:Edit_help Хийцамаш баран гӀоьне] хьажа (Semantic MediaWiki)", "restriction-level-smw-pageedit": "ла<PERSON><PERSON><PERSON>на (богӀуш болу бен)", "action-smw-patternedit": "хийца<PERSON>аш бар рогӀера аларш лелош Semantic MediaWiki", "action-smw-pageedit": "аннотацица агӀонаш хийцар <code>Is edit protected</code> (''Semantic MediaWiki'')", "group-smwadministrator": "Администр<PERSON><PERSON><PERSON><PERSON>ш (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|администратор (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:Админи<PERSON>тр<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator": "(Semantic MediaWiki) кураторш", "group-smwcurator-member": "{{GENDER:$1|(Semantic MediaWiki) куратор}}", "grouppage-smwcurator": "{{ns:project}}:(Semantic MediaWiki) кураторш", "group-smweditor": "(Semantic MediaWiki) редакторш", "group-smweditor-member": "{{GENDER:$1|(Semantic MediaWiki) редактор}}", "grouppage-smweditor": "{{ns:project}}:(Semantic MediaWiki) редакторш", "action-smw-admin": "Semantic MediaWiki административан функцишна тӀекхача аьтто", "action-smw-ruleedit": "бакъонийн агӀонаш хийца (Semantic MediaWiki)", "smw-property-namespace-disabled": "[https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks ЦӀерийн меттиган] башхалла дӀайаьккхина йу, тайпа йа кхин тайпа билгалонаш дӀакхайкхо гӀертар оцу башхаллашна йиш йац.", "smw-property-predefined-default": "«$1» — хьал<PERSON>хе билгалдаьккхина тайпанан башхалла $2.", "smw-property-predefined-common": "ХӀара хьалххе дӀасадаржийна башхалла йу (иштта йевзаш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties леррина башхалла]) ткъа шеца кхин а административан бакъонаш йу, амма лело мегар ду муьлхха а кхин [https://www.semantic-mediawiki.org/wiki/Property декъашхочо билгалйаьккхина башхалла].", "smw-property-predefined-ask": "«$1» — хьалххе билгалйаьккхина башхалла йу, гойту метахаамаш ([https://www.semantic-mediawiki.org/wiki/Subobject бухара объектан] формехь) индивидуалан дехарех лаьцна, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "«$1» — хьалххе билгалйаьккхина башхалла йу, цо гулдо дехаршкахь лелочу билламийн дукхалла, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "«$1» — хьалххе билгалйаьккхина башхалла йу, дехаран кӀоргенах лаьцна хаам беш, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "Терахьийн маьӀна, схьадаьлла бухара дехарийн, зӀаьнарийн башхалла, лело йиш йолчу дийцарийн элементийн бен бан, лелайо дехарш кхочушдаран дозанаш тоха <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code> гӀоьнца", "smw-property-predefined-askpa": "$1 — хьалххе билгалйаьккхина башхалла йу, дехаран жамӀана Ӏаткъам беш йолу параметраш йовзуьйтуш. ДӀаделла [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "ХӀара башхаллийн гуламийн дакъа ду, билгалдоккху [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler query profile].", "smw-sp-properties-docu": "ХӀокху агӀонан тӀехь гойтуш йу лело йиш йолу [https://www.semantic-mediawiki.org/wiki/Property башхоллаш] а, церан леларан терахь а хӀокху вики чохь. Керла статистика хилийта, дика ду [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics статистикин башхаллаш] белхан скрипт хаддаза лело. Дифференциаци йинчу хьажарна хьажа белхан агӀонашна тӀе [[Special:UnusedProperties|лелош йоцу]] йа [[Special:WantedProperties|оьшу башхаллаш]].", "smw-sp-properties-cache-info": "ХӀара хаамаш схьаэцна [https://www.semantic-mediawiki.org/wiki/Caching кэша чуьра] уьш тӀаьххьара карлабаьхна $1.", "smw-sp-properties-header-label": "ХӀумнийн могӀам", "smw-admin-settings-docu": "Гойту массо а стандартан а, локализацин а нисдаран гӀирсийн могӀам, цера уьйр йу Semantic MediaWiki гонах. ХӀора параметрех лаьцна хаамаш хьажа [https://www.semantic-mediawiki.org/wiki/Help:Configuration конфигурацин] гӀоьнан агӀоне.", "smw-sp-admin-settings-button": "Нисдаран гӀирсийн могӀам кхолла", "smw-admin-idlookup-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-idlookup-docu": "ХӀокху декъехь технически хаамаш бу цхьана объектах лаьцна (вики-агӀо, бухара объект, хьал, и. дӀ. кх.) Semantic MediaWiki чохь. Чулацам хила тарло терахьийн ID йа могӀанан маьӀна, догӀуш долу лехаман меттигца догӀуш, муьлхха а ID хьажор хила тарло Semantic MediaWiki, MediaWiki агӀонна йа версин лоьмарна ца хилча.", "smw-admin-iddispose-title": "ДӀайаккхар", "smw-admin-iddispose-docu": "Тидам бе, утилизацин операци доза доцуш йу, тӀечӀагӀдича дӀайоккхур йу чоьхьара объектан ID лармин бухера. Дехар до, хӀара гӀуллакх '''ларлуш''' кхочушде, [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal документаци] хьаьжначул тӀаьхьа.", "smw-admin-iddispose-done": "«$1» идентификатор дӀайаьккхина лармин чуьра.", "smw-admin-iddispose-references": "ID «$1» {{PLURAL:$2|жигара хьажоргаш йац|мел лаххара а цхьа жигара хьажорг йолуш}}.", "smw-admin-iddispose-references-multiple": "Цхьа жигара хьажорг йолуш нисдаларийн могӀам.", "smw-admin-iddispose-no-references": "Лехам нисбала йиш йацара «$1» таблицин элементца.", "smw-admin-idlookup-input": "Лахар:", "smw-admin-objectid": "Идентификатор:", "smw-admin-tab-general": "<PERSON>у<PERSON>е", "smw-admin-tab-notices": "Дехкарх лаьцна билгалдах<PERSON><PERSON>ш", "smw-admin-tab-maintenance": "Техни<PERSON><PERSON><PERSON> хьашташ", "smw-admin-tab-supplement": "<PERSON><PERSON>ин а функцеш", "smw-admin-tab-registry": "Реестр", "smw-admin-tab-alerts": "ДӀахаийтарш", "smw-admin-alerts-tab-deprecationnotices": "Дехкарх лаьцна билгалдах<PERSON><PERSON>ш", "smw-admin-alerts-tab-maintenancealerts": "Техник<PERSON><PERSON> хьа<PERSON><PERSON><PERSON><PERSON> кхочушдаран хаамаш", "smw-admin-alerts-section-intro": "ХӀокху декъехь гойту дӀахаийтарш а, хаа<PERSON><PERSON><PERSON> а, хьакхалуш долу нисдаран гӀирсашца, операцешца, кхин а ардамашца, уьш дӀасакъаьстина ду администраторан йа декъашхочун тидам тӀебахийта оьшу йолу бакъонаш йолучу.", "smw-admin-maintenancealerts-section-intro": "Дага<PERSON>ь латтош долу лардарш а, хаа<PERSON><PERSON>ш а дӀабахьа беза, тӀедиллина дацахь а, цо гӀо дийр ду аьлла хета система а, сервисийн ницкъ а тобарехь.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Таблицин оптимизаци", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Системо къастийна, тӀаьххьара [https://www.semantic-mediawiki.org/wiki/Table_optimization таблицин оптимизаци] дӀайаьхьна хилар $2 де хьалха ($1 тӀера дӀайаздар) иза $3 дийнахь латторан порог совйаьлла. Документацехь хьахийна ма-хиллара, оптимизаци дӀайахьаро аьтто бийр бу дехарийн план хӀотторган дехарех лаьцна дика сацамаш бан, цундела тӀедиллина рогӀера таблицин оптимизаци дӀайахьа.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Ширделла духаш", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Системо дагардина $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities ширделла духаш], кхаьчна критикин тӀегӀан тӀе терго йоцуш латторан, $2 маьӀна совдаккхарца. Цунна дика хир ду [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] латторан скрипт лело.", "smw-admin-maintenancealerts-invalidentities-alert-title": "Нийса йоцу духаш", "smw-admin-maintenancealerts-invalidentities-alert": "Системо нисйина $1 [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace ловш йолу цӀерийн меттиган] [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|духаца}}], [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] йа [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>] латторан скрипт лело.", "smw-admin-deprecation-notice-section": "Семантикин МедиаВики", "smw-admin-configutation-tab-settings": "Нисдаран гӀирс", "smw-admin-configutation-tab-namespaces": "ЦӀерийн меттигаш", "smw-admin-configutation-tab-schematypes": "Схемийн тайпанаш", "smw-admin-maintenance-tab-tasks": "Ӏалашонаш", "smw-admin-maintenance-tab-scripts": "Ӏалашдаран скрипташ", "smw-admin-maintenance-no-description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> бац.", "smw-admin-maintenance-script-section-title": "ТӀекхочуш йолу белхан скриптийн могӀам", "smw-admin-maintenance-script-section-intro": "Латто<PERSON><PERSON>н скрипташна оьшу администратор а, командан могӀанна тӀекхача аьтто а, скрипташ кхочушйан аьтто хилийта.", "smw-admin-maintenance-script-description-dumprdf": "Йолуш йолу триплеташ RDF форматехь экспорт йан.", "smw-admin-maintenance-script-description-rebuildconceptcache": "ХӀара скрипт лелайо Semantic MediaWiki-на концепцийн кэшашна урхалла дан, цигахь цуьнан йиш йу хаьржина кэш кхолла а, дӀайаккха а, карлайаккха а.", "smw-admin-maintenance-script-description-rebuilddata": "База чохь берриге а семантикин хаамаш йухакхуллу, семантикин хаамаш хила тарлучу массо а агӀонашна тӀехула цикл йеш.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Elasticsearch индекс йухаметтахӀоттайо (<code>ElasticStore</code> лелош йолчу дӀахӀоттамашна бен), массо а семантикин хаамаш болчу духашкахула цикл йеш.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Elasticsearch чохь доцу духаш лаха (<code>ElasticStore</code> лелош йолчу инсталляцешна бен) нийса карладаккхаран белхан тӀедиллар хӀоттаде.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "ЙухаметтахӀоттайо <code>SQLStore</code> дуьззина текстан лехаман индекс (параметр дӀахӀоттийначу инсталляцешна).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Массо а бахаман объекташна лелоран статистика йухаметтахӀоттайо.", "smw-admin-maintenance-script-description-removeduplicateentities": "ДӀайоккху духийн дублика<PERSON>а<PERSON>, х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> таблицашкахь карийна, жигара хьажорга<PERSON> йоцуш.", "smw-admin-maintenance-script-description-setupstore": "ХӀоттадо ларма а, лехаман серверан дакъа а <code>LocalSettings.php</code> чохь билгалдаьккхина ма-хиллара.", "smw-admin-maintenance-script-description-updateentitycollation": "Карлайоккху <code>smw_sort</code> меттиг <code>SQLStore</code> чохь ([https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation] нисдаран гӀирсаца).", "smw-admin-maintenance-script-description-populatehashfield": "<code>smw_hash</code> меттиг тӀейузу маьӀна ца тоьучу могӀанашна.", "smw-admin-maintenance-script-description-purgeentitycache": "ЦӀандан кэшан дӀайаздарш девзачу духашна а, цаьрца боьзна хаамашна а.", "smw-admin-maintenance-script-description-updatequerydependencies": "Дех<PERSON><PERSON><PERSON> а, дех<PERSON><PERSON>ийн дозуш хилар а карладаккха (хьажа [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Ширделла духаш а, деха<PERSON><PERSON>н хьажоргаш а дӀайаха.", "smw-admin-maintenance-script-description-runimport": "[https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs] чохь авто карийна чулацам тӀебузар а, импорт йар а.", "smw-admin-maintenance-script-section-update": "Скрипт<PERSON><PERSON> карлайаха", "smw-admin-maintenance-script-section-rebuild": "Скрипташ йухаметтахӀиттор", "smw-livepreview-loading": "Чуйолуш…", "smw-sp-searchbyproperty-description": "ХӀокху агӀоно ло атта [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces лехаман интерфейс], дийцинчу башхаллонашца а, цӀерашца а маьӀнашца духаш лаха. Кхин лело йиш йолу лехаман интерфейсаш йу [[Special:PageProperty|агӀонан башхалла лехар]] а, [[Special:Ask|лехаман дехарш кхуллуш]] а.", "smw-sp-searchbyproperty-resultlist-header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-sp-searchbyproperty-nonvaluequery": "ХӀоттина «$1» башхалла йолу маьӀнийн могӀам.", "smw-sp-searchbyproperty-valuequery": "«$1» башхалла йолу агӀонийн могӀам, дӀакхайкхийна «$2» маьӀна долуш.", "smw-datavalue-number-textnotallowed": "«$1» дӀадала йиш йац дӀакхайкхийначу тайпанан лоьмарна, маьӀна $2 долуш.", "smw-datavalue-number-nullnotallowed": "«$1» йуха<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> «NULL» маьӀнаца, иза терахьашна магош дац.", "smw-editpage-annotation-enabled": "ХӀокху агӀонехь ду текстан чуьра семантикин аннотацеш (масала, <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) дӀахӀоттийна, дехарш дан йиш йолу структурйина чулацам бан, Semantic MediaWiki-с луш йолу. Аннотацех йа ask парсеран функци лелор кхачам боллуш дийцар лаха, хьажа гӀоьналлин агӀонаш тӀе [https://www.semantic-mediawiki.org/wiki/Help:Getting_started белхан йуьхье], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation in-text annotation текстехь аннотацеш], [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries могӀанийн дехарш].", "smw-editpage-annotation-disabled": "ХӀокху агӀоно бакъо ца ло текстехь маьӀнин аннота<PERSON><PERSON>ш йан, цӀерийн меттиган дозанаш бахьана долуш. ЦӀерийн меттигаш муха дӀайаха йеза бохучух лаьцна дерг [https://www.semantic-mediawiki.org/wiki/Help:Configuration конфигурацин гӀоьнан агӀонан] тӀехь ду.", "smw-editpage-property-annotation-enabled": "ХӀара башхалла шорйан йиш йу семантикин аннотацешца, хаамийн тайпа билгалдаккхархьама (масала, <nowiki>\"[[Has type::Page]]\"</nowiki>) йа кхин гӀоьналлин чӀагӀдарш (масала, <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). ХӀара агӀо муха шорйан йеза хьажа [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration башхаллин дӀахьедар] гӀоьнан агӀо йа [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes тӀекхочу хаамийн тайпанийн могӀам].", "smw-editpage-property-annotation-disabled": "ХӀара башхалла шорйан йиш йац хаамийн тайпанан аннотацица (масала, <nowiki>\"[[Has type::Page]]\"</nowiki>), хӀунда аьлча иза хьалххе билгалдаьккхина ду (хьажа кхин а информаци [https://www.semantic-mediawiki.org/wiki/Help:Special_properties леррина башхаллаш]) гӀоьнан анӀонехь.", "smw-editpage-concept-annotation-enabled": "ХӀара концепт шорйан йиш йу #concept парсеран функцица. #concept муха лело йеза хьоьхучу хааме хьажа [https://www.semantic-mediawiki.org/wiki/Help:Concepts концептех] лаьцна гӀоьнан агӀонгахь.", "smw-search-syntax-support": "Лехаман меттиго лело [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search семантикин дехарийн синтаксис] ''MediaWiki''.", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Чулацаман гӀо] дӀахӀоттийна, массо а хила тарлуш йолу варианташ а, категореш а къасто атта хилийта.", "smw-search-help-intro": "<code><nowiki>[[ ... ]]</nowiki></code> йаздича, процессорна гойтур ду ''Semantic MediaWiki'' лехаман механизм лелор. Тидам бе, <code><nowiki>[[ ... ]]</nowiki></code> цхьаьнатохар нийсачу текстан лехамашца масала <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> лелош йац.", "smw-search-help-structured": "Структурйина лехам:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> — [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context харжаран контекст]\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> — [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context дехаран контекстца]", "smw-search-help-proximity": "Уллехь лехар (башхалла ца йевзаш хиларна, '''бен''' лело йиш йу бэкендашна, цуо ло йуьззина текстан лехаман интеграци):\n\n* <code><nowiki>[[in:lorem ipsum]]</nowiki></code> (массо а документашкахь лаха индекс йина йолу «lorem» а, «ipsum» а)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (нийса догӀу «lorem ipsum» фразаш санна)", "smw-search-help-ask": "ХӀокху хьажоргашкахь информаци йу <code>#ask</code> парсеран функци муха лелайо:\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages АгӀонаш къастор] — агӀонаш муха къасто йеза а, къасторан хьелаш кхолла а\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Лехаман операторш] — ловш йолу лехаман операторш, шайна йукъахь диапазон а, метасимволш а къастор", "smw-search-input": "ДӀайаздар а, лехар а", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance АвтотӀейузаран] дӀайазйаран меттигехь оьшу хӀокху дешхьалхенех цхьаъ:\n\n*<code>p:</code> башхалашна (масала, <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> категорешна\n\n*<code>con:</code> концепцешна", "smw-search-syntax": "Син<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile": "<PERSON><PERSON><PERSON>н а", "smw-search-profile-tooltip": "Semantic MediaWiki йоьзна йолу лехаман функцеш", "smw-search-profile-sort-best": "Дика цхь<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-sort-recent": "Уггаре а тӀаьххьара", "smw-search-profile-sort-title": "ЦӀе", "smw-search-profile-extended-help-intro": "[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile Шорйина агӀонан профиль] ''Белхан:Лехар'' ло лехаман функцешна тӀекхача, леррина йу Semantic MediaWiki а, цу чохь ловш долчу дехаран серверан дакъа а.", "smw-search-profile-extended-help-sort": "Дехаран жамӀийн рогӀалла дӀасакъастар:", "smw-search-profile-extended-help-sort-title": "*«ЦӀе» агӀонан цӀе (йа гайтаран цӀе) лелош дӀасакъасторан критереш санна", "smw-search-profile-extended-help-sort-recent": "* \"Уггаре тӀаьхьарниг\" агӀонаш гайта тӀаьххьара хийцаран рогӀехь (уггаре тӀаьхьарчу хийцамийн рогӀехь дуьйна уггаре а ширачу кхаччалц), амма бухара объекташ гойтур йац, хӀунда аьлча церан дац [[Property:Modification date|Хийцаран терахь]] башхалла билгалйаьккхина.", "smw-search-profile-extended-help-sort-best": "* «Уггаре дика цхьанадар» дӀасакъастор ду духаш [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy нийса хилар] бекэндо деллачу мах хадорашна тӀе а тевжаш", "smw-search-profile-extended-help-form": "Формаш ло (нагахь санна латтош йелахь) билгалчу варианташца догӀуш хилийта, тайп-тайпана башхаллин а, мех<PERSON><PERSON><PERSON><PERSON><PERSON> а меттиг<PERSON><PERSON> гучуйахарца, чуйаларан процесс готта йан а, декъашхошна лехаман дехарца дӀадахьа аьтто хилийта а. (хьажа $1)", "smw-search-profile-extended-help-namespace": "ЦӀерийн меттиг талларан меттиг къайлайоккхур йу форма хаьржиначул тӀаьхьа, амма гуш йан йиш йу «гайта/къайлайаккха» нуьйдин гӀоьнца.", "smw-search-profile-extended-help-search-syntax": "Лехаман меттиго лелайо <code>#ask</code> синтаксисин парсеран функцин семантикин лехамашна. Пайдечу аларшна йукъахь ду:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code>  «...» йукъахь долу шадерг лаха, къаьсттина пайдехьа ду лехаман контекст йа цу йукъайогӀу башхаллаш ца йевзаш хилча (масала, <code>in:(lorem && ipsum)</code> эквивалентан йу <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> нийсса цхьана рогӀаллехь \"...\" чохь долу шадерг лаха", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> муьлхха а дух нисйан \"...\" башхаллинца (масала, <code>has:(Foo && Bar)</code> эквиваленан йу <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> цхьана а духаца нийса ца хилийта, шена йукъахь \"...\" йукъайогӀуш йолуш.", "smw-search-profile-extended-help-search-syntax-prefix": "* Кхин а леррина дешхьалхенаш лело йиш йолуш а, билгалйаьхна а йу, масала: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Цхьадолу аларш лардина ду, масала: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "«Цхьайолу гайтина операцеш пайдехьа йу, нагахь санна уьш цхьаьнатоьхча йуьззина текстан индексца йа ElasticStore дӀахӀоттийна хилча.»", "smw-search-profile-extended-help-query": "<code><nowiki>$1</nowiki></code> лелийна дехар санна.", "smw-search-profile-extended-help-query-link": "Кхин дӀа хаа лаахь леладе $1.", "smw-search-profile-extended-help-find-forms": "ТӀекхочу форманаш", "smw-search-profile-extended-section-sort": "Къастам бе", "smw-search-profile-extended-section-form": "<PERSON>ор<PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-search-syntax": "<PERSON><PERSON><PERSON><PERSON><PERSON> леха", "smw-search-profile-extended-section-namespace": "ЦӀерийн меттиг", "smw-search-profile-extended-section-query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-link-caption-query": "де<PERSON><PERSON><PERSON><PERSON><PERSON> конструктор", "smw-search-show": "Гайта", "smw-search-hide": "Къайлайаккха", "log-name-smw": "Semantic MediaWiki журнал", "log-show-hide-smw": "$1 Semantic MediaWiki журнал", "logeventslist-smw-log": "Semantic MediaWiki журнал", "log-description-smw": "Semantic MediaWiki а, цу<PERSON><PERSON><PERSON> компоненташа а дӀайаздеш долу [https://www.semantic-mediawiki.org/wiki/Help:Logging йан йиш йолу хиламийн тайпанаш] хьокъехь долу гӀуллакх.", "logentry-smw-maintenance": "Сервисца боьзна хиламаш кхоьллина Semantic MediaWiki", "smw-datavalue-import-unknown-namespace": "Импортан цӀерийн меттиг «$1» йевзаш йац. OWL импортан информаци [[MediaWiki:Smw import $1]] агӀонца хиларх тешна хила.", "smw-datavalue-import-missing-namespace-uri": "[[MediaWiki:Smw import $1|$1 импорт]] чохь ца карийна цӀерийн меттиган URI «$1.", "smw-datavalue-import-missing-type": "Цхьа а тайпа билгалдаккхар ца карийна «$1» [[MediaWiki:Smw import $2|$2 импорт]] чохь.", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Импорт $1]]", "smw-datavalue-import-invalid-value": "«$1» нийса формат йац, иза хила йеза «цӀерийн меттиг»:«идентификатор» (масала «foaf:name»).", "smw-datavalue-import-invalid-format": "«$1 могӀа диъ декъе бекъа безаш бара, амма формат кхеташ йацара.", "smw-property-predefined-impo": "«$1» — хьалххе билгалйаьккхина башхалла йу, цо гойту [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary дӀаэцначу лексикица] йолу йукъаметтиг, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "«$1» — хьалххе билгалйаьккхина башхалла йу, цуо гойту [[Special:Types|хаамийн тайпа]], иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "«$1» — хьалххе билгалйаьккхина башхалла йу, гойту [https://www.semantic-mediawiki.org/wiki/Help:Container контейнеран] конструкци, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "ХӀокху лармо аьтто бу Ӏалашдан башхаллийн маьӀнаш, рогӀера вики-агӀонан тӀехь санна, амма леррина меттигехь дӀадихкина иза чулоцучу агӀонца.", "smw-property-predefined-errp": "«$1» — хьалххе билгалйаьккхина башхалла йу аннотацешкахь нийса доцу маьӀна лело, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "Хаа<PERSON>ийн тайпа нийса ца хилар бахьана долуш йа [[Property:Allows value|тӀекхочу маьӀнаш]] дехкар бахьана долуш кхойкху.", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value «$1»] — хьалххе билгалйаьккхина башхалла йу, цуо билгалбаккха йиш йу бакъо йеллачу маьӀнийн могӀам, маьӀна дӀадалар доза тохархьама, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list «$1»] — хьалххе билгалйаьккхина башхалла йу, хьажорг Ӏалашйан бакъо йеллачу башхиллийн маьӀнийн могӀам ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]''.", "smw-datavalue-property-restricted-annotation-use": "«$1» башхалла шатайпа Ӏалашо йолуш йу, цуьнан маьӀна семантикин аннотацехь хӀотто йиш йац.", "smw-datavalue-property-restricted-declarative-use": "«$1» — башхалла декларативан йу, хьашт йа категорин агӀонаш тӀехь бен лело ца йеза.", "smw-datavalue-property-create-restriction": "«$1» башхалла хӀинца а йац, ткъа декъашхочун бакъо йац «$2» (хьажа [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode ТӀекхачаран урхаллин раж]) керла башхалла кхоллархьама.", "smw-datavalue-property-invalid-character": "«$1» чохь магийна доцу хьаьрк «$2» ду, цундела нийса ца хилар билгалдаьккхина ду.", "smw-datavalue-property-invalid-chain": "«$1» башхаллин могӀа лело бакъо йац семантикин аннотацехь.", "smw-datavalue-restricted-use": "«$1» хаамийн маьӀна билгалдаьккхина лело йиш йоцуш.", "smw-datavalue-invalid-number": "«$1» терахь санна тида йиш йац.", "smw-query-condition-circular": "Хила тарлуш долу циклан биллам гучубаьлла «$1» чохь.", "smw-query-condition-empty": "Дехарехь баьсса биллам бу.", "smw-types-list": "Хаа<PERSON>ийн тайпанийн могӀам", "smw-types-default": "«$1» дӀайаздина хаамийн тайпа.", "smw-types-help": "<PERSON><PERSON>ин а информаци а, маса<PERSON><PERSON> а каро йиш [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 гӀоьнан агӀонан тӀехь].", "smw-type-anu": "«$1» — [[Special:Types/URL|URL]] хаа<PERSON>ийн тайпанан вариант йу, иза коьртачу декъана лелайо «owl:AnnotationProperty» экспортан декларацина.", "smw-type-boo": "«$1» — бакъ/харц маьӀна гойтуш долу хьалхалера хаамийн тайпа.", "smw-type-cod": "«$1» — [[Special:Types/Text|Текст]] хаамийн тайпанан вариант йу, лаамехь йохалла йолчу техникин текстехь лело, маса<PERSON><PERSON> йуьхьанцара кодан могӀамашкахь.", "smw-type-geo": "«$1» — географин меттиг гойтуш долу хаамийн тайпа ду, шуьйра болх бан [https://www.semantic-mediawiki.org/wiki/Extension:Maps «Maps» шордар] дӀахӀотто деза.", "smw-type-tel": "«$1» — ле<PERSON><PERSON>ина хаамийн тайпа ду дуьненайукъара телефонан лоьмарш йовзийта RFC 3966 сацамца.", "smw-type-txt": "«$1» — лаа<PERSON><PERSON><PERSON>ь йохалла йолу могӀанийн дийцаран хьалхалера хаамийн тайпа.", "smw-type-dat": "«$1» — хенан моменташ цхьана форматехь гойтуш йолу хаамийн тайпа.", "smw-type-ema": "«$1» — электронан поштан адрес чохь долу ларма.", "smw-type-tem": "«$1» — леррина терахьийн тайпа ду температура ларйан.", "smw-type-qty": "«$1» — леррина терахьийн тайпа ду температура ларйан.", "smw-type-rec": "«$1» — тайпанийн башхаллин могӀаман хаамийн тайпа.", "smw-type-extra-tem": "Гочдаран процедуро ловш йу Кельвинан температурин шкалаш, Цельсин, Фаренгейта, Ранкина.", "smw-type-tab-properties": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-type-tab-types": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-type-tab-type-ids": "Тайпанан идентификаторш", "smw-type-tab-errors": "ГӀалаташ", "smw-type-primitive": "<PERSON><PERSON><PERSON>", "smw-type-contextual": "До<PERSON><PERSON><PERSON>", "smw-type-compound": "Цхьа<PERSON>накхетарш", "smw-type-container": "Кон<PERSON>ейнер", "smw-type-no-group": "Къайлаха дац", "smw-special-pageproperty-description": "ХӀокху агӀоно лехаман интерфейс ло массо башхаллин маьӀнашна а, йеллачу агӀонна а. Кхин а лелаш йолу лехаман интерфейсаш йу [[Special:SearchByProperty|башхаллин лехар]] а, [[Special:Ask|лехаман дехарш кхуллуш йолу]] а.", "smw-property-predefined-errc": "«$1» — хьалххе билгалйаьккхина башхалла йу, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], гойту маьӀнийн аннотацеш йа чудалар кечдаран гӀалат бахьана долуш.", "smw-property-predefined-long-errc": "ГӀалаташ гулдина ду [https://www.semantic-mediawiki.org/wiki/Help:Container контейнерехь], цу чохь хила тарло нийса ца догӀуш хилар бахьана хиллачу башхаллин тӀе хьажоргаш а.", "smw-property-predefined-errt": "«$1» — хьалххе билгалйаьккхина башхалла йу, шена чохь гӀалатан текстан дийцар а долуш, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Декъашхочо билгалйаьккхинчу бухара объектехь нийса йоцу цӀерийн схема йара. Хьалхарчу пхеа хьаьркехь йолу хан ($1) лерина йу шордаршна лело. Хьуна лаахь [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier цӀерийн идентификатор] дӀахӀотто мегар ду.", "smw-datavalue-record-invalid-property-declaration": "ДӀайаздаран чохь йу «$1» башхалла, иза ша дӀакхайкхийна дӀайаздаран тайпа ду, иза магош дац.", "smw-property-predefined-mdat": "«$1» — хьалххе билгалйаьккхина башхалла йу, иза догӀу объектан тӀаьххьара хийцаран ханца, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "«$1» — хьалххе билгалйаьккхина башхалла йу, иза догӀу субъектан хьалхара версин денца, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "«$1» — хьалххе билгалйаьккхина башхалла йу, цо гойту субъект керла йу йа йац, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "«$1» — хьалххе билгалйаьккхина башхалла йу хьалхара верси кхоьллинчу декъашхочун агӀонан цӀе шена чохь йолуш, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "«$1» — хьалххе билгалйаьккхина башхалла йу, цуо гойту чуйьккхинчу файлан MIME тайпа, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "«$1» — хьалххе билгалйаьккхина башхалла йу, цуо гойту чуйаьккхинчу медиа-файлан медиа-тайпа, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "«$1» — хьалххе билгалйаьккхина башхалла йу, цу чохь йу дехарехь лелош йолу чаккхенан форматан цӀе, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "«$1» — хьалххе билгалйаьккхина башхалла йу, лехаман хьелаш могӀанан кепехь гойтуш, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "«$1» — хьалххе билгалйаьккхина башхалла йу, шена чохь хан (секундашца) йолуш, дехар кхочушдан оьшуш йолу, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "«$1» — хьалххе билгалйаьккхина башхалла йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' луш йу, альтер<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (и. дӀ. арахьара) дехарийн хьостанаш Ӏалашдан.", "smw-property-predefined-askco": "«$1» — хьалххе билгалйаьккхина башхалла йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' дехарийн хьал Ӏалашдан йа цуьнан дакъош Ӏалашдан.", "smw-property-predefined-long-askco": "Дех<PERSON><PERSON><PERSON><PERSON> хьолан код гойтуш долу терахь йа терахьаш (хьажа [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler Дехарийн профилер]).", "smw-property-predefined-prec": "«$1» — хьалххе билгалйаьккхина башхалла йу, терахьийн хаамийн тайпанашна [https://www.semantic-mediawiki.org/wiki/Help:Display_precision гайтаран нийсалла] (дешдакъойн терахьашца) гойтуш.", "smw-property-predefined-attch-link": "«$1» — хьалххе билгалйаьккхина башхалла йу, агӀонехь карийна чухӀоттина файлан а, суьртан а хьажоргаш гулйеш йолу, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-inst": "«$1» — чоьхьара хьалххе билгалйаьккхина башхалла йу, цуо латтайо категорин информаци MediaWiki йозуш йоцуш, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-unit": "«$1» — декларати<PERSON><PERSON><PERSON> хьалххе билгалйаьккхина башхалла йу, терахьийн тайпанан башхаллашна гайтаран цхьаьнакхетараллаш билгалйаха, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-unit": "ЦӀоьмалгца къастийначу могӀамо аьтто бо гайтамехь лело йезаш йолу цхьаьнакхетараллаш йа форматаш йовзийта.", "smw-property-predefined-conv": "«$1» — декларатив<PERSON><PERSON> хьалххе билгалйаьккхина башхалла йу, физикин бараман цхьана цхьаьнакхетараллин хийцаран коэффициент билгалйаккхархьама, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-serv": "«$1» — деклара<PERSON><PERSON><PERSON><PERSON><PERSON> хьалххе билгалйаьккхина башхалла йу, башхаллашна белхан хьажоргаш тӀетоха, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-redi": "«$1» — чоьхьа<PERSON>а хьалххе билгалйаьккхина башхалла йу, хийцамаш дӀайазбан, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subp": "«$1» — декларативан хьалххе билгалйаьккхина башхалла йу, билгалйаьккхина кхечу [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of бухара башхаллин] хилар билгалдаккхархьама, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subc": "«$1» — декларативан хьалххе билгалйаьккхина башхалла йу, билгалйаьккхина кхечу [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of бухара категорин] хилар билгалдаккхархьама, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-conc": "«$1» — чоьхьара хьалххе билгалйаьккхина башхалла йу, цунах боьзна концепци билгалйаккхархьама, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-err-type": "«$1» — хьалххе билгалйаьккхина башхалла йу, [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors гӀалаташ нисдаран] тобанан йа классан идентификаци йан, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-skey": "«$1» — чоьхьа<PERSON>а хьалххе билгалйаьккхина башхалла йу, дӀасакъасторан хьажорг латто, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pplb": "«$1» — декларативан хьалххе билгалйаьккхина башхалла йу, [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label гӀоле хетачу башхаллин билгало] билгалйаккхархьама, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-chgpro": "«$1» — хьалххе билгалйаьккхина башхалла йу [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation хийцамаш баржоран] информаци латто, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-link": " ткъа дӀалуш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-format-schema": " ткъа дӀалуш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-profile-schema": " ткъа дӀалуш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans": " ткъа дӀалуш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-source": " ткъа дӀалуш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-group": " ткъа дӀалуш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cont-len": "«$1» — хьалххе билгалйаьккхина башхалла йу йохаллин информаци Ӏалашйан, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-len": "Иза лелайо [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] ([https://www.semantic-mediawiki.org/Attachment_processor тӀетохаран процессорца]) цхьаьна, чуйаьккхинчу файлера схьаэцна йохаллин информаци гулйан а, Ӏалашйан а (нагахь санна йелахь).", "smw-property-predefined-cont-lang": "«$1» — хьалххе билгалйаьккхина башхалла йу меттан информаци Ӏалашйан, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-lang": "Иза лелайо [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ткъа иштта [https://www.semantic-mediawiki.org/Attachment_processor тӀетохаран процессорца]) цхьаьна, схьаэцначу файлера схьаэцна меттан информаци гулйан а, Ӏалашйан а (нагахь санна иза йелахь).", "smw-property-predefined-cont-title": "«$1» — хьалххе билгалйаьккхина башхалла йу цӀеран информаци Ӏалашйан, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-title": "Иза лелайо [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] ([https://www.semantic-mediawiki.org/Attachment_processor тӀетохаран процессорца]) цхьаьна, чуйаьккхинчу файлера схьаэцна корта болу информаци гулйан а, Ӏалашйан а (нагахь санна йелахь).", "smw-property-predefined-cont-author": "«$1» — авторах лаьцна информаци Ӏалашйан хьалххе билгалйаьккхина башхалла йу, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-author": "Иза лелайо [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ткъа иштта [https://www.semantic-mediawiki.org/Attachment_processor тӀетохаран процессорца]) цхьаьна, чуйаьккхинчу файлера схьаэцна авторан информаци гулйан а, Ӏалашйан а (нагахь санна иза йелахь).", "smw-property-predefined-cont-date": "«$1» — хьалххе билгалйаьккхина башхалла йу, терахьан информации Ӏалашйан, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-date": "Иза лелайо [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (ткъа иштта [https://www.semantic-mediawiki.org/Attachment_processor тӀетохаран процессорца]) цхьаьна, чуйаьккхина файлера схьаэцна денна лерина информаци гулйан а, Ӏалашйан а (нагахь санна йеллехь).", "smw-property-predefined-cont-type": "«$1» — хьалххе билгалйаьккхина башхалла йу файлан тайпанан информаци Ӏалашйан, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-type": "Иза лелайо [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] ([https://www.semantic-mediawiki.org/Attachment_processor тӀетохаран процессорца]) цхьаьна, чуйаьккхина файлера схьаэцна тайпанан информаци гулйан а, Ӏалашйан а (нагахь санна йеллехь).", "smw-property-predefined-cont-keyw": "«$1» — коьрта дешнаш гойтуш хьалххе билгалйаьккхина башхалла йу, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-keyw": "Иза лелайо [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] ([https://www.semantic-mediawiki.org/Attachment_processor тӀетохаран процессорца]) цхьаьна, чуйаьккхинчу файлера схьадаьхна коьрта дешнаш гулдан а, Ӏалашдан а (нагахь санна билгалдаьккхинехь).", "smw-property-predefined-file-attch": "«$1» — хьалххе билгалйаьккхина башхалла йу, гойтуш йу тӀетохаран информаци Ӏалашйеш йолу контейнер, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-file-attch": "Иза лелайо [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] ([https://www.semantic-mediawiki.org/Attachment_processor тӀетохаран процессорца]) цхьаьна, массо а чулацаман леррина информаци гулйан, дӀайеллачу файлера схьайаккха йиш йолуш (нагахь санна дӀайелла йелахь).", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps «Maps» шордар] ца карийна, цундела «$1» башхалла шен болх барехь дозаделла ду.", "smw-datavalue-monolingual-dataitem-missing": "Цхьанаметтан маьӀна кхолларан хьоьжуш йолу элемент йац.", "smw-datavalue-languagecode-missing": "«$1» аннотацина парсеран аьтто ца баьлла меттан код къасто (масала \"foo@en\").", "smw-datavalue-languagecode-invalid": "«$1» билгал ца йаьккхина болуш болу меттан код санна.", "smw-property-predefined-lcode": "«$1» — хьалххе билгалйаьккхина башхалла йу BCP47 форматан меттан код гойтуш, иза луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "«$1» — [https://www.semantic-mediawiki.org/wiki/Help:Container контейнеран] хаамийн тайпа ду, текстан маьӀна билгалчу [[Property:Language code|меттан кодца]] уьйр йолуш.", "smw-types-extra-mlt-lcode": "ХӀокху хаамийн тайпанан {{PLURAL:$2|оьшу|ца оьшу}} меттан код (и. д. {{PLURAL:$2|меттан код йоцу маьӀнан аннотаци тӀе ца оьцу|меттан код йоцу маьӀнан аннотаци тӀеоьцу}}).", "smw-property-predefined-text": "«$1» — хьалххе билгалйаьккхина башхалла йу, леррина йохалла йолу текст гойтуш, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "«$1» — хьалххе билгалйаьккхина башхалла йу, цуо бакъо ло башхалла меттан контекстехь йийца, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "«$1» — хьалххе билгалйаьккхина башхалла йу, [[Special:Types/Record|record]] тайпанан башхаллица лелош болу башхаллийн могӀам билгалбаккхархьама, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Текстан чуьра аннотацин талларан хан", "smw-limitreport-intext-postproctime": "[SMW] пост-кечйаран хан", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|секунд}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|секунд}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] store-база карлайаккха<PERSON><PERSON><PERSON> хан (агӀо карлайаьккхича)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|секунд}}", "smw_allows_pattern": "АгӀо схьайи<PERSON><PERSON>ина хилийта «[[Property:Allows pattern|Allows pattern]]» башхаллашца, агӀонан тӀехь хила деза хьажоргийн могӀам (цул тӀаьхьа [https://en.wikipedia.org/wiki/Regular_expression рогӀера аларш]). ХӀара агӀо хийца <code>smw-patternedit</code> бакъонаш оьшу.", "smw-datavalue-allows-pattern-mismatch": "«$2» рогӀера аларо «$1» маьӀна нийса доцуш санна классификаци йина.", "smw-datavalue-allows-pattern-reference-unknown": "«$1» кепан хьажоргаш йуста йиш йацара [[MediaWiki:Smw allows pattern]] чохь долчу дӀайаздарца.", "smw-datavalue-allows-value-list-unknown": "«$1» могӀа ца карийна [[MediaWiki:Smw allows list $1]] могӀамехь.", "smw-datavalue-allows-value-list-missing-marker": "«$1» могӀамехь цхьа а могӀанаш дац могӀаман маркерца <code>*</code>.", "smw-datavalue-feature-not-supported": "ХӀокху вики тӀехь «$1» функци лелош йац йа дӀайайина йу.", "smw-property-predefined-pvap": "«$1» — хьалххе билгалйаьккхина башхалла йу, иза хила йиш йу [[MediaWiki:Smw allows pattern|кепан тӀе хьажорг]] билгалйаьккхина [https://en.wikipedia.org/wiki/Regular_expression рогӀера аларш] догӀуш хилар кхочушдар, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "«$1» — хьалххе билгалйаьккхина башхалла йу, шена тӀедилла йиш йолуш билгалйолу гайтаман цӀе духанна, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "«$1» — хьалххе билгалйаьккхина башхалла йу, [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] луш долу, хӀора инстанцина маьӀна дӀадалар шатайпа маьӀна (йа максимум цхьаъ) дӀадаккхархьама.", "smw-property-predefined-long-pvuc": "Шатайпаналла кхоллало, нагахь санна ши маьӀна цхьатерра ца хилча шайн символан гайтамехь, ткъа муьлхха а и дехкар дохор гӀалат санна лорур ду.", "smw-datavalue-constraint-uniqueness-violation": "«$1» башхалло шатайпа маьӀна бен дӀа цало, ткъа «$2» хӀинцале а аннотаци йина йу «$3» темина.", "smw-datavalue-constraint-uniqueness-violation-isknown": "«$1» башхалла бен ца магайо шатайпа тӀедилларш дан. «$2» агӀонан тӀехь хӀинцале а билгалдина маьӀна ду. «$3» — шатайпа хиларан дехар дохош ду.", "smw-datavalue-constraint-violation-non-negative-integer": "«$1» башхаллин «дацаре доцу дерриге терахь» дехкар ду, ткъа «$2» маьӀно и тӀедиллар доха до.", "smw-datavalue-constraint-violation-must-exists": "«$1» башха<PERSON><PERSON>ин <code>must_exists</code> дехкар ду, ткъа «$2» маьӀна и тӀедиллар дохош ду.", "smw-datavalue-constraint-violation-single-value": "«[[Property:$1|$1]]» башхаллин <code>single_value</code> дехкар ду, ткъа «$2» маьӀна и тӀедиллар дохош ду.", "smw-constraint-violation-uniqueness": "<code>unique_value_constraint</code> дӀахӀоттийна «[[Property:$1|$1]]» башхаллина, цо бен ца ло шатайпа маьӀна дӀадала, ткъа маьӀнийн аннотаци «$2» хӀинцале а карийна «$3» субъектехь аннотаци йина.", "smw-constraint-violation-uniqueness-isknown": "«[[Property:$1|$1]]» башхаллина <code>unique_value_constraint</code> дӀахӀоттийна ду, цундела шатайпа маьӀнийн аннотацеш бен ца магайо. ''$2'' хӀинцале а аннотаци йина маьӀна ду «$3», цуо дохадо карарчу хенахь йолчу субъектан шатайпа дехкарш.", "smw-constraint-violation-non-negative-integer": "<code>non_negative_integer</code> дехкар делла «[[Property:$1|$1]]» башха<PERSON><PERSON>ин, ткъа аннотацин маьӀно дохош ду «$2» тӀедиллар дохош ду.", "smw-constraint-violation-must-exists": "<code>must_exists</code> дехкар делла «[[Property:$1|$1]]» башха<PERSON><PERSON>ин, ткъа аннотацин маьӀно дохош ду «$2» тӀедиллар дохош ду.", "smw-constraint-violation-single-value": "<code>single_value</code> дехкар делла «[[Property:$1|$1]]» башхал<PERSON>ин, ткъа аннотацин маьӀно дохош ду «$2» тӀедиллар дохош ду.", "smw-constraint-violation-class-shape-constraint-missing-property": "<code>shape_constraint</code> дӀахӀоттийна «[[:$1]]» категорина <code>property</code> догӀанца, оьшу «$2» башхалла ца тоьу.", "smw-constraint-violation-class-shape-constraint-wrong-type": "<code>shape_constraint</code> дӀало «[[:$1]]» категорина <code>property_type</code> догӀанца, «$2» башхалла нийса ца йогӀу «$3» тайпанца.", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "<code>shape_constraint</code> дӀало «[[:$1]]» категорина <code>max_cardinality</code> догӀанца, «$2» башхалла нийса ца йогӀу «$3» кардиналаллаца.", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "<code>shape_constraint</code> дӀало «[[:$1]]» категорина <code>min_textlength</code> догӀанца, «$2» башхалла нийса ца йогӀу «$3» йохаллин дехаршца.", "smw-constraint-violation-class-mandatory-properties-constraint": "<code>mandatory_properties</code> дехкар хӀоттина «[[:$1]]» категорина, цунна оьшу хӀара тӀейихкина башхаллаш: $2", "smw-constraint-violation-allowed-namespace-no-match": "<code>allowed_namespaces</code> дехкар дӀахӀоттийна «[[Property:$1|$1]]» башхал<PERSON>ашна, «$2» дохадо цӀерийн меттиган тӀедиллар, лахахь йийцина «$3» цӀерийн меттигаш бен магош йац.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "<code>allowed_namespaces</code> дехкарна агӀонан тайпа оьшу.", "smw-constraint-schema-category-invalid-type": "Аннота<PERSON><PERSON> йина «$1» схема нийса йац категорина, цунна оьшу «$2» тайпа.", "smw-constraint-schema-property-invalid-type": "Аннота<PERSON><PERSON> йина «$1» схема нийса йац башхаллина, цунна оьшу «$2» тайпа.", "smw-constraint-error-allows-value-list": "«$1» йукъа ца йогӀу могӀанан ($2) [[Property:Allows value|магийна маьӀнаш]] «$3» билгалонан.", "smw-constraint-error-allows-value-range": "«$1» йукъа ца йогӀу дехкарийн  могӀанан ($2) [[Property:Allows value|магийна маьӀнаш]] «$3» билгалонан.", "smw-property-predefined-boo": "«$1» — [[Special:Types/Boolean|хаамийн тайпа]] а, хьалххе билгалйаьккхина башхалла а йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' луш йолу булан маьӀнаш Ӏалашдан.", "smw-property-predefined-num": "«$1» — [[Special:Types/Number|хаамийн тайпа]] а, хьалххе билгалйаьккхина башхалла а йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' терахьийн хаамаш Ӏалашбарна.", "smw-property-predefined-dat": "«$1» — [[Special:Types/Date|хаамийн тайпа]] а, хьалххе билгалйаьккхина башхалла а йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' хенаш Ӏалашйан.", "smw-property-predefined-uri": "«$1» — [[Special:Types/Date|хаамийн тайпа]] а, хьалххе билгалйаьккхина башхалла а йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' URI а, URL а Ӏалашйан.", "smw-property-predefined-qty": "«$1» — [[Special:Types/Date|хаамийн тайпа]] а, хьалххе билгалйаьккхина башхалла а йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' мах хадоран дакъошца Ӏалашдаран.", "smw-datavalue-time-invalid-offset-zone-usage": "«$1» чохь йолу меттахдалар а, сахьтан аса а ца йовза.", "smw-datavalue-time-invalid-values": "«$1» маьӀнан чохь йу йевзаш йоцу информаци «$2».", "smw-datavalue-time-invalid-date-components-common": "«$1» чохь цхьацца тида йиш йоцу хаамаш бу.", "smw-datavalue-time-invalid-date-components-dash": "«$1» чохь йу арахьара дефис йа кхин хьаьрк, терахьехь лело мегар доцуш.", "smw-datavalue-time-invalid-date-components-empty": "«$1» чохь цхьацца йаьсса компоненташ йу.", "smw-datavalue-time-invalid-date-components-three": "«$1» чохь кхаа сов компонент йу, терахь довза оьшуш йолу.", "smw-datavalue-time-invalid-date-components-sequence": "«$1» чохь хьаьркийн рогӀалла йу, лела<PERSON> йолчу терахьийн компонентийн матрицаца тида йиш йоцуш.", "smw-datavalue-time-invalid-ampm": "«$1» чохь бу «$2» бухара могӀа, иза кхойтта сахьтан хенан форматехь латто йиш йац.", "smw-datavalue-time-invalid-jd": "«$1» нийса Юлианан де санна анализ ца йо (талларан жамӀаш — «$2» ду)", "smw-datavalue-time-invalid-prehistoric": "Исторехь хьалха хиллачу «$1» чудаларан маьӀна тида йиш йац. Масала, шераш сов билгалдаьхна хиларо йа календаран модель йухайерзо тарло дагахь доцу жамӀаш историн хьалхалерчу контекстехь.", "smw-datavalue-time-invalid": "«$1» чудаларан маьӀна тида йиш йац нийса терахь йа хенан компонент санна, хӀунда аьлча «$2» арахоьцу.", "smw-datavalue-external-formatter-uri-missing-placeholder": "URI маска чохь «$1» хьаьрк дац.", "smw-datavalue-external-formatter-invalid-uri": "«$1» — нийса йоцу URL-адрес.", "smw-datavalue-external-identifier-formatter-missing": "Башхалла дӀа ца хӀоттина «[[Property:External formatter uri|External formatter URI]]».", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "Арахьара идентификатор «$1» хьоьжу масех меттиг хийца, амма карарчу маьӀнехь «$2 лаххара а цхьа маьӀнан параметр йац тӀедилларшца догӀуш.", "smw-datavalue-keyword-maximum-length": "Коьрта дешан максималан дохалла совйаьлла $1 {{PLURAL:$1|хьаьрк}}.", "smw-property-predefined-eid": "«$1» — [[Special:Types/External identifier|хаамийн тайпа]] а, хьалххе билгалдаьккхина башхалла а йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' арахьара идентификаторш Ӏалашйан.", "smw-property-predefined-peid": "«$1» — хьалххе билгалйаьккхина башхалла йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' арахьара идентификатор Ӏалашйан.", "smw-property-predefined-pefu": "«$1» — хьалххе билгалйаьккхина башхалла йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' хӀотторан хьаьркца арахьара ресурсан адрес Ӏалашдан.", "smw-property-predefined-long-pefu": "URI чохь хила деза хьаьрк, иза хийцалур ду [[Special:Types/External identifier|арахьара идентификаторна]] тӀе нийса ресурсан хьажорг эца.", "smw-type-eid": "«$1» — «[[Special:Types/Text|Текст]]» хаамийн тайпа ду арахьара (URI based) ресурсаш йовзийтаран. Оцу хаамийн тайпанна тӀейилла йеза «[[Property:External formatter uri|External formatter URI]]» аьлла дӀакхайкхош йолу башхаллаш.", "smw-property-predefined-keyw": "«$1» — хьалххе билгалйаьккхина башхалла а, [[Special:Types/Keyword|type]] а луш йолу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], цуо текст нормале йоккху а, хьаьркийн йохаллин доза долуш а.", "smw-type-keyw": "$1» — [[Special:Types/Text|Text]] тайпанан вариант йу, хьаьркийн йохалла доза тоьхна йолуш, чулацаман гайтам нормализаци йина.", "smw-datavalue-stripmarker-parse-error": "Билгалдаьккхина маьӀна «$1» чохь йу [https://en.wikipedia.org/wiki/Help:Strip_markers асанийн маркераш], цундела нийса анализ йан йиш йац.", "smw-datavalue-parse-error": "Делла «$1» маьӀнех кхеташ дацара.", "smw-datavalue-propertylist-invalid-property-key": "Башхаллийн могӀаман «$1» чохь «$2» башхаллин магийна доцу догӀа ду.", "smw-datavalue-type-invalid-typeuri": "«$1» тайпа нийса URI тӀе хийца йиш йац.", "smw-datavalue-wikipage-missing-fragment-context": "Вики-агӀонан чулацаман маьӀна «$1» лело йиш йац контекстан агӀо йоцуш.", "smw-datavalue-wikipage-invalid-title": "«АгӀо» тайпанан «$1» маьӀна нийса доцу хьаьркаш долуш ду йа дуьззина дац, дагахь доцу жамӀаш хила тарло семантикин аннотаци йича йа дехарш дича.", "smw-datavalue-wikipage-property-invalid-title": "«$2» маьӀна долуш «АгӀо» тайпанан «$1» башхаллин нийса доцу хьаьркаш долуш йу йа йуьззина йац, дагахь доцу жамӀаш дан тарло семантикин аннотаци йича йа дехарш дича.", "smw-datavalue-wikipage-empty": "Вики агӀонан чудаларан маьӀна даьсса ду (масала, <code>[[SomeProperty::]], [[]]</code>), цундела иза лело йиш йац цӀе санна а, дехаран билламан дакъа санна а.", "smw-type-ref-rec": "«$1» — [https://www.semantic-mediawiki.org/wiki/Container контейнеран] тайпа, цуо аьтто бо маьӀнаш даларах лаьцна кхин а информаци (масала, провенансан хаамаш) дӀайазйан.", "smw-datavalue-reference-invalid-fields-definition": "«[[Special:Types/Reference|Хьажорг]]» тайпанна оьшу «[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Has fields]» башхаллашца дӀакхайкхийна башхаллийн могӀам.", "smw-parser-invalid-json-format": "JSON-парсеро делла гӀалат «$1».", "smw-property-preferred-label-language-combination-exists": "«$1» лело йиш йац гӀоле йолу билгало санна, хӀунда аьлча «$2» мотт хӀинцале а билгалбина бу «$3» билгалонна.", "smw-clipboard-copy-link": "Хь<PERSON><PERSON><PERSON>рг буфере копийан", "smw-property-userdefined-fixedtable": "«$1» — башхалла дӀахӀоттийна йу [https://www.semantic-mediawiki.org/wiki/Fixed_properties fixed] санна, ткъа цуьнан [https://www.semantic-mediawiki.org/wiki/Type_declaration тайпанан] муьлхха а хийцамаш бан беза йа <code>setupStore.php</code> йа, «База дӀахӀоттор а, карлайаккхар а», ''[[Special:SemanticMediaWiki|SemanticMediaWiki]]'' белхан агӀонан тӀехь.", "smw-data-lookup": "Х<PERSON><PERSON><PERSON><PERSON><PERSON> схьабахар...", "smw-data-lookup-with-wait": "Де<PERSON><PERSON><PERSON> кхочушдеш ду, цхьа хан йала тарло.", "smw-no-data-available": "ТӀекхочуш хаамаш бац.", "smw-property-req-violation-missing-fields": "«$2» тайпанан «$1» башхаллин йац оьшуш йолу деклараци [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>].", "smw-property-req-violation-multiple-fields": "«$1» башхаллин чохь масех (цундела къовсалуш)  [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Меттигийн йу</code>] карлайахар<PERSON>, «$2» тайпанна цхьаъ бен хир йац.", "smw-property-req-violation-missing-formatter-uri": "Оьшуш йолу <code>External formatter URI</code> башхалла «$1» башхаллина хӀоттийна йац.", "smw-property-req-violation-predefined-type": "Хьалха билгалйаьккхина «$1» башхаллин «$2» тайпа ду, иза догӀуш дац башхаллашна лерина маьӀнаца.", "smw-property-req-violation-import-type": "Тайпанан дӀахьедар карийна хьалххе билгалдаьккхинчу тайпанца догӀуш доцуш импорт йинчу дошаман «$1». Тайпа дӀакхайкхо оьшуш дац, хӀунда аьлча информаци импортан къастам тӀера схьаоьцу.", "smw-property-req-violation-change-propagation-locked-error": "«$1» башхалла хийцайелла, цуьнца йоьзна агӀонаш карлайаха йеза [https://www.semantic-mediawiki.org/wiki/Change_propagation хийцамаш баржош]. Башхаллин агӀо хийцамашкара дӀакъевлина йу йуьхьанца карладаккхар чекхдаллалц, карладаккхар йукъах ца далийта йа башхаллин нийса йоцу параметраш ца хилийта. Цунна цхьа хан йала тарло, [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue белхан рогӀан] бараме а, цуьнан лелоран кесталла а хьаьжжина, цул тӀаьхьа агӀо схьайоьллур йу хийцамашна.", "smw-property-req-violation-change-propagation-locked-warning": "«$1» башхалла хийцайелла, [https://www.semantic-mediawiki.org/wiki/Change_propagation хийцамаш баржоран] процессан гӀоьнца, тӀедиллина духийн йуха мах хадо беза. Карладаккхаро цхьа хан йала тарло, хӀунда аьлча иза дозуш ду [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue белхан рогӀан] планхӀотторан барамца а, кесталлаца а, цундела дика ду хийцамаш бар тӀаьхьататта башхаллехь, йукъара йукъахдовларш йа нийса йоцу спецификацеш ца хилийта.", "smw-property-req-violation-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation Хийцамаш баржоре] хьоьжу ($1 гергга [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|декхар}}]). Баа<PERSON>халлин агӀо хийцар тӀаьхьататта дика ду, процесс чекхйаллалц, карладаккхарехь йа нийса йоцу башхаллин параметраш йукъахь ца йалийта.", "smw-property-req-violation-missing-maps-extension": "ХӀокху хаамийн тайпанна оьшуш йолу [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] шордар ца карийна. И бахьана долуш географин хаамаш Ӏалашбан а, лело а аьтто бац.", "smw-property-req-violation-type": "ХӀокху башхаллина билгалдаьхна цхьаьна ца догӀу хаамийн тайпанаш, цуьнан жамӀ хила тарло цунна нийса доцу маьӀна хӀоттор. Цхьа нийса тайпа къасто деза.", "smw-property-req-error-list": "Башха<PERSON><PERSON>ин чохь хӀара гӀалаташ йа дӀахьедарш ду:", "smw-property-req-violation-parent-type": "«$1» башхалла а, тӀейиллина дай-нанойн «$2» башхалла а тайп-тайпана тайпанийн аннотацеш йолуш йу.", "smw-property-req-violation-forced-removal-annotated-type": "[https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance Mandatory parent type inheritance] бакъо дӀахӀоттийна, аннотацин тайпа «$1» башхалла нийса ца догӀу шен дай-нанойн тайпанца «$2» ткъа оцу тӀедахкаршца догӀуш хийцина хилла. АгӀонан чуьра тайпанан къастам нисбан дика ду, гӀалатан хаам а, тӀейиллина бакъо а дӀадаккхархьама оцу башхаллина.", "smw-change-propagation-protection": "ХӀара агӀо дӀакъевлина йу хийцамашна, ларамаза хийцамаш ца хилийта [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation хийцамаш баржош]. Цхьа хан йала тарло, хийцамашна агӀо схьайеллале [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue белхан рагӀанийн] бараме а, кесталлашка а хьаьжжина.", "smw-category-change-propagation-locked-error": "Категори «$1» хийцайелла, цуьнца йоьзна агӀонаш карлайаха йеза [https://www.semantic-mediawiki.org/wiki/Change_propagation хийцамаш баржор]. Цу йукъана категорин агӀо дӀакъовлу хийцамашна, карладаккхар чекхдаллалц, карладаккхар йукъах ца далийта йа нисдаран гӀирсашкахь нийса цахилар ца хилийта. Цхьа хан йала мега, [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue белхан рагӀанийн] бараме хьаьжжина, хийцамаш бан агӀо схьайеллале.", "smw-category-change-propagation-locked-warning": "Категори «$1» хийцайелла, цуьнца йоьзна агӀонаш карлайаха йеза [https://www.semantic-mediawiki.org/wiki/Change_propagation хийцамаш баржош]. Цхьа хан йала тарло, [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue белхан рагӀанийн] бараме хьаьжжина, агӀо хийцамашна схьайеллале. АгӀо хийцар тӀаьхьататта тӀедуьллу, карладаккхар чекхдаллалц, карладаккхар йукъах ца далийта йа нисдаран гӀирсашкахь нийса цахилар ца хилийта.", "smw-category-change-propagation-pending": "Карладаккхар хьоьжуш ду [https://www.semantic-mediawiki.org/wiki/Change_propagation карладаккхаран даржош] жамӀехь ($1 гергга [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|декхар}}]). Категори хийцар тӀаьхьататта дика ду, карладаккхар чекхдаллалц, карладаккхар йукъах ца далийта йа нийса йогӀуш йоцу параметраш ца хилийта.", "smw-category-invalid-value-assignment": "«$1» — нийса семантикин аннотаци йа категорин тӀедиллар дац.", "protect-level-smw-pageedit": "АгӀонаш хийца бакъо йолчу декъашхошна бен бакъо ца ло (Semantic MediaWiki)", "smw-create-protection": "[https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Дехкина тӀакхачаран раж] мел ду, «$1» башхалла кхолла магийна бакъо йолу декъашхошна «$2» (йа [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups тобанан чуьра]).", "smw-create-protection-exists": "[https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Дехкина тӀакхачаран раж] мел ду, «$1» башхалла хийца магийна бакъо йолу декъашхошна «$2» (йа [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups тобанан чуьра]).", "smw-edit-protection": "ХӀара агӀо йу [[Property:Is edit protected|хийцарех ларйина]] хаамашна ца оьшу хийцамаш ца хилийта, хийца йиш йац «$1» бакъо йолчу декъашхоша бен йа хӀокху [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups тобанан] чуьра бен.", "smw-edit-protection-disabled": "Хийцарех ларйаран раж дӀайайина йу, цундела «$1» башхалла лело мегар дац агӀо хийцарх ларйан.", "smw-edit-protection-auto-update": "АгӀонан ларйаран тӀегӀа хийцина «Is edit protected» башхаллин маьӀнан тӀе а тевжаш.", "smw-edit-protection-enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ларйина (Semantic MediaWiki)", "smw-patternedit-protection": "ХӀара агӀо ларйеш йу, хийца йиш йац <code>smw-patternedit</code> бакъо йолчу декъашхоша бен (хьажа ''[https://www.semantic-mediawiki.org/wiki/Help:Permissions бакъонаш]'' ).", "smw-property-predefined-edip": "«$1» — хьалххе билгалйаьккхина башхалла йу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]'' цуо Ӏалашйо хийцаран ларйаран билгало.", "smw-property-predefined-long-edip": "Муьлхха а декъ<PERSON><PERSON><PERSON><PERSON>чун йиш йелахь а агӀонан тӀехь и хьал хӀотто, леррина бакъонаш йолчу декъашхойн бен аьтто хир бац ларйар хийца йа дӀадаккха, иза хӀоттийча.", "smw-query-reference-link-label": "Дехаран тӀе хьажорг", "smw-format-datatable-emptytable": "Таблица чохь хаамаш бац", "smw-format-datatable-info": "Гойту дӀайаздарш _START_ тӀера _END_ кхаччалц _TOTAL_", "smw-format-datatable-infoempty": "ДӀайаздарх 0-0 гойту", "smw-format-datatable-infofiltered": "(_MAX_ дӀайаздаршкара фильтраци йина)", "smw-format-datatable-lengthmenu": "_MENU_ дӀайаздарш гайта", "smw-format-datatable-loadingrecords": "Чуйолуш…", "smw-format-datatable-processing": "Кечдар...", "smw-format-datatable-search": "Лахар:", "smw-format-datatable-zerorecords": "ЦхьаьнабогӀу хаамаш ца карийна", "smw-format-datatable-first": "ЦхьалгӀаниг", "smw-format-datatable-last": "ТӀехьарниг", "smw-format-datatable-next": "РогӀера", "smw-format-datatable-previous": "Хь<PERSON><PERSON><PERSON><PERSON><PERSON>а", "smw-format-datatable-sortascending": ":активаци йе бӀогӀам хьалаболуш рогӀехь дӀасакъасто", "smw-format-datatable-sortdescending": ":активаци йе бӀогӀам лахаболуш рогӀехь дӀасакъасто", "smw-format-datatable-toolbar-export": "Экспорт", "smw-category-invalid-redirect-target": "«$1» категорехь йу нийса йоцу дӀасахьажорг кхечу цӀерийн меттиге.", "smw-parser-function-expensive-execution-limit": "Парсеран функци шен мах болуш кхочушдаран дозане кхаьчна (хьажа конфигурацин параметр [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "Semantic MediaWiki карлайоккху карарчу хенахь йолу агӀо, нагахь санна оьшуш долу дехарна тӀаьхьара болх бан безахь.", "apihelp-smwinfo-summary": "API модуль Semantic MediaWiki статистиках а, кхин метаинформацех а лаьцна информаци схьаэца.", "apihelp-ask-summary": "API модуль дехарш дан Semantic MediaWiki дехарийн мотт лелош.", "apihelp-askargs-summary": "API модуль Semantic MediaWiki дехарш дан, деха<PERSON>ийн мотт делош, х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, зор<PERSON><PERSON><PERSON>шна, параметрашна лерина могӀам санна.", "apihelp-browsebyproperty-summary": "API модуль башхаллех йа башхаллийн могӀамех лаьцна информаци схьаэца.", "apihelp-browsebysubject-summary": "API модуль предметех лаьцна информаци схьаэца.", "apihelp-smwtask-summary": "Semantic MediaWiki-ца доьзна гӀуллакхаш кхочушдаран API модуль (чоьхьара бен, массо а лелош йац).", "apihelp-smwbrowse-summary": "Semantic MediaWiki чохь тайп-тайпанчу объекташна хьажаран гӀуллакхашна гӀо дан API модуль.", "apihelp-ask-parameter-api-version": "Арахецаран формат:\n;2:ТӀехьа нийса формат {} лелош жамӀийн могӀамна.\n;3:Экспериментан формат [] лелош жамӀийн могӀамна.", "apihelp-smwtask-param-task": "ТӀедилларан тайпа билгалдоккху", "apihelp-smwtask-param-params": "JSON кодировка йина параметраш нийса йогӀуш йу хаьржинчу гӀуллакхан тайпанан тӀедахкаршца", "smw-apihelp-smwtask-example-update": "Цхьана билгалчу субъектана лерина карладаккхаран гӀуллакх дӀадахьаран масал:", "smw-api-invalid-parameters": "Нийса йоцу параметраш, \"$1\"", "smw-parser-recursion-level-exceeded": "Рекурсийн тӀегӀан $1 совдаьлла анализ йеш. Кепийн структура хьажа йеза йа, оьшуш хилча, конфигурацин параметр <code>$maxRecursionDepth</code> нисйан йеза.", "smw-property-page-list-count": "ХӀара башхалла лелош йолу $1 {{PLURAL:$1|агӀо|агӀонаш}}.", "smw-property-page-list-search-count": "$1 {{PLURAL:$1|агӀо|агӀонаш}} башхалла маьӀна хӀоттийна «$2» йолу.", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter Лехаман фильтро] аьтто бо [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions дехаран аларш] йукъадало масала <code>~>code!</code> йа . Хаьржина [https://www.semantic-mediawiki.org/wiki/Query_engine дехаран моторо] а гӀо дан мега доккха элп ца лоруш дехаршна йа кхин доца аларшна, масала:\n\n* <code>in:</code> жамӀехь хила йеза термин, масала '<code>in:Foo</code>'\n\n* <code>not:</code> жамӀехь хила ца йеза термин, масала '<code>not:Bar</code>'", "smw-property-reserved-category": "Категори", "smw-category": "Категори", "smw-datavalue-uri-invalid-scheme": "«$1» нийса URI протокол санна билгалйаьккхина йац", "smw-datavalue-uri-invalid-authority-path-component": "«$1» чохь нийса йоцу бакъонан компонент йа некъаш «$2» хилар гучуделира.", "smw-browse-property-group-title": "Бил<PERSON><PERSON><PERSON><PERSON>нийн тоба", "smw-browse-property-group-label": "Ба<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н тобанан билгало", "smw-browse-property-group-description": "Бил<PERSON><PERSON><PERSON><PERSON><PERSON>н тобанах лаьцна", "smw-property-predefined-ppgr": "«$1» — хьалххе билгалйаьккхина башхалла йу, цуо билгалйоккху духаш (дукхах йолу категореш) лелош йолу башхаллаш тобанашна, луш йолу ''[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]''.", "smw-filter": "Фильтр", "smw-section-expand": "Схьадела дакъа", "smw-section-collapse": "ДӀачӀагӀа дакъа", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1] формат", "smw-help": "ГӀо", "smw-cheat-sheet": "Шпаргалка", "smw-personal-jobqueue-watchlist": "Белхан рагӀ ларйаран могӀам", "smw-personal-jobqueue-watchlist-explain": "Терахьаша гойту кхочушдан хьоьжуш йолчу белхан рогӀехь дӀайаздарийн дукхалла.", "smw-property-predefined-label-skey": "ДӀасакъасторан догӀа", "smw-processing": "Кечдар...", "smw-loading": "Чуйолуш…", "smw-fetching": "Схьаоьцуш...", "smw-preparing": "Кечам...", "smw-expand": "Схьайаста", "smw-collapse": "ДӀахьарчо", "smw-copy": "Ко<PERSON><PERSON><PERSON>н", "smw-copy-clipboard-title": "Чулацам буфер чу копи бар", "smw-jsonview-expand-title": "JSON хьажар гайтар", "smw-jsonview-collapse-title": "JSON хьажа<PERSON> къайладаккхар", "smw-jsonview-search-label": "Лахар:", "smw-redirect-target-unresolvable": "Ӏалашо магош йац, бахьана: «$1»", "smw-types-title": "Тайпа: $1", "smw-schema-namespace-editcontentmodel-disallowed": "[https://www.semantic-mediawiki.org/wiki/Help:<PERSON><PERSON>a Схемин агӀонан] чулацаман модель хийца бакъо йац.", "smw-schema-namespace-edit-protection": "ХӀара агӀо ларйина йу, <code>smw-schemaedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions бакъо] йолчу декъашхоша бен хийца йиш йац.", "smw-schema-namespace-edit-protection-by-import-performer": "ХӀара агӀо импорт йина йу гайтинчу [https://www.semantic-mediawiki.org/wiki/Import_performer импортерца], иза бохург ду билгалбинчу декъашхойн бен бакъо йац хӀокху агӀонан чулацам хийца.", "smw-schema-error-title": "Валидацин {{PLURAL:$1|1=гӀалат|гӀалаташ}}", "smw-schema-error-schema": "'''$1''' валидацин схемин карийна хӀара нийса цахиларш:", "smw-schema-error-miscellaneous": "Тайп-тайпана гӀалаташ ($1)", "smw-schema-error-validation-json-validator-inaccessible": "JSON валидатор \"<b>$1</b>\" тӀекхочуш йац (йа дӀахӀоттийна йац) и бахьана долуш файл \"$2\" талла а, карарчу хенахь йолу агӀо Ӏалашйан а, хийца а новкъарло йо.", "smw-schema-error-validation-file-inaccessible": "Талла<PERSON><PERSON><PERSON> файл «$1» тӀекхочуш йац.", "smw-schema-error-type-missing": "Чулаца<PERSON>ан тайпа дац иза довза а, лело а оьшуш долу [https://www.semantic-mediawiki.org/wiki/Help:Schema схемийн цӀераш] чохь.", "smw-schema-error-type-unknown": "«$1» тайпа дӀайаздина дац, цун дела цӀерийн меттигехь чулацамна лело йиш йац [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-error-json": "JSON гӀалат: \"$1\"", "smw-schema-error-input": "Чуйаларан валидацино билгалйаьхна чулацам Ӏалашбале хьалха дӀайаха йезаш йолу проблемаш. [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling Схемин гӀо] агӀонан тӀехь хьехам хила тарло схема чуйаларца нийса ца догӀуш дерг йа проблемаш дӀайахарехь.", "smw-schema-error-input-schema": "Талларан схемина '''$1''' карийна хӀара нийса ца догӀуш дерг, чулацам Ӏалашбаре дӀадаккха дезаш долу. И проблемаш муха дӀайаха йеза лаьцна хьехамаш каро йиш йу [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling схемин гӀоьнан агӀонан] тӀехь.", "smw-schema-error-title-prefix": "ХӀокху схемин тайпанна оьшу схемин цӀе «$1» дешхьалхенца йолайала.", "smw-schema-validation-error": "«$1» тайпа дӀайаздина дац, цун дела цӀерийн меттигехь чулацамна лело йиш йац [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-validation-schema-title": "JSON-схема", "smw-schema-summary-title": "<PERSON>уьнах лаьцна", "smw-schema-title": "Схема", "smw-schema-usage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-schema-type": "Схемин тайпа", "smw-schema-type-description": "Тай<PERSON>анах лаьцна", "smw-schema-description": "Схемех лаьцна", "smw-schema-description-link-format-schema": "ХӀокху схемин тайпано гӀо до контекстан хьашташ кхочушдаран билгалонаш билгалйаха, дӀайеллачу [[Property:Formatter schema|форматеран схемин]] башхаллица цхьаьна.", "smw-schema-description-search-form-schema": "ХӀокху схемин тайпано гӀо до [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch шуьйрачу лехамна] профилан чуйалоран формаш а, башха<PERSON><PERSON><PERSON><PERSON> а къастош, цигахь цу чохь йу тӀедиллар, муха генераци йан йеза чуйоьллина меттигаш, билгалйаха стандартан цӀерийн меттигаш, йа дӀакхайкхо деза префиксан лехаман дехарна.", "smw-schema-description-property-profile-schema": "ХӀокху схемин тайпано гӀо до профиль къастош, дӀайеллачу башхаллин амал а, цуьнан аннотацин маьӀна а дӀакхайкхо.", "smw-schema-description-facetedsearch-profile-schema": "ХӀокху схемин тайпано гӀо до [[Special:FacetedSearch|Фасетан лехаман]] гонехь лелош йолу профилаш къасто.", "smw-schema-description-property-group-schema": "ХӀокху схемин тайпано гӀо до [https://www.semantic-mediawiki.org/wiki/Help:Property_group башхаллийн тобанаш] къасто [https://www.semantic-mediawiki.org/wiki/Help:Special:B<PERSON><PERSON> хьажаран] интерфейсан структур йан гӀо деш йолу.", "smw-schema-description-property-constraint-schema": "ХӀокхо гӀо до башхаллин экземпляран дехкарийн бакъонаш билгалйаха, иштта цунна маьӀнаш хӀитто.", "smw-schema-description-class-constraint-schema": "ХӀокху схемин тайпано гӀо до классан экземпляран дехкарийн бакъонаш билгалйаха (иштта категори санна йевзаш йолу).", "smw-schema-tag": "{{PLURAL:$1|Тег}}:", "smw-property-predefined-constraint-schema": "«$1» — хьалххе билгалйаьккхина башхалла йу, цо билгалйоккху дехкарийн схема, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-desc": "«$1» — хьалххе билгалйаьккхина башхалла йу, шена чохь схемин дийцар а долуш, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-def": "«$1» — хьалххе билгалйаьккхина башхалла йу, схемин чулацам гойтуш, иза луш ду [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-tag": "«$1» — хьалххе билгалйаьккхина башхалла йу, луш йу [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] идентификацин коллекци билгалйаккхархьама.", "smw-property-predefined-long-schema-tag": "Цхьатерра чулацам йа амалш йолу схемаш билгалйохуш йолу билгало.", "smw-property-predefined-schema-type": "«$1» — хьалххе билгалйаьккхина башхалла йу, схемин тоба къасторан тайпа гойтуш долу, иза ло [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-schema-type": "ХӀора [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type тайпа] синтаксисин элементийн а, лелоран бакъонийн а шен тидар ло, иза гайта йиш йу [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation талларан схемин] гӀоьнца.", "smw-ask-title-keyword-type": "Коьртачу дешнашца леха", "smw-ask-message-keyword-type": "ХӀара лехар билламца догӀуш ду <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Генарчу Ӏалашонца «$1» зӀе латто аьтто ца баьлла.", "smw-remote-source-disabled": "Хьосто '''$1''' генара дехарш дӀадаьхна!", "smw-remote-source-unmatched-id": "Хьост '''$1''' нийса ца догӀу Semantic MediaWiki версица, генара дехарш дан йиш йолуш.", "smw-remote-request-note": "ЖамӀ схьаэцна генарчу хьостанера '''$1''', ткъа генераци йинчу чулацамехь хир йу карарчу хенахь вики чохь йоцу информаци.", "smw-remote-request-note-cached": "ЖамӀ '''кэш'' ду генарчу хьостанера '''$1''', ткъа генераци йина чулацамехь хир йу карарчу хенахь вики чохь йоцу информаци.", "smw-parameter-missing": "«$1» параметр йац.", "smw-property-tab-usage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-property-tab-profile-schema": "Профил<PERSON><PERSON> схема", "smw-property-tab-redirects": "Синони<PERSON><PERSON>ш", "smw-property-tab-subproperties": "Бухара башхаллаш", "smw-property-tab-errors": "Нийса доцу тӀедилларш", "smw-property-tab-constraint-schema": "Дех<PERSON><PERSON><PERSON><PERSON>н схема", "smw-property-tab-constraint-schema-title": "ХӀоттийна лацаран схема", "smw-property-tab-specification": "… дуккхаха", "smw-concept-tab-list": "МогӀам", "smw-concept-tab-errors": "ГӀалаташ", "smw-ask-tab-result": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-extra": "<PERSON><PERSON><PERSON>н а", "smw-ask-tab-debug": "Нис<PERSON><PERSON><PERSON>", "smw-ask-tab-code": "<PERSON>од", "smw-install-incomplete-tasks-title": "Кхочуш ца дина администрацин декхарш", "smw-install-incomplete-intro": "{{PLURAL:$1|ХӀоттор}} чекхдаккха дезаш ду $2 йа [[Special:PendingTaskList|хьоьжуш ду]] {{PLURAL:$2|тӀедиллар|тӀедилларш}} [https://www.semantic-mediawiki.org Semantic MediaWiki]. Кхоччуш бакъонаш йолчу администраторан йа декъашхочун таро йу {{PLURAL:$2|хӀара гӀуллакх чекхдаккха|хӀара гӀуллакхаш чекхдаха}}. Иза дан деза керла хаамаш карлабахале, цхьаьнадогӀуш ца хилийта.", "smw-install-incomplete-intro-note": "ХӀара хаам дӀабоьрзур бу дерриге а гӀуллакхаш чекхдевллачул тӀаьхьа.", "smw-pendingtasks-intro-empty": "Цхьа а гӀуллакх классификаци ца йина лаьтташ санна, чекх ца даьлла йа кхочуш данза санна Semantic MediaWiki доьзна.", "smw-pendingtasks-intro": "ХӀокху агӀонан тӀехь информаци йу гӀуллакхех лаьцна, уьш хьоьжуш, чекх ца даьлла, йа кхочуш данза санна, Semantic MediaWiki доьзна.", "smw-pendingtasks-setup-no-tasks-intro": "ХӀоттор (йа карладаккхар) чекхдаьлла, карарчу хенахь дисина йа чекх ца даьхна гӀуллакхаш дац.", "smw-pendingtasks-tab-setup": "Нисдаран гӀирс", "smw-updateentitycollation-incomplete": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> параметр дукха хан йоццуш хийцина йу, цунна оьшу <code>[https://www.semantic-mediawiki.org/wiki/Help: updateEntityCollation.php]</code> скрипт кхочушйан, духаш карлайаха а, нийса нисдаран меттиган маьӀна хилийта а.", "smw-updateentitycountmap-incomplete": "<code>smw_countmap</code> меттиг тӀетоьхна тӀаьххьарчу релизехь, цунна оьшу <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> скрипт кхочушйан, функцешна оцу меттиган чулацаме кхача аьтто хилийта.", "smw-populatehashfield-incomplete": "<code>smw_hash</code> меттиг дӀайазйар дӀадаьккхина дӀахӀотторан хенахь. Оьшу <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> скрипт кхочушйан.", "smw-install-incomplete-populate-hash-field": "<code>smw_hash</code> меттиг дӀайазйар дӀадаьккхина дӀахӀотторан хенахь. Оьшу <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> скрипт кхочушйан.", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code> х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore Ӏад йитаран кепаца ларма] санна, амма шордарна ца карийра цхьа а дӀайаздар, <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> скрипт кхочушйина аьлла. ДӀахоттайе скрип инструкцешца.", "smw-elastic-rebuildelasticindex-run-incomplete": "<code>ElasticStore</code> х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore Ӏад йитаран кепаца ларма] санна, амма шордарна ца карийра цхьа а дӀайаздар, <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> скрипт кхочушйина аьлла. ДӀахоттайе скрип инструкцешца.", "smw-pendingtasks-setup-intro": "<b>Semantic MediaWiki</b> {{PLURAL:$1|хӀотторо|карлайаккхаро}} классификаци йина хӀара гӀуллакхаш [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade incomplete], ткъа хьоьжу кхачам боллуш администраторо (йа декъашхочо бакъо ло и гӀуллакхаш кхочушдан хьалха) чулацам кхолла йа хийца.", "smw-pendingtasks-setup-tasks": "Ӏалашонаш", "smw-filter-count": "Фильтр лоруш", "smw-es-replication-check": "Репликаци таллар (Elasticsearch)", "smw-es-replication-error": "Репликацин проблема Elasticsearch", "smw-es-replication-file-ingest-error": "Файл чуйаккхаран проблема", "smw-es-replication-maintenance-mode": "Elasticsearch латтор", "smw-es-replication-error-missing-id": "Репликацин мониторингехь гучуделира $1» (ID: $2) йаззам ца хилар Elasticsearch бекэндехь.", "smw-es-replication-error-divergent-date": "Репликацин мониторингехь гучуделира «$1» (ID: $2) <b> йаззаман <b>х<PERSON><PERSON>и<PERSON> хан</b> гойту нийса цахилар.", "smw-es-replication-error-divergent-date-short": "Дустарна лелийна хӀара денош:", "smw-es-replication-error-divergent-date-detail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> хийцина хан:\n* Elasticsearch: $1\n* Database: $2", "smw-es-replication-error-divergent-revision": "Репликацин мониторингехь гучуделира «$1» (ID: $2) йаззамна <b>богӀуш болу хийцам</b> гойту нийса цахилар.", "smw-es-replication-error-divergent-revision-short": "Дустарна лелийна хӀара йуха хьаж<PERSON><PERSON><PERSON><PERSON> хаамаш:", "smw-es-replication-error-divergent-revision-detail": "Бо<PERSON>зна хийцамаш:\n* Elasticsearch: $1\n* Database: $2", "smw-es-replication-error-maintenance-mode": "Elasticsearch репликаци карарчу хенахь дозаделла йу, хӀунда аьлча иза болх беш йу [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>гӀулаккхан рожехь</b>], хийцамаш духашкахь а, агӀонашкахь а сихха гуш <b>бац</b>, ткъа дехаран жамӀашкахь хила тарло ширйелла информаци.", "smw-es-replication-error-no-connection": "Репликацин мониторан йиш йац цхьа а таллам бан, хӀунда аьлча цуьнан йиш йац Elasticsearch кластерца зӀе латто.", "smw-es-replication-error-bad-request-exception": "Elasticsearch зӀенан нисйечо вон дехаран йукъарадаьккхира («400 conflict http гӀалат»), гойтуш репликацин а, лехаман а дехарш деш лаьтташ йолу проблема.", "smw-es-replication-error-other-exception": "Elasticsearch зӀенан нисйечо делла йукъарадаккхар: «$1».", "smw-es-replication-error-suggestions": "Цхьаьна ца догӀуш дерг нисдархьама агӀо хийца йа дӀайаккха дика ду. Нагахь санна проблема лаьтташ йелахь, хьажа ша Elasticsearch кластере (аллокатор, йукъарадахарш, диска тӀехь меттиг, и. дӀ. кх. а).", "smw-es-replication-error-suggestions-maintenance-mode": "Викин администраторе йаздар дика ду, [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild индекс меттахӀоттор] йа <code>refresh_interval</code> карарчу хенахь Ӏад йитаран кепаца дӀахӀоттийна йуй.", "smw-es-replication-error-suggestions-no-connection": "Рекомендаци йо вики-администраторе йаздар, «зӀе йац» бохучух лаьцна хаам бар.", "smw-es-replication-error-suggestions-exception": "Жу<PERSON><PERSON><PERSON><PERSON><PERSON> хьажа Elasticsearch хьолах лаьцна информаци а, цуьнан индексаш а, хила тарлуш йолу галморзахаллин проблемаш а.", "smw-es-replication-error-file-ingest-missing-file-attachment": "Репликацин мониторингехь гучуделира «$1» цахилар [[Property:File attachment|Файл тӀетохар]] аннотаци, гойтуш файлаш чуйахаран процесс болх беш ца хилар йа чекхйаьлла ца хилар.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion Файлаш тӀельцу] файлан аннотаци а, индекс а хилале хьалха дӀахӀоттийна а, чекхйаьккхина а хиларх тешна хила.", "smw-report": "Отчет", "smw-legend": "Легенда", "smw-datavalue-constraint-schema-category-invalid-type": "Аннота<PERSON><PERSON> йина «$1» схема нийса йац категорина, цунна оьшу «$2» тайпа.", "smw-datavalue-constraint-schema-property-invalid-type": "Аннота<PERSON><PERSON> йина «$1» схема нийса йац башхаллина, цунна оьшу «$2» тайпа.", "smw-entity-examiner-check": "Фонан рожехь {{PLURAL:$1|экзаменатор|экзаменаторш}} дӀахӀоттор", "smw-entity-examiner-indicator": "Субъектийн проблемийн панель", "smw-entity-examiner-deferred-check-awaiting-response": "«$1» эксперт карарчу хенахь бекэндера жоьпе хьоьжу.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "Доз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-entity-examiner-associated-revision-mismatch": "Верси", "smw-entity-examiner-deferred-fake": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-entity-examiner-indicator-suggestions": "Объект толлуш, карийна хӀара {{PLURAL:$1|проблема|проблемаш}}, ткъа тӀедуьллу лерина {{PLURAL:$1|проблема|уьш}} хьажа а, богӀу {{PLURAL:$1|ардам|ардамаш}} бан а.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Дехкар|Дехкарш}}", "smw-indicator-revision-mismatch": "Редакци", "smw-indicator-revision-mismatch-error": "[https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner Йоьзна йолу ревизина] карийна нийса ца догӀуш хилар MediaWiki хьахийначу ревизина а,  Semantic MediaWiki тӀехь йиначу оцу духана а йукъахь.", "smw-indicator-revision-mismatch-comment": "Цхьаьна ца догӀуш хиларо дукхахьолахь гойту цхьана процессо Semantic MediaWiki Ӏалашдаран операци йукъахйаьккхина хилар. Серверан журналаш хьажа а, йукъарадахарш йа кхин галморзахаллаш лаха а дика ду.", "smw-facetedsearch-intro-text": "[https://www.semantic-mediawiki.org/wiki/Faceted_search <b>Фацетан лехаро</b>] Semantic MediaWiki декъашхошна атта интерфейс ло, лехаман жамӀаш сиха готта дан шардарца, дозуш долчу башхаллаш а, категореш а бина фасетийн хьажар лелош.", "smw-facetedsearch-intro-tips": "* Леладе <code>category:?</code>, <code>property:?</code>, йа <code>concept:?</code> леха лело йиш йолу категореш, башхаллаш, йа концепцеш, би<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> кхоллархьама.\n* #ask синтаксис лелайе, биллам бийца (масала, <code><nowiki>[[Category:Foo]]</nowiki></code>).\n* Чолхе хьелаш кхолла «OR», «AND», йа кхин дехаран аларш леладе.\n* <code>in:</code> йа <code>phrase:</code> санна долу аларш лело мегар ду дуьззина текстан лехамашкахь йа структурехь доцу лехаршкахь, нагахь санна хаьржинчу [https://www.semantic-mediawiki.org/wiki/Query_engine дехарийн системо] и аларш ловш делахь.", "smw-facetedsearch-profile-label-default": "Профиль Ӏад йитаран кепаца", "smw-facetedsearch-intro-tab-explore": "Талла", "smw-facetedsearch-intro-tab-search": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-facetedsearch-explore-intro": "Коллекци хаь<PERSON><PERSON><PERSON>на хьажар доладе.", "smw-facetedsearch-profile-options": "Профилан параметраш", "smw-facetedsearch-size-options": "Пей<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> параметраш", "smw-facetedsearch-order-options": "Заказан варианташ", "smw-facetedsearch-format-options": "Гуш болу гӀирсаш", "smw-facetedsearch-format-table": "Таблица", "smw-facetedsearch-input-filter-placeholder": "фильтр...", "smw-facetedsearch-no-filters": "Фильт<PERSON><PERSON><PERSON> йац.", "smw-facetedsearch-no-filter-range": "Фильтран диапазон йац.", "smw-facetedsearch-no-output": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> «$1» форматан, цхьа а арахецар ца хилла.", "smw-facetedsearch-clear-filters": "ДӀадаккха {{PLURAL:$1|фильтр}}", "smw-search-placeholder": "Лоьху...", "smw-listingcontinuesabbrev": "(кхин дlа)", "smw-showingresults": "<PERSON>а<PERSON><PERSON>х<PERSON>а {{PLURAL:$1|гойту}} <strong>$1</strong> {{PLURAL:$1|хилам}}, дӀаболало кху № <strong>$2</strong>."}