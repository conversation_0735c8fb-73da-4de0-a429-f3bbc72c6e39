{"@metadata": {"authors": ["Bjankuloski06", "Nemo bis", "Rancher", "Vlad5250", "아라"]}, "smw-desc": "Го прави вашето вики подостапно - за машини ''и'' луѓе ([https://www.semantic-mediawiki.org/wiki/Help:User_manual?uselang=mk документација])", "smw-error": "Грешка", "smw-upgrade-release": "Издание", "smw-upgrade-progress": "Напредок", "smw-upgrade-error-title": "Грешка » Семантички МедијаВики", "smw-semantics-not-enabled": "Семантички МедијаВики не е овозможен на ова вики.", "smw_viewasrdf": "RDF-тековник", "smw_finallistconjunct": " и", "smw-factbox-head": "... повеќе за „$1“", "smw-factbox-facts": "Факти", "smw-factbox-facts-help": "Ги прикажува исказите и фактите што се создадени од некој корисник", "smw-factbox-attachments": "Прилози", "smw-factbox-attachments-value-unknown": "Н/П", "smw-factbox-attachments-help": "Ги прикажува достапните прилози", "smw-factbox-facts-derived": "Изведени факти", "smw_isspecprop": "Ова својство е специјално својство во ова вики.", "smw-concept-cache-header": "Употреба на меѓусклад", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count Меѓускладот на концепти] содржи {{PLURAL:$1|'''единица''' entity|'''$1''' единици}} ($2).", "smw-concept-no-cache": "Нема достапен меѓусклад.", "smw_concept_description": "Опис на концептот „$1“", "smw_no_concept_namespace": "Концептите можат да се определуваат само на страници во именскиот простор „Концепт:“ („Concept:“).", "smw_multiple_concepts": "Секоја концептна страница може да има само по една одредба на концепт.", "smw_concept_cache_miss": "Концептот „$1“ во моментов не може да се користи, бидејќи местењата на викито бараат тој да се пресмета вонсемрежно.\nАко проблемот не исчезне по извесно време, побарајте од вашиот администратор да го овозможи тој концепт.", "smw_noinvannot": "На обратните својства не можат да им се назначуваат вредности.", "version-semantic": "Семантички додатоци", "smw_baduri": "URI-ја од обликот „$1“ не се дозволени.", "smw_printername_count": "Исход од пребројувањето", "smw_printername_csv": "CSV извоз", "smw_printername_dsv": "DSV-извоз", "smw_printername_debug": "Поправање грешки во барања (за експерти)", "smw_printername_embedded": "Вметни содржина од страница", "smw_printername_json": "JSON извоз", "smw_printername_list": "Список", "smw_printername_plainlist": "Прост список", "smw_printername_ol": "Список со редни броеви", "smw_printername_ul": "Список со потточки", "smw_printername_table": "Табела", "smw_printername_broadtable": "Широка табела", "smw_printername_template": "Предлошка", "smw_printername_templatefile": "Предложна податотека", "smw_printername_rdf": "RDF-извоз", "smw_printername_category": "Категорија", "validator-type-class-SMWParamSource": "текст", "smw-paramdesc-limit": "Највеќе ставки во исходот за прикажување", "smw-paramdesc-offset": "Отстапувањето на првата исходна ставка.", "smw-paramdesc-headers": "Прикажувај наслови/имиња на својства", "smw-paramdesc-mainlabel": "Ознаката која се дава на името на главната страница", "smw-paramdesc-link": "Прикажи ги вредностите како врски", "smw-paramdesc-intro": "Текстот за прикажување пред исходот од барањето, ако го има", "smw-paramdesc-outro": "Текстот за прикажување по исходот од барањето, ако го има", "smw-paramdesc-default": "Текстот за прикажување ако нема исход од барањето", "smw-paramdesc-sep": "Одделувач на исходни ставки", "smw-paramdesc-propsep": "Одделувачот помеѓу својства во исходна ставка", "smw-paramdesc-valuesep": "Одделувачот помеѓу вредностите за својство во исходна ставка", "smw-paramdesc-showsep": "Прикажувај го одделувачот најгоре во CSV-податотеката (\"sep=<value>\")", "smw-paramdesc-distribution": "Наместо да се прикажат сите вредности, изброј колку пати се јавуваат и прикажи ги нив.", "smw-paramdesc-distributionsort": "Подреди ја распределбата на вредностите по број на јавувања.", "smw-paramdesc-distributionlimit": "Ограничи ја распределбата на вредности според бројот на само некои вредности.", "smw-paramdesc-template": "Името на предлошката со чија помош ќе се прикажуваат податоците", "smw-paramdesc-columns": "Бројот на столбови за приказ на исходни ставки", "smw-paramdesc-userparam": "Вредност која се дава при секое повикување на предлошка, ако се користи", "smw-paramdesc-class": "Дополнителна CSS-класа за задавање во списокот", "smw-paramdesc-introtemplate": "Име на предлошката за приказ пред исходот од пребарувањето, ако го има", "smw-paramdesc-outrotemplate": "Име на предлошката за приказ по исходот од пребарувањето, ако го има", "smw-paramdesc-embedformat": "HTML-ознаката која се користи за определување на наслови", "smw-paramdesc-embedonly": "Не прикажувај наслови", "smw-paramdesc-table-class": "Дополнителна CSS-класа за табелата", "smw-paramdesc-table-transpose": "Прикажувај ги заглавијата на табелите вертикално, а ставките хоризонтално", "smw-paramdesc-rdfsyntax": "RDF-синтаксата што ќе се користи", "smw-paramdesc-csv-sep": "Укажува столбен разделник", "smw-paramdesc-csv-valuesep": "Укажува вредносен разделник", "smw-paramdesc-dsv-separator": "Кој разделник да се користи", "smw-paramdesc-dsv-filename": "Име на DSV-податотеката", "smw-paramdesc-filename": "Име на изводната податотека", "smw-smwdoc-description": "Прикажува табела на сите параметри што можат да се искористат за назначениот формат на исход заедно со основните вредности и описи.", "smw-smwdoc-par-format": "Форматот на ставките за кои се прикажува параметарска документација.", "smw-smwdoc-par-parameters": "Кои параметри да се прикажуваат. „specific“ за оние што ги додава форматот, „base“ за оние достапни во сите формати, а „all“ за обете.", "smw-paramdesc-sort": "По кое својство да се подреди барањето", "smw-paramdesc-order": "Редослед на подредување на барањето", "smw-paramdesc-searchlabel": "Текст за продолжување на пребарувањето", "smw-paramdesc-named_args": "Именувајте ги аргументите што ѝ се предаваат на предлошката.", "smw-paramdesc-export": "Можност за извоз", "smw-paramdesc-prettyprint": "Дали исписот да се форматира со дополнителни вовлекувања и нови редови", "smw-paramdesc-json-type": "Тип на серијализација", "smw-paramdesc-source": "Алтер<PERSON>тивен извор за барање", "smw-paramdesc-jsonsyntax": "JSON-синтакса што ќе се користи", "smw-printername-feed": "RSS- и Атом-тековник", "smw-paramdesc-feedtype": "Вид тековник", "smw-paramdesc-feedtitle": "Текст како наслов на тековникот", "smw-paramdesc-feeddescription": "Текст како опис на тековникот", "smw-paramdesc-feedpagecontent": "Содржина на страницата што се прикажува со тековникот", "smw-label-feed-description": "$2-тековник на $1", "smw-paramdesc-mimetype": "Медиумски тип (MIME-тип) за изводната податотека", "smw_iq_disabled": "На ова вики се оневозможени семантички барања.", "smw_iq_moreresults": "... понатамошни ставки", "smw_parseerror": "Дадената вредност не беше разбрана.", "smw_decseparator": ",", "smw_kiloseparator": ".", "smw_notitle": "„$1“ не може да се користи како име на страница во ова вики.", "smw_noproperty": "„$1“ не може да се користи како име на својство во ова вики.", "smw_wrong_namespace": "Тука се дозволени само страници во именскиот простор „$1“.", "smw_manytypes": "За својството е определен повеќе од еден тип.", "smw_emptystring": "Не се прифаќаат празни низи.", "smw_notinenum": "„$1“ не е на списокот ($2) на [[Property:Allows value|допуштени вредности]] за својството „$3“.", "smw-datavalue-constraint-error-allows-value-list": "„$1“ не е на списокот ($2) на [[Property:Allows value|допуштени вредности]] за својството „$3“.", "smw_noboolean": "„$1“ не претставува Булова вредност (точно/неточно).", "smw_true_words": "вистина,да,в,д,true,t,yes,y", "smw_false_words": "лага,л,невистина,н,не,false,f,no,n", "smw_nofloat": "„$1“ не претставува број.", "smw_infinite": "Броевите со толкава должина како „$1“ не се поддржани.", "smw_unitnotallowed": "„$1“ не се смета за важечка мерна единица за ова својство.", "smw_nounitsdeclared": "Нема наведено мерни единици за ова својство.", "smw_novalues": "Нема назначено вредности.", "smw_nodatetime": "Датумот „$1“ не е разбран.", "smw_toomanyclosing": "Во барањето има премногу јавувања на „$1“.", "smw_noclosingbrackets": "Во вашето барање беа искористени загради „<nowiki>[[</nowiki>“ на кои им недостатуваат соодветни затворачки загради „]]“.", "smw_misplacedsymbol": "Знакот „$1“ е додаден на место кајшто не е корисен", "smw_unexpectedpart": "Делот „$1“ од барањето не беше разбран.\nИсходот може да се разликува од очекуваното.", "smw_emptysubquery": "Едно од подбарањата нема важечки услов.", "smw_misplacedsubquery": "Користено е подбарање на некое место кадешто не се дозволени подбарања.", "smw_valuesubquery": "Подбарањата не се поддржани за вредностите на својството „$1“.", "smw_badqueryatom": "Извесен дел „<nowiki>[[…]]</nowiki>“ од барањето не беше разбран.", "smw_propvalueproblem": "Вредноста на својството „$1“ не е разбрана.", "smw_noqueryfeature": "Некои функции од барањето не се поддржани на ова вики, и затоа е испуштен дел од барањето ($1).", "smw_noconjunctions": "Сврзници во барања не се поддржани во ова вики, и затоа е испуштен дел од барањето ($1).", "smw_nodisjunctions": "Во ова вики не се поддржани дисјункции во барањата, и затоа еден дел од барањето е отфрлен ($1).", "smw_querytoolarge": "Следниве услови на барањето <code>$1</code> не можеа да бидат земени предвид заради ограничувањата на големината или длабочината на барањата во ова вики.", "smw_notemplategiven": "Наведете вредност за параметарот „template“ за да може да работи овој формат на барање.", "smw_db_sparqlqueryproblem": "Не можев да добијам исход за барањето од базата SPARQL. Грешката може да е привремена или да се должи на бубачка во програмот на базата.", "smw_db_sparqlqueryincomplete": "Одговарањето на барањето испадна претешко и беше прекинато. Може да се изоставени некои исходни ставки. Ако можете, обидете се со поедноставно барање.", "smw_type_header": "Својства од типот „$1“", "smw_typearticlecount": "{{PLURAL:$1|Прикажано е $1 својство кое го користи|Прикажани се $1 својства кои го користат}} овој тип.", "smw_attribute_header": "Страници кои го користат својството „$1“", "smw_attributearticlecount": "{{PLURAL:$1|Прикажана е $1 страница која го користи ова својство|Прикажани се $1 страници кои го користат}} ова својство.", "smw-propertylist-subproperty-header": "Потсвојства", "smw-propertylist-redirect-header": "Истозначници", "smw-propertylist-error-header": "Страници со несвојствени доделувања", "smw-propertylist-count": "{{PLURAL:$1|Прикажана е $1 единица|Прикажани се $1 единици}}.", "smw-propertylist-count-with-restricted-note": "{{PLURAL:$1|Прикажана е $1 поврзана единица|Прикажани се $1 поврзани единици}}. (достапни се повеќе, но ограничувањето е „$2“).", "smw-propertylist-count-more-available": "{{PLURAL:$1|Прикажана е $1 поврзана единица|Прикажани се $1 поврзани единици}}. (достапни се повеќе).", "specialpages-group-smw_group": "Семантички МедијаВики", "specialpages-group-smw_group-maintenance": "Одржување", "specialpages-group-smw_group-properties-concepts-types": "Својства, концепти и типови", "specialpages-group-smw_group-search": "Прелистување и пребарување", "exportrdf": "Извези страници во RDF", "smw_exportrdf_docu": "Оваа страница ви овозможува да преземете податоци од страница во RDF формат.\nЗа да ги извезете страниците, внесете ги насловите во кутијата подолу (по еден наслов во секој ред).", "smw_exportrdf_recursive": "Рекурзивно извези ги сите поврзани страници.\nИмајте на ум дека исходот може да биде мошне голем!", "smw_exportrdf_backlinks": "Извези ги и страниците кои укажуваат на извезените страници.\nСоздава RDF со поддршка за прелистување.", "smw_exportrdf_lastdate": "Не извезувај страници кои се немаат променето од назначениот датум наваму.", "smw_exportrdf_submit": "Извези", "uriresolver": "URI претворач", "properties": "Својства", "smw-categories": "Категории", "smw_properties_docu": "Во викито се користат следниве својства.", "smw_property_template": "$1 од типот $2 ($3 {{PLURAL:$3|употреба|употреби}})", "smw_propertylackspage": "Сите својства треба да се опишани во страница!", "smw_propertylackstype": "Нема назначено тип за ова својство (засега по основно ќе се користи типот $1)", "smw_propertyhardlyused": "Ова својство речиси не се користи на викито!", "smw-property-name-invalid": "Својството $1 не може да се користи (има неважечко име).", "smw-sp-property-searchform": "Прикажи својства што содржат:", "smw-sp-property-searchform-inputinfo": "Филтрирањето разликува големи и мали букви, па затоа ќе ви се прикажат само својства што одговараат точно на внесеното.", "smw-special-property-searchform": "Прикажи својства што содржат:", "smw-special-property-searchform-inputinfo": "Филтрирањето разликува големи и мали букви, па затоа ќе ви се прикажат само својства што одговараат точно на внесеното.", "smw-special-property-searchform-options": "Можности", "smw-special-wantedproperties-filter-label": "Филтер:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Неодобрени", "concepts": "Концепти", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Concepts?uselang=mk Концептот] може да се гледа како „динамична категорија“, т.е. збир страници што не се создадени рачно, туку пресметани од Семантички МедијаВики како опис на дадено барање.", "smw-special-concept-header": "Список на концепти", "smw-special-concept-count": "Во списокот {{PLURAL:$1|е наведен следниов концепт|се наведени следниве $1 концепти}}.", "smw-special-concept-empty": "Не пронајдов ниеден концепт.", "unusedproperties": "Неискористени својства", "smw-unusedproperties-docu": "На страницава се наведени [https://www.semantic-mediawiki.org/wiki/Unused_properties неискористени својства] кои се изјавени, иако ниедна друга страница не ги користи. За диференциран изглед погледајте ги службените страници за [[Special:Properties|сите]] или [[Special:WantedProperties|потребни својства]].", "smw-unusedproperty-template": "$1 од типот $2", "wantedproperties": "Потребни својства", "smw-wantedproperties-docu": "На страницава се наведени [https://www.semantic-mediawiki.org/wiki/Wanted_properties потребни својства] се користат во викито, но сè уште немаат своја описна страница. За диференциран изглед, погледајте ги службените страници за [[Special:Properties|сите]] или [[Special:UnusedProperties|неискористени својства]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|употреба|употреби}})", "smw-special-wantedproperties-docu": "На страницава се наведени [https://www.semantic-mediawiki.org/wiki/Wanted_properties потребни својства] кои се користат во викито, но сè уште немаат своја описна страница. За диференциран изглед, погледајте ги службените страници за [[Special:Properties|сите]] или [[Special:UnusedProperties|неискористени својства]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|употреба|употреби}})", "smw_purge": "Превчит<PERSON><PERSON>", "smw-purge-failed": "Превчитувањето не успеа", "types": "Типови", "smw_types_docu": "Список на [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes расположливи податочни типови] со секој [https://www.semantic-mediawiki.org/wiki/Help:Datatype тип] кој претставува единствен збир атрибути за опишување на вредноста во однос на особености за складирање и приказ кои се наследени од доделено својство.", "smw-special-types-no-such-type": "„$1“ е непознат или не е назначен како важечки податочен тип.", "smw-statistics": "Семантички статистики", "smw-statistics-property-instance": "{{PLURAL:$1|Вредност на својство|Вредности на својства}} (вкупно)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Својство|Својства}}]] (вкупно)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Својство|Својства}} (вкупно)", "smw-statistics-property-page": "{{PLURAL:$1|Својство|Својства}} (заведени со страница)", "smw-statistics-property-type": "{{PLURAL:$1|Својство|Својства}} (назначени на податочен тип)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Барање|Барања}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Барање|Барања}}]] (вградено, вкупно)", "smw-statistics-query-format": "Формат <code>$1</code>", "smw-statistics-query-size": "Големина на барањето", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Концепт|Концепти}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Коцепт|Коцепти}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Подобјект|Подобјекти}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Подобјект|Подобјекти}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Податочен тип|Податочни типови}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Вредност на својството|Вредности на својството}} ([[Special:ProcessingErrorList|{{PLURAL:$1|несоодветна прибелешка|несоодветни прибелешки}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Вредност за својство|Вредности за својства}} ({{PLURAL:$1|несоодветен запис|несоодветни записи}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Застарена единица|Застарени единици}}]", "smw_uri_doc": "URI-претварачот применува [$1 W3C пронаоѓање на ознаки на httpRange-14].\nТој има за задача да внимава луѓето да не се претворат во мрежни места.", "ask": "Семантичко пребарување", "smw_ask_sortby": "Подреди по столб (незадолжително)", "smw_ask_ascorder": "Нагорен", "smw_ask_descorder": "Надолен", "smw-ask-order-rand": "Случајна", "smw_ask_submit": "Пронајди", "smw_ask_editquery": "Уреди барање", "smw_add_sortcondition": "(Додај услов за подредување)", "smw-ask-sort-add-action": "Додај услов за подредување", "smw_ask_hidequery": "Скриј барање (компактен изглед)", "smw_ask_help": "Помош со поставување барања", "smw_ask_queryhead": "Услов", "smw_ask_printhead": "Дополнителни податоци за приказ", "smw_ask_printdesc": "(додавајте едно име на својство по ред)", "smw_ask_format_as": "Формати<PERSON><PERSON><PERSON> како:", "smw_ask_defaultformat": "по основно", "smw_ask_otheroptions": "Други нагодувања", "smw-ask-otheroptions-info": "Овој оддел содржи можности за менување на исписот. Описите на параметрите ќе ги видите ако отидете со глувчето врз нив.", "smw-ask-otheroptions-collapsed-info": "Стиснете на иконата „+“ (плус) за да ги погледате сите расположливи можности", "smw_ask_show_embed": "Прикажи вграден код", "smw_ask_hide_embed": "Скриј вметнат код", "smw_ask_embed_instr": "За да го вметнете ова барање во викистраницата меѓу редови, употребете го кодот подолу.", "smw-ask-delete": "Отстрани", "smw-ask-sorting": "Подредување", "smw-ask-options": "Поставки", "smw-ask-options-sort": "Подредувања", "smw-ask-format-options": "Формат и можности", "smw-ask-parameters": "Параметри", "smw-ask-search": "Преб<PERSON><PERSON><PERSON><PERSON>", "smw-ask-debug": "Исправки", "smw-ask-no-cache": "Оневозможи меѓусклад", "smw-ask-result": "Исход", "smw-ask-empty": "Исчисти ги сите ставки", "smw-ask-format": "Формат", "smw-ask-format-selection-help": "Помош за избраниот формат: $1", "smw-ask-extra-other": "Друго", "searchbyproperty": "Пребарај по својство", "processingerrorlist": "Список на грешки при обработка", "constrainterrorlist": "Список на грешки во ограничувањата", "propertylabelsimilarity": "Извештај за сличности во натписите на својствата", "missingredirectannotations": "Отсутни прибелешки за пренасочување", "smw-processingerrorlist-intro": "Следниов список дава преглед на [https://www.semantic-mediawiki.org/wiki/Processing_errors грешки при обработката] кои се имаат јавено во врска со [https://www.semantic-mediawiki.org/ Семантички МедијаВики]. Се предлага редовно надгледување на списоков и да се исправат неважечките прибелешки на вредностите.", "smw-constrainterrorlist-intro": "Следниов список дава преглед на [https://www.semantic-mediawiki.org/wiki/Constraint_errors грешки во ограничувањата] кои се имаат јавено во  врска со [https://www.semantic-mediawiki.org/ Семантички МедијаВики]. Се предлага редовно надгледување на списоков и да се исправат неважечките прибелешки на вредностите.", "smw-missingredirects-intro": "Во овој оддел се наведени страници на кои им отсуствуваат прибелешки за [https://www.semantic-mediawiki.org/wiki/Redirects пренасочување] во Семантички МедијаВики (споредувајќи со информациите складирани во МедијаВИки). За да ги востановите, или рачно [https://www.semantic-mediawiki.org/wiki/Help:Purge пречистете] ја страницата, или пуштете ја скриптата за одржување <code>rebuildData.php</code> (со можноста <code>--redirects</code>).", "smw-missingredirects-list-intro": "Прикажувам $1 {{PLURAL:$1|страница|страници}} со отсутни прибелешки за пренасочување.", "smw-missingredirects-noresult": "Не пронајдов отсутни прибелешки за пренасочување.", "smw_sbv_docu": "Пребарување на сите страници кои имаат дадено својство и вредност.", "smw_sbv_novalue": "Внесете важечка вредност за својството, или пак погледнете ги сите вредности на својството „$1“.", "smw_sbv_displayresultfuzzy": "Список на сите страници кои имаат својство „$1“ со вредност „$2“.\nБидејќи има само неколку исходни ставки, прикажани се и приближни вредности.", "smw_sbv_property": "Својство:", "smw_sbv_value": "Вредност:", "smw_sbv_submit": "Пронајди", "browse": "Прелистај вики", "smw_browselink": "Прелистај својства", "smw_browse_article": "Внесете го името на страницата од која би почнале да прелистувате.", "smw_browse_go": "<PERSON><PERSON><PERSON>", "smw_browse_show_incoming": "Прикажи дојдовни својства", "smw_browse_hide_incoming": "Скриј дојдовни својства", "smw_browse_no_outgoing": "Оваа страница нема својства.", "smw_browse_no_incoming": "До оваа страница не водат никакви својства.", "smw-browse-show-group": "Прикажи групи", "smw-browse-hide-group": "Скриј групи", "smw_inverse_label_default": "$1 од", "smw_inverse_label_property": "Обратен наслов на својството", "pageproperty": "Пребарување својства на страница", "smw_pp_docu": "Внесете и страница и својство, или само својство за да ги добиете сите доделени вредности.", "smw_pp_from": "Од страница:", "smw_pp_type": "Својство:", "smw_pp_submit": "Најди ставки", "smw-prev": "{{PLURAL:$1|претходна $1|претходни $1}}", "smw-next": "{{PLURAL:$1|следна $1|следни $1}}", "smw_result_prev": "Претходно", "smw_result_next": "Следно", "smw_result_results": "Исход", "smw_result_noresults": "Нема исход.", "smwadmin": "Управувачница на Семантички МедијаВики", "smw-admin-statistics-job-title": "Статистики за задачи", "smw-admin-statistics-section-explain": "ОДделот дава дополнителни статистики за администраторите.", "smw-admin-setupsuccess": "Складишниот погон е поставен.", "smw_smwadmin_return": "Назад на $1", "smw_smwadmin_updatestarted": "Започнат е нов процес за обнова на семантичките податоци.\nСите складирани податоци ќе бидат преработени или поправени, каде што е потребно.\nНа оваа службена страница можете да го следите процесот на поднова.", "smw_smwadmin_updatenotstarted": "Веќе е во тек едно подновување.\nНема да се создаде друго.", "smw_smwadmin_updatestopped": "Сите постоечки процеси на поднова се сопрени.", "smw_smwadmin_updatenotstopped": "За да го запрете текот на подновата, мора да го активирате кутивчето за избор кое укажува на тоа дека сте навистина сигурни дека сакате да направите така.", "smw-admin-docu": "Оваа службена страница ви помага во текот на воспоставката, подновата, одржувањето и употребата на <a href=\"https://www.semantic-mediawiki.org/?uselang=mk\">Семантички МедијаВики</a> и исто така обезбедува дополнителни административни функции и задачи, како и статистика.\nНе заборавајте да направите резервни примероци од значајни податоци пред да вршите административни постапки.", "smw-admin-db": "Поставување на базата на податоци", "smw-admin-dbdocu": "Семантички МедијаВики бара додавање на извесни додатоци кон базата на податоци на МедијаВики за да складира семантички податоци.\nФункцијата подолу ви гарантира дека вашата база на податоци е правилно поставена.\nПромените извршени во овој чекор немаат влијание врз остатокот од базата на МедијаВики, и лесно можат да се вратат по старо ако се јави потреба.\nОваа функција на поставката може да се врши повеќе пати без тоа да причини каква било штета, но потребна е само еднаш при воспоставка или подновување.", "smw-admin-permissionswarn": "Ако постапката не успее со грешки при исполнувањето на SQL-наредбите, тогаш корисникот на базата на податоци што го употребува вашето вики (проверете ја вашата податотека „LocalSettings.php“) нема доволно дозволи.\nДоделете му доволно дозволи на корисникот за да може да создава и брише табели, привремено внесете го најавниот корен (root) на вашата база на податоци во „LocalSettings.php“, или пак употребете ја скриптата за одржување <code>setupStore.php</code> која може да ги користи акредитивите за администратор.", "smw-admin-dbbutton": "Започни или поднови табели", "smw-admin-announce": "Објавете го вашето вики", "smw-admin-announce-text": "Ако вашето вики е јавно, можете да го пријавите на <a href=\"https://wikiapiary.com\">WikiApiary</a> — викито за следење на викија.", "smw-admin-deprecation-notice-title": "Напомени за застарување", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> е отстранет во $2", "smw-admin-deprecation-notice-title-notice": "Претстојни промени", "smw-admin-deprecation-notice-title-replacement": "Заменети или преименувани поставки", "smw-admin-deprecation-notice-title-replacement-explanation": "Следново содржи поставки кои се преименувани или поинаку изменети. Се препорачува неодложна поднова на нивното име или формат.", "smw-admin-deprecation-notice-title-removal": "Отстранети поставки", "smw_smwadmin_datarefresh": "Поправка и поднова на податоци", "smw_smwadmin_datarefreshdocu": "Постои можност за враќање на сите податоци на Семантички МедијаВики врз основа на тековните содржини на викито.\nОва е корисно за поправка на оштетени податоци или за обнова на податоците ако внатрешниот формат се има променето како последица на некаква надградба на програмската опрема.\nПодновувањето се врши страница по страница, и ќе треба да помине некое време за да профункционира.\nПодолу е прикажано дали има поднова во тек, и тоа ви овозможува да започнувате или запирате подновувања (освен ако таа можност не е исклучена од администраторот на мрежното место).", "smw_smwadmin_datarefreshprogress": "<strong>Веќе е во тек една поднова.</strong>\nНормално е подновата да биде бавна, бидејќи податоците се обновуваат во мали делчиња секојпат кога корисникот ќе отиде на викито.\nЗа да ја убрзате подновата, можете да ја повикате скриптата за одржување на MediaWiki <code>runJobs.php</code> (користете ја можноста <code>--maxjobs 1000</code> за да го ограничите бројот на поднови во една партија).\nПроценет напредок на тековната поднова:", "smw_smwadmin_datarefreshbutton": "Започни со подновување на податоци", "smw_smwadmin_datarefreshstop": "Започни го ова подновување", "smw_smwadmin_datarefreshstopconfirm": "Да, {{GENDER:$1|сигурен|сигурна}} сум.", "smw-admin-support": "Добивање поддршка", "smw-admin-supportdocu": "Обезбедени се различни ресурси за да ви помогнат во случај на проблеми:", "smw-admin-installfile": "Ако наидете на проблеми при воспоставката, најпрвин прочитајте ги напатствијата во <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">податотеката INSTALL</a> и <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">страницата за воспоставка</a>.", "smw-admin-smwhomepage": "Целосната корисничка документација за Семантички МедијаВики ќе ја најдете на <b><a href=\"http://semantic-mediawiki.org?uselang=mk\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Бубачките (грешките) можат да се пријават на <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>.", "smw-admin-questions": "Ако имате некои други прашања или предлози, приклучете ѝ се на дискусијата на <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">форумот за корисници на Семантички МедијаВики</a>.", "smw-admin-other-functions": "Други функции", "smw-admin-statistics-extra": "Статистички функции", "smw-admin-statistics": "Статистики", "smw-admin-supplementary-settings-intro": "<u>$1</u> ги испишува нагодувањата користени во Семантички МедијаВики", "smw-admin-main-title": "Семантички МедијаВики » $1", "smw-admin-supplementary-operational-statistics-title": "Оперативни статистики", "smw-admin-supplementary-operational-statistics-short-title": "оперативни статистики", "smw-admin-supplementary-operational-statistics-intro": "Прикажува проширен збир на <u>$1</u>", "smw-admin-supplementary-operational-table-statistics-title": "Статистики за табели", "smw-admin-supplementary-operational-table-statistics-short-title": "статистики за табели", "smw-admin-supplementary-elastic-settings-title": "Нагодувања (показатели)", "smw-admin-supplementary-elastic-mappings-title": "Пресликувања", "smw-admin-supplementary-elastic-mappings-summary": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-nodes-title": "Јазли", "smw-admin-supplementary-elastic-statistics-title": "Статистики", "smw-admin-supplementary-elastic-replication-header-title": "Статус на репликацијата", "smw-admin-supplementary-elastic-replication-function-title": "Репликација", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> прикажува информации за неуспешни репликации", "smw-admin-supplementary-elastic-replication-files": "Податотеки", "smw-admin-supplementary-elastic-replication-pages": "Страници", "smw-admin-supplementary-elastic-endpoints": "Завршници", "smw-property-label-similarity-title": "Извештај за сличности во натписите на својствата", "smw-property-label-similarity-threshold": "Праг:", "smw-property-label-similarity-noresult": "Не најдов ништо за избраните можности.", "smw-property-label-similarity-docu": "Оваа страница врши споредба на [https://www.semantic-mediawiki.org/wiki/Property_similarity оддалеченоста на сличностите] (што е различно од семантички или лексички сличности) помеѓу натписите на својствата и ги пријавува ако го надминат прагот. Со извештајот се пронаоѓаат погрешно напишаниет или еквивалентните својства кои го претставуваат истиот концепт (погл. службената страница [[Special:Properties|својства]] за појаснување на концептот и употребата на пријавени својства). Прагот може да се прилагоди за да се прошири или стесни оддалеченоста на приближното совпаѓање. За изземање на својствата од анализата се користи <code>[[Property:$1|$1]]</code>.", "smw_adminlinks_datastructure": "Структура на податоците", "smw_adminlinks_displayingdata": "Приказ на податоци", "smw_adminlinks_inlinequerieshelp": "Помош со вметнати барања", "smw-property-indicator-type-info": "{{PLURAL:$1|Кориснички|Системски}} дефинирано својство", "smw-createproperty-isproperty": "Ова е својство од видот $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Дозволената вредност за ова својство е|Дозволените вредности за ова својство се}}:", "smw-paramdesc-category-delim": "Разделувачот", "smw-paramdesc-category-template": "Предлошка со која ќе се форматираат елементите", "smw-paramdesc-category-userparam": "Параметар што ќе ѝ се даде на предлошката", "smw-info-par-message": "Порака за приказ.", "smw-info-par-icon": "Икона за приказ на „инфо“ или „предупредување“.", "prefs-smw": "Семантички МедијаВики", "prefs-general-options": "Општи можности", "prefs-ask-options": "Семантичко пребарување", "smw-prefs-intro-text": "Долунаведените можности се достапни преку [https://www.semantic-mediawiki.org/ Семантички МедијаВики] (или поврзаните додтоци) со цел да овозможи поединечни прилагодувања на извесни функции. Поподробен опис ќе најдете на [https://www.semantic-mediawiki.org/wiki/Help:User_preferences помошната страница].", "smw-prefs-ask-options-tooltip-display": "Прикажи го текстот на параметарот како информативен отскочен текст", "smw-prefs-ask-options-compact-view-basic": "Вклучи основен збиен поглед", "smw-prefs-general-options-time-correction": "Овозможи временска исправка за службени страници користејќи го поставеното месно [[Special:Preferences#mw-prefsection-rendering|часовно отстапување]]", "smw-prefs-general-options-jobqueue-watchlist": "Прикажувај ја набљудуваната редица задачи во мојата лична лента", "smw-prefs-help-general-options-jobqueue-watchlist": "Ако е вклучено, прикажува [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist список на] избрани задачи кои се на чекање, заедно со должините на нивните редици.", "smw-prefs-general-options-disable-editpage-info": "Исклучи го воведниот текст на уредувачката страница", "smw-prefs-general-options-disable-search-info": "Исклучи ги информациите за поддршка на синтаксата на стандардната пребарувачка страница", "smw-prefs-general-options-suggester-textinput": "Вклучи помошник за внесување кај семантичките единици", "smw-prefs-help-general-options-suggester-textinput": "Ако е вклучено, овозможува употреба на [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance помош при внесување] за пронаоѓање на својства, концепти и категории добиени од вносниот контекст.", "smw-ui-tooltip-title-property": "Својство", "smw-ui-tooltip-title-quantity": "Претворање на единици", "smw-ui-tooltip-title-info": "Информации", "smw-ui-tooltip-title-service": "Услужни врски", "smw-ui-tooltip-title-warning": "Предупредување", "smw-ui-tooltip-title-error": "Грешка", "smw-ui-tooltip-title-parameter": "Пара<PERSON><PERSON><PERSON><PERSON>р", "smw-ui-tooltip-title-event": "Настан", "smw-ui-tooltip-title-note": "Белешка", "smw-ui-tooltip-title-legend": "Легенда", "smw-ui-tooltip-title-reference": "Навод", "smw_unknowntype": "„$1” типот од ова својство е неважечки", "smw-concept-cache-text": "Концептот има вкупно {{PLURAL:$1|една страница|$1 страници}}, а подновен е на $3 во $2 ч.", "smw_concept_header": "Страници на концептот „$1“", "smw_conceptarticlecount": "{{PLURAL:$1|Прикажана е $1 страница која му припаѓа|Прикажани се $1 страници кои му припаѓаат}} на тој концепт.", "smw-qp-empty-data": "Не можам да ги прикажам побараните податоци поради извесни недоволни критеруми за делот.", "right-smw-admin": "Пристап до административни задачи (Семантички МедијаВики)", "action-smw-patternedit": "уредување на регуларни изрази кои ги користи Семантички МедијаВики", "group-smwadministrator": "Администратори (Семантички МедијаВики)", "group-smwadministrator-member": "{{GENDER:$1|администратор (Семантички МедијаВики)}}", "grouppage-smwadministrator": "{{ns:project}}:Ад<PERSON><PERSON><PERSON><PERSON>стратор<PERSON> (Семантички МедијаВики)", "group-smwcurator": "Згрижувачи (Семантички МедијаВики)", "group-smwcurator-member": "{{GENDER:$1|згрижувач (Семантички МедијаВики)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON>гр<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Семантички МедијаВики)", "action-smw-admin": "пристап до административните задачи на Семантички МедијаВики", "smw-property-predefined-default": "„$1“ е предодредено својство.", "smw-property-predefined-common": "Ова својство е однапред програмски зададено (наречено и [https://www.semantic-mediawiki.org/wiki/Help:Special_properties посебно својство]) и со него има дополнителни административни привилегии, но може да се користи како секое друго [https://www.semantic-mediawiki.org/wiki/Property кориснички-зададено својство].", "smw-property-predefined-ask": "„$1“ е предодредено својство кое претставува метаинформации (во облик на [https://www.semantic-mediawiki.org/wiki/Subobject subobject]) за поединечни барања и се добива од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички МедијаВики].", "smw-property-predefined-asksi": "„$1“ е предодредено својство што го собира бројот на услови кои се користат во барање и се добива од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички МедијаВики].", "smw-property-predefined-askde": "„$1“ е предодредено својство што укажува на длабочината на барање и е преземено од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички МедијаВики].", "smw-sp-properties-docu": "На оваа страница се наведени [https://www.semantic-mediawiki.org/wiki/Property својствата] и нивниот број на употреби достапен на ова вики. За најнови статистики за бројот на употреби се препорачува редовно пуштање на скриптата за [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics статистики за својства]. Разделен преглед можете да добиете на службените страници [[Special:UnusedProperties|неискористени својства]] и [[Special:WantedProperties|потребни својства]].", "smw-sp-properties-cache-info": "Наведените податоци се преземени од [https://www.semantic-mediawiki.org/wiki/Caching?uselang=mk меѓускладот], a последен пат изменети на $1.", "smw-sp-properties-header-label": "Список на својства", "smw-admin-settings-docu": "Прикажува список на сите стандардни и местоприлагодени и преведени поставки што се однесуваат на дадената околина во Семантички МедијВики. Повеќе за поединечните поставки ќе најдете на страницата за [https://www.semantic-mediawiki.org/wiki/Help:Configuration помош со поставки].", "smw-sp-admin-settings-button": "Создај список на поставки", "smw-admin-idlookup-title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-idlookup-docu": "Овој оддел покажува технички поединости за поединечна единица (викистраница, потпредмет, својство итн.) во Семантички МедијаВики. Вносот може да биде бројчена назнака или низна вредност што одговара на соодветното поле за бребарување, но сепак било која назнака се однесува на Семантички МедијаВики, но не на назнаката на страницата или преработката на МедијаВики.", "smw-admin-iddispose-title": "Отстранување", "smw-admin-idlookup-input": "Пребарај:", "smw-admin-objectid": "Назнака:", "smw-admin-tab-notices": "Напомени за застарување", "smw-admin-tab-maintenance": "Одржување", "smw-admin-deprecation-notice-section": "Семантички МедијаВики", "smw-admin-configutation-tab-settings": "Нагодувања", "smw-admin-configutation-tab-namespaces": "Именски простори", "smw-admin-maintenance-tab-tasks": "Зада<PERSON>и", "smw-admin-maintenance-tab-scripts": "Скрипти за одржување", "smw-admin-maintenance-no-description": "Нема опис.", "smw-admin-maintenance-script-section-title": "Список на расположливите скрипти за одржување", "smw-livepreview-loading": "Вчитувам...", "smw-sp-searchbyproperty-description": "Страницава дава прост [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces прелистувачки посредник] за пронаоѓање на единици опишани со својство и именувана вредност. На располагање ви се и други пребарувачки посредници: [[Special:PageProperty|пребарување на својства на страници]] и [[Special:Ask|срочувачот на барања]].", "smw-sp-searchbyproperty-resultlist-header": "Список на ставки од исходот", "smw-sp-searchbyproperty-nonvaluequery": "Список на вредности со својство „$1“.", "smw-sp-searchbyproperty-valuequery": "Список на вредности со својство „$1“ со вредноста „$2“.", "smw-datavalue-number-textnotallowed": "„$1“ не може да се додели на изјавен бројчен тип со вредност $2.", "smw-editpage-annotation-enabled": "Оваа страница поддржува семантички прибелешки во текст (на пр.<nowiki>„[[Is specified as::World Heritage Site]]“</nowiki>) за изградба на структурирани пребарливи содржини од Семантички МедијаВики. Поопфатен опис за употребата на прибелешки или расчленувачката функција #ask, погледајте ги помошните страници [https://www.semantic-mediawiki.org/wiki/Help:Getting_started Како да почнете], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation Прибелешки во текст] или [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries Барање во редови].", "smw-editpage-annotation-disabled": "На оваа страница не се овозможени семантички прибелешки во текст поради ограничувањата на именскиот простор. Ако сакате да дознаете како да ги овозможите за овој именски простор, прочитајте ја помошната страница [https://www.semantic-mediawiki.org/wiki/Help:Configuration Поставување].", "smw-editpage-property-annotation-enabled": "Ова својство може да се дополнува користејќи семантички прибелешки за укажување на податочниот тип (на пр.<nowiki>„[[Has type::Page]]“</nowiki>) или други поддржни изјави (како на пр.<nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). За да дознаете како да ја збогатите страницата, погледајте ја помошната страница [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration Изјава на својство] или [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes списокот на расположливи податочни типови].", "smw-editpage-property-annotation-disabled": "Ова својство не може да се дополни со прибелешка за податочен тип (на пр.<nowiki>„[[Has type::Page]]“</nowiki>) бидејќи истиот му е веќе е предзададен (повеќе информации на помошната страница [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Посебни својства]).", "smw-editpage-concept-annotation-enabled": "Овој концепт може да се надополни користејќи ја расчленувачката функција #concept. За да дознаете како да користите #concept, погледајте ја помошната страница [https://www.semantic-mediawiki.org/wiki/Help:Concepts Концепти].", "smw-search-syntax": "Синтакса", "smw-search-profile": "Напредно", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-find-forms": "обрасци на располагање", "smw-search-profile-extended-section-sort": "Подреди по", "smw-search-profile-extended-section-form": "Обрасци", "smw-search-profile-extended-section-namespace": "Именски простор", "smw-search-profile-extended-section-query": "Барање", "smw-search-show": "Прикажи", "smw-search-hide": "Скриј", "log-name-smw": "Дневник на Семантички МедијаВики", "log-show-hide-smw": "$1 дневник на Семантички МедијаВики", "logeventslist-smw-log": "Дневник на Семантички МедијаВики", "log-description-smw": "Активности за [https://www.semantic-mediawiki.org/wiki/Help:Logging овозможени типови на настани] пријавени од Семантички МедијаВики и неговите составни делови.", "smw-datavalue-import-unknown-namespace": "Увозниот именски простор „$1“ е непознат. Осигурајте се дека податоците за увоз во OWL се достапни преку [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Не можам да најдам URI „$1“ на именскиот простор во [[MediaWiki:Smw import $1|увозот на $1]].", "smw-datavalue-import-missing-type": "Не пронајдов типска определба за „$1“ во [[MediaWiki:Smw import $2|увозот на $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Увоз на $1]]", "smw-datavalue-import-invalid-value": "„$1“ не претставува важечки формат и се очекува да содржи „именски простор“:„назнака“ (на пр. „foaf:name“).", "smw-property-predefined-impo": "„$1“ е предодредено својство кое опишува поврзаност со [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary увезен лексикон] и е преземено од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички МедијаВики].", "smw-property-predefined-type": "„$1“ е предодредено својство што го опишува [[Special:Types|податочниот тип]] на едно својство и е преземено од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички Медијавики].", "smw-property-predefined-sobj": "„$1“ е предодредено својство што претставува [https://www.semantic-mediawiki.org/wiki/Help:Container пополнувачки] конструкт и е преземено од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички Медијавики].", "smw-property-predefined-errp": "„$1“ е предодредено својство за следење на грешките во внесувањето кај прибелешките со неправилни вредности и е преземено од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички Медијавики].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value „$1“] е предодредено својство што може да определи список на допуштени вредности за ограничување на вредносните доделувања за дадено својство и е преземено од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички Медијавики].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list „$1“] е предодредено својство што може да определи навод кон список на допуштени вредности за ограничување на вредносните доделувања за дадено својство и е преземено од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички Медијавики].", "smw-datavalue-property-restricted-annotation-use": "Својството „$1“ има ограничена област на примена и не може да се користи како својство на прибелешка од страна на некој корисник.", "smw-datavalue-property-restricted-declarative-use": "Својството „$1“ е декларативно и може да се користи само на страница на својство или категорија.", "smw-datavalue-restricted-use": "Податочната вредност „$1“ е означена за ограничена употреба.", "smw-datavalue-invalid-number": "Не можам да го протолкувам „$1“ како број.", "smw-query-condition-circular": "Во „$1“ е утврден можен кружен услов.", "smw-query-condition-empty": "Описот на барањето има празен услов.", "smw-types-list": "Список на податочни типови", "smw-types-default": "„$1“ е вграден податочен тип.", "smw-types-help": "Повеќе информации и примери ќе најдете на оваа [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 помошна страница].", "smw-type-anu": "„$1“ е варијанта на податочниот тип [[Special:Types/URL|URL]] и претежно се користи за извозната изјава ''owl:AnnotationProperty''.", "smw-type-boo": "„$1“ е основен податочен тип што опишува вредност точно/неточно.", "smw-type-cod": "„$1“ е варијанта на податочниот тип [[Special:Types/Text|текст]] што се корити во технички текстови со произволна големина, како страници со програмски кодови.", "smw-type-geo": "„$1“ е податочен тип што опишува географска местоположба и бара [https://www.semantic-mediawiki.org/wiki/Semantic_Maps Семантички карти].", "smw-type-tel": "„$1“ е посебен податочен тип што го опишува меѓународните телефонски броеви според RFC 3966.", "smw-type-txt": "„$1“ е основен податочен тип што опишува низи со произволна големина.", "smw-type-dat": "„$1“ е основен податочен тип што претставува моменти во времето во еднообразен формат.", "smw-type-ema": "„$1“ е посебен податочен тип што претставува е-пошта.", "smw-type-tem": "„$1“ е посебен податочен тип што претставува температура.", "smw-type-tab-properties": "Својства", "smw-type-tab-types": "Типови", "smw-type-tab-errors": "Грешки", "smw-type-primitive": "Основно", "smw-type-no-group": "Некласифицирани", "smw-property-predefined-errc": "„$1“ е предодредено својство кое се презема од [https://www.semantic-mediawiki.org/wiki/Special_properties Семантички Медијавики] и претставува грешки што се јавуваат во врска со несоодветни вредносни прибелешки или неправилна обработка на вносот.", "smw-property-predefined-errt": "„$1“ е предодредено својство што содржи текстуален опис на грешка и е преземено од [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантички МедијаВики].", "smw-subobject-parser-invalid-naming-scheme": "Кориснички-зададен подобјект користи неважечка шема за именување. Записот со точки ($1) во првите пет знаци е резервиран за додатоците. Можете да зададете [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier именувана назнака].", "smw-datavalue-record-invalid-property-declaration": "Определбата на записот го содржи својството „$1“, кое самото е зададено како тип на запис и не се допушта.", "smw-datavalue-languagecode-invalid": "„$1“ не е препознаен како важечки јазичен код.", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|секунда|секунди}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|секунда|секунди}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|секунда|секунди}}", "smw-datavalue-external-formatter-invalid-uri": "„$1“ е неважечка URL-адреса.", "smw-datavalue-parse-error": "Дадената вредност „$1“ не беше разбрана.", "smw-clipboard-copy-link": "Копирај врска во меѓускладот", "smw-no-data-available": "Нема податоци на располагање.", "smw-format-datatable-emptytable": "Нема расположливи податоци во табелата", "smw-format-datatable-info": "Се прикажуваат _START_ до _END_ од вкупно _TOTAL_ записи", "smw-format-datatable-infoempty": "Се прикажуваат 0 до 0 од вкупно 0 записи", "smw-format-datatable-infofiltered": "(исфилтрирано од вкупно _MAX_ записи)", "smw-format-datatable-lengthmenu": "Прикажи ставки на _MENU_", "smw-format-datatable-loadingrecords": "Вчитувам...", "smw-format-datatable-processing": "Обработувам...", "smw-format-datatable-search": "Пребарај:", "smw-format-datatable-zerorecords": "Не се пронајдени соодветни записи", "smw-format-datatable-first": "Прва", "smw-format-datatable-last": "Последна", "smw-format-datatable-next": "Следна", "smw-format-datatable-previous": "Претходна", "smw-format-datatable-sortascending": ": вклучете за да ги подредите столбовите нагорно", "smw-format-datatable-sortdescending": ": вклучете за да ги подредите столбовите надолно", "smw-format-datatable-toolbar-export": "Извези", "smw-api-invalid-parameters": "Неважечки параметри, „$1“", "smw-property-page-list-count": "{{PLURAL:$1|Прикажана е $1 страница која го користи ова својство|Прикажани се $1 страници кои го користат}} ова својство.", "smw-property-reserved-category": "Категорија", "smw-category": "Категорија", "smw-filter": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "smw-section-expand": "Расклопи го одделот", "smw-section-collapse": "Склопи го одделот", "smw-ask-format-help-link": "Формат [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "Помош", "smw-property-predefined-label-skey": "Клуч за подредување", "smw-processing": "Обработувам ...", "smw-expand": "Расклопи", "smw-collapse": "Собери", "smw-copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "smw-types-title": "Тип: $1", "smw-schema-error-title": "{{PLURAL:$1|Грешка|Грешки}} при проверката", "smw-schema-error-json": "Грешка на JSON: \"$1\"", "smw-schema-summary-title": "Краток опис", "smw-schema-title": "Шема", "smw-schema-usage": "Употреба", "smw-schema-type": "Тип на шема", "smw-schema-type-description": "Опис на типот", "smw-schema-description": "Опис на шемата", "smw-schema-tag": "{{PLURAL:$1|Ознака|Ознаки}}", "smw-property-tab-usage": "Употреба", "smw-property-tab-redirects": "Синоними", "smw-property-tab-subproperties": "Потсвојства", "smw-property-tab-errors": "Несвојствени доделувања", "smw-property-tab-specification": "... повеќе", "smw-concept-tab-list": "Список", "smw-concept-tab-errors": "Грешки", "smw-ask-tab-result": "Исход", "smw-ask-tab-debug": "Исправки", "smw-ask-tab-code": "<PERSON>од", "smw-report": "Извештај", "smw-legend": "Легенда", "smw-listingcontinuesabbrev": "продолжува", "smw-showingresults": "Подолу {{PLURAL:$1|е прикажана <strong>1</strong> ставка|се прикажани <strong>$1</strong> ставки}} почнувајќи од бр. <strong>$2</strong>."}