{"@metadata": {"authors": ["01miki10", "<PERSON><PERSON><PERSON>", "Beluga", "Cimon <PERSON>", "Crt", "Mediawikitranslator", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nike", "Pahkiqaz", "<PERSON><PERSON><PERSON><PERSON>", "Samoasambia", "<PERSON><PERSON><PERSON><PERSON>", "Str4nd", "Surjection", "Valtlai", "<PERSON><PERSON><PERSON>", "Veikk0.ma", "VezonThunder"]}, "smw-desc": "Lisää wikisi saavutettavuutta – niin koneille kuin ihmisille ([https://www.semantic-mediawiki.org/wiki/Help:User_manual verkko-opas])", "smw-error": "<PERSON><PERSON><PERSON>", "smw-upgrade-release": "<PERSON><PERSON><PERSON>", "smw-upgrade-progress": "<PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-error-title": "V<PERSON>he » Semantic MediaWiki", "smw-upgrade-error-why-title": "Miksi näen tämän sivun?", "smw-upgrade-error-how-title": "<PERSON>ten korjaan tämän virheen?", "smw-extensionload-error-how-title": "<PERSON>ten korjaan tämän virheen?", "smw-upgrade-maintenance-title": "Ylläpito » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Miksi näen tämän virheen?", "smw-semantics-not-enabled": "<PERSON><PERSON><PERSON>sen MediaWikin toiminnallisuutta ei ole otettu käyttöön tässä wikissä.", "smw_viewasrdf": "RDF-syöte", "smw_finallistconjunct": " ja", "smw-factbox-head": "... lisää sivusta \"$1\"", "smw-factbox-facts": "Faktat", "smw-factbox-attachments-is-local": "On pai<PERSON><PERSON>n", "smw_isspecprop": "<PERSON><PERSON><PERSON><PERSON> on erikoisominaisuus.", "smw-concept-cache-header": "<PERSON>ä<PERSON>ui<PERSON><PERSON>", "smw-concept-no-cache": "Ei välimuistia saatavilla.", "smw_concept_description": "Käsitteen ”$1” kuvaus", "smw_no_concept_namespace": "Konseptit voidaan määritellä vain Konsepit: -ni<PERSON><PERSON><PERSON><PERSON>n sivuilla.", "smw_multiple_concepts": "Ku<PERSON><PERSON> käsitesivulla voi olla vain yksi käsitteen määritelmä.", "smw_noinvannot": "Arvoja ei voi kohdistaa käänteisiin ominaisuuksiin.", "version-semantic": "<PERSON><PERSON><PERSON><PERSON>", "smw_baduri": "$1 ei ole sallittu URIn muoto.", "smw_printername_count": "Laskennan tulokset", "smw_printername_csv": "CSV-vienti", "smw_printername_dsv": "DSV-vienti", "smw_printername_embedded": "Upota sivujen si<PERSON>ä<PERSON>ö<PERSON>", "smw_printername_json": "JSON-vienti", "smw_printername_list": "Lista", "smw_printername_ol": "Numeroitu l<PERSON>", "smw_printername_ul": "Numeroimaton luettelo", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "Leveä taulukko", "smw_printername_template": "<PERSON><PERSON>", "smw_printername_templatefile": "Mallinetiedosto", "smw_printername_rdf": "RDF-vienti", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "<PERSON><PERSON><PERSON>", "smw-paramdesc-limit": "Palautettavien tulosten enimmäismäärän", "smw-paramdesc-offset": "Ensimmäisen näytettävän osuman järjestysnumero", "smw-paramdesc-link": "Näytä arvot linkkeinä", "smw-paramdesc-intro": "<PERSON><PERSON><PERSON>, joka n<PERSON>t<PERSON>n ennen kysely<PERSON> tulo<PERSON>, jos ni<PERSON><PERSON> on.", "smw-paramdesc-outro": "<PERSON><PERSON><PERSON>, joka n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kysely<PERSON> t<PERSON>ten jälk<PERSON>, jos ni<PERSON><PERSON> on.", "smw-paramdesc-default": "<PERSON><PERSON><PERSON>, j<PERSON>, jos kysely ei tuotta<PERSON> tulo<PERSON>.", "smw-paramdesc-sep": "Tulosten välinen erotin", "smw-paramdesc-showsep": "Näytä erotin CSV-tied<PERSON><PERSON> alussa (\"sep=<arvo>\")", "smw-paramdesc-template": "Tulosteiden näyttämisessä käytettävän mallineen nimi", "smw-paramdesc-columns": "Tulosnäkymän sarak<PERSON>iden lukumäärä", "smw-paramdesc-userparam": "<PERSON><PERSON><PERSON>, joka välitetää<PERSON> joka<PERSON><PERSON>, jos mall<PERSON> k<PERSON>n", "smw-paramdesc-introtemplate": "<PERSON>nen kyselytuloksia näytettävän mallineen nimi, jos tuloksia l<PERSON>.", "smw-paramdesc-outrotemplate": "Kyselytulosten jälkeen näytettävän mallineen nimi, jos tulo<PERSON><PERSON>.", "smw-paramdesc-embedonly": "Älä nä<PERSON>ä otsikoita", "smw-paramdesc-rdfsyntax": "Käytettävä RDF-syntaksi", "smw-paramdesc-csv-sep": "Määrittelee sarake-erottimen", "smw-paramdesc-dsv-separator": "Käytettävä erotin", "smw-paramdesc-dsv-filename": "DSV-<PERSON><PERSON><PERSON> nimi", "smw-paramdesc-filename": "Ulostulotied<PERSON><PERSON> nimi", "smw-paramdesc-sort": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jonka mukaan kys<PERSON>.", "smw-paramdesc-searchlabel": "<PERSON><PERSON> j<PERSON> kertova teksti", "smw-paramdesc-export": "<PERSON>ie valinta", "smw-paramdesc-source": "Vaihtoehtoinen kyselyn tie<PERSON>ähde", "smw-paramdesc-jsonsyntax": "Käytettävä JSON-syntaksi", "smw-printername-feed": "RSS- <PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-paramdesc-feedtype": "Syötteen tyyppi", "smw-paramdesc-feedtitle": "Syötteen otsikkoteksti", "smw-paramdesc-feeddescription": "Syötteen kuvausteksti", "smw-paramdesc-feedpagecontent": "<PERSON><PERSON><PERSON>, joka n<PERSON>ytetään s<PERSON>ötteessä", "smw-label-feed-description": "$1 $2-syöte", "smw_iq_disabled": "<PERSON><PERSON><PERSON><PERSON> haku<PERSON> on estetty tässä wikissä.", "smw_iq_moreresults": "… lisää tuloksia", "smw_parseerror": "Tarjottua arvoa ei ymmärretty.", "smw_decseparator": ",", "smw_kiloseparator": "&nbsp;", "smw_notitle": "Nimeä ”$1” ei voi käyttää sivun nimenä tässä wikissä.", "smw_noproperty": "Nimeä ”$1” ei voi käyttää ominaisuuden nimenä tässä wikissä.", "smw_wrong_namespace": "<PERSON><PERSON> ”$1” sivut sallitaan tässä.", "smw_manytypes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on määritelty useampi kuin yksi tyyppi.", "smw_emptystring": "Tyhjiä merkkijonoja ei hyväksytä.", "smw_notinenum": "”$1” ei ole ominaisuuden ”$3” [[Property:Allows value|sallittujen arvojen]] luettelossa ($2).", "smw_noboolean": "”$1” ei ole tunnistettavissa totuusarvoksi.", "smw_true_words": "kyll<PERSON>,k,y", "smw_false_words": "ei,e,n", "smw_nofloat": "”$1” ei ole luku.", "smw_infinite": "<PERSON><PERSON><PERSON> ”$1” kokoisia lukuja ei tueta.", "smw_unitnotallowed": "”$1” ei ole sallittu mittayksikkö tälle ominaisuudelle.", "smw_nounitsdeclared": "Tälle ominaisuudelle ei ole määritetty mittayksikköä.", "smw_novalues": "Arvoja ei ole m<PERSON>tty.", "smw_nodatetime": "Päiväystä ”$1” ei tunnistettu.", "smw_toomanyclosing": "Hakukyselyssä tuntuisi olevan liian monta termin ”$1” esiintymää.", "smw_noclosingbrackets": "<PERSON><PERSON><PERSON> k<PERSON><PERSON> ha<PERSON> ”<nowiki>[[</nowiki>” puuttuu sulkevat hakasulut ”]]”.", "smw_misplacedsymbol": "Symbolia ”$1” k<PERSON><PERSON><PERSON><PERSON>, johon se ei sovel<PERSON>.", "smw_unexpectedpart": "<PERSON><PERSON><PERSON><PERSON> ”$1” ei voitu tulkita.\nTulokset eivät ehkä vastaa odotuksia.", "smw_emptysubquery": "<PERSON><PERSON><PERSON> alikyselyllä ei ole kelvo<PERSON>ta eh<PERSON>a.", "smw_misplacedsubquery": "<PERSON><PERSON> alik<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> k<PERSON>, jossa alikysely ei ole sallittu.", "smw_valuesubquery": "Alikyselyjä ei tueta ominaisuuden ”$1” arvoille.", "smw_badqueryatom": "<PERSON><PERSON><PERSON><PERSON> \"<nowiki>[[...]]</nowiki>\" ei voitu tulkita.", "smw_propvalueproblem": "Ominaisuuden ”$1” arvoa ei voitu tulkita.", "smw_noqueryfeature": "<PERSON><PERSON><PERSON> kyselyn ehtoja ei tueta tässä wikissä ja siksi osa kyselystä jäi tekemättä ($1).", "smw_noconjunctions": "Kyselyjen JA-toimituksia ei tueta tässä wikissä ja siksi osa kyselystä jäi tekemättä ($1).", "smw_nodisjunctions": "Kyselyjen TAI-toimituksia ei tueta tässä wikissä ja siksi osa kyselystä jäi tekemättä ($1).", "smw_querytoolarge": "{{PLURAL:$2|Seuraavaa kyselyehtoa|Seuraavia kyselyehtoja}} ei voitu huomioida tämän wikin kyselyjen koko- tai syvyysraj<PERSON><PERSON>sen vuoksi: <code>$1</code>.", "smw_notemplategiven": "Parametri ”template” on pak<PERSON><PERSON>, jotta tämä kys<PERSON><PERSON><PERSON> toimisi.", "smw_db_sparqlqueryproblem": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ta ei voitu noutaa SPARQL-tietokannasta. Virhe voi olla väliaikainen tai osoitus tietokannan ohjelmistovirheestä.", "smw_db_sparqlqueryincomplete": "<PERSON><PERSON><PERSON><PERSON>n osoittautui liian vaikeaksi ja kysely keskey<PERSON>. Jotkut tulokset voivat puuttua. Voit yrittää uudelleen yksinkertaisemmalla kyselyllä mikäli vain mahdollista.", "smw_type_header": "Ominaisuudet jotka ovat tyypiltään $1", "smw_typearticlecount": "Näytetään $1 tätä tyyppiä {{PLURAL:$1|käyttävä ominaisuus|käyttävää ominaisuutta}}.", "smw_attribute_header": "Ominaisuutta ”$1” käyttävät sivut", "smw_attributearticlecount": "Näytetään $1 tätä ominaisuutta {{PLURAL:$1|käyttävä sivu|käyttävää sivua}}.", "smw-propertylist-subproperty-header": "Alao<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-propertylist-redirect-header": "Synonyymit", "smw-propertylist-count": "Näytetään $1 {{PLURAL:$1|liittyvä aihe|liittyvää aihetta}}.", "smw-propertylist-count-more-available": "Näytetään $1 liittyvää {{PLURAL:$1|aihe|aihetta}} (lisää on saatavilla).", "specialpages-group-smw_group": "Semantic MediaWiki", "specialpages-group-smw_group-maintenance": "Ylläpito", "specialpages-group-smw_group-properties-concepts-types": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, konseptit ja tyypit", "specialpages-group-smw_group-search": "<PERSON><PERSON><PERSON><PERSON> ja hakeminen", "exportrdf": "Vie sivut RDF-muodossa", "smw_exportrdf_docu": "Tämän sivun avulla voit noutaa sivun tiedot RDF-muodossa.\nSyötä vietävien sivujen otsikot alla olevaan tekstiruutuun, yksi ots<PERSON>ko <PERSON> rivillä.", "smw_exportrdf_recursive": "Tee vienti rekursiivisesti kaikista liittyvistä sivuista.\n<PERSON><PERSON><PERSON>, että tulos saattaa olla suuri.", "smw_exportrdf_backlinks": "<PERSON>ie <PERSON>, joil<PERSON> viitataan vietäville sivuille.\nGeneroi selailtavan RDF:n.", "smw_exportrdf_lastdate": "Älä vie sivuja, jotka eivät ole muuttuneet määrätyn a<PERSON> jälk<PERSON>.", "smw_exportrdf_submit": "Vie", "uriresolver": "URI-resolveri", "properties": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-categories": "Lu<PERSON><PERSON>", "smw_properties_docu": "Wikissä käytetään seuraavia ominaisuuksia.", "smw_property_template": "$1 on tyypiltään $2 ($3 {{PLURAL:$3|esiintymä|esiintymää}})", "smw_propertylackspage": "Jo<PERSON><PERSON>la ominaisu<PERSON>lla pitäisi olla ku<PERSON>u.", "smw_propertylackstype": "Ominaisuuden tyyppiä ei ole määritelty (oletetaan tyypiksi $1).", "smw_propertyhardlyused": "Tätä ominaisuutta ei juurikaan käytetä.", "smw-sp-property-searchform": "Näytä omina<PERSON>, jotka sisältävät:", "smw-special-property-searchform-options": "Asetukset", "smw-special-wantedproperties-filter-label": "Suodatin:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON> mit<PERSON>n", "smw-special-wantedproperties-filter-unapproved": "Hyväksymättömät", "concepts": "Konseptit", "smw-special-concept-header": "<PERSON><PERSON><PERSON> k<PERSON>sitteitä", "smw-special-concept-empty": "Käsitettä ei lö<PERSON>y.", "unusedproperties": "Käyttämättömät ominaisuudet", "smw-unusedproperties-docu": "Tämä sivu luettelee [https://www.semantic-mediawiki.org/wiki/Unused_properties käyttämättömiä ominaisuuksia], jotka on määritelty, vaikka mikään sivu ei käytä niitä. Erotellun näkymän löytää [[Special:Properties|kaikkien]] tai [[Special:WantedProperties|haluttujen ominaisuuksien]] erikoissivuilta.", "smw-unusedproperty-template": "$1 on tyypiltään $2", "wantedproperties": "<PERSON><PERSON><PERSON> omina<PERSON>t", "smw-wantedproperties-docu": "Tämä sivu sisältää [https://www.semantic-mediawiki.org/wiki/Wanted_properties halutut ominaisuudet] joita käytetään tässä wikissä, mutta joilla ei ole kuvaussivua. Eriytettyä näkymää varten, katso [[Special:Properties|kaikki]] tai [[Special:UnusedProperties|käyttämättömien ominaisuuksien]] toimintosivut.", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|esiintymä|esiintymää}})", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|käyttö|käyttöä}})", "smw_purge": "Päivitä", "smw-purge-failed": "Semantic MediaWiki yritti tyhjentää sivun välimuistista, mutta epäonnistui", "types": "Ty<PERSON>pit", "smw-special-types-no-such-type": "\"$1\" on tuntematon tai sitä ei ole määritetty kelvolliseksi tietotyypiksi.", "smw-statistics": "<PERSON><PERSON><PERSON><PERSON>", "smw-statistics-cached": "<PERSON><PERSON><PERSON><PERSON> (välimuistissa)", "smw-statistics-entities-total": "Entiteetit (yhteensä)", "smw-statistics-query-size": "<PERSON><PERSON><PERSON><PERSON>", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Käsite|Käsitteet}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Käsite|Käsitteet}}]]", "smw-statistics-delete-count-info": "Poistettavaksi merkityt entiteetit tulee hävittää säännöllisesti mukana toimitettujen ylläpitoskriptien avu<PERSON>.", "ask": "<PERSON><PERSON><PERSON><PERSON> haku", "smw_ask_sortby": "<PERSON><PERSON>r<PERSON><PERSON><PERSON> sa<PERSON> mukaan (valinnainen)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON>", "smw_ask_descorder": "<PERSON><PERSON><PERSON>", "smw-ask-order-rand": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Hae tuloksia", "smw_ask_editquery": "Muok<PERSON>a kyselyä", "smw_add_sortcondition": "[Li<PERSON><PERSON><PERSON> lajittel<PERSON>hto]", "smw-ask-sort-add-action": "Lisää la<PERSON>o", "smw_ask_hidequery": "<PERSON><PERSON><PERSON> k<PERSON> (tiivis näkymä)", "smw_ask_help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_queryhead": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_printhead": "Tulosteiden valinta", "smw_ask_printdesc": "(lisää yksi ominaisuuden nimi per rivi)", "smw_ask_format_as": "Muotoilu:", "smw_ask_defaultformat": "o<PERSON>us", "smw_ask_otheroptions": "<PERSON><PERSON> vali<PERSON>t", "smw-ask-otheroptions-info": "Tämän osion valinnat vaikuttavat tulostuslausekkeisiin. Parametrin kuvauksen saa näkyviin siirtämällä kohdistin nimen kohdalle.", "smw-ask-otheroptions-collapsed-info": "<PERSON><PERSON> kaikki vai<PERSON> plus-kuvak<PERSON>en avulla.", "smw_ask_show_embed": "Näytä upotettava koodi", "smw_ask_hide_embed": "<PERSON><PERSON><PERSON> upotettava koodi", "smw_ask_embed_instr": "<PERSON><PERSON> ha<PERSON>at lisätä tämän kyselyn wikisivulle, käytä alla olevaa koodia.", "smw-ask-delete": "Poista", "smw-ask-sorting": "Lajittelu", "smw-ask-options": "Asetukset", "smw-ask-options-sort": "Järjest<PERSON> valinnat", "smw-ask-format-options": "<PERSON><PERSON><PERSON> ja as<PERSON>", "smw-ask-parameters": "Parametrit", "smw-ask-search": "Hae", "smw-ask-debug": "Vianetsintä", "smw-ask-no-cache": "Poista kyselyn välimuisti käytöstä", "smw-ask-no-cache-desc": "Tulokset ilman kyselyn välimuistia", "smw-ask-result": "<PERSON><PERSON>", "smw-ask-empty": "Tyhjennä kaikki me<PERSON>", "smw-ask-download-link-desc": "Lataa kysytyt tulokset $1-muodossa", "smw-ask-format": "<PERSON><PERSON>", "smw-ask-format-selection-help": "<PERSON>je valitulla muodolla: $1", "smw-ask-condition-change-info": "<PERSON><PERSON><PERSON> on muutettu ja hakukoneen on suoritettava kysely uudelleen tuottaakseen tuloksia, jotka vastaavat uusia vaatimuksia.", "smw-ask-input-assistance": "Syöttöapu", "smw-ask-extra-other": "<PERSON><PERSON>", "searchbyproperty": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "processingerrorlist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constrainterrorlist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "missingredirectannotations": "Puuttuvat uudelleenohjausmerkinnät", "smw-missingredirects-list-intro": "Näytetään $1 {{PLURAL:$1|sivu, josta puuttuu uudelleenohjausmerkintä|sivua, joista puuttuvat uudelleenohjausmerkinnät}}", "smw_sbv_docu": "<PERSON>oit selata sivu<PERSON>, j<PERSON><PERSON> on tietty arvo tietylle ominaisuudelle.", "smw_sbv_novalue": "Syötä ominaisuuden arvo alle. Lista mahdollisista arvoista löytyy sivulta $1.", "smw_sbv_displayresultfuzzy": "Lista kaikista sivuista, joilla ominaisuuden $1 arvona on $2.\nKoska tuloksia on vain vähän, my<PERSON>s lähellä olevat arvot näytetään.", "smw_sbv_property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_sbv_value": "Arvo", "smw_sbv_submit": "Etsi tuloksia", "browse": "Selaa wikiä", "smw_browselink": "<PERSON><PERSON>n omina<PERSON>t", "smw_browse_article": "<PERSON><PERSON><PERSON><PERSON> sen sivun nimi, jonka o<PERSON><PERSON><PERSON><PERSON><PERSON> haluat selata.", "smw_browse_go": "<PERSON><PERSON><PERSON>", "smw_browse_show_incoming": "Näytä saapuvat ominaisuudet", "smw_browse_hide_incoming": "<PERSON><PERSON><PERSON> sa<PERSON>u<PERSON> omina<PERSON>t", "smw_browse_no_outgoing": "Tällä sivulla ei ole ominaisuuksia.", "smw_browse_no_incoming": "Mik<PERSON><PERSON><PERSON> ominaisuus ei viittaa tälle sivulle.", "smw-browse-show-group": "Näytä ryhmät", "smw-browse-hide-group": "<PERSON><PERSON><PERSON>", "smw_inverse_label_default": "$1 /", "pageproperty": "Sivun ominaisuuksien haku", "smw_pp_docu": "Syötä sivu ja ominaisuus, tai vain ominaisuus hakeaksesi kaikki määrätyt arvot.", "smw_pp_from": "Sivu:", "smw_pp_type": "Ominaisuus:", "smw_pp_submit": "Hae", "smw-prev": "edelliset {{PLURAL:$1|$1}}", "smw-next": "seuraavat {{PLURAL:$1|$1}}", "smw_result_prev": "<PERSON><PERSON><PERSON>", "smw_result_next": "<PERSON><PERSON><PERSON>", "smw_result_results": "Tulokset", "smw_result_noresults": "<PERSON><PERSON> haku<PERSON>.", "smwadmin": "Semantic MediaWikin kojelauta", "smw-admin-statistics-job-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-permission-missing": "<PERSON><PERSON><PERSON><PERSON> tälle sivulle on estetty puuttuvien käyttöoikeuksien vuoksi, kysy neuvoa [https://www.semantic-mediawiki.org/wiki/Help:Permissions oikeuksien] apusivulta saadaksesi lisätietoja tarvittavista asetuksista.", "smw_smwadmin_return": "<PERSON><PERSON>a kohteeseen $1", "smw_smwadmin_updatestarted": "<PERSON><PERSON><PERSON>sten tieto<PERSON>n pä<PERSON>sp<PERSON>ssi on käynnistetty.\nTietokantaan tallennetut tiedot kootaan uudellee tai korjataan tarpeen vaatiessa.\nVoit seurata päivityksen etenemistä tällä toimintosivulla.", "smw_smwadmin_updatenotstarted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on jo k<PERSON><PERSON>.\nUutta ei käynnistetä.", "smw_smwadmin_updatestopped": "<PERSON><PERSON><PERSON> nykyi<PERSON> päivitysprosessit on pysäytetty.", "smw_smwadmin_updatenotstopped": "<PERSON><PERSON> ha<PERSON> p<PERSON>äyttää käynnissä olev<PERSON> p<PERSON>, varmista päätöksesi aktivoimalla valintaruutu.", "smw-admin-docu": "Tämä toimintosivu auttaa <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWikin</a> as<PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON>, ylläpidossa ja käytössä sekä tarjoaa myös enemmän hallinnollisia funktioita ja tehtäviä, kuten myö<PERSON> tilast<PERSON>.\n\n\nMuista tehdä varmuuskopiot tärkeistä tiedoista ennen kuin aloitat ylläpitotoiminnot.", "smw-admin-environment": "Ohjelmistoympäristö", "smw-admin-db": "<PERSON>iet<PERSON><PERSON><PERSON> asennus", "smw-admin-dbdocu": "Semantic MediaWiki tarvitsee oman tietokantarakenteen (ja on itsenäinen MediaWikistä, joten se ei vaikuta muuhun MediaWiki-asennukseen), jotta semanttista tietoa voidaan tallentaa.\nAsennus voidaan suorittaa monta kertaa ilman että siitä ei aiheutuisi vahinkoa, mutta se on tehtävä vain kerran asennuksen tai päivityksen yhteydessä.", "smw-admin-dbbutton": "Alusta tai päivitä taulukot", "smw-admin-announce": "Julkista wikisi", "smw-admin-deprecation-notice-title-notice": "Poistuneet muutokset", "smw-admin-deprecation-notice-title-replacement": "Korvatut tai uudelleennimetyt asetukset", "smw-admin-deprecation-notice-title-removal": "Poistetut asetukset", "smw-smwadmin-refresh-title": "<PERSON><PERSON><PERSON><PERSON><PERSON> korja<PERSON> ja p<PERSON>s", "smw_smwadmin_datarefresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_smwadmin_datarefreshdocu": "Semantic MediaWikin data voidaan palauttaa wikin nykyisen sisällön avulla.\nTämä voi olla hyödyllistä, kun halutaan korjata tai päivittää dataa, jonka sis<PERSON>inen muoto on muuttunut jonkin ohjelmistopäivityksen takia.\nPäivitys etenee sivu sivulta eikä valmistu nopeasti.\nAlla näkyy päivityksen eteneminen ja mahdollistaa päivitysten käynnistämisen tai pysäyttämisen (paitsi jos sivuston ylläpitäjä on ottanut toiminnon pois käytöstä).", "smw_smwadmin_datarefreshprogress": "<strong>Päivitys on jo k<PERSON><PERSON>.</strong>\nOn normaalia, että päivitys etenee hitaasti, sillä tietoja päivitetään pienissä osissa silloin kuin wikin sivuja ladataan.\nJo<PERSON> haluat päivityksen valmistuvan <PERSON>, käytä MediaWikin ylläpitokomentosarjaa <code>runJobs.php</code> (k<PERSON>yt<PERSON> valitsinta <code>--maxjobs 1000</code> rajoittaaksesi yhdessä erässä tehtävien päivitysten määrää).\nNykyisen päivityksen arvioitu eteneminen:", "smw_smwadmin_datarefreshbutton": "<PERSON><PERSON><PERSON> uudistaminen", "smw_smwadmin_datarefreshstop": "Lopeta tämä pä<PERSON>s", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON><PERSON><PERSON>, o<PERSON> {{GENDER:$1|varma}}.", "smw-admin-support": "<PERSON><PERSON>", "smw-admin-installfile": "<PERSON><PERSON> <PERSON><PERSON> ka<PERSON> on ongel<PERSON>, lue ensin <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">INSTALL-tiedoston</a> oh<PERSON>t ja <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">asennussivu</a>.", "smw-admin-smwhomepage": "Semantic MediaWikin täydellinen käyttäjän opas löytyy osoitteesta <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Ohjelmistovirheet voi raportoida <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHubiin</a>.", "smw-admin-questions": "<PERSON><PERSON> on kysymyksiä tai eh<PERSON>, liity keskusteluun <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">Semanttisen MediaWikin käyttäjäfoorumilla</a>.", "smw-admin-statistics": "Tilastot", "smw-admin-supplementary-settings-title": "Määritykset ja asetukset", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-cache-title": "Välimuistitilastot", "smw-admin-supplementary-elastic-version-info": "Versio", "smw-admin-supplementary-elastic-settings-title": "Asetukset (indeksit)", "smw-admin-supplementary-elastic-mappings-summary": "Yhteenveto", "smw-admin-supplementary-elastic-statistics-title": "Tilastot", "smw-admin-supplementary-elastic-replication-files": "<PERSON><PERSON><PERSON><PERSON>", "smw-property-label-similarity-threshold": "<PERSON><PERSON><PERSON>:", "smw-property-label-similarity-noresult": "Valituilla asetuksilla ei lö<PERSON>yt yhtään tulosta.", "smw_adminlinks_datastructure": "Tietorakenne", "smw_adminlinks_displayingdata": "Tietojen n<PERSON>ö", "smw_adminlinks_inlinequerieshelp": "Upotettujen k<PERSON> ohje", "smw-createproperty-isproperty": "Se on tyypin $1 ominaisuus.", "smw-createproperty-allowedvals": "T<PERSON><PERSON><PERSON>n ominaisuuden  {{PLURAL:$1|sallittu arvo on|sallitut arvot ovat}}:", "smw-paramdesc-category-delim": "<PERSON><PERSON><PERSON>", "smw-paramdesc-category-template": "<PERSON><PERSON>, jolla koh<PERSON><PERSON> muo<PERSON>", "smw-paramdesc-category-userparam": "Parametri, jotka välitetään mallineeseen", "smw-info-par-message": "Näytettävä sanoma.", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "<PERSON><PERSON><PERSON><PERSON>", "prefs-extended-search-options": "Laajennettu haku", "prefs-ask-options": "<PERSON><PERSON><PERSON><PERSON> haku", "smw-prefs-ask-options-tooltip-display": "Näytä parametri työ<PERSON>uvihjeenä #ask [[Special:Ask|kys<PERSON>nrakent<PERSON>]] toim<PERSON><PERSON><PERSON><PERSON><PERSON>.", "smw-prefs-general-options-time-correction": "<PERSON><PERSON> kä<PERSON>öön aikakorjaus toimintosivuille käyttämällä paikallista [[Special:Preferences#mw-prefsection-rendering|aikaero]]as<PERSON>ta", "smw-prefs-general-options-disable-editpage-info": "Poista johdantoteksti käytöstä muokkaussivulla", "smw-prefs-general-options-disable-search-info": "Poista syntaksin tukitiedot käytöstä tavallisella hakusivulla", "smw-prefs-general-options-suggester-textinput": "<PERSON><PERSON> k<PERSON> s<PERSON>ttöap<PERSON> semanttisille entiteeteille", "smw-ui-tooltip-title-property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-quantity": "Yksikkömuunnos", "smw-ui-tooltip-title-info": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-service": "Palvelun linkit", "smw-ui-tooltip-title-warning": "Varo<PERSON><PERSON>", "smw-ui-tooltip-title-error": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Parametri", "smw-ui-tooltip-title-event": "Tapahtuma", "smw-ui-tooltip-title-note": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-legend": "Selite", "smw-ui-tooltip-title-reference": "Viite", "smw_unknowntype": "<PERSON>ä<PERSON>än ominaisuuden tyyppi \"$1\" on virheellinen", "smw_concept_header": "Käsitteen ”$1” sivut", "smw_conceptarticlecount": "Näytetään alla $1 {{PLURAL:$1|sivu|sivua}}", "right-smw-vieweditpageinfo": "<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Edit_help muokkausohjetta] (Semantic MediaWiki)", "group-smwadministrator": "Ylläpitäjät (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|ylläpitäjä (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:Ylläpitäjät (Semantic MediaWiki)", "group-smwcurator": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|k<PERSON><PERSON><PERSON> (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "action-smw-admin": "päästä Semantic MediaWikin hallintotehtäviin", "smw-sp-properties-header-label": "<PERSON><PERSON><PERSON> o<PERSON>", "smw-admin-idlookup-input": "Hae:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Yleiskatsaus", "smw-admin-configutation-tab-settings": "Asetukset", "smw-admin-configutation-tab-namespaces": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-maintenance-tab-scripts": "Ylläpitoskriptit", "smw-admin-maintenance-no-description": "<PERSON>i kuva<PERSON>a.", "smw-livepreview-loading": "<PERSON><PERSON><PERSON>…", "smw-sp-searchbyproperty-resultlist-header": "<PERSON><PERSON><PERSON> tulo<PERSON>", "smw-search-syntax": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-namespace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-query": "<PERSON><PERSON><PERSON>", "smw-search-show": "Näytä", "smw-search-hide": "<PERSON><PERSON><PERSON>", "log-name-smw": "Semantic MediaWiki -loki", "log-show-hide-smw": "$1 Semantic MediaWiki -loki", "logeventslist-smw-log": "Semantic MediaWiki -loki", "smw-datavalue-invalid-number": "Arvoa ”$1” ei voi tulkita luvuksi.", "smw-query-condition-empty": "<PERSON><PERSON><PERSON><PERSON> kuva<PERSON> sisältää tyhjän ehdon.", "smw-types-list": "<PERSON><PERSON><PERSON>", "smw-types-default": "”$1” on sisäänrakennettu tietotyyppi.", "smw-type-boo": "”$1” perustietotyyppi, joka kuvaa totuusarvoa.", "smw-type-txt": "”$1” on perustietotyyppi, joka kuvaa mielivaltaisen pitkiä merkkijonoja.", "smw-type-dat": "”$1” on perustietotyyppi, joka kuvaa ajanhetkiä yhtenäisessä muodossa.", "smw-type-extra-tem": "Muunnosskeema sisältää tuetut yksiköt kuten <PERSON>, <PERSON><PERSON><PERSON>, Fahrenheit ja <PERSON>.", "smw-type-tab-properties": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-type-tab-types": "Ty<PERSON>pit", "smw-type-tab-errors": "Virheet", "smw-type-primitive": "Perustyypit", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|sekunti|sekuntia}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekunti|sekuntia}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|sekunti|sekuntia}}", "smw-datavalue-allows-pattern-mismatch": "Arvo ”$1” luokiteltiin virheelliseksi säännöllisen lausekkeen ”$2” perusteella.", "smw-datavalue-external-formatter-invalid-uri": "”$1” on virheellinen URL-osoite.", "smw-clipboard-copy-link": "<PERSON><PERSON><PERSON> leikepöydälle", "smw-no-data-available": "Tietoja ei ole saata<PERSON>.", "smw-edit-protection-enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "smw-format-datatable-loadingrecords": "Ladataan...", "smw-format-datatable-processing": "K<PERSON><PERSON>llään...", "smw-format-datatable-search": "Haku:", "smw-format-datatable-first": "<PERSON><PERSON>mmä<PERSON>", "smw-format-datatable-last": "Viimeinen", "smw-format-datatable-next": "<PERSON><PERSON><PERSON>", "smw-format-datatable-previous": "<PERSON><PERSON><PERSON>", "smw-format-datatable-toolbar-export": "Vie", "smw-property-page-list-count": "Näytetään $1 tätä ominaisuutta {{PLURAL:$1|käyttävä sivu|käyttävää sivua}}.", "smw-property-reserved-category": "<PERSON><PERSON><PERSON>", "smw-category": "<PERSON><PERSON><PERSON>", "smw-browse-property-group-title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-filter": "<PERSON><PERSON><PERSON>", "smw-section-expand": "<PERSON><PERSON><PERSON><PERSON>", "smw-section-collapse": "Tiivistä osio", "smw-help": "<PERSON><PERSON>", "smw-property-predefined-label-skey": "Pi<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-processing": "K<PERSON><PERSON>llään...", "smw-types-title": "Tyyppi: $1", "smw-schema-error-title": "Vahvistus{{PLURAL:$1|virhe|virheet}}", "smw-schema-summary-title": "Yhteenveto", "smw-schema-type": "<PERSON><PERSON><PERSON> tyy<PERSON>i", "smw-ask-title-keyword-type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-property-tab-usage": "Käyttö", "smw-property-tab-redirects": "Synonyymit", "smw-concept-tab-list": "<PERSON><PERSON><PERSON>", "smw-concept-tab-errors": "Virheet", "smw-ask-tab-result": "<PERSON><PERSON>", "smw-ask-tab-extra": "Ekstra", "smw-ask-tab-code": "<PERSON><PERSON><PERSON>", "smw-legend": "Selite", "smw-listingcontinuesabbrev": "jatkuu", "smw-showingresults": "Alla on vain {{PLURAL:$1|<strong>1</strong> hakutulos|<strong>$1</strong> hakutulosta}} alkaen tuloksesta nro <strong>$2</strong>."}