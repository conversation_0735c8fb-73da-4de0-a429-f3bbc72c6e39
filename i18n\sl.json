{"@metadata": {"authors": ["Dbc334", "Eleassar", "HairyFotr", "Janezdrilc", "Lesko987", "<PERSON><PERSON>"]}, "smw-desc": "<PERSON><PERSON><PERSON> vaš viki dostopnejši – za stroje ''in'' ljudi ([https://www.semantic-mediawiki.org/wiki/Help:User_manual spletna dokumentacija])", "smw-error": "Napaka", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantični MediaWiki] je bil name<PERSON>en in omogočen, vendar manjka ustrezen [https://www.semantic-mediawiki.org/wiki/Help:Upgrade ključ za nadgradnjo].", "smw-upgrade-release": "Izdaja", "smw-upgrade-progress": "<PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-progress-explain": "<PERSON><PERSON><PERSON>, k<PERSON>j bo nad<PERSON> k<PERSON>č<PERSON>, je te<PERSON><PERSON>, saj je odvisna od velikosti podatkovne shrambe in razpoložljive strojne opreme in lahko za dokončanje večjih vikijev traja nekaj trenutkov.\n\nZa več informacij o napredku se obrnite na svojega lokalnega administratorja.", "smw-upgrade-progress-create-tables": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ali <PERSON>) tabel in indeksov&nbsp;...", "smw-upgrade-progress-post-creation": "Izvajanje opravil po ustvaritvi&nbsp...", "smw-upgrade-progress-table-optimization": "Optimiziranje tabel ...", "smw-upgrade-progress-supplement-jobs": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>nih opravil ...", "smw-upgrade-error-title": "Napaka » Semantični MediaWiki", "smw-upgrade-error-why-title": "<PERSON><PERSON><PERSON> vidim to stran?", "smw-upgrade-error-why-explain": "Notranja struktura podatkovne zbirke Semantičnega MediaWikija se je spremenila in zahteva nekaj prilagoditev, da bo popolnoma delovala. Razlogov je lahko več, vključno z naslednjim:\n* Dodane so bile dodatne trajne lastnosti (zahteva dodatno nastavitev tabel).\n* Nadgradnja vsebuje nekatere spremembe tabel ali indeksov, zaradi česar je pred dostopom do podatkov obvezno prestrezanje.\n* Spremembe v shranjevanju ali poizvedovalnem mehanizmu.", "smw-upgrade-error-how-title": "<PERSON><PERSON> to napako?", "smw-upgrade-error-how-explain-admin": "Administrator (ali ka<PERSON> koli o<PERSON>ba z administratorskimi pravicami) mora zagnati MediaWiki [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] ali vzdrževalni skript Semantičnega MediaWikija [https://www .semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php].", "smw-upgrade-error-how-explain-links": "Za dodatno pomoč si lahko ogledate tudi naslednje strani:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation Navodila za namestitev]\n* [https://www.semantic-mediawiki .org/wiki/Help:Installation/Troubleshooting Stran s pomočjo za odpravljanje težav]", "smw-extensionload-error-why-title": "<PERSON><PERSON><PERSON> vidim to stran?", "smw-extensionload-error-why-explain": "Razširitev <b>ni</b> bila naložena z <code>enableSemantics</code>, temveč je bila omogočena z drugimi sredstvi, na primer z neposredno uporabo <code>wfLoadExtension( 'SemanticMediaWiki')</code>.", "smw-extensionload-error-how-title": "<PERSON><PERSON> to napako?", "smw-extensionload-error-how-explain": "Da bi omogočili razširitev in se izognili težavam z deklaracijami imenskega prostora in čakajočimi konfiguracijami, je treba uporabiti <code>enableSemantics</code>, ki bo <PERSON><PERSON><PERSON>, da bodo zahtevane spremenljivke nastavljene pred nalaganjem razširitve prek <code>ExtensionRegistry</code>.\n\nZa nadaljnjo pomoč glejte stran pomoči [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics].", "smw-upgrade-maintenance-title": "Vzdrževanje » Semantični MediaWiki", "smw-upgrade-maintenance-why-title": "<PERSON><PERSON><PERSON> vidim to stran?", "smw-upgrade-maintenance-note": "Sistem se trenutno [https://www.semantic-mediawiki.org/wiki/Help:Upgrade nadgrajuje] z razširitvijo [https://www.semantic-mediawiki.org/ Semantični MediaWiki] skupaj z njenim repozitorijem podatkov. Prosimo vas za potrpežljivost, da se vzdrževanje konča in bo viki znova dostopen.", "smw-upgrade-maintenance-explain": "Razširitev poskuša čim bolj zmanjšati vpliv in čas nedelovanja tako, da večino svojih vzdrževalnih nalog preloži na obdobje po <code>update.php</code>, vendar je treba najprej dokončati nekatere spremembe, povezane s podatkovno zbirko, da se izognete nedoslednosti podatkov. Vključuje lahko:\n* Spreminjanje struktur tabel, kot je dodajanje novih ali spreminjanje obstoječih polj.\n* Spreminjanje ali dodajanje tabelarnih indeksov.\n* Izvajanje optimizacij tabel (ko je omogočeno).", "smw-semantics-not-enabled": "Možnost Semantični MediaWiki ni bila omogočena za ta viki.", "smw_viewasrdf": "Vir RDF", "smw_finallistconjunct": "in", "smw-factbox-head": "... več o »$1«", "smw-factbox-facts": "<PERSON><PERSON><PERSON><PERSON>", "smw-factbox-facts-help": "Prikazuje izjave in dejstva, ki jih je ustvaril uporabnik", "smw-factbox-attachments": "<PERSON><PERSON>onke", "smw-factbox-attachments-value-unknown": "N. p.", "smw-factbox-attachments-is-local": "Lokalno", "smw-factbox-attachments-help": "Prikazuje razpoložljive priponke", "smw-factbox-facts-derived": "<PERSON><PERSON><PERSON><PERSON>", "smw-factbox-facts-derived-help": "<PERSON><PERSON><PERSON><PERSON>, ki izhajajo iz pravil ali drugih tehnik sklepanja", "smw_isspecprop": "Ta lastnost je posebna lastnost v tem vikiju.", "smw-concept-cache-header": "Uporaba predpomnilnika", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count Predpomnilnik konceptov] vsebuje {{PLURAL:$1| '''eno''' entiteto|'''$1''' entiteti|'''$1''' entitete|'''$1''' entitet}} ($2).", "smw-concept-no-cache": "Predpomnilnik ni na voljo.", "smw_concept_description": "Opis zasnove »$1«", "smw_no_concept_namespace": "Koncepti so lahko opredeljeni samo na straneh v imenskem prostoru »Koncept:«", "smw_multiple_concepts": "Vsaka konceptna stran ima lahko samo eno definicijo koncepta.", "smw_concept_cache_miss": "Koncepta »$1« trenutno ni mogoče uporabiti, ker konfiguracija vikija zahteva, da se izračuna brez povezave.\nČe težava čez nekaj časa ne izgine, zaprosite administratorja spletnega mesta, da omogoči ta koncept.", "smw_noinvannot": "Vrednosti ni mogoče dodeliti nasprotnim lastnostim.", "version-semantic": "Semantična razširitev", "smw_baduri": "URI-ji v obliki »$1« niso dovoljeni.", "smw_csv_link": "CSV", "smw_json_link": "JSON", "smw_rdf_link": "RDF", "smw_printername_count": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_csv": "Izvoz v CSV", "smw_printername_dsv": "Izvoz DSV", "smw_printername_debug": "Razhr<PERSON><PERSON><PERSON><PERSON><PERSON> poi<PERSON> (za strokovnjake)", "smw_printername_embedded": "Vstavljena vsebina strani", "smw_printername_json": "Izvoz v JSON", "smw_printername_list": "Seznam", "smw_printername_plainlist": "Gol <PERSON><PERSON>nam", "smw_printername_ol": "Oštevilčen seznam", "smw_printername_ul": "Neoštevilčen seznam", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON><PERSON> tabela", "smw_printername_template": "Predloga", "smw_printername_templatefile": "Datoteka predloge", "smw_printername_rdf": "Izvoz RDF", "smw_printername_category": "Kategorija", "validator-type-class-SMWParamSource": "be<PERSON><PERSON>", "smw-paramdesc-limit": "Najve<PERSON><PERSON>", "smw-paramdesc-offset": "Odmik prvega zadetka", "smw-paramdesc-headers": "Prikaž<PERSON> g<PERSON>/nazive <PERSON>", "smw-paramdesc-mainlabel": "Oznaka za naslov glavne strani", "smw-paramdesc-link": "Prikaži vrednosti kot povezave", "smw-paramdesc-intro": "Besedilo za prikaz pred zadetki poizvedbe, če obstajajo", "smw-paramdesc-outro": "Besedilo za prikaz po rezultatih poizvedbe, če obstajajo", "smw-paramdesc-default": "<PERSON><PERSON><PERSON> za p<PERSON>, če poizvedba nima z<PERSON>kov", "smw-paramdesc-sep": "Ločilo med zadetki", "smw-paramdesc-propsep": "Ločilo med lastnostmi zadetka", "smw-paramdesc-valuesep": "Ločilo med vrednostmi za lastnost zadetka", "smw-paramdesc-showsep": "Prikaže ločilo na vrhu datoteke CSV (»sep=<value>«)", "smw-paramdesc-distribution": "<PERSON><PERSON> da prikažete vse vrednosti, preštejte njihove pojavitve in jih prikažite.", "smw-paramdesc-distributionsort": "Razvrsti porazdelitev vrednosti po številu pojavitev.", "smw-paramdesc-distributionlimit": "Omeji porazdelitev vrednosti na število samo nekaterih vrednosti.", "smw-paramdesc-aggregation": "<PERSON><PERSON><PERSON><PERSON>, na kaj naj se združevanje nanaša", "smw-paramdesc-template": "<PERSON><PERSON>, s katero naj bodo prikazani izpisi", "smw-paramdesc-columns": "Število stol<PERSON>cev za prikaz zadetkov", "smw-paramdesc-userparam": "<PERSON><PERSON><PERSON><PERSON>, posredovana v vsak k<PERSON> predl<PERSON>e, če je uporabljena predloga", "smw-paramdesc-class": "Dodatni razred CSS, ki ga nastavite za seznam", "smw-paramdesc-introtemplate": "<PERSON><PERSON> predloge za prikaz pred zadetki poizvedbe, če so na voljo.", "smw-paramdesc-outrotemplate": "<PERSON><PERSON> predloge za prikaz po zadetkih poizvedbe, če so na voljo.", "smw-paramdesc-embedformat": "Značka HTML, ki se uporablja za definiranje naslovov", "smw-paramdesc-embedonly": "Ne prikaži na<PERSON>lovov", "smw-paramdesc-table-class": "Dodatni razred CSS, ki ga nastavite za tabelo", "smw-paramdesc-table-transpose": "Prikaži glave tabel navpično in zadetke vodoravno", "smw-paramdesc-prefix": "Upravljanje prikaza imenskega prostora v izpisih", "smw-paramdesc-rdfsyntax": "Skladnja RDF, ki se uporablja", "smw-paramdesc-csv-sep": "Določ<PERSON> ločilo s<PERSON>", "smw-paramdesc-csv-valuesep": "Določa ločilo vrednosti", "smw-paramdesc-csv-merge": "Združi vrednosti vrstic in stolpcev z enakim identifikatorjem subjekta (tudi prvi stolpec)", "smw-paramdesc-csv-bom": "Na vrh izhodne datoteke doda BOM (znak za signaliziranje vrstnega reda bajtov)", "smw-paramdesc-dsv-separator": "Uporabljeno ločilo", "smw-paramdesc-dsv-filename": "<PERSON><PERSON> da<PERSON>", "smw-paramdesc-filename": "<PERSON><PERSON>e da<PERSON>ke", "smw-smwdoc-description": "<PERSON><PERSON><PERSON><PERSON> vs<PERSON> para<PERSON>, ki jih je mogoče uporabiti za navedeno obliko <PERSON>kov, sku<PERSON><PERSON> s privzetimi vrednostmi in opisi.", "smw-smwdoc-default-no-parameter-list": "Ta format rezultata ne zagotavlja parametrov, specifičnh za obliko.", "smw-smwdoc-par-format": "Format zadetkov za prikaz dokumentacije parametrov.", "smw-smwdoc-par-parameters": "Katere parametre prikazati. »specific« za dodane po formatu, »base« za razpoložljive v vseh formatih in »all« za oboje.", "smw-paramdesc-sort": "Lastnost za razvrstitev poizvedbe", "smw-paramdesc-order": "Vrstni red poizvedbenega razvrščanja", "smw-paramdesc-searchlabel": "Besedilo za nadaljevanje <PERSON>", "smw-paramdesc-named_args": "<PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON> predlogi.", "smw-paramdesc-template-arguments": "<PERSON><PERSON><PERSON>, kako se imenovani argumenti posredujejo v predlogo", "smw-paramdesc-import-annotation": "Med razčlenjevanjem strani se kopirajo dodatni označeni podatki", "smw-paramdesc-export": "Možnost izvoza", "smw-paramdesc-prettyprint": "<PERSON><PERSON>, ki prikazuje dodatne zamike in nove vrstice", "smw-paramdesc-json-unescape": "Izhod vsebuje neubežne poševnice in večbajtne znake Unicode", "smw-paramdesc-json-type": "Tip serializacije", "smw-paramdesc-source": "Alternativni vir poizvedbe", "smw-paramdesc-jsonsyntax": "Skladnja JSON, ki se uporablja", "smw-printername-feed": "Vir RSS in Atom", "smw-paramdesc-feedtype": "<PERSON>rsta vira", "smw-paramdesc-feedtitle": "<PERSON><PERSON><PERSON>, ki naj se uporabi kot naslov vira", "smw-paramdesc-feeddescription": "<PERSON><PERSON><PERSON>, ki naj se uporabi kot opis vira", "smw-paramdesc-feedpagecontent": "Z virom se prikazuje vsebina strani", "smw-label-feed-link": "RSS", "smw-label-feed-description": "Vir $1 $2", "smw-paramdesc-mimetype": "Vrsta predstavnosti (tip MIME) za izhodno datoteko", "smw_iq_disabled": "Za ta viki so bile semantične poizvedbe onemogočene.", "smw_iq_moreresults": "<PERSON> na<PERSON><PERSON><PERSON>", "smw_parseerror": "Predložena vrednost ni bila razumljena.", "smw_decseparator": ".", "smw_kiloseparator": ",", "smw_notitle": "V tem vikiju niza »$1« ni mogoče uporabiti za naslov strani.", "smw_noproperty": "V tem vikiju niza »$1« ni mogoče uporabiti kot ime lastnosti.", "smw_wrong_namespace": "Tu so dovoljene samo strani v imenskem prostoru »$1«.", "smw_manytypes": "<PERSON>a lastnost je definiran več kot en tip.", "smw_emptystring": "Prazni nizi niso spre<PERSON>jivi.", "smw_notinenum": "»$1« ni na seznamu ($2) [[Property:Allows value|dovoljenih vrednosti]] za lastnost »$3«.", "smw-datavalue-constraint-error-allows-value-list": "»$1« ni na seznamu ($2) [[Property:Allows value|dovoljenih vrednosti]] za lastnost »$3«.", "smw-datavalue-constraint-error-allows-value-range": "»$1« ni znotraj tega obsega »$2«, določenega z omejitvijo [[Property:Allows value|dovoljenih vrednosti]] za lastnost »$3«.", "smw-constraint-error": "Težava z omejitvijo", "smw-constraint-error-suggestions": "Preverite navedene kršitve in lastnosti skupaj z njihovimi označenimi vrednostmi, da z<PERSON>tov<PERSON>, da so izpolnjene vse zahteve glede omejitev.", "smw-constraint-error-limit": "Seznam bo vseboval največ $1 kršitev.", "smw_noboolean": "»$1« ni prepoznan kot logična (true/false) vrednost.", "smw_true_words": "true,t,yes,y,res<PERSON><PERSON><PERSON>,r,da,d", "smw_false_words": "false,f,no,n,ne<PERSON><PERSON><PERSON>,nr,ne", "smw_nofloat": "»$1« ni število.", "smw_infinite": "<PERSON><PERSON><PERSON><PERSON>, velike kot »$1«, niso podprte.", "smw_unitnotallowed": "»$1« ni dekla<PERSON>ran kot veljavna enota merjenja za to lastnost.", "smw_nounitsdeclared": "<PERSON><PERSON> to last<PERSON>t niso bile deklarirane enote merje<PERSON>.", "smw_novalues": "Določenih ni nobenih vrednosti.", "smw_nodatetime": "Datum »$1« ni bil razumljen.", "smw_toomanyclosing": "Videti je, da se v poizvedbi pojavlja preveč »$1«.", "smw_noclosingbrackets": "Nekatere uporabe »<nowiki>[[</nowiki>« v vaši poizvedbi niso bile zaprte z ujemajočim se »]]«.", "smw_misplacedsymbol": "Simbol »$1« je bil uporabljen na mestu, kjer ni uporaben.", "smw_unexpectedpart": "Del poizvedbe »$1« ni bil razumljen.\nZadetki mogoče ne bodo ustrezali pričakovanju.", "smw_emptysubquery": "Neka podpoizvedba ima neveljaven pogoj.", "smw_misplacedsubquery": "Neka podpoizvedba je bila uporabljena na mestu, kjer podpoizvedbe niso dovoljene.", "smw_valuesubquery": "Za vrednosti lastnosti »$1« podpoizvedbe niso podprte.", "smw_badqueryatom": "<PERSON><PERSON><PERSON> <PERSON> »<nowiki>[[...]]</nowiki>« poizvedbe ni bil razumljen.", "smw_propvalueproblem": "Vrednost lastnosti »$1« ni bila razumljena.", "smw_noqueryfeature": "Neka poizvedbena možnost v tem vikiju ni bila podprta in del poizvedbe je bil opuščen ($1).", "smw_noconjunctions": "Vezniki v poizvedbah v tem vikiju niso podprti in del poizvedbe je bil opuščen ($1).", "smw_nodisjunctions": "Disjunkcije v tem vikiju v poizvedbah niso podprte in del poizvedbe je bil opuščen ($1).", "smw_querytoolarge": "{{PLURAL:$2|Naslednjega poizvedbenega pogoja|Naslednjih $2 poizvedbenih pogojev}} ni bilo mogoče upoštevati zaradi omejitev tega vikija glede velikosti ali globine poizvedbe: <code>$1</code>.", "smw_notemplategiven": "Za delovanje te oblike poizvedbe podajte vrednost za parameter »template«.", "smw_db_sparqlqueryproblem": "Iz podatkovne zbirke SPARQL ni bilo mogoče pridobiti rezultata poizvedbe. Ta napaka je lahko zač<PERSON>na ali kaže na programsko napako v podatkovni zbirki.", "smw_db_sparqlqueryincomplete": "Razreševanje poizvedbe se je izkazalo za pretežko in je bilo prekinjeno. Nekateri zadetki morda manjkajo. Če je mogoče, poskusite namesto tega uporabiti preprostejšo poizvedbo.", "smw_type_header": "Lastnosti tipa »$1«", "smw_typearticlecount": "Prikazujem $1 {{PLURAL:$1|lastnost|lastnosti}}, ki uporablja ta tip.", "smw_attribute_header": "<PERSON><PERSON>, ki uporabljajo lastnost »$1«", "smw_attributearticlecount": "Prikazujem $1 {{PLURAL:$1|stran|strani}}, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to lastnost.", "smw-propertylist-subproperty-header": "Podlastnosti", "smw-propertylist-redirect-header": "<PERSON><PERSON><PERSON><PERSON>", "smw-propertylist-error-header": "Strani z nepravilnimi dodelitvami", "smw-propertylist-count": "Prikaz $1 {{PLURAL:$1|povezane entitete|povezanih entitet}}.", "smw-propertylist-count-with-restricted-note": "Prikaz $1 {{PLURAL:$1|povezane entitete|povezanih entitet}} (na voljo jih je več, vendar je prikaz omejen na »$2«).", "smw-propertylist-count-more-available": "Prikaz $1 {{PLURAL:$1|povezane entitete|povezanih entitet}} (na voljo jih je več).", "specialpages-group-smw_group": "Semantični MediaWiki", "specialpages-group-smw_group-maintenance": "Vzdrževanje", "specialpages-group-smw_group-properties-concepts-types": "Lastnosti, koncepti in tipi", "specialpages-group-smw_group-search": "Prebrskaj in poišči", "exportrdf": "Izvoz strani v RDF", "smw_exportrdf_docu": "Ta stran vam o<PERSON>, da dobite podatke iz strani v formatu RDF.\nZa izvoz strani vpišite njihove naslove v spodnjem oknu, po en naslov na vrstico.", "smw_exportrdf_recursive": "Rekurzivni izvoz vseh povezanih strani.\n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da je rezultat lahko obsežen!", "smw_exportrdf_backlinks": "Izvozi tudi vse strani ki se sklicujejo na strani izvoza.\nUstvari brskalni RDF.", "smw_exportrdf_lastdate": "Ne izvažaj<PERSON> strani, ki od danega trenutka niso bile spremenjene.", "smw_exportrdf_submit": "<PERSON><PERSON><PERSON><PERSON>", "uriresolver": "Razreševalnik URI-jev", "properties": "<PERSON><PERSON><PERSON>", "smw-categories": "Kategorije", "smw_properties_docu": "V tem vikiju se uporabljajo naslednje lastnosti.", "smw_property_template": "$1 tipa $2 ($3 {{PLURAL:$3|uporaba|uporabi|uporabe|uporab}})", "smw_property_template_notype": "$1 ($2)", "smw_propertylackspage": "Vse lastnosti mora opisovati ena stran!", "smw_propertylackstype": "Za to lastnost ni bil naveden noben tip (za zdaj se predpostavlja, da je tip $1).", "smw_propertyhardlyused": "Ta lastnost se v tem vikiju le malo uporablja!", "smw-property-name-invalid": "Lastnosti $1 ni mogoče uporabiti (neveljavno ime lastnosti).", "smw-property-name-reserved": "»$1« je bilo navedeno kot pridržano ime in se ne sme uporabljati kot lastnost. Naslednja [https://www.semantic-mediawiki.org/wiki/Help:Property_naming stran pomoči] lahko vsebuje informacije o tem, zakaj je to ime pridržano.", "smw-sp-property-searchform": "<PERSON><PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>:", "smw-sp-property-searchform-inputinfo": "Vnos razlikuje med velikimi in malimi črkami, in ko se uporablja za filtriranje, so prikazane samo <PERSON>, ki ustrezajo pogojem.", "smw-special-property-searchform": "<PERSON><PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON>:", "smw-special-property-searchform-inputinfo": "Vnos razlikuje med velikimi in malimi črkami, in ko se uporablja za filtriranje, so prikazane samo <PERSON>, ki ustrezajo pogojem.", "smw-special-property-searchform-options": "<PERSON><PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Neodobreno", "smw-special-wantedproperties-filter-unapproved-desc": "Možnost filtra, ki se uporablja v povezavi s pooblastitvenim načinom.", "concepts": "Koncepti", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Concepts Koncept] je mogoče obravnavati kot »dinamično kategorijo«, tj. kot zbirko strani, ki niso ustvarje<PERSON> ro<PERSON>, ampak jih izračuna Semantični MediaWiki iz opisa dane poizvedbe.", "smw-special-concept-header": "Sez<PERSON> konceptov", "smw-special-concept-count": "{{PLURAL:$1|Naslednji koncept je|Naslednja $1 koncepta sta|Naslednji koncepti $1 so|Naslednjih $1 konceptov je}} na seznamu.", "smw-special-concept-empty": "Najden ni bil noben koncept.", "unusedproperties": "Neuporab<PERSON><PERSON><PERSON>", "smw-unusedproperties-docu": "Na tej strani so navedene [https://www.semantic-mediawiki.org/wiki/Unused_properties neuporabljene lastnosti], ki so opredel<PERSON><PERSON>, čeprav jih ne uporablja nobena druga stran. Za razčlenjeni prikaz si oglejte posebne strani [[Special:Properties|vseh]] ali [[Special:WantedProperties|želenih lastnosti]].", "smw-unusedproperty-template": "$1 tipa $2", "wantedproperties": "<PERSON><PERSON><PERSON>", "smw-wantedproperties-docu": "Na tej strani so navedene [https://www.semantic-mediawiki.org/wiki/Wanted_properties želene lastnosti], ki se uporabljajo v vikiju, vendar nimajo opisne strani. Razčlenjeni prikaz si oglejte na posebnih straneh [[Special:Properties|vseh]] ali [[Special:UnusedProperties|neuporabljenih lastnosti]].", "smw-wantedproperty-template": "$1 ( $2 {{PLURAL:$2|uporaba|uporabi|uporabe|uporab}})", "smw-special-wantedproperties-docu": "Na tej strani so navedene [https://www.semantic-mediawiki.org/wiki/Wanted_properties želene lastnosti], ki se uporabljajo v vikiju, vendar nimajo opisne strani. Razčlenjeni prikaz si lahko ogledate na posebnih straneh [[Special:Properties|vseh]] ali [[Special:UnusedProperties|neuporabljenih lastnosti]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|uporaba|uporabi|uporabe|uporab}})", "smw_purge": "Osveži", "smw-purge-update-dependencies": "Semantični MediaWiki čisti trenutno stran zaradi nekaterih zastarelih odvisnosti, ki jih je odkril in zahtevajo posodobitev.", "smw-purge-failed": "Semantični MediaWiki je poskušal počistiti predpomnjenje strani, vendar ni uspelo", "types": "T<PERSON><PERSON>", "smw_types_docu": "Seznam [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes available podatkovnih tipov], kjer vsak [https://www.semantic-mediawiki.org/wiki/Help:Datatype tip] predstavlja izvirno množico atributov za opis vrednosti v smislu značilnosti hrambe in prikaza, ki so dedne dodeljeni lastnosti.", "smw-special-types-no-such-type": "»$1« ni znan ali pa ni bil določen kot veljaven podatkovni tip.", "smw-statistics": "Semantična statistika", "smw-statistics-cached": "Semantična statistika (predpomnjena)", "smw-statistics-entities-total": "Entitete (skupaj)", "smw-statistics-entities-total-info": "Ocenjeno <PERSON>tevilo vrstic entitet. <PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON>, koncepte ali katero koli drugo registrirano predstavitev objekta, ki zahteva dodelitev ID-ja.", "smw-statistics-property-instance": "Lastnost {{PLURAL:$1|vrednost|vrednosti}} (skupaj)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Lastnost|Lastnosti}}]] (skupaj)", "smw-statistics-property-total-info": "Skupno število registriranih lastnosti.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Lastnost|Lastnosti}} (skupaj)", "smw-statistics-property-used": "{{PLURAL:$1|Lastnost|Lastnosti}} (uporablja se z vsaj eno vrednostjo)", "smw-statistics-property-page": "{{PLURAL:$1|Lastnost|Lastnosti}} (registrirano na strani)", "smw-statistics-property-page-info": "<PERSON><PERSON><PERSON><PERSON>, ki imajo namensko stran in opis.", "smw-statistics-property-type": "{{PLURAL:$1|Lastnost|Lastnosti}} (do<PERSON><PERSON><PERSON> podatkovnemu tipu)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1| Poizvedba|Poizvedbe}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Poizvedba|Poizvedbe}}]] (vdelano, skupaj)", "smw-statistics-query-format": "Format <code>$1</code>", "smw-statistics-query-size": "Velikost poizvedbe", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Koncept|Koncepti}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Koncept|Koncepti}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Podobjekt|Podobjekti}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Podobjekt|Podobjekti}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Podatkovni tip|Podatkovni tipi}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Vrednost lastnosti|Vrednosti lastnosti}} ([[Special:ProcessingErrorList|{{PLURAL:$1|neustrezna opomba|neustrezne opombe}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Vrednost lastnosti|Vrednosti lastnosti}} ({{PLURAL:$1|neustrezna opomba|neustrezne opombe}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Zastarela entiteta|Zastarele entitete}}]", "smw-statistics-delete-count-info": "<PERSON><PERSON><PERSON>, ki so bile označene za odstranitev, je treba redno odstranjevati z uporabo priloženih vzdrževalnih skriptov.", "smw_uri_doc": "Razreševalec URI implementira [$1 W3C TAG najdbo na httpRange-14].\n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da je reprezentacija RDF (za stroje) ali stran viki (za ljudi) dostavljena glede na zahtevek.", "ask": "Semantič<PERSON>", "smw-ask-help": "Ta razdelek vsebuje nekaj povezav za pojasnitev uporabe skladnje <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecting pages] opisuje, kako izbrati strani in določiti pogoje\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Search operators] vsebuje seznam razpoložljivih iskalnih operatorjev, vključno z operatorji za razpone in nadomestne znake\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Displaying information] opisuje uporabo izpisnih izjav in možnosti oblikovanja", "smw_ask_sortby": "Razvrsti po stolpcu (izbirno)", "smw_ask_ascorder": "Naraščaj<PERSON>če", "smw_ask_descorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-order-rand": "Naključno", "smw_ask_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_editquery": "<PERSON><PERSON><PERSON> p<PERSON>", "smw_add_sortcondition": "[Dodaj pravilo razvrščanja]", "smw-ask-sort-add-action": "Dodaj pravilo razvrščanja", "smw_ask_hidequery": "Sk<PERSON>j <PERSON>oi<PERSON> (strnjeni prikaz)", "smw_ask_help": "Pomoč za poizvedbo", "smw_ask_queryhead": "Pogoj", "smw_ask_printhead": "Izbira izpisa", "smw_ask_printdesc": "(dodaj po eno lastnost v vrstico)", "smw_ask_format_as": "Oblik<PERSON>j kot:", "smw_ask_defaultformat": "privzeto", "smw_ask_otheroptions": "Druge možnosti", "smw-ask-otheroptions-info": "Ta razdelek vseb<PERSON><PERSON> m<PERSON>, ki spreminjajo izjave o izpisu. Opise parametrov si lahko ogledate tako, da nadnje premaknete miško.", "smw-ask-otheroptions-collapsed-info": "<PERSON>a ogled vseh razpoložljivih možnosti uporabite ikono plus", "smw_ask_show_embed": "Prikaži besedilo za vstavljanje", "smw_ask_hide_embed": "Skrij kodo za vstavljanje", "smw_ask_embed_instr": "Za vstavljanje te poizvedbe na viki stran uporabite naslednje besedilo.", "smw-ask-delete": "Odstrani", "smw-ask-sorting": "Razvrščanje", "smw-ask-options": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-options-sort": "Možnosti razvrščanja", "smw-ask-format-options": "Format in možnosti", "smw-ask-parameters": "Parametri", "smw-ask-search": "<PERSON><PERSON><PERSON>", "smw-ask-debug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-debug-desc": "Ustvari informacije o odpravljanju napak v poizvedbi", "smw-ask-no-cache": "Onemogoči predpomnjenje poizvedb", "smw-ask-no-cache-desc": "Rezultati brez predpomnjenja poizvedb", "smw-ask-result": "Rezultat", "smw-ask-empty": "Počisti vse vnose", "smw-ask-download-link-desc": "Prenesite poizvedovane zadetke v formatu $1", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Pomoč za izbrani format: $1", "smw-ask-condition-change-info": "Pogoj je bil spremenjen in iskalnik mora znova izvesti poizvedbo, da ustvari z<PERSON>tke, ki ustrezajo novim zahtevam.", "smw-ask-input-assistance": "Pomoč pri vnosu", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Pomoč pri vnosu] je na voljo za izpis, razvrščanje in polje pravila. Polje pravila zahteva uporabo ene od naslednjih predpon:", "smw-ask-condition-input-assistance-property": "<code>p:</code> za pridobitev predlogov lastnosti (npr. <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> za pridobitev predlogov kategorij", "smw-ask-condition-input-assistance-concept": "<code>con:</code> za pridobitev konceptnih predlogov", "smw-ask-format-change-info": "Oblika je bila spremenjena in treba je znova izvesti poizvedbo, da se ujema z novimi parametri in možnostmi vizualizacije.", "smw-ask-format-export-info": "Izbrani format je izvozni format, ki nima vizualne predstavitve, zato so zadetki na voljo samo kot prenos.", "smw-ask-query-search-info": "Na poizvedbo <code><nowiki>$1</nowiki></code> je odgovoril {{PLURAL:$3|1=<code>$2</code> (iz predpomnilnika)|<code>$2</code> (iz predpomnilnika)|<code>$2</code>}} v $4 {{PLURAL:$4|sekundi|sekundah}}.", "smw-ask-extra-query-log": "Dnevnik poizvedb", "smw-ask-extra-other": "Drugo", "searchbyproperty": "Is<PERSON><PERSON> po last<PERSON>", "processingerrorlist": "Seznam napak pri obdelavi", "constrainterrorlist": "<PERSON><PERSON><PERSON> nap<PERSON>", "propertylabelsimilarity": "Poročilo o podobnosti oznake lastnosti", "missingredirectannotations": "Manjkajoče označitve preusmeritev", "smw-processingerrorlist-intro": "Naslednji seznam ponuja pregled [https://www.semantic-mediawiki.org/wiki/Processing_errors napak pri obdelavi], ki so se pojavile v povezavi s [https://www.semantic-mediawiki.org/ Semantičnim MediaWikijem]. Priporočljivo je, da ta seznam redno spremljate in popravljate neveljavne opombe vrednosti.", "smw-processingerrorlist-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Processing_errors", "smw-constrainterrorlist-intro": "Naslednji seznam ponuja pregled [https://www.semantic-mediawiki.org/wiki/Constraint_errors napak pri omejitvah], ki so se pojavile v povezavi s [https://www.semantic-mediawiki.org/ Semantičnim MediaWikijem]. Priporočljivo je, da ta seznam redno spremljate in popravljate neveljavne opombe vrednosti.", "smw-constrainterrorlist-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Constraint_errors", "smw-missingredirects-intro": "V naslednjem razdelku bodo navedene strani, ki jim v Semantičnem MediaWikiju (v primerjavi z informacijami, shranjenimi v MediaWikiju) manjkajo označitve [https://www.semantic-mediawiki.org/wiki/Redirects preusmeritev], da lahko te označitve obnovite ročno z [https://www.semantic-mediawiki.org/wiki/Help:Purge osvežitvijo] strani ali zaženete vzdrževalni skript <code>rebuildData.php</code> (z možnostjo <code>--redirects</code>).", "smw-missingredirects-list": "Strani z manjkajočimi označitvami", "smw-missingredirects-list-intro": "Prikaz $1 {{PLURAL:$1|strani}} z manjkajočimi oznacitvami preusmeritev.", "smw-missingredirects-noresult": "Ni najdenih manjkajočih označitev preusmeritev.", "smw_sbv_docu": "<PERSON><PERSON><PERSON><PERSON> vse strani, ki imajo določeno lastnost in vrednost.", "smw_sbv_novalue": "Vnesite pravilno vrednost za lastnost ali pa si oglejte vse vrednosti lastnosti \"$1\".", "smw_sbv_displayresultfuzzy": "Seznam vseh strani, ki imajo last<PERSON> »$1« z vrednostjo »$2«.\nKer je bilo le malo <PERSON>, so prikazane strani s približnim ujemanjem.", "smw_sbv_property": "Lastnost:", "smw_sbv_value": "Vrednost:", "smw_sbv_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "browse": "Razišči viki", "smw_browselink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_browse_article": "Vpišite ime strani za začetek brskanja.", "smw_browse_go": "<PERSON><PERSON><PERSON>", "smw_browse_more": "...", "smw_browse_show_incoming": "Prikaži vhodne lastnosti", "smw_browse_hide_incoming": "Skrij vhodne lastnosti", "smw_browse_no_outgoing": "Ta stran nima lastnosti.", "smw_browse_no_incoming": "S to stranjo se ne povezuje nobena lastnost.", "smw-browse-from-backend": "Poteka pridobivanje podatkov iz zaledja.", "smw-browse-intro": "Na tej posebni strani si lahko ogledate podatke in informacije, shranjene na straneh ali v primerkih entitet. Vnesite ime strani, ki jo želite pregledati.", "smw-browse-invalid-subject": "Preverjanje predmeta je vrnilo napako »$1«.", "smw-browse-api-subject-serialization-invalid": "Podstran ima neveljaven format serializacije.", "smw-browse-js-disabled": "<PERSON><PERSON> <PERSON>, da je JavaScript onemogočen ali da ni na voljo. Priporočamo uporabo brskalnika, v katerem je podprt. Druge možnosti so obravnavane na [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>] strani o konfiguracijskih parametrih.", "smw-browse-show-group": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "smw-browse-hide-group": "<PERSON><PERSON><PERSON><PERSON>", "smw-noscript": "Za delovanje te strani ali dejanja potrebujete JavaScript. Prosimo, omogočite JavaScript v svojem brskalniku ali uporabite brskalnik, v katerem je podprt, da se lahko zagotovi želena funkcionalnost. Za dodatno pomoč glejte stran pomoči [https://www.semantic-mediawiki.org/wiki/Help:Noscript Noscript].", "smw_inverse_label_default": "$1 od", "smw_inverse_label_property": "Oznaka nasprotne <PERSON>", "pageproperty": "Iskanje v lastnostih strani", "pendingtasklist": "Seznam čakajočih nalog", "facetedsearch": "Fasetno iskanje", "smw_pp_docu": "<PERSON>a pridobitev vseh dodeljenih vrednosti vnesite stran in lastnost ali samo lastnost.", "smw_pp_from": "S strani:", "smw_pp_type": "Lastnost:", "smw_pp_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-prev": "{{PLURAL:$1|prejšnji|prejšnja $1|prejšnji $1|prejšnjih $1}}", "smw-next": "{{PLURAL:$1|naslednji|naslednja $1|naslednji $1|naslednjih $1}}", "smw_result_prev": "<PERSON><PERSON><PERSON>", "smw_result_next": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "<PERSON>.", "smwadmin": "Nadzorna plošča Semantičnega MediaWikija", "smw-admin-statistics-job-title": "Statistika opravil", "smw-admin-statistics-job-docu": "Statistika opravil prikazuje informacije o načrtovanih opravilih Semantičnega MediaWikija, ki še niso bila izvedena. Število opravil je lahko nekoliko netočno ali vsebuje neuspele poskuse. Za dodatne informacije glejte [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue priročnik].", "smw-admin-statistics-querycache-title": "Predpomnilnik poizvedb", "smw-admin-statistics-querycache-disabled": "[https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] v tem vikiju ni bil omogočen, zato ni na voljo nobena statistika.", "smw-admin-statistics-querycache-legend": "Statistika predpomnjenja naj bi vsebovala prehodne kumulativne in izpeljane podatke, vključno z naslednjimi:\n* »Zgrešitve« kot skupno število poskusov priklica podatkov iz predpomnjenja z nedosegljivimi odgovori, kar je zahtevalo neposreden priklic iz shrambe (podatkovna zbirka, shramba trojic idr.).\n* »Izbrisi« kot skupno število operacij izognitve predpomnjenju (bodisi s počiščenjem ali poizvedbeno odvisnostjo).\n* »Zadetki« podajajo število predpomnjenih priklicev bodisi za vdelane (poizvedbe, klicane iz viki strani) bodisi za nevdelane (če je omogočeno, zahtevki strani, kot so Special:Ask ali API) vire.\n* »medianRetrievalResponseTime« je orientacijska vrednost medianega časa odgovora (v sekundah) za predpomnjene priklicne zahtevke v času trajanja procesa zbiranja.\n* »noCache« označuje število zahtevkov brez poskusa (limit=0 poizvedb, možnost »no-cache« idr.) za priklic zadetkov iz predpomnjenja.", "smw-admin-statistics-section-explain": "V razdelku so na voljo dodatne statistike za administratorje.", "smw-admin-statistics-semanticdata-overview": "Pregled", "smw-admin-permission-missing": "Dostop do te strani je bil blokiran zaradi manjkajočih dovoljenj. Za podrobnosti o potrebnih nastavitvah si oglejte [https://www.semantic-mediawiki.org/wiki/Help:Permissions stran pomoči za dovoljenja].", "smw-admin-setupsuccess": "Skladiščni motor je nastavljen.", "smw_smwadmin_return": "Vrnitev na $1", "smw_smwadmin_updatestarted": "Začel se je nov postopek posodabljanja za osveževanje semantičnih podatkov.\nVsi shranjeni podatki bodo po potrebi ponovno zgrajeni ali popravljeni.\nNa tej posebni strani lahko spremljate napredek posodobitve.", "smw_smwadmin_updatenotstarted": "Postopek posodabljanja že poteka.\nNe ustvarjam drugega.", "smw_smwadmin_updatestopped": "Vsi obstoječi posodobitveni procesi so bili ustavljeni.", "smw_smwadmin_updatenotstopped": "Če želite ustaviti potekajoči postopek posodabljanja, morate aktivirati potrditveno polje, da označite, da ste res prepričani.", "smw-admin-docu": "Ta posebna stran vam pomaga pri namestitvi, nadgradnji, vzdrževan<PERSON> in uporabi <a href=\"https://www.semantic-mediawiki.org\">Semantičnega MediaWiki</a> in hkrati nudi dodatne administrativne funkcije in opravila, kot tudi statistiko.\nPred izvajanje administrativnih dejanj ustvarite varnostno kopijo uporabnih podatkov.", "smw-admin-environment": "<PERSON><PERSON>", "smw-admin-db": "Nastavitev podatkovne zbirke", "smw-admin-db-preparation": "Inicializacija tabele poteka in lahko traja nekaj trenutkov, preden se prikažejo zadetki glede na velikost in možne optimizacije tabele.", "smw-admin-dbdocu": "Semantični MediaWiki potrebuje lastno strukturo podatkovne zbirke (in je neodvisen od MediaWikija, zato ne vpliva na preostanek namestitve MediaWikija), da lahko shrani semantične podatke.\nTo nastavitveno možnost je mogoče izvesti večkrat, ne da bi pri tem povzročili kakršno koli škodo, vendar je pri namestitvi ali nadgradnji potreben samo enkrat.", "smw-admin-permissionswarn": "Če operacija ne uspe zaradi napak SQL, uporabnik podatkovne zbirke, ki ga uporablja vaš viki (preverite datoteko »LocalSettings.php«) verjetno nima zadostnih dovoljenj.\nTemu uporabniku dodelite dodatna dovoljenja za ustvarjanje in brisanje tabel, začasno vnesite korensko prijavo podatkovne zbirke v datoteko »LocalSettings.php« ali uporabite vzdrževalni skript <code>setupStore.php</code>, ki lahko uporablja administratorske poverilnice.", "smw-admin-dbbutton": "Inicializiraj ali nadgradi tabele", "smw-admin-announce": "Objava vikija", "smw-admin-announce-text": "Če je vaš viki javen, ga lahko registrirate na <a href=\"https://wikiapiary.com\">WikiApiary</a>, vikiju za sledenje vikijem.", "smw-admin-deprecation-notice-title": "Obvestila o opustitvah", "smw-admin-deprecation-notice-docu": "Naslednji razdelek vsebuje nastavitve, ki so bile opuščene ali odstranjene, vendar je bilo zaznano, da so aktivne v tem vikiju. Pričakuje se, da bo prihodnja izdaja odpravila podporo za te konfiguracije.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> je opuščen in bo v $2 odstranjen", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help: $1 $1]</code> bo odstranil (ali <PERSON>) {{PLURAL:$2|naslednjo možnost|naslednje možnosti}}:", "smw-admin-deprecation-notice-config-notice-option-list": "Parameter <code>$1</code> je opuščen in bo v $2 odstranjen", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> je bilo zamenja<PERSON> z <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>Parameter [https://www.semantic-mediawiki.org/wiki/Help: $1 $1]</code> je zamenjal parameter <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "{{PLURAL:$2|Možnost|Možnosti} za <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>}:", "smw-admin-deprecation-notice-config-replacement-option-list": "Parameter <code>$1</code> se nadomešča s parametrom <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>Parameter [https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> je bil v različici $2 odstranjen", "smw-admin-deprecation-notice-title-notice": "Opuščene nastavitve", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Opuščene nastavitve</b> prikazujejo nastavitve, za katere je bilo u<PERSON>ovl<PERSON>, da se bodo uporabljale v tem vikiju in so načrtovane za odstranitev ali spremembo v prihodnji izdaji.", "smw-admin-deprecation-notice-title-replacement": "Zamenjane ali preimenovane nastavitve", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Zamenjane ali preimenovane nastavitve</b> vsebujejo nastavitve, ki so bile preimenovane ali drugače spremenjene, zato je p<PERSON>ji<PERSON>, da njihovo ime ali format takoj posodobite.", "smw-admin-deprecation-notice-title-removal": "Odstranjene nastavitve", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Odstranjene nastavitve</b> navajajo nastavitve, ki so bile odstranjene v prejšnji izdaji, vendar je bilo u<PERSON>ovl<PERSON>no, da se v tem vikiju uporabljajo.", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw-smwadmin-refresh-title": "Popravilo in posodobitev podatkov", "smw_smwadmin_datarefresh": "Ponovna izgradnja podatkov", "smw_smwadmin_datarefreshdocu": "Na podlagi trenutne vsebine vikija je mogoče obnoviti vse podatke Semantičnega MediaWikija.\nTo je lahko koristno za popravilo poškodovanih podatkov ali osvežitev podatkov, če se je notranji format spremenil zaradi nadgradnje programske opreme.\nPosodobitev se izvaja stran za stranjo in ne bo dokončana takoj.\n<PERSON><PERSON><PERSON><PERSON> je prika<PERSON>, ali poteka posodobitev, in je na voljo možnost, da začnete ali ustavite posodobitve (razen če je to možnost onemogočil administrator spletnega mesta).", "smw_smwadmin_datarefreshprogress": "<strong>Poso<PERSON><PERSON>ev že poteka.</strong> \n<PERSON><PERSON> je, da posodobitev napreduje le počasi, saj osveži podatke samo v majhnih kosih vsakič, ko uporabnik dostopa do vikija.\nČe želite to posodobitev dokončati hitreje, lahko pokličete vzdrževalni skript MediaWiki <code>runJobs.php</code> (za omejitev števila izvedenih posodobitev v enem paketu uporabite možnost <code>--maxjobs 1000</code>).\nOcenjeni napredek trenutne posodobitve:", "smw_smwadmin_datarefreshbutton": "Načrtuj ponovno izgradnjo podatkov", "smw_smwadmin_datarefreshstop": "<PERSON><PERSON><PERSON> to poso<PERSON><PERSON><PERSON>", "smw_smwadmin_datarefreshstopconfirm": "Da, {{GENDER:$1|prepričan|prepričana|prepričan_a}} sem.", "smw-admin-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki", "smw-admin-job-scheduler-note": "Naloge (tiste, ki so omogočene) v tem razdelku se izvajajo po čakalni vrsti opravil, da se med njihovim izvajanjem preprečijo zastoji. Za obdelavo je odgovorna [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue čakalna vrsta opravil] in bistveno je, da ima vzdrževalni skript <code>runJobs.php</code> ustrezno zmogljivost (glejte tudi konfiguracijski parameter <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Odstranjevanje zastarelih entitet", "smw-admin-outdateddisposal-intro": "<PERSON><PERSON><PERSON><PERSON> de<PERSON> (sprememba vrste last<PERSON>, odstranitev vikistrani ali popravek vrednosti napake) bodo povzročile [https://www.semantic-mediawiki.org/wiki/Outdated_entities zastaranje entitet] in priporočljivo je, da jih občasno odstranite, da sprostite povezani prostor tabel.", "smw-admin-outdateddisposal-active": "Načrtovano je opravilo odstranjevanja zastarelih entitet.", "smw-admin-outdateddisposal-button": "Načrtuj odstranjevanje", "smw-admin-feature-disabled": "Ta možnost je v tem vikiju onemogočena. Prosimo, obiščite stran pomoči za <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">nastavitve</a> ali se obrnite na sistemskega administratorja.", "smw-admin-propertystatistics-title": "<PERSON><PERSON><PERSON> statist<PERSON>", "smw-admin-propertystatistics-intro": "Obnovi celotno statistiko uporabe lastnosti in v njej posodobi in popravi [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count število uporab] lastnosti.", "smw-admin-propertystatistics-active": "Načrtovano je opravilo obnove statistike <PERSON>.", "smw-admin-propertystatistics-button": "Načrtuj obnovo statistike", "smw-admin-fulltext-title": "<PERSON><PERSON>nova iskanja po celotnem besedilu", "smw-admin-fulltext-intro": "<PERSON>z tabel lastnosti znova zgradi iskalni indeks z omogočenim podatkovnim tipom »[https://www.semantic-mediawiki.org/wiki/Full-text iskanje po celotnem besedilu]«. Spremembe pravil indeksiranja (spremenjene ustavitvene besede, nov korenčnik idr.) in/ali na novo dodana ali spremenjena tabela zahtevajo ponovni zagon tega opravila.", "smw-admin-fulltext-active": "Načrtovano je opravilo obnove iskanja po celotnem besedilu.", "smw-admin-fulltext-button": "Načrtuj obnovo iskanja po celotnem besedilu", "smw-admin-support": "Pridobitev podpore", "smw-admin-supportdocu": "Za pomoč v primeru težav so na voljo različni viri:", "smw-admin-installfile": "Če imate težave z namestitvijo, najprej preverite navodila v <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">datoteki INSTALL</a> in na <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">strani o namestitvi</a>.", "smw-admin-smwhomepage": "Celotna uporabniška dokumentacija za Semantic MediaWiki je na voljo na naslovu <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Hrošče lahko prijavite na <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">sledilniku težav</a>; stran za <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">poročanje o napakah</a> nudi smernice za pisanje učinkovitega poročila o napaki.", "smw-admin-questions": "<PERSON>e imate dodatna vprašanja ali predloge, se pridružite razpravi na <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">poštnem seznamu uporabnikov</a> Semantičnega MediaWikija.", "smw-admin-other-functions": "Druge funkcije", "smw-admin-statistics-extra": "Statistične funkcije", "smw-admin-statistics": "Statistika", "smw-admin-supplementary-section-title": "Dodatne funkcije", "smw-admin-supplementary-section-subtitle": "Podprte osrednje funkcije", "smw-admin-supplementary-section-intro": "V tem razdelku so opisane dodatne funkcije, ki presegajo obseg vzdrž<PERSON><PERSON>h dejavnosti, in nekatere od njih (glejte dokumentacijo [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions]) so morda omejene ali nerazpoložljive in zato v tem vikiju nedostopne.", "smw-admin-supplementary-settings-title": "Konfiguracija in nastavitve", "smw-admin-supplementary-settings-intro": "<u>$1</u> prika<PERSON><PERSON> parametre, ki določajo vedenje Semantičnega MediaWikija", "smw-admin-main-title": "Semantični MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Statistika delovanja", "smw-admin-supplementary-operational-statistics-short-title": "statist<PERSON>", "smw-admin-supplementary-operational-statistics-intro": "Prikaže razširjen nabor <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Iskanje in odstranjevanje entitet", "smw-admin-supplementary-idlookup-short-title": "iskanje in odstranjevanje entitet", "smw-admin-supplementary-idlookup-intro": "Podpira preprosto <PERSON>jo <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Iskanje podvojenih entitet", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> za iskanje entite<PERSON>, ki so za izbrano matriko tabele kategorizirane kot dvojniki", "smw-admin-supplementary-duplookup-docu": "Na tej strani so navedeni vnosi iz izbranih tabel, ki so bile kategorizirane kot [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities dvojniki]. Podvojeni vnosi bi se morali (če sploh) pojavljati samo v redkih primerih, ki jih lahko povzroči prekinitev posodobitve ali neuspešna izvedba vrnitve.", "smw-admin-supplementary-duplookup-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities", "smw-admin-supplementary-operational-statistics-cache-title": "Statistika predpomnjenja", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> prikazuje izbran niz statisti<PERSON><PERSON><PERSON> podat<PERSON>, povezanih s predpomnjenjem", "smw-admin-supplementary-operational-table-statistics-title": "Statistika tabel", "smw-admin-supplementary-operational-table-statistics-short-title": "statist<PERSON> tabel", "smw-admin-supplementary-operational-table-statistics-intro": "Ustvari <u>$1</u> za izbran niz tabel", "smw-admin-supplementary-operational-table-statistics-explain": "Ta razdelek vsebuje izbrane statistične podatke o tabelah, ki administratorjem in skrbnikom podatkov pomagajo pri sprejemanju informiranih odločitev o tem stanju ozadja in mehanizma za shranjevanje.", "smw-admin-supplementary-operational-table-statistics-legend": "Legenda opisuje nekatere ključe, ki se uporabljajo za statistiko tabele, in vključuje:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> skupno število vrstic v tabeli", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> – <PERSON>adnji ID, ki je trenutno v uporabi;\n* <code>duplicate_count</code> – število dvojnikov, najdenih v tabeli id_table (glejte tudi [[Special:SemanticMediaWiki/duplicate-lookup|Iskanje podvojenih entitet]]);\n* <code>rows.rev_count</code> – šte<PERSON><PERSON> vrst<PERSON>, ki imajo dodeljen revision_id, ki označuje neposredno povezavo do vikistrani;\n* <code>rows.smw_namespace_group_by_count</code> – število združenih vrstic za imenske prostore, uporabljene v tabeli;\n* <code>rows.smw_proptable_hash.query_match_count</code> – število podobjektov poizvedbe z ustreznim sklicem na tabelo;\n* <code>rows.smw_proptable_hash.query_null_count</code> – število podobjektov poizvedbe brez sklica na tabelo (<PERSON><PERSON><PERSON>an, <PERSON><PERSON><PERSON><PERSON> sklic).", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> – odstotek izrazov, ki so edinstveni (nizka odstotna stopnja pomeni, da vs<PERSON><PERSON> in indeks tabel zapolnjujejo ponavljajoči se izrazi);\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> – <PERSON><PERSON><PERSON><PERSON>, ki se pojavijo samo enkrat;\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> – <PERSON><PERSON><PERSON><PERSON> i<PERSON>ov, ki se pojavijo večkrat.", "smw-admin-supplementary-elastic-title": "Elasticsearch", "smw-admin-supplementary-elastic-version-info": "Različica", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> prika<PERSON>je podrobnosti o nastavitvah in statistiko indeksa", "smw-admin-supplementary-elastic-docu": "Ta stran vsebuje informacije o nastavitvah, pres<PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON> in statistiki indeksov, povezan<PERSON> z gručo Elasticsearch, ki je povezana s Semantičnim MediaWikijem in njegovo shrambo [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Podprte funkcije", "smw-admin-supplementary-elastic-settings-title": "Nastavitve (indeksi)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u>, ki ga za upravljanje indeksov Semantičnega MediaWikija uporablja Elasticsearch", "smw-admin-supplementary-elastic-mappings-title": "Preslikave", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> za seznam indeksov in preslikav polj", "smw-admin-supplementary-elastic-mappings-docu": "Ta stran vsebuje podrobnosti preslikav polj, ki jih uporablja trenutni indeks. Priporočlji<PERSON> je, da spremljate preslikave v povezavi z <code>index.mapping.total_fields.limit</code> (določa največje dovoljeno število polj v indeksu).", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code> se nanaša na število indeksiranih osrednjih polj, <code>nested_fields</code> pa se nanaša na akumulirano število dodatnih polj, dodeljenih osrednjemu polju za podporo specifičnim strukturiranim vzorcem iskanja.", "smw-admin-supplementary-elastic-mappings-summary": "Povzetek", "smw-admin-supplementary-elastic-mappings-fields": "Preslikave polj", "smw-admin-supplementary-elastic-nodes-title": "<PERSON><PERSON><PERSON>šča", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> prikazuje statistiko vozlišč", "smw-admin-supplementary-elastic-indices-title": "Indeksi", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> <PERSON><PERSON><PERSON><PERSON><PERSON> pregled razpoložlji<PERSON>h indeksov in njihove statistike", "smw-admin-supplementary-elastic-statistics-title": "Statistika", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> prikazuje statistiko ravni indeksov", "smw-admin-supplementary-elastic-statistics-docu": "Ta stran ponuja vpogled v statistiko indeksov za različne operacije, ki se izvajajo na ravni indeksov, vrnjena statistika je združena s primarnimi in skupnimi združevanji. [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html stran pomoči] vsebuje podroben opis razpoložljive statistike indeksov.", "smw-admin-supplementary-elastic-status-replication": "Status podvajanja podatkov", "smw-admin-supplementary-elastic-status-last-active-replication": "Zadnja aktivna replikacija: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Interval osveževanja: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Zaostanek pri obnovitvenih opravilih: $1 (ocena)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Zaostanek opravil indeksiranja (datotek): $1 (ocena)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replikacija zaklenjena: $1 (poteka ponovna izgradnja)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Nadzor replikacije (aktivna): $1", "smw-admin-supplementary-elastic-replication-header-title": "Status replikacije", "smw-admin-supplementary-elastic-replication-function-title": "Replikacija", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> prikazuje informacije o neuspelih replikacijah", "smw-admin-supplementary-elastic-replication-docu": "Na tej strani so navedene informacije o [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring stanju replikacije] entitet, za katere so bile zabeležene težave z gručo Elasticsearch. Priporočljivo je, da navedene entitete pregledate in vsebino p<PERSON>č<PERSON>ite, da potrdite, da je bila težava samo prehodna.", "smw-admin-supplementary-elastic-replication-files-docu": "Up<PERSON>š<PERSON><PERSON><PERSON> je treba, da je treba za seznam datotek najprej izvesti opravilo [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion file ingest], ki mora dokončati obdelavo.", "smw-admin-supplementary-elastic-replication-files": "Datoteke", "smw-admin-supplementary-elastic-replication-pages": "Strani", "smw-admin-supplementary-elastic-endpoints": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-config": "Konfiguracije", "smw-admin-supplementary-elastic-no-connection": "Viki trenutno '''ne more''' vzpostaviti povezave z gručo Elasticsearch. Obrnite se na administratorja vikija, da razišče težavo, saj onemogoča indeksiranje in poizvedbeno sposobnost sistema.", "smw-list-count": "Seznam vsebuje $1 {{PLURAL:$1|vnos|vnosa|vnose|vnosov}}.", "smw-property-label-uniqueness": "Oznaka »$1« se ujema z vsaj eno drugo predstavitvijo lastnosti. Za rešitev težave glejte [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness stran pomoči].", "smw-property-label-similarity-title": "Poročilo o podobnosti oznake lastnosti", "smw-property-label-similarity-intro": "<u>$1</u> izračuna podobnosti za obstoječe oznake lastnosti", "smw-property-label-similarity-threshold": "Prag:", "smw-property-label-similarity-type": "ID vrste prikaza", "smw-property-label-similarity-noresult": "Za izbrane možnosti ni najdenih zadetkov.", "smw-property-label-similarity-docu": "Ta stran primerja [https://www.semantic-mediawiki.org/wiki/Property_similarity razdaljo podobnosti] (ne sme se zamenjati s semantično ali leksikalno podobnostjo) med oznakami lastnosti in poroča o njih, če presežejo prag. Poročilo lahko pomaga pri filtriranju napačno črkovanih ali enakovrednih lastnosti, ki predstavljajo isti koncept (za pomoč pri razjasnitvi koncepta in uporabe prijavljenih lastnosti glejte posebno stran [[Special:Properties|properties]]). Prag je mogoče prilagoditi za razširitev ali zožanje razdalje, uporabljene za približno ujemanje. <code>Za izvzetje lastnosti iz analize se uporablja [[Property:$1|$1]]</code>.", "smw-admin-operational-statistics": "Ta stran vsebuje operativne statistične podatke, zbrane v funkcijah ali iz funkcij, povezanih s Semantičnim MediaWikijem. Razširjen seznam statisti<PERSON><PERSON><PERSON> podatkov, specifi<PERSON><PERSON><PERSON> za viki, lahko najdet<PERSON> [[Special:Statistics|<b>tukaj</b>]].", "smw_adminlinks_datastructure": "Podatkovna struktura", "smw_adminlinks_displayingdata": "<PERSON><PERSON><PERSON>", "smw_adminlinks_inlinequerieshelp": "Pomoč za vrstične poizvedbe", "smw-page-indicator-usage-count": "<PERSON><PERSON><PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count število uporab]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|Uporabniško|Sistemsko}} definirana lastnost", "smw-property-indicator-last-count-update": "Ocenjeno <PERSON>tevilo uporab\nZadnja posodobitev: $1", "smw-concept-indicator-cache-update": "Število predpomnjenj\nZadnja posodobitev: $1", "smw-createproperty-isproperty": "To je lastnost tipa $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Dovoljena vrednost za to lastnost je|Dovoljene vrednosti za to lastnost so}}:", "smw-paramdesc-category-delim": "<PERSON><PERSON><PERSON>", "smw-paramdesc-category-template": "Predloga za oblikovanje predmetov", "smw-paramdesc-category-userparam": "Parameter za posredovanje predlogi", "smw-info-par-message": "Sporočilo za prikaz.", "smw-info-par-icon": "Ikona za prikaz, bodisi »info« ali »warning«.", "prefs-smw": "Semantični MediaWiki", "prefs-general-options": "Sp<PERSON>š<PERSON> m<PERSON>", "prefs-extended-search-options": "Razš<PERSON><PERSON><PERSON>", "prefs-ask-options": "Semantič<PERSON>", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantični MediaWiki] in povezane razširitve zagotavljajo posamezne prilagoditve za skupino izbranih možnosti in funkcij. Seznam posameznih nastavitev z njihovim opisom in značilnostmi je na voljo na naslednji [https://www.semantic-mediawiki.org/wiki/Help:User_preferences strani pomoči].", "smw-prefs-ask-options-tooltip-display": "Na posebni strani #ask [[Special:Ask|gradilnika poizvedb]] prika<PERSON>i besedilo parametra kot informativni opis orodja", "smw-prefs-ask-options-compact-view-basic": "Omogoči osnovni kompaktni pogled", "smw-prefs-help-ask-options-compact-view-basic": "Če je omogočeno, prikaže v kompaktnem pogledu Special:Ask zmanjšan nabor povezav", "smw-prefs-general-options-time-correction": "Omogoči časovni popravek za posebne strani z uporabo nastavitve lokalnega [[Special:Preferences#mw-prefsection-rendering|časovnega zamika]]", "smw-prefs-general-options-jobqueue-watchlist": "V moji osebni vrstici prikaži nadzorni seznam čakalne vrste opravil", "smw-prefs-help-general-options-jobqueue-watchlist": "<PERSON><PERSON> je o<PERSON>, p<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist seznam] čakajočih izbranih opravil skupaj z ocenjeno velikostjo čakalne vrste.", "smw-prefs-general-options-disable-editpage-info": "Onemogoči uvodno besedilo na strani za urejanje", "smw-prefs-general-options-disable-search-info": "Onemogoči podporne informacije o skladnji na standardni iskalni strani", "smw-prefs-general-options-suggester-textinput": "Pri semantičnih entitetah omogoči pomoč pri vnosu", "smw-prefs-help-general-options-suggester-textinput": "<PERSON><PERSON> je o<PERSON><PERSON><PERSON><PERSON>, omogoča uporabo [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance pomoči pri vnosu] za iskanje <PERSON>, kon<PERSON><PERSON><PERSON> in kategorij iz konteksta vnosa.", "smw-prefs-general-options-show-entity-issue-panel": "Prikaži panel s te<PERSON><PERSON><PERSON> entitete", "smw-prefs-help-general-options-show-entity-issue-panel": "<PERSON><PERSON> j<PERSON>, za<PERSON><PERSON> preverjanje celovitosti na vsaki strani in prikaže [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel panel s težavami entitete].", "smw-prefs-factedsearch-profile": "Izberite privzeti profil [[Special:FacetedSearch|fasetnega iskanja]]:", "smw-ui-tooltip-title-property": "Lastnost", "smw-ui-tooltip-title-quantity": "Pretvorba enot", "smw-ui-tooltip-title-info": "Informacije", "smw-ui-tooltip-title-service": "Povezave do storitev", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-error": "Napaka", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "Dogodek", "smw-ui-tooltip-title-note": "Opomba", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "Sklic", "smw_unknowntype": "Tip »$1« te lastnosti je neveljaven", "smw-concept-cache-text": "Koncept ima skupaj $1 {{PLURAL:$1|stran|strani}} in je bil zadnjič posodobljen dne $3 ob $2.", "smw_concept_header": "Strani zasnove »$1«", "smw_conceptarticlecount": "Spodaj {{PLURAL:$1|je prikazana $1 stran|sta prikazani $1 strani|so prikazane $1 strani|je prikazanih $1 strani}}.", "smw-qp-empty-data": "<PERSON>ah<PERSON>vanih podatkov ni bilo mogoče prikazati zaradi nekaterih nezadostnih kriterijev izbire.", "right-smw-admin": "Dostop do administrativnih opravil (Semantični MediaWiki)", "right-smw-patternedit": "Urejanje dostopa za ohranjanje dovoljenih regularnih izrazov in vzorcev (Semantični MediaWiki)", "right-smw-pageedit": "Urejanje dostopa do strani, označenih z <code>Is edit protected</code> (Semantični MediaWiki)", "right-smw-schemaedit": "<PERSON><PERSON>jan<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:<PERSON><PERSON>a strani sheme] (Semantični MediaWiki)", "right-smw-viewjobqueuewatchlist": "Dostop do [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist nadzornega seznama] vrste opravil (Semantični MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Dostop do podatkov o neujemanju redakcij, povezanih z entiteto (Semantični MediaWiki)", "right-smw-vieweditpageinfo": "Dostop do [https://www.semantic-mediawiki.org/wiki/Help:Edit_help pomoči za urejanje] (Semantični MediaWiki)", "restriction-level-smw-pageedit": "zaščiteno (samo pooblaščeni uporabniki)", "action-smw-patternedit": "urejan<PERSON><PERSON>, ki jih uporablja Semantični MediaWiki", "action-smw-pageedit": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> z <code>Is edit protected</code> (Semantični MediaWiki)", "group-smwadministrator": "Administratorji (Semantični MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrator (Semantični MediaWiki)|administratorka (Semantični MediaWiki)|administrator_ka (Semantični MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:<PERSON><PERSON> (Semantični MediaWiki)", "group-smwcurator": "Kustosi (Semantični MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|kustos (Semantični MediaWiki)|kustosinja (Semantični MediaWiki)|kustos_inja (Semantični MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON> (Semantični MediaWiki)", "group-smweditor": "Urejevalci (Semantični MediaWiki)", "group-smweditor-member": "{{GENDER:$1|urejevalec (Semantični MediaWiki)|urejevalka (Semantični MediaWiki)|urejevalec_ka (Semantični MediaWiki)}}", "grouppage-smweditor": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantični MediaWiki)", "action-smw-admin": "dostop do administratorskih opravil Semantičnega MediaWikija", "action-smw-ruleedit": "urejanje strani pravil (Semantični MediaWiki)", "smw-property-namespace-disabled": "Lastnost [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks namespace] je one<PERSON><PERSON><PERSON><PERSON>, zato poskus deklaracije vrste ali drugih lastnosti, specif<PERSON><PERSON><PERSON><PERSON> za to lastnost, ni mogoč.", "smw-property-predefined-default": "»$1« je vnaprej določena lastnost tipa $2.", "smw-property-predefined-common": "Ta lastnost je vnaprej uvedena (znana tudi kot [https://www.semantic-mediawiki.org/wiki/Help:Special_properties posebna lastnost]) in prihaja z dodatnimi administratorskimi pravicami, vendar jo je mogoče uporabljati tako kot vse druge [https:/ /www.semantic-mediawiki.org/wiki/Lastnost uporabniško določene lastnosti].", "smw-property-predefined-ask": "»$1« je vnaprej določena lastnost, ki predstavlja metainformacije (v obliki [https://www.semantic-mediawiki.org/wiki/Subobject podobjekta]) o posameznih poizvedbah in jo zagotavlja [https://www.semantic -mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-asksi": "»$1« je vnaprej določena lastnost, ki zbira število pogojev, uporabljenih v poizvedbi, in jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-askde": "»$1« je vnaprej določena lastnost za dodajanje povezav do storitev lastnostim in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-askde": "To je številska vrednost, izračunana na podlagi gnezdenja podpoizvedb, verig <PERSON> in razpoložljivih opisnih elementov, pri <PERSON>emer je izvajanje poizvedb omejeno s konfiguracijskim parametrom <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "»$1« je vnaprej določena lastnost za opisovanje parametrov, ki vplivajo na rezultat poizvedbe, ki jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-askpa": "To je del zbirke last<PERSON>, ki do<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler profil poizved<PERSON>].", "smw-sp-properties-docu": "Na tej strani so navedene [https://www.semantic-mediawiki.org/wiki/Property lastnosti], ki so na voljo za ta viki, in število njihovih uporab. Za posodobljene statistične podatke o štetju je priporočljivo, da se redno izvaja skript za vzdrževanje [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics statistike lastnosti]. Za drugačen pogled glejte posebno stran [[Special:UnusedProperties|Neuporabljene lastnosti]] ali [[Special:WantedProperties|<PERSON><PERSON>ne lastnosti]].", "smw-sp-properties-cache-info": "Navedeni podatki so bili pridobljeni iz [https://www.semantic-mediawiki.org/wiki/Caching predpomnilnika] in nazadnje posodobljeni $1.", "smw-sp-properties-header-label": "<PERSON>z<PERSON>", "smw-admin-settings-docu": "Ta stran prikazuje seznam vseh privzetih in lokaliziranih nastavitev, ki so pomembne za okolje Semantičnega MediaWikija. Za podrobnosti o posameznih nastavitvah glejte stran pomoči [https://www.semantic-mediawiki.org/wiki/Help:Configuration Configuration].", "smw-sp-admin-settings-button": "Ustvari seznam nastavitev", "smw-admin-idlookup-title": "<PERSON><PERSON><PERSON>", "smw-admin-idlookup-docu": "Ta razdelek prikazuje tehnične podrobnosti o posamezni entiteti (vikistran, podobjekt, lastnost idr.) v Semantičnem MediaWikiju. Vnos je lahko številski ID ali vrednost niza, ki se ujema z ustreznim iskalnim poljem, vendar se vsak sklic ID-ja nanaša na Semantični MediaWiki in ne na ID strani ali redakcije MediaWiki.", "smw-admin-iddispose-title": "Odstranjevanje", "smw-admin-iddispose-docu": "Upoštevati je treba, da je operacija odstranjevanja neomejena in bo entiteto odstranila iz mehanizma za shranjevanje skupaj z vsemi njenimi sklici v čakajočih tabelah, če bo potrjena. Prosimo, da to opravilo izvedete '''previdno''' in šele potem, ko ste preuč<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal dokumentacijo].", "smw-admin-iddispose-done": "ID »$1« je odstranjen iz shranjevalnega zaledja.", "smw-admin-iddispose-references": "ID »$1« {{PLURAL:$2|nima nobenega aktivnega sklica|ima vsaj en aktivni sklic}}:", "smw-admin-iddispose-references-multiple": "Seznam zadetkov z vsaj enim zapisom o aktivnem sklicu.", "smw-admin-iddispose-no-references": "Pri iskanju »$1« ni bil v tabeli najden noben vnos.", "smw-admin-idlookup-input": "Iskanje:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Pregled", "smw-admin-tab-notices": "Obvestila o opustitvah", "smw-admin-tab-maintenance": "Vzdrževanje", "smw-admin-tab-supplement": "Dodatne funkcije", "smw-admin-tab-registry": "Register", "smw-admin-tab-alerts": "Opomniki", "smw-admin-alerts-tab-deprecationnotices": "Obvestila o opustitvah", "smw-admin-alerts-tab-maintenancealerts": "Opomniki vzdrževanja", "smw-admin-alerts-section-intro": "V tem razdelku so prikazani opomniki in obvestila v zvezi z nastavitvami, operacijami in drugimi dejavnostmi, za katere je bilo prepo<PERSON>, da zahtevajo pozornost administratorja ali uporabnika z ustreznimi pravicami.", "smw-admin-maintenancealerts-section-intro": "Naslednje opomnike in obvestila je treba razrešiti in čeprav niso klju<PERSON>na, se pričakuje, da bo to pomagalo izboljšati vzdrževanje in delovanje sistema.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Optimizacija tabel", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Sistem je ugotovil, da je bila zadnja [https://www.semantic-mediawiki.org/wiki/Table_optimization optimizacija tabele] izvedena pred $2 dnevi(-om/-oma) (zapis z datumom $1), kar presega prag vzdrževanja $3 dni. Kot je navedeno v dokumentaciji, bo izvajanje optimizacij načrtovalniku poizvedb omogočilo sprejemanje boljših odločitev o poizvedbah, zato je predlagano, da tabelo redno optimizirate.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Zastarele entitete", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Sistem je naštel $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities zastarelih entitet] in dosegel kritično raven nevzdr<PERSON>evanja, ko je presežen prag $2. P<PERSON>oročamo, da zaženete vzdrževalni skript [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>].", "smw-admin-maintenancealerts-invalidentities-alert-title": "Neveljavne entitete", "smw-admin-maintenancealerts-invalidentities-alert": "Sistem je v [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace nevzdrževanem imenskem prostoru] našel $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|neveljavno entiteto|neveljavni entiteti|neveljavne entitete|neveljavnih entitet}}]. Priporočljivo je, da zaženete vzdrževalni skript [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] ali [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>].", "smw-admin-deprecation-notice-section": "Semantični MediaWiki", "smw-admin-configutation-tab-settings": "Nastavitve", "smw-admin-configutation-tab-namespaces": "<PERSON><PERSON>ski prostori", "smw-admin-configutation-tab-schematypes": "<PERSON><PERSON>i shem", "smw-admin-maintenance-tab-tasks": "Opravila", "smw-admin-maintenance-tab-scripts": "Vzdrževalni skripti", "smw-admin-maintenance-no-description": "Ni opisa.", "smw-admin-maintenance-script-section-title": "Seznam razpoložljivih vzdrževalnih skriptov", "smw-admin-maintenance-script-section-intro": "Za izvajanje naslednjih vzdrževalnih skriptov so potrebni administratorska pooblastila in dostop do ukazne vrstice.", "smw-admin-maintenance-script-description-dumprdf": "Izvoz obstoječih trojic RDF.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Ta skript se uporablja za upravljanje konceptnih predpomnilnikov v Semantičnem MediaWikiju,ki omogoča ustvarjanje, odstranjevanje in posodabljanje izbranih predpomnilnikov.", "smw-admin-maintenance-script-description-rebuilddata": "Znova ustvari vse semantične podatke v podatkovni zbirki tako, da preleti vse strani, ki bi lahko vsebovale semantične podatke.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Ponovno zgradi indeks Elasticsearch (samo za namestitve, ki uporabljajo <code>ElasticStore</code>), tako da preide skozi vse entitete, ki imajo semanti<PERSON> podatke.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Poišče manjkajoče entitete v Elasticsearch (samo za namestitve, ki uporabljajo <code>ElasticStore</code>) in razporedi ustrezna posodabljanja.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Ponovno zgradi indeks iskanja po celotnem besedilu <code>SQLStore</code> (za namestitve, pri katerih je bila ta nastavitev omogočena).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Ponovno zgradi statistiko uporabe za vse entitete lastnosti.", "smw-admin-maintenance-script-description-removeduplicateentities": "Odstrani podvojene entitete, najdene v izbranih tabelah, ki nimajo aktivnih sklicev.", "smw-admin-maintenance-script-description-setupstore": "Nastavi zaledje za shranjevanje in poizvedbo, kot je določeno v <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Posodobi polje <code>smw_sort</code> v <code>SQLStore</code> (v skladu z nastavitvijo [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "Izpolni polje <code>smw_hash</code> v<PERSON><PERSON> z manjkajočo vrednostjo.", "smw-admin-maintenance-script-description-purgeentitycache": "Počisti vnose v predpomnilnik za znane entitete in njihove povezane podatke.", "smw-admin-maintenance-script-description-updatequerydependencies": "Posodobi poizvedbe in poizvedbene odvisnosti (glejte nastavitev [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Odstrani zastarele entitete in povezave na poizvedbe.", "smw-admin-maintenance-script-description-runimport": "Na<PERSON>ni in uvozi samodejno odkrito vsebino iz [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Posodobitveni skripti", "smw-admin-maintenance-script-section-rebuild": "Obnovitveni skripti", "smw-livepreview-loading": "Nalaganje ...", "smw-sp-searchbyproperty-description": "Ta stran vsebuje preprost [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces brskalni vmesnik] za iskanje entitet, ki jih opisujeta lastnost in navedena vrednost. Drugi razpoložljivi iskalni vmesniki vključujejo [[Special:PageProperty|iskanje v lastnostih strani]] in [[Special:Ask|gradilnik poizvedb Semantično iskanje]].", "smw-sp-searchbyproperty-resultlist-header": "Sez<PERSON> zadetkov", "smw-sp-searchbyproperty-nonvaluequery": "Seznam vrednosti, ki imajo dodeljeno lastnost »$1«.", "smw-sp-searchbyproperty-valuequery": "Seznam strani, ki imajo lastnost »$1« z označeno vrednostjo »$2«.", "smw-datavalue-number-textnotallowed": "»$1« ni mogoče dodeliti deklariraneu številskemu tipu z vrednostjo $2.", "smw-datavalue-number-nullnotallowed": "Vrnitev »$1« z vrednostjo »NULL«, ki ni dovoljena kot število.", "smw-editpage-annotation-enabled": "Ta stran podpira semantične označitve v besedilu (npr<nowiki> »[[Is specified as::World Heritage Site]]«</nowiki>) za ustvarjanje strukturirane in poizvedljive vsebine, ki jo zagotavlja Semantični MediaWiki. Za izčrpen opis uporabe označevanja ali funkcije razčlenjevalnika #ask glejte strani pomoči [https://www.semantic-mediawiki.org/wiki/Help:Getting_started Getting started] (Kako zač<PERSON>), [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation In-text annotation] (Vrstično označevanje) in [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries Inline queries] (Vrstične poizvedbe).", "smw-editpage-annotation-disabled": "Semantično označevanje besedila na tej strani ni podprto zaradi omejitve imenskega prostora. Podrobnosti o tem, kako ga vklopiti v imenskem prostoru, so na voljo na strani pomoči [https://www.semantic-mediawiki.org/wiki/Help:Configuration Configuration].", "smw-editpage-property-annotation-enabled": "To lastnost je mogoče razširiti z uporabo semantičnih opomb za določitev podatkovnega tipa (npr<nowiki> »[[Has type::Page]]«</nowiki> ) ali druge podporne izjave (npr. <nowiki>»[[Subproperty of::dc:date]]«</nowiki>). Za opis, kako raz<PERSON> to stran, glejte stran pomoči [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration Deklaracija lastnosti] ali [https://www.semantic-mediawiki.org/ wiki/Help:List_of_datatypes Seznam razpoložljivih tipov podatkov].", "smw-editpage-property-annotation-disabled": "Te lastnosti ni mogoče razširiti z označitvijo podatkovnega tipa (npr. <nowiki>»[[Has type::Page]]«</nowiki>), saj je že vnaprej določen (za več informacij glejte stran pomoči [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Posebne lastnosti]).", "smw-editpage-concept-annotation-enabled": "Ta koncept je mogoče razširiti s funkcijo razčlenjevalnika #concept. Za opis uporabe funkcije #concept glejte stran pomoči [https://www.semantic-mediawiki.org/wiki/Help:Concepts Concept] (Koncept).", "smw-search-syntax-support": "Iskalni vnos podpira uporabo semantične [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search poizvedbene skladnje] za pomoč pri povezovanju zadetkov z uporabo Semantičnega MediaWikija.", "smw-search-input-assistance": "Omogoč<PERSON> je tudi [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance pomočnik za vnašanje], ki o<PERSON><PERSON><PERSON> predhodno izbiro razpoložlji<PERSON><PERSON> last<PERSON> in kategorij.", "smw-search-help-intro": "Vnos <nowiki> <code>[[ ... ]]</code></nowiki> bo signaliziral vhodnemu procesorju, da uporabi iskalno zaledje Semantičnega MediaWikija. Treba je opozoriti, da združevanje <nowiki> <code>[[ ... ]]</code></nowiki> z iskanjem po nestrukturiranem besedilu, kot je npr. <nowiki><code>[[ ... ]] OR Lorem ipsum</code></nowiki>, ni podprto.", "smw-search-help-structured": "Strukturirana iskanja:\n\n* <code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (kot [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context filtrirani kontekst])\n\n* <code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (s [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context poizvedbenim kontekstom])", "smw-search-help-proximity": "<PERSON><PERSON><PERSON> po bli<PERSON>ini (če lastnost ni znana, je na voljo '''samo''' za tiste zaledne sisteme, ki omogočajo integracijo iskanja po celotnem besedilu):\n\n*<nowiki><code>[[in:lorem ipsum]]</code></nowiki> (iskanje v vseh dokumentih za »lorem« in »ipsum«, ki sta bila indeksirana);\n\n*<nowiki><code>[[phrase:lorem ipsum]]</code></nowiki> (ujemanje z »lorem ipsum« kot frazo).", "smw-search-help-ask": "Naslednje povezave pojasn<PERSON>, kako up<PERSON> skladnjo <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Izbiranje strani] opisuje, kako izbrati strani in določiti pogoje!\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Iskalni operatorji] navaja razpoložljive iskalne operatorje, vključno s tistimi za poizvedbe z obsegom in nadomestnimi znaki", "smw-search-input": "Vnos in iskanje", "smw-search-help-input-assistance": "Za vnosno polje je na voljo [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance pomoč pri vnosu], ki zahteva uporabo ene od naslednjih predpon:\n\n* <code>p:</code> za omogočenje predlogov lastnosti (npr. <nowiki><code>[[p:Has ...</code></nowiki>);\n\n* <code>c:</code> za omogočenje predlogov kategorij;\n\n* <code>con:</code> za omogočenje predlogov pojmov.", "smw-search-syntax": "Skladnja", "smw-search-profile": "Razširjeno", "smw-search-profile-tooltip": "Iskalne funkcije v povezavi s Semantičnim MediaWikijem", "smw-search-profile-sort-best": "Najboljše ujemanje", "smw-search-profile-sort-recent": "Najnovejše", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-intro": "[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> profil] Special:Search omogoča dostop do specifičnih iskalnih funkcij Semantičnega MediaWikija in njegovega podprtega poizvedbenega zaledja.", "smw-search-profile-extended-help-sort": "Določa prednost razvrščanja za prikaz zadetkov z:", "smw-search-profile-extended-help-sort-title": "* »Naslov« z uporabo naslov strani (ali prikazanega naslova) kot merila za razvrščanje", "smw-search-profile-extended-help-sort-recent": "* »Najnovejše« bo prikazovalo najnovejše spremenjene entitete (podobjekt entitete bo skrit, saj te entitete niso označene z [[Property:Modification date|datumom spremembe]])", "smw-search-profile-extended-help-sort-best": "* »Najboljše ujemanje« bo razvrstilo entitete po [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy ustreznosti] na podlagi ocen, ki jih zagotovi zaledje", "smw-search-profile-extended-help-form": "Na voljo so obrazci (če so vzdr<PERSON>evani) za poiskanje posebnih primerov uporabe z izpostavitvijo različnih polj lastnosti in vrednosti, da se zoži postopek vnosa in uporabniki lažje nadaljujejo iskalni zahtevek (glejte $1).", "smw-search-profile-extended-help-namespace": "Izbirnik imenskega prostora se bo skril takoj, ko bo izbrana oblika, prikažete pa ga lahko z uporabo gumba »pokaži/skrij«.", "smw-search-profile-extended-help-search-syntax": "Iskalno vnosno polje podpira uporabo skladnje <code>#ask</code> za določitev iskalnega konteksta, specifičnega za Semantični MediaWiki. Uporabni izrazi vključujejo:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> za iskanje <PERSON> k<PERSON>, kar vs<PERSON><PERSON><PERSON> »...« in je še posebej <PERSON>, kadar iskalni kontekst ali vključene lastnosti niso znani (npr. <code>in:(lorem && ipsum)</code> je enakovredno <nowiki><code>[[~~*lorem*]] && [[~~*ipsum*]]</code></nowiki>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> za poiskanje vsega, kar vsebu<PERSON> »...« v popolnoma enakem vrstnem redu", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> za poiskanje vseh entitet z lastnostjo »...« (npr. <code>has:(Foo && Bar)</code> je enakovredno <nowiki><code>[[Foo::+]] && [[Bar::+]]</code></nowiki>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> za izključitev iz iskanja vseh entitet, ki vključujejo »...«", "smw-search-profile-extended-help-search-syntax-prefix": "* Na voljo in definirane so dodatne predpone po meri, kot je: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Nekateri izrazi so p<PERSON>, npr.:<nowiki> $1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Nekatere od navedenih operacij so uporabne samo v povezavi z omogočenim indeksiranjem celotnega besedila ali ElasticStore.''", "smw-search-profile-extended-help-query": "Uporaba <code><nowiki>$1</nowiki></code> kot poizvedba.", "smw-search-profile-extended-help-query-link": "Za več podrobnosti uporabite $1.", "smw-search-profile-extended-help-find-forms": "razpoložljivi obrazci", "smw-search-profile-extended-section-sort": "Razvrsti po", "smw-search-profile-extended-section-form": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-search-syntax": "Poišči vnos", "smw-search-profile-extended-section-namespace": "<PERSON><PERSON><PERSON> prostor", "smw-search-profile-extended-section-query": "Poizvedba", "smw-search-profile-link-caption-query": "gradilnik poizvedb", "smw-search-show": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-hide": "<PERSON>k<PERSON><PERSON>", "log-name-smw": "Dnevnik Semantičnega MediaWikija", "log-show-hide-smw": "Dnevnik Semantičnega MediaWikija $1", "logeventslist-smw-log": "Dnevnik Semantičnega MediaWikija", "log-description-smw": "Dejavnosti za [https://www.semantic-mediawiki.org/wiki/Help:Logging omogočene vrste dogodkov], o katerih poročajo Semantični MediaWiki in njegove komponente.", "logentry-smw-maintenance": "<PERSON><PERSON><PERSON>, povezani z vzdrževanjem, ki jih oddaja Semantični MediaWiki", "smw-datavalue-import-unknown-namespace": "Imenski prostor uvoza »$1« ni znan. Z<PERSON>tovite, da so podatki o uvozu OWL na voljo na [[MediaWiki:Smw import $1]].", "smw-datavalue-import-missing-namespace-uri": "V [[MediaWiki:Smw import $1|uvozu $1]] ni mogoče najti URI-ja imenskega prostora.", "smw-datavalue-import-missing-type": "Za »$1« ni bila v [[MediaWiki:Smw import $2|uvozu $2]] najdena nobena definicija tipa.", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Uvoz $1]]", "smw-datavalue-import-invalid-value": "»$1« ni veljaven format. Ta mora ustrezati shemi »imenski prostor«:»identifikator« (npr. »foaf:name«).", "smw-datavalue-import-invalid-format": "»$1« ni veljavno zaporedje znakov za to shemo. Razdeljena mora biti v štiri dele.", "smw-property-predefined-impo": "»$1« je vnaprej določena lastnost, ki opisuje povezavo z [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary uvoženim besednjakom] in je navedena v [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantičnem MediaWikiju].", "smw-property-predefined-type": "»$1« je vnaprej določena lastnost, ki opisuje [[Special:Types|podatkovni tip]] lastnosti in jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-sobj": "»$1« je vnaprej določena lastnost, ki predstavlja konstrukcijo [https://www.semantic-mediawiki.org/wiki/Help:Container vsebnika] in je navedena v [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantičnem MediaWikiju].", "smw-property-predefined-long-sobj": "Vsebnik omogoča kopičenje dodelitev vrednosti lastnostim, podobnih tistim na običajni vikistrani, vendar v drugem prostoru entitete, med povezovanjem z vdelanim podobjektom.", "smw-property-predefined-errp": "»$1« je vnaprej dolo<PERSON> lastnost, ki sledi napake pri vnosu za iregularna označevanja vrednosti in jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-errp": "V <PERSON><PERSON><PERSON> primerov je to posledica neujemanja tipa ali omejitve [[Property:Allows value|vrednosti]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value »$1«] je vnaprej dolo<PERSON>ena lastnost, ki lahko določa seznam dovoljenih vrednosti za omejitev dodelitev vrednosti lastnostim in jo zagotavlja [https:// www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list »$1«] je vnaprej dolo<PERSON>ena lastnost, ki lahko poda sklic na seznam, ki vsebuje dovoljene vrednosti za omejitev dodelitve vrednosti lastnostim in jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "Lastnost »$1« ima omejeno področje uporabe in je uporabnik ne more uporabiti kot lastnost označitve.", "smw-datavalue-property-restricted-declarative-use": "Lastnost »$1« je deklarativna lastnost in jo je mogoče uporabiti samo na strani lastnosti ali kategorije.", "smw-datavalue-property-create-restriction": "Lastnost »$1« ne obstaja in uporabniku manjka dovoljenje »$2« (za ustvarjanje ali označevanje vrednosti neodobrene lastnosti glejte stran [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Authority mode]).", "smw-datavalue-property-invalid-character": "Lastnost »$1« vsebuje naveden znak »$2« kot del oznake lastnosti in je zato razvrščena kot neveljavna.", "smw-datavalue-property-invalid-chain": "Uporaba »$1« kot verige lastnosti ni dovoljena med postopkom označevanja.", "smw-datavalue-restricted-use": "Podatkovna vrednost »$1« je označena za omejeno uporabo.", "smw-datavalue-invalid-number": "»$1« ni mogoče interpretirati kot številko.", "smw-query-condition-circular": "V »$1« je bil zaznan možen krožni pogoj.", "smw-query-condition-empty": "Opis poizvedbe ima prazen pogoj.", "smw-types-list": "Seznam podatkovnih tipov", "smw-types-default": "»$1« je vgrajen podatkovni tip.", "smw-types-help": "Dodatne informacije in primere so na voljo na tej [https://www.semantic-mediawiki.org/wiki/Help:Type_ $1 strani pomoči].", "smw-type-anu": "»$1« je različica podatkovnega tipa [[Special:Types/URL|URL]] in se večinoma uporablja za deklaracijo izvoza ''owl:AnnotationProperty''.", "smw-type-boo": "»$1« je osnovni podatkovni tip za opis vrednosti true/false.", "smw-type-cod": "»$1« je različica podatkovnega tipa [[Specia:Types/Text|Text]], ki se uporablja za tehnična besedila poljubne dolžine, kot je izvorna koda.", "smw-type-geo": "»$1« je podatkovni tip, ki opisuje geografske lokacije in za zagotavljanje razširjene funkcionalnosti zahteva razširitev [https://www.semantic-mediawiki.org/wiki/Extension:Maps »Maps«].", "smw-type-tel": "»$1« je poseben podatkovni tip za opis mednarodnih telefonskih številk v skladu z RFC 3966.", "smw-type-txt": "»$1« je osnovni podatkovni tip za opis nizov poljubne dolžine.", "smw-type-dat": "»$1« je osnovni podatkovni tip za predstavitev časovnih točk v poenotenem formatu.", "smw-type-ema": "»$1« je poseben podatkovni tip za predstavitev e-pošte.", "smw-type-tem": "»$1« je poseben številski podatkovni tip za predstavitev temperature.", "smw-type-qty": "»$1« je podatkovni tip za opis količin z numerično predstavitvijo in mersko enoto.", "smw-type-rec": "»$1« je vsebniški podatkovni tip, ki določa seznam vtipkanih lastnosti v stalnem vrstnem redu.", "smw-type-extra-tem": "<PERSON><PERSON> pretvorbe vklju<PERSON><PERSON><PERSON>p<PERSON> enote, kot so <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fahrenheit in Rankine.", "smw-type-tab-properties": "<PERSON><PERSON><PERSON>", "smw-type-tab-types": "T<PERSON><PERSON>", "smw-type-tab-type-ids": "<PERSON><PERSON><PERSON> <PERSON>ov", "smw-type-tab-errors": "Napake", "smw-type-primitive": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-contextual": "Kontekstualni", "smw-type-compound": "Sestavljeni", "smw-type-container": "Vsebniški", "smw-type-no-group": "Nerazvrščeni", "smw-specials-bytype-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Type_$1", "smw-special-pageproperty-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:PageProperty", "smw-special-pageproperty-description": "Ta stran vsebuje brskalni vmesnik za pridobitev vseh vrednosti lastnosti in dane strani. Drugi razpoložljivi iskalni vmesniki vključujejo [[Special:SearchByProperty|iskanje lastnosti]] in [[Special:Ask|gradilnik poizvedb Semantično iskanje]].", "smw-property-predefined-errc": "»$1« je vnaprej določena lastnost, ki jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki] in predstavlja napake, ki so se pojavile v povezavi z neustreznimi označitvami vrednosti ali obdelavo vnosa.", "smw-property-predefined-long-errc": "Napake se zbirajo v [https://www.semantic-mediawiki.org/wiki/Help:vsebnik vsebniku], ki lahko vključuje sklic na lastnost, ki je povzročila neskladje.", "smw-property-predefined-errt": "»$1« je vnaprej dolo<PERSON> lastnost, ki vsebuje besedilni opis napake in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Uporabniško določeni podobjekt je vseboval neveljavno shemo poimenovanja. Zapis s piko ($1), uporabljen v prvih petih znakih, je pridržan za razširitve. Nastavite lahko [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier poimenovani identifikator].", "smw-datavalue-record-invalid-property-declaration": "Definicija zapisa vsebuje lastnost »$1«, ki je sama dekla<PERSON> kot vrsta zapisa, kar ni dovol<PERSON>.", "smw-property-predefined-mdat": "»$1« je vnaprej dolo<PERSON> lastnost, ki ustreza datumu zadnje spremembe strani in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-cdat": "»$1« je vnaprej določena lastnost, ki ustreza datumu prve redakcije strani in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-newp": "»$1« je v<PERSON><PERSON>j <PERSON>, ki oz<PERSON>, ali je stran nova ali ne in jo omogoč<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-ledt": "»$1« je vnaprej dolo<PERSON> lastnost, ki vsebuje ime strani uporabnika, ki je ustvaril zadnjo redakcijo, in jo omogo<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-mime": "»$1« je vnaprej določena lastnost, ki opisuje tip MIME naložene datoteke in jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-media": "»$1« je vnaprej določena lastnost, ki opisuje tip predstavnosti naložene datoteke in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-askfo": "»$1« je vnaprej določena lastnost, ki vsebuje ime oblike zadetkov v poizvedbi in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-askst": "»$1« je vnaprej določena lastnost, ki opisuje pogoje poizvedbe kot niz in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-askdu": "»$1« je vnaprej določena lastnost, ki vsebuje časovno vrednost (v sekundah), ki je bila potrebna za dokončanje izvajanja poizvedbe in jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-asksc": "\" $1 \" je v<PERSON><PERSON>j dolo<PERSON> last<PERSON>, ki jo o<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki] in identificira alternativne (npr. oddaljene, združene) vire poizvedb.", "smw-property-predefined-askco": "» $1 « je vnaprej določena lastnost, ki jo omogo<PERSON>a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki] za opis stanja poizvedbe ali njenih komponent.", "smw-property-predefined-long-askco": "Dodeljena številka ali številke predstavljajo interno kodificirano stanje, ki je razloženo na [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler strani pomo<PERSON>].", "smw-property-predefined-prec": "»$1« je vnaprej določena lastnost, ki opisuje [https://www.semantic-mediawiki.org/wiki/Help:Display_precision natančnost prikaza] (v decimalnih števkah) za številske podatkovne tipe.", "smw-property-predefined-attch-link": "»$1« je vnaprej določena lastnost, ki zbira število pogojev, uporabljenih v poizvedbi, in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-inst": "»$1« je vnaprej določena notranja lastnost za shranjevanje informacij o kategorijah neodvisno od Mediawikija in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-unit": "»$1« je deklarativna vnaprej določena lastnost za definiranje enot prikaza številskih tipskih lastnosti in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-unit": "<PERSON><PERSON><PERSON>, lo<PERSON><PERSON> z vejicami, omogoča opis enot ali formatov, ki bodo uporabljeni za prikaz.", "smw-property-predefined-conv": "»$1« je deklarativna vnaprej določena lastnost za definiranje pretvorbenega faktorja za posamezno enoto fizikalne količine in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-serv": "»$1« je deklarativna vnaprej določena lastnost za dodajanje povezav do storitev lastnostim, ki jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-redi": "»$1« je notranja vnaprej določena lastnost za zapisovanje preusmeritev in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-subp": "»$1« je deklarativna vnaprej določena lastnost, ki dolo<PERSON>, da je lastnost [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of podlastnost] druge in jo omogoč<PERSON> [https://www.semantic- mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-subc": "»$1« je vnaprej določena lastnost za opredelitev, da je kategorija [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of podkategorija druge] in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-conc": "»$1« je notranja vnaprej določena lastnost za opredelitev povezanega koncepta in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-err-type": "»$1« je vnaprej določena lastnost za identifikacijo skupine ali razreda [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors napak pri obdelavi] in jo <PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-skey": "»$1« je notranja vnaprej določena lastnost za razvrščanje sklicev in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-pplb": "»$1« je deklarativna vnaprej določena lastnost za določitev [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label prefererenčne oznake lastnosti] in jo omogoča [https://www.semantic- mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-chgpro": "»$1« je vnaprej določena lastnost za shranjevanje informacij o [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation širjenju sprememb] in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-schema-link": " in jo o<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-format-schema": " in jo o<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-profile-schema": " in jo o<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-trans": " in jo o<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-trans-source": " in jo o<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-trans-group": " in jo o<PERSON><PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-cont-len": "»$1« je vnaprej določena lastnost za shranjevanje informacij o dolžini in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-cont-len": "Uporablja se v povezavi z [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (in [https://www.semantic-mediawiki.org/Attachment_processor obdelovalnikom priponk]) za zbiranje in shranjevanje informacij o dolžini, pridobljenih iz obdelovane datoteke (če je predložena).", "smw-property-predefined-cont-lang": "»$1« je vnaprej določena lastnost za shranjevanje informacij o dolžini in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-cont-lang": "Uporablja se v povezavi z [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (in [https://www.semantic-mediawiki.org/Attachment_processor obdelovalnikom priponk]) za zbiranje in shranjevanje informacij o jeziku, pridobljenih iz obdelovane datoteke (če je predložena).", "smw-property-predefined-cont-title": "»$1« je vnaprej določena lastnost za shranjevanje informacij o naslovih, ki jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-cont-title": "Uporablja se v povezavi z [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (in [https://www.semantic-mediawiki.org/Attachment_processor obdelovalnikom priponk]) za zbiranje in shranjevanje informacij o naslovih, pridobljenih iz obdelovane datoteke (če je predložena).", "smw-property-predefined-cont-author": "»$1« je vnaprej določena lastnost za shranjevanje informacij o avtorjih, ki jo omogo<PERSON>a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-cont-author": "Uporablja se v povezavi z [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (in [https://www.semantic-mediawiki.org/Attachment_processor obdelovalnikom priponk]) za zbiranje in shranjevanje informacij o avtorjih, pridobljenih iz obdelovane datoteke (če je predložena).", "smw-property-predefined-cont-date": "»$1« je vnaprej določena lastnost za shranjevanje informacij o datumih, ki jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-cont-date": "Uporablja se v povezavi z [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (in [https://www.semantic-mediawiki.org/Attachment_processor obdelovalnikom priponk]) za zbiranje in shranjevanje informacij o datumih, pridobljenih iz obdelovane datoteke (če je predložena).", "smw-property-predefined-cont-type": "»$1« je vnaprej določena lastnost za shranjevanje informacij o vrsti datoteke, ki jo omogo<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-cont-type": "Uporablja se v povezavi z [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (in [https://www.semantic-mediawiki.org/Attachment_processor obdelovalnikom priponk]) za zbiranje in shranjevanje informacij o vrsti obdelovane datoteke (če je predložena).", "smw-property-predefined-cont-keyw": "»$1« je vnaprej določena lastnost za navajanje ključnih besed, ki jo omogo<PERSON>a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-cont-keyw": "Uporablja se v povezavi z [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (in [https://www.semantic-mediawiki.org/Attachment_processor obdelovalnikom priponk]) za zbiranje in shranjevanje informacij o ključnih besedah, pridobljenih iz obdelovane datoteke (če je predložena).", "smw-property-predefined-file-attch": "»$1« je vnaprej določena lastnost za shranjevanje informacij o vsebniku, ki jo omogo<PERSON>a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-file-attch": "Uporablja se v povezavi z [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (in [https://www.semantic-mediawiki.org/Attachment_processor obdelovalnikom priponk]) za zbiranje vseh vsebinsko specifičnih informacij, ki jih je mogoče pridobiti iz obdelovane datoteke (če je navedena).", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps Razširitev »Maps«] ni bila zaznana, zato je delovanje »$1« omejeno.", "smw-datavalue-monolingual-dataitem-missing": "Manjka pričakovana postavka za izdelavo enojezične sestavljene vrednosti.", "smw-datavalue-monolingual-lcode-parenthesis": "($1)", "smw-datavalue-languagecode-missing": "Za označitev »$1« razčlenjevalnik ni mogel določiti oznake jezika (tj. »foo@en«).", "smw-datavalue-languagecode-invalid": "»$1« ni bilo prepoznano kot podprta oznaka jezika.", "smw-property-predefined-lcode": "»$1« je vnaprej določena lastnost za predstavljanje oznake jezika v obliki BCP47, ki jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-type-mlt-rec": "»$1« je podatkovni tip [https://www.semantic-mediawiki.org/wiki/Help:Container vsebnika], ki besedilno vrednost povezuje z določeno [[Property:Language code|oznako jezika]].", "smw-types-extra-mlt-lcode": "Podatkovni tip {{PLURAL:$2|zah<PERSON><PERSON>|ne zahteva}} oznake jezika (tj. {{PLURAL:$2|označitev vrednosti brez oznake jezika ni sprejeta|označitev vrednosti brez oznake jezika je sprejeta}}).", "smw-property-predefined-text": "»$1« je vnaprej določena lastnost, ki opisuje tip MIME naložene datoteke in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-pdesc": "»$1« je vnaprej določena lastnost, ki opisuje lastnost v kontekstu jezika in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-list": "»$1« je vnaprej določena lastnost za opredelitev seznama lastnosti, ki se uporablja z lastnostjo tipa [[Special:Types/Record|record]] lastnosti in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Čas uporabe razčlenjevalnika za znotrajvrstične anotacije", "smw-limitreport-intext-postproctime": "[SMW] čas naknadne obdelave", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|sekunda|sekundi|sekunde|sekund}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekunda|sekundi|sekunde|sekund}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Čas posodabljanja podatkovne zbirke (ob osvežitvi strani)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|sekunda|sekundi|sekunde|sekund}}", "smw_allows_pattern": "Ta stran naj bi vseb<PERSON><PERSON> seznam sklicev (ki jim sledijo [https://en.wikipedia.org/wiki/Regular_expression regularni izrazi]), ki bodo na voljo z lastnostjo [[Property:Allows pattern|Allows pattern]]. Za urejanje te strani je potrebna pravica <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "»$1« je regularni izraz »$2« razvrstil kot neveljavno.", "smw-datavalue-allows-pattern-reference-unknown": "Sklica na vzorec »$1« ni bilo mogoče povezati z vnosom v [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "Sklica seznama »$1« ni bilo mogoče povezati s stranjo [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "V vsebini seznama »$1« manjkajo vnosi z oznako *.", "smw-datavalue-feature-not-supported": "Možnost »$1« v tem vikiju ni podprta ali je bila onemogočena.", "smw-property-predefined-pvap": "»$1« je vnaprej določena lastnost za določitev [[MediaWiki:Smw allows pattern|sklica na vzorec]], kar omogoča uporabo iskanja z  [https://en.wikipedia.org/wiki/Regular_expression regularnimi izrazi] in jo zagotavlja [https: //www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-dtitle": "»$1« je vnaprej dolo<PERSON> lastnost, ki lahko entiteti dodeli posamezen naslov za prikaz in jo omogoča [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-pvuc": "»$1« je vnaprej določena lastnost, ki jo omogo<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki] za omejitev dodelitve vrednosti vsakemu primerku tako, da je ta edinstven (ali največ eden).", "smw-property-predefined-long-pvuc": "Edinstvenost se vzpostavi, ko dve vrednosti nista enaki v svoji literalni predstavitvi in vsaka kršitev te omejitve bo kategorizirana kot napaka.", "smw-datavalue-constraint-uniqueness-violation": "Lastnost »$1« dovoljuje samo edinstvene dodelitve vrednosti, vrednost ''$2'' pa je že uporabljena kot označitev na strani »$3«.", "smw-datavalue-constraint-uniqueness-violation-isknown": "Lastnost »$1« dovoljuje samo edinstvene označitve vrednosti, ''$2'' pa že vsebuje dodeljeno vrednost. »$3« krši omejitev edinstvenosti.", "smw-datavalue-constraint-violation-non-negative-integer": "Lastnost »$1« ima omejitev »nenegativno c<PERSON>«, vrednost ''$2'' pa kr<PERSON>i to zahtevo.", "smw-datavalue-constraint-violation-must-exists": "Lastnost »$1« ima omejitev <code>must_exists</code>, vrednost ''$2'' pa kr<PERSON>i to zahtevo.", "smw-datavalue-constraint-violation-single-value": "Lastnost »[[Property:$1|$1]]« ima omejitev <code>single_value</code>, vrednost »$2« pa krši to zahtevo.", "smw-constraint-violation-uniqueness": "Lastnosti »[[Property:$1|$1]]« je do<PERSON><PERSON><PERSON> <code>unique_value_constraint</code>, ki dovoljuje samo edinstvene dodelitve vrednosti, dodelitev vrednosti ''$2'' pa je bila že najdena v subjektu »$3«.", "smw-constraint-violation-uniqueness-isknown": "Lastnosti »[[Property:$1|$1]]« je dodel<PERSON><PERSON> omejitev <code>unique_value_constraint</code>, zato so dovoljene samo edinstvene dodelitve vrednosti. ''$2'' že vsebuje dodeljeno vrednost z »$3«, s čimer krši omejitev edinstvenosti za trenutni subjekt.", "smw-constraint-violation-non-negative-integer": "Lastnosti »[[Property:$1|$1]]« je do<PERSON><PERSON><PERSON> <code>non_negative_integer</code>, označitev vrednosti ''$2'' pa kr<PERSON><PERSON> to omejitveno zahtevo.", "smw-constraint-violation-must-exists": "Lastnosti »[[Property:$1|$1]]« je do<PERSON><PERSON><PERSON> <code>must_exists</code>, označitev vrednosti ''$2''pa kr<PERSON><PERSON> to omejitveno zahtevo.", "smw-constraint-violation-single-value": "Lastnosti »[[Property:$1|$1]]« je do<PERSON><PERSON><PERSON> <code>single_value</code>, označitev vrednosti »$2« pa krši to omejitveno zahtevo.", "smw-constraint-violation-class-shape-constraint-missing-property": "<PERSON><PERSON><PERSON> »[[:$1]]« je do<PERSON><PERSON><PERSON> <code>shape_constraint</code> s ključem <code>property</code>, vendar z<PERSON><PERSON><PERSON> last<PERSON>t »$2« manjka.", "smw-constraint-violation-class-shape-constraint-wrong-type": "<PERSON><PERSON><PERSON> »[[:$1]]« je do<PERSON><PERSON><PERSON> <code>shape_constraint</code> s ključem <code>property_type</code>, vendar se lastnost »$2« ne ujema s tipom »$3«.", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "<PERSON><PERSON><PERSON> [[:$1]] je do<PERSON><PERSON><PERSON> <code>shape_constraint</code> s ključem <code>max_cardinality</code>, vendar se lastnost »$2« ne ujema s kardinalnostjo »$3«.", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "<PERSON><PERSON><PERSON> »[[:$1]]« je do<PERSON><PERSON><PERSON> <code>shape_constraint</code> s ključem <code>min_length</code>, vendar lastnost »$2« ne izpolnjuje zahteve dolžine »$3«.", "smw-constraint-violation-class-mandatory-properties-constraint": "<PERSON><PERSON><PERSON> »[[:$1]]« je do<PERSON><PERSON><PERSON> <code>mandatory_properties</code>, ki zahteva naslednje obvezne lastnosti: $2", "smw-constraint-violation-allowed-namespace-no-match": "Lastnosti »[[Property:$1|$1]]« je do<PERSON><PERSON><PERSON> «mejitev <code>allowed_namespaces</code> in »$2« krši zahtevo glede imenskega prostora; dovoljeni so samo naslednji imenski prostori: »$3«.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "Omejitev <code>allowed_namespaces</code> zahteva tip strani.", "smw-constraint-schema-category-invalid-type": "Označ<PERSON> »$1« ni veljavna za kategorijo; potreben je tip »$2«.", "smw-constraint-schema-property-invalid-type": "Označ<PERSON> »$1« ni veljavna za lastnost; potreben je tip »$2«.", "smw-constraint-error-allows-value-list": "»$1« ni na seznamu ($2) [[Property:Allows value|dovoljenih vrednosti]] za lastnost »$3«.", "smw-constraint-error-allows-value-range": "»$1« ni znotraj tega obsega »$2«, določenega z omejitvijo [[Property:Allows value|dovoljenih vrednosti]] za lastnost »$3«.", "smw-property-predefined-boo": "»$1« je [[Special:Types/Boolean|tip]] in vnaprej določena lastnost, ki jo za predstavitev Boolovih vrednosti zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-num": "»$1« je [[Special:Types/Number|tip]] in vnaprej določena lastnost, ki jo za predstavitev številskih vrednosti zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-dat": "»$1« je [[Special:Types/Date|tip]] in vnaprej določena lastnost, ki jo za predstavitev datumskih vrednosti zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-uri": "»$1« je [[Special:Types/Boolean|tip]] in vnaprej določena lastnost, ki jo za predstavitev logičnih vrednosti zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-qty": "»$1« je [[Special:Types/Number|tip]] in vnaprej določena lastnost, ki jo za predstavitev številskih vrednosti zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-datavalue-time-invalid-offset-zone-usage": "»$1« vsebuje odmik in identifikator cone, ki ni podprt.", "smw-datavalue-time-invalid-values": "Vrednost »$1« vsebuje nerazložljive informacije v obliki »$2«.", "smw-datavalue-time-invalid-date-components-common": "»$1« vsebuje nekaj nerazložljivih informacij.", "smw-datavalue-time-invalid-date-components-dash": "»$1« vsebuje zunanji vezaj ali drug znak, ki ni veljaven za razlago datuma.", "smw-datavalue-time-invalid-date-components-empty": "»$1« vsebuje nekaj praznih komponent.", "smw-datavalue-time-invalid-date-components-three": "»$1« vsebuje več kot tri komponente, potrebne za razlago datuma.", "smw-datavalue-time-invalid-date-components-sequence": "»$1« vseb<PERSON><PERSON> zaporedje, ki ga ni bilo mogoče interpretirati z razpoložljivo matriko ujemanja za datumske komponente.", "smw-datavalue-time-invalid-ampm": "»$1« vsebuje »$2« kot element ure, ki ni veljaven za 12-urni format zapis", "smw-datavalue-time-invalid-jd": "Vhodne vrednosti »$1« ni mogoče razložiti kot veljavno številko JD (julijanski dan), pri <PERSON><PERSON>er je sporočeno »$2«.", "smw-datavalue-time-invalid-prehistoric": "Ni mogoče interpretirati prazgodovinske vhodne vrednosti »$1«. Na primer določitev več kot določenega števila let v koledarskem modelu lahko v prazgodovinskem kontekstu vrne nepričakovane rezultate.", "smw-datavalue-time-invalid": "Vhodne vrednosti »$1« ni mogoče razložiti kot veljavno komponento datuma ali časa, pri čemer je sporočeno »$2«.", "smw-datavalue-external-formatter-uri-missing-placeholder": "URI-ju oblikovalnika manjka nadomestna spremenljivka ''$1''.", "smw-datavalue-external-formatter-invalid-uri": "»$1« je neveljaven URL.", "smw-datavalue-external-identifier-formatter-missing": "Lastnosti manjka dodelitev [[Property:External formatter uri|»URI-ja zunanjega oblikovalnika«]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "Zunanji identifikator »$1« pričakuje zamenjavo več polj, vendar trenutni vrednosti »$2« manjka vsaj en parameter vrednosti, ki ustreza zahtevi.", "smw-datavalue-keyword-maximum-length": "<PERSON><PERSON><PERSON><PERSON><PERSON> beseda je presegla največjo dolžino $1 {{PLURAL:$1|znak|znaka|znake|znakov}}.", "smw-property-predefined-eid": "»$1« je [[Special:Types/Date|tip]] in vnaprej določena lastnost, ki jo za predstavitev zunanjih identifikatorjev zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-peid": "»$1« je vnaprej določ<PERSON> lastnost, ki določa zunanji identifikator in jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-pefu": "»$1« je vnaprej določena lastnost, ki jo za določitev zunanjega vira z nadomestkom zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-pefu": "URI mora vsebovati nadomestek, ki je prilagojen z vrednostjo [[Special:Types/External identifier|zunanjega identifikatorja]], da se oblikuje veljaven sklic na vir.", "smw-type-eid": "»$1« je varianta podatkovnega tipa [[Special:Types/Text|Text]] za opis zunanjih virov (na podlagi URI-jev) in zahteva za deklaracijo [[Property:External formatter uri|URI-ja zunanjega oblikovalca]] dodel<PERSON><PERSON>.", "smw-property-predefined-keyw": "»$1« je vnaprej določena lastnost in [[Special:Types/Keyword|tip]] [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantičnega MediaWikija], ki normalizira besedilo in ima omejeno dolžino znakov.", "smw-type-keyw": "»$1« je različica podatkovnega tipa [[Special:Types/Text|Besedilo]], ki ima omejeno š<PERSON>vil<PERSON> z<PERSON>kov in normalizirano predstavitev vsebine.", "smw-datavalue-stripmarker-parse-error": "Podana vrednost »$1« vsebuje [https://en.wikipedia.org/wiki/Help:Strip_markers oznake maskirane vsebine] in je zato ni mogoče zadostno razčleniti.", "smw-datavalue-parse-error": "Podana vrednost »$1« ni bila razumljena.", "smw-datavalue-propertylist-invalid-property-key": "Seznam lastnosti »$1« je vseboval neveljaven ključ lastnosti »$2«.", "smw-datavalue-type-invalid-typeuri": "Tipa »$1« ni bilo mogoče pretvoriti v veljavno predstavitev URI-ja.", "smw-datavalue-wikipage-missing-fragment-context": "Vhodne vrednosti vikistrani »$1« ni mogoče uporabiti brez kontekstne strani.", "smw-datavalue-wikipage-invalid-title": "Vhodna vrednost vrste strani »$1« vsebuje neveljavne znake ali je nepopolna in lahko zato med postopkom poizvedbe ali označevanja povzroči nepričakovane rezultate.", "smw-datavalue-wikipage-property-invalid-title": "Lastnost »$1« z vhodno vrednostjo vrste »$2« vsebuje neveljavne znake ali je nepopolna in lahko zato med postopkom poizvedbe ali označevanja povzroči nepričakovane rezultate.", "smw-datavalue-wikipage-empty": "Vhodna vrednost vikistrani je prazna (npr. <code>[[SomeProperty::]], [[]]</code>) in je zato ni mogoče uporabiti kot ime ali kot del poizvedbenega pogoja.", "smw-type-ref-rec": "»$1« je vrsta [https://www.semantic-mediawiki.org/wiki/Container vsebnika], ki omogoča beleženje dodatnih informacij (npr. podatkov o poreklu) o dodelitvi vrednosti.", "smw-datavalue-reference-outputformat": "$1: $2", "smw-datavalue-reference-invalid-fields-definition": "Tip [[Special:Types/Reference|Sklic]] p<PERSON><PERSON><PERSON><PERSON><PERSON>, da bo seznam lastnosti deklariran z lastnostjo [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Has fields].", "smw-parser-invalid-json-format": "Razčlenjevalnik JSON je vrnil napako »$1«.", "smw-property-preferred-title-format": "$1 ($2)", "smw-property-preferred-label-language-combination-exists": "»$1« ni mogoče uporabiti kot prednostno oznako, ker je jezik »$2« že dodeljen oznaki »$3«.", "smw-clipboard-copy-link": "Kopiraj povezavo v odložišče", "smw-property-userdefined-fixedtable": "»$1« je bil konfiguriran kot [https://www.semantic-mediawiki.org/wiki/Fixed_properties fiksna lastnost] in vsaka sprememba njegove [https://www.semantic-mediawiki.org/wiki/Type_declaration deklaracije tipa] z<PERSON><PERSON><PERSON>, da zaž<PERSON>te <code>setupStore.php</code> ali dokončate posebno opravilo [[Special:SemanticMediaWiki|»namestitev podatkovne zbirke in nadgradnja«]].", "smw-data-lookup": "Pridobivanje podatkov&nbsp;...", "smw-data-lookup-with-wait": "Zahtevek se obdeluje, kar lahko vzame nekaj trenutkov.", "smw-no-data-available": "Ni razpoložlji<PERSON>h podatkov.", "smw-property-req-violation-missing-fields": "Lastnosti »$1« man<PERSON>a <PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] za ta tip »$2«.", "smw-property-req-violation-multiple-fields": "Lastnost »$1« vseb<PERSON><PERSON> ve<PERSON> (torej konkuren<PERSON>) izjav [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>]; za to vrsto »$2« se pričakuje samo ena.", "smw-property-req-violation-missing-formatter-uri": "Lastnosti »$1« manjkajo podrobnosti deklaracije za označeno vrsto, ker ni uspela opredeliti lastnosti <code>URI zunanjega oblikovalnika</code>.", "smw-property-req-violation-predefined-type": "Lastnost »$1« kot vnaprej določena lastnost vsebuje deklaracijo tipa »$2«, ki ni združljiva s privzetim tipom te lastnosti.", "smw-property-req-violation-import-type": "Zaznana je bila deklaracija tipa, ki ni združljiva s predhodno določenim tipom uvoženega besednjaka »$1«. Na splošno ni treba deklarirati tipa, ker se informacije pridobijo iz definicije uvoza.", "smw-property-req-violation-change-propagation-locked-error": "Lastnost »$1« je bila spremenjena in zahteva ponovno oceno dodeljenih entitet z uporabo procesa [https://www.semantic-mediawiki.org/wiki/Change_propagation razširjanja spremembe]. V tem času je do končanega posodabljanja osnovnega opravila stran lastnosti zaklenjena, da se preprečijo vmesne prekinitve ali navzkrižna opravila. Proces lahko nekaj časa traja, preden je stran mogoče odkleniti, saj je odvisen od velikosti in pogostosti razporejevalnika [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue čakalne vrste opravil].", "smw-property-req-violation-change-propagation-locked-warning": "Lastnost »$1« je bila spremenjena in zahteva ponovno oceno dodeljenih entitet z uporabo procesa [https://www.semantic-mediawiki.org/wiki/Change_propagation razširjanja spremembe]. Posodabljanje lahko nekaj časa traja, saj je odvisno od velikosti in frekvence razporejevalnika [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue čakalne vrste opravil]. Priporočljivo je, da do dokončanja procesa spreminjanje lastnosti odložite, da se izognete vmesnim prekinitvam ali navzkrižnim opravilom.", "smw-property-req-violation-change-propagation-pending": "Na obdelavo čakajo [https://www.semantic-mediawiki.org/wiki/Change_propagation posodobitve razširjanja sprememb] (ocenjeno $1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|opravilo|opravili|opravila|opravil}}]), zato je priporočljivo s spremembami lastnosti počakati, dokler se proces ne zakl<PERSON>či, da preprečite vmesne prekinitve ali protislovne specifikacije.", "smw-property-req-violation-missing-maps-extension": "Semantični MediaWiki ni mogel zaznati razširitve [https://www.semantic-mediawiki.org/wiki/Extension:Maps »Maps«], ki je osnovni pogoj, zato je funkcionalnost te lastnosti omejena (tj. ni mogoče shranjevati ali obdelovati geografskih podatkov).", "smw-property-req-violation-type": "Lastnost vsebuje konkurenčne specifikacije tipa, kar lahko povzroči neveljavne označitve vrednosti, zato se pričakuje, da uporabnik dodeli samo en ustrezni tip.", "smw-property-req-error-list": "Ta lastnost vsebuje naslednje napake ali opozorila:", "smw-property-req-violation-parent-type": "Lastnost »$1« in dodeljena nadrejena lastnost »$2« imata različno označitev tipa.", "smw-property-req-violation-forced-removal-annotated-type": "Omogočena je bila uveljavitev [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance obveznega dedovanja nadrejenega tipa]; tip označitve za lastnost »$1« se ne ujema z vrsto nadrejene lastnosti »$2« in je bil spremenjen, da odraža to zahtevo. Priporočljivo je prilagoditi definicijo tipa na strani, da se za to lastnost odstranita sporočilo o napaki in obvezna uveljavitev.", "smw-change-propagation-protection": "Ta stran je z<PERSON><PERSON>, da se prepreči nenamerno spreminjan<PERSON> podatkov, medtem ko se izvaja posodobitev z [https://www.semantic-mediawiki.org/wiki/Change_propagation razširjanjem spremembe]. Proces lahko nekaj časa traja, preden se stran odklene, saj je odvisen od velikosti in pogostosti razporejevalnika [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue čakalne vrste opravil].", "smw-category-change-propagation-locked-error": "Kategorija »$1« je bila spremenjena in zahteva ponovno oceno dodeljenih entitet z uporabo procesa [https://www.semantic-mediawiki.org/wiki/Change_propagation razširjanja spremembe]. V tem času je do dokončanja osnovnega opravila posodabljanja stran kategorije zaklenjena, da se preprečijo vmesne prekinitve ali navzkrižna opravila. Proces lahko nekaj časa traja, preden je stran mogoče odkleniti, saj je odvisen od velikosti in frekvence razporejevalnika [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue čakalne vrste opravil].", "smw-category-change-propagation-locked-warning": "Kategorija »$1« je bila spremenjena in zahteva ponovno oceno dodeljenih entitet z uporabo procesa [https://www.semantic-mediawiki.org/wiki/Change_propagation razširjanja spremembe]. Posodabljanje lahko nekaj časa traja, saj je odvisno od velikosti in frekvence razporejevalnika [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue čakalne vrste opravil]. Priporočljivo je, da do dokončanja procesa spreminjanje kategorije odložite, da se izognete vmesnim prekinitvam ali navzkrižnim opravilom.", "smw-category-change-propagation-pending": "Na obdelavo čakajo [https://www.semantic-mediawiki.org/wiki/Change_propagation posodobitve razširjanja sprememb] (ocenjeno $1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|opravilo|opravili|opravila|opravil}}]), zato je priporočljivo s spremembami lastnosti počakati, dokler se proces ne zakl<PERSON>či, da preprečite vmesne prekinitve ali protislovne specifikacije.", "smw-category-invalid-value-assignment": "»$1« ni prepoznano kot veljavna označitev kategorije ali vrednosti.", "protect-level-smw-pageedit": "Dovoli samo uporabnikom z dovoljenjem za urejanje strani (Semantični MediaWiki)", "smw-create-protection": "Us<PERSON><PERSON><PERSON><PERSON> »$1« je omejeno na uporabnike z ustrezno pravico »$2« (ali [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups skupino uporabnikov]), ko je omogo<PERSON><PERSON> [https://www .semantic-mediawiki.org/wiki/Help:Authority_mode pooblastitveni način].", "smw-create-protection-exists": "S<PERSON><PERSON>embe lastnosti »$1« so omejene na uporabnike z ustrezno pravico »$2« (ali [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups skupino uporabnikov]), ko je omogo<PERSON> [https://www .semantic-mediawiki.org/wiki/Help:Authority_mode pooblastitveni način].", "smw-edit-protection": "Ta stran je [[Property:Is edit protected|za<PERSON><PERSON><PERSON><PERSON>]], da se prepreči nenamerno spremin<PERSON><PERSON> pod<PERSON> in jo lahko urejajo samo uporabniki z ustrezno urejevalsko pravico (»$1 «) ali [https://www.semantic-mediawiki.org/ wiki/Pomoč:Uporabniške_pravice_in_uporabniške_skupine uporabniško skupino].", "smw-edit-protection-disabled": "Zaščita urejanja je bila onemogočena, zato »$1« ni mogoče uporabiti za zaščito strani entitete pred nepooblaščenim urejanjem.", "smw-edit-protection-auto-update": "Semantični MediaWiki je posodobil status zaščite v skladu z lastnostjo »Je zaščiteno pred urejanjem«.", "smw-edit-protection-enabled": "Zaščiteno pred urejanjem (Semantični MediaWiki)", "smw-patternedit-protection": "Ta stran je zašč<PERSON>na in jo lahko urejajo samo uporabniki z ustreznim [https://www.semantic-mediawiki.org/wiki/Help:Permissions dovoljenjem] <code>smw-patternedit</code>.", "smw-property-predefined-edip": "»$1« je vnaprej določena lastnost, ki jo zagotavlje [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki] za oz<PERSON>itev, ali je stran zaščitena za urejanje ali ne.", "smw-property-predefined-long-edip": "Čeprav je vsak uporabnik kvalificiran za dodajanje te lastnosti subjektu, lahko ureja ali prekliče za<PERSON><PERSON><PERSON> entitete, potem ko je dodana, samo uporabnik z namenskim dovoljenjem.", "smw-query-reference-link-label": "Sklic poizvedbe", "smw-format-datatable-emptytable": "V tabeli ni razpoložljivih podatkov", "smw-format-datatable-info": "Prikaz vnosov od _START_ do _END_ od _TOTAL_", "smw-format-datatable-infoempty": "Prikaz od 0 do 0 od 0 vnosov", "smw-format-datatable-infofiltered": "(filtrirano iz _MAX_ skupnih vnosov)", "smw-format-datatable-infothousands": ",", "smw-format-datatable-lengthmenu": "Prikaz _MENU_ vnosov", "smw-format-datatable-loadingrecords": "Nalaganje ...", "smw-format-datatable-processing": "O<PERSON><PERSON>ovan<PERSON> ...", "smw-format-datatable-search": "Iskanje:", "smw-format-datatable-zerorecords": "Ni najdenih ustrezajočih zapisov", "smw-format-datatable-first": "Prvi", "smw-format-datatable-last": "<PERSON><PERSON><PERSON>", "smw-format-datatable-next": "<PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-previous": "Prejšnji", "smw-format-datatable-sortascending": ": aktivirajte za razvrščanje stolpcev naraščajoče", "smw-format-datatable-sortdescending": ": aktivirajte za razvrščanje stolpcev padajoče", "smw-format-datatable-toolbar-export": "Izvoz", "smw-format-list-other-fields-open": " (", "smw-format-list-other-fields-close": ")", "smw-category-invalid-redirect-target": "Kategorija »$1« vsebuje neveljaven cilj preusmeritve v nekategorijski imenski prostor.", "smw-parser-function-expensive-execution-limit": "Funkcija razčlenjevalnika je dosegla omejitev za drage izvedbe (glejte konfiguracijski parameter [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "Semantični MediaWiki bo osvežil trenutno stran, pri <PERSON><PERSON><PERSON> bo potrebna naknadna obdelava podatkov.", "apihelp-smwinfo-summary": "Modul API za pridobivanje informacij o statističnih podatkih Semantičnega MediaWikija in drugih metainformacijah.", "apihelp-ask-summary": "Modul API za poizvedovanje po Semantičnem MediaWikiju z uporabo jezika »ask«.", "apihelp-askargs-summary": "Modul API za poizvedovanje po Semantičnem MediaWikiju z uporabo jezika »ask« kot seznama pogojev, <PERSON><PERSON><PERSON><PERSON> in parametrov.", "apihelp-browsebyproperty-summary": "Modul API za pridobivanje informacij o lastnostih ali seznamu lastnosti.", "apihelp-browsebysubject-summary": "Modul API za pridobivanje informacij o temi.", "apihelp-smwtask-summary": "Modul API za izvajanje opravil, povezanih s Semantićnim MediaWikijem (samo za interno uporabo, ne za javno uporabo).", "apihelp-smwbrowse-summary": "Modul API za podporo dejavnosti brskanja za različne vrste entitet v Semantičnem MediaWikiju.", "apihelp-ask-parameter-api-version": "Oblikovanje izhoda:\n;2:<PERSON>zv<PERSON><PERSON> združljiva oblika z uporabo {} za seznam zadetkov.\n;3:<PERSON><PERSON><PERSON><PERSON> oblika z uporabo [] za seznam zadetkov.", "apihelp-smwtask-param-task": "Določa vrsto opravila", "apihelp-smwtask-param-params": "Parametri, kodirani z JSON, ki ustrezajo zahtevi za izbrano vrsto opravila", "smw-apihelp-smwtask-example-update": "Zgled izvajanja opravila posodabljanja za določen predmet:", "smw-api-invalid-parameters": "Neveljavni parametri, »$1«", "smw-parser-recursion-level-exceeded": "Med postopkom razčlenjevanja je bila presežena raven $1 rekurzij. <PERSON><PERSON><PERSON><PERSON>, da preverite strukturo predloge ali po potrebi prilagodite konfiguracijski parameter <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "Prikazujem $1 {{PLURAL:$1|stran|strani}}, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to lastnost.", "smw-property-page-list-search-count": "Prikazujem $1 {{PLURAL:$1|stran|strani}} z uporabo te lastnosti z ujemanjem vrednosti »$2«.", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter Iskalni filter] omogoč<PERSON> vkl<PERSON>čitev [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions poizvedbenih izrazov], kot sta <code>~</code> ali <code>!</code>. Izbrani [https://www.semantic-mediawiki.org/wiki/Query_engine poizvedbeni mehanizem] lahko podpira tudi ujemanje brez razlikovanja med velikimi in malimi črkami ali druge kratke izraze, kot je:\n\n* <code>in:</code> zadetek mora vsebovati izraz, npr. »<code>in:Foo</code>«\n\n* <code>not:</code> zadetek ne sme vsebovati izraza, npr. »<code>not:Bar</code>«", "smw-property-reserved-category": "Kategorija", "smw-category": "Kategorija", "smw-datavalue-uri-invalid-scheme": "»$1« ni navedeno kot veljavna shema URI.", "smw-datavalue-uri-invalid-authority-path-component": "Ugotovljeno je bilo, da »$1« vsebuje neveljavno pooblastilo ali komponento poti »$2«.", "smw-browse-property-group-title": "<PERSON><PERSON><PERSON>", "smw-browse-property-group-label": "<PERSON><PERSON><PERSON> sku<PERSON>", "smw-browse-property-group-description": "<PERSON><PERSON> sku<PERSON>", "smw-property-predefined-ppgr": "»$1« je vnaprej dolo<PERSON> lastnost, ki identificira entitete (predvsem kategorije), ki se uporabljajo kot primerki združevanja za lastnosti in jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-filter": "Filter", "smw-section-expand": "Razš<PERSON> raz<PERSON>", "smw-section-collapse": "<PERSON><PERSON><PERSON> r<PERSON>", "smw-ask-format-help-link": "Fomat [https://www.semantic-mediawiki.org/wiki/Help: $1 _format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Plonkec", "smw-personal-jobqueue-watchlist": "Nadzorni seznam čakalne vrste opravil", "smw-personal-jobqueue-watchlist-explain": "Številke označujejo oceno vnosov v čakalni vrsti opravil, ki čakajo na izvedbo.", "smw-property-predefined-label-skey": "Razvrstilni ključ", "smw-processing": "Obdelovanje&nbsp;...", "smw-loading": "Nalaganje&nbsp;...", "smw-fetching": "Pridobivanje&nbsp;...", "smw-preparing": "Pripravljanje&nbsp;...", "smw-expand": "Razširi", "smw-collapse": "Strn<PERSON>", "smw-copy": "<PERSON><PERSON><PERSON>", "smw-copy-clipboard-title": "Kopira vsebino v odložišče", "smw-jsonview-expand-title": "<PERSON><PERSON><PERSON><PERSON> pogled JSON", "smw-jsonview-collapse-title": "<PERSON><PERSON> pogled JSON", "smw-jsonview-search-label": "<PERSON><PERSON><PERSON><PERSON>:", "smw-redirect-target-unresolvable": "Cilj je nerazrešljiv zaradi razloga »$1«", "smw-types-title": "Tip: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Spreminjanje vsebinskega modela [https://www.semantic-mediawiki.org/wiki/Help:<PERSON><PERSON>a strani sheme] ni dovoljeno.", "smw-schema-namespace-edit-protection": "Ta stran je zašč<PERSON>na in jo lahko urejajo samo uporabniki z ustreznim [https://www.semantic-mediawiki.org/wiki/Help:Permissions dovoljenjem]  <code>smw-schemaedit</code>.", "smw-schema-namespace-edit-protection-by-import-performer": "To stran je uvozi<PERSON> naveden<PERSON> [https://www.semantic-mediawiki.org/wiki/Import_performer i<PERSON><PERSON><PERSON><PERSON><PERSON> uvo<PERSON>]. To pomeni, da je spreminjanje vsebine te strani omejeno samo na navedene uporabnike.", "smw-schema-error-title": "{{PLURAL:$1|Napaka|Napake}} validacije", "smw-schema-error-schema": "<PERSON><PERSON> pre<PERSON> '''$1''' je odkrila naslednje nedoslednosti:", "smw-schema-error-miscellaneous": "<PERSON><PERSON> napaka ($1)", "smw-schema-error-validation-json-validator-inaccessible": "Validator JSON »<b>$1</b>« ni dostopen (ali <PERSON>) in je razlog, zakaj datote<PERSON> »$2« ni mogoče pregledati, kar preprečuje shranjevanje ali spreminjanje trenutne strani.", "smw-schema-error-validation-file-inaccessible": "Datoteka za preverjanje veljavnosti »$1« je nedostopna.", "smw-schema-error-violation": "[\"$1\", \"$2\"]", "smw-schema-error-type-missing": "<PERSON><PERSON><PERSON><PERSON> tip, da bi bila prepoznana in uporabna v [https://www.semantic-mediawiki.org/wiki/Help:Schema imenskem prostoru shem].", "smw-schema-error-type-unknown": "Tip »$1« ni registriran in ga ni mogoče uporabiti za vsebino v imenskem prostoru [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-error-json": "Napaka JSON-a: »$1«", "smw-schema-error-input": "Preverjanje vnosa je odkrilo naslednje težave, ki jih je treba odpraviti, preden lahko vsebino shranite. Na strani [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling pomoči za sheme] lahko najdete nekaj nasvetov o tem, kako odstraniti nedoslednosti ali odpraviti težave z vnosom sheme.", "smw-schema-error-input-schema": "Shema pre<PERSON> '''$1''' je odkrila naslednje nedoslednosti, ki jih je treba odpraviti, preden bo mogoče shraniti vsebino. Na strani [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling pomoči za sheme] lahko najdete nekaj nasvetov o tem, kako odpraviti te težave.", "smw-schema-error-title-prefix": "Ta vrsta sheme z<PERSON>, da se naslov sheme začne s predpono »$1«.", "smw-schema-validation-error": "Tip »$1« ni registriran in ga ni mogoče uporabiti za vsebino v imenskem prostoru [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-validation-schema-title": "<PERSON><PERSON>", "smw-schema-summary-title": "Povzetek", "smw-schema-title": "<PERSON><PERSON>", "smw-schema-usage": "Uporaba", "smw-schema-type": "Tip sheme", "smw-schema-type-description": "Opis vrste", "smw-schema-description": "Opis sheme", "smw-schema-description-link-format-schema": "Ta tip sheme podpira opredelitev značilnosti za ustvarjanje na kontekst odzivnih povezav v povezavi z dodeljeno lastnostjo [[Property:Formatter schema|sheme oblikovalnika]].", "smw-schema-description-search-form-schema": "Ta vrsta sheme podpira opredelitev vnosnih obrazcev in lastnosti za profil [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch razširjenega iskanja], pri <PERSON><PERSON>er vsebuje navodila za generiranje vnosnih polj, opredelitev privzetih imenskih prostov ali pripis predponskih izrazov iskalnemu zahtevku.", "smw-schema-description-property-profile-schema": "Ta tip sheme podpira opredelitev profila za pripis značilnosti dodeljeni lastnosti in njenim označevalnim vrednostim.", "smw-schema-description-facetedsearch-profile-schema": "Ta tip shem podpira definicijo profilov, ki se uporabljajo kot del okolja [[Special:FacetedSearch|fasetnega iskanja]].", "smw-schema-description-property-group-schema": "Ta vrsta sheme podpira opredelitev [https://www.semantic-mediawiki.org/wiki/Help:Property_group lastnosti skupin] za pomoč pri strukturiranju [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse brskalnega] vmesnika.", "smw-schema-description-property-constraint-schema": "To podpira definicijo omejitvenih pravil za primerek lastnosti in vrednosti, ki so mu dodeljene.", "smw-schema-description-class-constraint-schema": "Ta tip sheme podpira opredelitev omejevalnih pravil za skupinsko instanco (tj. kategorijo).", "smw-schema-tag": "{{PLURAL:$1|Oznaka|Oznake}}", "smw-property-predefined-constraint-schema": "»$1« je vnaprej določena lastnost za opredelitev omejitvene sheme, ki jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-schema-desc": "»$1« je vnaprej določena lastnost za shranjevanje opisa sheme, ki jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-schema-def": "»$1« je vnaprej določena lastnost za shranjevanje vsebine sheme, ki jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-schema-tag": "»$1« je vnaprej določ<PERSON> lastnost, ki jo za identifikacijo zbirke shem zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-schema-tag": "<PERSON><PERSON><PERSON>, ki ozna<PERSON>uje sheme s podobno vsebino ali značilnostmi.", "smw-property-predefined-schema-type": "»$1« je vnaprej določena lastnost, ki opisuje tip predstavnosti naložene datoteke, ki jo zagotavlja [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantični MediaWiki].", "smw-property-predefined-long-schema-type": "Vsak [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type tip] uporablja lastno interpretacijo skladenjski<PERSON> elementov in aplikacijskih pravil ter ga je mogoče izraziti z [https://www.semantic -mediawiki.org/wiki/Help:Schema#validation validacij<PERSON> shemo].", "smw-ask-title-keyword-type": "Is<PERSON><PERSON> kl<PERSON> besede", "smw-ask-message-keyword-type": "To iskanje se ujema s poogojem <nowiki><code>$1</code></nowiki>.", "smw-remote-source-unavailable": "Ni mogoče vzpostaviti povezave z oddaljenim ciljem »$1«.", "smw-remote-source-disabled": "Vir '''$1''' je onemogočil podporo za oddaljene zahtevke!", "smw-remote-source-unmatched-id": "Vir '''$1''' se ne ujema z različico Semantičnega MediaWikija, ki podpira oddaljeni zahtevek.", "smw-remote-request-note": "Rezultat je pridobljen iz '''$1''' oddaljenega vira in verjetno bo ustvarjena vsebina vsebovala informacije, ki niso na voljo znotraj trenutnega vikija.", "smw-remote-request-note-cached": "Rezultat je '''predpomnjen''' iz oddaljenega vira '''$1''' in verjetno bo ustvarjena vsebina vsebovala informacije, ki niso na voljo znotraj trenutnega vikija.", "smw-parameter-missing": "<PERSON><PERSON>a parameter »$1«.", "smw-property-tab-usage": "Uporaba", "smw-property-tab-profile-schema": "<PERSON><PERSON><PERSON> she<PERSON>", "smw-property-tab-redirects": "<PERSON><PERSON><PERSON><PERSON>", "smw-property-tab-subproperties": "Podlastnosti", "smw-property-tab-errors": "Neustrezne dodelitve", "smw-property-tab-constraint-schema": "<PERSON><PERSON><PERSON><PERSON><PERSON> shema", "smw-property-tab-constraint-schema-title": "<PERSON><PERSON><PERSON> o<PERSON>a shema", "smw-property-tab-specification": ".. ve<PERSON>", "smw-concept-tab-list": "Seznam", "smw-concept-tab-errors": "Napake", "smw-ask-tab-result": "Rezultat", "smw-ask-tab-extra": "Dodatno", "smw-ask-tab-debug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-code": "<PERSON><PERSON>", "smw-install-incomplete-tasks-title": "Nedokončana administrativna opravila", "smw-install-incomplete-intro": "{{PLURAL:$2|1 opravilo je nedokon<PERSON>ano|$2 opravili sta nedokončani|$2 opravila so nedokončana|$2 opravil je nedokončanih}} ali [[Special:PendingTaskList|{{PLURAL:$2|čaka|čakata|čakajo|čaka}}]] na dokončanje {{PLURAL:$1|namestitve|nadgradnje}} [https://www.semantic-mediawiki.org Semantičnega MediaWikija]. Dokonča {{PLURAL:$2|ga|ju|jih}} lahko administrator ali uporabnik z ustreznimi pravicami. To je treba storiti pred dodajanjem novih podatkov, da se izognete nedoslednostim.", "smw-install-incomplete-intro-note": "To sporočilo bo iz<PERSON><PERSON>, ko bodo razrešena vsa ustrezna opravila.", "smw-pendingtasks-intro-empty": "Nobeno opravilo ni razvrščeno kot čakajoče, nepopolno ali odprto v povezavi s Semantičnim MediaWikijem.", "smw-pendingtasks-intro": "Ta stran zagotavlja informacije o opravilih, ki so razvrščena kot čakajoča, nepopolna ali odprta v povezavi s Semantičnim MediaWikijem.", "smw-pendingtasks-setup-no-tasks-intro": "Namestitev (ali nad<PERSON>) je končana; trenutno ni čakajočih ali odprtih opravil.", "smw-pendingtasks-tab-setup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-updateentitycollation-incomplete": "Pred kratkim je bila spremenjena nastavitev <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> in zahteva, da se izvede skript <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code>, da bodo entitete posodobljene in bodo vsebovale pravilno vrednost razvrstilnega polja.", "smw-updateentitycountmap-incomplete": "V nedavni izdaji je bilo dodano polje <code>smw_countmap</code> in zahteva, da se izvede skript <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code>, da lahko funkcije dostopajo do vsebine tega polja.", "smw-populatehashfield-incomplete": "Izpolnitev polja <code>smw_hash</code> je bila med nastavitvijo preskočena. Izvesti je treba skript <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-populate-hash-field": "Izpolnitev polja <code>smw_hash</code> je bila med nastavitvijo preskočena. Izvesti je treba skript <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code> je bil izbran kot [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore privzeta shramba], vendar razširitev ni mogla najti nobenega zapisa, da je bil izveden skript <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code>; po navodilih zaženite skript.", "smw-elastic-rebuildelasticindex-run-incomplete": "<code>ElasticStore</code> je bil izbran kot [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore privzeta shramba], vendar razširitev ni mogla najti nobenega zapisa, da je bil izveden skript <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code>; po navodilih zaženite skript.", "smw-pendingtasks-setup-intro": "{{PLURAL:$1|Namestitev|Nadgradnja}} <b>Semantičnega MediaWikija</b> je naslednja opravila razvrstila kot [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade nepopolna] in naj jih razreši administrator (ali uporabnik z ustreznimi pravicami), preden uporabniki nadaljujejo ustvarjanje ali spreminjanje vsebine.", "smw-pendingtasks-setup-tasks": "Opravila", "smw-filter-count": "<PERSON><PERSON><PERSON><PERSON>", "smw-es-replication-check": "Preverjanje replikacije (Elasticsearch)", "smw-es-replication-error": "Težava z replikacijo Elasticsearch", "smw-es-replication-file-ingest-error": "Težava pri indeksiranju datoteke", "smw-es-replication-maintenance-mode": "Vzdrževanje Elasticsearch", "smw-es-replication-error-missing-id": "Pri spremljanju replikacije je bilo ugotovljeno, da v zaledju Elasticsearch manjka članek »$1« (ID: $2).", "smw-es-replication-error-divergent-date": "Pri spremljanju replikacije je bilo u<PERSON>l<PERSON>, da <b>datum modifika<PERSON></b> pri <PERSON><PERSON><PERSON> »$1« (ID: $2) kaže neskladnost.", "smw-es-replication-error-divergent-date-short": "Za primerjavo so bili uporabljeni naslednji podatki o datumu:", "smw-es-replication-error-divergent-date-detail": "Referenčni datum spremembe:\n*Elasticsearch: $1 \n*Podatkovna zbirka: $2", "smw-es-replication-error-divergent-revision": "Pri spremljanju replikacije je bilo u<PERSON>l<PERSON>, da <b>povezana redakcija</b> članka »$1« (ID: $2) kaže neskladnost.", "smw-es-replication-error-divergent-revision-short": "Za primerjavo so bili uporabljeni naslednji podatki o povezani redakciji:", "smw-es-replication-error-divergent-revision-detail": "Referencirana povezana redakcija:\n*Elasticsearch: $1 \n*<PERSON><PERSON>atkov: $2", "smw-es-replication-error-maintenance-mode": "Replikacija Elasticsearch je trenutno omejena, ker deluje v [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>načinu vzdr<PERSON>evanja</b>]; spremembe entitet in strani <b>niso</b> tako<PERSON> vidne, zadetki poizvedb pa lahko vsebujejo zastarele informacije.", "smw-es-replication-error-no-connection": "Nadzor replikacije ne more izvesti nobenega preverjanja, ker ne more vzpostaviti povezave z gručo Elasticsearch.", "smw-es-replication-error-bad-request-exception": "Obravnavalnik povezave Elasticsearch je izvrgel izjemo slabega zahtevka (»napaka navzkrižja http 400«), ki kaže na vztrajno težavo pri podvajanju in iskalnih zahtevkih.", "smw-es-replication-error-other-exception": "Obravnavalnik povezav ElasticSearch je izvrgel izjemo »$1«.", "smw-es-replication-error-suggestions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> je, da stran uredite ali os<PERSON>ž<PERSON>, da odstranite neskladje. Če težava vztraja, preverite sa<PERSON> gru<PERSON>o Elasticsearch (alokator, izjeme, diskovni prostor idr.).", "smw-es-replication-error-suggestions-maintenance-mode": "<PERSON><PERSON><PERSON><PERSON>, da se obrnete na administratorja vikija, da prever<PERSON>, ali trenutno poteka [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild obnova indeksa] ali pa parameter <code>refresh_interval</code> ni nastavljen na pričakovano privzeto vrednost.", "smw-es-replication-error-suggestions-no-connection": "<PERSON><PERSON><PERSON><PERSON>, da se obrnete na administratorja vikija in prijavite težavo »ni povezave«.", "smw-es-replication-error-suggestions-exception": "Preverite dnevnike za informacije o statusu Elasticsearch, njihovih indeksih in morebitnih težavah z napačno konfiguracijo.", "smw-es-replication-error-file-ingest-missing-file-attachment": "Pri nadzoru replikacije je bilo u<PERSON>l<PERSON>, da »$1« manjka označitev [[Property:File attachment|datotečne priloge]], kar ka<PERSON>, da se procesor za indeksiranje datotek ni zagnal ali ni končal.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "<PERSON><PERSON><PERSON><PERSON>, da je opravilo [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion indeksiranja datotek] na<PERSON><PERSON>ovano in se izvede, preden sta označitev in indeks datoteke na voljo.", "smw-report": "<PERSON><PERSON><PERSON><PERSON>", "smw-legend": "<PERSON>a", "smw-datavalue-constraint-schema-category-invalid-type": "Označ<PERSON> »$1« ni veljavna za kategorijo; potreben je tip »$2«.", "smw-datavalue-constraint-schema-property-invalid-type": "Označ<PERSON> »$1« ni veljavna za lastnost; potreben je tip »$2«.", "smw-entity-examiner-check": "V ozadju se {{PLURAL:$1|izvaja preverjanje podatkov|izvajajo preverjanja podatkov}}.", "smw-entity-examiner-indicator": "Plošča za težave z datotekami", "smw-entity-examiner-deferred-check-awaiting-response": "Preverjanje podatkov »$1« trenutno čaka na odgovor zaledja.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "<PERSON><PERSON><PERSON><PERSON>", "smw-entity-examiner-associated-revision-mismatch": "Redakcija", "smw-entity-examiner-deferred-fake": "Lažni prikaz", "smw-entity-examiner-indicator-suggestions": "Pri preverjanju entitete {{PLURAL:$1|je bila najdena naslednja težava|sta bili najdeni naslednji težavi|so bile najdene naslednje težave}}. Predl<PERSON><PERSON>, da {{PLURAL:$1|jo|ju|jih}} natan<PERSON><PERSON> pregledate in ustrezno {{PLURAL:$1|ukrepate}}.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Omejitev|Omejitve}}", "smw-indicator-revision-mismatch": "Redakcija", "smw-indicator-revision-mismatch-error": "Preverjanje [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner redakcij strani] je odkrilo neujemanje med redakcijo, navedeno v MediaWikiju, in tisto, ki je za to entiteto povezana v Semantičnem MediaWikiju.", "smw-indicator-revision-mismatch-comment": "Neujemanje običajno pomeni, da je nek proces prekinil operacijo shranjevanja v Semantičnem MediaWikiju. Priporočljivo je, da pregledate strežniške dnevnike in poiščete izjeme ali druge napake.", "smw-facetedsearch-intro-text": "[https://www.semantic-mediawiki.org/wiki/Faceted_search <b>Fasetno iskanje</b> ] Semantičnega MediaWikija uporabnikom ponuja preprost vmesnik za hitro zožitev zadetkov poizvedbe iz pogoja z uporabo fasetnih prikazov, ustvarjenih iz odvisnih lastnosti in kategorij.", "smw-facetedsearch-intro-tips": "* Za iskanje raz<PERSON><PERSON> ka<PERSON>, lastnosti ali konceptov za izdelavo nabora pogojev uporabite <code>category:?</code> , <code>property:?</code> ali <code>concept:?</code>.\n* Za opis stanja uporabite skladnjo #ask (npr.<nowiki> <code>[[Category:Foo]]</code></nowiki> )\n* Za ustvarjanje zapletenih pogojev uporabite »ALI«, »IN« ali druge poizvedbene izraze.\n* Za ujemanja celotnega besedila je mogoče uporabiti izraze, kot je <code>in:</code> ali <code>phrase:</code>, če je izbran [https ://www.semantic-mediawiki.org/wiki/Query_engine poizvedbeni pogon], ki podpira te izraze.", "smw-facetedsearch-profile-label-default": "Privzeti profil", "smw-facetedsearch-intro-tab-explore": "Raziskuj", "smw-facetedsearch-intro-tab-search": "<PERSON><PERSON><PERSON><PERSON>", "smw-facetedsearch-explore-intro": "Izberite zbirko in začnite brskati.", "smw-facetedsearch-profile-options": "Možnosti profila", "smw-facetedsearch-size-options": "Možnosti ostranjevanja", "smw-facetedsearch-order-options": "Možnosti razvrščanja", "smw-facetedsearch-format-options": "Možnosti prikaza", "smw-facetedsearch-format-table": "<PERSON><PERSON><PERSON>", "smw-facetedsearch-input-filter-placeholder": "Filtriranje&nbsp;...", "smw-facetedsearch-no-filters": "<PERSON><PERSON> filtrov.", "smw-facetedsearch-no-filter-range": "<PERSON>rez razpona filtra.", "smw-facetedsearch-no-output": "Za izbrani format »$1« ni bil na voljo noben izhod.", "smw-facetedsearch-clear-filters": "Počisti {{PLURAL:$1|filter|filtre}}", "smw-specials-facetedsearch-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:FacetedSearch", "smw-search-placeholder": "Iskanje&nbsp;...", "smw-listingcontinuesabbrev": "na<PERSON><PERSON>.", "smw-showingresults": "Prikazujem do '''$1''' {{PLURAL:$1|zadetek|zadetka|zadetke|zadetkov}}, za<PERSON><PERSON><PERSON>i s št. '''$2'''."}