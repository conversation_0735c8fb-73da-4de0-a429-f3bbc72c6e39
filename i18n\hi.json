{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "AnupamM", "<PERSON><PERSON><PERSON><PERSON>", "Jayprakash12345", "<PERSON><PERSON><PERSON><PERSON>", "Nitin1485", "Phoenix303", "Proabscorp!", "Rajatkatiyar10", "Sachinkatiyar", "<PERSON><PERSON><PERSON><PERSON>", "Sfic", "Shypoetess", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Upendradutt93", "Wikiuser13", "गोपाल", "चक्रपाणी", "जन<PERSON> राज भट्ट", "రహ్మానుద్దీన్"]}, "smw-desc": "आपके विकि को मशीन और मनुष्य - दोनों के लिए अधिक सुलभ बनाते हुए ([https://www.semantic-mediawiki.org/wiki/Help:User_manual ऑनलाइन प्रलेख])", "smw-error": "त्रुटि", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ सिमैंटिक मीडियाविकि] स्थापित और सक्षम है मगर इसमें एक उचित [https://www.semantic-mediawiki.org/wiki/Help:Upgrade अपग्रेड कुँजी] नहीं है।", "smw-upgrade-release": "प्रका<PERSON>न", "smw-upgrade-progress": "प्रगति", "smw-upgrade-progress-explain": "एक अनुमान कि अपग्रेड कब खत्म होता, क्योंकि यह पता लगाना मुश्किल है और यह डेटा रिपॉज़िटरी के आकार तथा उपलब्ध हार्डवेयर पर निर्भर होता है, साथ ही, बड़े विकियों पर इसे पूरा होने में ज़्यादा समय लग सकता है।\n\nप्रगति के बारे में अधिक जानकारी पाने के लिए कृपया अपने लोकल प्रबंधक से संपर्क करें।", "smw-upgrade-progress-create-tables": "टेबल और इनडेक्स बनाए (या अपडेट किए) जा रहे हैं...", "smw-upgrade-progress-post-creation": "सृष्टि के बाद के कार्य चलाए जा रहे हैं...", "smw-upgrade-progress-table-optimization": "टेबल ऑप्टिमाइज़ेशन्स चलाए जा रहे हैं...", "smw-upgrade-progress-supplement-jobs": "निम्न कार्य जोड़े जा रहे हैं...", "smw-upgrade-error-title": "त्रुटि » सिमैंटिक मीडियाविकि", "smw-upgrade-error-why-title": "मुझे यह पृष्ठ क्यों दिख रहा है?", "smw-upgrade-error-why-explain": "सिमैंटिक मीडियाविकि के आंतरिक डेटाबेस संरचना में बदलाव आया है और इसे पूरी तरह काम करने के लिए कुछ और बदलावों की ज़रूरत है। इसके कई कारण हो सकते हैं, जैसे:\n* अतिरिक्त ठीक किए गए गुणधर्मों का जोड़ा जाना (जिसमें अतिरिक्त टेबल्स की ज़रूरत है)\n* एक अपग्रेड में टेबल्स या इनडेक्सों पर कुछ ऐसे बदलाव आए हैं जिनकी वजह से डेटा का इस्तेमाल करने से पहले अवरोधन ज़रूरी है\n* संग्रह या क्वेरी इंजन में बदलाव", "smw-upgrade-error-how-title": "मैं इस त्रुटि को कैसे ठीक करूँ?", "smw-upgrade-error-how-explain-admin": "एक प्रबंधक (या प्रबंधक के अधिकारों वाले किसी भी सदस्य) को या तो मीडियाविकि का [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] चलाना होगा या फिर सिमैंटिक मीडियाविकि का [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] अनुरक्षण स्क्रिप्ट।", "smw-upgrade-error-how-explain-links": "आप अधिक जानकारी के लिए इन पृष्ठों से मदद ले सकते हैं:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation स्थापना] के अनुदेश\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting समस्या निवारण] के लिए सहायता पृष्ठ", "smw-extensionload-error-why-title": "मुझे यह पृष्ठ क्यों दिख रहा है?", "smw-extensionload-error-why-explain": "इस एक्सटेंशन को <code>enableSemantics</code> की मदद से लोड <b>न</b> करके सीधे <code>wfLoadExtension( 'SemanticMediaWiki' )</code> जैसे किसी तरीके से सक्षम किया गया है।", "smw-extensionload-error-how-title": "मैं इस त्रुटि को कैसे ठीक करूँ?", "smw-extensionload-error-how-explain": "एक्सटेंशन को सक्षम करने तथा नामस्थान घोषणाओं और लंबित कॉन्फ़िगरेशन्स के साथ टकराव से बचने के लिए <code>enableSemantics</code> का इस्तेमाल करना ज़रूरी है, जो सुनिश्चित करेगा कि उचित वेरिएबलों को एक्सटेंशन को लोड करने से पहले <code>ExtensionRegistry</code> के ज़रिए सेट कर दिया जाता है।\n\nअधिक जानकारी के लिए कृपया [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] सहायता पृष्ठ देखें।", "smw-upgrade-maintenance-title": "अनुरक्षण » सिमैंटिक मीडियाविकि", "smw-upgrade-maintenance-why-title": "मुझे यह पृष्ठ क्यों दिख रहा है?", "smw-upgrade-maintenance-note": "प्रणाली में इस समय [https://www.semantic-mediawiki.org/ सिमैंटिक मीडियाविकि] एक्सटेंशन के साथ इसके डेटा रिपॉज़िटरी का एक [https://www.semantic-mediawiki.org/wiki/Help:Upgrade अपग्रेड] चल रहा है, और हम चाहते हैं कि आप थोड़ा धैर्य रखें जब तक अपग्रेड खत्म हो और विकि दोबारा पठनीय हो।", "smw-upgrade-maintenance-explain": "एक्सटेंशन इसके प्रभाव और डाउनटाइम को न्यूनतम मात्रा में रखने के लिए अपने ज़्यादातर काम <code>update.php</code> के बाद करता है, मगर डेटाबेस में कुछ बदलावों को इसके पहले करना ज़रूरी है, वरना डेटा में असंगतता आ जाएगी। ऐसे कुछ कार्य हैं:\n* टेबल संरचना बदलना जैसे नए फ़ील्ड्स जोड़ना या मौजूदा फ़ील्ड्स को संपादित करना\n* टेबल इनडेक्स बदलना या जोड़ना\n* टेबल ऑप्टिमाइज़ेशन्स चलाना (जब सक्षम हो)", "smw-semantics-not-enabled": "सिमैंटिक मीडियाविकि कार्यक्षमता इस विकि के लिए अक्षम है।", "smw_viewasrdf": "RDF फ़ीड", "smw_finallistconjunct": ", और", "smw-factbox-head": "... \"$1\" के बारे में अधिक", "smw-factbox-facts": "तथ्य", "smw-factbox-facts-help": "किसी सदस्य द्वारा बनाए गए बयान और तथ्य दिखाता है", "smw-factbox-attachments": "संलग्नक", "smw-factbox-attachments-value-unknown": "लागू नहीं", "smw-factbox-attachments-is-local": "लोकल है", "smw-factbox-attachments-help": "उपलब्ध संलग्नक दिखाता है", "smw-factbox-facts-derived": "प्राप्त तथ्य", "smw-factbox-facts-derived-help": "तथ्य दिखाता है जिन्हें नियमों से या फिर दूसरे तार्किक तकनीकों की मदद से प्राप्त किया गया है", "smw_isspecprop": "यह गुणधर्म इस विकि पर एक विशेष गुणधर्म है।", "smw-concept-cache-header": "कैश प्रयोग", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count परिकल्पना कैश] में {{PLURAL:$1|'''एक''' सत्ता है|'''$1''' सत्ते हैं}} ($2)।", "smw-concept-no-cache": "कोई कैश उपलब्ध नहीं।", "smw_concept_description": "\"$1\" परिकल्पना का विवरण", "smw_no_concept_namespace": "परिकल्पनाओं को सिर्फ Concept: नामस्थान में परिभाषित किया जा सकता है।", "smw_multiple_concepts": "हर परिकल्पना पृष्ठ पर सिर्फ एक ही परिकल्पना की परिभाषा हो सकती है।", "smw_concept_cache_miss": "इस समय परिकल्पना \"$1\" का इस्तेमाल नहीं किया जा सकता, क्योंकि विकि कॉन्फ़िरेशन के अनुसार इसके ऑफ़लाइन कंप्यूट किया जाएगा।\nअगर समस्या कुछ समय बाद भी रहती है, अपने साइट प्रबंधक से कहें कि वे इस परिकल्पना को उपलब्ध कराएँ।", "smw_noinvannot": "विलोम गुणधर्मों पर वैल्यू जोड़े नहीं जा सकते।", "version-semantic": "सिमैंटिक एक्सटेंशन्स", "smw_baduri": "\"$1\" के रूप में URI-ओं की अनुमति नहीं है।", "smw_printername_count": "परिणाम गिनें", "smw_printername_csv": "CSV निर्यात", "smw_printername_dsv": "DSV निर्यात", "smw_printername_debug": "डीबग क्वेरी (विशेषज्ञों के लिए)", "smw_printername_embedded": "पृष्ठ की सामग्री एम्बेड करें", "smw_printername_json": "JSON निर्यात", "smw_printername_list": "सूची", "smw_printername_plainlist": "साधारण सूची", "smw_printername_ol": "संख्यात्मक सूची", "smw_printername_ul": "बिंदु सूची", "smw_printername_table": "टेबल", "smw_printername_broadtable": "विस्तृत टेबल", "smw_printername_template": "साँचा", "smw_printername_templatefile": "साँचा फ़ाइल", "smw_printername_rdf": "RDF निर्यात", "smw_printername_category": "श्रेणी", "validator-type-class-SMWParamSource": "टेक्स्ट", "smw-paramdesc-limit": "लौटाने के लिए परिणामों की अधिकतम संख्या", "smw-paramdesc-offset": "पहले परिणाम का ऑफ़सेट", "smw-paramdesc-headers": "हैडर्स/गुणधर्मों के नाम दिखाएँ", "smw-paramdesc-mainlabel": "मुखपृष्ठ के नाम को देने के लिए लेबल", "smw-paramdesc-link": "वैल्यूओं को कड़ियों के रूप में दिखाता है", "smw-paramdesc-intro": "क्वेरी के परिणामों से पहले दिखाने के लिए टेक्स्ट, अगर कोई परिणाम हुआ तो", "smw-paramdesc-outro": "क्वेरी के परिणामों के बाद दिखाने के लिए टेक्स्ट, अगर कोई परिणाम हुआ तो", "smw-paramdesc-default": "अगर कोई परिणाम न हुआ तो दिखाने के लिए टेक्स्ट", "smw-paramdesc-sep": "परिणामों के बीच विभाजक", "smw-paramdesc-propsep": "परिणाम में एंट्री के गुणधर्मों के बीच विभाजक", "smw-paramdesc-valuesep": "परिणाम के गुणधर्मों के वैल्यूओं के बीच विभाजक", "smw-paramdesc-showsep": "CSV फ़ाइल के शीर्ष पर विभाजक दिखाएँ (\"sep=<वैल्यू>\")", "smw-paramdesc-distribution": "सभी वैल्यू दिखाने की जगह उनके घटनों को गिनें, और उन्हें दिखाएँ।", "smw-paramdesc-distributionsort": "घटनों की संख्या के अनुसार वैल्यू के वितरण को छाँटें।", "smw-paramdesc-embedonly": "कोई शीर्षक नहीं प्रदर्शित करें", "smw-paramdesc-table-class": "टेबल के लिए सेट करने के लिए एक अतिरिक्त सीएसएस क्लास", "smw-paramdesc-rdfsyntax": "RDF सिंटैक्स का इस्तेमाल किया जायेगा", "smw-paramdesc-csv-sep": "एक कॉलम विभाजक निर्दिष्ट करता है", "smw-paramdesc-csv-valuesep": "एक वैल्यू विभाजक निर्दिष्ट करता है", "smw-paramdesc-csv-merge": "पंक्तियों और कॉमन्स को एक समान विषय पहचानकर्ता (यानी पहले कॉलम) से मर्ज करें", "smw-paramdesc-csv-bom": "आउटपुट फ़ाइल के ऊपर एक BOM (एंडियानेस को चिह्नित करने के लिए अक्षर) जोड़ें", "smw-paramdesc-dsv-separator": "उपयोग करने के लिए विभाजक", "smw-paramdesc-dsv-filename": "DSV फ़ाइल के लिए नाम", "smw-paramdesc-filename": "आउटपुट फाइल के लिए नाम", "smw-paramdesc-searchlabel": "खोज को जारी रखने के लिए पाठ", "smw-paramdesc-export": "निर्यात विकल्प", "smw-paramdesc-source": "वैकल्पिक क्वेरी स्रोत", "smw-printername-feed": "आरएसएस और एटम फीड", "smw-paramdesc-feedtype": "फीड के प्रकार", "smw-label-feed-description": "$1 $2 फीड", "smw_iq_moreresults": "… आगे के रिज़ल्ट", "smw_wrong_namespace": "केवल \"$1\" नामस्थान के पन्ने की यहाँ लिए जाते हैं।", "smw_manytypes": "गुणो के लिए एक से अधिक प्रकार परिभाषित", "smw_emptystring": "रिक्त स्ट्रिंग स्वीकार नहीं किए जाते हैं।", "smw_true_words": "सही,t,हां,y", "smw_false_words": "गलत,f,ना,n", "smw_nofloat": "“$1” यह संख्या नहीं हैं।", "smw_novalues": "कोई मान नहीं दिया गया।", "smw_nodatetime": "दिनांक \"$1\" समझने लायक नहीं है।", "smw_type_header": "“$1” प्रकारके गुणधर्म", "smw-propertylist-subproperty-header": "उपगुण", "smw-propertylist-redirect-header": "पर्यायवाची", "exportrdf": "आरडीएफ को पृष्ठ निर्यात करें", "smw_exportrdf_submit": "निर्यात", "uriresolver": "यूआरएलरिसोल्वर", "properties": "गुणधर्म", "smw-categories": "श्रेणियाँ", "smw-special-property-searchform": "प्रदर्शित गुण जिसमें शामिल हैं:", "smw-special-property-searchform-inputinfo": "निविष्ट कारक संवेदनशील है और जब निष्पंदन के लिए प्रयुक्त होता है, केवल गुण जो स्थिति से मेल खाते हैं, प्रदर्शित होते हैं।", "smw-special-property-searchform-options": "विकल्प", "smw-special-wantedproperties-filter-label": "निष्पंदन", "smw-special-wantedproperties-filter-none": "कोई भी नहीं", "smw-special-wantedproperties-filter-unapproved": "अस्वीकृत", "concepts": "अवधारणा", "smw-special-concept-header": "अवधारणाओं की सूची", "smw-special-concept-empty": "कोई अवधारणा नहीं मिली।", "unusedproperties": "इस्तेमाल न किये हुए गुणधर्म", "smw-unusedproperty-template": "$2 प्रकारक<PERSON> $1", "wantedproperties": "चाहिये होनेवाले गुणधर्म", "smw_purge": "टटका करौ", "smw-purge-failed": "ताज़ा करने में विफल रहा", "types": "प्र<PERSON><PERSON>र", "smw-special-types-no-such-type": "निर्दिष्ट डेटा प्रकार मौजूद नहीं है", "smw-statistics": "सिमेंटिक आँकड़े", "smw-statistics-property-total-legacy": "{{PLURAL:$1|गुण}} (कुल)", "smw-statistics-property-page": "{{PLURAL:$1|गुण}} (पृष्ठ के साथ पंजीकृत)", "smw-statistics-query-size": "क्वेरी आकार", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|उपवस्तु}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|डाटा प्रकार}}]]", "ask": "सेमैंटिक खोज", "smw_ask_sortby": "स्तंभ द्वारा क्रमबद्ध करें (वैकल्पिक)", "smw_ask_ascorder": "बढ़ते क्रम", "smw_ask_descorder": "घटते क्रम", "smw-ask-order-rand": "कोई भी", "smw_ask_submit": "परिणाम खोजें", "smw_ask_editquery": "क्वेरी संपादित करें", "smw_add_sortcondition": "[सॉर्टिंग शर्त जोड़ें]", "smw-ask-sort-add-action": "चुनी गई शर्त जोङें", "smw_ask_hidequery": "क्वेरी छुपाएँ", "smw_ask_help": "क्वेरी सहायता", "smw_ask_queryhead": "शर्त", "smw_ask_printhead": "प्रिंटआउट चयन", "smw_ask_printdesc": "(प्रति पंक्ति एक प्रॉपर्टी नाम जोड़ें)", "smw_ask_format_as": "इस रूप में स्वरूपित करें:", "smw_ask_defaultformat": "प्राथमिक", "smw_ask_otheroptions": "अन्य विकल्प", "smw_ask_show_embed": "एम्बेड कोड दिखाएं", "smw_ask_hide_embed": "एम्बेड कोड छुपाएं", "smw-ask-delete": "हटाएँ", "smw-ask-sorting": "सोर्टिंग", "smw-ask-options": "विकल्प", "smw-ask-options-sort": "चुने गए विकल्प", "smw-ask-format-options": "प्रारूप और विकल्प", "smw-ask-parameters": "पैमाने", "smw-ask-search": "खोजें", "smw-ask-debug": "दोषमार्जन", "smw-ask-no-cache": "कैश नही", "smw-ask-result": "परिणाम", "smw-ask-empty": "रिक<PERSON>त", "smw-ask-format": "प्रारूप", "smw-ask-format-selection-help": "चयनित प्रारूप के साथ सहायता के लिए: $1", "smw-ask-format-change-info": "प्रारूप संशोधित किया गया था और नए पैमाने और विज़ुअलाइजेशन विकल्पों से मिलान करने के लिए फिर से जाँच को निष्पादित करना आवश्यक है।", "searchbyproperty": "गुण अनुसार खोजें", "smw_sbv_property": "गुण:", "smw_sbv_value": "मान:", "smw_sbv_submit": "परिणाम खोजें", "browse": "विकि देखे", "smw_browselink": "गुण देखें", "smw_browse_go": "आगे बढ़ें", "smw_browse_no_outgoing": "इस पृष्ठ में कोई गुण नहीं है।", "smw_browse_no_incoming": "इस पृष्ठ से कोई गुण जुड़ा नहीं है।", "smw-browse-show-group": "समुह दिखाये", "smw-browse-hide-group": "समुह छुपाए", "smw-noscript": "इस प्रष्ठ या क्रिया पर काम करने के लिए जावास्क्रिप्ट आवश्यक है, कृपया अपने ब्राउज़र में जावास्क्रिप्ट सक्षम करें या जावास्क्रिप्ट समर्थित ब्राउज़र का उपयोग करें ताकि कार्यक्षमता का लाभ दिया जा सके और अनुरोध पर उपलब्ध कराया जाता है। आगे की सहायता के लिए, कृपया सहायता प्रष्ठ पर नजर डालें [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript]।", "smw_pp_docu": "या तो सभी सौंपे मूल्यों को प्राप्त करने के लिए एक पृष्ठ और संपत्ति या केवल एक संपत्ति दर्ज करें।", "smw_pp_from": "पृष्ठ से:", "smw_pp_type": "गुणधर्म:", "smw_pp_submit": "परिणाम खोजें", "smw_result_prev": "पिछला", "smw_result_next": "अगला", "smw_result_results": "परिणाम", "smw_result_noresults": "कोई परिणाम नहीं", "smw-admin-statistics-job-title": "कार्य आँकड़े", "smw_smwadmin_return": "$1 पर वापस जायें।", "smw-admin-deprecation-notice-title-notice": "आगा<PERSON>ी बदलाव", "smw-admin-deprecation-notice-title-removal": "हटाई गई सेटिंग", "smw_smwadmin_datarefreshbutton": "अनुसूची डेटा पुनर्निर्माण", "smw_smwadmin_datarefreshstop": "इस अद्यतन को रोकें", "smw-admin-job-scheduler-note": "निष्पादन के दौरान डेडलॉक परिस्थितियों से बचने के लिए इस खंड में अधिकांश गतिविधियां नौकरी के रूप में होती हैं। प्रसंस्करण के लिए [https://www.mediawiki.org/wiki/Manual:Job_queue कार्य अनुसूचक] जिम्मेदार है और यह महत्वपूर्ण है कि रखरखाव <code>runJobs.php</code> स्क्रिप्ट (यह भी देखें <code>$wgRunJobsAynync</code>) में एक उपयुक्त क्षमता है।", "smw-admin-outdateddisposal-active": "एक पुरानी संस्थाओं के निपटान की नौकरी निर्धारित की गई है।", "smw-admin-propertystatistics-active": "एक संपत्ति के आंकड़ों का पुनर्निर्माण कार्य निर्धारित किया गया है।", "smw-admin-fulltext-active": "एक पूर्ण-पाठ खोज पुनर्निर्माण कार्य निर्धारित किया गया है।", "smw-admin-supplementary-duplookup-title": "प्रतिरूप प्रविष्टियां", "smw-admin-supplementary-duplookup-docu": "यह पृष्ठ [https://www.semantic-mediawiki.org/wiki/Help:Entity_table इकाई तालिका] से प्रविष्टियों को सूचीबद्ध करता है जिन्हें डुप्लिकेट के रूप में वर्गीकृत किया गया है। प्रतिरूप प्रविष्टियां (यदि सभी में हों) केवल एक दुर्लभ अवसरों पर होती हैं जो संभावित रूप से एक डेटाबेस अद्यतन या एक असफल रोलबैक लेनदेन के दौरान समाप्त प्रक्रिया के कारण होती हैं।", "smw-list-count": "सूची में $1 {{PLURAL:$1|प्रविष्टि|प्रविष्टियां}} शामिल हैं|", "smw_adminlinks_displayingdata": "डाटा डिस्प्ले", "smw-createproperty-isproperty": "यह $1 प्रकार का गुणधर्म हैं।", "smw-info-par-message": "दिखाने हेतु संदेश", "prefs-smw": "सिमेंटिक मीडियाविकि", "prefs-ask-options": "सिमेंटिक खोज विकल्प", "smw-ui-tooltip-title-property": "गुणधर्म", "smw-ui-tooltip-title-quantity": "इकाई परिवर्तन", "smw-ui-tooltip-title-info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ी", "smw-ui-tooltip-title-service": "सेवा कड़ी", "smw-ui-tooltip-title-warning": "त्रुटि", "smw-ui-tooltip-title-parameter": "प्राचल", "smw-ui-tooltip-title-event": "आयोजन", "smw-ui-tooltip-title-note": "नोट", "smw-ui-tooltip-title-legend": "कुंजी", "smw-ui-tooltip-title-reference": "सन्दर्भ", "smw_unknowntype": "इस गुण का प्रकार अमान्य है।", "smw-sp-properties-header-label": "गुणों की सूची", "smw-admin-idlookup-title": "वस्तु आईडी देखें", "smw-admin-objectid": "वस्तु आईडी:", "smw-admin-maintenancealerts-invalidentities-alert-title": "अमान्य संस्थाएँ", "smw-livepreview-loading": "लोड हो रहा है...", "smw-sp-searchbyproperty-resultlist-header": "परिणामों की सूची", "smw-search-input-assistance": "उपलब्ध संपत्तियों और श्रेणियों के पूर्व चयन को सरल करने के लिए [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance input assistant] भी सक्षम है।", "log-name-smw": "सिमेंटिक मीडियाविकि लॉग", "log-show-hide-smw": "$1 सिमेंटिक मीडियाविकि लॉग", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1 आयात]]", "smw-datavalue-property-create-restriction": "संपत्ति \"$1\" मौजूद नहीं है और उपयोगकर्ता को \"$2\" (देखें [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode authority mode]) बनाने की या असमर्थित संपत्ति के साथ कथित टिप्पणी की अनुमति नहीं है।", "smw-types-list": "डाटा प्रकारों की सूची", "smw-special-pageproperty-description": "यह पृष्ठ एक संपत्ति के सभी मूल्यों और एक दिए गए पृष्ठ को खोजने के लिए एक ब्राउज़िंग इंटरफ़ेस प्रदान करता है। अन्य उपलब्ध खोज इंटरफेस में [[Special:SearchByProperty|property search]], और [[Special:Ask|ask query builder]] शामिल है।", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|सेकंड}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|सेकंड}}", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" अमान्य पता है।", "smw-data-lookup": "आंकड़ा प्राप्त किया जा रहा है...", "smw-data-lookup-with-wait": "अनुरोध को संसाधित किया जा रहा है और कुछ समय लग सकता है।", "smw-no-data-available": "कोई डाटा उपलब्ध नहीं है।", "smw-edit-protection": "यह प्रष्ठ [[Property:Is edit protected|protected]] आकस्मिक डेटा संशोधन को रोकने के लिए है और उपयुक्त संपादन अधिकार वाले उपयोगकर्ताओं द्वारा ही संपादित किया जा सकता है (\"$1\") या [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups user group]।", "smw-format-datatable-loadingrecords": "लोड हो रहा है...", "smw-format-datatable-processing": "प्रोसेस हो रहा...", "smw-format-datatable-search": "खोज:", "smw-format-datatable-first": "पहला", "smw-format-datatable-last": "आखिरी", "smw-format-datatable-next": "अगला", "smw-format-datatable-previous": "पिछला", "smw-section-expand": "अंश का विस्तार करें", "smw-section-collapse": "अंश को संक्षिप्त करें", "smw-loading": "खुल रहा है...", "smw-fetching": "लाया जा रहा है...", "smw-preparing": "तैयार कर रहे हैं...", "smw-types-title": "प्रकार: $1", "smw-facetedsearch-intro-tab-search": "खोजें", "smw-facetedsearch-format-table": "टेबल", "smw-facetedsearch-input-filter-placeholder": "फ़िल्टर...", "smw-search-placeholder": "खोजें...", "smw-listingcontinuesabbrev": "<PERSON><PERSON><PERSON><PERSON>", "smw-showingresults": "नीचे क्रमांक '''$2''' से प्रारंभ कर के अधिकतम '''$1''' परिणाम {{PLURAL:$1|दिखाया गया है|दिखाए गए हैं}}।"}