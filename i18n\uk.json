{"@metadata": {"authors": ["Ahonc", "<PERSON>", "Andriykopanytsia", "<PERSON><PERSON>", "Avatar6", "Base", "<PERSON><PERSON><PERSON>", "DDPAT", "<PERSON>", "Eduardoad<PERSON>", "Ice bulldog", "KHMELNYTSKYIA", "METROKOP228 UA", "McDut<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nemo bis", "<PERSON><PERSON>", "Piramidion", "Prima klasy4na", "Ren<PERSON>rr", "<PERSON><PERSON>rusyn", "SteveR", "Vlad5250", "Ата", "Тест", "<PERSON><PERSON><PERSON><PERSON> Булка", "아라"]}, "smw-desc": "Робить вашу вікі доступнішою — для машин ''та'' людей ([https://www.semantic-mediawiki.org/wiki/Help:User_manual довідка в мережі])", "smw-error": "Помилка", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Семантичну MediaWiki] встановлено й увімкнено, але вона не має належного [https://www.semantic-mediawiki.org/wiki/Help:Upgrade ключа оновлення].", "smw-upgrade-release": "Випуск", "smw-upgrade-progress": "Прогрес", "smw-upgrade-progress-explain": "Приблизний час завершення оновлення важко передбачити, оскільки він залежить від розміру репозиторію даних та доступного апаратного забезпечення, і більші вікі можуть потребувати більше часу на оновлення.\n\nБудь ласка, зв'яжіться зі своїми локальними адміністраторами, щоб отримати інформацію про прогрес оновлення.", "smw-upgrade-progress-create-tables": "Створення (чи оновлення) таблиць та індексів…", "smw-upgrade-progress-post-creation": "Виконання завдань після створення…", "smw-upgrade-progress-table-optimization": "Виконання оптимізації таблиць…", "smw-upgrade-progress-supplement-jobs": "Додавання допоміжних завдань…", "smw-upgrade-error-title": "Помилка » Семантична MediaWiki", "smw-upgrade-error-why-title": "Чому я бачу цю сторінку?", "smw-upgrade-error-why-explain": "Внутрішня структура бази даних Семантичної MediaWiki змінилася і потребує деякого коригування, щоб повноцінно функціювати. Цьому може бути декілька причин, серед яких:\n* Додано додаткові фіксовані властивості (необхідні додаткові налаштування таблиць)\n* Оновлення містить якісь зміни до таблиць або індекси, що роблять перехоплення обов'язковим перед отриманням доступу до даних\n* Зміни в системі зберігання чи запитів", "smw-upgrade-error-how-title": "Як мені виправити цю помилку?", "smw-upgrade-error-how-explain-admin": "Адміністратор (або інша особа з адміністративними правами) має запустити або [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] для Медіавікі, або скрипт обслуговування [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] для Семантичної Медіавікі.", "smw-upgrade-error-how-explain-links": "Також ви можете проконсультуватися з такими сторінками, щоб отримати додаткову підтримку:\n* Інструкції щодо [https://www.semantic-mediawiki.org/wiki/Help:Installation встановлення]\n* Довідка щодо [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting виправлення помилок]", "smw-extensionload-error-why-title": "Чому я бачу цю сторінку?", "smw-extensionload-error-why-explain": "Розширення <b>не</b> було завантажено за допомогою <code>enableSemantics</code>, а натомість його було увімкнено якимсь іншим способом, на кшталт безпосереднього використання <code>wfLoadExtension( 'SemanticMediaWiki' )</code>.", "smw-extensionload-error-how-title": "Як мені виправити цю помилку?", "smw-extensionload-error-how-explain": "Щоб увімкнути розширення та уникнути проблем із деклараціями просторів назв і незавершеними конфігураціями, необхідно скористатися опцією <code>enableSemantics</code>, яка впевниться, щоб необхідні змінні було встановлено, перш ніж завантажити розширення через <code>ExtensionRegistry</code>. \n\nБудь ласка, перегляньте сторінку довідки [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics], щоб дізнатись більше.", "smw-upgrade-maintenance-title": "Обслуговування » Семантична MediaWiki", "smw-upgrade-maintenance-why-title": "Чому я бачу цю сторінку?", "smw-upgrade-maintenance-note": "Система зараз саме [https://www.semantic-mediawiki.org/wiki/Help:Upgrade оновлює] розширення [https://www.semantic-mediawiki.org/ Semantic MediaWiki] разом з його репозиторієм даних, тож ми хотіли б попросити вас набратися терпіння і дочекатися завершення технічного обслуговування, перш ніж вікі стане знову доступною.", "smw-upgrade-maintenance-explain": "Розширення намагається мінімізувати вплив технічного обслуговування і час, протягом якого система є недоступною, шляхом відстрочення більшості завдань з обслуговування на пізніше, після <code>update.php</code>, але спершу мають завершитися зміни, пов'язані з базою даних, аби уникнути будь-якої неузгодженості даних. До таких змін можуть належати:\n* Зміна структури таблиць, на кшталт додавання нових чи модифікація наявних полів\n* Зміна чи додавання табличних індексів\n* Виконання оптимізації таблиць (коли увімкнено)", "smw-semantics-not-enabled": "Функціонал Семантичної MediaWiki не ввімкнено в цій вікі.", "smw_viewasrdf": "вивід RDF", "smw_finallistconjunct": " і", "smw-factbox-head": "... більше про \"$1\"", "smw-factbox-facts": "Факти", "smw-factbox-facts-help": "Показує висловлення і факти, які були створені користувачем", "smw-factbox-attachments": "Вкладення", "smw-factbox-attachments-value-unknown": "Н/Д", "smw-factbox-attachments-is-local": "Є локальним", "smw-factbox-attachments-help": "Показує доступні вкладення", "smw-factbox-facts-derived": "Отримані факти", "smw-factbox-facts-derived-help": "Показує факти, які були отримані з правил, або за допомогою інших методів розсуджень", "smw_isspecprop": "Ця властивість є спеціальною у цій вікі", "smw-concept-cache-header": "Використання кешу", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count Кеш концептів] містить {{PLURAL:$1|'''одну''' суність|'''$1''' сутрості|'''$1''' сутростей}} ($2).", "smw-concept-no-cache": "Немає кешу.", "smw_concept_description": "<PERSON><PERSON><PERSON><PERSON> концепції «$1».", "smw_no_concept_namespace": "Концепції можна означувати лише у просторі назв «Концепція:».", "smw_multiple_concepts": "Кожна сторінка концепції може мати лише одне означення концепції.", "smw_concept_cache_miss": "Концепцію «$1» неможливо використати у цей момент, оскільки налаштування цієї вікі вимагає попередньої автономної обробки. Якщо проблема не вирішиться протягом певного часу, зверніться до адміністратора сайту.", "smw_noinvannot": "У зворотні властивості неможливо записати значення.", "version-semantic": "Розширен<PERSON>я Semantic", "smw_baduri": "Використовувати адреси (URI), що мають форму «$1» не дозволяється.", "smw_printername_count": "Результати підрахунку", "smw_printername_csv": "Експорт у CSV", "smw_printername_dsv": "Експорт DSV", "smw_printername_debug": "Налагоджувальний запит (для фахівців)", "smw_printername_embedded": "Включити вміст сторінки", "smw_printername_json": "Експорт у JSON", "smw_printername_list": "Список", "smw_printername_plainlist": "Звичайний список", "smw_printername_ol": "Нумерований список", "smw_printername_ul": "Маркований список", "smw_printername_table": "Таблиця", "smw_printername_broadtable": "Широка таблиця", "smw_printername_template": "Шабл<PERSON>н", "smw_printername_templatefile": "Файл шаблону", "smw_printername_rdf": "експорт у RDF", "smw_printername_category": "Категорія", "validator-type-class-SMWParamSource": "текст", "smw-paramdesc-limit": "Максимальне число результатів", "smw-paramdesc-offset": "Зміщення першого результату", "smw-paramdesc-headers": "Показувати заголовки (або назви властивостей)", "smw-paramdesc-mainlabel": "Заголовок стовпця із назвою сторінки", "smw-paramdesc-link": "Показувати значення у вигляді посилань", "smw-paramdesc-intro": "Текст, який показувати перед результатами запиту, якщо такі є", "smw-paramdesc-outro": "Текст, який показувати після результатів запиту, якщо такі є", "smw-paramdesc-default": "Текст, який показувати у випадку відсутності результатів запиту", "smw-paramdesc-sep": "Розділювач між результатами", "smw-paramdesc-propsep": "Розділювач між властивостями у кінцевому записі", "smw-paramdesc-valuesep": "Розділювач між значеннями змінної для цієї властивості", "smw-paramdesc-showsep": "Показувати роздільник у верхній частині CSV-файлу (\"sep=<значення>\")", "smw-paramdesc-distribution": "Замість показу усіх значень, підрахувати їх частоту і показати це", "smw-paramdesc-distributionsort": "Сортування значень розподілу за числом входжень.", "smw-paramdesc-distributionlimit": "Обмеження розподілу значень за числом деяких значень.", "smw-paramdesc-aggregation": "Уточніть, чого має стосуватися агрегація", "smw-paramdesc-template": "Назва шаблону, який використовувати для виводу результатів запиту", "smw-paramdesc-columns": "Кількість стовпців для виводу результатів запиту", "smw-paramdesc-userparam": "Значення, що передається в кожне звертання до шаблону при використанні шаблонів", "smw-paramdesc-class": "Додатковий клас CSS для задання списку", "smw-paramdesc-introtemplate": "Назва шаблону, який показувати перед результатами запиту, якщо такі є", "smw-paramdesc-outrotemplate": "Назва шаблону, який показувати після результатів запиту, якщо такі є", "smw-paramdesc-embedformat": "Теґ HTML, який використовувати для означення заголовків", "smw-paramdesc-embedonly": "Не показувати заголовків", "smw-paramdesc-table-class": "Додатковий клас CSS для задання таблиці", "smw-paramdesc-table-transpose": "Відобразити заголовки таблиці по вертикалі, а результати - по горизонталі", "smw-paramdesc-rdfsyntax": "<PERSON><PERSON><PERSON> синтаксис RDF вживати", "smw-paramdesc-csv-sep": "Вказує роздільник колонок", "smw-paramdesc-csv-valuesep": "Вказує роздільник значень", "smw-paramdesc-csv-merge": "Поєднати значення рядків і стовпців з ідентичним ідентифікатором суб'єкта (aka перший стовпчик)", "smw-paramdesc-csv-bom": "Додати BOM (символ для сигнальної сутності) у верху вихідного файлу", "smw-paramdesc-dsv-separator": "Використовувати роздільник", "smw-paramdesc-dsv-filename": "Ім'я файлу DSV", "smw-paramdesc-filename": "Ім'я вихідного файлу", "smw-smwdoc-description": "Показує таблицю всіх параметрів, які можуть бути використані для вказаного формату результату разом із типовими значеннями і описами.", "smw-smwdoc-default-no-parameter-list": "Цей результат не надає специфичні для формату параметри.", "smw-smwdoc-par-format": "Результуючий формат для відображення параметрів документації.", "smw-smwdoc-par-parameters": "Які параметри показувати. \"specific\" для доданих за форматом, \"base\" для доступних в усіх форматах, та \"all\" для обох.", "smw-paramdesc-sort": "Властивість для сортування запиту за", "smw-paramdesc-order": "Порядок сортування запиту", "smw-paramdesc-searchlabel": "Текст для продовження пошуку", "smw-paramdesc-named_args": "Імена аргументів, переданих в шаблон", "smw-paramdesc-template-arguments": "Задає як іменовані аргументи передаються в шаблон", "smw-paramdesc-import-annotation": "Додаткові анотовані дані, що мають бути скопійовані під час парсингу суб'єкта", "smw-paramdesc-export": "Параметри експорту", "smw-paramdesc-prettyprint": "Вивід докладного друку, який відображає додаткові відступи та символи", "smw-paramdesc-json-unescape": "У виводі є неекрановані косі риски і багатобайтні символи Юнікоду", "smw-paramdesc-json-type": "Тип серіал<PERSON>зації", "smw-paramdesc-source": "Альтернативне джерело запиту", "smw-paramdesc-jsonsyntax": "JSON синтаксис для використання", "smw-printername-feed": "Канал RSS та Atom", "smw-paramdesc-feedtype": "<PERSON>и<PERSON> каналу", "smw-paramdesc-feedtitle": "Текст, який необхідно використовувати як назву каналу", "smw-paramdesc-feeddescription": "Текст, який необхідно використовувати як опис каналу", "smw-paramdesc-feedpagecontent": "Вміст сторінки буде відображатися з каналу", "smw-label-feed-description": "$1 $2 канал", "smw-paramdesc-mimetype": "Тип медіа<PERSON><PERSON><PERSON><PERSON><PERSON> (тип MIME) для файлу на виході", "smw_iq_disabled": "Семантичні запити було вимкнено в цій вікі.", "smw_iq_moreresults": "... подальші результати", "smw_parseerror": "Вказане значення — незрозуміле.", "smw_notitle": "«$1» неможливо використати у якості назви сторінки у цій вікі.", "smw_noproperty": "\" $1 \" не можна використовувати як ім'я властивості в цій вікі.", "smw_wrong_namespace": "Тут дозволено лише сторінки в просторі назв «$1».", "smw_manytypes": "Для властивості означено більше одного типу даних.", "smw_emptystring": "Використовувати пусті рядки не дозволяється.", "smw_notinenum": "«$1» не є у списку ($2) [[Property:Allows value|дозволених значень]] для властивості «$3».", "smw-datavalue-constraint-error-allows-value-list": "«$1» немає в списку ($2) [[Property:Allows value|дозволених значень]] для властивості «$3».", "smw-datavalue-constraint-error-allows-value-range": "«$1» не перебуває в межах діапазону «$2», вказаного в обмеженні [[Property:Allows value|дозволених значень]] для властивості «$3».", "smw-constraint-error": "Проблема з обмеженням", "smw-constraint-error-suggestions": "Будь ласка, перегляньте перелічені порушення і властивості разом із їхніми анотованими значеннями, аби впевнитись, що всі вимоги до обмежень виконано.", "smw-constraint-error-limit": "Список міститиме максимум $1 порушень.", "smw_noboolean": "«$1» не розпізнається як булове значення (значення «так/ні»).", "smw_true_words": "true,t,yes,y,так,т", "smw_false_words": "false,f,no,n,ні,н", "smw_nofloat": "«$1» не є числом", "smw_infinite": "Такі довгі числа, як «$1» не підтримуються.", "smw_unitnotallowed": "«$1» не оголошена як допустима одиниця виміру цієї властивості.", "smw_nounitsdeclared": "Для цієї властивості не оголошені одиниці вимірювання.", "smw_novalues": "Не вказано значень.", "smw_nodatetime": "Дата «$1» — незрозуміла.", "smw_toomanyclosing": "«$1» зустрічається занадто багато разів.", "smw_noclosingbrackets": "Деякі з «<nowiki>[[</nowiki>» у запиті не було закрито відповідними «]]».", "smw_misplacedsymbol": "Символ «$1» використано у невідповідному місці.", "smw_unexpectedpart": "Фрагмент запиту «$1» — незрозумілий. Результати можуть відрізнятись від очікуваних.", "smw_emptysubquery": "Деякий підзапит не містить інформації про умову.", "smw_misplacedsubquery": "Деякий підзапит був використаний у невідповідному місці.", "smw_valuesubquery": "Підзапити не підтримуються для значень властивості «$1».", "smw_badqueryatom": "Певна частина «<nowiki>[[…]]</nowiki>» — незрозуміла.", "smw_propvalueproblem": "Значення властивості «$1» — незрозуміле.", "smw_noqueryfeature": "Певна функція запиту не підтримується у цій вікі, тому ця частина запиту була пропущена ($1).", "smw_noconjunctions": "Операція логічного «і» не підтримується у цій вікі, тому ця частина запиту була пропущена ($1).", "smw_nodisjunctions": "Операція логічного «або» не підтримується у цій вікі, тому ця частина запиту була пропущена ($1).", "smw_querytoolarge": "{{PLURAL:$2|Вказана умова запиту не може бути опрацьована|Вказані умови запиту не можуть бути опрацьовані}} через чинні у вікі обмеження в розмірі або глибині запиту: <code>$1</code>.", "smw_notemplategiven": "Щоб цей формат запиту спрацював, потрібно вказати значення параметра «template».", "smw_db_sparqlqueryproblem": "Не вдалося отримати результат запиту до бази даних SPARQL. Ця може бути тимчасова помилка або проблема в програмному забезпеченні бази даних.", "smw_db_sparqlqueryincomplete": "Пошук відповіді на запит виявився занадто складним і був перерваний. Деякі результати можуть бути не показані. По можливості спробуйте спростити запит.", "smw_type_header": "Властивості типу даних «$1»", "smw_typearticlecount": "{{PLURAL:$1|Показується|Показуються|Показується}} $1 {{PLURAL:$1|властивість|властивості|властивостей}} цього типу даних.", "smw_attribute_header": "Сторінки, що використовують властивість «$1»", "smw_attributearticlecount": "{{PLURAL:$1|Показується|Показуються|Показується}} $1 {{PLURAL:$1|сторінка, що використовує|сторінки, що використовують|сторінок, що використовують}} цю властивість.", "smw-propertylist-subproperty-header": "Підвластивості", "smw-propertylist-redirect-header": "Синоніми", "smw-propertylist-error-header": "Сторінки з невідповідними призначеннями", "smw-propertylist-count": "Показано $1 {{PLURAL:$1|пов'язану сутність|пов'язані сутності|пов'язаних сутностей}}.", "smw-propertylist-count-with-restricted-note": "Показано $1 {{PLURAL:$1|пов'язану сутність|пов'язані сутності|пов'язаних сутностей}} (доступно більше, але кількість обмежено до «$2»).", "smw-propertylist-count-more-available": "Показано $1 {{PLURAL:$1|пов'язану сутність|пов'язані сутності|пов'язаних сутностей}} (доступно більше).", "specialpages-group-smw_group": "Семантична MediaWiki", "specialpages-group-smw_group-maintenance": "Обслуговування", "specialpages-group-smw_group-properties-concepts-types": "Властивості, концепти і типи", "specialpages-group-smw_group-search": "Перегляд і пошук", "exportrdf": "Експорт сторінок у RDF", "smw_exportrdf_docu": "Ця сторінка дозволяє отримати дані із сторінки у форматі RDF. Введіть назви сторінок, по одній у рядку, в текстовому полі, що знаходиться нижче.", "smw_exportrdf_recursive": "Рекурсивно експортувати усі пов’язані сторінки. Зауважте: результат може бути об’ємним.", "smw_exportrdf_backlinks": "Також експортувати усі сторінки, що звертаються до експортованих. Генерує RDF з можливістю навігації.", "smw_exportrdf_lastdate": "Не експортувати сторінки, що не були змінені з часу заданого моменту в часі.", "smw_exportrdf_submit": "Експорт", "uriresolver": "Оброблювач URI", "properties": "Властивості", "smw-categories": "Категорії", "smw_properties_docu": "Наступні властивості використовуються у цій вікі.", "smw_property_template": "$1 із типом $2 ($3 {{PLURAL:$3|1=використання|використань}})", "smw_propertylackspage": "Кожна властивість повинна мати сторінку, що її описує.", "smw_propertylackstype": "Для цієї властивості не було означено типу даних (поки що використовується $1).", "smw_propertyhardlyused": "Ця властивість використовується рушієм вікі.", "smw-property-name-invalid": "Властивість  $1  не може використовуватися (неприпустиме ім'я властивості).", "smw-property-name-reserved": "«$1» входить у число зарезервованих імен і не повинне бути використане як властивість. Інформація про те, чому це ім'я зарезервовано, може міститися у [https://www.semantic-mediawiki.org/wiki/Help:Property_naming довідковій сторінці].", "smw-sp-property-searchform": "Відобразити властивості, які містять:", "smw-sp-property-searchform-inputinfo": "Введення чутливе до регістру і коли використовується для фільтрації, відображаються лише ті властивості, які відповідають умові.", "smw-special-property-searchform": "Показати властивості, які містять:", "smw-special-property-searchform-inputinfo": "Введені дані чутливі до регістру, і якщо їх використати для фільтрування, виведені будуть лише ті властивості, які відповідають цій умові.", "smw-special-property-searchform-options": "Опції", "smw-special-wantedproperties-filter-label": "Фільтр:", "smw-special-wantedproperties-filter-none": "Немає", "smw-special-wantedproperties-filter-unapproved": "Незатверджені", "smw-special-wantedproperties-filter-unapproved-desc": "Опція фільтра, яка використовується у сполученні з режимом авторитету.", "concepts": "Концепції", "smw-special-concept-docu": "Вікі [https://www.semantic-mediawiki.org/wiki/Help:Concepts concept] можна переглянути як \"динамічну категорію\", тобто як збірку сторінок, що не створені вручну, а опрацьовані Семантичною Медіавікі на основі опису заданого запиту.", "smw-special-concept-header": "Пере<PERSON><PERSON><PERSON> концепцій", "smw-special-concept-count": "{{PLURAL:$1|Наступне поняття|$1 наступні поняття|$1 наступних понять}} {{PLURAL:$1|перераховане|переховані|перераховано}}.", "smw-special-concept-empty": "Поняття не знайдено.", "unusedproperties": "Властивості, що не використовуються", "smw-unusedproperties-docu": "На цій сторінці перераховані [https://www.semantic-mediawiki.org/wiki/Unused_properties невикористані властивості], які були оголошені, хоча ніякі інші сторінки їх не використовують. Для диференційованого вигляду див. спеціальні сторінки [[Special:Properties|всіх]] або [[Special:WantedProperties|потрібних властивостей]].", "smw-unusedproperty-template": "$1 із типом даних $2", "wantedproperties": "Потрібні властивості", "smw-wantedproperties-docu": "На цій сторінці перелічені [https://www.semantic-mediawiki.org/wiki/Wanted_properties бажані властивості], які використовуються у вікі, але не мають сторінки опису. Диференційований показ див. на спеціальних сторінках  [[Special:Properties|усіх]] або  [[Special:UnusedProperties|невикористовуваних властивостей]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|звернення|звернення|звернень}})", "smw-special-wantedproperties-docu": "На цій сторінці перелічені [https://www.semantic-mediawiki.org/wiki/Wanted_properties бажані властивості], які використовуються у вікі, але не мають сторінки опису. Диференційований показ див. на спеціальних сторінках  [[Special:Properties|усіх]] або  [[Special:UnusedProperties|невикористовуваних властивостей]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|звернення|звернення|звернень}})", "smw_purge": "Оновити", "smw-purge-update-dependencies": "Семантична MediaWiki очищає кеш поточної сторінки через якісь застарілі залежності, які вона виявила, і які потребують оновлення.", "smw-purge-failed": "Від Semantic MediaWiki була невдала спроба очистити сторінку", "types": "Типи", "smw_types_docu": "Список [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes доступних типів даних], де кожен [https://www.semantic-mediawiki.org/wiki/Help:Datatype тип] репрезентує унікальний набір атрибутів, що описують значення з точки зору характеристик зберігання й відображення, похідних від присвоєної властивості.", "smw-special-types-no-such-type": "\"$1\" невідомий або не був указаний як правильний тип даних.", "smw-statistics": "Семантична статистика", "smw-statistics-cached": "Семантична статистика (кешована)", "smw-statistics-entities-total": "Сутності (всього)", "smw-statistics-entities-total-info": "Приблизний підрахунок рядків сутностей. Він включає властивості, поняття, та будь-які інші зареєстровані репрезентації об'єктів, які вимагають призначення ідентифікатора.", "smw-statistics-property-instance": "{{PLURAL:$1|Значення}} властивості (всього)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Властивість|Властивості}}]] (всього)", "smw-statistics-property-total-info": "Всього зареєстрованих властивостей.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Властивість|Властивості}} (всього)", "smw-statistics-property-used": "{{PLURAL:$1|Властивість|Властивості}} (використовується щонайменше з одним значенням)", "smw-statistics-property-page": "{{PLURAL:$1|Властивість|Властивості}} (зареєстровано із сторінкою)", "smw-statistics-property-page-info": "Кількість властивостей, які мають власну сторінку та опис.", "smw-statistics-property-type": "{{PLURAL:$1|Властивість|Властивості}} (присвоєно тип даних)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Запит|Запити}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Запит|Запити}}]] (вбудовано, усього)", "smw-statistics-query-format": "Формат <code>$1</code>", "smw-statistics-query-size": "Розмір запиту", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Поняття}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Поняття}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Підоб'єкт|Підоб'єкти}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Підоб'єкт|Підоб'єкти}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Тип даних|Типи даних}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Значення властивості}} ([[Special:ProcessingErrorList|{{PLURAL:$1|неправильна анотація|неправильні анотації}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Значення властивості}} ({{PLURAL:$1|некоректна анотація|некоректні анотації}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Застаріла сутність|Застарілі сутності}}]", "smw-statistics-delete-count-info": "Сутностей, які було позначено на вилучення, слід позбуватися регулярно за допомогою скриптів обслуговування.", "smw_uri_doc": "Оброблювач URI виконує [$1 «W3C TAG finding on httpRange-14»].\nЦя функція забезпечує видачу на відповідний запит репрезентації RDF (для машин) або вікісторінки (для людей).", "ask": "Семантичний пошук", "smw-ask-help": "Цей розділ містить посилання на деякі сторінки, де пояснюється, як використовувати синтаксис <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Обирання сторінок] описує, як обирати сторінки і будувати умови\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Оператори пошуку] перелічує оператори пошуку, включно з операторами для діапазонів і знаки підстановки\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Відображувана інформація] виділяє використання тверджень виводу та опції форматування", "smw_ask_sortby": "Сортувати за стовпцем (необов’язково)", "smw_ask_ascorder": "За зростанням", "smw_ask_descorder": "За спаданням", "smw-ask-order-rand": "Випадково", "smw_ask_submit": "Знайти", "smw_ask_editquery": "Редагувати запит", "smw_add_sortcondition": "[Додати умову сортування]", "smw-ask-sort-add-action": "Додати умову сортування", "smw_ask_hidequery": "Приховати запит (компактний вигляд)", "smw_ask_help": "Довідка про запити", "smw_ask_queryhead": "Умова", "smw_ask_printhead": "Вибір роздруківки", "smw_ask_printdesc": "(одна назва властивості на рядок)", "smw_ask_format_as": "Форматувати як:", "smw_ask_defaultformat": "за замовчуванням", "smw_ask_otheroptions": "Інші опції", "smw-ask-otheroptions-info": "Цей розділ містить параметри, які змінюють роздруківку заяви. Описи параметрів можна переглядати, навівши на них курсор.", "smw-ask-otheroptions-collapsed-info": "Будь ласка, використовуйте значок \"+\", щоб переглянути всі доступні параметри", "smw_ask_show_embed": "Показати включений код", "smw_ask_hide_embed": "Приховати включений код", "smw_ask_embed_instr": "Щоб вбудувати цей запит в вікісторінку, використовуйте поданий нижче код.", "smw-ask-delete": "Вилучити", "smw-ask-sorting": "Сортування", "smw-ask-options": "Опції", "smw-ask-options-sort": "Опції сортування", "smw-ask-format-options": "Формат і опції", "smw-ask-parameters": "Параметри", "smw-ask-search": "По<PERSON><PERSON>к", "smw-ask-debug": "Налагодження", "smw-ask-debug-desc": "Генерує інформацію про запит щодо налагодження", "smw-ask-no-cache": "Відімкнути кеш запиту", "smw-ask-no-cache-desc": "Результати без кешу запиту", "smw-ask-result": "Результат", "smw-ask-empty": "Очистити все", "smw-ask-download-link-desc": "Завантажити запитувані результати у форматі $1", "smw-ask-format": "Формат", "smw-ask-format-selection-help": "Довідка для вибраного формату: $1.", "smw-ask-condition-change-info": "Умову було змінено й пошукові треба запустити запит повторно, щоб вивести результати, які відповідають новим вимогам.", "smw-ask-input-assistance": "Допомога при вводі", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Допомога з введенням] надається для полів друку, сортування та умови. Поле умови повинно мати один з таких префіксів:", "smw-ask-condition-input-assistance-property": "<code>p:</code> для виклику підказок властивості (наприклад, <code>[[p:Має ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> для виклику підказок категорії", "smw-ask-condition-input-assistance-concept": "<code>con:</code> для виклику підказок концепції", "smw-ask-format-change-info": "Формат було змінено, і запит треба виконати ще раз, щоб він відповідав новим параметрам та опціям візуалізації.", "smw-ask-format-export-info": "Обраний формат є форматом експорту, який не має візуальної репрезентації, а тому результати надаються лише як завантаження.", "smw-ask-query-search-info": "На запит <code><nowiki>$1</nowiki></code> було отримано відповідь із {{PLURAL:$3|1=<code>$2</code> (з кешу)|<code>$2</code> (з кешу)|<code>$2</code>}} за $4 {{PLURAL:$4|секунду|секунди|секунд}}.", "smw-ask-extra-query-log": "<PERSON><PERSON><PERSON><PERSON><PERSON> за<PERSON>и<PERSON><PERSON>в", "smw-ask-extra-other": "Інше", "searchbyproperty": "Шукати за властивістю", "processingerrorlist": "Список помилок опрацювання", "constrainterrorlist": "Список помилок обмежень", "propertylabelsimilarity": "Звіт про подібність назв властивостей", "missingredirectannotations": "Відсутні анотації перенаправлень", "smw-processingerrorlist-intro": "Наведений список подає загальний огляд [https://www.semantic-mediawiki.org/wiki/Processing_errors помилок опрацювання], що виникли у зв'язку з [https://www.semantic-mediawiki.org/ Семантичною MediaWiki]. Рекомендується регулярно стежити за цим списком і виправляти недійсні анотації значень.", "smw-constrainterrorlist-intro": "Наведений список подає загальний огляд [https://www.semantic-mediawiki.org/wiki/Constraint_errors помилок обмежень], що виникли у зв'язку із [https://www.semantic-mediawiki.org/ Семантичною MediaWiki]. Рекомендується регулярно стежити за цим списком і виправляти недійсні анотації значень.", "smw-missingredirects-intro": "Вказаний розділ перелічить сторінки, які не мають анотацій [https://www.semantic-mediawiki.org/wiki/Redirects перенаправлень] у Семантичній MediaWiki (шляхом порівняння з інформацією, що зберігається в MediaWiki), а щоб відновити ті анотації, або [https://www.semantic-mediawiki.org/wiki/Help:Purge очистіть кеш] сторінки вручну, або запустіть <code>rebuildData.php</code> скрипт технічного обслуговування (з опцією <code>--redirects</code>).", "smw-missingredirects-list": "Сторінки з відсутніми анотаціями", "smw-missingredirects-list-intro": "Показано $1 {{PLURAL:$1|сторінку|сторінки|сторінок}} з відсутніми анотаціями перенаправлень.", "smw-missingredirects-noresult": "Сторінок із перенаправленнями без анотацій не знайдено.", "smw_sbv_docu": "Шукати всі сторінки, що мають вказану властивість і значення.", "smw_sbv_novalue": "Введіть правильне значення властивості або перегляньте всі значення властивості «$1».", "smw_sbv_displayresultfuzzy": "Список всіх сторінок, що мають властивість «$1» із значенням «$2». Оскільки було лише кілька результатів, показано також сторінки, що містять близькі значення цієї властивості.", "smw_sbv_property": "Властивість:", "smw_sbv_value": "Значення:", "smw_sbv_submit": "Знайти", "browse": "Перегляд вікі", "smw_browselink": "Перегляд властивостей", "smw_browse_article": "Введіть назву сторінки, з якої почати перегляд.", "smw_browse_go": "Перейти", "smw_browse_show_incoming": "Показати вхідні властивості", "smw_browse_hide_incoming": "Приховати вхідні властивості", "smw_browse_no_outgoing": "Ця сторінка не містить властивостей.", "smw_browse_no_incoming": "На цю сторінку не посилається жодна властивість.", "smw-browse-from-backend": "Інформація наразі отримується з бекенду.", "smw-browse-intro": "Ця сторінка надає подробиці про тему або примірник сутності, будь ласка, введіть назву об'єкта, який слід перевірити.", "smw-browse-invalid-subject": "Перевірка суб'єкта повернулася з помилкою «$1».", "smw-browse-api-subject-serialization-invalid": "Суб'єкт має некоректний формат серіалізації.", "smw-browse-js-disabled": "Є підозра, що JavaScript відключений або недоступний. Рекомендуємо використовувати браузер, де він підтримується. Інші варіанти обговорюються на сторінці параметра конфігурації [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Показати групи", "smw-browse-hide-group": "Приховати групи", "smw-noscript": "Ця сторінка чи дія вимагає роботи JavaScript. Будь ласка, увімкніть JavaScript у своєму браузері, або скористайтеся браузером, де він підтримується, щоб функціонал діяв і послуги надавалися відповідно до запиту. Якщо Вам потрібна додаткова підтримка, будь ласка, перегляньте сторінку довідки [https://www.semantic-mediawiki.org/wiki/Help:Noscript «Noscript»]", "smw_inverse_label_default": "$1 з", "smw_inverse_label_property": "Назва зворотної властивості", "pageproperty": "Пошук властивостей сторінки", "pendingtasklist": "Список завдань, що очікують на виконання", "smw_pp_docu": "Введіть або сторінку та властивість, або просто властивість, щоб отримати всі задані значення.", "smw_pp_from": "Зі сторінки:", "smw_pp_type": "Властивість:", "smw_pp_submit": "Знайти", "smw-prev": "{{PLURAL:$1|попередня $1|попередні $1|попередніх $1}}", "smw-next": "{{PLURAL:$1|наступна $1|наступні $1|наступних $1}}", "smw_result_prev": "Попередня", "smw_result_next": "Наступна", "smw_result_results": "Результати", "smw_result_noresults": "Немає результатів.", "smwadmin": "Панель інструментів Семантичної MediaWiki", "smw-admin-statistics-job-title": "Статистика завдань", "smw-admin-statistics-job-docu": "Статистика завдань показує інформацію про заплановані завдання Семантичної MediaWiki, які ще не було виконано. Кількість завдань може бути дещо неточною або містити невдалі спроби. Будь ласка, перегляньте [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue посібник], щоб дізнатися подробиці.", "smw-admin-statistics-querycache-title": "Запит на кеш", "smw-admin-statistics-querycache-disabled": "[https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] не було увімкнено в цій вікі, а тому немає доступної статистики.", "smw-admin-statistics-querycache-legend": "Статистика кешу повинна містити тимчасові збірні дані, а також отримані дані, зокрема: \n* «misses» — загальна кількість спроб отримати дані з кешу з недосяжними відповідями, що спричиняє примусове пряме виведення репозиторію (бази даних, потрійного сховища тощо) \n* «deletes» — як загальну кількість операцій з вилучення кешу (або через очищення кешу, або ж через залежності в запитах) \n* «hits» — містить кількість виведень кешу або із вбудованих (запити, надіслані зсередини вікісторінки), або з невбудованих (якщо увімкнено, запити надсилаються такими сторінками, як Special:Ask, чи API) джерел\n* «medianRetrievalResponseTime» — значення орієнтації середнього часу відповіді (в секундах) для кешованих чи некешованих запитів на інформацію за період часу, потрібний для процесу її накопичення  \n* «noCache» позначає кількість запитів без спроб (limit=0, опція 'no-cache' тощо) для отримання результатів з кешу", "smw-admin-statistics-section-explain": "Цей розділ надає додаткову статистику для адміністраторів.", "smw-admin-statistics-semanticdata-overview": "Огляд", "smw-admin-permission-missing": "Доступ до цієї сторінки було заблоковано через відсутність відповідних прав. Будь ласка, ознайомтеся зі сторінкою довідки щодо [https://www.semantic-mediawiki.org/wiki/Help:Permissions прав], щоб отримати інформацію про необхідні налаштування.", "smw-admin-setupsuccess": "Базу даних налаштовано.", "smw_smwadmin_return": "Повернутись до $1", "smw_smwadmin_updatestarted": "Запущено новий процес оновлення семантичних даних.\nУсі збережені дані буде перебудовано чи відновлено за необхідності.\nЗа процесом оновлення можна стежити на цій сторінці.", "smw_smwadmin_updatenotstarted": "Процес оновлення вже працює.\nНового запущено не буде.", "smw_smwadmin_updatestopped": "Усі процеси оновлення було зупинено.", "smw_smwadmin_updatenotstopped": "Щоб зупинити запущений процес оновлення, Ви маєте поставити позначку, аби засвідчити, що ви справді впевнені в цьому.", "smw-admin-docu": "Ця сторінка допоможе при встановленні, оновленні, обслуговуванні та використанню <a href=\"https://www.semantic-mediawiki.org\">Семантичної MediaWiki</a>, а також забезпечує подальші адміністративні функції і завдання, так само як і статистику. Не забудьте зробити резервну копію всіх важливих даних перед запуском адміністративних функцій.", "smw-admin-environment": "Оболонка програмного забезпечення", "smw-admin-db": "Налаштування бази даних", "smw-admin-db-preparation": "Триває ініціалізація таблиці, і це може зайняти трохи часу, перед тим як результати буде виведено, залежно від розміру та ймовірної оптимізації таблиці.", "smw-admin-dbdocu": "Семантична MediaWiki вимагає своєї власної структури бази даних (і є незалежною від MediaWiki, тож не впливає на решту інсталяції MediaWiki) для зберігання семантичних даних.\nЦю функцію налаштування можна виконувати багато разів без жодної шкоди, але насправді вона необхідна лише раз — під час встановлення чи оновлення.", "smw-admin-permissionswarn": "Якщо ця операція буде зупинена через помилки SQL, вірогідною причиною може бути відсутність необхідних прав користувача, під іменем якого вікі під'єднується до бази даних (він вказується у файлі «LocalSettings.php»). В такому випадку вам необхідно або дозволити цьому користувачу створювати та вилучати таблиці БД, або тимчасово ввести в файл «LocalSettings.php» ім'я користувача БД «root», або використати сценарій обслуговування <code>setupStore.php</code>, який може використовувати облікові дані адміністратора.", "smw-admin-dbbutton": "Ініціалізація або оновлення таблиць", "smw-admin-announce": "Заявити про Вашу вікі", "smw-admin-announce-text": "Якщо Ваша вікі публічна, Ви можете зареєструвати її у <a href=\"https://wikiapiary.com\">WikiApiary</a>, вікі з відстеження вікі.", "smw-admin-deprecation-notice-title": "Сповіщення про застарілість", "smw-admin-deprecation-notice-docu": "Поданий нижче розділ містить налаштування, які вже застаріли або були усунуті, але, як було виявлено, все ще є активними в цій вікі. Очікується, що будь-який майбутній випуск припинить підтримку цих конфігурацій.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> є застарілим і буде усунутий у версії $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> вилучить (або замінить)\n {{PLURAL:$2|таку опцію|такі опції}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> — застарілий параметр і його буде усунено в $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> замінено на <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> було замінено на <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|опція|опції|опцій}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> буде замінено <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> усунуто у версії $2", "smw-admin-deprecation-notice-title-notice": "Застарілі налаштування", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Застарілі налаштування</b> показує ті налаштування, використання яких було виявлено в цій вікі, та які планують вилучити або змінити в майбутньому випуску.", "smw-admin-deprecation-notice-title-replacement": "Замінені або перейменовані налаштування", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Замінені або перейменовані налаштування</b> містить налаштування, які було перейменовано чи змінено в інший спосіб, а тому рекомендуємо негайно оновити їхні назви чи формат.", "smw-admin-deprecation-notice-title-removal": "Вилучені налаштування", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Вилучені налаштування</b> ідентифікує ті налаштування, які було вилучено в попередньому випуску, але виявлено, що вони все ще використовуються в цій вікі.", "smw-admin-deprecation-notice-section-legend": "Легенда", "smw-smwadmin-refresh-title": "Відновлення і оновлення даних", "smw_smwadmin_datarefresh": "Перебудова даних", "smw_smwadmin_datarefreshdocu": "Можна відновити всі дані Semantic MediaWiki, базуючись на поточному стані вмісту вікі. Це можна використати при відновленні пошкоджених даних або при оновленні даних, якщо внутрішній формат змінився через оновлення програмного забезпечення. Оновлення буде виконувати сторінка за сторінкою, тому займе якийсь час. Нижче показано прогрес процесу оновлення, дається можливість запустити або зупинити оновлення (якщо ця функція не була заблокована адміністратором).", "smw_smwadmin_datarefreshprogress": "<strong>Процес оновлення вже запущено.</strong>\nПроцес оновлення відбувається маленькими порціям із кожним відкриттям сторінки користувачами, тому він проходить повільно. Щоб швидше закінчити оновлення, можна скористатись сценарієм обслуговування <code>runJobs.php</code> (використовуйте опцію <code>--maxjobs 1000</code> щоб обмежити число оновлень в одному пакеті).\nПриблизний стан запущеного оновлення:", "smw_smwadmin_datarefreshbutton": "Почати оновлення даних", "smw_smwadmin_datarefreshstop": "Зупинити це оновлення", "smw_smwadmin_datarefreshstopconfirm": "Так, я впевнен{{GENDER:$1|ий|а}}.", "smw-admin-job-scheduler-note": "Завдання (ті, що увімкнені) у цьому розділі виконуються через чергу завдань, щоб уникнути глухих кутів у ході їх виконання. [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Черга завдань] відповідальна за обробку, тому критично важливо, щоб скрипт обслуговування мав відповідну спроможність <code>runJobs.php</code> (див. також параметр конфігурації <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Ліквідація застарілих сутностей", "smw-admin-outdateddisposal-intro": "Деякі дії (зміна типу властивості, вилучення вікісторінок чи виправлення помилкових значень) спричинять появу [https://www.semantic-mediawiki.org/wiki/Outdated_entities застарілих сутностей], тому рекомендується час від часу вилучати їх, щоб звільняти простір у пов'язаній таблиці. Залежно від часового інтервалу, визначеного планувальником завдань, процес очищення може зайняти деякий час перед тим, як завдання буде виконано і завершено. Залежно від вікна часу, визначеного планувальником завдань, процес виправлення може зайняти деякий час перед тим, як завдання буде виконано й завершено.", "smw-admin-outdateddisposal-active": "Було заплановано завдання з ліквідації застарілих сутностей.", "smw-admin-outdateddisposal-button": "Запланувати ліквідацію", "smw-admin-feature-disabled": "Цю функцію було вимкнено для цієї вікі. Будь ласка, перегляньте довідкову сторінку щодо <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">налаштувань</a>, або зв'яжіться з системним адміністратором.", "smw-admin-propertystatistics-title": "Перебудова статистики властивості", "smw-admin-propertystatistics-intro": "Перебудовує всю статистику використання властивостей, після чого оновлює і виправляє [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count кількість використань] властивостей.", "smw-admin-propertystatistics-active": "Завдання з перебудови статистики властивостей заплановано.", "smw-admin-propertystatistics-button": "Запланувати перебудову статистики", "smw-admin-fulltext-title": "Перебудова повнотекстового пошуку", "smw-admin-fulltext-intro": "Перебудовує пошуковий індекс із таблиць властивостей з увімкненим типом даних [https://www.semantic-mediawiki.org/wiki/Full-text повнотекстового пошуку]. Зміни до правил індексування (змінені шумові слова, новий стемер тощо) та/або заново додана або змінена таблиця потребують запуску цього завдання ще раз.", "smw-admin-fulltext-active": "Завдання з перебудови повнотекстового пошуку заплановано.", "smw-admin-fulltext-button": "Запланувати повнотекстову перебудову", "smw-admin-support": "Отримання підтримки", "smw-admin-supportdocu": "Є різні ресурси, створені, щоб допомогти Вам у разі виникнення проблем:", "smw-admin-installfile": "Якщо у вас виникли проблеми із встановленням, насамперед подивіться поради у <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">файлі INSTALL</a> та на <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">сторінці інсталяції</a>.", "smw-admin-smwhomepage": "Повна довідка із користування Семантичною MediaWiki знаходиться на сайті <b><a href=\"https://www.semantic-mediawiki.org/wiki/%D0%A1%D0%B5%D0%BC%D0%B0%D0%BD%D1%82%D0%B8%D1%87%D0%BD%D0%B0_%D0%9C%D0%B5%D0%B4%D1%96%D0%B0%D0%B2%D1%96%D0%BA%D1%96\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Повідомлення про помилки відсилайте через  <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">багтрекер</a>; сторінка про <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">надсилання звітів про помилки</a> має деякі підказки щодо того, як написати ефективний звіт про помилку.", "smw-admin-questions": "Якщо у вас є додаткові запитання чи побажання, приєднуйтесь до обговорень у <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">поштовій розсилці для користувачів Семантичної MediaWiki</a>.", "smw-admin-other-functions": "Інші функції", "smw-admin-statistics-extra": "Статистичні функції", "smw-admin-statistics": "Статистика", "smw-admin-supplementary-section-title": "Додаткові функції", "smw-admin-supplementary-section-subtitle": "Підтримувані функції ядра", "smw-admin-supplementary-section-intro": "У цьому розділі подано додаткові функції поза цілями підтримки, і може бути, що деякі з перелічених функцій (див. [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions документацію]), будуть обмежені або недоступні у цій вікі.", "smw-admin-supplementary-settings-title": "Конфігурація й налаштування", "smw-admin-supplementary-settings-intro": "<u>$1</u> показує параметри, що визначають поведінку Семантичної MediaWiki", "smw-admin-main-title": "Семантична MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Опера<PERSON><PERSON><PERSON>на статистика", "smw-admin-supplementary-operational-statistics-short-title": "опера<PERSON><PERSON><PERSON>на статистика", "smw-admin-supplementary-operational-statistics-intro": "Відображає розширений набір «<u>$1</u>»", "smw-admin-supplementary-idlookup-title": "Пошук і вилучення сутності", "smw-admin-supplementary-idlookup-short-title": "пошук і вилучення сутності", "smw-admin-supplementary-idlookup-intro": "Підтримує просту функцію «<u>$1</u>»", "smw-admin-supplementary-duplookup-title": "Перегляд дубльованих сутностей", "smw-admin-supplementary-duplookup-intro": "<u>$1</span> для знаходження записів, які класифікуються як дублікати у вибраній матриці таблиць", "smw-admin-supplementary-duplookup-docu": "Ця сторінка перелічує записи з вибраних таблиць, що були категоризовані як [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities дублікати]. Дублікати записів можуть з'являтися лише в рідкісних випадках (або й узагалі ніколи), потенційно спричинені передчасно завершеним оновленням чи невдалою транзакцією відкоту.", "smw-admin-supplementary-operational-statistics-cache-title": "Статистика кешу", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> показує вибраний набір кеш-дотичної статистику", "smw-admin-supplementary-operational-table-statistics-title": "Статистика таблиць", "smw-admin-supplementary-operational-table-statistics-short-title": "статистика таблиць", "smw-admin-supplementary-operational-table-statistics-intro": "Генерує <u>$1</u> для вибраного набору таблиць", "smw-admin-supplementary-operational-table-statistics-explain": "Цей розділ містить статистику вибраних таблиць, призначену допомогти адміністраторам та кураторам даних із прийняттям виважених рішень про стан бекенду та рушія зберігання", "smw-admin-supplementary-operational-table-statistics-legend": "Легенда описує деякі з ключів, які використовуються для статистики таблиць, і включає:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> — загальна кількість рядків у таблиці", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> — останній ідентифікатор, що зараз використовується\n* <code>duplicate_count</code> — кількість дублікатів, виявлених в id_table (див. також [[Special:SemanticMediaWiki/duplicate-lookup|Пошук дублікатів сутностей]]) \n* <code>rows.rev_count</code> — кількість рядків, які мають призначений revision_id, що свідчить про пряме посилання на вікісторінку\n* <code>rows.smw_namespace_group_by_count</code> — кількість агрегованих рядків для просторів назв, використаних у таблиці\n* <code>rows.smw_proptable_hash.query_match_count</code> — кількість підоб'єктів запитів із відповідним посиланням на таблицю\n* <code>rows.smw_proptable_hash.query_null_count</code> — кількість підоб'єктів запитів без посилання на таблицю (незакріплене, незв'язане посилання)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> — відсоток термінів, які є унікальними (низький відсоток свідчить про те, що вміст таблиці та її індекс займають переважно терміни, які повторюються) \n* <code>rows.terms_occurrence.single_occurrence_total_count</code> — кількість термінів, які трапляються лише раз\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> — кількість термінів, які трапляються більш ніж один раз", "smw-admin-supplementary-elastic-version-info": "Версія", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> показує деталі щодо налаштування та статистику індексу", "smw-admin-supplementary-elastic-docu": "Ця сторінка містить інформацію про налаштування, мапування, здоров'я, а також статистику індексу, пов'язані з кластером Elasticsearch, який поєднаний із Семантичною MediaWiki та її [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Підтримувані функції", "smw-admin-supplementary-elastic-settings-title": "Налаштування (індекси)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> використовується Elasticsearch для керування індексами Семантичної MediaWiki", "smw-admin-supplementary-elastic-mappings-title": "Мапування", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> для перелічення індексів і мапувань полів", "smw-admin-supplementary-elastic-mappings-docu": "Ця сторінка містить подробиці щодо мапувань полів, використовуваних поточним індексом. Рекомендується прослідковувати мапування у зв'язку з <code>index.mapping.total_fields.limit</code> (зазначає максимальну кількість полів у індексі).", "smw-admin-supplementary-elastic-mappings-docu-extra": "Опція <code>property_fields</code> стосується кількості індексованих основних полів, тоді як <code>nested_fields</code> стосується акумульованої кількості додаткових полів, прикріплених до основного поля для підтримки специфічних патернів структурованого пошуку.", "smw-admin-supplementary-elastic-mappings-summary": "Підсумок", "smw-admin-supplementary-elastic-mappings-fields": "Мапування полів", "smw-admin-supplementary-elastic-nodes-title": "Вузли", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> показує статистику вузлів", "smw-admin-supplementary-elastic-indices-title": "Індекси", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> надає загальний огляд доступних індексів та їхню статистику", "smw-admin-supplementary-elastic-statistics-title": "Статистика", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> показує статистику рівня індексів", "smw-admin-supplementary-elastic-statistics-docu": "Ця сторінка дає загальне уявлення про статистику індексів для різних операцій, що відбуваються на рівні індексів; статистика, що виводиться, збирається шляхом праймеріз та загальних агрегацій. [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html Сторінка довідки] містить детальний опис доступної статистики індексів.", "smw-admin-supplementary-elastic-status-replication": "Статус реплікації", "smw-admin-supplementary-elastic-status-last-active-replication": "Остання активна реплікація: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Інтервал оновлення: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Затримка завдань з відновлення: $1 (приблизно)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Затримка завдань із вживання (файл): $1 (приблизно)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Реплікацію заблоковано: $1 (перебудова у процесі)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Моніторинг реплікації (активно): $1", "smw-admin-supplementary-elastic-replication-header-title": "Статус реплікації", "smw-admin-supplementary-elastic-replication-function-title": "Реплікація", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> показує інформацію про невдалі реплікації", "smw-admin-supplementary-elastic-replication-docu": "Ця сторінка надає інформацію про [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring статус реплікації] сутностей, щодо яких за допомогою кластера Elasticsearch було повідомлено про наявність проблем. Рекомендується переглянути перелічені сутності та очистити кеш контенту для того, аби підтвердити, що проблема мала тимчасовий характер.", "smw-admin-supplementary-elastic-replication-files-docu": "Слід зазначити, що для списку файлів необхідно спершу виконати завдання із [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion поглинання файлів] і дочекатися завершення цього процесу.", "smw-admin-supplementary-elastic-replication-files": "Файли", "smw-admin-supplementary-elastic-replication-pages": "Сторінки", "smw-admin-supplementary-elastic-endpoints": "Кінцеві точки", "smw-admin-supplementary-elastic-config": "Конфігураці<PERSON>", "smw-admin-supplementary-elastic-no-connection": "Ця вікі наразі '''не може''' встановити зв'язок із кластером Elasticsearch; будь ласка, зв'яжіться з адміністратором цієї вікі, щоб дослідити причину проблеми, оскільки ця проблема піддає сумніву спроможність системи до індексування й роботи із запитами.", "smw-list-count": "Список містить $1 {{PLURAL:$1|запис|записи|записів}}.", "smw-property-label-uniqueness": "Назва «$1» збігається принаймні з одним іншим представленням властивості. Будь ласка, проконсультуйтеся зі [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness сторінкою довідки], щоб дізнатись, як вирішити цю проблему.", "smw-property-label-similarity-title": "Звіт про подібність назв властивостей", "smw-property-label-similarity-intro": "<u>$1</u> обчислює подібності наявних назв властивостей", "smw-property-label-similarity-threshold": "Поріг:", "smw-property-label-similarity-type": "Показувати ідентифікатор типу", "smw-property-label-similarity-noresult": "Для вибраних опцій не знайдено жодних результатів.", "smw-property-label-similarity-docu": "Ця сторінка порівнює і звітує про [https://www.semantic-mediawiki.org/wiki/Property_similarity синтаксичну відстань] (не плутати з семантичною або лексичною подібністю) між двома назвами властивостей і повідомляє про них, якщо вони перетинають поріг. Звіт може допомогти відфільтрувати властивості, які мають помилки у назві або є еквівалентними і репрезентують той же концепт (див. спеціальну сторінку [[Special:Properties|властивостей]], що допомагає прояснити концепт і використання властивостей, отриманих у звіті). Поріг можна налаштувати, щоб розширити або звузити відстань, яка використовується для наближеного співставлення. <code>[[Property:$1|$1]]</code> використовується для виключення властивостей з аналізу.", "smw-admin-operational-statistics": "Ця сторінка містить операційну статистику, зібрану в або з функцій, пов'язаних із Семантичною MediaWiki. Розширений список статистичних даних, специфічних для вікі, можна переглянути [[Special:Statistics|<b>тут</b>]].", "smw_adminlinks_datastructure": "Структура даних", "smw_adminlinks_displayingdata": "Відображення даних", "smw_adminlinks_inlinequerieshelp": "Довідка із вбудованих запитів", "smw-page-indicator-usage-count": "Приблизний [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count підрахунок використання]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Властивість, визначена {{PLURAL:$1|користувачем|системою}}", "smw-property-indicator-last-count-update": "Приблизна кількість використань\nВостаннє оновлено: $1", "smw-concept-indicator-cache-update": "Кешована кількість\nВостаннє оновлено: $1", "smw-createproperty-isproperty": "Це властивість типу $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|1=Дозволене значення для цієї властивості є|Дозволені значення значення для цієї властивості є}}:", "smw-paramdesc-category-delim": "Розділ<PERSON><PERSON><PERSON>ч", "smw-paramdesc-category-template": "Шаблон для форматування елементів із", "smw-paramdesc-category-userparam": "Параметр, який передається в шаблон", "smw-info-par-message": "Повідомлення для відображення.", "smw-info-par-icon": "<PERSON><PERSON><PERSON>ок, щоб показати \"info\" або \"warning\".", "prefs-smw": "Семантична MediaWiki", "prefs-general-options": "Загальні опції", "prefs-extended-search-options": "Розширений пошук", "prefs-ask-options": "Семантичний пошук", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Семантична MediaWiki] та пов'язані розширення забезпечують можливість індивідуальних налаштувань для групи обраних функцій. Список індивідуальних налаштувань з їхнім описом та характеристикою доступний на цій [http://semantic-mediawiki.org/wiki/Help:User_preferences сторінці довідки].", "smw-prefs-ask-options-tooltip-display": "Показувати текст параметра як інформаційну підказку на спеціальній сторінці [[Special:Ask|формулювання запиту]] #ask", "smw-prefs-ask-options-compact-view-basic": "Увімкнути базовий компактний режим перегляду", "smw-prefs-help-ask-options-compact-view-basic": "Якщо увімкнено, показує зменшений набір посилань у компактному режимі перегляду Special:Ask.", "smw-prefs-general-options-time-correction": "Увімкнути коригування часу для спеціальних сторінок за допомогою локального налаштування [[Special:Preferences#mw-prefsection-rendering|часового зміщення]]", "smw-prefs-general-options-jobqueue-watchlist": "Показувати на моїй персональній панелі список спостереження за чергою завдань", "smw-prefs-help-general-options-jobqueue-watchlist": "Якщо увімкнено, показує [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist список] обраних завдань, що очікують своєї черги, разом із приблизними розмірами черги.", "smw-prefs-general-options-disable-editpage-info": "Вимкнути вступний текст на сторінці редагування", "smw-prefs-general-options-disable-search-info": "Вимкнути довідкову інформацію про синтаксис на стандартній сторінці пошуку", "smw-prefs-general-options-suggester-textinput": "Увімкнути підтримку вводу для семантичних сутностей", "smw-prefs-help-general-options-suggester-textinput": "Якщо увімкнено, дає змогу використовувати [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance підтримку вводу] для пошуку властивостей, концептів та категорій у контексті введеного пошукового запиту.", "smw-ui-tooltip-title-property": "Властивість", "smw-ui-tooltip-title-quantity": "Конверсія одиниць", "smw-ui-tooltip-title-info": "Інформація", "smw-ui-tooltip-title-service": "Службове посилання", "smw-ui-tooltip-title-warning": "Попередження", "smw-ui-tooltip-title-error": "Помилка", "smw-ui-tooltip-title-parameter": "Параметр", "smw-ui-tooltip-title-event": "Подія", "smw-ui-tooltip-title-note": "Примітка", "smw-ui-tooltip-title-legend": "Легенда", "smw-ui-tooltip-title-reference": "Посилання на джерело", "smw_unknowntype": "Тип \"$1\" для цієї властивості некоректний", "smw-concept-cache-text": "Поняття має $1 {{PLURAL:$1|сторінку|сторінки|сторінок}}, і востаннє було оновлено $3, $2.", "smw_concept_header": "Стор<PERSON>нки, що належать до концепції «$1»", "smw_conceptarticlecount": "{{PLURAL:$1|1=Показана|Показані}} нижче $1 {{PLURAL:$1|сторінка|сторінки|сторінок}}.", "smw-qp-empty-data": "Запитані дані не можуть відображатися через деякі недостатні критерії відбору.", "right-smw-admin": "Доступ до завдань адміністрування (Семантична MediaWiki)", "right-smw-patternedit": "Доступ до редагування для обслуговування дозволених регулярних виразів та шаблонів (Семантична MediaWiki)", "right-smw-pageedit": "Доступ до редагувань сторінок з анотацією <code>Is edit protected</code> (Семантична MediaWiki)", "right-smw-schemaedit": "Редагування [https://www.semantic-mediawiki.org/wiki/Help:Schema сторінок схеми] (Семантична MediaWiki)", "right-smw-viewjobqueuewatchlist": "Доступ до функції [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist списку спостереження] за чергою завдань (Семантична MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Доступ до інформації про невідповідність версії, пов'язаної із сутністю (Семантична MediaWiki)", "right-smw-vieweditpageinfo": "Перегляд [https://www.semantic-mediawiki.org/wiki/Help:Edit_help довідку з редагування] (Semantic MediaWiki)", "restriction-level-smw-pageedit": "захищено (лише прийнятні користувачі)", "action-smw-patternedit": "редагування регулярних виразів, що використовуються Семантичною MediaWiki", "action-smw-pageedit": "редагування сторінок з анотацією <code>Is edit protected</code> (Семантична MediaWiki)", "group-smwadministrator": "Адміністратор<PERSON> (Семантична MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|адміністратор (Семантична MediaWiki)|адміністраторка (Семантична MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:Ад<PERSON><PERSON>н<PERSON>стратор<PERSON> (Семантична MediaWiki)", "group-smwcurator": "Кура<PERSON><PERSON><PERSON><PERSON> (Семантична MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|кура<PERSON>о<PERSON> (Семантична MediaWiki)|кураторка (Семантична MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Семантична MediaWiki)", "group-smweditor": "Редактори (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|учасник (Semantic MediaWiki)|учасниця (Semantic MediaWiki)}}", "grouppage-smweditor": "{{ns:project}}:Уча<PERSON>ники (Semantic MediaWiki)", "action-smw-admin": "доступ до завдань адміністрування Семантичної MediaWiki", "action-smw-ruleedit": "редагувати сторінки правил (Семантична MediaWiki)", "smw-property-namespace-disabled": "[https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks Простір назв] цієї властивості зараз вимкнений, тому неможливо задекларувати тип, чи інші специфічні характеристики цієї властивості.", "smw-property-predefined-default": "«$1» — попередньо визначена властивість типу $2.", "smw-property-predefined-common": "Це властивість є попередньо розгорнутою (також відома як [https://www.semantic-mediawiki.org/wiki/Help:Special_properties спеціальна властивість]) і несе додаткові адміністративні привілеї, але може використовуватися, як будь-яка інша [https://www.semantic-mediawiki.org/wiki/Property визначена користувачем властивість].", "smw-property-predefined-ask": "«$1» — попередньо визначена властивість, яка представляє метаінформацію (у вигляді [https://www.semantic-mediawiki.org/wiki/Subobject підоб'єкта]) про індивідуальні запити, і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "«$1» — попередньо визначена властивість, яка збирає ряд умов, використаних у запиті, і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "«$1» — попередньо визначена властивість, яка інформує про глибину запиту, і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-askde": "Це — числове значення, обчислене на основі гніздування підзапитів, ланцюжків властивостей, і доступних елементів опису із виконанням запиту, що обмежується параметром конфігурації <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "«$1» — це попередньо визначена властивість, що описує параметри, які впливають на результат запиту, і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-askpa": "Це частина колекції властивостей, які визначають [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler профіль запиту].", "smw-sp-properties-docu": "На цій сторінці перелічено [https://www.semantic-mediawiki.org/wiki/Property властивості], які доступні для цієї вікі, та їх використання. Для найсвіжішої статистики рекомендується, щоб скрипт [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics статистики властивості] запускався регулярно. Для диференційованого перегляду почитайте спеціальну сторінку  [[Special:UnusedProperties|невикористані]] або [[Special:WantedProperties|бажані властивості]].", "smw-sp-properties-cache-info": "Перераховані дані отримані з [https://www.semantic-mediawiki.org/wiki/Caching кеш], і востаннє оновлені $1.", "smw-sp-properties-header-label": "Список властивостей", "smw-admin-settings-docu": "Відображає список всіх стандартних і локалізованих налаштувань, які мають стосунок до середовища Семантичної MediaWiki. За детальною інформацією про окремі параметри зверніться до сторінки довідки щодо [https://www.semantic-mediawiki.org/wiki/Help:Configuration налаштувань].", "smw-sp-admin-settings-button": "Створити список налаштувань", "smw-admin-idlookup-title": "По<PERSON><PERSON>к", "smw-admin-idlookup-docu": "Цей розділ показує технічні подробиці про окрему сутність (вікісторінку, підоб'єкт, властивість тощо) в Семантичній MediaWiki. Введені дані можуть бути числовим ідентифікатором або рядковим значенням, відповідно до поля пошуку, проте будь-яке посилання на ідентифікатор стосується Семантичної MediaWiki, а не ідентифікатора версії чи сторінки в MediaWiki.", "smw-admin-iddispose-title": "Ліквідація", "smw-admin-iddispose-docu": "Зверніть увагу, що операція з ліквідації не обмежена і призведе до вилучення ідентифікатора внутрішнього об'єкта з рушія бази даних після підтвердження. Будь ласка, виконуйте цю дію '''обережно''' і лише після того, як ознайомитеся з [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal документацією].", "smw-admin-iddispose-done": "ID «$1» вилучено з бекенду бази даних.", "smw-admin-iddispose-references": "Ідентифікатор \"$1\" {{PLURAL:$2|не має активних посилань|має щонайменше одне активне посилання}}:", "smw-admin-iddispose-references-multiple": "Список збігів із принаймні одним активним записом посилання.", "smw-admin-iddispose-no-references": "Пошук не зміг зіставити \"$1\" з елементом таблиці.", "smw-admin-idlookup-input": "Пошук:", "smw-admin-objectid": "Ідентифікатор:", "smw-admin-tab-general": "Огляд", "smw-admin-tab-notices": "Сповіщення про застарілість", "smw-admin-tab-maintenance": "Обслуговування", "smw-admin-tab-supplement": "Додаткові функції", "smw-admin-tab-registry": "Реєстр", "smw-admin-tab-alerts": "Звістки", "smw-admin-alerts-tab-deprecationnotices": "Сповіщення про застарілість", "smw-admin-alerts-tab-maintenancealerts": "Звістки щодо технічного обслуговування", "smw-admin-alerts-section-intro": "Цей розділ показує звістки і сповіщення, пов'язані з налаштуваннями, операціями та іншими типами активності, які було класифіковано як такі, що потребують уваги адміністратора чи користувача з відповідними правами.", "smw-admin-maintenancealerts-section-intro": "Підняті у вказаних звістках питання слід вирішити, і навіть якщо вони не є істотними, очікується, що це допоможе покращити процес технічного обслуговування системи й операцій у ній.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Оптимізація таблиць", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Система виявила, що остання [https://www.semantic-mediawiki.org/wiki/Table_optimization оптимізація таблиць] відбулася $2 {{PLURAL:$2|день тому|дні тому|днів тому}} (запис від $1), що перевищує допустимий поріг обслуговування, який становить $3 {{PLURAL:$3|день|дні|днів}}. Як зазначено в документації, запуски оптимізації дозволять планувальникові запитів приймати кращі рішення про запити, а тому рекомендовано запускати оптимізацію таблиць регулярно.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Застар<PERSON>лі сутності", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Система нарахувала $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities застарілих сутностей] і досягла критичного рівня нездійснення обслуговування, перевищивши допустимий поріг, який становить $2. Рекомендується запустити скрипт обслуговування [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>].", "smw-admin-maintenancealerts-invalidentities-alert-title": "Помилкові сутності", "smw-admin-maintenancealerts-invalidentities-alert": "Система пов'язала $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|сутність|сутності}}] з [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace простором назв, що не підтримується] тому рекомендується запустити наступний із скриптів обслуговування: [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] або [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>].", "smw-admin-deprecation-notice-section": "Семантична MediaWiki", "smw-admin-configutation-tab-settings": "Налаштування", "smw-admin-configutation-tab-namespaces": "Простори назв", "smw-admin-configutation-tab-schematypes": "Типи схем", "smw-admin-maintenance-tab-tasks": "Завдання", "smw-admin-maintenance-tab-scripts": "Скрипти обслуговування", "smw-admin-maintenance-no-description": "Немає опису.", "smw-admin-maintenance-script-section-title": "Список доступних скриптів обслуговування", "smw-admin-maintenance-script-section-intro": "Подані скрипти обслуговування вимагають прав адміністратора й доступу до командного рядка, щоб можна було виконувати перелічені скрипти.", "smw-admin-maintenance-script-description-dumprdf": "RDF-експорт існуючих трійок.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Цей скрипт використовується для керування кешем концептів для Семантичної вікі, де він може створювати, вилучити й оновлювати вибраний кеш.", "smw-admin-maintenance-script-description-rebuilddata": "Відтворює всі семантичні дані в базі даних шляхом швидкого аналізу всіх сторінок, які можуть мати семантичні дані.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Відбудовує індекс Elasticsearch (лише для інстал<PERSON><PERSON><PERSON><PERSON>, які використовують <code>ElasticStore</code>) шляхом швидкого аналізу всіх сутностей, які мають семантичні дані.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Знайти сутності, яких бракує в Elasticsearch (лише для тих інсталя<PERSON><PERSON><PERSON>, які використовують <code>ElasticStore</code>) і запланувати відповідні завдання із оновлення.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Відбудовує індекс повнотекстового пошуку <code>SQLStore</code> (для інсталяцій, у яких це налаштування увімкнено).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Відбудовує статистику використання для всіх сутностей властивостей.", "smw-admin-maintenance-script-description-removeduplicateentities": "Вилучає дублікати сутностей, виявлені у вибраних таблицях, які не мають активних посилань на джерела.", "smw-admin-maintenance-script-description-setupstore": "Встановлює бекенд зберігання та запитів, як це визначено в <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Оновлює поле <code>smw_sort</code> у <code>SQLStore</code> (відповідно до налаштування [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "Заповнює поле <code>smw_hash</code> для рядків, які не мають відповідного значення.", "smw-admin-maintenance-script-description-purgeentitycache": "Записи очищення кешу для відомих сутностей та їх пов'язаних даних.", "smw-admin-maintenance-script-description-updatequerydependencies": "Оновити запити і залежності запитів (див. налаштування [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Позбутися застарілих посилань на сутності та запити.", "smw-admin-maintenance-script-description-runimport": "Заповнити та імпортувати автоматично виявлений контент із [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Cкрипти оновлення", "smw-admin-maintenance-script-section-rebuild": "Скрипти перебудови", "smw-livepreview-loading": "Завантаження…", "smw-sp-searchbyproperty-description": "На цій сторінці розміщено простий [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces інтерфейс перегляду] для пошуку сутностей, описаних властивістю й іменним значенням. До інших доступних пошукових інтерфейсів належать [[Special:PageProperty|пошук властивостей сторінки]] та [[Special:Ask|майстер запитів ask]].", "smw-sp-searchbyproperty-resultlist-header": "Список результатів", "smw-sp-searchbyproperty-nonvaluequery": "Список значень, яким призначена властивість «$1».", "smw-sp-searchbyproperty-valuequery": "Список сторінок, що мають властивість «$1» із анотованим значенням «$2».", "smw-datavalue-number-textnotallowed": "«$1» не може бути присвоєно заявленому типу числа типу зі значенням $2.", "smw-datavalue-number-nullnotallowed": "«$1» повернув значення «NULL», яке не дозволене в якості числа.", "smw-editpage-annotation-enabled": "Ця сторінка підтримує семантичні анотації в тексті (e.g. <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) для побудови структурованого контенту, у який можна робити запити, що забезпечується Семантичною Semantic MediaWiki. Для комплексного опису, як використовувати анотації або парсерну функцію ask, будь ласка, відвідайте сторінки [https://www.semantic-mediawiki.org/wiki/Help:Getting_started початку роботи], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation анотації в тексті] та довідкову сторінку [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries рядкових запитів].", "smw-editpage-annotation-disabled": "На цій сторінці не можливі семантичні анотації в тексті через обмеження простору назв. Деталі щодо того, як дозволити простір назв, можна знайти на довідковій сторінці [https://www.semantic-mediawiki.org/wiki/Help:Configuration конфігурації].", "smw-editpage-property-annotation-enabled": "Цю властивість можна розширити з допомогою семантичних анотацій для вказання типу даних (e.g. <nowiki>\"[[Has type::Page]]\"</nowiki>) або інших підтримуваних тверджень (e.g. <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). Опис, як розширити цю сторінки, див на сторінці довідки про [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration заявлення властивості] або [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes список доступних типів даних].", "smw-editpage-property-annotation-disabled": "Цю властивість не можна розширити анотацією типу даних (e.g. <nowiki>\"[[Has type::Page]]\"</nowiki>), оскільки вона уже попередньо визначена (див. детальнішу інформацію на довідковій сторінці про [https://www.semantic-mediawiki.org/wiki/Help:Special_properties спеціальні властивості]).", "smw-editpage-concept-annotation-enabled": "Цей концепт можна розширити з використанням парсерної функції #concept. Опис, як використовувати #concept, див. на довідковій сторінці про [https://www.semantic-mediawiki.org/wiki/Help:Concepts концепт].", "smw-search-syntax-support": "Пошукові запити підтримують [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search синтаксис семантичних запитів] для пошуку результатів з використанням Семантичної MediaWiki.", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Асистента вводу] також увімкнено для полегшення попереднього вибору доступних властивостей і категорій.", "smw-search-help-intro": "Введення <code><nowiki>[[ ... ]]</nowiki></code> повідомить процесорові вводу, що він має скористатися бекендом пошуку Семантичної MediaWiki. Слід зауважити, що поєднання <code><nowiki>[[ ... ]]</nowiki></code> з неструктурованим текстовим пошуком, таке як <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> не підтримується.", "smw-search-help-structured": "Структурований пошук:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (як [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context фільтрований контекст])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (із [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context контекстом запиту])", "smw-search-help-proximity": "Приблизний пошук (коли властивість невідома, доступно '''лише''' для тих бекендів, які забезпечують інтеграцію повнотекстового пошуку):\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code> (пошук «lorem» та «ipsum» у всіх індексованих документах)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (пошук збігів «lorem ipsum» як цілісної фрази)", "smw-search-help-ask": "Подані посилання пояснять, як користуватися синтаксисом <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Вибір сторінок] — описує, як вибирати сторінки й будувати умови\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Пошукові оператори] — перелічує доступні пошукові оператори, включно з тими, що використовуються для пошуку в діапазоні, а також запитами з використанням пошукових шаблонів (wildcards)", "smw-search-input": "Введення тексту й пошук", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Підтримка вводу] надається для поля вводу і вимагає використання одного з таких префіксів:\n\n*<code>p:</code> для увімкнення пропозицій властивостей (напр., <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> для увімкнення пропозицій категорій\n\n*<code>con:</code> для увімкнення пропозицій концептів", "smw-search-syntax": "Син<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile": "Додатково", "smw-search-profile-tooltip": "Функції пошуку у зв'язку із Семантичною MediaWiki", "smw-search-profile-sort-best": "Найкр<PERSON><PERSON><PERSON> збіг", "smw-search-profile-sort-recent": "Найновіші", "smw-search-profile-sort-title": "Назва", "smw-search-profile-extended-help-intro": "[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile Розширений профііль] спеціальної сторінки Special:Search надає доступ до пошукових функцій, специфчних для Семантичної MediaWiki та її підтримуваного бекенду запитів.", "smw-search-profile-extended-help-sort": "Встановлює налаштування сортування для показу результатів:", "smw-search-profile-extended-help-sort-title": "* «Назва» використовуватиме назву сторінки (чи назву, яку використовується для показу) як критерій сортування", "smw-search-profile-extended-help-sort-recent": "* «Найновіші» покаже спершу сутності, які недавно хтось змінював (підоб'єктні сутності буде приховано, оскільки вони не анотуються за допомогою [[Property:Modification date|дати редагування]])", "smw-search-profile-extended-help-sort-best": "* «Найкращий збіг» сортуватиме сутності за [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy відповідністю] на основі оцінок, які надає бекенд", "smw-search-profile-extended-help-form": "Форми надаються (якщо обслуговуються) для порівняння специфічних випадків використання шляхом розкриття різних полів властивостей та значень для звуження процесу вводу та полегшення для користувачів процесу подання пошукового запиту (див. $1).", "smw-search-profile-extended-help-namespace": "Поле вибору простору назв буде приховано, коли буде обрано форму, але його можна показати за допомогою кнопки «показати/сховати».", "smw-search-profile-extended-help-search-syntax": "Поле пошуку підтримує синтаксис функції парсера <code>#ask</code> для семантичного пошуку. Корисні вирази включають:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> для пошуку будь-чого, що містить «...» і є особливо корисним, коли контекст пошуку чи залучені властивості невідомі (напр., <code>in:(lorem && ipsum)</code> є еквівалентним <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> для пошуку будь-чого, що містить «...» у точно такому ж порядку", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> Для порівняння будь-якої сутності з властивістю «...» (напр., <code>has:(Foo && Bar)</code> є еквівалентним <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> для уникнення збігів із будь-якими сутностями, що містять «...»", "smw-search-profile-extended-help-search-syntax-prefix": "* Доступні й визначені також додаткові, нестандартні префікси, на кшталт: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Деякі вирази зарезервовані, наприклад: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Деякі з перелічених операцій корисні лише в поєднанні з увімкненим повнотекстовим індексом або з ElasticStore.''", "smw-search-profile-extended-help-query": "Використано <code><nowiki>$1</nowiki></code> як запит.", "smw-search-profile-extended-help-query-link": "Детальн<PERSON><PERSON>е, будь ласка, дивіться на $1.", "smw-search-profile-extended-help-find-forms": "доступні форми", "smw-search-profile-extended-section-sort": "Сортувати за", "smw-search-profile-extended-section-form": "Форми", "smw-search-profile-extended-section-search-syntax": "Пошуковий термін", "smw-search-profile-extended-section-namespace": "Простір назв", "smw-search-profile-extended-section-query": "Запит", "smw-search-profile-link-caption-query": "конструктор запитів", "smw-search-show": "Показати", "smw-search-hide": "Приховати", "log-name-smw": "<PERSON><PERSON><PERSON><PERSON><PERSON> Семантичної MediaWiki", "log-show-hide-smw": "$1 журнал Семантичної MediaWiki", "logeventslist-smw-log": "<PERSON><PERSON><PERSON><PERSON><PERSON> Семантичної MediaWiki", "log-description-smw": "Діяльність щодо [https://www.semantic-mediawiki.org/wiki/Help:Logging увімкнених типів подій], яку реєструє Семантична MediaWiki та її компоненти.", "logentry-smw-maintenance": "Поді<PERSON>, пов'язані з підтримкою, що відбулися у Семантичній MediaWiki", "smw-datavalue-import-unknown-namespace": "Простір назв імпорту «$1» невідомий. Будь ласка, впевніться, що деталі імпорту OWL доступні через [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Не вдалося знайти URI простору назв «$1» в [[MediaWiki:Smw import $1|імпорті $1]].", "smw-datavalue-import-missing-type": "Не було знайдено визначення типу для «$1» в [[MediaWiki:Smw import $2|імпорті $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Імпорт $1]]", "smw-datavalue-import-invalid-value": "«$1» не є дійсним форматом і має складатися з \"простір назв\":\"ідентифікатор\" (e.g. \"foaf:name\").", "smw-datavalue-import-invalid-format": "Очікувалося, що рядок «$1» мав бути поділеним на чотири частини, але формат виявився незрозумілим.", "smw-property-predefined-impo": "«$1» — попередньо визначена властивість, яка описує зв'язок з [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary імпортованим словником], і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "«$1» — попередньо визначена властивість, яка описує [[Special:Types|тип даних]] властивості, і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "«$1» — попередньо визначена властивість, яка репрезентує побудову [https://www.semantic-mediawiki.org/wiki/Help:Container контейнера], що забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "Контейнер дозволяє акумулювати призначення властивість-значення подібно до нормальної вікісторінки, але в межах іншого простору властивості, будучи пов'язаним з вбудовуваним об'єктом.", "smw-property-predefined-errp": "«$1» — це попередньо визначена властивість що відслідковує помилки вводу для анотацій нерегулярних значень і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "В більшості випадків спричинено невідповідністю типу або обмеженням [[Property:Allows value|значення]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value «$1»] — попередньо визначена властивість, яка може визначати список дозволених значень, щоб обмежувати призначення значень для властивості, і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list «$1»] — попередньо визначена властивість, яка може зазначати посилання на список, який містить допустимі значення для обмеження призначень значень для властивості, і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "Властивість «$1» має особливе призначення і не може застосовуватися користувачем як властивість анотації.", "smw-datavalue-property-restricted-declarative-use": "Властивість «$1» — деклара<PERSON><PERSON><PERSON><PERSON>, і може використовуватись тільки на сторінках властивостей або категорій.", "smw-datavalue-property-create-restriction": "Властивість «$1» не існує, а користувач не має права «$2» (див. [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode режим авторитету]) для створення чи анотування значень із незатвердженою властивістю.", "smw-datavalue-property-invalid-character": "«$1» містить символ «$2» як частину назви властивості, і тому класифікована як недійсна.", "smw-datavalue-property-invalid-chain": "Використання «$1» як ланцюжка властивостей протягом процесу анотації заборонене.", "smw-datavalue-restricted-use": "Значення даних «$1» було позначено для обмеженого використання.", "smw-datavalue-invalid-number": "«$1» не може бути інтерпретовано як число.", "smw-query-condition-circular": "Можливу циклічну умову було виявлено у «$1».", "smw-query-condition-empty": "В описі запиту існує порожня умова.", "smw-types-list": "Список типів даних", "smw-types-default": "«$1» є вбудованим типом даних.", "smw-types-help": "Подальшу інформацію і приклади можна знайти на цій [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 сторінці довідки].", "smw-type-anu": "«$1» — це варіант типу даних [[Special:Types/URL|URL]] і здебільшого використовується для експортної декларації ''owl:AnnotationProperty''.", "smw-type-boo": "«$1» — це звичайний тип даних для опису значення істина/хиба.", "smw-type-cod": "«$1» — це варіант типу даних [[Special:Types/Text|Текст]] для використання у технічних текстах довільної довжини, таких як списки вихідного коду.", "smw-type-geo": "«$1» — це тип даних, який описує географічне положення і вимагає [https://www.semantic-mediawiki.org/wiki/Extension:Maps розширення «Карти»] для розширеного функціоналу.", "smw-type-tel": "«$1» — це спеціальний тип даних для опису міжнародних телефонних номерів згідно з RFC 3966.", "smw-type-txt": "«$1» — це основний тип даних для опису рядків довільної довжини.", "smw-type-dat": "«$1» — це основний тип даних для представлення моментів часу в єдиному форматі.", "smw-type-ema": "«$1» — спеціальний тип даних для зберігання адреси електронної пошти.", "smw-type-tem": "«$1» — спеціальний числовий тип даних для зберігання температури.", "smw-type-qty": "«$1» — тип даних для опису кількостей із числовою репрезентацією та одиницею вимірювання.", "smw-type-rec": "«$1» — контейнерний тип даних, що вказує список типізованих властивостей у фіксованому порядку.", "smw-type-extra-tem": "Схема конвертації включає підтримувані одиниці, такі як одиниці за Кельвіном, Цельсієм, Фаренгейтом та Ранкіном.", "smw-type-tab-properties": "Властивості", "smw-type-tab-types": "Типи", "smw-type-tab-type-ids": "Ідентифікатори типів", "smw-type-tab-errors": "Помилки", "smw-type-primitive": "Основні", "smw-type-contextual": "Залежні", "smw-type-compound": "Складні", "smw-type-container": "Кон<PERSON>ейнер", "smw-type-no-group": "Некласифіковані", "smw-special-pageproperty-description": "Ця сторінка надає інтерфейс перегляду для пошуку всіх значень властивості та заданої сторінки. До інших доступних пошукових інтерфейсів належать [[Special:SearchByProperty|пошук властивостей]] та [[Special:Ask|конструктор запитів «ask»]].", "smw-property-predefined-errc": "«$1» — це попередньо визначена властивість, що забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki] і репрезентує помилки, які виникли у зв'язку з неправильними анотаціями значень чи обробкою вводу.", "smw-property-predefined-long-errc": "Помилки накопичуються в [https://www.semantic-mediawiki.org/wiki/Help:Container контейнері], який також може включати посилання на властивість, яка й спричинила невідповідність.", "smw-property-predefined-errt": "«$1» — це попередньо визначена властивість, що містить текстовий опис помилки і забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Визначений користувачем підоб'єкт містив недійсну схему йменування. Крапка ($1) у перших п'яти символах призначена для використання виключно розширеннями. Ви можете задати [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier іменований ідентифікатор].", "smw-datavalue-record-invalid-property-declaration": "Визначення запису містить властивість «$1», яка сама по собі оголошена типом запису, і це не допускається.", "smw-property-predefined-mdat": "«$1» — попередньо визначена властивість, що відповідає даті останньої зміни об'єкта, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-cdat": "«$1» — попередньо визначена властивість, що відповідає даті першої версії суб'єкта, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-newp": "«$1» — попередньо визначена властивість, яка вказує, чи є суб'єкт новим чи ні, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-ledt": "«$1» — попередньо визначена властивість, що містить назву сторінки того користувача, який створив першу версію, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-mime": "«$1» — попередньо визначена властивість, що описує MIME-тип завантаженого файлу, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-media": "«$1» — попередньо визначена властивість, що описує медіатип завантаженого медіафайлу, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-askfo": "«$1» — попередньо визначена властивість, що містить назву кінцевого формату, використаного в запиті, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-askst": "«$1» — попередньо визначена властивість, що описує умови запиту у формі рядка, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-askdu": "«$1» — попередньо визначена властивість, що містить значення часу (в секундах), який був використаний на завершення виконання запиту, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-asksc": "«$1» — попередньо визначена властивість, що надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki] та ідентифікує альтернативні (напр., віддалені, федеративні) джерела запитів.", "smw-property-predefined-askco": "«$1» — попередньо визначена властивість, що надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki] для опису стану запиту чи його компонентів.", "smw-property-predefined-long-askco": "Номер чи номери, призначені для репрезентації внутрішнього кодифікованого стану, який пояснений на [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler сторінці довідки].", "smw-property-predefined-prec": "«$1» — попередньо визначена властивість, що описує [https://www.semantic-mediawiki.org/wiki/Help:Display_precision точність показу] (в десяткових цифрах) для числових типів даних.", "smw-property-predefined-attch-link": "«$1» — попередньо визначена властивість, яка збирає посилання на вбудовані файли та зображення і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-inst": "«$1» — внутрішня попередньо визначена властивість, що зберігає інформацію про категорію незалежно від MediaWiki, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-unit": "«$1» — декларативна попередньо визначена властивість для визначення одиниць показу для властивостей числового типу, і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-unit": "Список через кому дає змогу описати одиниці чи формати, які слід використати для показу.", "smw-property-predefined-conv": "«$1» — декларативна попередньо визначена властивість для визначення фактору конвертації для певної одиниці чи фізичної кількості; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-serv": "«$1» — декларативна попередньо визначена властивість для додавання сервісних посилань до властивості; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-redi": "«$1» — внутрішня попередньо визначена властивість для записування перенаправлень; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-subp": "«$1» — декларативна попередньо визначена властивість для визначення того, чи є певна властивість [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of підвластивістю] іншої властивості; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-subc": "«$1» — попередньо визначена властивість для визначення того, чи є категорія [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of підкатегорією] іншої категорії; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-conc": "«$1» — внутрішня попередньо визначена властивість для визначення асоційованого концепту; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-err-type": "«$1» — попередньо визначена властивість для ідентифікації групи чи класу [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors помилок опрацювання]; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-skey": "«$1» — внутрішня попередньо визначена властивість для утримування інформації про засади сортування («a sort reference»); надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-pplb": "«$1» — декларативна попередньо визначена властивість для вказування [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label бажаної мітки властивості]; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-chgpro": "«$1» — попередньо визначена властивість для утримування інформації про [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation поширення змін]; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-schema-link": ", і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-format-schema": ", і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-profile-schema": ", і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-trans": ", і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-trans-source": ", і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-trans-group": ", і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-cont-len": "«$1» — попередньо визначена властивість для зберігання інформації про довжину; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-cont-len": "Вона використовується в поєднанні з [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (а також [https://www.semantic-mediawiki.org/Attachment_processor процесором вкладень]) для збирання та зберігання інформації про довжину, отриману із прийнятого файлу (якщо такий надано).", "smw-property-predefined-cont-lang": "«$1» — попередньо визначена властивість для зберігання інформації про мову; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-cont-lang": "Вона використовується в поєднанні з [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (а також [https://www.semantic-mediawiki.org/Attachment_processor процесором вкладень]) для збирання та зберігання інформації про мову, отриману з прийнятого файлу (якщо такий надано).", "smw-property-predefined-cont-title": "«$1» — попередньо визначена властивість для зберігання інформації про заголовок; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-cont-title": "Вона використовується в поєднанні з [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (а також [https://www.semantic-mediawiki.org/Attachment_processor процесором вкладень]) для збирання та зберігання інформації про заголовок, отриману із прийнятого файлу (якщо такий надано).", "smw-property-predefined-cont-author": "«$1» — попередньо визначена властивість для зберігання інформації про автора; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-cont-author": "Вона використовується в поєднанні з [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (а також [https://www.semantic-mediawiki.org/Attachment_processor процесором вкладень]) для збирання та зберігання інформації про автора, отриману із прийнятого файлу (якщо такий надано).", "smw-property-predefined-cont-date": "«$1» — попередньо визначена властивість для зберігання інформації про дату; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-cont-date": "Вона використовується в поєднанні з [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (а також [https://www.semantic-mediawiki.org/Attachment_processor процесором вкладень]) для збирання та зберігання інформації про дату, отриману із прийнятого файлу (якщо такий надано).", "smw-property-predefined-cont-type": "«$1» — попередньо визначена властивість для зберігання інформації про тип файлу; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-cont-type": "Вона використовується в поєднанні з [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (а також [https://www.semantic-mediawiki.org/Attachment_processor процесором вкладень]) для збирання та зберігання інформації про тип файлу, отриману із прийнятого файлу (якщо такий надано).", "smw-property-predefined-cont-keyw": "«$1» — попередньо визначена властивість для репрезентування ключових слів; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-cont-keyw": "Вона використовується в поєднанні з [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (а також [https://www.semantic-mediawiki.org/Attachment_processor процесором вкладень]) для збирання та зберігання ключових слів, отриманих із прийнятого файлу (якщо такий надано).", "smw-property-predefined-file-attch": "«$1» — попередньо визначена властивість для репрезентування контейнера, який зберігає інформацію про вкладення; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-file-attch": "Вона використовується в поєднанні з [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (а також [https://www.semantic-mediawiki.org/Attachment_processor процесором вкладень]) для збирання всієї інформації, специфічної для контенту, яку можна отримати із прийнятого файлу (якщо такий надано).", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps Розширення «Карти»] не виявлене, тому властивість «$1» обмежена у своїй здатності працювати.", "smw-datavalue-monolingual-dataitem-missing": "Відсутній очікуваний елемент для побудови одномовного значення.", "smw-datavalue-languagecode-missing": "Для анотації «$1» парсер не зміг визначити коду мови (тобто «foo@en»).", "smw-datavalue-languagecode-invalid": "«$1» не було розпізнано як підтримуваний код мови.", "smw-property-predefined-lcode": "«$1» — попередньо визначена властивість, що репрезентує BCP47-форматований код мови; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-type-mlt-rec": "«$1» — [https://www.semantic-mediawiki.org/wiki/Help:Container контейнерний] тип даних, що пов'язує текстове значення із конкретним [[Property:Language code|кодом мови]].", "smw-types-extra-mlt-lcode": "Цей тип даних {{PLURAL:$2|вимагає|не вимагає}} коду мови (тобто {{PLURAL:$2|анотація значення без коду мови не приймається|анотація значення без коду мови приймається}}).", "smw-property-predefined-text": "«$1» — попередньо визначена властивість, що репрезентує текст допоміжної довжини; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-pdesc": "«$1» — попередньо визначена властивість, що дає змогу описати властивість у контексті мови; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-list": "«$1» — попередньо визначена властивість для визначення списку властивостей, використаних із властивістю типу [[Special:Types/Record|record]]; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Час розбору внутрішньотекстової анотації", "smw-limitreport-intext-postproctime": "[SMW] час пост-обробки", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|секунда|секунди|секунд}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|секунда|секунди|секунд}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Час оновлення store-бази даних (при оновленні сторінки)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|секунда|секунди|секунд}}", "smw_allows_pattern": "Ця сторінка повинна містити список посилань (після якого йдуть [https://uk.wikipedia.org/wiki/Регулярний_вираз регулярні вирази]), і доступ до неї має бути відкритий за допомогою властивості «[[Property:Allows pattern|Allows pattern]]». Щоб редагувати цю сторінку, потрібне право <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "Регулярний вираз «$2» класифікував значення «$1» як неприпустиме.", "smw-datavalue-allows-pattern-reference-unknown": "Посилання на шаблон «$1» не вдалося порівняти із записом на [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "Посилання на список «$1» не збігалось зі сторінкою [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "Контентові списку «$1» бракує елементів з маркером списку «*».", "smw-datavalue-feature-not-supported": "Функція «$1» не підтримується або була вимкнена в цій вікі.", "smw-property-predefined-pvap": "«$1» — попередньо визначена властивість, якою можна вказати [[MediaWiki:Smw allows pattern|посилання на шаблон]], аби застосувати зіставлення [https://en.wikipedia.org/wiki/Regular_expression регулярних виразів]; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-dtitle": "«$1» — попередньо визначена властивість, якою для сутності можна призначити окрему назву для показу; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-pvuc": "«$1» — попередньо визначена властивість, що надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki] для обмеження призначень значень для кожного примірника таким чином, щоб вони були унікальними (або щонайбільше єдиним)", "smw-property-predefined-long-pvuc": "Унікальність встановлюється, коли два значення є неоднаковими у своїй символьній репрезентації, і будь-яке порушення цього обмеження буде кваліфіковано як помилку.", "smw-datavalue-constraint-uniqueness-violation": "Властивість «$1» дозволяє лише унікальні присвоєння значень, а ''$2'' вже було анотовано в темі «$3».", "smw-datavalue-constraint-uniqueness-violation-isknown": "Властивість «$1» дозволяє лише унікальні анотації значень, ''$2'' вже містить закріплене значення. «$3» порушує вимогу унікальності.", "smw-datavalue-constraint-violation-non-negative-integer": "Властивість «$1» має обмеження «не негативне ціле число», і значення ''$2'' порушує цю вимогу.", "smw-datavalue-constraint-violation-must-exists": "Властивість «$1» має обмеження <code>must_exists</code>, і значення ''$2'' порушує цю вимогу.", "smw-datavalue-constraint-violation-single-value": "Властивість «[[Property:$1|$1]]» має обмеження <code>single_value</code>, і значення «$2» порушує цю вимогу.", "smw-constraint-violation-uniqueness": "Обмеження <code>unique_value_constraint</code> призначення властивості «[[Property:$1|$1]]» що дозволяє лише призначення з унікальними значеннями, тоді як для значення ''$2'' виявлено вже наявну анотацію в темі «$3».", "smw-constraint-violation-uniqueness-isknown": "Обмеження <code>unique_value_constraint</code> призначене властивості «[[Property:$1|$1]]», і тому дозволені лише унікальні анотації значень. ''$2'' вже містить анотоване значення з «$3», що порушує обмеження унікальності для поточної теми.", "smw-constraint-violation-non-negative-integer": "Обмеження <code>non_negative_integer</code> призначене властивості «[[Property:$1|$1]]», тоді як анотація значення ''$2'' порушує вимогу цього обмеження.", "smw-constraint-violation-must-exists": "Обмеження <code>must_exists</code> призначене властивості «[[Property:$1|$1]]», тоді як анотація значення ''$2'' порушує вимогу цього обмеження.", "smw-constraint-violation-single-value": "Обмеження <code>single_value</code> призначене властивості «[[Property:$1|$1]]», тоді як анотація значення «$2» порушує вимогу цього обмеження.", "smw-constraint-violation-class-shape-constraint-missing-property": "Обмеження <code>shape_constraint</code> призначене категорії «[[:$1]]» із ключем <code>property</code>, тоді як необхідна властивість «$2» відсутня.", "smw-constraint-violation-class-shape-constraint-wrong-type": "Обмеження <code>shape_constraint</code> призначене категорії «[[:$1]]» з ключем <code>property_type</code>, тоді як властивість «$2» не відповідає типу «$3».", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "Обмеження <code>shape_constraint</code> призначене категорії «[[:$1]]» з ключем <code>max_cardinality</code>, тоді як властивість «$2» не відповідає кардинальності «$3».", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "Обмеження <code>shape_constraint</code> призначене категорії «[[:$1]]» з ключем <code>min_textlength</code>, тоді як властивість «$2» не відповідає вимозі до довжини «$3».", "smw-constraint-violation-class-mandatory-properties-constraint": "Обмеження <code>mandatory_properties</code> призначене категорії «[[:$1]]» і потребує таких обов'язкових властивостей: $2", "smw-constraint-violation-allowed-namespace-no-match": "Обмеження <code>allowed_namespaces</code> призначене властивості «[[Property:$1|$1]]», тоді як «$2» порушує вимогу до простору назв; дозволено лише такі простори назв: «$3».", "smw-constraint-violation-allowed-namespaces-requires-page-type": "Обмеження <code>allowed_namespaces</code> потребує типу сторінки.", "smw-constraint-schema-category-invalid-type": "Анотована схема «$1» є недійсною для категорії, вона потребує типу «$2».", "smw-constraint-schema-property-invalid-type": "Анотована схема «$1» є недійсною для властивості, вона потребує типу «$2».", "smw-constraint-error-allows-value-list": "«$1» немає в списку ($2) [[Property:Allows value|дозволених значень]] для властивості «$3».", "smw-constraint-error-allows-value-range": "«$1» не перебуває в межах діапазону «$2», вказаного в обмеженні [[Property:Allows value|дозволених значень]] для властивості «$3».", "smw-property-predefined-boo": "«$1» — [[Special:Types/Boolean|тип]] і наперед визначена властивість, яку надає [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантична MediaWiki] для представлення логічних значень.", "smw-property-predefined-num": "«$1» — [[Special:Types/Number|тип]] і наперед визначена властивість, яку надає [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантична MediaWiki] для представлення числових значень.", "smw-property-predefined-dat": "«$1» — [[Special:Types/Date|тип]] і наперед визначена властивість, яку надає [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантична MediaWiki] для представлення значень дат.", "smw-property-predefined-uri": "«$1» — [[Special:Types/URL|тип]] і наперед визначена властивість, яку надає [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантична MediaWiki] для представлення значень URI/URL.", "smw-property-predefined-qty": "«$1» — [[Special:Types/Quantity|тип]] і наперед визначена властивість, яку надає [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантична MediaWiki] для представлення кількісних значень.", "smw-datavalue-time-invalid-offset-zone-usage": "«$1» містить ідентифікатори зміщення та зони, що не підтримуються.", "smw-datavalue-time-invalid-values": "Значення «$1» містить інформацію, непридатну до інтерпретації у формі «$2».", "smw-datavalue-time-invalid-date-components-common": "«$1» містить деяку інформацію, непридатну до інтерпретації.", "smw-datavalue-time-invalid-date-components-dash": "«$1» містить невластиве тире або інші символи, непридатні до інтерпретації дати.", "smw-datavalue-time-invalid-date-components-empty": "«$1» містить деякі порожні компоненти.", "smw-datavalue-time-invalid-date-components-three": "«$1» містить понад три компоненти, необхідні для інтерпретації дати.", "smw-datavalue-time-invalid-date-components-sequence": "«$1» містить послідовність, яку не можна інтерпретувати згідно з матрицею для компонентів дати.", "smw-datavalue-time-invalid-ampm": "«$1» містить «$2» як елемент години, непридатний для 12-годинного формату часу.", "smw-datavalue-time-invalid-jd": "Не вдалося інтерпретувати значення вводу «$1» як дійсне число (день за Юліанським календарем). Було видано «$2».", "smw-datavalue-time-invalid-prehistoric": "Не вдалося інтерпретувати доісторичне значення вводу «$1». Наприклад, якщо вказати більше інформації, ніж роки чи календарну модель, це може вивести неочікувані результати у доісторичному контексті.", "smw-datavalue-time-invalid": "Не вдалося розпізнати вхідне значення «$1» як дійсну дату чи компонент часу. На виході було отримано «$2».", "smw-datavalue-external-formatter-uri-missing-placeholder": "URI форматувальника не містить плейсхолдера «$1».", "smw-datavalue-external-formatter-invalid-uri": "«$1» — неправильна URL-адреса.", "smw-datavalue-external-identifier-formatter-missing": "Властивість відсутня в призначенні [[Property:External formatter uri|«URI зовнішнього форматувальника»]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "Зовнішній ідентифікатор «$1» очікує заміщення багатьох полів, але поточному значенню «$2» для відповідності вимогам бракує щонайменше одного параметра зі значенням.", "smw-datavalue-keyword-maximum-length": "Ключове слово перевищило максимальну довжину в $1 {{PLURAL:$1|символ|символи|символів}}.", "smw-property-predefined-eid": "«$1» — [[Special:Types/External identifier|тип]] і наперед визначена властивість, яку надає [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантична MediaWiki] для представлення зовнішніх ідентифікаторів.", "smw-property-predefined-peid": "«$1» — попередньо визначена властивість, що зазначає зовнішній ідентифікатор; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-pefu": "«$1» — попередньо визначена властивість, що надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki] для вказування зовнішнього ресурсу за допомогою заповнювача.", "smw-property-predefined-long-pefu": "URI має містити заповнювач, який буде налаштовано за допомогою значення [[Special:Types/External identifier|зовнішнього ідентифікатора]], щоб сформувати валідне посилання на ресурс.", "smw-type-eid": "«$1» — це варіант типу даних [[Special:Types/Text|Текст]] для опису зовнішніх ресурсів (базується на URI), що вимагає призначених властивостей для оголошення [[Property:External formatter uri|URI зовнішнього форматувальника]].", "smw-property-predefined-keyw": "«$1» — попередньо визначена властивість і [[Special:Types/Keyword|тип]], що надаються [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki] та нормалізують текст, який має обмеження довжини в символах.", "smw-type-keyw": "«$1» — варіант типу даних [[Special:Types/Text|«Text»]], який має обмеження довжини в символах із нормалізованою репрезентацією контенту.", "smw-datavalue-stripmarker-parse-error": "Задане значення «$1» містить маркери [https://en.wikipedia.org/wiki/Help:Strip_markers «strip markers»], а тому його не можна парсити належним чином.", "smw-datavalue-parse-error": "Задане значення «$1» було незрозумілим.", "smw-datavalue-propertylist-invalid-property-key": "Список властивостей «$1» містив недійсний ключ властивості «$2».", "smw-datavalue-type-invalid-typeuri": "Тип «$1» не вдалося трансформувати у дійсну репрезентацію URI.", "smw-datavalue-wikipage-missing-fragment-context": "Вхідне значення «$1» вікісторінки не можна використовувати без контекстної сторінки.", "smw-datavalue-wikipage-invalid-title": "Вхідне значення «$1» типу сторінки містить недійсні символи або є неповним, а тому воно може призвести до несподіваних результатів під час запиту чи процесу анотації.", "smw-datavalue-wikipage-property-invalid-title": "Властивість «$1» (як тип сторінки) із введеним значенням «$2» містить недійсні символи, або ж є неповною, а тому може спричинити непередбачені результати в процесі виконання запиту чи анотації.", "smw-datavalue-wikipage-empty": "Вхідне значення вікісторінки порожнє (напр., <code>[[SomeProperty::]], [[]]</code>), а тому його не можна використати як назву чи як частину умови запиту.", "smw-type-ref-rec": "«$1» є [https://www.semantic-mediawiki.org/wiki/Container контейнерним] типом, що дозволяє записувати додаткову інформацію (напр., дані про першоджерело) про встановлення значення.", "smw-datavalue-reference-invalid-fields-definition": "Тип [[Special:Types/Reference|посилання]] передбачає, що має бути визначений список властивостей з використанням властивості [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields «Has fields»].", "smw-parser-invalid-json-format": "JSON-парсер вивів помилку «$1».", "smw-property-preferred-label-language-combination-exists": "«$1» не можна використати як бажану назву, оскільки мова «$2» вже закріплена за назвою «$3».", "smw-clipboard-copy-link": "Скопіювати посилання в буфер обміну", "smw-property-userdefined-fixedtable": "«$1» було налаштовано як [https://www.semantic-mediawiki.org/wiki/Fixed_properties фіксовану властивість] і будь-яка модифікація її [https://www.semantic-mediawiki.org/wiki/Type_declaration декларації типу] вимагає, щоб або було запущено <code>setupStore.php</code>, або завершено спеціальне завдання [[Special:SemanticMediaWiki|«Інсталяція та оновлення бази даних»]].", "smw-data-lookup": "Отримання даних…", "smw-data-lookup-with-wait": "Запит обробляється, і це може зайняти трохи часу.", "smw-no-data-available": "Немає доступних даних.", "smw-property-req-violation-missing-fields": "Властивості «$1» бракує обов'язкової декларації [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] для цього типу «$2».", "smw-property-req-violation-multiple-fields": "Властивість «$1» містить декілька декларацій [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] (тому вони конфліктують), тоді як для цього типу «$2» очікується лише одна.", "smw-property-req-violation-missing-formatter-uri": "Властивості «$1» бракує даних декларації для анотованого типу, оскільки не вдалося визначити властивість <code>External formatter URI</code>.", "smw-property-req-violation-predefined-type": "Властивість «$1», як попередньо визначена властивість, містить декларацію типу «$2», яка є несумісною зі стандартним типом цієї властивості.", "smw-property-req-violation-import-type": "Виявлено декларацію типу, несумісну з попередньо визначеним типом імпортованого словника «$1». Загалом, непотрібно декларувати тип, оскільки інформація отримується з визначення імпорту.", "smw-property-req-violation-change-propagation-locked-error": "Властивість «$1» змінено й вона потребує повторного оцінювання призначених сутностей за допомогою процесу [https://www.semantic-mediawiki.org/wiki/Change_propagation зміни поширення]. Сторінку властивості буде замкнуто, доки не завершиться оновлення основної специфікації, щоб запобігти проміжним порушенням чи суперечливим специфікаціям. Цей процес може зайняти трохи часу, перш ніж сторінку можна буде розблокувати, оскільки це залежить від розміру та частоти планувальника [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue черги завдань].", "smw-property-req-violation-change-propagation-locked-warning": "Властивість «$1» змінено й вона потребує повторного оцінювання призначених сутностей за допомогою процесу [https://www.semantic-mediawiki.org/wiki/Change_propagation зміни поширення]. Оновлення може зайняти трохи часу, оскільки це залежить від розміру та частоти планувальника [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue черги завдань], тому рекомендується також відкласти зміни властивості на пізніше, щоб запобігти проміжним порушенням чи суперечливим специфікаціям.", "smw-property-req-violation-change-propagation-pending": "Оновлення [https://www.semantic-mediawiki.org/wiki/Change_propagation зміни поширення] перебувають в очікуванні (приблизно $1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|завдання|завдання|завдань}}]), а тому рекомендовано почекати з будь-якими змінами властивості, доки цей процес не завершиться, щоб запобігти проміжним порушенням чи суперечливим специфікаціям.", "smw-property-req-violation-missing-maps-extension": "Семантичній MediaWiki не вдалося виявити розширення [https://www.semantic-mediawiki.org/wiki/Extension:Maps «Maps»], наявність якого є обов'язковою, і, як наслідок, обмежує функціональність (тобто не може зберігати чи опрацьовувати географічні дані) цієї властивості.", "smw-property-req-violation-type": "Властивість містить суперечливі специфікації типів, результатом чого можуть стати недійсні анотації значень, тому очікується, що користувач призначить один відповідний тип.", "smw-property-req-error-list": "Властивість містить такі помилки чи попередження:", "smw-property-req-violation-parent-type": "Властивість «$1» та призначена батьківська властивість «$2» мають різні анотації типу.", "smw-property-req-violation-forced-removal-annotated-type": "Увімкнено примусове [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance успадкування обов'язкового батьківського типу], тоді як анотований тип властивості «$1» не відповідає своєму батьківському типу властивості «$2», і його було змінено, щоб віддзеркалити ту вимогу. Рекомендовано пристосувати визначення типу в межах сторінки, щоб можна було прибрати повідомлення про помилку і примусове успадкування обов'язкового батьківського типу для цієї властивості.", "smw-change-propagation-protection": "Цю сторінку закрито з метою запобігання випадковій модифікації даних під час виконання оновлення [https://www.semantic-mediawiki.org/wiki/Change_propagation поширення змін]. Цей процес може зайняти трохи часу, перш ніж сторінку буде знову відкрито, і залежить від розміру та частоти планувальника [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue черги завдань].", "smw-category-change-propagation-locked-error": "Категорію «$1» змінено, і призначені сутності слід повторно оцінити за допомогою процесу [https://www.semantic-mediawiki.org/wiki/Change_propagation зміни поширення]. Тим часом сторінку категорію заблоковано, доки не завершиться оновлення основної специфікації, щоб запобігти проміжним порушенням чи суперечливим специфікаціям. Цей процес може зайняти трохи часу, перед тим як сторінку можна буде розблокувати, оскільки це залежить від розміру та частоти планувальника [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue черги завдань].", "smw-category-change-propagation-locked-warning": "Категорію «$1» змінено, і призначені сутності необхідно повторно оцінити за допомогою процесу [https://www.semantic-mediawiki.org/wiki/Change_propagation зміни поширення]. Оновлення може зайняти трохи часу, оскільки воно залежить від розміру та частоти планувальника [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue черги завдань], а тому рекомендовано відкласти будь-які подальші зміни в категорії, щоб запобігти проміжним порушенням чи суперечливим специфікаціям.", "smw-category-change-propagation-pending": "Оновлення [https://www.semantic-mediawiki.org/wiki/Change_propagation зміни поширення] перебувають в очікуванні (приблизно $1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|завдання|завдання|завдань}}]), тому рекомендовано почекати з будь-якими змінами в категорії, доки не завершиться цей процес, щоб запобігти проміжним порушенням чи суперечливим специфікаціям.", "smw-category-invalid-value-assignment": "«$1» не розпізнається як дійсна категорія чи анотація значення.", "protect-level-smw-pageedit": "Дозволити лише користувачам, що мають права на редагування сторінок (Семантична MediaWiki)", "smw-create-protection": "Можливість створення властивості «$1» обмежене лише до користувачів з відповідним правом «$2» (або до [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups групи користувачів]), наряду з увімкненим [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode режимом авторитету].", "smw-create-protection-exists": "Можливість зміни властивості «$1» обмежене лише до користувачів з відповідним правом «$2» (або до [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups групи користувачів]), наряду з увімкненим [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode режимом авторитету].", "smw-edit-protection": "Цю сторінку [[Property:Is edit protected|захищено]] для запобігання випадковій зміні даних, а тому її можуть редагувати лише користувачі з відповідним редакторським правом («$1») або ті, що належать до відповідної [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups групи користувачів].", "smw-edit-protection-disabled": "Захист від редагувань було вимкнено, тому «$1» не може бути використано для захисту сторінок сутностей від неавторизованого редагування.", "smw-edit-protection-auto-update": "Семантична MediaWiki оновила статус захисту відповідно до властивості «Is edit protected».", "smw-edit-protection-enabled": "Захищено від редагувань (Семантична MediaWiki)", "smw-patternedit-protection": "Ця сторінка захищена, її можуть редагувати лише користувачі з відповідними <code>smw-patternedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions правами].", "smw-property-predefined-edip": "«$1» — це попередньо визначена властивість, що забезпечується [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki] для зазначення того, чи встановлено захист від редагувань, чи ні.", "smw-property-predefined-long-edip": "Тоді як будь-який користувач може додати цю властивість до якоїсь теми, лише користувач з особливими правами може редагувати чи відкликати захист сутності після того, як властивість було додано.", "smw-query-reference-link-label": "Посилання на запит", "smw-format-datatable-emptytable": "Немає даних у таблиці", "smw-format-datatable-info": "Відображення записів від _START_ по _END_ з _TOTAL_", "smw-format-datatable-infoempty": "Показані від 0 до 0 з 0 записів", "smw-format-datatable-infofiltered": "(відфільтровано з усього _MAX_ записів)", "smw-format-datatable-infothousands": "&nbsp;", "smw-format-datatable-lengthmenu": "Показати _MENU_ записів", "smw-format-datatable-loadingrecords": "Завантаження…", "smw-format-datatable-processing": "Обробка…", "smw-format-datatable-search": "Пошук:", "smw-format-datatable-zerorecords": "Нічого не знайдено відповідно до критеріїв пошуку", "smw-format-datatable-first": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-next": "Наступний", "smw-format-datatable-previous": "Попередній", "smw-format-datatable-sortascending": ": активувати сортування стовпців за зростанням", "smw-format-datatable-sortdescending": ": активувати сортування стовпців за спаданням", "smw-format-datatable-toolbar-export": "Експорт", "smw-format-list-other-fields-open": "(", "smw-category-invalid-redirect-target": "Категорія «$1» містить недійсну ціль перенаправлення на простір назв, який не є категорією.", "smw-parser-function-expensive-execution-limit": "Ця парсерна функція досягла ліміту для витратних запусків (див. параметр конфігурації [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "Семантична MediaWiki оновлює поточну сторінку за умови необхідності деякої пост-обробки запиту.", "apihelp-smwinfo-summary": "Модуль API для отримання інформації про статистику Семантичної MediaWiki та іншої мета-інформації.", "apihelp-ask-summary": "Модуль API для надсилання запитів до Семантичної MediaWiki з використанням мови «Ask».", "apihelp-askargs-summary": "Модуль API для надсилання запитів до Семантичної MediaWiki за допомогою мови «Ask» у формі списку умов, роздруківок та параметрів.", "apihelp-browsebyproperty-summary": "Модуль API для отримання інформації про властивість або список властивостей.", "apihelp-browsebysubject-summary": "Модуль API для отримання інформації про предмет.", "apihelp-smwtask-summary": "Модуль API для виконання завдань, пов'язаних із Семантичною MediaWiki (лише для внутрішнього, не для публічного використання).", "apihelp-smwbrowse-summary": "Модуль API для підтримки дій з перегляду для різних типів сутностей Семантичної MediaWiki.", "apihelp-ask-parameter-api-version": "Вихідне форматування:\n;2:Зворотно-сумісний формат із використанням {} для списку результатів.\n;3:Експериментальний формат із використанням [] як списку результатів.", "apihelp-smwtask-param-task": "Визначає тип завдання", "apihelp-smwtask-param-params": "JSON-кодовані параметри, що відповідають вимогам до вибраного типу завдання", "smw-apihelp-smwtask-example-update": "Приклад запуску завдання з оновлення для конкретної теми:", "smw-api-invalid-parameters": "Помилкові параметри, «$1»", "smw-parser-recursion-level-exceeded": "Рівень рекурсій $1 було перевищено протягом процесу парсингу. Пропонуємо перевірити структуру шаблону, або, якщо є така необхідність, підкоригувати параметр конфігурації <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "Показано $1 {{PLURAL:$1|сторінку, яка використовує|сторінки, які використовують|сторінок, які використовують}} цю властивість.", "smw-property-page-list-search-count": "Показано $1 {{PLURAL:$1|сторінку, яка використовує|сторінки, які використовують|сторінок, які використовують}} цю властивість зі збігом значення «$2».", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter Пошуковий фільтр] дозволяє включати [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions вирази запитів] на кшталт <code>~</code> чи <code>!</code>. Вибраний [https://www.semantic-mediawiki.org/wiki/Query_engine рушій запитів] може також підтримувати пошук збігів без врахування регістру чи інші короткі вирази на кшталт:\n\n* <code>in:</code> результат має містити термін, напр., '<code>in:Foo</code>'\n\n* <code>not:</code> результат не має містити термін, напр., '<code>not:Bar</code>'", "smw-property-reserved-category": "Категорія", "smw-category": "Категорія", "smw-datavalue-uri-invalid-scheme": " «$1» не додано до списку як дійсна схема URI.", "smw-datavalue-uri-invalid-authority-path-component": "Виявлено, що «$1» містить недійсний компонент шляху чи авторитету «$2».", "smw-browse-property-group-title": "Група властивостей", "smw-browse-property-group-label": "Позначка групи властивостей", "smw-browse-property-group-description": "Опис групи властивостей", "smw-property-predefined-ppgr": "«$1» — попередньо визначена властивість, що ідентифікує сутності (у першу чергу — категорії), які використовуються як примірник групування для властивостей; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-filter": "Фільтр", "smw-section-expand": "Розкрити розділ", "smw-section-collapse": "Згорнути розділ", "smw-ask-format-help-link": "Формат [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "Допомога", "smw-cheat-sheet": "Шпаргалка", "smw-personal-jobqueue-watchlist": "Список спостереження черги завдань", "smw-personal-jobqueue-watchlist-explain": "Ці числа позначають приблизну кількість окремих елементів у черзі завдань, що очікують на виконання.", "smw-property-predefined-label-skey": "Ключ сортування", "smw-processing": "Обробка…", "smw-loading": "Завантаження…", "smw-fetching": "Отримання…", "smw-preparing": "Підготовка…", "smw-expand": "Розгорнути", "smw-collapse": "Згорнути", "smw-copy": "Копіювати", "smw-copy-clipboard-title": "Копіювання вмісту у буфер обміну", "smw-jsonview-expand-title": "Розгорнути JSON-перегляд", "smw-jsonview-collapse-title": "Згорнути JSON-перегляд", "smw-jsonview-search-label": "Пошук:", "smw-redirect-target-unresolvable": "Ціль неможливо вирішити з такої причини: «$1»", "smw-types-title": "Тип: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Зміна моделі вмісту [https://www.semantic-mediawiki.org/wiki/Help:Schema сторінки схеми] не дозволена.", "smw-schema-namespace-edit-protection": "Ця сторінка захищена, її можуть редагувати лише користувачі з відповідними <code>smw-schemaedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions правами].", "smw-schema-namespace-edit-protection-by-import-performer": "Цю сторінку імпортовано одним із [https://www.semantic-mediawiki.org/wiki/Import_performer виконавців імпорту], перелічених у відповідному списку, і це означає, що зміна контенту цієї сторінки обмежена лише до користувачів із того списку.", "smw-schema-error-title": "{{PLURAL:$1|1=Помилка|Помилки}} валідації", "smw-schema-error-schema": "Схема валідації '''$1''' виявила такі невідповідності:", "smw-schema-error-miscellaneous": "Різні помилки ($1)", "smw-schema-error-validation-json-validator-inaccessible": "Валіда<PERSON><PERSON><PERSON> «<b>$1</b>» не доступний (чи не встановлений), і є причиною того, що файл «$2» не вдається дослідити, що, в свою чергу, запобігає збереженню чи зміні поточної сторінки.", "smw-schema-error-validation-file-inaccessible": "Файл валідації «$1» недоступний.", "smw-schema-error-violation": "[«$1», «$2»]", "smw-schema-error-type-missing": "Контентові бракує зазначення типу, щоб його можна було розпізнати й використовувати в [https://www.semantic-mediawiki.org/wiki/Help:Schema просторі назв для схем].", "smw-schema-error-type-unknown": "Тип «$1» не зареєстрований і не може використовуватись для контенту в просторі назв [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-error-json": "Помилка JSON: «$1»", "smw-schema-error-input": "Валідація введених даних виявила вказані проблеми, і їх треба вирішити, перш ніж зберігати контент. Сторінка [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling довідки щодо схем] може містити якісь поради щодо того, як усунути будь-які невідповідності чи вирішити проблеми, пов'язані із введенням даних для схем.", "smw-schema-error-input-schema": "Схема валідації '''$1''' виявила вказані невідповідності, і їх треба вирішити, перш ніж зберігати контент. Сторінка [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling довідки щодо схем] може містити якісь поради щодо того, як вирішувати такі проблеми.", "smw-schema-error-title-prefix": "Цей тип схеми вимагає, щоб назва схеми починалася з префікса «$1».", "smw-schema-validation-error": "Тип «$1» не зареєстрований, і його не можна використовувати для контенту в просторі назв [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-validation-schema-title": "JSON-схема", "smw-schema-summary-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-schema-title": "Схема", "smw-schema-usage": "Використання", "smw-schema-type": "Тип схеми", "smw-schema-type-description": "О<PERSON>ис типу", "smw-schema-description": "<PERSON><PERSON><PERSON><PERSON> схеми", "smw-schema-description-link-format-schema": "Цей тип схеми підтримує визначення характеристики для створення чутливих до контексту посилань у поєднанні із призначеною властивістю [[Property:Formatter schema|схеми форматувальника]].", "smw-schema-description-search-form-schema": "Цей тип схеми підтримує визначення форм вводу та характеристики для профілю [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch розширеного пошуку], де він містить інструкції щодо того, як генерувати поля вводу, визначати стандартні простори назв, чи декларувати префіксальні вирази для пошукового запиту.", "smw-schema-description-property-profile-schema": "Цей тип схеми підтримує визначення профілю для декларування характеристик призначеній властивості та її анотаційним значенням.", "smw-schema-description-property-group-schema": "Цей тип схеми підтримує визначення [https://www.semantic-mediawiki.org/wiki/Help:Property_group груп властивостей], аби допомогти структурувати інтерфейс [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse перегляду].", "smw-schema-description-property-constraint-schema": "Цей тип схеми підтримує визначення правил обмежень для примірника властивості, так само як і для значень, призначених йому.", "smw-schema-description-class-constraint-schema": "Цей тип схеми підтримує визначення правил обмежень для примірника класу (також відомого як категорія).", "smw-schema-tag": "{{PLURAL:$1|Тег|Теги}}", "smw-property-predefined-constraint-schema": "«$1» — попередньо визначена властивість, яка визначає схему обмеження; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-schema-desc": "«$1» — це попередньо визначена властивість, що зазначає опис схеми і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-schema-def": "«$1» — це попередньо визначена властивість, що зазначає вміст схеми і надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-schema-tag": "«$1» — попередньо визначена властивість, що надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki] для визначення збірки схем.", "smw-property-predefined-long-schema-tag": "Мітка, що ідентифікує схеми із подібним контентом чи характеристиками.", "smw-property-predefined-schema-type": "«$1» — попередньо визначена властивість, що описує тип для виокремлення групи схем; надається [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Семантичною MediaWiki].", "smw-property-predefined-long-schema-type": "Кожен [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type тип] надає власну інтерпретацію елементів синтаксису та правил застосування, і може бути вираженим за допомогою [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation схеми валідації].", "smw-ask-title-keyword-type": "Пошук за ключовим словом", "smw-ask-message-keyword-type": "Цей пошук відповідає умові <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Не вдалося під'єднатись до віддаленої цілі «$1».", "smw-remote-source-disabled": "Джерело '''$1''' вимкнуло підтримку віддалених запитів!", "smw-remote-source-unmatched-id": "Джерело '''$1''' не відповідає версії Семантичної MediaWiki, яка має підтримку віддалених запитів.", "smw-remote-request-note": "Результат отримано із віддаленого джерела '''$1''', і є ймовірність, що згенерований контент містить інформацію, недоступну в межах поточної вікі.", "smw-remote-request-note-cached": "Результат '''кешовано''' з віддаленого джерела '''$1''', і є ймовірність, що згенерований контент містить інформацію, недоступну в межах поточної вікі.", "smw-parameter-missing": "Відсутн<PERSON>й параметр «$1».", "smw-property-tab-usage": "Використання", "smw-property-tab-profile-schema": "Схема профілю", "smw-property-tab-redirects": "Синоніми", "smw-property-tab-subproperties": "Підвластивості", "smw-property-tab-errors": "Неналежні призначення", "smw-property-tab-constraint-schema": "Схема обмеження", "smw-property-tab-constraint-schema-title": "Скомпільована схема обмеження", "smw-property-tab-specification": "... більше", "smw-concept-tab-list": "Список", "smw-concept-tab-errors": "Помилки", "smw-ask-tab-result": "Результат", "smw-ask-tab-extra": "Додатково", "smw-ask-tab-debug": "Налагодження", "smw-ask-tab-code": "<PERSON>од", "smw-install-incomplete-tasks-title": "Незавершені завдання адміністрування", "smw-install-incomplete-intro": "Є незавершені {{PLURAL:$2|завдання}}, чи такі, що [[Special:PendingTaskList|очікують на завершення]] ($2) {{PLURAL:$1|інсталяції|оновлення}} [https://www.semantic-mediawiki.org Семантичної MediaWiki]. {{PLURAL:$2|Їх}} може завершити адміністратор, чи користувач із достатніми для цього правами. Це слід зробити ще до того, як додавати нові дані, аби уникнути будь-якої неузгодженості.", "smw-install-incomplete-intro-note": "Це повідомлення зникне після того, як усі релевантні завдання буде виконано.", "smw-pendingtasks-intro-empty": "Немає завдань, які було б класифіковано як такі, що очікують на виконання, є незавершеними, чи є невиконаними у зв'язку із Семантичною MediaWiki.", "smw-pendingtasks-intro": "Ця сторінка надає інформацію про завдання, які було класифіковано як такі, що очікують на виконання, є незавершеними чи невиконаними у зв'язку із Семантичною MediaWiki.", "smw-pendingtasks-setup-no-tasks-intro": "Інсталя<PERSON><PERSON>ю (чи оновлення) завершено, зараз немає незавершених чи невиконаних завдань.", "smw-pendingtasks-tab-setup": "Встановлення", "smw-updateentitycollation-incomplete": "Налаштування <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> було щойно змінено і потребує запуск скрипта <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> для оновлення сутностей та коректного сортування значень полів.", "smw-updateentitycountmap-incomplete": "В останньому випуску було додано поле <code>smw_countmap</code>, яке вимагає виконання скрипта <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code>, щоб функції могли отримати доступ до контенту цього поля.", "smw-populatehashfield-incomplete": "Заповнення полів <code>smw_hash</code> було пропущено під час встановлення. Необхідно виконати скрипт <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-populate-hash-field": "Заповнення полів <code>smw_hash</code> було пропущено під час встановлення. Необхідно виконати скрипт <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code> вибрано як [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore стандартний сховок], але розширення не змогло відшукати будь-які записи про виконання скрипта <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code>; будь ласка, запустіть скрипт згідно з інструкціями.", "smw-elastic-rebuildelasticindex-run-incomplete": "<code>ElasticStore</code> вибрано як [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore стандартний сховок], але розширення не змогло відшукати будь-які записи про виконання скрипта <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code>; будь ласка, запустіть скрипт згідно з інструкціями.", "smw-pendingtasks-setup-intro": "{{PLURAL:$1|Інсталяція|Оновлення}} <b>Семантичної MediaWiki</b> {{PLURAL:$1|класифікувала|класифікувало}} вказані завдання як [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade незавершені]; очікується, що адміністратор (чи користувач із достатніми для цього правами) виконає ці завдання, перш ніж користувачі продовжать створювати чи змінювати контент.", "smw-pendingtasks-setup-tasks": "Завдання", "smw-filter-count": "Кількість фільтрів", "smw-es-replication-check": "Перевірка реплікації (Elasticsearch)", "smw-es-replication-error": "Проблема з реплікацією Elasticsearch", "smw-es-replication-file-ingest-error": "Проблема з прийняттям файлу", "smw-es-replication-maintenance-mode": "Обслуговування Elasticsearch", "smw-es-replication-error-missing-id": "Моніторинг реплікації виявив, що статті «$1» (ID: $2) бракує в бекенді Elasticsearch.", "smw-es-replication-error-divergent-date": "Моніторинг реплікації виявив, що для статті «$1» (ID: $2) <b>дата зміни</b> показує невідповідні дані.", "smw-es-replication-error-divergent-date-short": "Для порівняння було використано таку інформацію про дату:", "smw-es-replication-error-divergent-date-detail": "Вказана дата зміни:\n*Elasticsearch: $1 \n*База даних: $2", "smw-es-replication-error-divergent-revision": "Моніторинг реплікації виявив, що для статті «$1» (ID: $2) <b>пов'язана версія</b> показує невідповідні дані.", "smw-es-replication-error-divergent-revision-short": "Для порівняння було використано такі дані про пов'язану версію:", "smw-es-replication-error-divergent-revision-detail": "Вказана пов'язана версія:\n*Elasticsearch: $1 \n*База даних: $2", "smw-es-replication-error-maintenance-mode": "Реплікація Elasticsearch зараз обмежена, оскільки вона працює в [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>режимі обслуговування</b>], зміни сутностей та сторінок <b>не</b> будуть видимими відразу, а результати запитів можуть містити застарілу інформацію.", "smw-es-replication-error-no-connection": "Моніторинг реплікації не може виконувати жодних перевірок, оскільки він не може встановити зв'язок із кластером Elasticsearch.", "smw-es-replication-error-bad-request-exception": "Обробник з'єднання Elasticsearch видав виняток про поганий запит (\"400 conflict http error\"), що свідчить про проблему, ка триває під час реплікації та пошукових запитів.", "smw-es-replication-error-other-exception": "Обробник з'єднань Elasticsearch видав виняток: «$1».", "smw-es-replication-error-suggestions": "Пропонується відредагувати сторінку чи очистити її кеш, щоб усунути невідповідність. Якщо проблема не зникатиме, перевірте сам кластер Elasticsearch (розподільник, винятки, простір на диску тощо).", "smw-es-replication-error-suggestions-maintenance-mode": "Пропонується зв'язатися з адміністратором вікі, щоб перевірити, чи [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild перебудова індексу] все ще триває, чи для <code>refresh_interval</code> не було встановлено очікуване стандартне значення.", "smw-es-replication-error-suggestions-no-connection": "Пропонується зв'язатися з адміністратором вікі та повідомити про проблему з відсутністю з'єднання.", "smw-es-replication-error-suggestions-exception": "Будь ласка, перевірте журнали на наявність інформації про статус Elasticsearch, його індекси та потенційні проблеми, пов'язані з неправильною конфігурацією.", "smw-es-replication-error-file-ingest-missing-file-attachment": "Моніторинг реплікації виявив, що «$1» бракує анотації [[Property:File attachment|вкладення файлу]], що свідчить про те, що процесор прийняття файлу не запустився, чи ще не завершив свою роботу.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Будь ласка, переконайтеся, що завдання із [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion прийняття файлу] заплановане й виконане, перш ніж індекс анотацій та файлів стане доступним.", "smw-report": "<PERSON>в<PERSON>т", "smw-legend": "Легенда", "smw-datavalue-constraint-schema-category-invalid-type": "Анотована схема «$1» є недійсною для категорії, вона потребує типу «$2».", "smw-datavalue-constraint-schema-property-invalid-type": "Анотована схема «$1» є недійсною для властивості, вона потребує типу «$2».", "smw-entity-examiner-check": "Триває фонова робота {{PLURAL:$1|інспектора|інспекторів}}", "smw-entity-examiner-indicator": "Панель проблем сутностей", "smw-entity-examiner-deferred-check-awaiting-response": "Інспектор «$1» наразі очікує на відповідь із бекенду.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "Обмеження", "smw-entity-examiner-associated-revision-mismatch": "Версія", "smw-entity-examiner-deferred-fake": "Фальшиві", "smw-entity-examiner-indicator-suggestions": "У рамках інспекції сутності було виявлено {{PLURAL:$1|вказану проблему|вказані проблеми}}. Пропонуємо {{PLURAL:$1|її|їх}} уважно переглянути та вжити відповідних {{PLURAL:$1|заходів}}.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Обмеження}}", "smw-indicator-revision-mismatch": "Версія", "smw-indicator-revision-mismatch-error": "Перевірка [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner пов'язаної версії] виявила невідповідність між версією, на яку посилається MediaWiki, та тією, з якою здійснюється пов'язування в Семантичній MediaWiki для цієї сутності.", "smw-indicator-revision-mismatch-comment": "Невідповідність зазвичай свідчить про те, що якийсь процес перервав операцію зі зберігання в Семантичній MediaWiki. Рекомендується переглянути журнали сервера та пошукати винятків чи інших невдач.", "smw-facetedsearch-intro-tab-search": "По<PERSON><PERSON>к", "smw-facetedsearch-format-table": "Таблиця", "smw-search-placeholder": "По<PERSON>ук…", "smw-listingcontinuesabbrev": "(прод.)", "smw-showingresults": "Нижче {{PLURAL:$1|показане|показані|показані|}} <strong>$1</strong> {{PLURAL:$1|результат|результати|результатів|}}, починаючи з №&nbsp;<strong>$2</strong>"}