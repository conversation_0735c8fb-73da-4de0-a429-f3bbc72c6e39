{"@metadata": {"authors": ["COMISSIONTOPOC", "Cedric31", "<PERSON><PERSON><PERSON><PERSON>", "Kghbln", "Nemo bis", "<PERSON>", "Unuaiga", "아라"]}, "smw-desc": "Rendre lo wiki mai accessible - per las maquinas ''e'' los umans ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentacion en linha])", "smw_viewasrdf": "<PERSON><PERSON><PERSON> coma RDF", "smw_finallistconjunct": " e", "smw_isspecprop": "Aquesta proprietat es una proprietat especiala sus aqueste wiki.", "smw-concept-no-cache": "Cap de cache pas disponible.", "smw_concept_description": "Descripcion del concèpte « $1 »", "smw_no_concept_namespace": "Los concèptes pòt unicament èsser definits dins la pagina dins lo Concèpte : espaci de nom.", "smw_multiple_concepts": "Cada pagina de concèpte pòt pas aver qu’una sola definicion.", "smw_concept_cache_miss": "Lo concèpte « $1 » pòt pas èsser utilizat pel moment, perque la configuracion del wiki requerís que siá aviat fòra linha. Se lo problèma persistís aprèp qualques instants, demandatz a vòstre administrator del site de rendre disponible aqueste concèpte.", "smw_noinvannot": "Las valors pòdon pas èsser allogadas per inversar de proprietats.", "smw_baduri": "O planhèm. Las URIs del domeni $1 son pas disponiblas a aqueste emplaçament", "smw_printername_count": "Comptatge dels resultats", "smw_printername_csv": "expòrt en CSV", "smw_printername_dsv": "Exportar al format DSV", "smw_printername_debug": "<PERSON><PERSON><PERSON><PERSON><PERSON> de debogatge (pels expèrts)", "smw_printername_embedded": "Contengut de las paginas incrustadas", "smw_printername_json": "expòrt en JSON", "smw_printername_list": "Lista", "smw_printername_ol": "Enumeracion", "smw_printername_ul": "<PERSON><PERSON><PERSON>", "smw_printername_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON> larga", "smw_printername_template": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_rdf": "Exportar al format RDF", "smw_printername_category": "Categoria", "validator-type-class-SMWParamSource": "tèxte", "smw_iq_disabled": "O planhèm. Las recèrcas dins los articles d'aqueste wiki son pas autorizadas.", "smw_iq_moreresults": "&hellip; autres resultats", "smw_parseerror": "La donada indicada es pas estada compresa.", "smw_notitle": "“$1” pòt pas èsser utilizat coma nom de pagina sus aqueste wiki.", "smw_wrong_namespace": "Solas las paginas de l'espaci de noms « $1 » son autorizadas aicí.", "smw_manytypes": "Maites tipes de donadas son estats assignats a l'atribut.", "smw_emptystring": "Las cadenas voidas son pas acceptadas.", "smw_notinenum": "« $1 » es pas dins la lista ($2) de las [[Property:Allows value|valors autorizadas]] per la proprietat « $3 ».", "smw_noboolean": "\\\"$1\\\" es pas reconegut coma una valor boleana (verai/fals).", "smw_true_words": "verai,v,òc,true", "smw_false_words": "fals,f,non,false", "smw_nofloat": "\"$1\" es pas un nombre.", "smw_infinite": "Los nombres tant grands coma « $1 » son pas suportats.", "smw_nodatetime": "La data \"$1\" es pas estada compresa.", "smw_toomanyclosing": "Sembla que i a tròp d'ocuréncias de “$1” dins la requèsta.", "smw_noclosingbrackets": "D'unes “<nowiki>[[</nowiki>” dins vòstra requèsta son pas estats clauses per de “]]” correspondents.", "smw_misplacedsymbol": "Lo simbòl “$1” es estat utilizat a un endrech ont es pas util.", "smw_unexpectedpart": "La partida “$1” de la requèsta es pas estada compresa. Los resultats pòdon èsser imprevists.", "smw_emptysubquery": "D'unas sosrequèstas an una condicion invalida.", "smw_misplacedsubquery": "D'unas sosrequèstas son estadas utilizadas a un endrech ont cap de sosrequèsta es pas permesa.", "smw_valuesubquery": "Sosrequèsta pas suportada per las valors de la proprietat “$1”.", "smw_badqueryatom": "Las partidas “<nowiki>[[…]]</nowiki>” de la requèsta son pas estadas compresas.", "smw_propvalueproblem": "La valor de la proprietat “$1” es pas estada compresa.", "smw_noqueryfeature": "Qualques foncionalitats de requèstas son pas suportadas sus aqueste wiki e una partida d’entre elas es estada levada ($1).", "smw_noconjunctions": "Las conjoncions dins las requèstas son pas suportadas sus aqueste wiki e una partida d’entre elas es estada levada ($1).", "smw_nodisjunctions": "Las disjoncions dins las requèstas son pas suportadas sus aqueste wiki e de partidas de la requèsta son estadas ignoradas($1).", "smw_querytoolarge": "Las condicions seguentas de la requèsta an pas pogut èsser evaluadas en rason de las restriccions d'aqueste wiki a la talha o a la prigondor de las requèstas : $1.", "smw_notemplategiven": "Provesissètz una valor pel paramètre « modèl » per aqueste format de requèsta per trabalhar.", "smw_type_header": "Atributs de tipe “$1”", "smw_typearticlecount": "Afichar {{PLURAL:$1|la proprietat qu'utiliza|las $1 proprietats qu'utilizan}} aqueste tipe.", "smw_attribute_header": "Paginas utilizant l'atribut “$1”", "smw_attributearticlecount": "Afichar {{PLURAL:$1|la pagina qu'utiliza|las $1 paginas qu'utilizan}} aquesta proprietat.", "exportrdf": "Exportar l'article en RDF", "smw_exportrdf_docu": "Sus aquesta pagina, de partidas del contengut d'un article pòdon èsser exportadas dins lo format RDF. Picatz lo nom de las paginas desiradas dins la bóstia de tèxte çaijós, <i>un nom per linha </i>.", "smw_exportrdf_recursive": "Exportar tanben totas las paginas pertinentas d'un biais recursiu. Aquesta possibilitat pòt abotir a un fòrt grand nombre de resultats !", "smw_exportrdf_backlinks": "Exportar tanben totas las paginas que renvian a de paginas exportadas. Produtz un RDF dins lo qual la navigacion es mai aisida.", "smw_exportrdf_lastdate": "Exportar pas las paginas pas modificadas dempuèi lo moment indicat.", "smw_exportrdf_submit": "Exportar", "uriresolver": "Resolveire d'URI", "properties": "Proprietats", "smw-categories": "Categorias", "smw_properties_docu": "Sus aqueste wiki, son utilizadas las proprietats seguentas.", "smw_property_template": "$1 del tipe $2 ($3 {{PLURAL:$3|utilizacion|utilizacions}})", "smw_propertylackspage": "Tota proprietat deuriá èsser descrita per una pagina !", "smw_propertylackstype": "Cap de tipe es pas estat especificat per aquesta proprietat (tipe actualament supausat : $1).", "smw_propertyhardlyused": "Aquesta proprietat es fòrça utilizada sus aqueste wiki !", "concepts": "Concèptes", "unusedproperties": "Proprietats inutilizadas", "smw-unusedproperties-docu": "Las proprietats seguentas existisson, quitament se cap de pagina las utiliza pas.", "smw-unusedproperty-template": "$1 de tipe $2", "wantedproperties": "Proprietats demandadas", "smw-wantedproperties-docu": "Las proprietats seguentas son utilizadas sus aqueste wiki mas an pas encara de pagina per las descriure.", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|utilizacion|utilizacions}})", "smw_purge": "Reactualizar", "types": "Tipes", "smw_types_docu": "Aquò es una lista de totes los tipes de donadas que pòdon èsser assignats a las proprietats.", "smw-statistics-property-instance": "{{PLURAL:$1|Valor|Valors}} de proprietat (total)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Proprietat|Proprietats}}]] (total)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Proprietat|Proprietats}} (total)", "smw-statistics-property-page": "{{PLURAL:$1|Proprietat|Proprietats}} (enregistradas amb una pagina)", "smw-statistics-property-type": "{{PLURAL:$1|Proprietat|Proprietats}} (assignadas a un tipe de donadas)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Req<PERSON>èsta|Requèstas}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|requèsta|requèstas}}]]", "smw-statistics-query-size": "<PERSON><PERSON><PERSON> <PERSON> re<PERSON>u<PERSON>", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Concèpte|Concèptes}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Concèpte|Concèptes}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|Possedís {{PLURAL:$1|un sosobjècte|de sosobjèctes}}]]", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Tipe de donadas|Tipes de donadas}}]]", "smw_uri_doc": "Lo resolveire d'URI implementa la [$1 conclusion del TAG del W3C a prepaus del httpRange-14]. Se pòt assegurar que los umans vengan pas de sites web.", "ask": "Recèrca semantica", "smw_ask_sortby": "Triar per colomnas (opcional)", "smw_ask_ascorder": "Creissent", "smw_ask_descorder": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Trobar de resultats", "smw_ask_editquery": "Editar la requèsta", "smw_add_sortcondition": "[Apond las condicions de triada]", "smw_ask_hidequery": "Amagar la requèsta", "smw_ask_help": "Ajuda a la requèsta", "smw_ask_queryhead": "Condicion", "smw_ask_printhead": "Seleccion de las donadas d'imprimir", "smw_ask_printdesc": "(apondre un nom de proprietat per linha)", "smw_ask_format_as": "Formatar en :", "smw_ask_defaultformat": "defaut", "smw_ask_otheroptions": "Autras opcions", "smw-ask-otheroptions-collapsed-info": "Utilizatz l'icòna \"plus\" per afichar totas las opcions disponiblas", "smw_ask_show_embed": "Far veire lo còdi incrustat", "smw_ask_hide_embed": "Amagar lo còdi incrustat", "smw_ask_embed_instr": "Per incrustar aquesta requèsta dins una pagina wiki, utilizatz lo còdi <PERSON>.", "smw-ask-delete": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-sorting": "Triada", "smw-ask-search": "<PERSON><PERSON><PERSON>", "searchbyproperty": "Recercar per atribut", "smw_sbv_docu": "<PERSON><PERSON>car totas las paginas qu'an un atribut donat amb una cèrta valor.", "smw_sbv_novalue": "Picatz una valor o consultatz totas las valors dels atributs per $1.", "smw_sbv_displayresultfuzzy": "Una lista de totas las paginas que son la proprietat « $1 » amb la valor « $2 ». Perque i a pas que qualques resultats, las valors pròchas tanben son afichadas.", "smw_sbv_property": "Proprietat :", "smw_sbv_value": "Valor :", "smw_sbv_submit": "Trobar de resultats", "browse": "Percórrer lo wiki", "smw_browselink": "Cercar las proprietats", "smw_browse_article": "Picatz lo nom de la pagina a partir de la quala volètz començar la navigacion.", "smw_browse_go": "Validar", "smw_browse_show_incoming": "afichar las proprietats que puntan aicí", "smw_browse_hide_incoming": "amagar las proprietats que puntan aicí", "smw_browse_no_outgoing": "Aquesta pagina a pas cap de proprietat.", "smw_browse_no_incoming": "Cap de proprietat punta pas cap a aquesta pagina.", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "Inversar lo labèl de proprietat", "pageproperty": "Recercar dins las proprietats de la pagina", "smw_pp_docu": "Picatz una pagina e una proprietat o solament una proprietat per recuperar totas las valors assignadas.", "smw_pp_from": "De la pagina", "smw_pp_type": "Proprietat :", "smw_pp_submit": "Afichar los resultats", "smw_result_prev": "Precedent", "smw_result_next": "Seguent", "smw_result_results": "Resultats", "smw_result_noresults": "O planhèm, i a pas cap de resultats.", "smwadmin": "Foncions d'administrator", "smw-admin-setupsuccess": "Lo motor d'emmagazinatge es estat mes en plaça.", "smw_smwadmin_return": "Tornar cap a $1", "smw_smwadmin_updatestarted": "Un processús novèl pel refrescament de semantic data a començat.\nTotas las donadas estocadas seràn reconstruchas o quitament reparadas se necessari.\nPodètz seguir la progression de la mesa a jorn sus aquesta pagina especiala.", "smw_smwadmin_updatenotstarted": "Un processús de mesa a jorn ja es en cors d’execucion.\nNe creetz pas d'autre.", "smw_smwadmin_updatestopped": "Totes los processús de mesa a jorn son estats arrestats.", "smw_smwadmin_updatenotstopped": "Per arrestar lo processús en cors de mesa a jorn, vos cal marcar la casa per indicar que ne sètz vertadièrament segur(a).\n\nTornar a $1 .", "smw-admin-docu": "Aquesta pagina especiala vos ajuda pendent l’installacion, la mesa a jorn, la mantenença e l'utilizacion de <a href=\"https://semantic-mediawiki.org\">Semantic MediaWiki</a> e compòrta d'autras foncions e prètzfaits administratius e tanben d'estatisticas.\nDoblidetz pas de salvar vòstras donadas importantas abans d'executar de foncions d’administracion.", "smw-admin-db": "Installacion e mesa a nivèl de la basa de donadas", "smw-admin-dbdocu": "Semantic MediaWiki requerís maitas extensions per la basa de donadas MediaWiki en òrdre per emmagazinar las donadas de semantica.\nLa foncion çaijós verifica que vòstra basa de donadas es estada installada corrèctament.\nLas modificacions faitas al moment d'aquesta etapa afectaràn pas la rèsta de la basa de donadas Mediawiki, e pòt èsser desfaita a volontat.\nAquesta foncion d’installacion pòt èsser aviada mantun còp sens causar lo mendre degalh, mas una sola installacion o mesa e nivèl es necessària.", "smw-admin-permissionswarn": "Se l’operacion fracassa amb d'errors SQL, l’utilizaire de la basa de donadas utilizada per vòstre wiki (agachatz vòstre LocalSettings.php) a probablament pas los dreits sufisents.\nCal siá permetre a aqueste utilizaire de dispausar de las permissions per crear e suprimir las taulas, siá entrar temporàriament la connexion en root a vòstra basa de donadas dins lo LocalSettings.php, siá utilizar l'escript de mantenença <code>setupStore.php</code> lo qual pòt utilizar las informacions de connexion d'un administrator.", "smw-admin-dbbutton": "Inicializar o metre a nivèl las taulas", "smw-admin-announce": "Anonciar vòstre wiki", "smw_smwadmin_datarefresh": "Reconstruccion de las donadas", "smw_smwadmin_datarefreshdocu": "Es possible de restablir totas las donadas Semantic MediaWiki basadas suls contenguts corrents d'aqueste wiki.\nAquò pòt èsser util per reparar de donadas rompudas o per refrescar las donadas se lo format intèrne a cambiat al moment de las remesas a nivèl.\nLa mesa a jorn es executada pagina per pagina e se serà pas acabada immediatament.\nLa pagina seguenta aficha se una mesa a jorn es en cors d’execucion e vos permet de començar o d’arrestar aquestas (levat s'aquesta foncionalitat est desactivada per l’administrator del site).", "smw_smwadmin_datarefreshprogress": "<strong>Una mesa a jorn es a s’executar.</strong>\nEs normal qu’una mesa a jorn progresse lentament dempuèi que refresque unicament las donadas dins de troces pichons a cada còp qu’un utilizaire accedís al wiki.\nPer acabar aquesta actualizacion pus rapidament, podètz invocar l'escript de mantenença MediaWiki <code>runJobs.php</code> (utilizatz l’opcion <code>--maxjobs 1000</code> per restrénher lo nombre de las mesas a jorn per escript aviat).\nProgression estimada de l’actualizacion actuala :", "smw_smwadmin_datarefreshbutton": "Planificar la reconstruccion de las donadas", "smw_smwadmin_datarefreshstop": "Arrestar aquesta mesa a jorn", "smw_smwadmin_datarefreshstopconfirm": "Òc, ne soi {{GENDER:$1|segur|segura}}.", "smw-admin-job-scheduler-note": "La màger part de las activitats dins aquesta seccion son faitas com de tascas per evitar las situacions de camin d'enlòc pendent l'execucion. Lo [https://www.mediawiki.org/wiki/Manual:Job_queue job scheduler] es responsable de l'execucion e es critic que l'escript de mantenença <code>runJobs.php</code> (veire tanben <code>$wgRunJobsAsync</code>) aja una capacitat adaptada.", "smw-admin-outdateddisposal-active": "Una tasca de supression d'entitats obsoletas es estada planificada.", "smw-admin-propertystatistics-active": "Una tasca de reconstruccion d'estatisticas de proprietat es estada planificada.", "smw-admin-fulltext-active": "Una tasca de reconstruccion de la recèrca plen tèxte es estat planificat.", "smw-admin-support": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "smw-admin-supportdocu": "Divèrsas ressorsas vos poirián ajudar en cas de problèmas :", "smw-admin-installfile": "S'experimentatz de problèmas amb vòstra installacion, comen<PERSON>tz per agachar lo guida en linha dins lo <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">fichièr INSTALL</a>.", "smw-admin-smwhomepage": "La documentacion completa de l’utilizaire de Semantic MediaWiki se tròba sus <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Los bugs pòdon èsser senhalats sus <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>.", "smw-admin-questions": "Av<PERSON><PERSON> d’autras questions o de suggestions, re<PERSON><PERSON><PERSON><PERSON> la discussion sul <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">forum dels utilizaires de Semantic MediaWiki</a>.", "smw-admin-supplementary-settings-title": "Paramètres de configuracion", "smw_adminlinks_datastructure": "Estructura de las donadas", "smw_adminlinks_displayingdata": "Afichatge de las donadas", "smw_adminlinks_inlinequerieshelp": "Ajuda sus las requèstas", "smw-property-indicator-type-info": "Proprietat definida per l{{PLURAL:$1|'utilizaire|o sistèma}}", "smw-createproperty-isproperty": "Aquò es una proprietat del tipe $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|La valor autorizada per aquesta proprietat es|Las valors autorizadas per aquesta proprietat son}} :", "smw-paramdesc-category-delim": "Lo delimitador", "smw-ui-tooltip-title-property": "Proprietat", "smw-ui-tooltip-title-quantity": "Conversion d'unitat", "smw-ui-tooltip-title-info": "Informacion", "smw-ui-tooltip-title-service": "Ligams de servici", "smw-ui-tooltip-title-warning": "Error", "smw-ui-tooltip-title-parameter": "Paramètre", "smw-ui-tooltip-title-event": "Eveniment", "smw-ui-tooltip-title-note": "Nò<PERSON>", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "Re<PERSON><PERSON><PERSON>", "smw_unknowntype": "Lo tipe d'aquesta proprietat es invalid", "smw_concept_header": "Paginas del concèpte « $1 »", "smw_conceptarticlecount": "Afichar çaijós $1 {{PLURAL:$1|pagina|paginas}}.", "group-smwadministrator": "Administrators (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrator|administratritz}} (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Administrators (Semantic MediaWiki)", "group-smwcurator": "Conservadors (MediaWiki Semantica)", "smw-property-predefined-default": "\"$1\" es una proprietat predefinida.", "smw-sp-properties-header-label": "Lista de las proprietats", "smw-sp-admin-settings-button": "Generar la lista dels paramètres", "smw-admin-objectid": "Id de l’objècte :", "smw-livepreview-loading": "Cargament…", "smw-sp-searchbyproperty-resultlist-header": "Lista de resultats", "smw-datavalue-number-nullnotallowed": "\"$1\" tornat amb \"NULL\" qu'es pas una valor numerica autorizada.", "log-name-smw": "Semantica del jornal MediaWiki", "smw-types-list": "Lista dels tipes de donadas", "smw-special-pageproperty-description": "Aquesta pagina provesís un interfaci de recerca per trobar totas las valors de la proprietat e una pagina especifica. D''autres interfacis de recerca comprenon  [[Special:SearchByProperty|property search]] e [[Special:Ask|ask query builder]].", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|segonda|segondas}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|segonda|segondas}}", "smw-datavalue-external-formatter-invalid-uri": "« $1 » es una URL invalida.", "smw-datavalue-reference-outputformat": "$1 : $2", "smw-parser-invalid-json-format": "L’analisador JSON a tornat un « $1 ».", "smw-no-data-available": "Cap de donada pas disponibla.", "smw-listingcontinuesabbrev": "(seguida)", "smw-showingresults": "Afichatge de <b>$1</b> resultat{{PLURAL:$1||s}} a partir del n°<b>$2</b>."}