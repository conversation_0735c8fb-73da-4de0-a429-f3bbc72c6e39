{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Amire80", "Avatar6", "BaRaN6161 TURK", "Felipe95a", "H78c67c", "<PERSON>", "Hello903hello", "Kghbln", "<PERSON><PERSON><PERSON>", "Liuxinyu970226", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Nemo bis", "Nike", "Ochaochaocha3", "Omotecho", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Umher<PERSON>render", "<PERSON><PERSON><PERSON> p", "Vlad5250", "Zoranzoki21"]}, "smw-desc": "{{desc|name=Semantic MediaWiki|url=https://www.mediawiki.org/wiki/Extension:Semantic_MediaWiki}}", "smw-title": "{{notranslate}}\n\nName of the extension as it should be displayed.\n\n{{Identical|Semantic MediaWiki}}", "smw-error": "{{Identical|Error}}", "smw-upgrade-error": "This is an error message.\n\nParameter:\n* $1 - holds the upgrade key", "smw-upgrade-release": "This is a section header on the software status page. Translate as a noun.", "smw-upgrade-progress": "This is a section header on the software status page. Translate as a noun.", "smw-upgrade-progress-explain": "This is a section header on the software status page.", "smw-upgrade-progress-create-tables": "This is a status information on the software status page.", "smw-upgrade-progress-post-creation": "This is a status information on the software status page.", "smw-upgrade-progress-table-optimization": "This is a status information on the software status page.", "smw-upgrade-progress-supplement-jobs": "This is a status information on the software status page.", "smw-upgrade-error-title": "This is the title of a section header on the update status page. Semantic MediaWiki is a given name.\n{{Identical|Error}}", "smw-upgrade-error-why-title": "This is a section header.", "smw-upgrade-error-why-explain": "This is a information on the software status page.", "smw-upgrade-error-how-title": "This is a section header.", "smw-upgrade-error-how-explain-admin": "This is an error message.", "smw-upgrade-error-how-explain-links": "This is an informatory message.", "smw-extensionload-error-why-title": "This is a section header on the software status page.", "smw-extensionload-error-why-explain": "This is a information on the software status page.", "smw-extensionload-error-how-title": "This is a section header on the software status page.", "smw-extensionload-error-how-explain": "This is an information on the software status page.", "smw-upgrade-maintenance-title": "This is a section header.", "smw-upgrade-maintenance-why-title": "This is an informatory message.", "smw-upgrade-maintenance-note": "This is an informatory message.", "smw-upgrade-maintenance-explain": "This is a information on the software status page.", "smw-semantics-not-enabled": "This is an informatory message.", "smw_viewasrdf": "This is the text of the link that is triggering the results export when clicked. It appears on pages in namespace Concept: or within the [https://semantic-mediawiki.org/wiki/Help:Browsing_interfaces#The_factbox factbox].", "smw_finallistconjunct": "The last separator in a list. For example a list could look like this: \"A, B, and C\". The comma might not be appropriate in your language.\n\t{{Identical|And}}", "smw-factbox-head": "This is the intro of tab.\n\nParameter:\n* $1 - holds the name of a page", "smw-factbox-facts": "This is the title of a tab.\n{{Identical|Fact}}", "smw-factbox-facts-help": "This is a information shown as a tooltip when hovering over a tab.", "smw-factbox-attachments": "This is the label of a tab. Attachements as in file attachments.", "smw-factbox-attachments-value-unknown": "This is a status information for file attachements within the factbox.", "smw-factbox-attachments-is-local": "This is a status information for file attachements within the factbox indicating that the respective file was uploaded locally.", "smw-factbox-attachments-help": "This is a information shown as a tooltip when hovering over a tab. Attachments as in file attachments.", "smw-factbox-facts-derived": "This is the title of a tab.", "smw-factbox-facts-derived-help": "This is a information shown as a tooltip when hovering over a tab.", "smw_isspecprop": "Used on [[Special:Properties]] to identify a special (build in) property.", "smw-concept-cache-header": "This is a section header on a page containing a concept.", "smw-concept-cache-count": "This is an informatory message shown on the page containing a concept.\n\nParameter:\n* $1 holds the number of cached objects.\n* $2 holds the timestamp the cache was generated.", "smw-concept-no-cache": "This is an informatory message.", "smw_concept_description": "Title of the section containing the code of a [https://semantic-mediawiki.org/wiki/Help:Concepts concept] (special precomputable query).\n\nParameters:\n* $1 - the name of the concept", "smw_no_concept_namespace": "This is an error/warning message that appears when a concept is added to a page in namespaces other than Concept:.", "smw_multiple_concepts": "This is an error/warning message that appears when more than concept is added to a page in namespaces Concept:.", "smw_concept_cache_miss": "This is an information message. Parameters:\n* $1 holds the name of the [https://semantic-mediawiki.org/wiki/Help:Concepts concept] (special precomputable query)", "smw_noinvannot": "This is an information message.", "version-semantic": "This is the name of the extension group on special page [[Special:Version|\"Version\"]].", "smw_uri_blacklist": "{{notranslate}}", "smw_baduri": "This is an information message. Parameters:\n* $1 holds the malformed URI.", "smw_csv_link": "This is the text of the link that is triggering the results export to a CSV-file when clicked.\n\n{{optional}}", "smw_dsv_link": "This is the text of the link that is triggering the results export to a DSV-file when clicked.\n\n{{optional}}", "smw_json_link": "This is the text of the link that is triggering the results export to a JSON-file when clicked.\n\n{{optional}}", "smw_rdf_link": "This is the text of the link that is triggering the results export to a RDF-file when clicked.\n\n{{optional}}", "smw_printername_count": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Count_format count].", "smw_printername_csv": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:CSV_format CSV].", "smw_printername_dsv": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:DSV_format DSV].", "smw_printername_debug": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Debug_format Debug].", "smw_printername_embedded": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Embedded_format Embedded].", "smw_printername_json": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:JSON_format JSON].", "smw_printername_list": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:List_format List].\n{{Identical|List}}", "smw_printername_plainlist": "This is the name of a result format.", "smw_printername_ol": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Ol_format Numbered list].", "smw_printername_ul": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Ul_format Bulleted list].", "smw_printername_table": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Table_format Table].\n\n{{Identical|Table}}", "smw_printername_broadtable": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Broadtable_format Broadtable].", "smw_printername_template": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Template_format Template].\n\n{{Identical|Template}}", "smw_printername_templatefile": "This is the name of the result format [https://www.semantic-mediawiki.org/wiki/Help:Templatefile_format templatefile].", "smw_printername_rdf": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:RDF_format RDF].", "smw_printername_category": "This is the name of the result format [https://semantic-mediawiki.org/wiki/Help:Category_format Category].\n\n{{Identical|Category}}", "validator-type-class-SMWParamSource": "This is the description of the \"source\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].\n{{Identical|Text}}", "smw-paramdesc-limit": "This is the description of the \"limit\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-offset": "This is the description of the \"offset\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-headers": "This is the description of the \"headers\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-mainlabel": "This is the description of the \"mainlabel\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-link": "This is the description of the \"link\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-intro": "This is the description of the \"intro\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-outro": "This is the description of the \"outro\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-default": "This is the description of the \"default\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-sep": "This is the description of the \"sep\" (result separator) parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-propsep": "This is the description of the \"propsep\" (property separator) parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-valuesep": "This is the description of the \"valuesep\" (value separator) parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-showsep": "This is the description of the \"showsep\" (show value separator at top of csv file) parameter for the \"csv\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].\n{{doc-important|Do not translate the parameter name \"sep\". However <value> may be translated.}}", "smw-paramdesc-distribution": "This is the description of the \"distribution\" parameter for the \"jqplotbar\" and \"jqplotpie\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-distributionsort": "This is the description of the \"distributionsort\" parameter for the \"jqplotbar\" and \"jqplotpie\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-distributionlimit": "This is the description of the \"distributionlimit\" parameter for the \"jqplotbar\" and \"jqplotpie\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-aggregation": "This is the description of the \"aggregation\" parameter for the \"jqplotchart\" and \"sparkline\" [https://www.semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-template": "This is the description of the \"introtemplate\" parameter of the \"template\", \"list\", \"ol\" and \"ul\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-columns": "This is the description of the \"columns\" parameter of the \"category\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].\n\nParameters:\n* $1 - default value", "smw-paramdesc-userparam": "This is the description of the \"userparam\" parameter for the \"template\", \"list\", \"ol\" and \"ul\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-class": "This is the description of the \"css\" parameter of the \"list\"/\"ul\"/\"ol\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-introtemplate": "This is the description of the \"introtemplate\" parameter of the \"template\", \"list\", \"ol\" and \"ul\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-outrotemplate": "This is the description of the \"outrotemplate\" parameter of the \"template\", \"list\", \"ol\" and \"ul\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-embedformat": "This is the description of the \"embedformat\" parameter of the \"embedded\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-embedonly": "This is the description of the \"embedonly\" parameter of the \"embedded\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-table-class": "This is the description of the \"css\" parameter of the \"table\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-table-transpose": "This is the description of the \"transpose\" parameter of the \"table\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-prefix": "This is the description of the \"prefix\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-rdfsyntax": "This is the description of the \"syntax\" parameter for the \"rdf\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-csv-sep": "This is the description of the \"separator\" parameter for the \"csv\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-csv-valuesep": "This is the description of the \"valuesseparator\" parameter for the \"csv\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-csv-merge": "This is the description of the \"merge\" parameter for the \"csv\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-csv-bom": "This is the description of the \"bom\" parameter for the \"csv\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries]. BOM as in [https://en.wikipedia.org/wiki/Byte_order_mark BOM].", "smw-paramdesc-dsv-separator": "This is the description of the \"separator\" parameter for the \"dsv\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-dsv-filename": "This is the description of the \"filename\" parameter for the \"dsv\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-filename": "This is the description of the \"filename\" parameter for the [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-smwdoc-description": "This is the short description of the [https://semantic-mediawiki.org/wiki/Help:Generating_documentation smwdoc parser function].", "smw-smwdoc-default-no-parameter-list": "This is an informatory message.", "smw-smwdoc-par-format": "This is the description of the parameter \"format\" of the [https://semantic-mediawiki.org/wiki/Help:Generating_documentation smwdoc parser function].", "smw-smwdoc-par-parameters": "This is the description of the parameter \"parameters\" of the [https://semantic-mediawiki.org/wiki/Help:Generating_documentation smwdoc parser function]. {{doc-important|Do not translate the possible parameter values \"specific\", \"base\" and \"all\".}}", "smw-paramdesc-sort": "This is the description of the \"sort\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-order": "This is the description of the \"order\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-searchlabel": "This is the description of the \"searchlabel\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries]. \"… further results\" must be identical to message {{msg-mw|Smw iq moreresults}}.", "smw-paramdesc-named_args": "This is the description of the \"named args\" parameter of the \"template\", \"list\", \"ol\" and \"ul\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-template-arguments": "This is the description of the \"template arguments\" parameter of the \"template\", \"list\", \"ol\" and \"ul\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-import-annotation": "This is the description of the \"import-annotation\" parameter of the \"template\" and \"list\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-export": "This is the description of the \"export\" parameter of the \"json\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-prettyprint": "This is the description of the \"[[wikipedia:prettyprint|prettyprint]]\" parameter of the \"JSON\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-json-unescape": "This is the description of the \"unescaped\" parameter of the \"json\" [https://www.semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-json-type": "This is the description of the \"type\" parameter for the \"json\" [https://www.semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-source": "This is the description of the \"source\" parameter for [https://semantic-mediawiki.org/wiki/Help:Inline_queries#Standard_parameters_for_inline_queries inline queries].", "smw-paramdesc-jsonsyntax": "This is the description of the \"syntax\" parameter used by [http://www.semantic-mediawiki.org/wiki/Help:JSON_format].", "smw-printername-feed": "This is the name of the [https://semantic-mediawiki.org/wiki/Help:Feed_format Feed result format].", "smw-paramdesc-feedtype": "This is the description of the feed type", "smw-paramdesc-feedtitle": "This is the description of the \"title\" parameter of the \"feed\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-feeddescription": "This is the description of the \"description\" parameter for the \"feed\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-feedpagecontent": "This option allows to enable page content to be generated with the feed", "smw-label-feed-link": "This is the text of the link that is triggering the results export to a RSS-file when clicked. {{optional}}", "smw-label-feed-description": "This message is displayed as the feed description, e.g. \"News on translatewiki.net rss feed\".\n\nParameters:\n* $1 - a feed description, e.g. \"News on translatewiki.net\"\n* $2 - a feed type. either \"rss\" or \"atom\"\n{{Identical|Feed}}", "smw-paramdesc-mimetype": "This is the description of the \"mimetype\" parameter of the \"templatefile\" [https://www.semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw_iq_disabled": "This is an information message.", "smw_iq_moreresults": "This is the text of the link that points to further results in case the [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline query] has more than currently displayed on the page. See also message {{msg-mw|Smw-paramdesc-searchlabel}}.", "smw_parseerror": "This is an error/warning message.", "smw_decseparator": "{{Callout|<big>Please also translate even if your translation will be identical to the source.</big>|error}}\n{{Doc-important|In case you add a separator symbol here, make sure that you also add the corresponding symbol to {{Msg-mw|smw_kiloseparator}} or add an empty translation if no matching symbol is used in your language!!!}}\nThis message is as a separator symbol for decimal digits in numbers, like \".\" in English 1,234.23. It is used for formatting number output '''and''' for reading user input. Therefore it should be carefully considered whether to change an existing value, since existing installations may depend on this value for their content to be read properly.\n\nNote that spaces and space-like HTML entities are always ignored when reading numbers.", "smw_kiloseparator": "{{Callout|<big>Please also translate even if your translation will be identical to the source.</big>|error}}\n{{Doc-important|In case you add a separator symbol here, make sure that you also add the corresponding symbol to {{Msg-mw|smw_decseparator}} or add an empty translation if no matching symbol is used in your language!!!}}\nThis message is as a separator symbol for thousands in numbers, like \",\" in English 1,234.23. It is used for formatting number output '''and''' for reading user input. Therefore it should be carefully considered whether to change an existing value, since existing installations may depend on this value for their content to be read properly.\n\nNote that spaces and space-like HTML entities are always ignored when reading numbers, whether or not a space symbol is used here.", "smw_notitle": "This is an error/warning message. Parameters:\n* $1 holds the property value causing the error/warning.", "smw_noproperty": "This is an error/warning message. Parameters:\n* $1 holds the property value causing the error/warning.", "smw_wrong_namespace": "This is an error/warning message. Parameters:\n* $1 holds the name of the namespace that may be used.", "smw_manytypes": "This is an error/warning message.", "smw_emptystring": "This is an error/warning message.", "smw_notinenum": "This is an error/warning message.\n\nParameters:\n* $1 holds the property value causing the error/warning.\n* $2 holds the property values that may be used.\n* $3 holds the property name.", "smw-datavalue-constraint-error-allows-value-list": "This is an error message.\n\nParameters:\n* $1 - holds the property value\n* $2 - holds the name of the list containing property values\n* $3 - holds the name of the property", "smw-datavalue-constraint-error-allows-value-range": "This is an error message.\n\nParameters:\n* $1 - holds the property value\n* $2 - holds the name of the range for property values\n* $3 - holds the name of the property", "smw-constraint-error": "This is the header within a tooltip.", "smw-constraint-error-suggestions": "This is an informatory message on special page \"ConstraintErrorList\".", "smw-constraint-error-limit": "This is a message on special page \"ConstraintErrorList\".\n\nParameter:\n* $1 - Holds the number of violations.", "smw_noboolean": "This is an error/warning message. Parameters:\n* $1 holds the property value causing the error/warning.", "smw_true_words": "These are the boolean values for \"true\" which may be assigned to properties of data type [https://semantic-mediawiki.org/wiki/Help:Type_Boolean Boolean], given as a comma-separated list.\n\nSee also:\n* {{msg-mw|Smw false words}}", "smw_false_words": "These are the boolean values for \"false\" which may be assigned to properties of data type [https://semantic-mediawiki.org/wiki/Help:Type_Boolean Boolean], given as a comma-separated list.\n\nSee also:\n* {{msg-mw|Smw true words}}", "smw_nofloat": "This is an error/warning message. Parameters:\n* $1 holds the property value causing the error/warning.", "smw_infinite": "This is an error/warning message. Parameters:\n* $1 holds the property value causing the error/warning.", "smw_unitnotallowed": "This is an error/warning message. Parameters:\n* $1 holds the unit causing the error/warning.", "smw_nounitsdeclared": "This is an error/warning message.", "smw_novalues": "This is an error/warning message.", "smw_nodatetime": "This is an error/warning message. Parameters:\n* $1 holds the property value causing the error/warning.", "smw_toomanyclosing": "This is an error/warning message. Parameters:\n* $1 holds the string causing the error/warning.", "smw_noclosingbrackets": "This is an error/warning message.", "smw_misplacedsymbol": "This is an error/warning message. Parameters:\n* $1 holds the string causing the error/warning.", "smw_unexpectedpart": "This is an error/warning message. Parameters:\n* $1 holds the string causing the error/warning.", "smw_emptysubquery": "This is an error/warning message.", "smw_misplacedsubquery": "This is an error/warning message.", "smw_valuesubquery": "This is an error/warning message. Parameters:\n* $1 holds the property value causing the error/warning.", "smw_badqueryatom": "This is an error/warning message.", "smw_propvalueproblem": "This is an error/warning message. Parameters:\n* $1 holds the property value causing the error/warning.", "smw_noqueryfeature": "This is an error/warning message. Parameters:\n* $1 holds the part(s) of the query which were dropped.", "smw_noconjunctions": "This is an error/warning message. Parameters:\n* $1 holds the part(s) of the query which were dropped.", "smw_nodisjunctions": "This is an error/warning message. Parameters:\n* $1 holds the part(s) of the query which were dropped.", "smw_querytoolarge": "This is an error/warning message.\n\nParameters:\n* $1 holds the part(s) of the query which were dropped.\n* $2 holds the number of parts of the query which were dropped.", "smw_notemplategiven": "{{doc-important|Do not translate the \"template\" parameter.}}\n\nThis is an error/warning message.", "smw_db_sparqlqueryproblem": "This is an error message.", "smw_db_sparqlqueryincomplete": "This is an error message.", "smw_type_header": "This is the header on [[Special:Types]]. Parameters:\n* $1 holds the name of the respective data type.", "smw_typearticlecount": "This is the introductory message below the header on [[Special:Types]]. Parameters:\n* $1 holds the number of property pages displayed in the current view.", "smw_attribute_header": "This is the header on a page in namespace \"Property\". Parameters:\n* $1 holds the name of the respective property.", "smw_attributearticlecount": "This is the introductory message below the header on a page in namespace \"Property\". Parameters:\n* $1 holds the number of property pages displayed in the current view.", "smw-propertylist-subproperty-header": "This is a section header on a page.", "smw-propertylist-redirect-header": "This is a section header on a page.", "smw-propertylist-error-header": "This is a section header of a property page.", "smw-propertylist-count": "This is an informatory message.\n\nParameter:\n\n* $1 Number of related entities", "smw-propertylist-count-with-restricted-note": "This is an informatory message.\n\nParameters:\n\n* $1 - Number of related entities\n* $2 - Maximum number of related entities that can be shown.", "smw-propertylist-count-more-available": "This is an informatory message.\n\nParameter:\n\n* $1 Number of related entities", "specialpages-group-smw_group": "{{optional}}\n{{doc-special-group|that=are related to (or depend on) Semantic MediaWiki|like=[[Special:OfflineImportLexicon]], [[Special:Ask]], [[Special:QueryCreator]], [[Special:Browse]], [[Special:PageProperty]], [[Special:SearchByProperty]], [[Special:SMWAdmin]], [[Special:ExportRDF]], [[Special:ObjectEditor]], [[Special:WidgetAssembler]], [[Special:WidgetClone]], [[Special:BrowseWiki]], [[Special:SolrSearch]]}}\n{{Identical|Semantic MediaWiki}}", "specialpages-group-smw_group-maintenance": "This is a section header on special page \"SpecialPages\"", "specialpages-group-smw_group-properties-concepts-types": "This is a section header on special page \"SpecialPages\". Translate types as in datatypes.", "specialpages-group-smw_group-search": "This is a section header on special page \"SpecialPages\"", "exportrdf": "{{doc-special|ExportRDF}}", "smw_exportrdf_docu": "This is the introductory message at the top of [[Special:ExportRDF]].", "smw_exportrdf_recursive": "This is the text describing an option available to choose from on [[Special:ExportRDF]].", "smw_exportrdf_backlinks": "This is the text describing an option available to choose from on [[Special:ExportRDF]].", "smw_exportrdf_lastdate": "This is the text describing an optional input field available on [[Special:ExportRDF]].", "smw_exportrdf_submit": "This is the name of the submit button on [[Special:ExportRDF]] to trigger the export.\n\n{{Identical|Export}}", "uriresolver": "This is the name of [[Special:URIResolver]].", "properties": "{{doc-special|Properties}}\n{{Identical|Property}}", "smw-categories": "A label.\n{{Identical|Category}}", "smw_properties_docu": "This is the introductory message at the top of [[Special:Properties]].", "smw_property_template": "This message is used on [[Special:Properties]] to display each property listed. It holds the following parameters:\n* $1 link to the property's page\n* $2 link to the property's data type page\n* $3 number of times the property is used", "smw_property_template_notype": "{{optional}}\nThis is used on [[Special:Properties]].\n*$1 holds the link to the property page.\n*$2 holds the number of times the property is used within the wiki.", "smw_propertylackspage": "This is an error/warning message.", "smw_propertylackstype": "This is an error/warning message. Parameters:\n* $1 holds the name of the assumed data type which is in fact always data type Page.", "smw_propertyhardlyused": "This is an error/warning message.", "smw-property-name-invalid": "This is an information message. Parameters:\n* $1 - property name", "smw-property-name-reserved": "This is an error message.\n\nParameter:\n* $1 - holds the reserved name", "smw-sp-property-searchform": "Introductory text for the property search form", "smw-sp-property-searchform-inputinfo": "Additional explanatory text about the filtering condition.", "smw-special-property-searchform": "Introductory text for the property search form", "smw-special-property-searchform-inputinfo": "Additional explanatory text about the filtering condition.", "smw-special-property-searchform-options": "This is a section header on special page \"Ask\".\n\n{{Identical|Options}}", "smw-special-wantedproperties-filter-label": "This is a field label on special page \"WantedProperties\".\n\n{{Identical|Filter}}", "smw-special-wantedproperties-filter-none": "This is an option selectable via a drop-down menu for filtering options on special page \"WantedProperties\". Translate in a sense: Apply no filter.\n\n{{Identical|None}}", "smw-special-wantedproperties-filter-unapproved": "This is an option selectable via a drop-down menu on special page \"WantedProperties\".\nTranslate in a sense: Apply filter for Unapproved.\n\n{{Identical|Unapproved}}", "smw-special-wantedproperties-filter-unapproved-desc": "This is an informative message.", "concepts": "{{doc-special|Concepts}}\n{{Identical|Concept}}", "smw-special-concept-docu": "This is an introductory message at the top of [[Special:Concepts]].", "smw-special-concept-header": "This is a header used on [[Special:Concepts]].", "smw-special-concept-count": "Used on [[Special:Concepts]] and to display available concepts.\n\nIf there are no concepts, {{msg-mw|Smw-special-concept-empty}} is used.\n\nParameters:\n* $1 - number of concepts", "smw-special-concept-empty": "This is used on [[Special:Concepts]] and to display that no concepts are available.\n\nSee also:\n* {{msg-mw|Smw-sp-concept-count}} - if there are concepts", "unusedproperties": "{{doc-special|UnusedProperties}}", "smw-unusedproperties-docu": "This is the introductory message at the top of [[Special:UnusedProperties]].", "smw-unusedproperty-template": "This message is used on [[Special:UnusedProperties]] to display each property listed. It holds the following parameters:\n* $1 link to the property's page\n* $2 link to the property's data type page", "wantedproperties": "{{doc-special|WantedProperties}}", "smw-wantedproperties-docu": "This is the introductory message at the top of [[Special:WantedProperties]]. Contrast with wanted but [https://translatewiki.net/w/i.php?title=Special:Translate&showMessage=smw-unusedproperties-docu&group=mwgithub-semanticmediawiki&language=en unused properties]. Compare with [[mediawiki:Smw-wantedproperties-docu/en|same context]].", "smw-wantedproperty-template": "This is the message on [[Special:WantedProperties]] showing the name of the property without an assigned data type and how often it is used in the wiki.\n\nParameters:\n* $1 holds the name of the wanted property.\n* $2 holds the number of values annotated with the wanted property.", "smw-special-wantedproperties-docu": "This is the introductory message at the top of special page \"WantedProperties\". Compare with those used in wikis but [[mediawiki:Smw-wantedproperties-docu/jen|without description page]]. Contrast with wanted but [https://translatewiki.net/w/i.php?title=Special:Translate&showMessage=smw-unusedproperties-docu&group=mwgithub-semanticmediawiki&language=en unused properties].", "smw-special-wantedproperties-template": "This is the message on [[Special:WantedProperties]] showing the name of the property without an assigned data type and how often it is used in the wiki.\n\nParameters:\n* $1 holds the name of the wanted property.\n* $2 holds the number of values annotated with the wanted property.", "smw_purge": "{{doc-actionlink}}\nThis is the label of a tab of an action item for the content area.\n{{Identical|Refresh}}", "smw-purge-update-dependencies": "This is an informatory message in a balloon informing about the automatic purge of the page triggered by the annotation query marker.", "smw-purge-failed": "This is an error/warning message. Please translate consistent to {{msg-mw|smw_purge}}.", "types": "{{doc-special|Types}}\n{{Identical|Type}}", "smw_types_docu": "This is the introductory message at the top of [[Special:Types]].", "smw-special-types-no-such-type": "Error message shown on [[Special:Types]] when specifying an invalid data type.\n\nParameter:\n* $1 - holds the constant of the specified datatype", "smw-statistics": "Is a label that is displayed on the [[Special:Statistics|Statistics]] page.\n{{Identical|Semantic statistics}}", "smw-statistics-cached": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.", "smw-statistics-entities-total": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.", "smw-statistics-entities-total-info": "This is a information shown as a tooltip when hovering over an icon.", "smw-statistics-property-instance": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of property values", "smw-statistics-property-total": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of properties", "smw-statistics-property-total-info": "This is a information shown as a tooltip when hovering over an icon.", "smw-statistics-property-total-legacy": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of properties", "smw-statistics-property-used": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameter:\n* $1 - number of properties", "smw-statistics-property-page": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of properties", "smw-statistics-property-page-info": "This is a information shown as a tooltip when hovering over an icon.", "smw-statistics-property-type": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of properties", "smw-statistics-query-inline-legacy": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page. See also message {{msg-mw|Smw-statistics-query-inline}}.\n\nParameters:\n* $1 - number of queries\n{{Identical|Query}}", "smw-statistics-query-inline": "Used as a label linking to the respective property that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of queries\n{{Identical|Query}}", "smw-statistics-query-format": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameter:\n* $1 - name of the format", "smw-statistics-query-size": "Is a label that is displayed on the [[Special:Statistics|Statistics]] page", "smw-statistics-concept-count-legacy": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - concepts\n{{Identical|Concept}}", "smw-statistics-concept-count": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of concepts\n{{Identical|Concept}}", "smw-statistics-subobject-count": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of subobjects", "smw-statistics-subobject-count-legacy": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of subobjects", "smw-statistics-datatype-count": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of datatypes", "smw-statistics-error-count": "Used as a label linking to the respective property that is displayed on the [[Special:Statistics|Statistics]] page. See also message {{msg-mw|smw-statistics-property-instance}}.\n\nParameters:\n* $1 - number of property values", "smw-statistics-error-count-legacy": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of property values and improper annotations respectively", "smw-statistics-delete-count": "Used as a label that is displayed on the [[Special:Statistics|Statistics]] page.\n\nParameters:\n* $1 - number of marked entities", "smw-statistics-delete-count-info": "This is a information shown as a tooltip when hovering over an icon.", "smw_uri_doc": "{{doc-important|Do not alter or translate link targets!}}\nThis message sketches the (very technical) function of this unlisted special page.\nIt probably does not need translation in most cases. The special page as such is used in all URIs used by Semantic MediaWiki.\nWhen somebody resolves such a URI, the special page will redirect to the according wiki page or to the according metadata export (OWL/RDF/XML).\nThis is controlled by the HTTP request header. Semantic Web crawlers and browsers can thus request more metadata on a particular subject, while humans are referred to readable pages.\nThis method is called ''content negotiation''.\n\nParameters:\n* $1 - the URL http://www.w3.org/2001/tag/issues.html#httpRange-14 (hard-coded)", "ask": "{{doc-special|Ask}}", "smw_ask_doculink": "{{notranslate}}\nThis message should be the URL of the semantic query documentation for that language. For English, this is https://semantic-mediawiki.org/wiki/Help:Semantic_search. For other languages, it should be an ''existing'' page on semantic-mediawiki.org. Note that only some languages have own documentations yet.\n\nThe SMW Project welcomes documentation translators! Please contact <PERSON> (https://semantic-mediawiki.org/wiki/<PERSON>) for getting a login at semantic-mediawiki.org if you wish to create SMW handbooks in another language.", "smw-ask-help": "This is an informatory message.", "smw_ask_sortby": "This is the text describing the textbox on [[Special:Ask]] available for optionally entering a printout statement for a query.", "smw_ask_ascorder": "This is the value in a dropdown menu on [[Special:Ask]] available for selecting a printout statement for a query.\n{{Identical|Ascending}}", "smw_ask_descorder": "This is the value in a dropdown menu on [[Special:Ask]] available for selecting a printout statement for a query.\n{{Identical|Descending}}", "smw-ask-order-rand": "This is an option selectable via a drop-down menu on special page \"Ask\".\n\n{{Identical|Random}}", "smw_ask_submit": "This is the name of the submit button on [[Special:Ask]] to start the semantic search.\n\n{{Identical|Find results}}", "smw_ask_editquery": "This is the text of the action link on [[Special:Ask]] to edit a query.", "smw_add_sortcondition": "{{doc-actionlink}}\nThis is the text of the action link on [[Special:Ask]] to add a printout statement to a query.", "smw-ask-sort-add-action": "This is the label of a link on special page \"Ask\".", "smw_ask_hidequery": "This is the text of the action link on [[Special:Ask]] to abandon a query.", "smw_ask_help": "This is the text of a link on [[Special:Ask]] pointing to the help page on [https://semantic-mediawiki.org/wiki/Help:Semantic_search semantic search].", "smw_ask_queryhead": "This is the header of the edit field available on [[Special:Ask]] to enter the [https://semantic-mediawiki.org/wiki/Help:Selecting_pages query conditions] of the query.\n{{Identical|Condition}}", "smw_ask_printhead": "This is the header of the edit field available on [[Special:Ask]] to enter the [https://semantic-mediawiki.org/wiki/Help:Displaying_information printout statements] of the query.", "smw_ask_printdesc": "This is an additional information message for the header of the edit field available on [[Special:Ask]] to enter the [https://semantic-mediawiki.org/wiki/Help:Displaying_information printout statements] of the query.", "smw_ask_format_as": "This is the text describing a dropdown box on [[Special:Ask]] available for choosing the [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for a query.", "smw_ask_defaultformat": "This is the label used for indicating the default [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] within a dropdown field.\n\n{{Identical|Default}}", "smw_ask_otheroptions": "This is the text for the header of a section on [[Special:Ask]] that is containing various different options to choose or enter in order to specify printout statements of a query.", "smw-ask-otheroptions-info": "This is the text describing the section ''Other options'' on [[Special:Ask]] that is containing various different options to choose or enter in order to specify printout statements of a query.", "smw-ask-otheroptions-collapsed-info": "Please use the plus icon to view all available options", "smw_ask_show_embed": "{{doc-actionlink}}\nThis is the text of the action link on [[Special:Ask]] to show the code of a query.", "smw_ask_hide_embed": "{{doc-actionlink}}\nThis is the text of the action link on [[Special:Ask]] to hide the code of a query.", "smw_ask_embed_instr": "This is an information message for the field on [[Special:Ask]] that contains the code of a query that may be copied and when pasted into a regular page on the wiki..", "smw-ask-delete": "This is the text of the action link on [[Special:Ask]] to delete a printout statement from a query.\n{{Identical|Remove}}", "smw-ask-sorting": "This is the text for the header of the section on [[Special:Ask]] that allows to specify sorting options for specific properties that are queried for.", "smw-ask-options": "This is a link label on special page \"Ask\".\n\n{{Identical|Options}}", "smw-ask-options-sort": "This is a section header on special page \"Ask\".", "smw-ask-format-options": "This is the header of a section in a tooltip.", "smw-ask-parameters": "This is the header of a section in a tooltip.\n{{Identical|Parameter}}", "smw-ask-search": "This is the label of an info box on \"Special:Ask\". Translate as a noun.\n\n{{Identical|Search}}", "smw-ask-debug": "This is the label of a link on special page \"Ask\".\nTranslate in a sense of \"start debugging the issue\".\n\n{{Identical|Debug}}", "smw-ask-debug-desc": "This is an explanatory message shown in a tooltip.", "smw-ask-no-cache": "This is the label of a link. Translate in a sense that the cache should be cleared when clicking.", "smw-ask-no-cache-desc": "This is an explanatory message shown in a tooltip.", "smw-ask-result": "This is a link label on special page \"Ask\".\n\n{{Identical|Result}}", "smw-ask-empty": "This is a link label on special page \"Ask\". Translate as an action: to empty something.", "smw-ask-download-link-desc": "This is an explanatory message shown in a tooltip.", "smw-ask-format": "This is a link label on special page \"Ask\".\n\n{{Identical|Format}}", "smw-ask-format-selection-help": "This is a text displayed on [[Special:Ask]] to inform about a help page on [https://semantic-mediawiki.org/wiki/ Semantic MediaWiki].\n\nParameters:\n* $1 - a link which points to Help: page on semantic-mediawiki.org displaying the name of the format.", "smw-ask-condition-change-info": "This is an informatory message shown at the top of special page \"Ask\".", "smw-ask-input-assistance": "This is the header of a section in a tooltip.", "smw-ask-condition-input-assistance": "This is an informatory message shown in a tooltip.", "smw-ask-condition-input-assistance-property": "This is an informatory message shown in a tooltip.\n\n{{Doc-important|Do not change the prefix <code>p:</code>}}", "smw-ask-condition-input-assistance-category": "This is an informatory message shown in a tooltip.\n\n{{Doc-important|Do not change the prefix <code>c:</code>}}", "smw-ask-condition-input-assistance-concept": "This is an informatory message shown in a tooltip.\n\n{{Doc-important|Do not change the prefix <code>con:</code>}}", "smw-ask-format-change-info": "This is an informatory message shown at the top of special page \"Ask\".", "smw-ask-format-export-info": "This is an informatory message shown at the top of special page \"Ask\".", "smw-ask-query-search-info": "An informatory message shown on \"Special:Ask\":\n\nParameters:\n* $1 shows the code of the query, e.g. <code><nowiki>[[Has number::+]]</nowiki></code>\n* $2 shows the name of the database backend that returned the answer, e.g. \"SMW\\SQLStore\\SQLStore\"\n* $3 indicates whether it is retrieved from cache or not \n* $4 shows the elapsed time needed to return the answer, e.g. 0.00635", "smw-ask-extra-query-log": "A tab on special page \"Ask\".", "smw-ask-extra-other": "A tab on special page \"Ask\".", "searchbyproperty": "{{doc-special|SearchByProperty}}", "processingerrorlist": "This is a header used on [[Special:ProcessingErrorList]].\n{{doc-important|This should be translated as \"List of processing errors\", and NOT as \"Processing the list of errors\".}}", "constrainterrorlist": "Name of special page \"ConstraintErrorList\".", "propertylabelsimilarity": "This is the title of a special page.\n\n{{Identical|Property label similarity report}}", "missingredirectannotations": "This is the label of a special page.", "smw-processingerrorlist-intro": "This is an introductory message at the top of [[Special:ProcessingErrorList]].", "smw-processingerrorlist-helplink": "{{Optional}}\n\nOnly change the link if a dedicated landing page for your language exists on semantic-mediawiki.org.", "smw-constrainterrorlist-intro": "This is a message on special page \"ConstraintErrorList\".", "smw-constrainterrorlist-helplink": "{{Optional}}\n\nOnly change the link if a dedicated landing page for your language exists on semantic-mediawiki.org.", "smw-missingredirects-intro": "This is an informatory message explaining the purpose of the special page and how to work with it.", "smw-missingredirects-list": "This is a section header on special page \"MissingRedirectAnnotations\".", "smw-missingredirects-list-intro": "This is an informatory message on special page \"MissingRedirectAnnotations\".\n\nParameter:\n\n* $1 - holds the number of pages found", "smw-missingredirects-noresult": "This is an informatory message on special page \"MissingRedirectAnnotations\".", "smw_sbv_docu": "This is the information message shown on [[Special:SearchByProperty]] in case no property and no property value were not yet provided to search for.", "smw_sbv_novalue": "This is an information message shown on [[Special:SearchByProperty]] in case no property value was provided.\n\nParameters:\n* $1 - the name of the property", "smw_sbv_displayresultfuzzy": "This is an information message shown on [[Special:SearchByProperty]] in case less than 20 results were found.\n\nParameters:\n* $1 - the name of the property\n* $2 - the value of the property one searched for", "smw_sbv_property": "This is the text describing a textbox on [[Special:SearchByProperty]] where the name of a property is expected to be typed in.\n\n{{Identical|Property}}", "smw_sbv_value": "This is the text describing a textbox on [[Special:SearchByProperty]] where the value of a property is expected to be typed in.\n\n{{Identical|Value}}", "smw_sbv_submit": "This is the name of the submit button on [[Special:SearchByProperty]] to trigger the search.\n\n{{Identical|Find results}}", "browse": "{{doc-special|B<PERSON>e}}", "smw_browselink": "This is the text of the link shown within the toolbox in the sidebar pointing to [[Special:<PERSON><PERSON><PERSON>]]", "smw_browse_article": "This is the text describing a textbox on [[Special:Browse]] where the name of a page is expected to be typed in.", "smw_browse_go": "This is the name of the submit button on [[Special:Browse]] to trigger the browsing of pages.\n\n{{Identical|Go}}", "smw_browse_more": "{{optional}}", "smw_browse_show_incoming": "{{doc-actionlink}}\nThis is the text of the link on [[Special:Browse]] that shows the properties linking to a page when clicked.\n\nSee also:\n* {{msg-mw|Smw browse hide incoming}}", "smw_browse_hide_incoming": "{{doc-actionlink}}\nThis is the text of the link on [[Special:Browse]] that hides the properties linking to a page when clicked.\n\nSee also:\n* {{msg-mw|Smw browse show incoming}}", "smw_browse_no_outgoing": "This is the information message shown on [[Special:Browse]] in case the browsed page does not contain any properties.", "smw_browse_no_incoming": "This is the information message shown on [[Special:Browse]] in case no properties link to the browsed page.", "smw-browse-from-backend": "This is an informatory message shown while the data are being retrieved for display on this special page.", "smw-browse-intro": "This is an informatory message explaining the purpose of the special page.", "smw-browse-invalid-subject": "This is an error/warning message.\n\nParameters:\n* $1 holds the error/warning", "smw-browse-api-subject-serialization-invalid": "This is an error message.", "smw-browse-js-disabled": "This is an error message.", "smw-browse-show-group": "{{doc-actionlink}}\nThis is the text of the link on [[Special:Browse]] that shows the property groups defined for certain properties.\n\nSee also:\n* {{msg-mw|Smw-browse-hide-group}}", "smw-browse-hide-group": "{{doc-actionlink}}\nThis is the text of the link on [[Special:Browse]] that hides the property groups defined for certain properties.\n\nSee also:\n* {{msg-mw|Smw-browse-show-group}}", "smw-noscript": "This is an informatory message.", "smw_inverse_label_default": "Inverse label default. Parameters:\n* $1 - a place marker\n{{Identical|Of}}", "smw_inverse_label_property": "Can as well be translated as \"Name of\", or \"Denomination of the inverse propery\". The inverse property is one having both its direction, and its source/target sets inverted. For example, the property \"is child of\" is the inverse propery of \"is parent of\".", "pageproperty": "This is the name of [[Special:PageProperty]].", "pendingtasklist": "This is the title of special page \"PendingTaskList\".", "facetedsearch": "{{doc-special|Facetedsearch}}", "smw_pp_docu": "This is the introductory message at the top of [[Special:PageProperty]].", "smw_pp_from": "This is the text describing a textbox on [[Special:PageProperty]] where the name of a page is expected to be typed in.\n{{Identical|From page}}", "smw_pp_type": "This is the text describing a textbox on [[Special:PageProperty]] where the name of a property is expected to be typed in.\n\n{{Identical|Property}}", "smw_pp_submit": "This is the name of the submit button on [[Special:PageProperty]] to trigger the search.\n\n{{Identical|Find results}}", "smw-prev": "This is the text of the link on property and concept pages that allows to return to the previous page with listings. Similar to {{Msg-mw|Prevn}} however never preceeded with 'show'.", "smw-next": "This is the text of the link on property and concept pages that allows to proceed to the next page with listings. Similar to {{Msg-mw|Nextn}} however never preceeded with 'show'.", "smw_result_prev": "This is the text of the link on [[Special:Ask]] or [[Special:SearchByProperty]] that allows to return to the previous page containing results for the query.\n\n{{Identical|Previous}}", "smw_result_next": "This is the text of the link on [[Special:Ask]] or [[Special:SearchByProperty]] that allows to proceed to the next page containing results for the query.\n\n{{Identical|Next}}", "smw_result_results": "This is the text on [[Special:Ask]] or [[Special:SearchByProperty]] preceding the shown range of results which were returned by the query, e.g. '''Results 1 - 50'''.\n\n{{Identical|Result}}", "smw_result_noresults": "This is the text on [[Special:Ask]] or [[Special:SearchByProperty]] shown in case a query does not return any results.\n{{Identical|No result}}", "smwadmin": "{{doc-special|SMWAdmin}}", "smw-admin-statistics-job-title": "This is a header.", "smw-admin-statistics-job-docu": "This is an informatory message.", "smw-admin-statistics-querycache-title": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-statistics-querycache-disabled": "This is an informatory message.", "smw-admin-statistics-querycache-legend": "This is an explanatory message used on a subpage to special page \"Semantic MediaWiki\".", "smw-admin-statistics-section-explain": "This is a message on special page \"SemanticMediaWiki\".", "smw-admin-statistics-semanticdata-overview": "This is a tab label on a subpage to special page \"SemanticMediaWiki\". \n\n{{Identical|Overview}}", "smw-admin-permission-missing": "This is an informatory message.", "smw-admin-setupsuccess": "This is the confirmation message at the bottom of the script's report concerning setup or update of the SMW database tables (process triggered from [[Special:SMWAdmin]]).", "smw_smwadmin_return": "This is the text of the link at the bottom of the report concerning setup or update of the SMW database tables pointing to [[Special:SMWAdmin]].\n\nParameters:\n* $1 - the link back to Special:SMWAdmin\n{{Identical|Return to $1}}", "smw_smwadmin_updatestarted": "This is the message at the top of [[Special:SMWAdmin]] confirming that the update process has been started.\n\nParameters:\n* $1 - the link back to Special:SMWAdmin", "smw_smwadmin_updatenotstarted": "This is the message at the top of [[Special:SMWAdmin]] informing that a new update process will not be started.\n\nParameters:\n* $1 - the link back to Special:SMWAdmin", "smw_smwadmin_updatestopped": "This is the message at the top of [[Special:SMWAdmin]] confirming that the update process has been stopped.\n\nParameters:\n* $1 - the link back to Special:SMWAdmin", "smw_smwadmin_updatenotstopped": "This is the message at the top of [[Special:SMWAdmin]] asking for reassurance concerning the termination of the current update process.\n\nParameters:\n* $1 - the link back to Special:SMWAdmin", "smw-admin-docu": "This is the introductory message at the top of [[Special:SMWAdmin]].", "smw-admin-environment": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-db": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-db-preparation": "This is an informatory message.", "smw-admin-dbdocu": "This is the text describing an available administrative option on [[Special:SMWAdmin]].", "smw-admin-permissionswarn": "This is the text describing a problem (including a solution) that may arise when using an available administrative option on [[Special:SMWAdmin]].", "smw-admin-dbbutton": "This is the text of the submit button on special page \"Semantic MediaWiki\" to trigger the respective process.", "smw-admin-announce": "This is the section header on special page \"Semantic MediaWiki\" as well as the name of the submit button to register the wiki at [https://www.wikiapiary.com wikiapiary.com].", "smw-admin-announce-text": "Short description of what wiki registration on [[Special:SMWAdmin]] means.", "smw-admin-deprecation-notice-title": "This is a section header on Special:SemanticMediaWiki.", "smw-admin-deprecation-notice-docu": "This is an informatory message explaining the purpose of some information provided.", "smw-admin-deprecation-notice-config-notice": "This is an informatory message.\n\nParameter:\n* $1 - holds the name of the depreciated configuration parameter\n* $2 - holds a version number", "smw-admin-deprecation-notice-config-notice-option": "This is an informatory message on special page \"SemanticMediaWiki\".\n\nParameter:\n* $1 - holds the name of the configuration parameter\n* $2 - counts the number of options", "smw-admin-deprecation-notice-config-notice-option-list": "This is an informatory message on special page \"SemanticMediaWiki\".\n\nParameter:\n* $1 - holds the name of the configuration parameter\n* $2 - holds the version number", "smw-admin-deprecation-notice-config-replacement": "This is an informatory message.\n\nParameters:\n* $1 - holds the name of the depreciated configuration parameter\n* $2 - holds the name of the replacing configuration parameter", "smw-admin-deprecation-notice-config-replacement-option": "This is an informatory message on special page \"SemanticMediaWiki\".\n\nParameter:\n* $1 - holds the name of the configuration parameter\n* $2 - counts the number of options\n{{Identical|Option}}", "smw-admin-deprecation-notice-config-replacement-option-list": "This is an informatory message on special page \"SemanticMediaWiki\".\n\nParameter:\n* $1 - holds the name of the current configuration parameter\n* $2 - holds the name of the future configuration parameter", "smw-admin-deprecation-notice-config-removal": "This is an informatory message.\n\nParameter:\n* $1 - holds the name of the removed configuration parameter\n* $2 - holds a version number", "smw-admin-deprecation-notice-title-notice": "This is a section header on special page \"Semantic MediaWiki\" informing about configuration parameters.", "smw-admin-deprecation-notice-title-notice-explanation": "This is an informatory message on special page \"Semantic MediaWiki\".", "smw-admin-deprecation-notice-title-replacement": "This is a section header on special page \"Semantic MediaWiki\" informing about configuration parameters.", "smw-admin-deprecation-notice-title-replacement-explanation": "\"Replaced or renamed settings\" is {{msg-mw|Smw-admin-deprecation-notice-title-replacement}}", "smw-admin-deprecation-notice-title-removal": "This is a section header on special page \"Semantic MediaWiki\" informing about configuration parameters.", "smw-admin-deprecation-notice-title-removal-explanation": "\"Removed settings\" is {{msg-mw|Smw-admin-deprecation-notice-title-removal}}", "smw-admin-deprecation-notice-section-legend": "This is a label.", "smw-smwadmin-refresh-title": "This is a section header on special page \"SemanticMediaWiki\".", "smw_smwadmin_datarefresh": "This is a section header on special page \"SemanticMediaWiki\".", "smw_smwadmin_datarefreshdocu": "This is the text describing an available administrative option on [[Special:SMWAdmin]].", "smw_smwadmin_datarefreshprogress": "This is an information message on [[Special:SMWAdmin]].", "smw_smwadmin_datarefreshbutton": "This is the name of the submit button on [[Special:SMWAdmin]] to trigger the respective process.", "smw_smwadmin_datarefreshstop": "This is the name of the submit button on [[Special:SMWAdmin]] to abort the respective process.", "smw_smwadmin_datarefreshstopconfirm": "This is the text next to the checkbox on [[Special:SMWAdmin]] reassuring that the respective process should indeed be cancelled.\nParameter:\n$1 - unused, or user who performed the action (to be used with GENDER)", "smw-admin-helplink": "{{Optional}}\n\nOnly change the URL if a dedicated page with translations into your language exist.", "smw-admin-job-scheduler-note": "This is an informatory message.", "smw-admin-outdateddisposal-title": "This is a section header on special page \"SemanticMediaWiki\".", "smw-admin-outdateddisposal-intro": "This is an informatory message shown on a special page explaining an administrative function. The last sentence refers to the scheduled interval of the cron job dealing with the MediaWiki job queue.", "smw-admin-outdateddisposal-active": "This is an informatory message on special page \"Semantic MediaWiki.", "smw-admin-outdateddisposal-button": "This is the text of the submit button on special page \"Semantic MediaWiki\" to trigger the respective process.", "smw-admin-feature-disabled": "This is an informatory text on special page \"Semantic MediaWiki\".", "smw-admin-propertystatistics-title": "This is a header.", "smw-admin-propertystatistics-intro": "This is an informatory message.", "smw-admin-propertystatistics-active": "This is an informatory message on special page \"Semantic MediaWiki.", "smw-admin-propertystatistics-button": "This is the text of the submit button on special page \"Semantic MediaWiki\" to trigger the respective process.", "smw-admin-fulltext-title": "This is a header.", "smw-admin-fulltext-intro": "This is an informatory message.", "smw-admin-fulltext-active": "This is an informatory message on special page \"Semantic MediaWiki.", "smw-admin-fulltext-button": "This is the text of the submit button on special page \"Semantic MediaWiki\" to trigger the respective process.", "smw-admin-support": "This is a section header on [[Special:SMWAdmin]].", "smw-admin-supportdocu": "This is the introductory text on [[Special:SMWAdmin]] followed by a list of resources.", "smw-admin-installfile": "This text on [[Special:SMWAdmin]] describes a resource for help.\n\n{{doc-important|Do not alter or translate link targets!}}", "smw-admin-smwhomepage": "This text on [[Special:SMWAdmin]] describes a resource for help.\n\n{{doc-important|Do not alter or translate link targets!}}", "smw-admin-bugsreport": "This text on [[Special:SMWAdmin]] describes where to report bugs.\n\n{{doc-important|Do not alter or translate link targets!}}", "smw-admin-questions": "This text on [[Special:SMWAdmin]] describes a resource for help.\n\n{{doc-important|Do not alter or translate link targets!}}", "smw-admin-other-functions": "This is a section header on special page \"SemanticMediaWiki.", "smw-admin-statistics-extra": "This is a section header on a subpage to special page \"SemanticMediaWiki\". Translate in the sense of further statistics.", "smw-admin-statistics": "This is a tab label on a subpage to special page \"SemanticMediaWiki\".", "smw-admin-supplementary-section-title": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-supplementary-section-subtitle": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-supplementary-section-intro": "This is an informatory message.", "smw-admin-supplementary-settings-title": "This is a label of a link.", "smw-admin-supplementary-settings-intro": "A message explaining a link.\n\nParameter:\n* $1 - Holds {{msg-mw|Smw-admin-supplementary-settings-title}}", "smw-admin-main-title": "This is a section header on special page \"Semantic MediaWiki\".\n\nParameter:\n* $1 - Holds the name of the subpages this message is used on.", "smw-admin-supplementary-operational-statistics-title": "This is a label of a link.", "smw-admin-supplementary-operational-statistics-short-title": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-supplementary-operational-statistics-intro": "A message explaining a link.\n\nParameter:\n* $1 - Holds {{msg-mw|Smw-admin-supplementary-operational-statistics-title}}", "smw-admin-supplementary-idlookup-title": "This is a label of a link.", "smw-admin-supplementary-idlookup-short-title": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-supplementary-idlookup-intro": "A message explaining a link.\n\nParameter:\n* $1 - Holds {{msg-mw|Smw-admin-supplementary-idlookup-title}}", "smw-admin-supplementary-duplookup-title": "This is a label of a link.", "smw-admin-supplementary-duplookup-intro": "A message explaining a link.\n\nParameter:\n* $1 - Holds {{msg-mw|Smw-admin-supplementary-duplookup-title}}", "smw-admin-supplementary-duplookup-docu": "This is an informatory message explaining the content of a special page.", "smw-admin-supplementary-duplookup-helplink": "{{Optional}}\n\nThis is a link in a tooltip pointing to a help page. Only change the link in case a dedicated page in your language exists on <Semantic-MediaWiki.org> website.", "smw-admin-supplementary-operational-statistics-cache-title": "This is the label of a link on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-operational-statistics-cache-intro": "This is an item shown on special page \"Semantic MediaWiki\"\n\nParameters:\n* Holds a link to a subpage. The lable of the link is defined with {{Msg-mw|Smw-admin-supplementary-operational-statistics-cache-title}}", "smw-admin-supplementary-operational-table-statistics-title": "This is the title of a subpage to special page \"SemanticMediaWiki\".", "smw-admin-supplementary-operational-table-statistics-short-title": "This is a section header on special page \"Semantic MediaWiki\". Translate table in the sense of a datatable in a database.", "smw-admin-supplementary-operational-table-statistics-intro": "This message explains a supbage to special page \"SemanticMediaWiki\".\n\nParamters:\n* $1 - Holds the name of the subpage as translated with {{Msg-mw|Smw-admin-supplementary-operational-table-statistics-title}}", "smw-admin-supplementary-operational-table-statistics-explain": "This is an explanatory message informing about the purpose of the resprective subpage to special page \"SemanticMediaWiki\".", "smw-admin-supplementary-operational-table-statistics-legend": "This is an explanatory message used on a subpage to special page \"Semantic MediaWiki\".", "smw-admin-supplementary-operational-table-statistics-legend-general": "This is an explanatory message used on a subpage to special page \"Semantic MediaWiki\".", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "This is an informatory message explaining statistics shown.", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "This is an explanatory message used on a subpage to special page \"Semantic MediaWiki\".", "smw-admin-supplementary-elastic-title": "{{Optional}}\nThis is the label of a link on special page \"SemanticMediaWiki\".\n\nElasticsearch is the name of a [https://www.elastic.co/elasticsearch back-end search engine]. It does ''not'' use “CamelCase”.", "smw-admin-supplementary-elastic-section-subtitle": "This is a section header on special page \"Semantic MediaWiki\".\n\nElasticsearch is the name of a [https://www.elastic.co/elasticsearch back-end search engine]. It does ''not'' use “CamelCase”.", "smw-admin-supplementary-elastic-intro": "This is an informatory message that describes the link accessible via $1.\n\nParameters:\n* $1 - holds the name specified in {{Msg-mw|Smw-admin-supplementary-elastic-title}}", "smw-admin-supplementary-elastic-docu": "This is an informatory message describing a section on special page \"SemanticMediawiki\"\n\nElasticsearch is the name of a [https://www.elastic.co/elasticsearch back-end search engine]. It does ''not'' use “CamelCase”.", "smw-admin-supplementary-elastic-functions": "This is a section header on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-settings-title": "This is the label of a link on special page \"SemanticMediaWiki\".\n\n{{Identical|Settings}}", "smw-admin-supplementary-elastic-settings-intro": "This is an informatory message that describes the link accessible via $1.\n\nParameters:\n* $1 - holds the name specified in {{Msg-mw|Smw-admin-supplementary-elastic-settings-title}}", "smw-admin-supplementary-elastic-mappings-title": "This is the label of a link on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-mappings-intro": "This is an informatory message that describes the link accessible via $1.\n\nParameters:\n* $1 - holds the name specified in {{Msg-mw|Smw-admin-supplementary-elastic-mappings-title}}", "smw-admin-supplementary-elastic-mappings-docu": "This is an informatory message on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-mappings-docu-extra": "This is an informatory message on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-mappings-summary": "This is a section header on special page \"SemanticMediaWiki\".\n\n{{Identical|Summary}}", "smw-admin-supplementary-elastic-mappings-fields": "This is a section header on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-nodes-title": "This is the label of a link on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-nodes-intro": "This is an informatory message that describes the link accessible via $1.\n\nParameters:\n* $1 - holds the name specified in {{Msg-mw|Smw-admin-supplementary-elastic-nodes-title}}", "smw-admin-supplementary-elastic-indices-title": "This is the label of a link on special page \"SemanticMediaWiki\".\n\n{{Identical/Index}}", "smw-admin-supplementary-elastic-indices-intro": "This is an informatory message that describes the link accessible via $1.\n\nParameters:\n* $1 - holds the name specified in {{Msg-mw|Smw-admin-supplementary-elastic-indices-title}}", "smw-admin-supplementary-elastic-statistics-title": "This is the label of a link on special page \"SemanticMediaWiki\".\n\n{{Identical|Statistics}}", "smw-admin-supplementary-elastic-statistics-intro": "This is an informatory message that describes the link accessible via $1.\n\nParameters:\n* $1 - holds the name specified in {{Msg-mw|Smw-admin-supplementary-elastic-statistics-title}}", "smw-admin-supplementary-elastic-statistics-docu": "This is an informatory message describing a subpage on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-status-replication": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-supplementary-elastic-status-last-active-replication": "This is a label.\n\nParameter:\n* $1 - holds a timestamp", "smw-admin-supplementary-elastic-status-refresh-interval": "This is a label.\n\nParameter:\n* $1 - holds a timestamp", "smw-admin-supplementary-elastic-status-recovery-job-count": "This is an item label. \n\nParameter:\n* $1 - holds the number of jobs", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "This is an item label.\n\nParameters:\n* $1 - holds the number of jobs", "smw-admin-supplementary-elastic-status-rebuild-lock": "This is an item message. \n\nParameter:\n* $1 - holds the number of jobs", "smw-admin-supplementary-elastic-status-replication-monitoring": "This is a label.\n\nParameter:\n\n* $1 - holds a status incicator", "smw-admin-supplementary-elastic-replication-header-title": "This is the label of a subpage to special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-replication-function-title": "This is the label of a link on special page \"SemanticMediaWiki\". See {{Msg-mw|Smw-admin-supplementary-elastic-replication-intro}}", "smw-admin-supplementary-elastic-replication-intro": "This is a message on special page \"SemanticMediaWiki\" describing the purpose of a linked page.\n\nParameter:\n* $1 - holds the label of the link. See {{Msg-mw|Smw-admin-supplementary-elastic-replication-function-title}}", "smw-admin-supplementary-elastic-replication-docu": "This is an infomatory message on special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-replication-files-docu": "This is an informatory message.", "smw-admin-supplementary-elastic-replication-files": "This is a section label on a subpage to special page \"SemanticMediaWiki\".", "smw-admin-supplementary-elastic-replication-pages": "This is a section label on a subpage to special page \"SemanticMediaWiki\".\n\n{{Identical|Page}}", "smw-admin-supplementary-elastic-endpoints": "This is a section header on special page \"Semantic MediaWiki\".", "smw-admin-supplementary-elastic-no-connection": "This is an error message.", "smw-list-count": "This is an informatory message.\n\nParameter:\n* $1 - holds the number of entries", "smw-property-label-uniqueness": "This is an informatory message.\n\nParameters:\n* $1 - Holds the name of the property label", "smw-property-label-similarity-title": "This is the section title on a special page.", "smw-property-label-similarity-intro": "This is an link desctiption.\n\nParameter:\n* $1 - Holds the lable of a page and links to it", "smw-property-label-similarity-threshold": "This is the label of an input field.\n{{Identical|Threshold}}", "smw-property-label-similarity-type": "This is the label of a checkbox input field.", "smw-property-label-similarity-noresult": "This is an informatory message shown if the report shows no results.", "smw-property-label-similarity-docu": "This is the informatory message explaining the purpose of the special page reporing on the similarity of property labels.\n\nParameter:\n* $1 - holds the name of a property", "smw-admin-operational-statistics": "This is an informatory message.", "smw_adminlinks_datastructure": "This is a section header on [[Special:AdminLinks]] which is needed for the integration of the [[mw:Extension:Admin_Links|Admin Links extension]] into this extension.", "smw_adminlinks_displayingdata": "This is a section header on [[Special:AdminLinks]] which is needed for the integration of the [[mw:Extension:Admin_Links|Admin Links extension]] into this extension.", "smw_adminlinks_inlinequerieshelp": "This is the link text for a link on [[Special:AdminLinks]] which is needed for the integration of the [[mw:Extension:Admin_Links|Admin Links extension]] into this extension.", "smw-page-indicator-usage-count": "This is an informatory message shown on property pages.\n\nParameter:\n* $2 holds the number from the usage count", "smw-property-indicator-type-info": "This informatory message is shown on property pages. It informs if the property is either user defined (=User) or provided by the software (=System).", "smw-property-indicator-last-count-update": "This informatory message is shown in a tooltip incicating the number of data values assigned to a property.\n\nParameter:\n* $1 - Holds the timestamp of the last update", "smw-concept-indicator-cache-update": "This is the content of a tooltip on a concept page.\n\nParameter:\n* $1 - holds the timestamp of the update", "smw-createproperty-isproperty": "This an information message at the top on a page in namespace \"Property\". Parameters:\n* $1 holds the name of the data type which was assigned to the respective property.", "smw-createproperty-allowedvals": "This an information message at the top on a page in namespace \"Property\" which gets inserted in case only certain property values are [https://semantic-mediawiki.org/wiki/Help:Special_property_Allows_value allowed to be used] for the respective property.\n\nParameters:\n* $1 - number of allowed values", "smw-paramdesc-category-delim": "This is the description of the \"delim\" parameter of the \"category\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-category-template": "This is the description of the \"template\" parameter of the \"category\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-paramdesc-category-userparam": "This is the description of the \"userparam\" parameter of the \"category\" [https://semantic-mediawiki.org/wiki/Help:Result_formats result format] for [https://semantic-mediawiki.org/wiki/Help:Inline_queries inline queries].", "smw-info-par-message": "This is the description of the \"message\" parameter of the parser function \"[https://semantic-mediawiki.org/wiki/Help:Adding_tooltips #info]\".", "smw-info-par-icon": "This is the description of the \"icon\" parameter of the parser function \"[https://semantic-mediawiki.org/wiki/Help:Adding_tooltips #info]\".\n\n{{doc-important|Do not translate the possible parameter values \"info\" and \"warning\".}}", "prefs-smw": "This is the text of a section header on [[Special:Preferences#mw-prefsection-smw|Special:Preferences]].\n{{Identical|Semantic MediaWiki}}", "prefs-general-options": "This is a section header so special page \"Preferences\".", "prefs-ask-options": "This is the text of a header within a section on [[Special:Preferences#mw-prefsection-smw|Special:Preferences]].", "smw-prefs-intro-text": "This is a user preference intro text on special page \"Preferences\".", "smw-prefs-ask-options-tooltip-display": "This is the text describing a user option on [[Special:Preferences#mw-prefsection-smw|Special:Preferences]].", "smw-prefs-ask-options-compact-view-basic": "This is a preference option on special page \"Preferences\"", "smw-prefs-help-ask-options-compact-view-basic": "This is an informative message explaining a preference option on special page \"Preferences\"", "smw-prefs-general-options-time-correction": "This is a message describing a user preference option selectable via special page \"Preferences\".\n\n“Time offset” should be translated as in {{msg-mw|Prefs-timeoffset}}.", "smw-prefs-general-options-jobqueue-watchlist": "This is a message describing a user preference option selectable via special page \"Preferences\".", "smw-prefs-help-general-options-jobqueue-watchlist": "This is an informative message explaining a preference option on special page \"Preferences\".", "smw-prefs-general-options-disable-editpage-info": "This is a message describing a user preference option selectable via special page \"Preferences\". Relates to information shown only when editing a page.", "smw-prefs-general-options-disable-search-info": "This message describes a user preference option on special page \"Preferences\".", "smw-prefs-general-options-suggester-textinput": "This is the description of a user preference option selectable on special page \"Preferences\".", "smw-prefs-help-general-options-suggester-textinput": "This is an informative message explaining a preference option on special page \"Preferences\". Input context means accessible from respective input fields.", "smw-prefs-general-options-show-entity-issue-panel": "This message describes a user preference option on special page \"Preferences\".\n\n{{Msg-mw|Smw-entity-examiner-indicator}}", "smw-prefs-help-general-options-show-entity-issue-panel": "This is a message describing a user preference option selectable via special page \"Preferences\". Relates to checks and information shown when viewing a page.\n\n{{Msg-mw|Smw-entity-examiner-indicator}}", "smw-prefs-factedsearch-profile": "This is a message describing a user preference option selectable via special page \"Preferences\"", "smw-ui-tooltip-title-property": "A label that is displayed on the info tooltip.\n{{Identical|Property}}", "smw-ui-tooltip-title-quantity": "A label that is displayed on the info tooltip.", "smw-ui-tooltip-title-info": "A label that is displayed on the info tooltip.\n{{Identical|Information}}", "smw-ui-tooltip-title-service": "A label that is displayed on the info tooltip.", "smw-ui-tooltip-title-warning": "A label that is displayed on the info tooltip.\n{{Identical|Warning}}", "smw-ui-tooltip-title-error": "A label that is displayed on the info tooltip.\n\n{{Identical|Error}}", "smw-ui-tooltip-title-parameter": "A label that is displayed on the info tooltip.\n{{Identical|Parameter}}", "smw-ui-tooltip-title-event": "A label that is displayed on the info tooltip.\n{{Identical|Event}}", "smw-ui-tooltip-title-note": "A label that is displayed on the info tooltip.\n{{Identical|Note}}", "smw-ui-tooltip-title-legend": "This is the name of a label.\n{{Identical|Legend}}", "smw-ui-tooltip-title-reference": "{{Identical|Reference}}", "smw_unknowntype": "Error message shown for properties that have a type unknown to the system.\n\nParameter:\n* $1 - holds the name of the datatype", "smw-concept-cache-text": "This is the message displays information about the cache status.\n\nParameters:\n* $1 - number of pages\n* $2 - date\n* $3 - time", "smw_concept_header": "This is the header on a page in namespace \"Concept\". Parameters:\n* $1 holds the name of the respective concept.", "smw_conceptarticlecount": "This is the introductory message below the header on pages in namespace \"Concept\". Parameters:\n* $1 holds the number of pages displayed in the current view.", "smw-qp-empty-data": "An error message shown for insufficient data.", "right-smw-admin": "{{doc-right|smw-admin}}", "right-smw-patternedit": "{{doc-right|smw-patternedit}}", "right-smw-pageedit": "{{doc-right|smw-pageedit}}", "right-smw-viewjobqueuewatchlist": "{{doc-right|smw-viewjobqueuewatchlist}}", "restriction-level-smw-pageedit": "This is an informatory message displaying the protection parameters on \"Special:ProtectedPages\". It is not possible to know the exact number of eligible users.", "action-smw-patternedit": "{{doc-action|smw-patternedit}}", "action-smw-pageedit": "{{doc-action|smw-pageedit}}", "group-smwadministrator": "{{doc-group|smwadministrator|group}}", "group-smwadministrator-member": "{{doc-group|smwadministrator|member}}", "grouppage-smwadministrator": "{{doc-group|smwadministrator|page}}", "group-smwcurator": "{{doc-group|smwcurator|group}}", "group-smwcurator-member": "{{doc-group|smwcurator|member}}", "grouppage-smwcurator": "{{doc-group|smwcurator|page}}", "action-smw-admin": "{{doc-action|smw-admin}}", "action-smw-ruleedit": "{{doc-action|smw-ruleedit}}", "smw-property-predefined-default": "This message is the default introductory text for [https://semantic-mediawiki.org/wiki/Help:Special_properties special (predefined) properties].\n\nParameter:\n* $1 - Name of the invoked predefined property\n* $2 - Name of the datatype of predefined property", "smw-property-predefined-common": "This informatory message explains what [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] is.", "smw-property-predefined-ask": "This informatory message describes a [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-asksi": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding the [https://semantic-mediawiki.org/wiki/Help:Special_property_Query_size size] of a query.\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-askde": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding the [https://semantic-mediawiki.org/wiki/Help:Special_property_Query_depth depth] of a query.\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-long-askde": "This informatory message describes the value added to a [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].", "smw-property-predefined-askpa": "This informatory message describes a [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-long-askpa": "This is an informatory message.", "smw-sp-properties-docu": "Extended introductory text for the [[Special:Properties]] page.", "smw-sp-properties-cache-info": "Information about the current cache status on a special page\n\nParameters:\n* $1 - Accommodates a user formatted date", "smw-sp-properties-header-label": "A header label", "smw-admin-settings-docu": "This is an informatory message used as an introductory text for an action that may be performed via special page \"[[Special:SMWAdmin]]\".\n\nPreceded by the heading {{msg-mw|Smw-sp-admin-settings-title}}.", "smw-sp-admin-settings-button": "A button text.\n\nPreceded by introduction:\n* {{msg-mw|Smw-sp-admin-settings-docu}}", "smw-admin-idlookup-title": "A header title", "smw-admin-idlookup-docu": "This is an informatory message used as an introductory text for an action that may be performed via special page \"[[Special:SMWAdmin]]\".\n\nPreceded by the heading {{msg-mw|Smw-sp-admin-idlookup-title}}.", "smw-admin-iddispose-title": "This is a section header on special page \"Special:SMWAdmin\"", "smw-admin-iddispose-docu": "This is an informatory message used as an introductory text for an action that may be performed via special page \"[[Special:SMWAdmin]]\".\n\nPreceded by the heading {{msg-mw|Smw-admin-iddispose-title}}.", "smw-admin-iddispose-done": "This is an informatory message after an action was performed.\n\nParameter.:\n* $1 holds the ID of an object", "smw-admin-iddispose-references": "This is an informatory message.\n\nParameters:\n* $1 holds the ID of an object\n* $2 holds the number of references", "smw-admin-iddispose-references-multiple": "This is an introductiory message preceeding a table of results.", "smw-admin-iddispose-no-references": "This is an informatory message on special page \"SemanticMediaWiki\".\n\nParameter:\n* $1 holds the ID of an object", "smw-admin-idlookup-input": "This is a field label. Please translate as a noun.\n{{Identical|Search}}", "smw-admin-objectid": "A label\n{{Identical|ID}}", "smw-admin-tab-general": "This is the label of a tab.\n{{Identical|Overview}}", "smw-admin-tab-notices": "This is the label of a tab.", "smw-admin-tab-maintenance": "This is the label of a tab on special page \"Semantic MediaWiki\".", "smw-admin-tab-supplement": "This is the label of a tab.", "smw-admin-tab-registry": "This is the label of a tab.\n\n{{Identical|Registry}}", "smw-admin-deprecation-notice-section": "This is a section header on special page \"Semantic MediaWiki\". This is the name of the extension. Usually it is not translated.", "smw-admin-configutation-tab-settings": "This is the name of a tab on special page \"SemanticMediaWiki\". Translate in a sense of configuration settings.\n\n{{Identical|Settings}}", "smw-admin-configutation-tab-namespaces": "This is the name of a tab on special page \"SemanticMediaWiki\".\n{{Identical|Namespace}}", "smw-admin-maintenance-tab-tasks": "This is the name of a tab on special page \"SemanticMediaWiki\". Translate in a sense of maintenance tasks.", "smw-admin-maintenance-tab-scripts": "This is the name of a tab on special page \"SemanticMediaWiki\".", "smw-admin-maintenance-no-description": "This is an informatory message.", "smw-admin-maintenance-script-section-title": "This is a section header on special page \"SemanticMediaWiki\".", "smw-admin-maintenance-script-section-intro": "This is an informatory text on special page \"SemanticMediaWiki\".", "smw-admin-maintenance-script-description-dumprdf": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-rebuildconceptcache": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-rebuilddata": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-rebuildelasticindex": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "This is an informatory message on special page \"SemanticMediaWiki\".", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-removeduplicateentities": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-setupstore": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-updateentitycollation": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-populatehashfield": "This is the short description of a maintenance script.", "smw-admin-maintenance-script-description-purgeentitycache": "This is a message on special page \"SemanticMediaWiki\".", "smw-admin-maintenance-script-description-updatequerydependencies": "This is an informatory message on special page \"SemanticMediaWiki\".", "smw-admin-maintenance-script-section-update": "This is a header on special page \"Semantic MediaWiki\".\n{{doc-important|This should be translated as \"scripts for updating\", NOT as \"update the scripts\".}}", "smw-admin-maintenance-script-section-rebuild": "This is a header on special page \"Semantic MediaWiki\".\n{{doc-important|This should be translated as \"Scripts for rebuilding\", NOT as \"Rebuild the scripts\".}}", "smw-livepreview-loading": "{{Identical|Loading}}Message displayed while data is loading.", "smw-sp-searchbyproperty-description": "Extended description text for the [[Special:SearchByProperty]] page.", "smw-sp-searchbyproperty-resultlist-header": "A header label", "smw-sp-searchbyproperty-nonvaluequery": "A description for a query result without values.\n\nParameters:\n* $1 - Name of the property", "smw-sp-searchbyproperty-valuequery": "A description for a query result with values.\n\nParameters:\n* $1 - Name of the property\n* $2 - Name of the value", "smw-datavalue-number-textnotallowed": "This is an error/warning message.\n\nParameters:\n* $1 holds the text causing the error/warning.\n* $2 holds the property value supposed to be used.", "smw-datavalue-number-nullnotallowed": "This is an error/warning message.\n\nParameter:\n* $1 holds the text causing the error/warning.", "smw-editpage-annotation-enabled": "A description to be displayed on the edit page.", "smw-editpage-annotation-disabled": "A description to be displayed on the edit page.", "smw-editpage-property-annotation-enabled": "A description to be displayed on the edit page.", "smw-editpage-property-annotation-disabled": "A description to be displayed on the edit page.", "smw-editpage-concept-annotation-enabled": "A description to be displayed on the edit page.", "smw-search-syntax-support": "This is an informatory message on special page \"Search\".", "smw-search-input-assistance": "This is an informatory message on special page \"Search\".", "smw-search-help-intro": "This informatory message is part of a large pop-up on special page \"Search\".", "smw-search-help-structured": "This informatory message is part of a large pop-up on special page \"Search\".", "smw-search-help-proximity": "This informatory message is part of a large pop-up on special page \"Search\".", "smw-search-help-ask": "This informatory message is part of a large pop-up on special page \"Search\".\n\nSee {{msg-mw|Smw-ask-help}}", "smw-search-input": "This is a section header in a large pop up on \"Special:Search\".", "smw-search-help-input-assistance": "This informatory message is part of a large pop-up on special page \"Search\".\n\nSee the following messages for inspiration:\n* {{Msg-mw|smw-ask-condition-input-assistance}}\n* {{Msg-mw|smw-ask-condition-input-assistance-property}}\n* {{Msg-mw|smw-ask-condition-input-assistance-category}}\n* {{Msg-mw|smw-ask-condition-input-assistance-concept}}", "smw-search-syntax": "This is a section header in a large pop up on \"Special:Search\".\n{{Identical|Syntax}}", "smw-search-profile": "This is the label of a tab on special page \"Search\".", "smw-search-profile-tooltip": "This is an informatory message provided via a tooltip.", "smw-search-profile-sort-best": "This is the label of a drop-down menu item on special page \"Search\" and an item in a tooltip.", "smw-search-profile-sort-recent": "This is the label of a drop-down menu item on special page \"Search\" and an item in a tooltip.", "smw-search-profile-sort-title": "This is the label of a drop-down menu item on special page \"Search\" and an item in a tooltip.\n\n{{Identical|Title}}", "smw-search-profile-extended-help-intro": "This is an informatory message shown in a tooltip on special page \"Search\".", "smw-search-profile-extended-help-sort": "This is an informatory message shown in a tooltip on special page \"Search\".", "smw-search-profile-extended-help-sort-title": "This is an informatory message shown in a tooltip on special page \"Search\".\n\nNote that the translation of the item should match {{Msg-mw|Smw-search-profile-sort-title}}.", "smw-search-profile-extended-help-sort-recent": "This is an informatory message shown in a tooltip on special page \"Search\".\n\nNote that the translation of the item should match {{Msg-mw|Smw-search-profile-sort-recent}}.", "smw-search-profile-extended-help-sort-best": "This is an informatory message shown in a tooltip on special page \"Search\".\n\nNote that the translation of the item should match {{Msg-mw|Smw-search-profile-sort-best}}.", "smw-search-profile-extended-help-form": "This is an informatory message shown in a tooltip on special page \"Search\".\n\nParameters:\n* $1 - holds a link", "smw-search-profile-extended-help-namespace": "This is an informatory message explaining namespace selection on the cheat sheet for special page \"Search\".", "smw-search-profile-extended-help-search-syntax": "This is an informatory message introducing several search parameters on the cheat sheet for special page \"Search\".", "smw-search-profile-extended-help-search-syntax-simplified-in": "This is an informatory message explaining a search parameter on the cheat sheet for special page \"Search\".", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "This is an informatory message explaining a search parameter on the cheat sheet for special page \"Search\".", "smw-search-profile-extended-help-search-syntax-simplified-has": "This is an informatory message explaining a search parameter on the cheat sheet for special page \"Search\".", "smw-search-profile-extended-help-search-syntax-simplified-not": "This is an informatory message explaining a search parameter on the cheat sheet for special page \"Search\".", "smw-search-profile-extended-help-search-syntax-prefix": "This is an informatory message explaining a search parameter on the cheat sheet for special page \"Search\".\n\nParameter:\n* $1 - holds the name of a search parameter", "smw-search-profile-extended-help-search-syntax-reserved": "This is an informatory message explaining a search parameter on the cheat sheet for special page \"Search\".\n\nParameter:\n* $1 - holds the name of a search parameter", "smw-search-profile-extended-help-search-syntax-note": "This is an informatory message about search parameters on the cheat sheet for special page \"Search\".", "smw-search-profile-extended-help-query": "This is an informatory message.\n\nParameter:\n* $1 - holds the code of the query.", "smw-search-profile-extended-help-query-link": "This message links to a help page. \n\nParameter:\n* $1 - holds the link to the help page. The caption of the link is specified in message {{Msg-mw|Smw-search-profile-link-caption-query}}", "smw-search-profile-extended-help-find-forms": "This the label for a link shown in a tooltip on special page \"Search\". See {{Msg-mw|Smw-search-profile-extended-help-form}} for the message holding the link.", "smw-search-profile-extended-section-sort": "This is the label of a drop-down menu on special page \"Search\" and a section header in a tooltip.", "smw-search-profile-extended-section-form": "This is the label of a drop-down menu on special page \"Search\" and a section header in a tooltip.", "smw-search-profile-extended-section-search-syntax": "This is a label on special page \"Search\".", "smw-search-profile-extended-section-namespace": "This is the label of a drop-down menu on special page \"Search\" and a section header in a tooltip.\n{{Identical|Namespace}}", "smw-search-profile-extended-section-query": "This is the label of a button on special page \"Search\" / section \"Extended\"", "smw-search-profile-link-caption-query": "This is the caption of a link.", "smw-search-show": "This is the label of a button on special page \"Search\" / section \"Extended\"\n\n{{Identical|Show}}", "smw-search-hide": "This is the label of a button on special page \"Search\" / section \"Extended\"\n\n{{Identical|Hide}}", "log-name-smw": "A name for the [[Special:Log]] Semantic MediaWiki entry.", "log-show-hide-smw": "This is the name for an entry added by Semantic MediaWiki to special page \"[[Special:Log]]\".\nParameters:\n* $1 - one of {{msg-mw|Show}} or {{msg-mw|Hide}}\n{{Related|Log-show-hide}}", "logeventslist-smw-log": "Semantic MediaWiki log option label on [[Special:Log]]", "log-description-smw": "A description for Semantic MediaWiki entries displayed in [[Special:Log]].", "logentry-smw-maintenance": "This is the message for an entry added by Semantic MediaWiki to special page \"[[Special:Log]]\".", "smw-datavalue-import-unknown-namespace": "This is an error message.\n\nParameter:\n* $1 holds the name of the namespace.", "smw-datavalue-import-missing-namespace-uri": "This is an error message.\n\nParameter:\n* $1 holds the name of the namespace.", "smw-datavalue-import-missing-type": "This is an error message show to the user during [https://semantic-mediawiki.org/wiki/Help:Import_vocabulary the import of an vocabulary]\n\nParameters:\n* $1 - name of the property to be imported\n* $2 - number of vocabulary to be imported", "smw-datavalue-import-link": "This is an informatory message shown to the user during [https://semantic-mediawiki.org/wiki/Help:Import_vocabulary the import of an vocabulary]\n\nParameters:\n* $1 - name of the vocabulary", "smw-datavalue-import-invalid-value": "This is an error message shown to the user during [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary the import of an vocabulary].\n\nParameter:\n* $1 holds the name of erroneous namespace identifier combination", "smw-datavalue-import-invalid-format": "This is an error message shown to the user during [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary the import of an vocabulary].\n\nParameter:\n* $1 holds the erroneous string", "smw-property-predefined-impo": "This informatory message describes the [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property] [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Imported_from imported from].\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-type": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding the [https://semantic-mediawiki.org/wiki/Help:Special_property_Has_type datatype] of a property.\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-sobj": "This informatory message shown in a tooltip describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding [https://semantic-mediawiki.org/wiki/Help:Special_property_Has_subobject subobjects]. Think of a subobject being an invisible wiki page which can only hold semantic annotations, i.e. assignments of values to properties.\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-long-sobj": "This informatory message shown on a property page describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding [https://semantic-mediawiki.org/wiki/Help:Special_property_Has_subobject subobjects]. Think of a subobject being an invisible wiki page which can only hold semantic annotations, i.e. assignments of values to properties.", "smw-property-predefined-errp": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding [https://semantic-mediawiki.org/wiki/Help:Special_property_Has_improper_value_for improper values].\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-long-errp": "This informatory message shown on a property page describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding [https://semantic-mediawiki.org/wiki/Help:Special_property_Has_improper_value_for improper values].", "smw-property-predefined-pval": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding [https://semantic-mediawiki.org/wiki/Help:Special_property_Allows_value allowed values]. Only allowed values may be saved without generating an error.\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-pvali": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] holding [https://semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list allowed values]. Only allowed values may be saved without generating an error.\n\nParameter:\n* $1 - name of predefined property", "smw-datavalue-property-restricted-annotation-use": "This is an error message.\n\nParameters:\n* $1 - holds the name of the property", "smw-datavalue-property-restricted-declarative-use": "This is an error message. Translate declarative in a sense of defining something.\n\nParameters:\n* $1 - holds the name of the property", "smw-datavalue-property-create-restriction": "This is an error message shown when am edit protected property page should be created.\n\nParameters:\n* $1 - Holds the name of property\n* $2 - Holds the name of the user right", "smw-datavalue-property-invalid-character": "This is an error message.\n\nParameters:\n* $1 holds the name of the property\n* $2 holds the invalid characters withing the name of the property", "smw-datavalue-property-invalid-chain": "This is an error message.\n\nParameter:\n* $1 - holds the name of the property chain", "smw-datavalue-restricted-use": "This is an error/warning message. This basically means that the datavalue may not be annotated.\n\nParameters:\n\n* $1 holds the datavalue causing the error/warning.", "smw-datavalue-invalid-number": "This is an error message shown to the user in case the stored value is not a number.", "smw-query-condition-circular": "This is an error/warning message.\n\nParameters:\n\n* $1 holds the name of the template causing the error/warning.", "smw-query-condition-empty": "This is an error message.", "smw-types-list": "A header", "smw-types-default": "This is an informatory message shown to the user.", "smw-types-help": "This is an informatory message shown to the user.", "smw-type-anu": "This is an informatory message shown to the user.", "smw-type-boo": "This is an informatory message shown to the user.", "smw-type-cod": "This is an informatory message shown to the user.", "smw-type-geo": "This is an informatory message shown to the user.", "smw-type-tel": "This is an informatory message shown to the user.", "smw-type-txt": "This is an informatory message shown to the user.", "smw-type-dat": "This is an informatory message shown to the user.", "smw-type-ema": "This is an informatory message on special page \"Types\".\n\nParameter:\n* $1 - holds the name of the datatype", "smw-type-tem": "This is an informatory message on special page \"Types\".\n\nParameter:\n* $1 - holds the name of the datatype", "smw-type-qty": "This is an informatory message on special page \"Types\".\n\nParameter:\n* $1 - holds the name of the datatype", "smw-type-rec": "This is an informatory message on special page \"Types\".\n\nParameter:\n* $1 - holds the name of the datatype", "smw-type-extra-tem": "This is an informatory message on special page \"Types\".", "smw-type-tab-properties": "This is the label of a tab on special page \"Types\".", "smw-type-tab-types": "This is the label of a tab on special page \"Types\". Translate types as in datatypes.\n{{Identical|Type}}", "smw-type-tab-type-ids": "This is the label of a tab on special page \"Datatypes\". Please translate consistent to {{Msg-mw|types}} and {{Msg-mw|smw-type-tab-types}}.\n\nThis means \"Identificators of types\"", "smw-type-tab-errors": "This is the label of a tab on special page \"Types\".", "smw-type-primitive": "This is the label of a tab on special page \"Types\".", "smw-type-contextual": "This is the label of a tab on special page \"Types\".", "smw-type-compound": "This is the label of a tab on special page \"Types\".", "smw-type-container": "This is a section header on special page \"Types\".", "smw-type-no-group": "This is a section header on special page \"Types\".", "smw-specials-browse-helplink": "{{notranslate}}", "smw-specials-bytype-helplink": "{{Optional}}\n\nThis message links to the respective help page on <semantic-mediawiki.org>.\n\nParameter:\n* Holds the string identifying the help page.", "smw-specials-types-helplink": "This message links to the respective help page on <semantic-mediawiki.org>.\n\n{{Optional}}", "smw-special-pageproperty-helplink": "{{Optional}}\n\nOnly change the URL if a dedicated page with translations into your language exist.", "smw-special-pageproperty-description": "This is an information message documenting special page \"PageProperty\".", "smw-property-predefined-errc": "This information message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] shown in a tooltip.", "smw-property-predefined-long-errc": "This information message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] on the property's page.", "smw-property-predefined-errt": "This information message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].", "smw-subobject-parser-invalid-naming-scheme": "This is an error message shown to the user in case a named identifier contains a dot (foo.bar).", "smw-datavalue-record-invalid-property-declaration": "This is an error message shown to the user.\n\n* $1 holds the name of the property of type record.", "smw-property-predefined-mdat": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-cdat": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-newp": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-ledt": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-mime": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-media": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-askfo": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-askst": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-askdu": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-asksc": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 holds the name of the special property.", "smw-property-predefined-askco": "This informatory message describes the [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property] holding the [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Query_status_code status code] of a query.\n\nParameter:\n\n* $1 - name of predefined property", "smw-property-predefined-long-askco": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] on the property's page. Translate \"internal codified\" in a sense of \"internally used representation of a state\" the query is in.", "smw-property-predefined-prec": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-attch-link": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 holds the name of the special property.", "smw-types-extra-geo-not-available": "This is an informatory message shown to the user.\n\n* $1 holds the name of the property.", "smw-datavalue-monolingual-dataitem-missing": "This is an error message shown to the user.", "smw-datavalue-monolingual-lcode-parenthesis": "{{optional}}", "smw-datavalue-languagecode-missing": "This is an error message.\n\n* $1 is the erroneous property value combination (=annotation) added by the user.", "smw-datavalue-languagecode-invalid": "This is an error message.\n\n* $1 is the erroneous language code added by the user.", "smw-property-predefined-lcode": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.\n\n[[wikipedia:en:IETF language tag|BCP47]] is an abbreviated language code (for example, en for English, pt-BR for Brazilian Portuguese, or nan-Hant-TW for Min Nan Chinese as spoken in Taiwan using traditional Han characters).", "smw-type-mlt-rec": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes datatype].\n\nParameter:\n* $1 holds the name of the datatype.", "smw-types-extra-mlt-lcode": "This is an error message shown to the user.", "smw-property-predefined-text": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-pdesc": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-list": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-limitreport-intext-parsertime": "This is an informatory message that extends the \"NewPP limit report\" generated by MediaWiki shown when viewing the raw HTML source of a generated page, e.g. [[Media:NewPP limit report swm.png|NewPP limit report swm]].\n\n{{doc-important|Do not translate: [SMW]}}", "smw-limitreport-intext-postproctime": "This is an informatory message that extends the \"NewPP limit report\" generated by MediaWiki shown when viewing the raw HTML source of a generated page, e.g. [[Media:NewPP limit report swm.png|NewPP limit report swm]].\n\n{{doc-important|Do not translate: [SMW]}}", "smw-limitreport-intext-parsertime-value": "This is an informatory message that extends the \"NewPP limit report\" generated by MediaWiki shown when viewing the raw HTML source of a generated page, e.g. [[Media:NewPP limit report swm.png|NewPP limit report swm]].\n\n* $1 holds the value of the seconds elapsed.\n\n{{Identical|Second}}", "smw-limitreport-intext-postproctime-value": "This is an informatory message that extends the \"NewPP limit report\" generated by MediaWiki shown when viewing the raw HTML source of a generated page, e.g. [[Media:NewPP limit report swm.png|NewPP limit report swm]].\n\n* $1 holds the value of the seconds elapsed.\n\n{{Identical|Second}}", "smw-limitreport-pagepurge-storeupdatetime": "This is an informatory message that extends the \"NewPP limit report\" generated by MediaWiki shown when viewing the raw HTML source of a generated page, e.g. [[Media:NewPP limit report swm.png|NewPP limit report swm]]. Store refers to the type of database backend used by the software, i.e. a SQL-store or RDF-store.\n\n{{doc-important|Do not translate: [SMW]}}", "smw-limitreport-pagepurge-storeupdatetime-value": "This is an informatory message that extends the \"NewPP limit report\" generated by MediaWiki shown when viewing the raw HTML source of a generated page, e.g. [[Media:NewPP limit report swm.png|NewPP limit report swm]].\n\n* $1 holds the value of the seconds elapsed (always plural).\n\n{{doc-important|Do not translate: [SMW]}}\n{{Identical|Second}}", "smw_allows_pattern": "Modifications are expected to occur on a local wiki only as this page represents a placeholder.", "smw-datavalue-allows-pattern-mismatch": "This is an error message.\n\nParameters:\n* $1 holds the property value\n* $2 holds the pattern", "smw-datavalue-allows-pattern-reference-unknown": "This is an error message.\n\nParameters:\n* $1 - holds the pattern string", "smw-datavalue-allows-value-list-unknown": "This is an error message.\n\nParameters:\n* $1 - holds the reference string", "smw-datavalue-allows-value-list-missing-marker": "This is an error message.\n\nParameter:\n* $1 - holds the name of the list", "smw-datavalue-feature-not-supported": "This is an error message.\n\nParameters:\n* $1 - holds the name of the feature", "smw-property-predefined-pvap": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-dtitle": "This informatory message describes the [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-pvuc": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] shown in a tooltip.", "smw-property-predefined-long-pvuc": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] on the property's page.", "smw-datavalue-constraint-uniqueness-violation": "This is an error message.\n\nParameters:\n* $1 holds the name of the property\n* $2 holds the data value assigned to the property\n* $3 holds the name of the page", "smw-datavalue-constraint-uniqueness-violation-isknown": "This is an error message.\n\nParameters:\n* $1 holds the name of the property\n* $2 holds the name of the page\n* $3 holds the data value assigned to the property", "smw-datavalue-constraint-violation-non-negative-integer": "This is an error message.\n\nParameters:\n* $1 holds the name of the property\n* $2 holds the data value assigned to the property", "smw-datavalue-constraint-violation-must-exists": "This is an error message.\n\n'''Note:''' The grammatically incorrect constraint name <code>must_exists</code> is in fact [https://github.com/SemanticMediaWiki/SemanticMediaWiki/pull/3981 implemented] as exactly that; do not correct it.\n\nParameters:\n* $1 holds the name of the property\n* $2 holds the data value assigned to the property", "smw-datavalue-constraint-violation-single-value": "This is an error message.\n\nParameters:\n* $1 holds the name of the property\n* $2 holds the data value assigned to the property", "smw-constraint-violation-must-exists": "'''Note:''' The grammatically incorrect constraint name <code>must_exists</code> is in fact [https://github.com/SemanticMediaWiki/SemanticMediaWiki/pull/3981 implemented] as exactly that; do not correct it.", "smw-constraint-violation-class-shape-constraint-missing-property": "This is an error message.\n\nParameter:\n* $1 - Holds the prefixed name of the category.\n* $2 - Holds the name of the property", "smw-constraint-violation-class-shape-constraint-wrong-type": "This is an error message.\n\nParameter:\n* $1 - Holds the prefixed name of the category.\n* $2 - Holds the name of the property\n* $3 - Holds the name of the datatype.", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "This is an error message.\n\nParameter:\n* $1 - Holds the prefixed name of the category.\n* $2 - Holds the name of the property\n* $3 - Holds the cardinality requirement as a number.", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "This is an error message.\n\nParameter:\n* $1 - Holds the prefixed name of the category.\n* $2 - Holds the name of the property\n* $3 - Holds the length requirement as a number.", "smw-constraint-violation-class-mandatory-properties-constraint": "This is an error message.\n\nParameter:\n* $1 - Holds the prefixed name of the category.\n* $2 - Holds the name of the property", "smw-property-predefined-boo": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes datatype].\n\nParameter:\n* $1 holds the name of the datatype.", "smw-property-predefined-num": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes datatype].\n\nParameter:\n* $1 holds the name of the datatype.", "smw-property-predefined-dat": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes datatype].\n\nParameter:\n* $1 holds the name of the datatype.", "smw-property-predefined-uri": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes datatype].\n\nParameter:\n* $1 holds the name of the datatype.", "smw-property-predefined-qty": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes datatype].\n\nParameter:\n* $1 holds the name of the datatype.", "smw-datavalue-time-invalid-offset-zone-usage": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value", "smw-datavalue-time-invalid-values": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value\n* $2 holds the part of the value which could not be interpreted", "smw-datavalue-time-invalid-date-components-common": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value", "smw-datavalue-time-invalid-date-components-dash": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value", "smw-datavalue-time-invalid-date-components-empty": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value", "smw-datavalue-time-invalid-date-components-three": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value", "smw-datavalue-time-invalid-date-components-sequence": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value", "smw-datavalue-time-invalid-ampm": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value\n* $2 holds the incorrect part of the annotated value", "smw-datavalue-time-invalid-jd": "This is an error message:\n\nParameters:\n* $1 holds the incorrectly annotated value\n* $2 holds the incorrect output", "smw-datavalue-time-invalid-prehistoric": "This is an error message. \"prehistoric\" refers to the year before which we do not accept anything but year numbers and largely discourage calendar models.\n\nParameter:\n* $1 holds the incorrectly annotated value", "smw-datavalue-time-invalid": "This is an error message:\n\nParameters:\n* $1 holds the incorrectly annotated value\n* $2 holds the incorrect output", "smw-datavalue-external-formatter-uri-missing-placeholder": "This is an error message:\n\nParameter:\n* $1 holds the missing part of the annotated value\n\nThink of an \"Formatter URI\" as and URI containing a placeholder which needs to be filled with a value, e.g. https://id.ndl.go.jp/auth/ndlna/$1. So if $1 is missing the URI cannot be generated like e.g. https://id.ndl.go.jp/auth/ndlna/00564222. Then the user gets this message.", "smw-datavalue-external-formatter-invalid-uri": "This is an error message:\n\nParameter:\n* $1 holds the incorrectly annotated value", "smw-datavalue-external-identifier-formatter-missing": "This is an error message.\n\nAn URI needs to be generated somehow using the given value. If the external formatter URI, like https://id.ndl.go.jp/auth/ndlna/$1, is missing, the software does not know how to generate it using the value (e.g, \"00564222\") which should be filled into $1 placeholder. In the end, the software should come up with https://id.ndl.go.jp/auth/ndlna/00564222.", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "This is an error message.\n\nParameters:\n$1 - holds the name of the identifier\n$2 - holds the value stored for the identifier", "smw-datavalue-keyword-maximum-length": "This is an error message.\n\nParameter:\n* $1 - holds the maximum length for the characters (defined integer)", "smw-property-predefined-eid": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-property-predefined-peid": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property\n\nThink of an \"Formatter URI\" as and URI containing a placeholder which needs to be filled with a value, e.g. https://id.ndl.go.jp/auth/ndlna/$1. So if the external identifier is missing the URI cannot be constructed like e.g. https://id.ndl.go.jp/auth/ndlna/00564222. In this example the external identifierer is \"00564222\".", "smw-property-predefined-pefu": "This informatory message describes the [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.\n\n\nThink of an \"Formatter URI\" as and URI containing a placeholder which needs to be filled with a value, e.g. https://id.ndl.go.jp/auth/ndlna/$1. So if the formatter URI is missing the URI cannot be gernerated like e.g. https://id.ndl.go.jp/auth/ndlna/00564222. In this example the formatter URI is \"https://id.ndl.go.jp/auth/ndlna/$1\".", "smw-property-predefined-long-pefu": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property] shown in a tooltip.\n\nThink of an \"Formatter URI\" as and URI containing a placeholder which needs to be filled with a value, e.g. https://id.ndl.go.jp/auth/ndlna/$1. So if $1 (=placeholder) is missing the URI cannot be generated like e.g. https://id.ndl.go.jp/auth/ndlna/00564222.", "smw-type-eid": "This is an explanatory message.\n\nParameter:\n* $1 holds the name of a data type\n\nThink of an \"Formatter URI\" as and URI containing a placeholder which needs to be filled with a value, e.g. https://id.ndl.go.jp/auth/ndlna/$1. So if $1 is missing the URI cannot be generated like e.g. https://id.ndl.go.jp/auth/ndlna/00564222.", "smw-property-predefined-keyw": "This informatory message describes the [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property] and [https://www.semantic-mediawiki.org/wiki/Help:Datatype datatype] holding a keyword.\n\nParameter:\n* $1 - name of predefined property", "smw-type-keyw": "This is an explanatory message.\n\nParameter:\n* $1 - holds the name of the datatype", "smw-datavalue-stripmarker-parse-error": "This is an error message.\n\nParameter:\n* $1 holds the invalid value.", "smw-datavalue-parse-error": "This is an error message.\n\nParameter:\n* $1 holds the property value which was not interpretable.", "smw-datavalue-propertylist-invalid-property-key": "This is an error message.\n\nParameters:\n* $1 holds the name of the property list.\n* $2 holds the property key which was invalid.", "smw-datavalue-type-invalid-typeuri": "This is an error message.\n\nParameter:\n* $1 holds the invalid type.", "smw-datavalue-wikipage-missing-fragment-context": "This is an error message.\n\nParameter:\n* $1 holds the invalid value.", "smw-datavalue-wikipage-invalid-title": "This is an error message.\n\nParameter:\n* $1 holds the invalid value.", "smw-datavalue-wikipage-property-invalid-title": "This is an error message.\n\nParameters:\n* $1 - holds the name of the property\n* $2 - holds the invalid datavalue", "smw-datavalue-wikipage-empty": "This is an error message.\n\nParameter:\n* $1 holds the invalid value.", "smw-type-ref-rec": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes datatype].\n\nParameter:\n* $1 holds the name of the datatype.", "smw-datavalue-reference-outputformat": "{{optional}}", "smw-datavalue-reference-invalid-fields-definition": "This is an error message.", "smw-parser-invalid-json-format": "This is an error message.\n\nParameter:\n* $1 holds the error output.", "smw-property-preferred-title-format": "{{optional}}", "smw-property-preferred-label-language-combination-exists": "This is an error message.\n\nParameters:\n* $1 name of the property label used\n* $2 language of the property label\n* $3 name of the already existing property label in use", "smw-parse": "{{Notranslate}}", "smw-clipboard-copy-link": "A label that is displayed on the info tooltip.", "smw-property-userdefined-fixedtable": "This is an informatory message.", "smw-data-lookup": "This is an informatory message.", "smw-data-lookup-with-wait": "This is an informatory message.", "smw-no-data-available": "This is an informatory message.", "smw-property-req-violation-missing-fields": "This is an error message.\n\nParameters:\n* $1 - Holds the name of the property\n* $2 - Holds the name of the datatype", "smw-property-req-violation-multiple-fields": "This is an error message. The word \"type\" refers to datatype.\n\nParameters:\n* $1 - holds the name of a property\n* $2 - holds the name of a datatype", "smw-property-req-violation-missing-formatter-uri": "This is an error message.\n\nParameter:\n* $1 - Holds the name of the property", "smw-property-req-violation-predefined-type": "This is an error message.\n\nParameters:\n* $1 - Holds the name of the property\n* $2 - Holds the name of the datatype", "smw-property-req-violation-import-type": "This is an error message.\n\nParameter:\n* $1 - holds the name of the imported vacabulary", "smw-property-req-violation-change-propagation-locked-error": "This is an informatory message. Similar to\n\n{{Msg-mw|Smw-property-req-violation-change-propagation-locked-warning}}\n\nand\n\n{{Msg-mw|smw-property-req-violation-change-propagation-pending}}\n\nParameters:\n* $1 - holds the name of the property", "smw-property-req-violation-change-propagation-locked-warning": "This is an informatory message. Similar to\n\n{{Msg-mw|Smw-property-req-violation-change-propagation-locked-error}}\n\nand\n\n{{Msg-mw|smw-property-req-violation-change-propagation-pending}}\n\nParameters:\n* $1 - holds the name of the property", "smw-property-req-violation-change-propagation-pending": "This is an informatory message on a property page. Similar to\n\n{{Msg-mw|smw-property-req-violation-change-propagation-locked-error}}\n\nand\n\n{{Msg-mw|smw-property-req-violation-change-propagation-locked-warning}}\n\nParameters:\n* $1 - holds the number of jobs queued", "smw-property-req-violation-missing-maps-extension": "This is an error message.", "smw-property-req-violation-type": "This is an informatory message. type refers to datatype.", "smw-property-req-error-list": "This is a message preceeding a list of issues.", "smw-property-req-violation-parent-type": "This is an error message. type refers to datatype.\n\nParameters:\n* $1 - holds the name of a property\n* $2 - holds the name of a property", "smw-property-req-violation-forced-removal-annotated-type": "This is an informatory/error message.\n\nParameters:\n* $1 - holds the name of a property\n* $2 - holds the name of d datatype", "smw-change-propagation-protection": "This is an informatory message. Similar to:\n* {{Msg-mw|Smw-category-change-propagation-locked-error}},\n* {{Msg-mw|Smw-category-change-propagation-locked-warning}} and\n* {{Msg-mw|Smw-category-change-propagation-pending}}", "smw-category-change-propagation-locked-error": "This is an informatory message. Similar to\n\n{{Msg-mw|Smw-category-change-propagation-locked-warning}}\n\nand\n\n{{Msg-mw|smw-category-change-propagation-pending}}\n\nParameters:\n* $1 - holds the name of the category", "smw-category-change-propagation-locked-warning": "This is an informatory message. Similar to\n\n{{Msg-mw|smw-category-change-propagation-locked-error}}\n\nand\n\n{{Msg-mw|smw-category-change-propagation-pending}}\n\nParameters:\n* $1 - holds the name of the category", "smw-category-change-propagation-pending": "This is an informatory message on a category page. Similar to\n\n{{Msg-mw|Smw-category-change-propagation-locked-error}}\n\nand\n\n{{Msg-mw|smw-category-change-propagation-locked-warning}}\n\nParameters:\n* $1 - holds the number of jobs queued", "smw-category-invalid-value-assignment": "This is an error message.\n\nParameter:\n* $1 - holds the value of the erroneous entry", "protect-level-smw-pageedit": "This is one of the options that may be selected when performing a page protection (<code>action=protect</code>) via the graphical interface of the wiki.", "smw-create-protection": "This is an error message shown when am edit protected property page should be edited.\n\nParameters:\n* $1 - Holds the name of property\n* $2 - Holds the name of the user right", "smw-create-protection-exists": "This is an error message shown when am edit protected property page should be edited.\n\nParameters:\n* $1 - Holds the name of property\n* $2 - Holds the name of the user right", "smw-edit-protection": "This is an informatory message shown on every page which is edit protected via an annotation.\n\nParameter:\n* $1 - Holds the name of the user right", "smw-edit-protection-disabled": "This is an informatory message.\n\nParameter:\n* $1 - Holds the name of the special property", "smw-edit-protection-auto-update": "This is an informatory message shown after a page protection was performed.", "smw-edit-protection-enabled": "This is an informatory message shown with a tooltip telling that the page is edit protected via the mechanism provided by Semantic MediaWiki.", "smw-patternedit-protection": "This is an informatory message shown on every page which is edit protected via a software setting.", "smw-property-predefined-edip": "This informatory message shown in a tooltip describes the [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property] holding [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Is_edit_protected Is edit protected].\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-long-edip": "This informatory message describes the value added to a [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].", "smw-query-reference-link-label": "This is the label of a link.", "smw-format-datatable-emptytable": "This is an informatory message shown by the \"datatable\" format.", "smw-format-datatable-info": "This is an informatory message shown by the \"datatable\" format, e.g. \"Showing 1 to 12 of 27 entries.\"\n\n{{doc-important|Do not translate the placeholders <code>_START_</code>, <code>_END_</code> and <code>_TOTAL_</code>.}}", "smw-format-datatable-infoempty": "This is an informatory message shown by the \"datatable\" format.", "smw-format-datatable-infofiltered": "This is an informatory message shown by the \"datatable\" format. The placeholder holds the total number of results.\n\n{{doc-important|Do not translate the placeholder <code>_MAX_</code>.}}", "smw-format-datatable-infothousands": "This message holds the thousands separator used by the \"datatables\" format.\n\n{{Optional}}", "smw-format-datatable-lengthmenu": "This is an informatory message shown by the \"datatable\" format. The placeholder holds a drop-down field allowing to select the number of results to be shown, e.g. 10, 25, 50, etc.\n\n{{doc-important|Do not translate the placeholder <code>_MENU_</code>.}}", "smw-format-datatable-loadingrecords": "This is a progress message shown by the \"datatable\" format.\n\n{{Identical|Loading}}", "smw-format-datatable-processing": "This is a progress message shown by the \"datatable\" format.\n{{Identical|Processing}}", "smw-format-datatable-search": "This is a field label shown by the \"datatable\" format. Translate as a noun.\n{{Identical|Search}}", "smw-format-datatable-zerorecords": "This is an informatory message shown by the \"datatable\" format.", "smw-format-datatable-first": "This is a message used as a selector. Show the ... resultset.\n\n{{Identical|First}}", "smw-format-datatable-last": "This is a message used as a selector. Show the ... resultset.\n\n{{Identical|Last}}", "smw-format-datatable-next": "This is a message used as a selector. Show the ... resultset.\n\n{{Identical|Next}}", "smw-format-datatable-previous": "This is a message used as a selector. Show the ... resultset.\n\n{{Identical|Previous}}", "smw-format-datatable-sortascending": "This is a selection message shown by the \"datatable\" format.", "smw-format-datatable-sortdescending": "This is a selection message shown by the \"datatable\" format.", "smw-format-datatable-toolbar-export": "This is the label of a link.\n\n{{Identical|Export}}", "smw-format-list-separator": "Separator between entries in a result list. See 'sep' at https://www.semantic-mediawiki.org/wiki/Help:List_format#Parameters", "smw-format-list-property-separator": "Separator between the properties of a result entry. See 'propsep' at https://www.semantic-mediawiki.org/wiki/Help:List_format#Parameters", "smw-format-list-value-separator": "Separator between the values belonging to one property of a result entry. See 'valsep' at https://www.semantic-mediawiki.org/wiki/Help:List_format#Parameters", "smw-format-list-field-label-separator": "Separator between a property label and the property values, e.g. the : in 'LABEL: VALUE'", "smw-format-list-other-fields-open": "{{Optional}}\n\nSeparator opening the group of other values, e.g. the ( in 'MAIN VALUE (OTHER VALUES)'", "smw-format-list-other-fields-close": "{{Optional}}\n\nSeparator closing the group of other values, e.g. the ) in 'MAIN VALUE (OTHER VALUES)'", "smw-category-invalid-redirect-target": "This is an error message.\n\nParamters:\n* $1 - holds the name of the category", "smw-parser-function-expensive-execution-limit": "This is an explanatory message.", "smw-postproc-queryref": "This is an informatory message.", "apihelp-smwinfo-summary": "This is an informatory message describing and API module.", "apihelp-ask-summary": "This is an informatory message describing and API module.", "apihelp-askargs-summary": "This is an informatory message describing and API module.", "apihelp-browsebyproperty-summary": "This is an informatory message describing and API module.", "apihelp-browsebysubject-summary": "This is an informatory message describing and API module.", "apihelp-smwtask-summary": "This is an informatory message describing and API module.", "apihelp-smwbrowse-summary": "This is an informatory message describing an API module.", "apihelp-ask-parameter-api-version": "This is an informatory message for the API.", "apihelp-smwtask-param-task": "This is an informatory message documenting an API parameter.", "smw-api-invalid-parameters": "This is an error message emitted by an API module.\n\nParameter:\n* $1 - holds the name of the invalid parameter", "smw-parser-recursion-level-exceeded": "This is an error message.\n\nParameter:\n* $1 - holds the count of recursions (integer) detected by the software", "smw-property-page-list-count": "This is an informatory message on a property page.\n\nParameter:\n* $1 - holds the number of pages", "smw-property-page-list-search-count": "This is an informatory message on a property page.\n\nParameters:\n* $1 - holds the number of pages\n* $2 - holds the datavalue searched for", "smw-property-reserved-category": "This is the name of a reserved property name.\n\n{{Identical|Category}}", "smw-category": "This is a label. singular only.\n\n{{Identical|Category}}", "smw-datavalue-uri-invalid-scheme": "This is an error message.\n\nParameter:\n* $1 - holds the identifier of an Uniform Resource Identifier (URI) schema", "smw-datavalue-uri-invalid-authority-path-component": "This is an error message. Translate authority in a sense of \"responsible for/responsibility\".\n\nParameters:\n* $1 - holds a broken URI\n* $2 - holds the transgressing part of the broken URI", "smw-browse-property-group-title": "This is the name of a header on special page \"Browse\".", "smw-browse-property-group-label": "This is the label of an entity similar to special property which is shown er on special page \"Browse\".", "smw-browse-property-group-description": "This is the label of an entity similar to special property which is shown er on special page \"Browse\".", "smw-property-predefined-ppgr": "This informatory message describes the [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\n* $1 holds the name of the special property.", "smw-filter": "This is a label.\n{{Identical|Filter}}", "smw-section-expand": "This is a tooltip.", "smw-section-collapse": "This is a tooltip.", "smw-ask-format-help-link": "This is an informatory message.\n\nParameter:\n* $1 - holds the name of a format", "smw-help": "{{Identical|Help}}", "smw-cheat-sheet": "This is the label of a link opening a tooltip.", "smw-personal-jobqueue-watchlist": "This is the label of a link pointing to a help page.", "smw-personal-jobqueue-watchlist-explain": "This is an explanatory message shown in a tooltip.", "smw-property-predefined-label-skey": "This is a label.\n\n{{Identical|Sortkey}}", "smw-processing": "This is an informatory message.", "smw-loading": "This is a progress message.\n\n{{Identical|Loading}}", "smw-fetching": "This is a progress message.", "smw-preparing": "This is a progress message.", "smw-expand": "This is the label of a button.\n\n{{Identical|Expand}}", "smw-collapse": "This is the label of a button.\n\n{{Identical|Collapse}}", "smw-copy": "This is the label of a button.\n\n{{Identical|Copy}}", "smw-copy-clipboard-title": "This is the content of a tooltip.", "smw-jsonview-expand-title": "This is the content of a tooltip.", "smw-jsonview-collapse-title": "This is the content of a tooltip.", "smw-jsonview-search-label": "This is the label of an input field.", "smw-redirect-target-unresolvable": "This is an error message.\n\nParameter:\n* $1 - holds an error note.", "smw-types-title": "This is the item label shown on special page \"Datatype\".\n\nParameter:\n* $1 - holds the string of the configuration defining the rule formatter, e.g. \"LINK_FORMAT_RULE\"\n\n{{Identical|Type}}", "smw-schema-namespace-editcontentmodel-disallowed": "This is an error message.", "smw-schema-namespace-edit-protection": "This is an informatory message.", "smw-schema-error-title": "This is the label of a tab on a page in namespace \"smw/schema\".", "smw-schema-error-schema": "This is an error message.\n\nParameter:\n* $1 - holds the erroneous specification", "smw-schema-error-validation-file-inaccessible": "This is an error message.\n\nParameter:\n* $1 - Holds the name of the file.", "smw-schema-error-violation": "{{optional}}\nThis is an error message.\n\nParameters:\n* $1 - holds the schema name\n* $2 - holds the error", "smw-schema-error-type-missing": "This is an error message.", "smw-schema-error-type-unknown": "This is an error message.\n\nParameter:\n* $1 - holds the name of the invalid type", "smw-schema-error-json": "This is an error message.\n\nParameter:\n* $1 - Holds the error.", "smw-schema-error-input": "This is an error message in namespace \"smw/schema\".", "smw-schema-error-input-schema": "This is an error message in namespace \"smw/schema\".\n\nParameter:\n* $1 - Holds the name of the schema validation information.", "smw-schema-error-title-prefix": "This is an error message.\n\nParameter:\n* $1 - Holds the string representing the intended prefix.", "smw-schema-validation-error": "This is an error message in namespace \"smw/schema\".\n\nParameter:\n* $1 - Holds the name of the datatype.", "smw-schema-validation-schema-title": "This is a tab on a page in namesace \"smw/schema\".", "smw-schema-summary-title": "This is a tab on a page in namesace \"smw/schema\".", "smw-schema-title": "This is the label of a tab on a page in namespace \"smw/schema\".", "smw-schema-type-help-link": "This is the link on a page in namespace \"smw/schema\".\n\nParameter:\n* $1 - holds the string of the configuration defining the schema formatter, e.g. \"LINK_FORMAT_SCHEMA\"\n\n{{Notranslate}}\n{{Optional}}", "smw-schema-usage": "This is a tab on a page in namespace \"smw/schema\". Displays the number of annotations.", "smw-schema-type": "This is the label of a tab on a page in namespace \"smw/schema\". \n\n{{Identical|Type}}", "smw-schema-type-description": "This is a tab on a page in namesace \"smw/schema\".", "smw-schema-description": "This is a tab on a page in namesace \"smw/schema\".", "smw-schema-description-link-format-schema": "This is an informatory message shown on a type page in namespace \"smw/schema\".", "smw-schema-description-search-form-schema": "This is an informatory message shown on a type page in namespace \"smw/schema\".", "smw-schema-description-property-profile-schema": "This is an informatory message shown on a type page in namespace \"smw/schema\".", "smw-schema-description-facetedsearch-profile-schema": "This is an informatory message shown on a type page in namespace \"smw/schema\".", "smw-schema-description-property-group-schema": "This is an informatory message shown on a type page in namespace \"smw/schema\".", "smw-schema-description-property-constraint-schema": "This is an informatory message shown on a type page in namespace \"smw/schema\".", "smw-schema-description-class-constraint-schema": "This is an informatory message shown on a type page in namespace \"smw/schema\".", "smw-schema-tag": "This is the label of a Link to a special property on a page in namespace \"smw/schema\".", "smw-property-predefined-constraint-schema": "This informatory message describes a [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 - name of predefined property", "smw-property-predefined-schema-desc": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 holds the name of the special property.", "smw-property-predefined-schema-def": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 holds the name of the special property.", "smw-property-predefined-schema-tag": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 holds the name of the special property.", "smw-property-predefined-long-schema-tag": "This informatory message describes the value added to a special property.", "smw-property-predefined-schema-type": "This informatory message describes a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property].\n\nParameter:\n* $1 holds the name of the special property.", "smw-property-predefined-long-schema-type": "This informatory message describes the value added to a [https://semantic-mediawiki.org/wiki/Help:Special_properties special property].", "smw-ask-title-keyword-type": "This is a label.", "smw-ask-message-keyword-type": "This is an informatory message.\n\nParameter:\n* $1 holds the query condition", "smw-remote-source-unavailable": "This is an error message.\n\nParameters:\n* $1 - holds the name of the remote target", "smw-remote-source-disabled": "This is an error message.\n\nParameters:\n* $1 - holds the name of the remote target", "smw-remote-source-unmatched-id": "This is an error message.\n\nParameters:\n* $1 - holds the name of the remote target", "smw-remote-request-note": "This is an informatory message.\n\nParameters:\n* $1 - holds the name of the remote target", "smw-remote-request-note-cached": "This is an informatory message.\n\nParameters:\n* $1 - holds the name of the remote target", "smw-parameter-missing": "This is an error message emitted by an API module.\n\nParameter:\n* $1 - holds the name of the missing parameter", "smw-property-tab-usage": "This is the label of a tab on a property or concept page.", "smw-property-tab-redirects": "This is the label of a tab on a property page. \n\n{{Identical|Synonym}}", "smw-property-tab-subproperties": "This is the label of a tab on a property page.", "smw-property-tab-errors": "This is the label of a tab on a property page.", "smw-property-tab-constraint-schema": "This is the name of a tab. Translate in a sense of constraints regarding data schemas.", "smw-property-tab-constraint-schema-title": "This is the name of a tab. Translate in a sense of constraints regarding data schemas and compiled in a sense of created.", "smw-property-tab-specification": "This is the label of a tab on a property page or concept page.", "smw-concept-tab-list": "This is the label of a tab on a concept page. \n\n{{Identical|List}}", "smw-concept-tab-errors": "This is the label of a tab on a concept page. \n\n{{Identical|Error}}", "smw-ask-tab-result": "This is the label of a tab on special page \"Ask\".\n\n{{Identical|Result}}", "smw-ask-tab-extra": "This is the label of a tab on special page \"Ask\".", "smw-ask-tab-debug": "This is the label of a tab on special page \"Ask\".", "smw-ask-tab-code": "This is the label of a tab on special page \"Ask\".", "smw-helplink-concepts": "This message links to the respective help page on <semantic-mediawiki.org>.\n\n{{Optional}}", "smw-install-incomplete-tasks-title": "This is a section header on special page \"SemanticMediaWiki\".", "smw-install-incomplete-intro": "This is an error message.", "smw-pendingtasks-tab-setup": "This is the label of a tab on special page \"SemanticMediaWiki\".", "smw-install-incomplete-populate-hash-field": "This is an error/informatory message.", "smw-install-incomplete-elasticstore-indexrebuild": "This is a information on the software status page.", "smw-pendingtasks-setup-tasks": "This is the label of a tab on special page \"SemanticMediaWiki\".\n{{Identical|Task}}", "smw-helplink": "This is the URL formatter for an link to the homepage to Semantic MediaWiki.\n{{Optional}} \n\nor\n\n{{Notranslate}}", "smw-filter-count": "This is the name of a tab.", "smw-es-replication-check": "This is a header.", "smw-es-replication-error": "This is the label of a tooltip.", "smw-es-replication-file-ingest-error": "This is the label of a tooltip.", "smw-es-replication-maintenance-mode": "This is a label.", "smw-es-replication-error-missing-id": "This is an error message.\n\nParameters:\n* $1 - holds the name of the page\n* $2 - holds the ID of the page", "smw-es-replication-error-divergent-date": "This is an error message.\n\nParameters:\n* $1 - Holds the name of the page\n* $2 - Holds the identifier (number) of the page", "smw-es-replication-error-divergent-date-detail": "This is the content of a tooltip.\n\nParameter:\n* $1 - Holds the timestamp of the stored data \n* $2 - holds the timestamp of the stored data", "smw-es-replication-error-divergent-revision": "This is an error message.\n\nParameters:\n* $1 - holds the name of the page\n* $2 - holds the ID of the page", "smw-es-replication-error-divergent-revision-detail": "This is the content of a tooltip.\n\nParameter:\n* $1 - Holds the timestamp of the stored data \n* $2 - holds the timestamp of the stored data", "smw-es-replication-error-maintenance-mode": "This is information on the software status page.", "smw-es-replication-error-no-connection": "This is an error message.", "smw-es-replication-error-bad-request-exception": "This is an error message. Translate as HTTP-Status 400 \"Bad request\".", "smw-es-replication-error-suggestions": "This is an informatory message in a tooltip.", "smw-es-replication-error-suggestions-maintenance-mode": "This is an error message.", "smw-es-replication-error-suggestions-no-connection": "This is an error message.", "smw-es-replication-error-suggestions-exception": "This is an error message.", "smw-es-replication-error-file-ingest-missing-file-attachment": "This is an error message.\n\nParaameter:\n* $1 - this is the name of a page.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "This is an informatory message.", "smw-report": "This is a tab label on a subpage to special page \"SemanticMediaWiki\".\n\n{{Identical|Report}}", "smw-legend": "This is a tab label on a subpage to special page \"SemanticMediaWiki\".", "smw-datavalue-constraint-schema-category-invalid-type": "This is an error message.\n\nParameter:\n* $1 - Holds the name of the schema.\n* $2 - Holds the name of a schema type.", "smw-datavalue-constraint-schema-property-invalid-type": "This is an error message.\n\nParameter:\n* $1 - Holds the name of the schema.\n* $2 - Holds the name of a schema type.", "smw-entity-examiner-check": "An ''examiner'' refers to a process for entity examination. See https://www.semantic-mediawiki.org/wiki/Help:Entity_examinations", "smw-entity-examiner-deferred-elastic-replication": "This is a label. Translate as noun. Elastic as Elasticsearch.", "smw-entity-examiner-deferred-constraint-error": "This is a label.", "smw-entity-examiner-deferred-fake": "This is a label.", "smw-indicator-revision-mismatch": "This is a label.", "smw-facetedsearch-intro-text": "This is an introductory sentence on Special:FacetedSearch.", "smw-facetedsearch-intro-tips": "This is the text for a tooltip on Special:FacetedSearch.", "smw-facetedsearch-profile-label-default": "This is a label on Special:FacetedSearch.", "smw-facetedsearch-intro-tab-explore": "This is the label of a tab on Special:FacetedSearch.", "smw-facetedsearch-intro-tab-search": "This is the label of a tab on Special:FacetedSearch.", "smw-facetedsearch-explore-intro": "This is a note on Special:FacetedSearch.", "smw-facetedsearch-profile-options": "This is the label of on option on Special:FacetedSearch.", "smw-facetedsearch-size-options": "This is a label of on option on Special:FacetedSearch.", "smw-facetedsearch-order-options": "This is a label of on option on Special:FacetedSearch.", "smw-facetedsearch-format-options": "This is a label of on option on Special:FacetedSearch.", "smw-facetedsearch-format-table": "This is a label of a selectable item on Special:FacetedSearch.", "smw-facetedsearch-input-filter-placeholder": "This is a label of a placeholder text on Special:FacetedSearch.", "smw-facetedsearch-no-filters": "This is a text on Special:FacetedSearch.", "smw-facetedsearch-no-filter-range": "This is a text on Special:FacetedSearch.", "smw-facetedsearch-no-output": "This is a label on Special:FacetedSearch.\n\nParameter:\n* $1 - Holds the name of the format.", "smw-facetedsearch-clear-filters": "This is a label for a button on Special:FacetedSearch.", "smw-specials-facetedsearch-helplink": "This is a status text on Special:FacetedSearch.", "smw-search-placeholder": "This is a label on Special:FacetedSearch.", "smw-listingcontinuesabbrev": "Shown in continuation of each first letter group. This message follows the first letter.\n\nThis is an abbreviation of the English word \"continued\". If the word is short in your language, the full stop in the end is not necessary.", "smw-showingresults": "This message is used on some special pages such as [[Special:WantedCategories]]. Parameters:\n* $1 ― the maximum number of results in the batch shown, starting at the item indicated by the next parameter\n* $2 ― the number of the first item listed\n\nNote that numbers given in $1 and $2 are preformated (and may contain locale-specific group separators or digits), so they cannot be used in the 1st parameter for the <code><nowiki>{{</nowiki>PLURAL:''number''<nowiki>|</nowiki>''textual variants...''<nowiki>}}</nowiki></code> parser funtion, which only accepts raw numbers written with ASCII digits.\n\n{{Int:Seealso}}{{Colon}}\n* {{msg-mw|Showingresultsnum}}"}