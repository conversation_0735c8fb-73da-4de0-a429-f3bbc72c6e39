{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Alexmar983", "Beta16", "Candalua", "Champ0999", "Civvì", "Cruccone", "<PERSON><PERSON>", "<PERSON><PERSON>, <PERSON>", "<PERSON><PERSON>", "FRacco", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Gmelfi", "Kaspo", "Lorelai87", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Matteocng", "McDut<PERSON><PERSON>", "Melos", "Nemo bis", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "S4b1nuz E.656", "Sakretsu", "Samuele2002", "<PERSON>", "Statix64", "<PERSON><PERSON><PERSON><PERSON>", "Wikitoni", "Ximo17", "පසිඳු කාවින්ද", "아라"]}, "smw-desc": "Rende la tua wiki più accessibile - per le macchine ''e'' per gli umani ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentazione in linea])", "smw-error": "Errore", "smw-upgrade-progress": "Avanzamento", "smw-upgrade-error-title": "Errore » Semantic MediaWiki", "smw-upgrade-error-why-title": "Perché visualizzo questa pagina?", "smw-upgrade-error-how-title": "Come risolvo questo errore?", "smw-extensionload-error-why-title": "Perché visualizzo questa pagina?", "smw-extensionload-error-how-title": "Come risolvo questo errore?", "smw-upgrade-maintenance-title": "Manutenzione » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Perché visualizzo questa pagina?", "smw-semantics-not-enabled": "Le funzionalità di Semantic MediaWiki non sono abilitate per questo wiki.", "smw_viewasrdf": "Feed RDF", "smw_finallistconjunct": " e", "smw-factbox-head": "... altro su \"$1\"", "smw-factbox-facts": "<PERSON><PERSON>", "smw-factbox-facts-help": "Mostra dichiarazioni e dati che sono stati creati da un utente", "smw-factbox-attachments": "Allegati", "smw-factbox-attachments-value-unknown": "N/D", "smw-factbox-attachments-is-local": "È locale", "smw-factbox-attachments-help": "Mostra allegati disponibili", "smw-factbox-facts-derived": "<PERSON><PERSON> derivati", "smw-factbox-facts-derived-help": "Mostra dati che sono stati derivati da principi o con l'aiuto di altre tecniche di ragionamento", "smw_isspecprop": "Questa proprietà è una proprietà speciale all'interno di questo wiki.", "smw-concept-cache-header": "<PERSON><PERSON><PERSON><PERSON>", "smw-concept-cache-count": "La [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count cache di concetti] contiene {{PLURAL:$1|'''una''' entità|'''$1''' entità}} ($2).", "smw-concept-no-cache": "La cache non è disponibile.", "smw_concept_description": "Descrizione del concetto \"$1\"", "smw_no_concept_namespace": "I concetti possono essere definiti solo sulle pagine nel namespace Concept:.", "smw_multiple_concepts": "Ogni pagina concetto può avere una sola definizione di concetto.", "smw_concept_cache_miss": "Il concetto \"$1\" non può essere usato ora, siccome la configurazione della wiki richiede che sia elaborato off-line. Se il problema non si risolve dopo un po' di tempo, chiedi all'amministratore del tuo sito di rendere disponibile questo concetto.", "smw_noinvannot": "Il valore non può essere assegnato alla proprietà inversa.", "version-semantic": "Estensioni semantiche", "smw_baduri": "Spiacenti. Gli URI del tipo “$1” non sono consentiti.", "smw_printername_count": "Conta risultati", "smw_printername_csv": "Esportazione CSV", "smw_printername_dsv": "Esportazione DSV", "smw_printername_debug": "Debug della query (per utenti esperti)", "smw_printername_embedded": "Includi i contenuti della pagina", "smw_printername_json": "Esportazione JSON", "smw_printername_list": "Elenco", "smw_printername_plainlist": "Lista semplice", "smw_printername_ol": "Elenco numerato", "smw_printername_ul": "Elenco puntato", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON> estesa", "smw_printername_template": "Template", "smw_printername_rdf": "Esportazione RDF", "smw_printername_category": "Categoria", "validator-type-class-SMWParamSource": "testo", "smw-paramdesc-limit": "Il numero massimo di risultati da restituire", "smw-paramdesc-headers": "Mostra i nomi di header/proprietà", "smw-paramdesc-mainlabel": "L'etichetta da dare al nome della pagina principale", "smw-paramdesc-link": "Mostra i valori come link", "smw-paramdesc-intro": "<PERSON>o da mostrare prima dei risultati della query, se ce ne sono", "smw-paramdesc-outro": "<PERSON>o da mostrare dopo i risultati della query, se ce ne sono", "smw-paramdesc-default": "<PERSON>o da mostrare se la query non dà risultati", "smw-paramdesc-sep": "Il separatore tra i risultati", "smw-paramdesc-template": "Il nome di un template con cui mostrare quanto restituito", "smw-paramdesc-columns": "Il numero di colonne in cui ordinare la visualizzazione dei risultati", "smw-paramdesc-embedformat": "Il tag HTML usato per definire le intestazioni", "smw-paramdesc-embedonly": "Non mostrare intestazioni", "smw-paramdesc-rdfsyntax": "La sintassi RDF da utilizzare", "smw-paramdesc-csv-sep": "Specifica un separatore di colonna", "smw-paramdesc-csv-valuesep": "Specifica un separatore di valori", "smw-paramdesc-dsv-separator": "Il separatore da usare", "smw-paramdesc-dsv-filename": "Il nome per il file DSV", "smw-paramdesc-searchlabel": "Testo per continuare la ricerca ai risultati successivi", "smw-paramdesc-export": "Opzioni di esportazione", "smw-printername-feed": "Feed RSS e Atom", "smw-paramdesc-feedtype": "Tipo di feed", "smw-paramdesc-feedtitle": "Il testo da utilizzare come titolo del feed", "smw-paramdesc-feeddescription": "Il testo da utilizzare come descrizione del feed", "smw-paramdesc-feedpagecontent": "Il contenuto della pagina da visualizzare con il feed", "smw-label-feed-description": "Feed $2 $1", "smw_iq_disabled": "Le query semantiche sono state disabilitate per questo wiki.", "smw_iq_moreresults": "&hellip; risultati successivi", "smw_parseerror": "Il valore dato non è stato compreso.", "smw_notitle": "“$1” non pu&ograve; essere utilizzato come nome di una pagina all'interno di questo wiki.", "smw_wrong_namespace": "Qui sono consentite solo pagine del namespace \"$1\".", "smw_manytypes": "È stato definito più di un tipo per la proprietà.", "smw_emptystring": "Le stringhe vuote non sono accettate.", "smw_notinenum": "\"$1\" non è nell'elenco ($2) dei [[Property:Allows value|valori consentiti]] per la proprietà \"$3\".", "smw_noboolean": "“$1” non &egrave; riconosciuto come valore Booleano (vero/falso).", "smw_true_words": "vero,v,si,s,true,t,yes,y", "smw_false_words": "falso,f,no,n,false", "smw_nofloat": "“$1” non è un numero.", "smw_infinite": "I numeri grandi come \"$1\" non sono supportati.", "smw_novalues": "Nessun valore specificato", "smw_nodatetime": "Non &egrave; stato possibile comprendere la data “$1”.", "smw_toomanyclosing": "Sembrano esserci troppe ripetizioni di “$1” all'interno della query.", "smw_noclosingbrackets": "Alcune \"<nowiki>[[</nowiki>\" all'interno della query non sono state chiuse con le corrispondenti \"]]\".", "smw_misplacedsymbol": "Il simbolo “$1” &grave; stato usato in un punto in cui &egrave; inutile.", "smw_unexpectedpart": "Non &egrave; stato possibile comprendere la parte “$1” della query. Il risultato potrebbe essere diverso da quello atteso.", "smw_emptysubquery": "Qualche subquery ha una condizione non valida.", "smw_misplacedsubquery": "Qualche subquery &egrave; stata utilizzata in una posizione in cui non era consentito.", "smw_valuesubquery": "Le subquery non sono supportate per i valori della proprietà \"$1\".", "smw_badqueryatom": "Non è stato possibile comprendere parte \"<nowiki>[[…]]</nowiki>\" della query.", "smw_propvalueproblem": "Non è stato possibile comprendere il valore della proprietà \"$1\".", "smw_noqueryfeature": "Qualche funzionalità di query non è stata supportata in questa wiki e parte della query è stata rimossa ($1).", "smw_noconjunctions": "Le congiunzioni nelle query non sono supportate in questa wiki e parte della query è stata rimossa ($1).", "smw_nodisjunctions": "La disgiunzione all'interno delle query non &egrave; supportata in questo wiki, quindi parte della query &egrave; stata ignorata ($1).", "smw_querytoolarge": "{{PLURAL:$2|La seguente condizione|Le seguenti condizioni}} all'interno della query <code>$1</code> non {{PLURAL:$2|è stata considerata|sono state considerate}} a causa delle restrizioni di dimensione o profondità delle query impostate per questo wiki: <code>$1</code>.", "smw_notemplategiven": "Per favore fornisci un valore per il parametro \"template\" per far funzionare questo formato di query.", "smw_type_header": "Proprietà del tipo \"$1\"", "smw_typearticlecount": "Visualizzazione di $1 {{PLURAL:$1|proprietà|proprietà}} che usano questo tipo.", "smw_attribute_header": "Pagine che usano la proprietà \"$1\"", "smw_attributearticlecount": "Visualizzazione di $1 {{PLURAL:$1|pagina che usa|pagine che usano}} questa proprietà.", "smw-propertylist-subproperty-header": "Sottoproprietà", "smw-propertylist-redirect-header": "<PERSON>nimi", "specialpages-group-smw_group-maintenance": "Manutenzione", "exportrdf": "Esporta le pagine in RDF", "smw_exportrdf_docu": "Questa pagina consente di ottenere dati da una pagina in formato RDF. Per esportare delle pagine, inseritene i titoli nella casella di testo sottostante, un titolo per riga.", "smw_exportrdf_recursive": "Esporta ricorsivamente tutte le pagine correlate. Nota: il risultato potrebbe essere molto grande!", "smw_exportrdf_backlinks": "Esporta anche le pagine che si riferiscono a quelle esportate. Genera un RDF navigabile.", "smw_exportrdf_lastdate": "Non esportare le pagine che non hanno sub&igrave;to modifiche dal momento specificato.", "smw_exportrdf_submit": "Esporta", "uriresolver": "Risolutore di URI", "properties": "Proprietà", "smw-categories": "Categorie", "smw_properties_docu": "Le seguenti proprietà sono utilizzate all'interno del wiki.", "smw_property_template": "$1 di tipo $2 (usata $3 {{PLURAL:$3|volta|volte}})", "smw_propertylackspage": "Tutte le proprietà dovrebbero essere descritte da una pagina!", "smw_propertylackstype": "Non è stato specificato nessun tipo per questa proprietà (per il momento si suppone sia di tipo $1).", "smw_propertyhardlyused": "Questa proprietà non è quasi mai usata nel wiki!", "smw-special-property-searchform": "Mostra le proprietà che contengono:", "smw-special-property-searchform-inputinfo": "L'input distingue le maiuscole dalle minuscole e quando viene usato per filtrare, solamente le proprietà che soddisfano le condizioni vengono mostrate.", "smw-special-property-searchform-options": "Opzioni", "smw-special-wantedproperties-filter-label": "Filtro:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Non approvato.", "concepts": "<PERSON><PERSON><PERSON>", "smw-special-concept-header": "Elenco di concetti", "smw-special-concept-empty": "<PERSON><PERSON><PERSON> concetto trovato.", "unusedproperties": "Proprietà non utilizzate", "smw-unusedproperties-docu": "Questa pagina elenca le [https://www.semantic-mediawiki.org/wiki/Unused_properties proprietà inutilizzate] che esistono nonostante nessun'altra pagina ne faccia uso. Per una vista differenziale, vedi le pagine speciali [[Special:Properties|intere]] o [[Special:WantedProperties|proprietà richieste]].", "smw-unusedproperty-template": "$1 di tipo $2", "wantedproperties": "Proprietà senza descrizione", "smw-wantedproperties-docu": "Questa pagina elenca le [https://www.semantic-mediawiki.org/wiki/Wanted_properties proprietà richieste] che sono usate nel wiki ma non hanno ancora una pagina che le descriva. Per una vista differenziale, vedi le pagine speciali [[Special:Properties|intere]] o  [[Special:UnusedProperties|proprietà inutilizzate]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|uso|usi}})", "smw_purge": "Aggiorna", "types": "T<PERSON><PERSON>", "smw_types_docu": "Elenco dei [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tipi di dati  disponibili] con ogni [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipo] che rappresenta un unico insieme di attributi per descrivere un valore in termini di archiviazione e visualizzazione di caratteristiche che sono ereditari a una proprietà assegnata.", "smw-special-types-no-such-type": "\"$1\" è sconosciuto o non è stato specificato come tipo di dati valido.", "smw-statistics": "Statistiche semantiche", "smw-statistics-entities-total": "Entità (totali)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Proprietà}}]] (totale)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Proprietà}} (totale)", "smw-statistics-property-used": "Proprietà ({{PLURAL:$1|utilizzata|utilizzate}} con almeno un valore)", "smw-statistics-property-page": "Proprietà ({{PLURAL:$1|registrata|registrate}} con una pagina)", "smw-statistics-property-page-info": "Conta le proprietà che hanno una pagina e una descrizione dedicate.", "smw-statistics-property-type": "Proprietà ({{PLURAL:$1|assegnata|assegnate}} a un tipo di dati)", "smw-statistics-query-format": "formato <code>$1</code>", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|<PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON>}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|<PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON>}}]]", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Tipo|Tipi}} di dato]]", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities Entità {{PLURAL:$1|obsoleta|obsolete}}]", "smw_uri_doc": "Il risolutore di URI implementa il [$1 W3C TAG finding on httpRange-14].\nGarantisce che una rappresentazione RDF (per le macchine) o una pagina wiki (per gli esseri umani) venga fornita a seconda della richiesta.", "ask": "Ricerca semantica", "smw_ask_sortby": "Ordina per colonna (opzionale)", "smw_ask_ascorder": "<PERSON><PERSON>", "smw_ask_descorder": "Decrescente", "smw-ask-order-rand": "Casuale", "smw_ask_submit": "<PERSON><PERSON><PERSON>", "smw_ask_editquery": "Modifica query", "smw_add_sortcondition": "[Aggiungi condizione di ordinamento]", "smw_ask_hidequery": "Nascondi query (vista compatta)", "smw_ask_help": "Help sulle query", "smw_ask_queryhead": "Condizione", "smw_ask_printhead": "Selezione dei dati da mostrare", "smw_ask_printdesc": "(aggiungi un nome di proprietà per riga)", "smw_ask_format_as": "Formatta come:", "smw_ask_defaultformat": "predefinito", "smw_ask_otheroptions": "Altre opzioni", "smw_ask_show_embed": "Mostra codice di inclusione", "smw_ask_hide_embed": "Nascondi codice di inclusione", "smw_ask_embed_instr": "Per includere questa query in linea in una pagina wiki, usa il codice qui sotto.", "smw-ask-delete": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-sorting": "Ordinamento", "smw-ask-options": "Opzioni", "smw-ask-format-options": "Formato e opzioni", "smw-ask-parameters": "Parametri", "smw-ask-search": "Cerca", "smw-ask-debug": "Debug", "smw-ask-result": "Risultato", "smw-ask-empty": "<PERSON><PERSON><PERSON><PERSON> tutte le voci", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Aiuto con il formato selezionato: $1", "smw-ask-condition-input-assistance-property": "<code>p:</code> per recuperare i suggerimenti di proprietà (e.g. <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> per recuperare i suggerimenti di categoria", "smw-ask-condition-input-assistance-concept": "<code>con:</code> per recuperare i suggerimenti di categoria", "smw-ask-query-search-info": "La query <code><nowiki>$1</nowiki></code> è stata eseguita da {{PLURAL:$3|1=<code>$2</code> (dalla cache)|<code>$2</code> (dalla cache)|<code>$2</code>}} in $4 {{PLURAL:$4|secondo|secondi}}.", "smw-ask-extra-other": "Altro", "searchbyproperty": "Cerca per proprietà", "smw-missingredirects-list": "Pagine con annotazioni mancanti", "smw_sbv_docu": "Cerca tutte le pagine che hanno proprietà e valore specificati.", "smw_sbv_novalue": "Inserire un valore valido per la proprietà, o visualizzare tutti i valori di proprietà per \"$1\".", "smw_sbv_displayresultfuzzy": "Una lista di tutte le pagine che hanno la proprietà \"$1\" col valore \"$2\".\nSiccome ci sono solo pochi risultati, sono visualizzati anche i valori vicini.", "smw_sbv_property": "Proprietà:", "smw_sbv_value": "Valore:", "smw_sbv_submit": "<PERSON><PERSON><PERSON>", "browse": "Esplora il sito", "smw_browselink": "Sfoglia le proprietà", "smw_browse_article": "Inserire il nome della pagina da cui iniziare l'esplorazione", "smw_browse_go": "Vai", "smw_browse_show_incoming": "Mostra proprietà in entrata", "smw_browse_hide_incoming": "Nascondi proprietà in entrata", "smw_browse_no_outgoing": "Questa pagina non ha proprietà.", "smw_browse_no_incoming": "Nessuna proprietà ha collegamenti verso questa pagina.", "smw-browse-show-group": "Mostra gruppi", "smw-browse-hide-group": "Nascondi gruppi", "smw-noscript": "Questa pagina o azione richiede JavaScript per funzionare. Abilita JavaScript nel tuo browser o utilizza un browser in cui è supportato, così che la funzionalità possa essere utilizzata e sia a disposizione come richiesto. Per ulteriore assistenza, dai un'occhiata alla pagina di supporto [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 di", "smw_inverse_label_property": "Etichetta della proprietà inversa", "pageproperty": "Ricerca proprietà della pagina", "smw_pp_docu": "Inserisci una pagina e una proprietà o solo una proprietà per recuperare tutti i valori assegnati.", "smw_pp_from": "Da pagina:", "smw_pp_type": "Proprietà:", "smw_pp_submit": "<PERSON><PERSON><PERSON>", "smw-prev": "{{PLURAL:$1|precedente|precedenti $1}}", "smw-next": "{{PLURAL:$1|successivo|successivi $1}}", "smw_result_prev": "Precedente", "smw_result_next": "Successivo", "smw_result_results": "Risultati", "smw_result_noresults": "<PERSON><PERSON><PERSON> r<PERSON>.", "smwadmin": "Cruscotto Semantic MediaWiki", "smw-admin-statistics-semanticdata-overview": "Panoramica", "smw-admin-permission-missing": "L'accesso a questa pagina è stato bloccato a causa di autorizzazioni mancanti, consulta la pagina di aiuto sulle [https://www.semantic-mediawiki.org/wiki/Help:Permissions autorizzazioni] per i dettagli sulle impostazioni necessarie.", "smw-admin-setupsuccess": "Il motore di storage è stato installato.", "smw_smwadmin_return": "Torna a $1", "smw_smwadmin_updatestarted": "È iniziato un nuovo processo di aggiornamento per ricaricare i dati semantici.\nTutti i dati registrati saranno ricostruiti o riparati se necessario.\nPuoi seguire i progressi dell'aggiornamento in questa pagina speciale.", "smw_smwadmin_updatenotstarted": "Un processo di aggiornamento è già in corso.\nNon ne verrà avviato un altro.", "smw_smwadmin_updatestopped": "<PERSON>tti i processi di aggiornamento in corso sono stati arrestati.", "smw_smwadmin_updatenotstopped": "Per arrestare il processo di aggiornamento in corso, devi cliccare nel riquadro per confermare che sei davvero sicuro di volerlo fare.", "smw-admin-docu": "Questa pagina speciale serve ad aiutarti durante l'installazione, aggiornamento, manutenzione e l'utilizzo di <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a> e fornirti anche ulteriori funzioni ed attività amministrative, nonché statistiche.\nRicordati di fare un backup dei dati importanti prima di eseguire funzioni amministrative.", "smw-admin-environment": "Ambiente software", "smw-admin-db": "Configurazione database", "smw-admin-dbdocu": "Semantic MediaWiki richiede la propria struttura di database (ed è indipendente da MediaWiki, quindi non influisce sul resto dell'installazione di MediaWiki) per archiviare i dati semantici.\nQuesta funzione di impostazione può essere rieseguita più volte senza conseguenze indesiderate, ma è richiesta solo una volta all'atto dell'installazione o dell'aggiornamento.", "smw-admin-permissionswarn": "Se l'operazione avrà esito negativo con errori SQL, l'utente del database usato dal tuo wiki (controlla il tuo file \"LocalSettings.php\") probabilmente non ha permessi sufficienti.\nPuoi assegnare a questo utente permessi aggiuntivi per creare e cancellare tabelle, oppure inserire temporaneamente nel file \"LocalSettings.php\" i dati di accesso root al database, oppure usare lo script di manutenzione <code>setupStore.php</code>, che può utilizzare le credenziali di un utente amministratore.", "smw-admin-dbbutton": "Inizializza o aggiorna tabelle", "smw-admin-announce": "Annuncia il tuo wiki", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> è stato sostituito da <code>$2</code>", "smw-admin-deprecation-notice-title-notice": "Impostazioni obsolete", "smw-admin-deprecation-notice-title-removal": "Impostazioni rimosse", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw_smwadmin_datarefresh": "Ricostruzione dati", "smw_smwadmin_datarefreshdocu": "Si possono ripristinare tutti i dati Semantic MediaWiki basati sui contenuti attuali del wiki.\nQuesto può essere utile per riparare dati corrotti o per aggiornare i dati se il loro formato interno si è modificato per qualche aggiornamento di software.\nL'aggiornamento è eseguito pagina per pagina e non sarà completato immediatamente.\nQui di seguito si vede se un aggiornamento è in corso e potrai avviare od arrestare gli aggiornamenti (a meno che questa funzionalità non sia stata disattivata dall'amministratore del sito).", "smw_smwadmin_datarefreshprogress": "<strong>Un aggiornamento è già in corso.</strong>\nNon è anormale che l'aggiornamento proceda solo lentamente, poiché verifica i dati solo a piccoli pacchetti ogni volta che un utente accede al wiki.\nPer terminare il processo pià rapidamente, puoi invocare lo script di manutenzione MediaWiki <code>runJobs.php</code> (usa l'opzione <code>--maxjobs 1000</code> per restringere il numero di verifiche svolte in ciascuna tornata).\nProgresso stimato dell'aggiornamento in corso:", "smw_smwadmin_datarefreshbutton": "Pianifica ricostruzione dati", "smw_smwadmin_datarefreshstop": "Ferma questo aggiornamento", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, sono {{GENDER:$1|sicuro|sicura}}.", "smw-admin-support": "<PERSON><PERSON><PERSON>", "smw-admin-supportdocu": "Diverse risorse sono fornite per esserti d'aiuto in caso di problemi:", "smw-admin-installfile": "Se hai problemi con la tua installazione, inizia a verificare le linee guida nel <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">file INSTALL</a> e la <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">pagina di installazione</a>.", "smw-admin-smwhomepage": "La documentazione utente completa per Semantic MediaWiki si trova in <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Gli errori possono essere segnalati sul <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">tracker di anomalie</a>, la pagina <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">riportare errori</a> fornisce alcune guide su come scrivere una segnalazione effettiva.", "smw-admin-questions": "Se hai altre domande o suggerimenti, unisciti alle discussioni sulla <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">mailing list</a> degli utenti di Semantic MediaWiki.", "smw-admin-other-functions": "Altre funzioni", "smw-admin-statistics": "Statistiche", "smw-admin-supplementary-section-title": "Funzioni supplementari", "smw-admin-supplementary-settings-title": "Configurazione e impostazioni", "smw-admin-supplementary-settings-intro": "<u>$1</u> mostra i parametri che definiscono il comportamento di Semantic MediaWiki", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> numero totale di righe in una tabella", "smw-admin-supplementary-elastic-version-info": "Versione", "smw-admin-supplementary-elastic-functions": "Funzioni supportate", "smw-admin-supplementary-elastic-settings-title": "Impostazioni (indici)", "smw-admin-supplementary-elastic-nodes-title": "Nodi", "smw-admin-supplementary-elastic-statistics-title": "Statistiche", "smw-admin-supplementary-elastic-status-refresh-interval": "Intervallo di aggiornamento: $1", "smw-admin-supplementary-elastic-replication-files": "File", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-config": "Configurazioni", "smw-property-label-similarity-threshold": "Soglia:", "smw_adminlinks_datastructure": "Struttura dei dati", "smw_adminlinks_displayingdata": "Visualizzazione dati", "smw_adminlinks_inlinequerieshelp": "<PERSON><PERSON> sulle query inline", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Numero di utilizzi] stimati: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Proprietà definita da {{PLURAL:$1|utente|sistema}}", "smw-createproperty-isproperty": "È una proprietà di tipo $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Il valore permesso per questa proprità è|I valori permessi per questa proprietà sono}}:", "smw-paramdesc-category-delim": "Il delimitatore", "smw-info-par-message": "Messaggio da visualizzare.", "prefs-general-options": "Opzioni generali", "prefs-ask-options": "Ricerca semantica", "smw-prefs-general-options-disable-search-info": "Disabilita le informazioni del supporto sintattico sulla pagina di ricerca standard", "smw-ui-tooltip-title-property": "Proprietà", "smw-ui-tooltip-title-quantity": "Conversione unità", "smw-ui-tooltip-title-info": "Informazione", "smw-ui-tooltip-title-service": "Collegamento di servizio", "smw-ui-tooltip-title-warning": "Attenzione", "smw-ui-tooltip-title-error": "Errore", "smw-ui-tooltip-title-parameter": "Parametro", "smw-ui-tooltip-title-event": "Evento", "smw-ui-tooltip-title-note": "<PERSON>a", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "<PERSON>a", "smw_unknowntype": "Il tipo \"$1\" di questa proprietà non è valido", "smw_concept_header": "<PERSON><PERSON>e del concetto \"$1\"", "smw_conceptarticlecount": "Mostro {{PLURAL:$1|una pagina|$1 pagine}}.", "right-smw-patternedit": "Modifica accesso per mantenere le espressioni regolari e i modelli consentiti (Semantic MediaWiki)", "right-smw-pageedit": "Modifica l'accesso per <code>Is edit protected</code> nelle pagine di annotazione (Semantic MediaWiki)", "action-smw-patternedit": "modificare le espressioni regolari utilizzate da Semantic MediaWiki", "group-smwadministrator": "Amministratori (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|amministratore|amministratrice|amministratore/trice}} (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Amministratori (Semantic MediaWiki)", "group-smwcurator": "Curatori (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|curatore|curatrice|curatore/trice}} (Semantic MediaWiki)", "grouppage-smwcurator": "{{ns:project}}:Curatori (Semantic MediaWiki)", "group-smweditor": "Editori (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|editore|editrice|editore/trice}} (Semantic MediaWiki)", "grouppage-smweditor": "{{ns:project}}:Editor<PERSON> (Semantic MediaWiki)", "smw-property-predefined-ask": "\"$1\" è una proprietà predefinita che rappresenta meta informazioni (sotto forma di [https://www.semantic-mediawiki.org/wiki/Subobject sottoggetto]) su singole interrogazioni ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "\"$1\" è una proprietà predefinita che contiene il numero delle condizioni usate in una interrogazione ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "\"$1\" è una proprietà predefinita che informa sulla profondità di un'interrogazione ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-sp-properties-header-label": "Elenco delle proprietà", "smw-admin-iddispose-title": "Smaltimento", "smw-admin-iddispose-docu": "Da notare che l'operazione di smaltimento non ha restrizioni e rimuove l'entità dal motore di archiviazione insieme a tutti i suoi riferimenti nelle tabelle, se confermata. Avvia questa attività con '''cautela''' e solo dopo aver consultato la [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentazione].", "smw-admin-idlookup-input": "Ricerca:", "smw-admin-objectid": "ID:", "smw-admin-tab-maintenance": "Manutenzione", "smw-admin-tab-registry": "Registro", "smw-admin-tab-alerts": "Avvisi", "smw-admin-maintenancealerts-invalidentities-alert-title": "Entità non valide", "smw-admin-configutation-tab-settings": "Impostazioni", "smw-admin-configutation-tab-namespaces": "Namespace", "smw-admin-configutation-tab-schematypes": "Tipi schema", "smw-admin-maintenance-no-description": "Nessuna descrizione.", "smw-livepreview-loading": "Caricamento in corso...", "smw-sp-searchbyproperty-resultlist-header": "Elenco dei risultati", "smw-datavalue-number-nullnotallowed": "\"$1\" ha restituito un \"NULL\", che non è consentito come numero.", "smw-search-help-intro": "Un <code><nowiki>[[ ... ]]</nowiki></code> input sarà segnalato al processore di input per usare la ricerca finale del Semantic MediaWiki. Si dovrà notare che la combinazione <code><nowiki>[[ ... ]]</nowiki></code> con una ricerca di testo non strutturato come <code><nowiki>[[ ... ]] O Lorem ipsum</nowiki></code> non è supportato.", "smw-search-help-structured": "Ricerche strutturate:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (come [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context filtro sul contesto])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (con un [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context query del contesto])", "smw-search-input": "Inserisci e cerca", "smw-search-syntax": "Sin<PERSON><PERSON>", "smw-search-profile": "<PERSON><PERSON><PERSON>", "smw-search-profile-tooltip": "Cerca le funzioni in connessione con Semantic MediaWiki", "smw-search-profile-sort-title": "<PERSON><PERSON>", "smw-search-profile-extended-help-search-syntax-reserved": "* Alcune espressioni sono riservate come: <nowiki>$1</nowiki>", "smw-search-profile-extended-section-sort": "Ordina per", "smw-search-profile-extended-section-namespace": "Namespace", "smw-search-show": "Mostra", "smw-search-hide": "Nascondi", "log-name-smw": "Semantic MediaWiki", "log-show-hide-smw": "$1 registro Semantic MediaWiki", "logeventslist-smw-log": "Semantic MediaWiki", "smw-property-predefined-impo": "\"$1\" è una proprietà predefinita che descrive una relazione ad un [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabolario importato] ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "\"$1\" è una proprietà predefinita che descrive il [[Special:Types|tipo  di dati]] di una proprietà ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "\"$1\" è una proprietà predefinita che rappresenta un costrutto [https://www.semantic-mediawiki.org/wiki/Help:Container contenitore] ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "Il contenitore consente di accumulare assegnazioni proprietà-valore simili a quelli di una normale pagina wiki, ma all'interno di una diversa entità di spazio, pur essendo collegati al soggetto incorporato .", "smw-property-predefined-errp": "\"$1\" è una proprietà predefinita che traccia gli errori di inserimento per valori di annotazioni irregolari ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] è una proprietà predefinita che può definire una lista di valori ammissibili per limitare assegnazioni di valori per una proprietà ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-create-restriction": "La proprietà \"$1\" non esiste e l'utente non possiede il premesso \"$2\" (vedi [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode authority mode]) per creare o annotare valori o proprietà non approvate.", "smw-datavalue-restricted-use": "Il valore dei dati \"$1\" è stato contrassegnato per l'uso limitato.", "smw-datavalue-invalid-number": "\"$1\" non può essere interpretato come un numero.", "smw-query-condition-circular": "Una possibile condizione circolare è stata rilevata in \"$1\".", "smw-types-list": "Elenco dei tipi di dati", "smw-types-help": "Ulteriori informazioni ed esempi sono riportati su questa [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 pagina di aiuto].", "smw-type-anu": "\"$1\" è una variante del tipo di dati [[Special:Types/URL|URL]] ed è in gran parte utilizzata per una dichiarazione di esportazione \"owl:AnnotationProperty\".", "smw-type-tel": "\"$1\" è un particolare tipo di dati utilizzato per descrivere i numeri di telefono internazionali secondo la RFC 3966.", "smw-type-dat": "\"$1\" è un tipo di dati base impiegato per rappresentare punti nel tempo in un formato unificato.", "smw-type-ema": "\"$1\" è un tipo di dati speciale per rappresentare una email.", "smw-type-tab-properties": "Proprietà", "smw-type-tab-types": "T<PERSON><PERSON>", "smw-type-tab-errors": "<PERSON><PERSON><PERSON>", "smw-type-primitive": "Base", "smw-type-contextual": "Contestuale", "smw-type-no-group": "Non classificato", "smw-property-predefined-errc": "\"$1\" è una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] che rappresenta errori che appaiono in connessioni con valori di annotazioni o processi di inserimento impropri.", "smw-property-predefined-long-errc": "Gli errori sono raccolti in un [https://www.semantic-mediawiki.org/wiki/Help:Container contenitore] che può includere un riferimento alla proprietà che ha causato la discrepanza.", "smw-property-predefined-errt": "\"$1\" è una proprietà predefinita che contiene una descrizione testuale di un errore ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mdat": "\"$1\" è una proprietà predefinita che corrisponde alla data dell'ultima modifica ad un soggetto ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "\"$1\" è una proprietà predefinita che corrisponde alla data della prima versione di un soggetto ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "\"$1\" è una proprietà predefinita che indica se un soggetto è nuovo o no ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "\"$1\" è una proprietà predefinita che contiene il nome della pagina dell'utente che ha creato l'ultima versione ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "\"$1\" è una proprietà predefinita che descrive il tipo MIME di un file caricato ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "\"$1\" è una proprietà predefinita che descrive il tipo di media di un file caricato ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "\"$1\" è una proprietà predefinita che contiene il nome del formato del risultato usato in una interrogazione ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "\"$1\" è una proprietà predefinita che descrive le condizioni di un'interrogazione come stringa ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "\"$1\" è una proprietà predefinita che contiene il tempo (in secondi) che è stato richiesto per completare l'esecuzione di un'interrogazione ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-prec": "\"$1\" è una proprietà predefinita che descrive una [https://www.semantic-mediawiki.org/wiki/Help:Display_precision precisione di visualizzazione] (in cifre decimali) per tipi di dati numerici.", "smw-types-extra-geo-not-available": "L'[https://www.semantic-mediawiki.org/wiki/Extension:Maps estensione \"Maps\"] non è stata rilevata, pertanto \"$1\" è limitato nella sua capacità di operare.", "smw-datavalue-languagecode-invalid": "\"$1\" non è stato riconosciuto come un codice di lingua supportato.", "smw-property-predefined-lcode": "\"$1\" è una proprietà predefinita che rappresenta un codice di lingua in formato BCP47 ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "\"$1\" è un tipo di dati [https://www.semantic-mediawiki.org/wiki/Help:Container contenitore] che associa un valore testuale con un specifico [[Property:Language code|codice lingua]].", "smw-types-extra-mlt-lcode": "Il tipo di dati {{PLURAL:$2|richiede|non richiede}} un codice per la lingua (cioè {{PLURAL:$2|un'annotazione di un valore senza un codice per la lingua non è accetatta|un'annotazione di un valore senza un codice per la lingua è accetatta}}).", "smw-property-predefined-text": "\"$1\" è una proprietà predefinita che rappresenta un testo di lunghezza arbitraria ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "\"$1\" è una proprietà predefinita che consente di descrivere una proprietà in contesto di una lingua ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "\"$1\" è una proprietà predefinita per definire un elenco di proprietà utilizzate con una proprietà tipo [[Special:Types/Record|record]] ed è fornito da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|secondo|secondi}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|secondo|secondi}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|secondo|secondi}}", "smw_allows_pattern": "Questa pagina dovrebbe contenere un elenco di riferimenti (se<PERSON><PERSON> da [https://it.wikipedia.org/wiki/Espressione_regolare espressioni regolari]) disponibile alla proprietà [[Property:Allows pattern|modello consentito]]. Per modificare questa pagina, è necessario il diritto <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "\"$1\" è stato classificato come non valido dall'espressione regolare \"$2\".", "smw-datavalue-allows-pattern-reference-unknown": "Il modello di riferimento \"$1\" non può aver corrispondenza ad un elemento in [[MediaWiki:Smw allows pattern]].", "smw-datavalue-feature-not-supported": "La funzionalità \"$1\" non è supportata o è stata disattivata su questo wiki.", "smw-property-predefined-pvap": "\"$1\" è una proprietà predefinita che può specificare un [[MediaWiki:Smw allows pattern|modello di riferimento]] da applicare alle corrispondenze delle [https://it.wikipedia.org/wiki/Espressione_regolare espressioni regolari] ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "\"$1\" è una proprietà predefinita che può assegnare un titolo da visualizzare diverso ad un'entità ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "\"$1\" è una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per limitare i valori assegnabili a ciascuna istanza ad essere unici (o uno al massimo).", "smw-property-predefined-long-pvuc": "L'unicità è stabilita quando i due valori non sono uguali nella loro rappresentazione letterale, e qualsiasi violazione di questo vincolo sarà classificato come errore.", "smw-datavalue-constraint-uniqueness-violation": "La proprietà \"$1\" consente l'assegnazione sono di valori unici, e ''$2'' è stato già annotato nel soggetto \"$3\".", "smw-property-predefined-boo": "\"$1\" è un [[Special:Types/Boolean|tipo]] e una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per rappresentare valori booleani.", "smw-property-predefined-num": "\"$1\" è un [[Special:Types/Number|tipo]] e una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per rappresentare valori numerici.", "smw-property-predefined-dat": "\"$1\" è un [[Special:Types/Date|tipo]] e una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per rappresentare valori di date.", "smw-property-predefined-uri": "\"$1\" è un [[Special:Types/URL|tipo]] e una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per rappresentare valori di URI/URL.", "smw-property-predefined-qty": "\"$1\" è un [[Special:Types/Quantity|tipo]] e una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per rappresentare valori di quantità.", "smw-datavalue-time-invalid-date-components-common": "\"$1\" contiene alcune informazioni non interpretabili.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" contiene alcuni componenti vuoti.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" contiene più di tre componenti necessari per un'interpretazione della data.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" è un URL non valido.", "smw-property-predefined-eid": "\"$1\" è un [[Special:Types/External identifier|tipo]] e una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per rappresentare identificatori esterni.", "smw-property-predefined-peid": "\"$1\" è una proprietà predefinita che rappresenta un identificatore esterno ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pefu": "\"$1\" è una proprietà predefinita fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per specificare una risorsa esterna con un segnaposto.", "smw-datavalue-parse-error": "Il valore dato \"$1\" non è stato compreso.", "smw-property-preferred-label-language-combination-exists": "\"$1\" non può essere utilizzato come etichetta preferita perché la lingua \"$2\" è già stata assegnata all'etichetta \"$3\".", "smw-clipboard-copy-link": "Copia il collegamento negli appunti", "smw-no-data-available": "<PERSON><PERSON><PERSON> dato disponibile.", "smw-property-req-violation-missing-fields": "Alla proprietà \"$1\" manca la dichiarazione obbligatoria [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] per questo tipo \"$2\".", "protect-level-smw-pageedit": "Consentito solo agli utenti con autorizzazione di modifica delle pagine (Semantic MediaWiki)", "smw-edit-protection": "Questa pagina è [[Property:Is edit protected|protetta]] per prevenire la modifica accidentale di dati e può essere modificata solo da utenti in possesso di un'appropriata autorizzazione (\"$1\") oppure [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups gruppo utente].", "smw-edit-protection-disabled": "La modifica di protezione è stata disattivata, pertanto \"$1\" non può essere utilizzato per proteggere il contenuto della pagina dalle modifiche non autorizzate.", "smw-property-predefined-long-edip": "Mentre qualsiasi utente è qualificato per aggiungere questa proprietà ad un soggetto, soltanto utenti con uno specifico permesso possono modificare o rimuovere la protezione ad un'entità, una volta che questa è stata aggiunta.", "smw-format-datatable-emptytable": "<PERSON><PERSON><PERSON> dato disponibile nella tabella", "smw-format-datatable-loadingrecords": "Caricamento in corso...", "smw-format-datatable-processing": "Elaborazione...", "smw-format-datatable-search": "Cerca:", "smw-format-datatable-first": "Primo", "smw-format-datatable-last": "Ultimo", "smw-format-datatable-next": "Successivo", "smw-format-datatable-previous": "Precedente", "smw-format-datatable-toolbar-export": "Esporta", "smw-format-list-other-fields-open": "(", "apihelp-smwbrowse-summary": "Modulo API per supportare le attività di navigazione per differenti tipi di entità in Semantic MediaWiki.", "smw-property-reserved-category": "Categoria", "smw-category": "Categoria", "smw-filter": "Filtro", "smw-section-expand": "Espandi la sezione", "smw-section-collapse": "Compimi la sezione", "smw-ask-format-help-link": "Formato [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON>", "smw-property-predefined-label-skey": "Chiave di ordinamento", "smw-loading": "Caricamento...", "smw-expand": "Espandi", "smw-collapse": "Comprimi", "smw-copy": "Copia", "smw-copy-clipboard-title": "Copia il contenuto negli appunti", "smw-jsonview-expand-title": "Espandi la vista JSON", "smw-jsonview-collapse-title": "Comprimi la vista JSON", "smw-jsonview-search-label": "Cerca:", "smw-types-title": "Tipo: $1", "smw-schema-error-title": "{{PLURAL:$1|Errore|Errori}} di convalida", "smw-schema-error-miscellaneous": "Errore vario ($1)", "smw-schema-error-json": "Errore JSON: \"$1\"", "smw-schema-usage": "<PERSON><PERSON><PERSON><PERSON>", "smw-schema-type": "Tipo schema", "smw-property-predefined-constraint-schema": "\"$1\" è una proprietà predefinita che definisce uno schema di vincoli ed è fornita da [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-tab-usage": "<PERSON><PERSON><PERSON><PERSON>", "smw-property-tab-redirects": "<PERSON>nimi", "smw-property-tab-subproperties": "Sottoproprietà", "smw-concept-tab-list": "Elenco", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-ask-tab-code": "Codice", "smw-legend": "<PERSON>a", "smw-entity-examiner-associated-revision-mismatch": "Versione", "smw-facetedsearch-intro-tab-explore": "Esplora", "smw-facetedsearch-format-options": "Opzioni di visualizzazione", "smw-facetedsearch-no-filters": "<PERSON><PERSON><PERSON> filtro.", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "Di seguito {{PLURAL:$1|viene presentato al massimo '''1''' risultato|vengono presentati al massimo '''$1''' risultati}} a partire dal numero '''$2'''."}