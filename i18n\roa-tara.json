{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>"]}, "smw-error": "Errore", "smw-upgrade-error-title": "Errore » MediaUicchi Semandeche", "smw-upgrade-error-why-title": "<PERSON><PERSON><PERSON><PERSON> stoche a 'druche sta pàgene?", "smw-upgrade-error-how-title": "Accumme pozze aggiustà st'errore?", "smw-upgrade-maintenance-title": "Manutenzione » MediaUicchi Semandeche", "smw-upgrade-maintenance-why-title": "<PERSON><PERSON><PERSON><PERSON> stoche a 'druche sta pàgene?", "smw-semantics-not-enabled": "Le funziune de Semantic MediaUicchi non ge sò abbilitate pe stu uicchi.", "smw_viewasrdf": "Feed RDF", "smw_finallistconjunct": ", e", "smw-factbox-head": "... de cchiù sus a \"$1\"", "smw-factbox-facts": "Date", "smw-factbox-attachments": "Allegate", "smw-factbox-attachments-value-unknown": "N/A", "smw-factbox-attachments-is-local": "Jè locale", "smw-factbox-attachments-help": "Fà 'ndrucà le allegate disponibbele", "smw-factbox-facts-derived": "Fatte derivate", "smw_isspecprop": "Sta probbietà jè 'na probbietà speciale jndr'à sta uicchi.", "smw-concept-cache-header": "Ause d'a cache", "smw-concept-no-cache": "Nisciuna cache disponibbile.", "smw_concept_description": "Descrizione d'u congette \"$1\"", "smw_no_concept_namespace": "Congette ca ponne essere definite sule sus a le pàggene d'u namespace Concept:.", "smw_multiple_concepts": "Ogne pàgene de congette pò avè sulamende 'na definizione de congette.", "version-semantic": "Estenziune semandeche", "smw_baduri": "Le URI da 'u module \"$1\" non ge sò permesse.", "smw_printername_count": "Resultate d'u condegge", "smw_printername_csv": "Esporte CSV", "smw_printername_dsv": "Esporte DSV", "smw_printername_debug": "Analise de l'inderrogazione (pe esperte)", "smw_printername_embedded": "'<PERSON>ap<PERSON><PERSON> le pàggene de condenute", "smw_printername_json": "esporte JSON", "smw_printername_list": "<PERSON><PERSON><PERSON>", "smw_printername_plainlist": "<PERSON><PERSON><PERSON> semblice", "smw_printername_ol": "Elenghe numerate", "smw_printername_ul": "<PERSON><PERSON><PERSON> pundate", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON><PERSON> est<PERSON>", "smw_printername_template": "Template", "smw_printername_templatefile": "File d'u template", "smw_printername_rdf": "Esporte RDF", "smw_printername_category": "Categorije", "validator-type-class-SMWParamSource": "teste", "smw-paramdesc-limit": "'U numere massime de resultate da turnà", "smw-paramdesc-link": "Fà vedè le calore cumme collegaminde", "smw-paramdesc-embedonly": "No ffà 'ndrucà le 'ndestaziune", "smw-paramdesc-rdfsyntax": "Adda essere ausate 'a sindasse RDF", "smw-paramdesc-csv-sep": "Specifiche 'nu separatore de culonne", "smw-paramdesc-csv-valuesep": "Specifiche 'nu valore separatore", "smw-paramdesc-dsv-separator": "Separatore da ausà", "smw-paramdesc-dsv-filename": "'U nome pu file DSV", "smw-paramdesc-filename": "'U nome pu file d'u resultate", "smw-paramdesc-export": "Opziune de esportazione", "smw-paramdesc-json-type": "Tipe de serializzazzione", "smw-paramdesc-source": "Fonde alternative de 'nderrogazione", "smw-paramdesc-jsonsyntax": "Sindasse JSON da ausà", "smw-printername-feed": "Feed RSS e Atom", "smw-paramdesc-feedtype": "Tipe de feed", "smw-label-feed-description": "$1 $2 feed", "smw_iq_moreresults": "... otre resultate", "smw_parseerror": "'U valore date non ge se capisce.", "smw_notitle": "\"$1\" non ge pò essere ausate cumme 'nu nome de 'na pàgene jndr'à sta uicchi.", "smw_noproperty": "\"$1\" non ge pò essere ausate cumme 'nu nome de 'na probbietà jndr'à sta uicchi.", "smw_wrong_namespace": "Sulamende le pàggene jndr'à 'u namespace \"$1\" se ponne mettere aqquà.", "smw-constraint-error-limit": "L'elenghe tène 'nu massime de $1 violaziune.", "smw_true_words": "vere,true,t,sine,si,yes,y", "smw_false_words": "fause,false,f,none,no,n", "smw_nofloat": "\"$1\" non g'è 'nu numere.", "smw_novalues": "Nisciune valore specificate.", "smw_nodatetime": "ìA date \"$1\" non g'ha state capite.", "smw_type_header": "Probbietà d'u tipe \"$1\"", "smw-propertylist-subproperty-header": "Sotteprobbietà", "smw-propertylist-redirect-header": "Sinonime", "specialpages-group-smw_group-maintenance": "Manutenzione", "specialpages-group-smw_group-properties-concepts-types": "Probbietà, congette e tipe", "specialpages-group-smw_group-search": "Sfogghie e cirche", "exportrdf": "Esporte le pàggene jndr'à RDF", "smw_exportrdf_submit": "Esporte", "uriresolver": "URIResolver", "properties": "Proprietà", "smw-categories": "Le Categorije", "smw_property_template": "$1 de tipe $2 ($3 {{PLURAL:$3|ause}})", "smw-special-property-searchform-options": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Filtre:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "None approvate", "concepts": "<PERSON><PERSON><PERSON>", "smw-special-concept-header": "<PERSON><PERSON><PERSON> conget<PERSON>", "smw-special-concept-empty": "<PERSON><PERSON><PERSON><PERSON> congette acchiate.", "unusedproperties": "Probbietà non ausate", "smw-unusedproperty-template": "$1 de tipe $2", "wantedproperties": "Probbietà cercate", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|ause|ausene}})", "smw_purge": "Aggiorne", "types": "Tipe", "smw-statistics": "Statisteche semandeche", "smw-statistics-cached": "Statisteche semandeche (casciate)", "smw-statistics-entities-total": "Entità (totale)", "smw-statistics-property-instance": "Probbietà {{PLURAL:$1|valore}} (totale)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Probbietà}}]] (totale)", "smw-statistics-property-total-info": "'U totale de le probbietà reggistrate.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Probbietà}} (totale)", "smw-statistics-property-used": "{{PLURAL:$1|Probbietà}} (ausate cu almene 'nu valore)", "smw-statistics-property-page": "{{PLURAL:$1|Probbietà}} (reggistrate cu 'na pàgene)", "smw-statistics-property-type": "{{PLURAL:$1|Probbietà}} (assegnate a 'nu tipe de date)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|'Nderrogazzione|'Nderrogazziune}}", "smw-statistics-query-format": "formate <code>$1</code>", "smw-statistics-query-size": "Dimenzione de l'inderrogazzione", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Congette}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Congette}}]]", "ask": "Recerche semandeche", "smw_ask_ascorder": "Inghiananne", "smw_ask_descorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-order-rand": "A uecchije", "smw_ask_submit": "<PERSON><PERSON><PERSON><PERSON> le resultate", "smw_ask_editquery": "Cange l'inderrogazione", "smw_add_sortcondition": "[Aggiunge 'a condizione de ordinamende]", "smw_ask_hidequery": "Scunne l'inderrogazione ('ndruche combatte)", "smw_ask_queryhead": "Condizione", "smw_ask_format_as": "Formatte cumme:", "smw_ask_defaultformat": "de base", "smw_ask_otheroptions": "<PERSON><PERSON>", "smw-ask-delete": "Live", "smw-ask-sorting": "Ordinamende", "smw-ask-options": "<PERSON><PERSON><PERSON>", "smw-ask-options-sort": "Opziune de ordinamende", "smw-ask-format-options": "Formate e opziune", "smw-ask-parameters": "Parametre", "smw-ask-search": "Cirche", "smw-ask-debug": "Debug", "smw-ask-result": "Resultate", "smw-ask-empty": "<PERSON><PERSON><PERSON>e tutte le vô<PERSON>ce", "smw-ask-format": "Formate", "smw-ask-extra-other": "<PERSON><PERSON>", "smw_sbv_property": "Probbietà:", "smw_sbv_value": "Valore:", "smw_sbv_submit": "<PERSON><PERSON><PERSON><PERSON> le resultate", "browse": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "smw_browselink": "Sfogghie le probbietà", "smw_browse_article": "Sckaffe 'u nome d'a pàgene da addò accumenzà 'u sfogliamende.", "smw_browse_go": "<PERSON><PERSON><PERSON>", "smw-browse-show-group": "Fà 'ndrucà le gruppe", "smw-browse-hide-group": "<PERSON><PERSON><PERSON> le gruppe", "smw_inverse_label_default": "$1 de", "smw_pp_from": "<PERSON> 'a pàgene:", "smw_pp_type": "Probbietà:", "smw_pp_submit": "<PERSON><PERSON><PERSON><PERSON> le resultate", "smw-prev": "rrede {{PLURAL:$1|$1}}", "smw-next": "nnande {{PLURAL:$1|$1}}", "smw_result_prev": "Precedende", "smw_result_next": "Prossime", "smw_result_results": "Resultate", "smw_result_noresults": "Nisciune resultate.", "smwadmin": "Cruscotte MediaUicchi Semandeche", "smw-admin-statistics-job-title": "Statisteche d'u job", "smw_smwadmin_return": "Tuèrne a $1", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> j<PERSON> s<PERSON> e adda essere luate jndr'à $2", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|opzione|opziune}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> ste avène cangiate da <code>$2</code>", "smw-admin-deprecation-notice-title-notice": "'Mbos<PERSON><PERSON>une sconzigliate", "smw-smwadmin-refresh-title": "Aggiustamende e aggiornamende d'u date", "smw_smwadmin_datarefresh": "Recostruzione d'u date", "smw-admin-other-functions": "<PERSON><PERSON> fun<PERSON>", "smw-admin-statistics-extra": "Fun<PERSON>une statisteche", "smw-admin-supplementary-section-title": "Funziune supplemendare", "smw-admin-supplementary-section-subtitle": "Funziune prengepàle supportate", "smw-admin-supplementary-settings-title": "Configurazione e 'mbostaziune", "smw-admin-main-title": "Semantic MediaUicchi » $1", "smw-admin-supplementary-operational-statistics-title": "Statisteche operazionale", "smw-admin-supplementary-elastic-settings-title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (indice)", "smw-admin-supplementary-elastic-mappings-title": "Mappature", "smw-admin-supplementary-elastic-mappings-summary": "Riepilog<PERSON>", "smw-admin-supplementary-elastic-mappings-fields": "Mappature de le cambe", "smw-admin-supplementary-elastic-nodes-title": "Node", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> fà 'ndrucà le statisteche d'u node", "smw-admin-supplementary-elastic-indices-title": "Indice", "smw-admin-supplementary-elastic-statistics-title": "Statisteche", "smw-admin-supplementary-elastic-replication-header-title": "State d'a repliche", "smw-admin-supplementary-elastic-replication-files": "File", "smw-property-label-similarity-threshold": "Soglie:", "smw-property-label-similarity-type": "Fà 'ndrucà l'ID d'u tipe", "smw-paramdesc-category-delim": "'U delimitatore", "smw-info-par-message": "Messàgge da fà 'ndrucà:", "prefs-smw": "Semantic MediaUicchi", "prefs-general-options": "Opziune generale", "smw-ui-tooltip-title-property": "Probbietà", "smw-ui-tooltip-title-warning": "Avvertimende", "smw-ui-tooltip-title-parameter": "Parametre", "smw-ui-tooltip-title-event": "<PERSON><PERSON>", "smw-ui-tooltip-title-note": "Note", "smw-ui-tooltip-title-legend": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-reference": "Riferimende", "smw_unknowntype": "'U tipe \"$1\" de sta probbietà non g'è valide", "smw-admin-idlookup-input": "Cirche:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Panorameche", "smw-admin-configutation-tab-settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-configutation-tab-namespaces": "Namespace", "smw-admin-maintenance-script-section-update": "Script di aggiornamento", "smw-admin-maintenance-script-section-rebuild": "Script di ricostruzione", "smw-livepreview-loading": "Stoche a careche…", "smw-sp-searchbyproperty-resultlist-header": "<PERSON><PERSON><PERSON> de le resultate", "smw-search-profile-sort-title": "<PERSON><PERSON>", "smw-search-profile-extended-section-sort": "Ordene pe", "smw-search-profile-extended-section-query": "'Nderrogazione", "log-name-smw": "Archivije de Semantic MediaUicchi", "log-show-hide-smw": "$1 Archivije de Semantic MediaUicchi", "smw-datavalue-invalid-number": "\"$1\" non ge pò essere 'nderpretate cumme 'nu numere.", "smw-type-tab-properties": "Probbietà", "smw-type-tab-types": "Tipe", "smw-type-tab-errors": "Errore", "smw-type-primitive": "Nderre-nderre", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|seconde}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|seconde}}", "smw-clipboard-copy-link": "Copie 'u collegamende jndr'à l'appunde", "smw-no-data-available": "Nisciune date disponibbile.", "smw-format-datatable-loadingrecords": "Stoche a careche…", "smw-format-datatable-processing": "Stoche a processe...", "smw-format-datatable-search": "Cirche:", "smw-format-datatable-first": "Prime", "smw-format-datatable-last": "<PERSON><PERSON><PERSON>", "smw-format-datatable-next": "Prossime", "smw-format-datatable-previous": "Precedende", "smw-loading": "Stoche a careche…", "smw-preparing": "<PERSON><PERSON>e a prepare...", "smw-expand": "<PERSON><PERSON>", "smw-collapse": "<PERSON><PERSON><PERSON>", "smw-copy": "<PERSON><PERSON>", "smw-copy-clipboard-title": "Copie 'u condenute jndr'à le appunde", "smw-schema-error-title": "{{PLURAL:$1|Errore}} de validazione", "smw-schema-error-json": "Errore JSON: \"$1\"", "smw-schema-validation-schema-title": "Scheme JSON", "smw-schema-summary-title": "Riepilog<PERSON>", "smw-schema-title": "Scheme", "smw-schema-type": "Tipe d'u scheme", "smw-schema-type-description": "Descrizione d'u tipe", "smw-schema-description-property-profile-schema": "Stu tipe de scheme supporte 'a definizione de 'nu profile pe dichiarà le caratteristeche a 'a probbietà assegnate e le valore de l'annotaziune.", "smw-property-tab-usage": "<PERSON><PERSON>", "smw-property-tab-redirects": "Sinonime", "smw-property-tab-subproperties": "Sotteprobbietà", "smw-property-tab-errors": "Assegnazziune sbagliate", "smw-property-tab-constraint-schema-title": "Vingole d'u scheme combilate", "smw-concept-tab-list": "<PERSON><PERSON><PERSON>", "smw-concept-tab-errors": "Errore", "smw-listingcontinuesabbrev": "cond.", "smw-showingresults": "Stoche a fazze vedè aqquà sotte {{PLURAL:$1|'''1''' resultete|'''$1''' resultete}} ca accumenzene cu #'''$2'''."}