{"@metadata": {"authors": ["<PERSON><PERSON>", "Amire80", "Dvdgmz", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jaumeort<PERSON>", "Jmarchn", "Kghbln", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Mguix", "Nemo bis", "<PERSON><PERSON>", "SMP", "Sociologist", "Solde", "<PERSON><PERSON>", "Vfc", "아라"]}, "smw-desc": "Fem el vostre wiki més accessible, per a les màquines ''i també'' per als humans ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentació en línia])", "smw-error": "Error", "smw-upgrade-error": "S'ha instal·lat i activat [https://www.semantic-mediawiki.org/ Semantic MediaWiki], però cal una [https://www.semantic-mediawiki.org/wiki/Help:Upgrade clau d'actualització] adequada.", "smw-upgrade-release": "<PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-progress": "Progrés", "smw-upgrade-progress-create-tables": "S'estan creant (o actualitzant) les taules i els índexs...", "smw-upgrade-progress-post-creation": "S'estan executant les tasques postcreació...", "smw-upgrade-progress-table-optimization": "S'estan executant les optimitzacions de taula...", "smw-upgrade-progress-supplement-jobs": "S'estan afegint tasques suplementàries...", "smw-upgrade-error-title": "Error • Semantic MediaWiki", "smw-upgrade-error-why-title": "Per què veig aquesta pàgina?", "smw-upgrade-error-why-explain": "La base de dades interna del Semantic MediaWiki ha canviat i calen alguns ajustaments perquè sigui plenament funcional. Pot haver-hi diferents motius, entre ells:\n* S'han afegit diferents propietats fixes addicionals (que requereixen una configuració de taules addicional)\n* Una actualització conté alguns canvis a les taules o als índexs que fan que una intercepció sigui obligatòria abans d'accedir a les dades\n* canvis al motor d'emmagatzematge o consulta", "smw-upgrade-error-how-title": "Com arreglo aquest error?", "smw-extensionload-error-why-title": "Per què veig aquesta pàgina?", "smw-extensionload-error-how-title": "Com corregeixo aquest error?", "smw-upgrade-maintenance-title": "Manteniment • Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Per què veig aquesta pàgina?", "smw-semantics-not-enabled": "No s'ha habilitat la funcionalitat de Semantic MediaWiki en aquest wiki.", "smw_viewasrdf": "Canal RDF", "smw_finallistconjunct": ", i", "smw-factbox-head": "... més sobre «$1»", "smw-factbox-facts": "Fets", "smw-factbox-facts-help": "Mostra declaracions i fets creats per un usuari", "smw-factbox-attachments": "Adjuncions", "smw-factbox-attachments-value-unknown": "N/D", "smw-factbox-attachments-is-local": "És local", "smw-factbox-attachments-help": "Mostra les adjuncions disponibles", "smw-factbox-facts-derived": "Fets derivats", "smw-factbox-facts-derived-help": "Mostra fets que s'han derivat de regles amb l'ajuda d'altres tècniques de raonament", "smw_isspecprop": "Aquesta és una propietat especial en aquest wiki.", "smw-concept-cache-header": "Ús de la memòria cau", "smw-concept-cache-count": "La [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count memòria cau de conceptes] conté {{PLURAL:$1|'''una''' entitat|'''$1''' entitats}} ($2).", "smw-concept-no-cache": "No hi ha memòria cau disponible.", "smw_concept_description": "Descripció del concepte «$1».", "smw_no_concept_namespace": "Els conceptes només poden ser definits en pàgines dins l'espai de noms (namespace) Concept:", "smw_multiple_concepts": "Cada pàgina de concepte només pot tenir una definició de concepte.", "smw_concept_cache_miss": "El concepte «$1» no pot ser utilitzat per ara, atès que la configuració del wiki s'ha de computar fora de línia.\nSi el problema persisteix passat cert temps, demaneu a l'administrador del vostre wiki que posi disponible aquest concepte.", "smw_noinvannot": "No es poden assignar valors a propietats inverses.", "version-semantic": "Extensions semàntiques", "smw_baduri": "Les URI del formulari «$1» no són permeses.", "smw_printername_count": "Resultats del recompte", "smw_printername_csv": "exportació a CSV", "smw_printername_dsv": "Exportació DSV", "smw_printername_debug": "Consulta de depuració (per a experts)", "smw_printername_embedded": "Continguts de pàgina incrustada", "smw_printername_json": "exportació a JSON", "smw_printername_list": "Llista", "smw_printername_plainlist": "Llista plana", "smw_printername_ol": "Llista numerada", "smw_printername_ul": "Llista puntejada", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "Taula ampla", "smw_printername_template": "Plantilla", "smw_printername_templatefile": "Fitxer de plantilla", "smw_printername_rdf": "Exportació RDF", "smw_printername_category": "Categoria", "validator-type-class-SMWParamSource": "text", "smw-paramdesc-limit": "El nombre màxim de resultats a retornar", "smw-paramdesc-offset": "El desplaçament (offset) del primer resultat", "smw-paramdesc-headers": "Mostra les capçaleres / noms de les propietats", "smw-paramdesc-mainlabel": "L'etiqueta que es donarà al nom de la pàgina principal", "smw-paramdesc-link": "Mostra els valors com a enllaços", "smw-paramdesc-intro": "El text que apareixerà abans que el resultat de la consulta, si n'hi hagués", "smw-paramdesc-outro": "El text que apareixerà després que els resultats de la consulta, si n'hi hagués", "smw-paramdesc-default": "El text que es mostrarà si no hi ha resultats de la consulta", "smw-paramdesc-sep": "El separador entre els resultats", "smw-paramdesc-propsep": "El separador entre les propietats d'una entrada de resultat", "smw-paramdesc-valuesep": "El separador entre els valors d'una propietat d'un resultat", "smw-paramdesc-showsep": "Mostra el separador a la part superior del fitxer CSV (\"sep=<value>\")", "smw-paramdesc-distribution": "En comptes de mostrar tots els valors, compta les coincidències i mostra-ho.", "smw-paramdesc-distributionsort": "Ordena la distribució de valor pel recompte d'aparició.", "smw-paramdesc-distributionlimit": "Limita la distribució de valor al recompte de només alguns valors.", "smw-paramdesc-aggregation": "Especifica amb què s'ha de relacionar l'agregació", "smw-paramdesc-template": "El nom d'una plantilla amb la qual mostrar els llistats", "smw-paramdesc-columns": "El nombre de columnes amb les quals mostrar els resultats", "smw-paramdesc-userparam": "Es passa un valor a cada crida de la plantilla, sempre que s'utilitzi una plantilla", "smw-paramdesc-class": "Una classe addicional CSS per definir a la llista", "smw-paramdesc-introtemplate": "El nom d'una plantilla que es mostrarà abans dels resultats de la consulta, si n'hi ha cap", "smw-paramdesc-outrotemplate": "El nom d'una plantilla que es mostrarà després dels resultats de la consulta, si n'hi ha cap", "smw-paramdesc-embedformat": "L'etiqueta HTML que s'utilitza per a definir les capçaleres", "smw-paramdesc-embedonly": "No mostris cap<PERSON>es", "smw-paramdesc-table-class": "Una classe CSS addicional per definir per a la taula", "smw-paramdesc-table-transpose": "Mostra les capçaleres de la taula verticalment i els resultats horitzontalment", "smw-paramdesc-rdfsyntax": "La sintaxi RDF que s'utilitzarà", "smw-paramdesc-csv-sep": "Especifica una separador de columnes", "smw-paramdesc-csv-valuesep": "Especifica un separador de valors", "smw-paramdesc-csv-merge": "Fusiona els valors de files i columnes amb un identificador de subjecte idèntic (p. ex., la primera columna)", "smw-paramdesc-csv-bom": "Afegeix un BOM (caràcter per assenyalar l'ordre dels bytes -endianness-) a la part superior del fitxer de sortida", "smw-paramdesc-dsv-separator": "El separador que s'utilitzarà", "smw-paramdesc-dsv-filename": "El nom del fitxer DSV", "smw-paramdesc-filename": "El nom del fitxer de sortida", "smw-smwdoc-description": "Mostra una taula de tots els paràmetres que es poden utilitzar per al format del resultat especificat juntament amb els valors per defecte i les descripcions.", "smw-smwdoc-default-no-parameter-list": "El format de resultat no proporciona paràmetres específics de format.", "smw-smwdoc-par-format": "El format del resultat per mostrar la documentació del paràmetre.", "smw-smwdoc-par-parameters": "Quins paràmetres mostrar. «specific» per a aquells afegits per format, «base» per a aquells disponibles en tots els formats, i «all» per als dos.", "smw-paramdesc-sort": "Propietat per ordenar la consulta per", "smw-paramdesc-order": "Ordre de la ordenació de la consulta", "smw-paramdesc-searchlabel": "Text per continuar la cerca", "smw-paramdesc-named_args": "Anomena els arguments que es passen a la plantilla", "smw-paramdesc-template-arguments": "Especifica com els arguments amb nom es passen a la plantilla", "smw-paramdesc-import-annotation": "Es copiaran dades anotades addicionals durant l'anàlisi d'un subjecte", "smw-paramdesc-export": "Opció d'exportació", "smw-paramdesc-prettyprint": "Una impressió agradable que mostra sagnies addicionals i salts de línia", "smw-paramdesc-json-unescape": "La sortida contindrà barres sense escapar i caràcters Unicode d’octets múltiples.", "smw-paramdesc-json-type": "Tipus de serialització", "smw-paramdesc-source": "Font de consulta alternativa", "smw-paramdesc-jsonsyntax": "Sintaxi JSON que s'utilitzarà", "smw-printername-feed": "Canal RSS i Atom", "smw-paramdesc-feedtype": "Tipus de canal", "smw-paramdesc-feedtitle": "El text que s'utilitzarà com a títol del canal", "smw-paramdesc-feeddescription": "El text que s'utilitzarà com a descripció del canal", "smw-paramdesc-feedpagecontent": "Contingut de la pàgina que es mostrarà amb el canal", "smw-label-feed-description": "Canal $2 $1", "smw-paramdesc-mimetype": "El tipus mèdia (tipus MIME) per al fitxer de sortida", "smw_iq_disabled": "Les consultes semàntiques estan inhabilitades en aquest wiki.", "smw_iq_moreresults": "... més resultats", "smw_parseerror": "El valor donat no s'ha entès.", "smw_decseparator": ",", "smw_kiloseparator": "&nbsp;", "smw_notitle": "«$1» no es pot fer servir com a nom de pàgina en aquest wiki.", "smw_noproperty": "No es pot utilitzar «$1» com a nom de propietat en aquest wiki.", "smw_wrong_namespace": "Aquí només es poden fer servir pàgines en l'espai de noms \"$1\".", "smw_manytypes": "S'ha definit més d'un tipus per la propietat.", "smw_emptystring": "No s'accepten cadenes buides.", "smw_notinenum": "«$1» no és a la llista ($2) de [[Property:Allows value|valors permesos]] de la propietat «$3».", "smw-datavalue-constraint-error-allows-value-list": "«$1» no es troba a la llista ($2) de [[Property:Allows value|valors permesos]] de la propietat «$3».", "smw-datavalue-constraint-error-allows-value-range": "«$1» no es troba en el rang «$2» especificat per la restricció «[[Property:Allows value|permet valor]]» de la propietat «$3».", "smw-constraint-error": "Problema de restricció", "smw-constraint-error-limit": "La llista contindrà un màxim de $1 violacions.", "smw_noboolean": "«$1» no es pot reconèixer com un valor booleà (cert/fals).", "smw_true_words": "verdader,vertader,veritat,cert,true,t,sí,s,yes,y", "smw_false_words": "fals,f,no,n,false", "smw_nofloat": "\"$1\" no és un nombre.", "smw_infinite": "No es permeten nombres tan llargs com «$1».", "smw_unitnotallowed": "«$1» no està declarada com una unitat de mesura vàlida per a aquesta propietat.", "smw_nounitsdeclared": "No s'ha declarat cap unitat de mesura d'aquesta propietat.", "smw_novalues": "No s'ha especificat cap valor.", "smw_nodatetime": "No s'ha entès la data «$1».", "smw_toomanyclosing": "Sembla ser que «$1» apareix massa vegades a la consulta.", "smw_noclosingbrackets": "Algun ús de \"<nowiki>[[</nowiki>\" en la vostra consulta no es clou amb els \"]]\" corresponents.", "smw_misplacedsymbol": "El símbol «$1» es fa servir en un lloc on no és útil.", "smw_unexpectedpart": "La part «$1» de la consulta no s'ha entès.\nEls resultats podrien no ser els esperats.", "smw_emptysubquery": "Alguna subconsulta no té una condició vàlida.", "smw_misplacedsubquery": "Alguna subconsulta es fa servir en un lloc on les subconsultes no són permeses.", "smw_valuesubquery": "Subconsultes no suportades per valors de la propietat \"$1\".", "smw_badqueryatom": "Una part «<nowiki>[[…]]</nowiki>» de la consulta no s'ha pogut entendre.", "smw_propvalueproblem": "El valor de la propietat «$1» no s'ha pogut entendre.", "smw_noqueryfeature": "Algun aspecte d'aquesta consulta no està suportat en aquest wiki i part de la consulta no s'ha tingut en compte ($1).", "smw_noconjunctions": "Les conjuncions en consultes no són suportades en aquest wiki i part de la consulta no s'ha tingut en compte ($1)", "smw_nodisjunctions": "Les disjuncions en consultes no són suportades en aquest wiki i part de la consulta no s'ha tingut en compte ($1).", "smw_querytoolarge": "{{PLURAL:$2|La condició de consulta següent podria no ser considerada|Les $2 condicions de consulta següents podrien no ser considerades}} degut a les restriccions de mida o profunditat per consultes en el wiki: $1.", "smw_notemplategiven": "Per fer que això funcioni, doneu un valor al paràmetre «template» (plantilla) per al format d'aquesta consulta.", "smw_db_sparqlqueryproblem": "No s'ha pogut obtenir el resultat de la consulta de la base de dades SPARQL. Aquest error podria ser temporal o indicar alguna mena d'error en el programari de la base de dades.", "smw_db_sparqlqueryincomplete": "Respondre la consulta ha acabat essent massa difícil i s'ha interromput. Podrien mancar-hi alguns resultats. Si fos possible, proveu millor una consulta més simple.", "smw_type_header": "Propietats de tipus «$1»", "smw_typearticlecount": "Es {{PLURAL:$1|mostra|mostren}} $1 {{PLURAL:$1|propietat que fa servir|propietats que fan servir}} aquesta propietat.", "smw_attribute_header": "Pàgines que fan servir la propietat \"$1\"", "smw_attributearticlecount": "Es {{PLURAL:$1|mostra|mostren}} $1 {{PLURAL:$1|pàgina que fa servir|pàgines que fan servir}} aquesta propietat.", "smw-propertylist-subproperty-header": "Subpropietats", "smw-propertylist-redirect-header": "Sinònims", "smw-propertylist-error-header": "Pàgines amb assignacions no apropiades", "smw-propertylist-count": "Es {{PLURAL:$1|mostra $1 entitat relacionada|mostren $1 entitats relacionades}}.", "smw-propertylist-count-with-restricted-note": "Es {{PLURAL:$1|mostra $1 entitat relacionada|mostren $1 entitats relacionades}} (hi ha més disponibles però la visualització està restringida a «$2»).", "smw-propertylist-count-more-available": "Es {{PLURAL:$1|mostra $1 entitat relacionada|mostren $1 entitats relacionades}} (hi ha més disponibles).", "specialpages-group-smw_group-maintenance": "Manteniment", "specialpages-group-smw_group-properties-concepts-types": "Propie<PERSON><PERSON>, conceptes i tipus", "specialpages-group-smw_group-search": "Navega i cerca", "exportrdf": "Exporta les pàgines a RDF", "smw_exportrdf_docu": "Aquesta pàgina permet obtenir dades en format RDF d'una pàgina del wiki.\nPer exportar pàgines, entra els títols a la caixa de text següent, un títol per línia.", "smw_exportrdf_recursive": "Exporta recursivament totes les pàgines relacionades.\nTingueu en compte que el resultat pot ser molt gran!", "smw_exportrdf_backlinks": "També exporta totes les pàgines que es refereixen a les pàgines exportades.\nGenera un RDF que es pot navegar.", "smw_exportrdf_lastdate": "No exportis pàgines que no s'han canviat des del punt donat  en el temps.", "smw_exportrdf_submit": "Exporta", "uriresolver": "Resolutor d'URI", "properties": "Propietats", "smw-categories": "Categories", "smw_properties_docu": "S'utilitzen les propietats següents al wiki.", "smw_property_template": "$1 de tipus $2 ($3 {{PLURAL:$3|ús|usos}})", "smw_propertylackspage": "Cal descriure totes les propietats amb una pàgina!", "smw_propertylackstype": "No s'ha especificat cap tipus per a la propietat (s'assumeix el tipus $1 per ara).", "smw_propertyhardlyused": "Propietats pràcticament no utilitzades al llarg del wiki!", "smw-property-name-invalid": "La propietat $1 no pot fer-se servir (nom de propietat no vàlid).", "smw-property-name-reserved": "«$1» està llista com a nom reservat i no s'hauria d'utilitzar com a propietat. La següent [https://www.semantic-mediawiki.org/wiki/Help:Property_naming pàgina d'ajuda] pot contenir informació sobre el motiu perquè aquest nom és reservat.", "smw-sp-property-searchform": "Mostra propietats que contenen:", "smw-sp-property-searchform-inputinfo": "L'entrada és sensible a les majúscules i, quan s'utilitza per al filtratge, només es mostren les propietats que coincideixen amb la condició.", "smw-special-property-searchform": "Mostreu les propietats que contenen:", "smw-special-property-searchform-inputinfo": "L'entrada distingeix entre majúscules i minúscules i, quan s'utilitza per filtrar, només es mostren les propietats que coincideixen amb la condició.", "smw-special-property-searchform-options": "Opcions", "smw-special-wantedproperties-filter-label": "Filtre:", "smw-special-wantedproperties-filter-none": "Cap", "smw-special-wantedproperties-filter-unapproved": "Sense aprovar", "smw-special-wantedproperties-filter-unapproved-desc": "Opció de filtratge utilitzada en connexió amb el mode d'autoritat.", "concepts": "Conceptes", "smw-special-concept-docu": "Un [https://www.semantic-mediawiki.org/wiki/Help:Concepts concepte] pot entendre's com una «categoria dinàmica», una col·lecció de pàgines que no es creen manualment, sinó que el Semantic MediaWiki les computa a partir de la descripció d'una consulta.", "smw-special-concept-header": "Llista de conceptes", "smw-special-concept-count": "Es {{PLURAL:$1|llista|llisten}} {{PLURAL:$1|el concepte|els $1 conceptes}} següents.", "smw-special-concept-empty": "No s'ha trobat cap concepte.", "unusedproperties": "Propietats no utilitzades", "smw-unusedproperties-docu": "Aquesta pàgina llista les [https://www.semantic-mediawiki.org/wiki/Unused_properties propietats no utilitzades], que són declarades però que cap pàgina les fa servir. Per a una vista diferenciada, visiteu les pàgines especials de [[Special:Properties|totes les propietats]] o de les [[Special:WantedProperties|propietats per definir]].", "smw-unusedproperty-template": "$1 del tipus $2", "wantedproperties": "Propietats per definir", "smw-wantedproperties-docu": "Aquesta pàgina llista les [https://www.semantic-mediawiki.org/wiki/Wanted_properties propietats per definir] que s'utilitzen en aquest wiki però que no tenen cap pàgina que les descrigui. Per a una vista diferenciada, visiteu les pàgines especials de [[Special:Properties|totes les propietats]] o de les [[Special:UnusedProperties|propietats no utilitzades]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|ús|usos}})", "smw-special-wantedproperties-docu": "Aquesta pàgina llista les [https://www.semantic-mediawiki.org/wiki/Wanted_properties propietats demanades] que s'utilitzen en el wiki però que no tenen una pàgina que les descriu. Per a una vista diferenciada, vegeu les pàgines especials de [[Special:Properties|totes les propietats]] o de les [[Special:UnusedProperties|no utilitzades]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|utilització|utilitzacions}})", "smw_purge": "Refresca", "smw-purge-update-dependencies": "Semantic MediaWiki està purgant la pàgina actual perquè té dependències desactualitzades que ha detectat que requerien una actualització.", "smw-purge-failed": "Semantic MediaWiki ha provat de purgar la pàgina però ha fallat", "types": "<PERSON><PERSON><PERSON>", "smw_types_docu": "Llista de [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tipus de dades disponibles], on cada [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipus] representa un conjunt únic d'atributs per descriure un valor en termes d'emmagatzematge i característiques de visualització que són hereditaris per a una propietat assignada.", "smw-special-types-no-such-type": "El tipus de dades «$1» és desconegut o no s'ha especificat.", "smw-statistics": "Estadístiques semàntiques", "smw-statistics-cached": "Estadístiques semàntiques (en memòria cau):", "smw-statistics-entities-total": "Entitats (total)", "smw-statistics-entities-total-info": "Un recompte estimat de les files d'entitats. Inclou propietats, conceptes o altres representacions d'objectes registrats que requereixen una assignació d'ID.", "smw-statistics-property-instance": "Propietat {{PLURAL:$1|valor|valors}} (total)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Propietat|Propietats}}]] (total)", "smw-statistics-property-total-info": "El total de propietats enregistrades.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Propietat|Propietats}} (total)", "smw-statistics-property-used": "{{PLURAL:$1|Propietat|Propietats}} (amb un valor utilitzat mínim)", "smw-statistics-property-page": "{{PLURAL:$1|Propietat|Propietats}} (registrat amb una pàgina)", "smw-statistics-property-page-info": "Recompte de les propietats que tenen una pàgina dedicada i una descripció.", "smw-statistics-property-type": "{{PLURAL:$1|Propietat|Propietats}} (assignat a un tipus de dades)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Consulta|Consultes}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Consulta|Consultes}}]] (incrustades, totals)", "smw-statistics-query-format": "format de <code>$1</code>", "smw-statistics-query-size": "Mida de la consulta", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Concepte|Conceptes}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Concepte|Conceptes}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|Té {{PLURAL:$1|subobjecte|subobjectes}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Subobjecte|Subobjectes}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Tipus de dades}}]]", "smw-statistics-error-count": "{{PLURAL:$1|El valor de la propietat|Els valors de la propietat}} ([[Special:ProcessingErrorList|{{PLURAL:$1|anotació impròpia|anotacions impròpies}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Valor de propietat|Valors de propietat}} ({{PLURAL:$1|anotació inadequada|anotacions indadequades}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Entitat desactualitzada|Entitats desactualitzades}}]", "smw-statistics-delete-count-info": "Les entitats que s'han marcat per suprimir que s'haurien d'alliberar regularment utilitzant els scripts de manteniment proporcionats.", "smw_uri_doc": "El resolutor d'URI implementa la [$1 cerca de W3C TAG en httpRange-14].\nTé cura que els humans no es tornin en llocs web.", "ask": "Cerca semàntica", "smw-ask-help": "Aquesta secció conté alguns enllaços per ajudar a explicar com utilitzar la sintaxi <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecció de pàgines]: descriu com seleccionar les pàgines i construir condicions\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadors de cerca]: llista els operadors de cerca disponibles, incloent-hi els de les consultes d'intèrval i de caràcters comodí\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Impressió d'informació]: resumeix l'ús de les opcions de declaració i formatatge", "smw_ask_sortby": "Ordena per columna (opcional)", "smw_ask_ascorder": "Ascendent", "smw_ask_descorder": "Descendent", "smw-ask-order-rand": "A l’atzar", "smw_ask_submit": "Cerca'n resultats", "smw_ask_editquery": "Edita la consulta", "smw_add_sortcondition": "Afegeix una condició d'ordenació", "smw-ask-sort-add-action": "Afegeix una condició d'ordenació", "smw_ask_hidequery": "Amaga la consulta (vista compacta)", "smw_ask_help": "Consulta de l'ajuda", "smw_ask_queryhead": "Condició", "smw_ask_printhead": "Selecció de dades a imprimir", "smw_ask_printdesc": "(afegeix un nom de propietat per línia)", "smw_ask_format_as": "Formata com:", "smw_ask_defaultformat": "per defecte", "smw_ask_otheroptions": "Altres opcions", "smw-ask-otheroptions-info": "Aquesta secció conté les opcions que alteren les declaracions d'impressió. Poden veure's les descripcions dels paràmetres en passar-hi el ratolí sobre ells.", "smw-ask-otheroptions-collapsed-info": "<PERSON><PERSON><PERSON><PERSON> la icona 'més' per mostrar totes les opcions disponibles", "smw_ask_show_embed": "Mostra el codi incrustat", "smw_ask_hide_embed": "Amaga el codi incrustat", "smw_ask_embed_instr": "Per a incrustar aquesta consulta en línia a una pàgina wiki utilitzeu el codi a continuació.", "smw-ask-delete": "Suprimeix", "smw-ask-sorting": "Ordenació", "smw-ask-options": "Opcions", "smw-ask-options-sort": "Opcions d'ordenació", "smw-ask-format-options": "Formatació i opcions", "smw-ask-parameters": "Paràmetres", "smw-ask-search": "Cerca", "smw-ask-debug": "Depura", "smw-ask-debug-desc": "Genera informació de depuració de consultes", "smw-ask-no-cache": "Inhabilita la cau de consulta", "smw-ask-no-cache-desc": "Resultats sense memòria cau de consulta", "smw-ask-result": "Resultat", "smw-ask-empty": "Buida totes les entrades", "smw-ask-download-link-desc": "Baixa els resultats consultats en format $1", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Ajuda amb el format seleccionat: $1", "smw-ask-condition-change-info": "S'ha alterat la condició i el motor de cerca necessita que es torni a executar la consulta per a produir resultats que coincideixin amb els nous requisits.", "smw-ask-input-assistance": "Assistència d'entrada", "smw-ask-condition-input-assistance": "Es proporciona [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance assistència d'entrada] per als camps d'impressió, d'ordenació i de condició. El camp de condició requereix un dels prefixos següents:", "smw-ask-condition-input-assistance-property": "<code>p:</code> per a recollir suggeriments de propietats (p. ex., <code>[[p:Has …</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> per a recollir suggeriments de categories", "smw-ask-condition-input-assistance-concept": "<code>con:</code> per a recollir suggeriments de conceptes", "smw-ask-format-change-info": "S'ha modificat el format i cal tornar a executar la consulta perquè coincideixin els nous paràmetres i opcions de visualització.", "smw-ask-format-export-info": "El format seleccionat és un format d'exportació que no té representació visual. Per tant, els resultats només es proporcionen com a baixada.", "smw-ask-query-search-info": "La consulta <code><nowiki>$1</nowiki></code> va ser resposta per {{PLURAL:$3|1=<code>$2</code> (de la memòria cau)|<code>$2</code> (de la memòria cau)|<code>$2</code>}} en $4 {{PLURAL:$4|segon|segons}}.", "smw-ask-extra-query-log": "Registre de consultes", "smw-ask-extra-other": "Altre", "searchbyproperty": "Cerca per propietat", "processingerrorlist": "Llista d'errors de processament", "constrainterrorlist": "Llista d'erros de restricció", "propertylabelsimilarity": "Informe de similitud d'etiquetes de propietat", "missingredirectannotations": "Absència d'anotacions de redirecció", "smw-processingerrorlist-intro": "La llista següent proporciona un resum dels [https://www.semantic-mediawiki.org/wiki/Processing_errors processing errors de processament] que han aparegut en relació amb [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Es recomana monitoritzar aquesta llista de forma regular i corregir les anotacions amb valors no vàlids.", "smw-missingredirects-list": "Pàgines on hi manquen anotacions", "smw-missingredirects-list-intro": "Mostra $1 {{PLURAL:$1|pàgina|pàgines}} amb les anotacions de redirecció que hi manquen.", "smw-missingredirects-noresult": "No s'han trobat anotacions de redirecció que hi manquessin.", "smw_sbv_docu": "Cerca totes les pàgines que tenen una propietat i valor donats.", "smw_sbv_novalue": "Introduïu un valor vàlid per a la propietat, o vegeu tots els valors de la propietat «$1».", "smw_sbv_displayresultfuzzy": "Una llista de totes les pàgines que tenen la propietat «$1» amb el valor «$2».\nCom hi ha hagut només uns pocs resultats, també es mostren valors propers.", "smw_sbv_property": "Propietat:", "smw_sbv_value": "Valor:", "smw_sbv_submit": "Troba els resultats", "browse": "Explora la wiki", "smw_browselink": "Explora les propietats", "smw_browse_article": "Introduïu el nom de la pàgina des d'on començar a navegar.", "smw_browse_go": "Vés-hi", "smw_browse_show_incoming": "Mostra les propietats entrants", "smw_browse_hide_incoming": "Amaga les propietats entrants", "smw_browse_no_outgoing": "La pàgina no té cap propietat.", "smw_browse_no_incoming": "No enllaça cap propietat a la pàgina.", "smw-browse-from-backend": "S'està recuperant la informació del servidor ara mateix.", "smw-browse-intro": "Aquesta pàgina proporciona informació sobre un tema o instància d'entitat. Introduïu el nom d'un objecte per ser inspeccionat.", "smw-browse-invalid-subject": "La validació del subjecte ha retornat amb un error «$1».", "smw-browse-api-subject-serialization-invalid": "El subjecte té un format de serialització no vàlid.", "smw-browse-js-disabled": "Se sospita que el Javascript està inhabilitat o no està disponible. Recomanem que feu servir un navegador que el pugui fer servir. Es discuteixen altres opcions a la pàgina del paràmetre de configuració [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Mostra els grups", "smw-browse-hide-group": "Amaga els grups", "smw-noscript": "Aquesta pàgina o acció necessita Javascript per funcionar. Habiliteu el Javascript al navegador o utilitzeu-ne un que el permeti per tal que la funcionalitat funcioni com es demana. Per a més ajuda, consulteu la pàgina d'ajuda [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "Etiqueta de propietat inversa", "pageproperty": "Cerca de les propietats de la pàgina", "pendingtasklist": "Llista de tasques pendents", "smw_pp_docu": "Introduïu una pàgina i una propietat, o bé només una propietat, per a recuperar-ne tots els valors assignats.", "smw_pp_from": "De la pàgina:", "smw_pp_type": "Propietat:", "smw_pp_submit": "Troba els resultats", "smw-prev": "{{PLURAL:$1|anterior|$1 anteriors}}", "smw-next": "{{PLURAL:$1|següent|$1 següents}}", "smw_result_prev": "Anterior", "smw_result_next": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_results": "Resultats", "smw_result_noresults": "No hi ha resultats.", "smwadmin": "Tauler de control de Semantic MediaWiki", "smw-admin-statistics-job-title": "Estadístiques de tasques", "smw-admin-statistics-job-docu": "Les estadístiques de tasques mostren informació de les tasques programades al Semantic MediaWiki que encara no s'han executat. El nombre de tasques podria ser lleugerament no acurat o contenir intents fallits. Consulteu el [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manual] per a més informació.", "smw-admin-statistics-querycache-title": "Memòria cau de consultes", "smw-admin-statistics-querycache-disabled": "No s'ha habilitat el [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] en aquest wiki i, per tant, les estadístiques no són disponibles.", "smw-admin-statistics-section-explain": "La secció proporciona estadístiques addicionals per als administradors.", "smw-admin-statistics-semanticdata-overview": "Resum", "smw-admin-permission-missing": "L'accés a la pàgina ha estat blocat perquè manquen permisos. Consulteu la pàgina d'ajuda sobre [https://www.semantic-mediawiki.org/wiki/Help:Permissions permisos] per a més detalls dels paràmetres necessaris.", "smw-admin-setupsuccess": "S'ha configurat el motor d'emmagatzematge.", "smw_smwadmin_return": "Torna a $1", "smw_smwadmin_updatestarted": "S'ha iniciat un nou procés d'actualització per a refrescar les dades semàntiques.\nEs reconstruiran totes les dades emmagatzemades o bé es repararan quan calgui.\nPodeu seguir el progrés de l'actualització en aquesta pàgina especial.", "smw_smwadmin_updatenotstarted": "Ja hi ha un procés d'actualització executant-se.\nNo es crearà cap altre.", "smw_smwadmin_updatestopped": "S'han aturat tots els processos d'actualització existents.", "smw_smwadmin_updatenotstopped": "Per a aturar l'execució del procés d'actualització, heu de marcar la casella per a indicar que n'esteu plenament segur.", "smw-admin-docu": "Aquesta pàgina especial us ajuda durant la instal·lació i l'actualització del <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> i també proporciona altres funcions i tasques administratives juntament amb estadístiques.\nRecordeu fer una còpia de seguretat de les dades importants abans d'executar cap funció administrativa.", "smw-admin-environment": "Entorn de programari", "smw-admin-db": "Configuració de la base de dades", "smw-admin-db-preparation": "La inicialització de la taula és en procés i pot trigar una estona fins que els resultats es mostrin segons la mida de la taula i les optimitzacions que s'hi facin.", "smw-admin-dbdocu": "Semantic MediaWiki necessita la seva pròpia estructura de base de dades (independent de MediaWiki, i per tant, no afecta la resta de la instal·lació de MediaWiki) per tal d'emmagatzemar les dades semàntiques.\nAquesta funció de configuració pot executar-se moltes vegades sense cap perill; però cal al menys una vegada durant la instal·lació o l'actualització.", "smw-admin-permissionswarn": "Si l'operació falla amb errors SQL, l'usuari de la base de dades que utilitza el vostre wiki (comproveu el LocalSettings.php) probablement no té suficients permisos.\nPodeu atorgar l'usuari permisos addicionals per a crear o suprimir taules, introduïu temporalment les dades d'inici de l'administrador (root) de la base de dades al LocalSettings.php, o bé feu servir l'script de manteniment <code>setupStore.php</code>, que pot utilitzar les credencials d'un administrador.", "smw-admin-dbbutton": "Inicialitza o actualitza les taules", "smw-admin-announce": "<PERSON><PERSON> conè<PERSON>er el vostre wiki", "smw-admin-announce-text": "Si el vostre wiki és pú<PERSON>lic, el podeu registrar a <a href=\"https://wikiapiary.com\">WikiApiary</a>, el catàleg wiki de wikis.", "smw-admin-deprecation-notice-title": "Avisos d'obsolescència", "smw-admin-deprecation-notice-docu": "La secció següent conté paràmetres que són obsolets o en desús però que s'han detectat com a actius en aquest wiki. S'espera que en versions futures se suprimirà el suport per a aquestes configuracions.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> és obsolet i se suprimirà en $2", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> és obsolet i se suprimirà en $2", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> està essent reemplaçat per <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> va ser suprimit a $2", "smw-admin-deprecation-notice-title-notice": "Paramètres obsolets", "smw-admin-deprecation-notice-title-notice-explanation": "Els <b>paràmetres obsolets</b> mostra els paràmetres detectats en aquest wiki, els quals s'ha planejat que se suprimiran o canviaran en una versió futura.", "smw-admin-deprecation-notice-title-replacement": "Paràmetres substituïts o canviats de nom", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Paràmetres substituïts o canviats</b> conté paràmetres que s'han canviat de nom o que s'han modificat d'una altra manera i es recomana actualitzar immediatament el seu nom o format. Es recomana que s'actualitzi el seu nom o format immediatament.", "smw-admin-deprecation-notice-title-removal": "Paràmetres suprimits", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Paràmetres eliminats</b> identifica els paràmetres que s'han eliminat en una versió anterior però que s'han detectat que encara s'utilitzen en aquest wiki.", "smw-admin-deprecation-notice-section-legend": "<PERSON><PERSON><PERSON><PERSON>", "smw-smwadmin-refresh-title": "Reparació de les dades i actualització", "smw_smwadmin_datarefresh": "Reconstrucció de les dades", "smw_smwadmin_datarefreshdocu": "És possible restaurar totes les dades del Semantic MediaWiki a partir dels continguts actuals del wiki.\nAixò pot ser útil per a reparar dades inconsistents o refrescar les dades si el format intern ha canviat per alguna actualització de programari.\nL'actualització s'executa pàgina per pàgina i no es completarà immediatament.\nA continuació és mostra si l'actualització és en curs i us permet iniciar o aturar les actualitzacions (a menys que aquesta característica estigui inhabilitada per l'administrador del lloc).", "smw_smwadmin_datarefreshprogress": "<strong>Ja hi ha una actualització en curs.</strong>\nÉs normal que l'actualització progressi només lentament perquè només refresca les dades en trossos petits cada vegada que l'usuari accedeix al wiki.\nPer a finalitzar l'actualització més ràpidament, podeu cridar l'script de manteniment del MediaWiki <code>runJobs.php</code> (empreu l'opció <code>--maxjobs 1000</code> per a restringir el nombre d'actualitzacions en un lot).\nProgrés estimat de l'actualització actual:", "smw_smwadmin_datarefreshbutton": "Programa la reconstrucció de les dades", "smw_smwadmin_datarefreshstop": "Atura l'actualització", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, estic {{GENDER:$1|segur|segura}}.", "smw-admin-job-scheduler-note": "Les tasques (les habilitades) d'aquesta secció es realitzen mitjançant la cua de treballs per evitar situacions de bloqueig durant la seva execució. La [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cua de treballs] és responsable del processament i és fonamental que el script de manteniment <code>runJobs.php</code> tingui una capacitat adequada (vegeu també el paràmetre de configuració <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Alliberament d'entitats obsoletes", "smw-admin-outdateddisposal-intro": "Algunes activitats (un canvi de tipus de propietat, l'eliminació de pàgines wiki o la correcció de valors erronis) resultarà en [https://www.semantic-mediawiki.org/wiki/Outdated_entities entitats obsoletes] i se suggereix suprimir-les periòdicament per tal d'alliberar l'espai de taula associat.", "smw-admin-outdateddisposal-active": "S'ha posat en cua una tasca d'alliberament d'entitats obsoletes.", "smw-admin-outdateddisposal-button": "Programa una eliminació", "smw-admin-feature-disabled": "S'ha inhabilitat aquesta funcionalitat en el wiki, consulteu la pàgina d'ajuda dels <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">paràmetres de configuració</a> o contacteu amb l'administrador del sistema.", "smw-admin-propertystatistics-title": "Reconstrucció de les estadístiques de les propietats", "smw-admin-propertystatistics-active": "S'ha programat una tasca de reconstrucció de les estadístiques.", "smw-admin-propertystatistics-button": "Programa la reconstrucció de les estadístiques", "smw-admin-fulltext-title": "Reconstrucció de la cerca en text complet", "smw-admin-fulltext-intro": "Reconstrueix l'índex de cerca de les taules de propietats amb un tipus de dades  [https://www.semantic-mediawiki.org/wiki/Full-text cerca de text complet] habilitat. Els canvis a les regles d'indexació (separadors modificats, noves arrels, etc.) i/o taules noves o afegides necessiten que es torni a executar la tasca de nou.", "smw-admin-fulltext-active": "S'ha programat una tasca de reconstrucció de cerca de text complet.", "smw-admin-fulltext-button": "Programa una reconstrucció de text sencer", "smw-admin-support": "Com obtenir <PERSON>", "smw-admin-supportdocu": "Es proporcionen diferents recursos per ajudar-vos en cas de problemes:", "smw-admin-installfile": "Si us trobeu amb problemes amb la vostra instal·lació, comenceu comprovant les instruccions del <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">fitxer INSTALL</a> i la <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">pàgina d'instal·lació</a>.", "smw-admin-smwhomepage": "Podeu trobar la documentació d'usuari completa del Semantic MediaWiki a <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Els errors es poden informar al <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">issue tracker (localitzador de problemes)</a>, la pàgina <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">reporting bugs (informar d'errors)</a> proporciona algunes indicacions sobre com escriure un informe de problema efectiu.", "smw-admin-questions": "Si teniu més preguntes o suggeriments, uniu-vos a la discussió a la <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">llista de distribució de correus d'usuaris</a> Semantic MediaWiki.", "smw-admin-other-functions": "Altres funcions", "smw-admin-statistics-extra": "Funcions d'estadístiques", "smw-admin-statistics": "Estadístiques", "smw-admin-supplementary-section-title": "Funcions suplementàries", "smw-admin-supplementary-section-subtitle": "Funcions centrals implementades", "smw-admin-supplementary-section-intro": "Aquesta secció proporciona funcions addicionals més enllà de l'abast de les tasques de manteniment i és possible que algunes de les funcions llistades (vegeu la [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentació]) estiguin restringides o no disponibles i, per tant, siguin inaccessibles en aquest wiki.", "smw-admin-supplementary-settings-title": "Configuració i paràmetres", "smw-admin-supplementary-settings-intro": "<u>$1</u> mostra els paràmetres que defineixen el comportament de Semantic MediaWiki", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Estadístiques operacionals", "smw-admin-supplementary-operational-statistics-short-title": "estadístiques operacionals", "smw-admin-supplementary-operational-statistics-intro": "Mostra un conjunt estès d'estadístiques de <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Cerca i alliberament d'entitats", "smw-admin-supplementary-idlookup-short-title": "consulta i rebuig d'entitats", "smw-admin-supplementary-idlookup-intro": "Implementa una funció simple de <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Cerca d’entitats duplicades", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> per a trobar entitats que estan categoritzades com a duplicades per a la taula seleccionada", "smw-admin-supplementary-duplookup-docu": "En aquesta pàgina es mostren les entrades de les taules seleccionades que s'han classificat com a [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities duplicades]. Les entrades duplicades només haurien de produir-se (si s'escau) en rares ocasions, potencialment causades per una actualització finalitzada o una transacció de retrocés sense èxit.", "smw-admin-supplementary-operational-statistics-cache-title": "Estadístiques de la memòria cau", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> mostra un conjunt seleccionat d'estadístiques relacionades amb la memòria cau", "smw-admin-supplementary-operational-table-statistics-title": "Estadístiques de taula", "smw-admin-supplementary-operational-table-statistics-short-title": "estadístiques de taula", "smw-admin-supplementary-operational-table-statistics-intro": "Genera <u>$1</u> per a un conjunt seleccionat de taules", "smw-admin-supplementary-operational-table-statistics-explain": "La secció conté estadístiques de taula seleccionades per a ajudar als administradors i als curadors de dades a prendre decisions informades sobre l'estat del rerefons i del motor d'emmagatzematge.", "smw-admin-supplementary-operational-table-statistics-legend": "La llegenda descriu algunes de les claus utilitzades per a les estadístiques de taula i inclou:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> nombre total de files en una taula", "smw-admin-supplementary-elastic-version-info": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> mostra detalls de paràmetres i estadístiques d'indexació", "smw-admin-supplementary-elastic-docu": "Aquesta pàgina conté informació de configuració, correspondència, salut i estadístiques d'indexació relacionades amb un clúster d'Elasticsearch que està connectat a Semantic MediaWiki i el seu [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Funcions permeses", "smw-admin-supplementary-elastic-settings-title": "Paràmetres (índexs)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> utilitzat per Elasticsearch per gestionar els índexs de Semantic MediaWiki", "smw-admin-supplementary-elastic-mappings-title": "Correspondències", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> per llistar índex i correspondències de camps", "smw-admin-supplementary-elastic-mappings-docu": "Aquesta pàgina conté detalls de la correspondència de camps que s'utilitzen amb els índexs actuals. Es recomana monitorar les correspondències en connexió amb <code>index.mapping.total_fields.limit</code> (que especifica el nombre màxim de camps permesos en un índex).", "smw-admin-supplementary-elastic-mappings-summary": "Resum", "smw-admin-supplementary-elastic-mappings-fields": "Correspondències de camp", "smw-admin-supplementary-elastic-nodes-title": "Nodes", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> mostra les estadístiques de node", "smw-admin-supplementary-elastic-indices-title": "Índexs", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> proporciona un resum dels índexs disponibles i llurs estadístiques", "smw-admin-supplementary-elastic-statistics-title": "Estadístiques", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> mostra estadístiques de nivell d'índex", "smw-admin-supplementary-elastic-status-replication": "Estat de replicació", "smw-admin-supplementary-elastic-status-last-active-replication": "Darrera replicació activa: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Interval de refrescament: $1", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replicació bloquejada: $1 (reconstrucció en procés)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Monitoratge de replicació (activa): $1", "smw-admin-supplementary-elastic-replication-header-title": "Estat de replicació", "smw-admin-supplementary-elastic-replication-function-title": "Replicació", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> mostra informació de les replicacions que han fallat", "smw-admin-supplementary-elastic-replication-files": "Fitxers", "smw-admin-supplementary-elastic-replication-pages": "Pàgines", "smw-admin-supplementary-elastic-endpoints": "Extrems", "smw-admin-supplementary-elastic-config": "Configuracions", "smw-list-count": "La llista conté $1 {{PLURAL:$1|entrada|entrades}}.", "smw-property-label-similarity-title": "Informe de similitud d'etiquetes de propietat", "smw-property-label-similarity-intro": "<u>$1</u> calcula les similituds de les etiquetes de propietats existents", "smw-property-label-similarity-threshold": "Llindar:", "smw-property-label-similarity-type": "Mostra l'ID de tipus", "smw-property-label-similarity-noresult": "No s'han trobat resultats per a les opcions seleccionades.", "smw-admin-operational-statistics": "Aquesta pàgina conté estadístiques operacionals recollides a partir de funcions relacionades amb Semantic MediaWiki. Es pot trobar una llista estesa d'estadístiques específiques del wiki [[Special:Statistics|<b>aquí</b>]].", "smw_adminlinks_datastructure": "Estructura de dades", "smw_adminlinks_displayingdata": "Visualització de les dades", "smw_adminlinks_inlinequerieshelp": "Ajuda de les consultes en línia", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Recompte d'ús] estimat: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Propietat definida {{PLURAL:$1|per l'usuari|pel sistema}}", "smw-property-indicator-last-count-update": "Recompte d'ús estimat\nDarrera actualització: $1", "smw-concept-indicator-cache-update": "Recompte de la memòria cau\nDarrera actualització: $1", "smw-createproperty-isproperty": "És una propietat del tipus $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|El valor permès per a aquestapropietat és|Els valors permesos per a aquestes propietats són}}:", "smw-paramdesc-category-delim": "El delimitador", "smw-paramdesc-category-template": "Una plantilla per donar format als elements amb", "smw-paramdesc-category-userparam": "Un paràmetre per passar a la plantilla", "smw-info-par-message": "Missatge per mostrar.", "smw-info-par-icon": "La icona que es mostrarà, o bé «info», o bé «avís».", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Opcions generals", "prefs-extended-search-options": "Cerca estesa", "prefs-ask-options": "Cerca semàntica", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] i extensions relacionades proporcionen personalització individual per a un grup de característiques i funcions seleccionades. Hi ha una llista de paràmetres individuals amb la seva descripció i característiques a la següent [https://www.semantic-mediawiki.org/wiki/Help:User_preferences pàgina d'ajuda].", "smw-prefs-ask-options-tooltip-display": "Mostra el text del paràmetre com a descripció d'informació a la pàgina especial #ask [[Special:Ask|query builder]].", "smw-prefs-ask-options-compact-view-basic": "Habilita la vista compacta bàsica", "smw-prefs-help-ask-options-compact-view-basic": "<PERSON> s'habilita, mostra un conjunt reduït d'enllaços en la vista compacta d'Special:Ask.", "smw-prefs-general-options-time-correction": "Habilita la correcció horària per a les pàgines especials que utilitzin la preferència de [[Special:Preferences#mw-prefsection-rendering|decalatge horari]] local", "smw-prefs-general-options-jobqueue-watchlist": "Mostra la llista de seguiment de la cua de tasques a la meva barra personal", "smw-prefs-general-options-disable-editpage-info": "Inhabilita el text introductori en la pàgina d'edició", "smw-prefs-general-options-disable-search-info": "Inhabilita la informació d'ajuda a la sintaxi en la pàgina estàndard de cerca", "smw-prefs-general-options-suggester-textinput": "Habilita l'assistència d'entrada per a les entitats semàntiques", "smw-ui-tooltip-title-property": "Propietat", "smw-ui-tooltip-title-quantity": "Conversió d'unitat", "smw-ui-tooltip-title-info": "Informació", "smw-ui-tooltip-title-service": "Enllaços de servei", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-error": "Error", "smw-ui-tooltip-title-parameter": "Paràmetre", "smw-ui-tooltip-title-event": "Esdeveniment", "smw-ui-tooltip-title-note": "<PERSON>a", "smw-ui-tooltip-title-legend": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-reference": "Referència", "smw_unknowntype": "El tipus «$1» d'aquesta propietat no és vàlid", "smw-concept-cache-text": "El concepte té un total {{PLURAL:$1|d'$1 pàgina|de $1 pàgines}} i es va actualitzar per darrera vegada el $3, $2.", "smw_concept_header": "Pàgines del concepte \"$1\"", "smw_conceptarticlecount": "Es {{PLURAL:$1|mostra|mostren}} a continuació $1 {{PLURAL:$1|pàgina|pàgines}}.", "smw-qp-empty-data": "No s'han pogut mostrar les dades sol·licitades per manca de criteris de selecció suficients.", "right-smw-admin": "Accedir a les tasques d'administració (Semantic MediaWiki)", "right-smw-patternedit": "Modificar l'accés per a mantenir les expressions regulars i patrons permesos (Semantic MediaWiki)", "right-smw-pageedit": "Modificar l'accés per a pàgines anotades amb <code>Is edit protected</code> (Semantic MediaWiki)", "restriction-level-smw-pageedit": "protegit (només usuaris elegibles)", "action-smw-patternedit": "modificar les expressions regulars que utilitza Semantic MediaWiki", "action-smw-pageedit": "modificar pàgines anotades amb <code>Is edit protected</code> (Semantic MediaWiki)", "group-smwadministrator": "Administradors (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrador|administratora}} (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Administrators (Semantic MediaWiki)", "group-smwcurator": "Curadors (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|conservador (Semantic MediaWiki)|conservadora (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:Conservadors (Semantic MediaWiki)", "group-smweditor": "Editors (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|editor (Semantic MediaWiki)|editora (Semantic MediaWiki)}}", "action-smw-admin": "accedir a les tasques d'administració de Semantic MediaWiki", "action-smw-ruleedit": "editar les pàgines de regla (Semantic MediaWiki)", "smw-property-predefined-default": "\"$1\" és una propietat predefinida del tipus $2.", "smw-property-predefined-common": "Aquesta propietat està predesplegada (també coneguda com a [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propietat especial]) i ve amb privilegis administratius addicionals, però pot utilitzar-se com qualsevol altra [https://www.semantic-mediawiki.org/wiki/Property propietat definida per l'usuari].", "smw-property-predefined-ask": "«$1» és una propietat predefinida que representa metainformació (en la forma de [https://www.semantic-mediawiki.org/wiki/Subobject subobjectes]) sobre consultes individuals i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "«$1» és una propietat predefinida que recull el nombre de condicions que s'utilitzen en una consulta i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "«$1» és una propietat predefinida que informa de la profunditat d'una consulta i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "És un valor numèric computat en la base de la subconsulta niuada, cadenes de propietat, i elements de descripció disponibles amb l'execució d'una consulta que és restringida pel paràmetre de configuració <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-long-askpa": "És part d'una col·lecció de propietats que especifiquen un [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler perfil de consulta].", "smw-sp-properties-docu": "Aquesta pàgina llista les [https://www.semantic-mediawiki.org/wiki/Property propietats] i el seu recompte d'ús en el wiki. Per a una estadística al dia, és recomanable córrer l'script de manteniment [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics rebuildPropertyStatistics] de forma regular. Per a una visió diferenciada, vegeu les pàgines especials de les [[Special:UnusedProperties|propietats no utilitzades]] o les [[Special:WantedProperties|propietats per definir]].", "smw-sp-properties-cache-info": "Les dades llistades s'han recuperat de la [https://www.semantic-mediawiki.org/wiki/Caching memòria cau], i s'han actualitzat el $1.", "smw-sp-properties-header-label": "Llista de propietats", "smw-admin-settings-docu": "Mostra una llista de tots els paràmetres per defecte i localitzats que són rellevants per a un entorn de Semantic MediaWiki. Per a més detalls sobre cada paràmetre en particular, consulteu la pàgina d'ajuda de la [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuració].", "smw-sp-admin-settings-button": "Genera la llista de paràmetres", "smw-admin-idlookup-title": "Consulta", "smw-admin-idlookup-docu": "Aquesta secció mostra els detalls tècnics d'una entitat individual (pàgina wiki, subobjecte, propietat, etc.) a Semantic MediaWiki. L'entrada pot ser un ID numèric o un valor de cadena per coincidir amb el camp de cerca rellevant, tot i que qualsevol referència ID està relacionada amb Semantic MediaWiki i no la pàgina de MediaWiki o l'ID de la revisió.", "smw-admin-iddispose-title": "Alliberament", "smw-admin-iddispose-docu": "Caldria notar que l'operació d'alliberament no està restringida i que suprimirà l'entitat del motor d'emmagatzematge juntament amb les seves referències en les taules pendents si es confirma. Realitzeu la tasca '''amb compte''' i només després de consultar la [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentació].", "smw-admin-iddispose-done": "L'ID «$1» s'ha suprimit del servidor d'emmagatzematge.", "smw-admin-iddispose-references": "L'ID «$1» {{PLURAL:$2|no té cap|té com a mínim una}} referència activa:", "smw-admin-iddispose-references-multiple": "Llista de coincidències amb com a mínim un registre de referència actiu.", "smw-admin-iddispose-no-references": "La cerca no ha pogut trobar «$1» a cap entrada de taula.", "smw-admin-idlookup-input": "Cerca:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Resum", "smw-admin-tab-notices": "Avisos d’obsolescència", "smw-admin-tab-maintenance": "Manteniment", "smw-admin-tab-supplement": "Funcions suplementàries", "smw-admin-tab-registry": "Registre", "smw-admin-tab-alerts": "<PERSON><PERSON><PERSON>", "smw-admin-alerts-tab-deprecationnotices": "Avisos d'obsolescència", "smw-admin-alerts-tab-maintenancealerts": "Alertes de manteniment", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Optimització de taules", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Entitats desfasades", "smw-admin-maintenancealerts-invalidentities-alert-title": "Entitats no vàlids", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Paràmetres", "smw-admin-configutation-tab-namespaces": "Espais de nom", "smw-admin-configutation-tab-schematypes": "Tipus d'esquema", "smw-admin-maintenance-tab-tasks": "Tasques", "smw-admin-maintenance-tab-scripts": "Scripts de manteniment", "smw-admin-maintenance-no-description": "Cap descripció.", "smw-admin-maintenance-script-section-title": "Llista d'scripts de manteniment disponibles", "smw-admin-maintenance-script-section-intro": "Els scripts de manteniment següents necessiten permisos d'administrador i accés a la línia d'ordres per tal de poder executar-los.", "smw-admin-maintenance-script-description-dumprdf": "Export RDF dels triples existents.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Cerca les entitats que no són a Elasticsearch (només per a instal·lacions que utilitzen <code>ElasticStore</code>) i programeu les tasques d'actualització apropiades.", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Reconstrueix les estadístiques d'ús per a totes les entitats de propietat.", "smw-admin-maintenance-script-description-removeduplicateentities": "Suprimeix les entitats duplicades que es troben en les taules seleccionades que no tenen cap referència activa.", "smw-admin-maintenance-script-description-purgeentitycache": "Purga les entrades cau de les entitats conegudes i llurs dades associades.", "smw-admin-maintenance-script-section-update": "Scripts d'actualització", "smw-admin-maintenance-script-section-rebuild": "Scripts de reconstrucció", "smw-livepreview-loading": "S'està carregant…", "smw-sp-searchbyproperty-description": "Aquesta pàgina proporciona una simple [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces interfície de navegació] per trobar entitats descrites per una propietat i un valor anomenat. Altres interfícies de cerca disponibles inclouen la [[Special:PageProperty|cerca de propietats de pàgina]] i el [[Special:Ask|generador de consultes ask]].", "smw-sp-searchbyproperty-resultlist-header": "Llista de resultats", "smw-sp-searchbyproperty-nonvaluequery": "Una llista de valors que tenen la propietat «$1» assignada.", "smw-sp-searchbyproperty-valuequery": "Una llista de pàgines que tenen la propietat «$1» amb el valor «$2» anotat.", "smw-datavalue-number-textnotallowed": "«$1» no pot assignar-se a un tipus de nombre declarat amb el valor $2.", "smw-datavalue-number-nullnotallowed": "«$1» ha tornat «NULL», que no és un nombre vàlid.", "smw-editpage-annotation-enabled": "Aquesta pàgina permet anotacions semàntiques en el text (p. ex., <nowiki>«[[Is specified as::World Heritage Site]]» (es considera com patrimoni de la Humanitat)</nowiki>) per a crear contingut estructurat i consultable proporcionat per Semantic MediaWiki. Per a una descripció detallada de com utilitzar les anotacions o la funció d'anàlisi #ask consulteu les pàgines d'ajuda [https://www.semantic-mediawiki.org/wiki/Help:Getting_started Primers passos], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation Anotació en el text] o [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries Consultes en el text].", "smw-editpage-annotation-disabled": "Aquesta pàgina no permet incloure anotacions semàntiques en el text per restriccions de l'espai de noms. Teniu més detalls de com habilitar l'espai de noms a la pàgina d'ajuda de la [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuració].", "smw-editpage-property-annotation-enabled": "Aquesta propietat pot ampliar-se mitjançant anotacions semàntiques per a especificar un tipus de dades (p. ex.,<nowiki>«[[</nowiki>Té tipus::Pàgina]]») o d'altres declaracions d'aquesta mena (p. ex.,<nowiki>«[[</nowiki>Subpropietat de::dc:date]]»). Per a una descripció sobre com augmentar aquesta pàgina, consulteu les pàgines d'ajuda de [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration Declaració d'una propietat] o [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes Llista de tipus de dades].<!--[[Has type::Page]], [[Subproperty of::dc:date]]-->", "smw-editpage-property-annotation-disabled": "Aquesta propietat no pot ampliar-se amb una anotació de tipus de dades (p. ex., <nowiki>«[[</nowiki>Té tipus::Pàgina]]») perquè ja està predefinida (consulteu la pàgina d'ajuda de les [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propietats especials] per a més informació).<!-- [[Has type::Page]]-->", "smw-editpage-concept-annotation-enabled": "Aquest concepte pot ser ampliat fent servir la funció d'anàlisi #concept. Per a una descripció de com utilitzar-la, consulteu la pàgina d'ajuda sobre els [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceptes].", "smw-search-help-structured": "Cerques estructurades:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (comme [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context context filtrat])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (avec un [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context context de cerca])", "smw-search-help-proximity": "Cerques de proximitat (una propietat que sigui desconeguda, '''només''' disponible per a aquells sistemes que proporcionen integració de cerca de text complet):\n\n* <code><nowiki>[[in:lorem ipsum]]</nowiki></code> (cerca a tots els documents «lorem» i «ipsum» que han estat indexats)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (troba la coincidència «lorem ipsum» com a frase)", "smw-search-input": "Entrada i cerca", "smw-search-syntax": "Sintaxi", "smw-search-profile": "Estès", "smw-search-profile-tooltip": "Funcions de cerca en connexió amb Semantic MediaWiki", "smw-search-profile-sort-best": "Millor coincidència", "smw-search-profile-sort-recent": "Més recent", "smw-search-profile-sort-title": "Títol", "smw-search-profile-extended-help-intro": "El [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile perfil estès] Special:Search proporciona accés a les funcions de cerca específiques de Semantic MediaWiki i del seu motor de consulta.", "smw-search-profile-extended-help-sort": "Especifica una preferència d'ordenació per a la mostra de resultats amb:", "smw-search-profile-extended-help-sort-title": "* «Títol» fent servir el títol de la pàgina (o el títol mostrat) com a criteri d'ordenació", "smw-search-profile-extended-help-sort-recent": "* «Més recent» mostrà les entrades modificades més recentment abans (les entitats que són subobjectes se suprimiran perquè no estan anotades amb una[[Property:Modification date|data de modificació]])", "smw-search-profile-extended-help-form": "Els formularis es proporcionen (sempre que es mantinguin al dia) per contenir correspondències de casos d'ús específics tot exposant diferents propietats i camps de valor per ajustar els processos d'entrada i facilitar als usuaris procedir amb la sol·licitud de cerca (vegeu $1).", "smw-search-profile-extended-help-namespace": "El quadre de selecció d'espai de noms s'amagarà tan aviat com se seleccioni el formulari, però es pot fer visible amb l'ajuda del botó «mostra/amaga».", "smw-search-profile-extended-help-search-syntax": "El camp d'entrada de cerca permet l'ús de sintaxi <code>#ask</code> per definir un context de cerca específic de Semantic MediaWiki. Les expressions útils inclouen:", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> per no fer correspondència amb cap entitat que inclou \"...\"", "smw-search-profile-extended-help-search-syntax-prefix": "* Prefixos personalitzats addicionals són disponibles i definits com: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Algunes expressions són reservades, com ara: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "«Algunes de les operacions llistades només són útils en connexió amb un índex de text complet habilitat o bé amb l'ElasticStore.»", "smw-search-profile-extended-help-query": "S'ha utilitzat <code><nowiki>$1</nowiki></code> com a consulta.", "smw-search-profile-extended-help-query-link": "(Per a més detalls, utilitzeu $1).", "smw-search-profile-extended-help-find-forms": "formularis disponibles", "smw-search-profile-extended-section-sort": "Ordena per", "smw-search-profile-extended-section-form": "Formularis", "smw-search-profile-extended-section-search-syntax": "Entrada de cerca", "smw-search-profile-extended-section-namespace": "Espai de noms", "smw-search-profile-extended-section-query": "Consulta", "smw-search-profile-link-caption-query": "constructor de consultes", "smw-search-show": "Mostra", "smw-search-hide": "<PERSON><PERSON>", "log-name-smw": "Registre de Semantic MediaWiki", "log-show-hide-smw": "Registre de Semantic MediaWiki $1", "logeventslist-smw-log": "Registre de Semantic MediaWiki", "log-description-smw": "Activitats dels [https://www.semantic-mediawiki.org/wiki/Help:Logging tipus d'esdeveniments habilitats] que han estat informats per Semantic MediaWiki i els seus components.", "logentry-smw-maintenance": "Esdeveniments relacionats amb el manteniment emesos per Semantic MediaWiki", "smw-datavalue-import-unknown-namespace": "L'espai de noms d'importació «$1» és desconegut. Assegureu-vos que els detalls d'importació OWL són disponibles a través de [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "No s'ha pogut trobat un URI de l'espai de noms «$1» en [[MediaWiki:Smw import $1|la importació de $1]].", "smw-datavalue-import-missing-type": "No s'ha trobat definició de tipus per a «$1» a la [[MediaWiki:Smw import $2|importació de $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|importació de $1]]", "smw-datavalue-import-invalid-value": "«$1» no és format vàlid i s'espera que consisteixi de «espai de noms»:«identificador» (p. ex. «foaf:name»).", "smw-datavalue-import-invalid-format": "S'esperava que la cadena «$1» estigués dividida en quatre parts però no s'ha entès el format.", "smw-property-predefined-impo": "«$1» és una propietat predefinida que descriu una relació amb un [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabulari importat] i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "«$1» és una propietat predefinida que descriu el [[Special:Types|tipus de dades]] d'una propietat i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "«$1» és una propietat predefinida que representa un constructe de [https://www.semantic-mediawiki.org/wiki/Help:Container contenidor] i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "El contenidor permet acumular assignacions de propietat-valor similars al d'una pàgina wiki normal però en un espai d'entitat diferent mentre que està enllaçat al subjecte que l'inclou.", "smw-property-predefined-errp": "«$1» és una propietat predefinida per a resseguir errors d'entrada de valors d'anotacions irregulars i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "En la majoria dels casos és causat per un desajustament de tipus o una restricció [[Property:Allows value|value]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value «$1»] és una propietat predefinida que defineix una llista de valors permesos per tal de restringir les assignacions de valor d'una propietat i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "La propietat «$1» té una àrea d'aplicació restringida i els usuaris no poden utilitzar-la com a propietat d'anotació.", "smw-datavalue-property-restricted-declarative-use": "La propietat «$1» és una propietat declarativa i només pot utilitzar-se en una pàgina de propietat o categoria.", "smw-datavalue-property-create-restriction": "La propietat «$1» no existeix i l'usuari no té el permís «$2» (consulteu [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode el mode d'autoritat]) per crear o anotar valors amb una propietat no aprovada.", "smw-datavalue-property-invalid-character": "«$1» conté un caràcter «$2» llistat com a part d'una etiqueta de propietat i que s'ha classificat llavors com a no vàlid.", "smw-datavalue-property-invalid-chain": "L'ús de «$1» com a cadena de propietat no està permès durant el procés d'anotació.", "smw-datavalue-restricted-use": "El valor de dades s'ha marcat per a ús restringit «$1».", "smw-datavalue-invalid-number": "«$1» no es pot interpretar com un nombre.", "smw-query-condition-circular": "S'ha detectat una possible condició circular a «$1».", "smw-query-condition-empty": "La descripció de la consulta té una condició en blanc.", "smw-types-list": "Llista de tipus de dades", "smw-types-default": "«$1» és un tipus de dades integrat.", "smw-types-help": "Es poden trobar més informació i exemples a la [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 pàgina d'ajuda].", "smw-type-anu": "«$1» és una variant del tipus de dades [[Special:Types/URL|URL]] i s'utilitza sobretot per a una declaració d'exportació de ''owl:AnnotationProperty''.", "smw-type-boo": "«$1» és un tipus de dades bàsic per descriure un valor de veritable/fals.", "smw-type-cod": "«$1» és una variant del tipus de dades [[Special:Types/Text|Text]] per utilitzar-lo amb texts tècnics de longitud arbitrària, com ara llistats de codi font.", "smw-type-geo": "«$1» és un tipus de dades que descriu ubicacions geogràfiques i que necessita l'extensió [https://www.semantic-mediawiki.org/wiki/Extension:Maps Maps] per proporcionar funcionalitat addicional.", "smw-type-tel": "«$1» és un tipus de dades especial per a descriure números de telèfon internacionals d'acord amb el RFC 3966.", "smw-type-txt": "«$1» és un tipus de dades bàsic per descriure cadenes de longitud arbitrària.", "smw-type-dat": "«$1» és un tipus de dades bàsic per representar punts en el temps en un format unificat.", "smw-type-ema": "«$1» és un tipus de dades especial que representa una adreça electrònica.", "smw-type-tem": "«$1» és un tipus de dades numèric especial que representa una temperatura.", "smw-type-qty": "«$1» és un tipus de dades per descriure quantitats amb una representació numèrica i una unitat de mesura.", "smw-type-rec": "«$1» és un tipus de dades de contenidor que especifica una llista de propietats amb tipus en un ordre fix.", "smw-type-extra-tem": "L'esquema de conversió inclou unitats permeses, com a<PERSON>, <PERSON><PERSON><PERSON>, Fahrenheit i Rankine.", "smw-type-tab-properties": "Propietats", "smw-type-tab-types": "<PERSON><PERSON><PERSON>", "smw-type-tab-type-ids": "IDs de tipus", "smw-type-tab-errors": "Errors", "smw-type-primitive": "Bàsic", "smw-type-contextual": "Contextual", "smw-type-compound": "Compost", "smw-type-container": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-no-group": "Sense classificar", "smw-special-pageproperty-description": "Aquesta pàgina proporciona una interfície de navegació por trobar tots els valors d'una propietat i una pàgina donada. Altres interfícies de cerca disponibles inclouen la [[Special:SearchByProperty|cerca de propietat]] i el [[Special:Ask|constructor de consultes]].", "smw-property-predefined-errc": "«$1» és una propietat predefinida proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] i que representa errors que van aparèixer en connexió amb anotacions de valors o processament d'entrades no adequats.", "smw-property-predefined-long-errc": "Els errors es recullen en un [https://www.semantic-mediawiki.org/wiki/Help:Container contenidor] que pot incloure una referència a la propietat que ha causat la discrepància.", "smw-property-predefined-errt": "«$1» és una propietat predefinida que conté una descripció textual d'un error i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Un subobjecte definit per l'usuari que conté un esquema de noms no vàlid. La notació de punt ($1) utilitzada en els primers cinc caràcters està reservada per a extensions. Podeu definir un [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier identificador amb nom].", "smw-datavalue-record-invalid-property-declaration": "La definició del registre conté la propietat «$1» que és declarada ella mateixa com a de tipus registre, i això no és permès.", "smw-property-predefined-mime": "«$1» és una propietat predefinida que descriu el tipus MIME d'un fitxer carregat i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-unit": "Una llista separada per comes permet descriure unitats o formats a utilitzar per a la visualització.", "smw-datavalue-monolingual-dataitem-missing": "Manca un element esperat per a construir un valor compost monolingüe.", "smw-datavalue-languagecode-missing": "Per a l'anotació «$1», l'analitzador no ha pogut determinar el codi de la llengua (p. ex., «foo@en»).", "smw-datavalue-languagecode-invalid": "No s'ha reconegut «$1» com a codi de llengua permès.", "smw-property-predefined-lcode": "«$1» una propietat predefinida que representa un codi de llengua formatat d'acord amb BCP47 i està proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Temps d'anàlisi de l'anotació en línia", "smw-limitreport-intext-postproctime": "[SMW] durada de postprocessament", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|segon|segons}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|segon|segons}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Temps d'actualització de l'emmagatzematge (en la purga de la pàgina)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|segon|segons}}", "smw_allows_pattern": "S'espera que aquesta pàgina contingui una llista de referències (seguit per unes  [https://ca.wikipedia.org/wiki/Expressi%C3%B3_regular expressions regulars]) que seran disponibles per a la propietat [[Property:Allows pattern|Permet patró]]. Per editar la pàgina cal el permís <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "S'ha classificat «$1» com a no vàlid per l'expressió regular «$2».", "smw-datavalue-allows-pattern-reference-unknown": "La referència de patró «$1» no ha pogut coincidir amb una entrada en  [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "La referència de llista «$1» no correspon a una pàgina de [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "Al contingut de la llista «$1» hi manquen elements amb un marcador de llista *.", "smw-datavalue-feature-not-supported": "La característica «$1» no és compatible amb el wiki o bé se n'ha inhabilitat.", "smw-property-predefined-long-pvuc": "La unicitat s'estableix quan dos valors no són iguals en llur representació literal. Llavors qualsevol violació d'aquesta restricció es categoritzarà com error.", "smw-datavalue-constraint-uniqueness-violation": "La propietat «$1» només permet assignacions de valor únic i «$2» ja estava anotada en el subjecte «$3».", "smw-datavalue-constraint-uniqueness-violation-isknown": "La propietat «$1» només permet anotacions de valor úniques. «$2» ja conté un valor assignat. «$3» viola la restricció d'unicitat.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "La restricció <code>allowed_namespaces</code> requereix un tipus de pàgina.", "smw-constraint-schema-category-invalid-type": "L'esquema anotat «$1» no és vàlid per a una categoria. Li cal un tipus «$2».", "smw-constraint-schema-property-invalid-type": "L'esquema anotat «$1» no és vàlid per a una propietat. Li cal un tipus «$2».", "smw-constraint-error-allows-value-list": "«$1» no és a la llista ($2) de [[Property:Allows value|valors permesos]] de la propietat «$3».", "smw-datavalue-time-invalid-offset-zone-usage": "«$1» conté un decalatge i un fus horari no permesos.", "smw-datavalue-time-invalid-values": "El valor «$1» conté informació no interpretable en la forma de «$2».", "smw-datavalue-time-invalid-date-components-common": "«$1» conté informació que no és interpretable.", "smw-datavalue-time-invalid-date-components-dash": "«$1» conté un guió extrínsec o altres caràcters que no són vàlids per a interpretar una data.", "smw-datavalue-time-invalid-date-components-empty": "«$1» conté alguns components buits.", "smw-datavalue-time-invalid-date-components-three": "«$1» conté més que no tres components necessaris per a la interpretació de les dates.", "smw-datavalue-time-invalid-date-components-sequence": "«$1» conté una seqüència que no s'ha pogut interpretar amb una matriu de coincidències de components de dates.", "smw-datavalue-time-invalid-ampm": "«$1» conté «$2» com element d'hora, que no és vàlid per a la convenció de 12 hores.", "smw-datavalue-external-formatter-invalid-uri": "«$1» és un URL no vàlid.", "smw-datavalue-external-identifier-formatter-missing": "A la propietat li manca una assignació d'[[Property:External formatter uri|«URI de formatador extern»]]", "smw-datavalue-keyword-maximum-length": "La paraula clau ha excedit la longitud màxima de $1 {{PLURAL:$1|caràcter|caràcters}}.", "smw-datavalue-parse-error": "El valor donat «$1» no s'ha entès.", "smw-datavalue-propertylist-invalid-property-key": "La llista de propietats «$1» contenia una clau de propietat «$2» no vàlida.", "smw-datavalue-type-invalid-typeuri": "El tipus «$1» no s'ha pogut transformar en una representació URI vàlida.", "smw-datavalue-wikipage-missing-fragment-context": "No es pot utilitzar el valor d'entrada wikipage «$1» sense una pàgina de context.", "smw-datavalue-wikipage-invalid-title": "El valor d'entrada de tipus pàgina «$1» conté caràcters no vàlids o és incomplet. Això pot causar resultats no esperats en una consulta o en el procés d'anotació.", "smw-datavalue-wikipage-property-invalid-title": "La propietat «$1» (com a tipus de pàgina) amb un valor d'entrada «$2» conté caràcters no vàlids o és incomplet. Això pot causar resultats inesperats durant un procés de consulta o d'anotació.", "smw-datavalue-wikipage-empty": "El valor d'entrada de pàgina wiki és buit (p. ex., <code>[[SomeProperty::]], [[]]</code>) i llavors no es pot utilitzar com a nom o part d'una condició de consulta.", "smw-type-ref-rec": "«$1» és un tipus de [https://www.semantic-mediawiki.org/wiki/Container contenidor] que permet enregistrar informació addicional (p. ex., provinència de les dades) de l'assignació d'un valor.", "smw-parser-invalid-json-format": "L'analitzador de JSON ha retornat amb «$1».", "smw-property-preferred-label-language-combination-exists": "No es pot utilitzar «$1» com etiqueta preferida perquè la llengua «$2» ja està assignada a l'etiqueta «$3».", "smw-clipboard-copy-link": "Copia l'enllaç al porta-retalls", "smw-data-lookup": "S'estan recuperant les dades...", "smw-data-lookup-with-wait": "S'està processant la sol·licitud i pot trigar una estona.", "smw-no-data-available": "No hi ha cap dada disponible.", "smw-property-req-violation-type": "La propietat conté tipus d'especificacions que entren en conflicte entre elles, que pot resultar en anotacions de valors no vàlids. S'espera que un usuari hi assigni un tipus apropiat.", "smw-property-req-error-list": "La propietat conté els errors o avisos següents:", "smw-property-req-violation-parent-type": "La propietat «$1» i la propietat pare assignada «$2» tenen tipus d'anotacions diferents.", "smw-change-propagation-protection": "Es bloqueja la pàgina per tal de prevenir modificacions de dades accidentals mentre s'executa una actualització de la [https://www.semantic-mediawiki.org/wiki/Change_propagation propagació de canvis]. Aquest procés pot trigar una estona abans que es torni a desbloquejar la pàgina i depèn de la mida i de la freqüència del programador de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cua de tasques].", "smw-category-invalid-value-assignment": "«$1» no és reconeguda com a categoria o anotació de valor vàlida.", "protect-level-smw-pageedit": "Permet només usuaris amb permisos d'edició de pàgines (Semantic MediaWiki)", "smw-edit-protection-disabled": "S'ha inhabilitat la protecció de modificació; per tant, «$1» no pot utilitzar-se per protegir pàgines d'entitat de modificacions no autoritzades.", "smw-edit-protection-auto-update": "Semantic MediaWiki ha actualitzat l'estat de protecció d'acord amb la propietat «Té protecció de modificació».", "smw-edit-protection-enabled": "Modifica protegit (Semantic MediaWiki)", "smw-patternedit-protection": "Aquesta pàgina està protegida i només pot ser modificada per usuaris amb els  [https://www.semantic-mediawiki.org/wiki/Help:Permissions permisos] <code>smw-patternedit</code> adequats.", "smw-property-predefined-edip": "«$1» és una propietat predeterminada proporcionada per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] per a indicar si la modificació està protegida o no.", "smw-property-predefined-long-edip": "Tot i que qualsevol usuaris pot afegir aquesta propietat al subjecte, només un usuari amb un permís dedicat pot modificar o revocar la protecció a una entitat després que s'hagi afegit.", "smw-query-reference-link-label": "Referència de consulta", "smw-format-datatable-emptytable": "No hi ha cap disponible a la taula", "smw-format-datatable-info": "Es mostren les entrades_START_ a _END_ de _TOTAL_", "smw-format-datatable-infoempty": "Es mostren 0 de 0 de 0 entrades", "smw-format-datatable-infofiltered": "(filtrades d'un total de _MAX_ entrades)", "smw-format-datatable-infothousands": ".", "smw-format-datatable-lengthmenu": "Mostra _MENU_ entrades", "smw-format-datatable-loadingrecords": "S'està carregant...", "smw-format-datatable-processing": "S'està processant...", "smw-format-datatable-search": "Cerca:", "smw-format-datatable-zerorecords": "No s'ha trobat cap registre que concideixi", "smw-format-datatable-first": "Primer", "smw-format-datatable-last": "Últim", "smw-format-datatable-next": "<PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-previous": "Anterior", "smw-format-datatable-sortascending": ": activa per ordenar la columna de forma ascendent", "smw-format-datatable-sortdescending": ": activa per ordenar la columna de forma descendent", "smw-format-datatable-toolbar-export": "Exporta", "smw-format-list-other-fields-open": "(", "smw-category-invalid-redirect-target": "La categoria «$1» conté un objectiu de redirecció no vàlid a un pàgina que no és un espai de noms de categoria.", "smw-postproc-queryref": "Semantic MediaWiki està refrescant la pàgina actual d'acord amb la condició d'unes consultes de postprocessament obligatòries.", "apihelp-smwinfo-summary": "Mòdul API per recuperar informació d'estadístiques de Semantic MediaWiki i d'altres metadades.", "apihelp-ask-summary": "Mòdul API per consultar Semantic MediaWiki fent servir el llenguatge Ask.", "apihelp-askargs-summary": "Mòdul API per fer consultes a Semantic MediaWiki utilitzant el llenguatge Ask com una llista de condicions, impressions i paràmetres.", "apihelp-browsebyproperty-summary": "Mòdul API per recuperar informació d'una propietat o d'una llista de propietats.", "apihelp-browsebysubject-summary": "Mòdul API per recuperar informació d'un tema.", "apihelp-smwtask-summary": "Mòdul API per executar tasques relacionades amb Semantic MediaWiki (només per a ús intern, no per a ús públic).", "apihelp-smwbrowse-summary": "Mòdul API per permetre les activitats de navegació de diferents tipus d'entitat a Semantic MediaWiki.", "apihelp-ask-parameter-api-version": "Format de sortida:\n;2:Compatibilitat enrere fent servir {} per a la llista de resultats.\n;3:Format experimental que utilitza [] per a la llista de resultats.", "apihelp-smwtask-param-task": "Defineix el tipus de tasca", "smw-api-invalid-parameters": "Paràmetres no vàlids, «$1»", "smw-parser-recursion-level-exceeded": "S'ha excedit el nivell de $1 recurrències durant el procés d'anàlisi. Se suggereix que es valida l'estructura de la plantilla o, si s'escau, que s'ajusti el paràmetre de configuració <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "Es mostren $1 {{PLURAL:$1|pàgina que utilitza|pàgines que utilitzen}} aquesta propietat.", "smw-property-page-list-search-count": "Es {{PLURAL:$1|mostra $1 pàgina que fa servir|mostren $1 pàgines que fan servir}} aquesta propietat amb una correspondència de valor «$2».", "smw-property-reserved-category": "Categoria", "smw-category": "Categoria", "smw-datavalue-uri-invalid-scheme": "No s'ha llistat «$1» com un esquema URI vàlid.", "smw-datavalue-uri-invalid-authority-path-component": "S'ha identificat que «$1» conté una autoritat «$2» o component de camí no vàlids.", "smw-browse-property-group-title": "Grup de propietats", "smw-browse-property-group-label": "Etiqueta del grup de propietats", "smw-browse-property-group-description": "Descripció del grup de propietats", "smw-filter": "Filtre", "smw-section-expand": "Desplega la secció", "smw-section-collapse": "Replega la secció", "smw-ask-format-help-link": "Format [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Full de referència", "smw-personal-jobqueue-watchlist": "Llista de seguiment de la cua de tasques", "smw-property-predefined-label-skey": "<PERSON><PERSON> <PERSON>ordenac<PERSON>", "smw-processing": "S’està processant…", "smw-loading": "S’està carregant…", "smw-fetching": "S’està recuperant…", "smw-preparing": "S’està preparant…", "smw-expand": "Amplia", "smw-collapse": "Redueix", "smw-copy": "Copia", "smw-copy-clipboard-title": "Copia el contingut al portaretalls", "smw-jsonview-expand-title": "Augmenta la visualització JSON", "smw-jsonview-collapse-title": "Redueix la visualització JSON", "smw-jsonview-search-label": "Cerca:", "smw-redirect-target-unresolvable": "La destinació no es pot resoldre pel motiu «$1».", "smw-types-title": "Tipus: $1", "smw-schema-namespace-editcontentmodel-disallowed": "No es permet canviar el model de contingut d'una [https://www.semantic-mediawiki.org/wiki/Help:Schema pàgina d'esquema].", "smw-schema-namespace-edit-protection": "Aquesta pàgina està protegida i només pot ser modificada per usuaris amb els [https://www.semantic-mediawiki.org/wiki/Help:Permissions permisos] <code>smw-schemaedit</code> adequats.", "smw-schema-error-title": "{{PLURAL:$1|Error|Errors}} de validació", "smw-schema-error-schema": "L'esquema de validació «$1» ha trobat les inconsistències següents:", "smw-schema-error-miscellaneous": "Error miscel·lani ($1)", "smw-schema-error-validation-file-inaccessible": "El fitxer de validació «$1» no és accessible.", "smw-schema-error-violation": "[«$1», «$2»]", "smw-schema-error-type-missing": "Al contingut li manca un tipus per tal que sigui reconegut i usable a l'[https://www.semantic-mediawiki.org/wiki/Help:Schema espai de noms de l'esquema].", "smw-schema-error-type-unknown": "El tipus «$1» no està registrat i llavors no pot utilitzar-se com a contingut a l'[https://www.semantic-mediawiki.org/wiki/Help:Schema espai de noms de l'esquema/smw].", "smw-schema-error-json": "Error de JSON: «$1»", "smw-schema-validation-schema-title": "Esquema JSON", "smw-schema-summary-title": "Resum", "smw-schema-title": "Esquema", "smw-schema-usage": "Ús", "smw-schema-type": "Tipus d'esquema", "smw-schema-type-description": "Descripció del tipus", "smw-schema-description": "Descripció de l'esquema", "smw-schema-tag": "{{PLURAL:$1|Etiqueta|Etiquetes}}", "smw-property-predefined-long-schema-tag": "Una etiqueta que identifica l'esquema de continguts o de característiques similars.", "smw-ask-title-keyword-type": "Cerca per paraula clau", "smw-ask-message-keyword-type": "Aquesta cerca coincideix amb la condició <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "No es pot connectar a l'objectiu remot «$1».", "smw-remote-source-disabled": "La font «$1» ha inhabilitat les sol·licituds remotes.", "smw-remote-source-unmatched-id": "La font «$1» no coincideix amb la versió de Semantic MediaWiki que pot acceptar sol·licituds remotes.", "smw-remote-request-note": "El resultat s’obté des de l’origen remot '''$1'''. És probable que el contingut generat inclogui informació que no és al wiki actual.", "smw-remote-request-note-cached": "El resultat s’'''emmagatzema dins la memòria cau''' des de l’origen remot '''$1'''. És probable que el contingut generat inclogui informació que no és al wiki actual.", "smw-parameter-missing": "Manca el paràmetre «$1».", "smw-property-tab-usage": "Ús", "smw-property-tab-profile-schema": "Esquema de perfil", "smw-property-tab-redirects": "Sinònims", "smw-property-tab-subproperties": "Subpropietats", "smw-property-tab-errors": "Assignacions no adequades", "smw-property-tab-constraint-schema": "Esquema de restricció", "smw-property-tab-specification": "... més", "smw-concept-tab-list": "Llista", "smw-concept-tab-errors": "Errors", "smw-ask-tab-result": "Resultat", "smw-ask-tab-extra": "Extra", "smw-ask-tab-debug": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-code": "Codi", "smw-install-incomplete-tasks-title": "Tasques d'administració incompletes", "smw-install-incomplete-intro": "Hi ha $2 {{PLURAL:$2|tasca incompleta|tasques incompletes}} o [[Special:PendingTaskList|pendent]] de finalitzar {{PLURAL:$1|la instal·lació|l'actualització}} de [https://www.semantic-mediawiki.org Semantic MediaWiki]. Un administrador o usuari amb drets suficients {{PLURAL:$2|el|els}} pot completar. Això s'hauria de fer abans d'afegir dades noves per evitar incoherències.", "smw-pendingtasks-tab-setup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-pendingtasks-setup-tasks": "Tasques", "smw-filter-count": "Recompte de filtres", "smw-es-replication-check": "Comprovació de replicació (Elasticsearch)", "smw-es-replication-error": "Incidència de replicació d'Elasticsearch", "smw-es-replication-file-ingest-error": "Incidència d'ingestió de fitxers", "smw-es-replication-maintenance-mode": "Manteniment d'Elasticsearch", "smw-es-replication-error-divergent-revision-detail": "Revisions associades referenciades:\n*Elasticsearch: $1 \n*Base de dades: $2", "smw-report": "Informe", "smw-legend": "<PERSON><PERSON><PERSON><PERSON>", "smw-entity-examiner-indicator": "Tauler d'incidències d'entitats", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-entity-examiner-associated-revision-mismatch": "Revisió", "smw-entity-examiner-deferred-fake": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-indicator-constraint-violation": "{{PLURAL:$1|Constricció|Constriccions}}", "smw-indicator-revision-mismatch": "Revisió", "smw-listingcontinuesabbrev": " cont.", "smw-showingresults": "Tot seguit es {{PLURAL:$1|mostra el resultat|mostren els <b>$1</b> resultats començant pel número <b>$2</b>}}."}