{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "Alien333", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Crochet.david", "DePlus<PERSON>ean", "Derugon", "Dinoxel", "<PERSON>", "Eduardoad<PERSON>", "<PERSON>eel<PERSON>", "Epok", "Eric.LEWIN", "Erkethan", "Faure.thomas", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hmevidia", "IAlex", "Kghbln", "<PERSON><PERSON><PERSON> kanane leopold", "LIMAFOX76", "<PERSON><PERSON><PERSON>", "<PERSON>rl<PERSON>", "MacOS Weed", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "<PERSON><PERSON>", "Metroitendo", "Nemo bis", "Nicolas <PERSON>", "Od1n", "Orlodrim", "Oujon", "Oupsa", "<PERSON><PERSON><PERSON>", "Peter<PERSON>", "PieRRoMaN", "<PERSON>", "Pols12", "Qwrop", "Seb35", "<PERSON><PERSON><PERSON><PERSON>", "SleaY", "Solitarius", "Tartare", "Thibaut120094", "Tititou36", "Urhixidur", "<PERSON><PERSON><PERSON> p", "VictorBrice", "<PERSON><PERSON>", "Wladek92", "Wuzhenwei", "Wyz", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "아라"]}, "smw-desc": "Rendre votre wiki plus accessible — pour les machines ''et'' les humains ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentation en ligne])", "smw-error": "<PERSON><PERSON><PERSON>", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] a été installé et activé, mais il manque une [https://www.semantic-mediawiki.org/wiki/Help:Upgrade clé de mise à jour] appropriée.", "smw-upgrade-release": "Publier", "smw-upgrade-progress": "Avancement", "smw-upgrade-progress-explain": "Une estimation du moment où la mise à jour sera terminée est difficile à prédire, car cela dépend de la taille du dépôt de données et du matériel disponible, et cela peut prendre un moment pour se terminer, sur les plus gros wikis.\n\nVeuillez contacter votre administrateur local pour obtenir plus d’informations sur cet avancement.", "smw-upgrade-progress-create-tables": "Création (ou mise à jour) des tables et des index...", "smw-upgrade-progress-post-creation": "Exécution des tâches post-création...", "smw-upgrade-progress-table-optimization": "Exécution des optimisations de table...", "smw-upgrade-progress-supplement-jobs": "Ajout des tâches supplémentaires...", "smw-upgrade-error-title": "Erreur ▸ MediaWiki Sémantique", "smw-upgrade-error-why-title": "Pourquoi vois-je cette page ?", "smw-upgrade-error-why-explain": "La structure de la base de données interne de MediaWiki Sémantique a été modifiée et nécessite des ajustements pour être pleinement fonctionnelle. Il peut y avoir plusieurs raisons à cela, dont :\n* des propriétés corrigées supplémentaires (nécessitant une installation de table supplémentaire) ont été ajoutées ;\n* une mise à jour contient certaines modifications de tables ou d’index rendant une interception obligatoire avant d’accéder aux données ;\n* des modifications au stockage ou au moteur de requêtes.", "smw-upgrade-error-how-title": "Comment dois-je corriger cette erreur ?", "smw-upgrade-error-how-explain-admin": "Un administrateur (ou toute personne ayant les droits administrateur) doit exécuter le script de maintenance soit [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] de MediaWiki soit [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] de MediaWiki Sémantique.", "smw-upgrade-error-how-explain-links": "<PERSON><PERSON> pouvez aussi consulter les pages suivantes pour une aide supplémentaire :\n* les instructions d’[https://www.semantic-mediawiki.org/wiki/Help:Installation installation] ;\n* page d’aide pour la [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting recherche de fautes].", "smw-extensionload-error-why-title": "Pourquoi vois-je cette page ?", "smw-extensionload-error-why-explain": "L’extension n’a <b>pas</b> été chargée avec <code>enableSemantics</code> mais par un autre moyen tel que l’utilisation de <code>wfLoadExtension( 'SemanticMediaWiki' )</code> directement.", "smw-extensionload-error-how-title": "Comment puis-je corriger cette erreur ?", "smw-extensionload-error-how-explain": "Pour activer l’extension et éviter les problèmes de déclarations d'espaces de noms et de configurations en attente, il est nécessaire d’utiliser <code>enableSemantics</code>, qui assure l’assignation des variables requises avant le chargement de l’extension via <code>ExtensionRegistry</code>. \n\nVeuillez consulter la page d’aide d’[https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] pour plus d’assistance.", "smw-upgrade-maintenance-title": "Maintenance » MediaWiki Sémantique", "smw-upgrade-maintenance-why-title": "Pourquoi vois-je cette page ?", "smw-upgrade-maintenance-note": "Le système est actuellement en cours de [https://www.semantic-mediawiki.org/wiki/Help:Upgrade mise à jour] pour l’extension [https://www.semantic-mediawiki.org/ MediaWiki Sémantique] ainsi que son dépôt de données et nous vous demandons de bien vouloir patienter et permettre à la maintenance de continuer avant que le wiki soit de nouveau accessible.", "smw-upgrade-maintenance-explain": "L’extension essaie de minimiser l’impact et de réduire le temps en délégant la plupart de ses tâches de maintenance après <code>update.php</code>, mais certaines modifications relatives à la base de données doivent d’abord se terminer pour éviter d’avoir des données incohérentes. Cela peut comprendre : \n* la modification des structures de tables comme l’ajout de nouveaux champs ou la modification de champs existants\n* la modification ou l’ajout d’index de tableaux \n* l’exécution des optimisations de tables (si activé)", "smw-semantics-not-enabled": "La fonctionnalité MediaWiki Sémantique n’est pas activée sur ce wiki.", "smw_viewasrdf": "flux RDF", "smw_finallistconjunct": " et", "smw-factbox-head": "... davantage au sujet de « $1 »", "smw-factbox-facts": "Faits", "smw-factbox-facts-help": "Affiche les affirmations et les faits qui ont été créés par un utilisateur", "smw-factbox-attachments": "Pièces jointes :", "smw-factbox-attachments-value-unknown": "n.d.", "smw-factbox-attachments-is-local": "Est local", "smw-factbox-attachments-help": "Affiche les pièces jointes disponibles", "smw-factbox-facts-derived": "Faits d<PERSON>", "smw-factbox-facts-derived-help": "Affiche les faits qui ont été dérivés des règles ou avec l’aide d’autres techniques de raisonnement", "smw_isspecprop": "Cette propriété est une propriété spéciale dans ce wiki.", "smw-concept-cache-header": "Utilisation du cache", "smw-concept-cache-count": "Le [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count cache de concept] contient {{PLURAL:$1|'''une''' entité|'''$1''' entités}} ($2).", "smw-concept-no-cache": "Aucune donnée en cache disponible.", "smw_concept_description": "Description du concept « $1 »", "smw_no_concept_namespace": "Les concepts ne peuvent être définis que dans les pages appartenant à l’espace de noms « Concept: ».", "smw_multiple_concepts": "Chaque page de concept ne peut avoir qu’une seule définition de concept.", "smw_concept_cache_miss": "Le concept « $1 » ne peut être utilisé en ce moment, car la configuration du wiki exige qu’il soit lancé hors-ligne.\nSi le problème persiste, demandez à l’administrateur de votre site de rendre ce concept disponible.", "smw_noinvannot": "Les valeurs ne peuvent pas être allouées pour inverser des propriétés.", "version-semantic": "Extensions sémantiques", "smw_baduri": "Les URI de la forme « $1 » ne sont pas autorisées.", "smw_printername_count": "Comptage des résultats", "smw_printername_csv": "Exporter au format CSV", "smw_printername_dsv": "Exporter au format DSV", "smw_printername_debug": "Requête de débogage (pour les experts)", "smw_printername_embedded": "<PERSON><PERSON>er le contenu de la page", "smw_printername_json": "Exporter au format JSON", "smw_printername_list": "Liste", "smw_printername_plainlist": "Liste simple", "smw_printername_ol": "Liste numérotée", "smw_printername_ul": "Liste à puces", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "Tableau élargi", "smw_printername_template": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_templatefile": "<PERSON><PERSON><PERSON> mod<PERSON>", "smw_printername_rdf": "Exporter au format RDF", "smw_printername_category": "<PERSON><PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "texte", "smw-paramdesc-limit": "Le nombre maximal de résultats à renvoyer", "smw-paramdesc-offset": "Le décalage du premier résultat", "smw-paramdesc-headers": "Afficher les en-têtes / noms de propriétés", "smw-paramdesc-mainlabel": "L’étiquette de la page d’accueil", "smw-paramdesc-link": "<PERSON><PERSON> les valeurs en tant que liens", "smw-paramdesc-intro": "Le texte à afficher avant les résultats de la requête, s’il y en a", "smw-paramdesc-outro": "Le texte à afficher après les résultats de la requête, s’il y en a", "smw-paramdesc-default": "Le texte à afficher s’il n’y a aucun résultat pour la requête", "smw-paramdesc-sep": "Le séparateur entre les résultats", "smw-paramdesc-propsep": "Le séparateur entre les propriétés d’une entrée de résultat", "smw-paramdesc-valuesep": "Le séparateur entre les valeurs pour une propriété de résultat", "smw-paramdesc-showsep": "Affiche<PERSON> le séparateur en haut du fichier CSV (« sep=<valeur> »)", "smw-paramdesc-distribution": "Au lieu d’afficher toutes les valeurs, compter leurs occurrences et les afficher.", "smw-paramdesc-distributionsort": "Trier la distribution de valeurs par nombre d’occurrences.", "smw-paramdesc-distributionlimit": "Limiter la distribution des valeurs à la prise en compte de seulement quelques valeurs.", "smw-paramdesc-aggregation": "Spécifier à quoi l’aggrégation doit être reliée", "smw-paramdesc-template": "Le nom d’un modèle qui servira à afficher les résultats", "smw-paramdesc-columns": "Le nombre de colonnes pour afficher les résultats", "smw-paramdesc-userparam": "Une valeur passée dans chaque appel de modèle, si un modèle est utilisé", "smw-paramdesc-class": "Une classe CSS supplémentaire à définir pour la liste", "smw-paramdesc-introtemplate": "Le nom d’un modèle à afficher avant les résultats de requête, s’il y en a", "smw-paramdesc-outrotemplate": "Le nom d’un modèle à afficher après les résultats de la requête, s’il y en a", "smw-paramdesc-embedformat": "La balise HTML qui sert à définir les en-têtes", "smw-paramdesc-embedonly": "<PERSON>’afficher aucun en-tête", "smw-paramdesc-table-class": "Une classe CSS supplémentaire à mettre pour la table", "smw-paramdesc-table-transpose": "Afficher les entêtes du tableau verticalement et les résultats horizontalement", "smw-paramdesc-prefix": "Contrôler l’affichage de l’espace de noms dans les impressions", "smw-paramdesc-rdfsyntax": "La syntaxe de RDF à utiliser", "smw-paramdesc-csv-sep": "Spécifie un séparateur de colonnes", "smw-paramdesc-csv-valuesep": "Spécifie un séparateur de valeurs", "smw-paramdesc-csv-merge": "Fusionne les valeurs des lignes et des colonnes avec un identifiant de sujet identique (alias première colonne)", "smw-paramdesc-csv-bom": "Ajouter un BOM (caractère pour signaler le boutisme) au début du fichier de sortie", "smw-paramdesc-dsv-separator": "Le séparateur à utiliser", "smw-paramdesc-dsv-filename": "Le nom du fichier DSV", "smw-paramdesc-filename": "Le nom du fichier de sortie", "smw-smwdoc-description": "Affiche un tableau de tous les paramètres qui peuvent être utilisés pour le format de résultat spécifié ensemble avec les valeurs et les descriptions par défaut.", "smw-smwdoc-default-no-parameter-list": "Ce format de résultat ne propose pas de paramètres de mise en forme spécifique.", "smw-smwdoc-par-format": "Le format de résultat dans lequel afficher la documentation d'un paramètre.", "smw-smwdoc-par-parameters": "Les paramètres à afficher : « specific » pour seulement ceux ajoutés par le format, « base » pour seulement ceux disponibles dans tous les formats, ou bien « all » pour les afficher tous.", "smw-paramdesc-sort": "Propriété selon laquelle trier la requête", "smw-paramdesc-order": "Ordre de tri pour la requête", "smw-paramdesc-searchlabel": "Texte pour continuer la recherche", "smw-paramdesc-named_args": "Nommez les arguments transmis au modèle", "smw-paramdesc-template-arguments": "Indique comment les arguments nommés sont passés au modèle", "smw-paramdesc-import-annotation": "Les données supplémentaires annotées sont copiées lors de l’analyse d’un sujet", "smw-paramdesc-export": "Options d’exportation", "smw-paramdesc-prettyprint": "Une sortie d’impression plus claire qui affiche les indentations et sauts de ligne ajoutés", "smw-paramdesc-json-unescape": "Sortie pour inclure sans échappement les barres obliques et les caractères Unicode codés sur plusieurs octets", "smw-paramdesc-json-type": "Type de sérialisation", "smw-paramdesc-source": "Source de requête alternative", "smw-paramdesc-jsonsyntax": "Syntaxe JSON à utiliser", "smw-printername-feed": "Flux RSS ou Atom", "smw-paramdesc-feedtype": "Type de flux", "smw-paramdesc-feedtitle": "Le texte à utiliser comme titre du flux", "smw-paramdesc-feeddescription": "Le texte à utiliser comme description du flux", "smw-paramdesc-feedpagecontent": "Contenu de la page à afficher avec le flux", "smw-label-feed-description": "Flux $1 $2", "smw-paramdesc-mimetype": "Le type de média (type MIME) du fichier de sortie", "smw_iq_disabled": "Les recherches sémantiques sur ce wiki sont désactivées.", "smw_iq_moreresults": "... autres résultats", "smw_parseerror": "La donnée indiquée n’a pas été comprise.", "smw_decseparator": ",", "smw_kiloseparator": "&nbsp;", "smw_notitle": "« $1 » ne peut être utilisé comme nom de page sur ce wiki.", "smw_noproperty": "« $1 » ne peut être utilisé comme un nom de propriété sur ce wiki.", "smw_wrong_namespace": "Seules les pages dans l’espace « $1 » sont autorisées ici.", "smw_manytypes": "Plusieurs types de données ont été affectés à cette propriété.", "smw_emptystring": "Les chaînes vides ne sont pas acceptées.", "smw_notinenum": "« $1 » n’est pas dans la liste ($2) des [[Property:Allows value|valeurs autorisées]] pour la propriété « $3 ».", "smw-datavalue-constraint-error-allows-value-list": "« $1 » n’est pas la liste ($2) des [[Property:Allows value|valeurs autorisées]] pour la propriété « $3 ».", "smw-datavalue-constraint-error-allows-value-range": "« $1 » n’est pas dans cet intervalle de « $2 » spécifié par la contrainte sur la [[Property:Allows value|valeur autorisée]] de la propriété « $3 ».", "smw-constraint-error": "Problème de contrainte", "smw-constraint-error-suggestions": "Veuillez vérifier les violations et les propriétés listées avec leurs valeurs annotées, pour vous assurer que toutes les exigences de contrainte sont vérifiées.", "smw-constraint-error-limit": "La liste contiendra au maximum $1 violations.", "smw_noboolean": "« $1 » n’est pas reconnu comme une valeur booléenne (vrai / faux).", "smw_true_words": "vrai,v,oui,o,true", "smw_false_words": "faux,f,non,n,false", "smw_nofloat": "« $1 » n’est pas un nombre.", "smw_infinite": "Les nombres aussi grands que « $1 » ne sont pas pris en charge.", "smw_unitnotallowed": "« $1 » n’est pas déclaré comme une unité valide de mesure pour cette propriété.", "smw_nounitsdeclared": "Aucune unité de mesure n’a été déclarée pour cette propriété.", "smw_novalues": "Aucune valeur n’a été spécifiée.", "smw_nodatetime": "La date « $1 » n’a pas été comprise.", "smw_toomanyclosing": "Il semble y avoir trop d’occurrences de « $1 » dans la requête.", "smw_noclosingbrackets": "Certaines paires « <nowiki>[[</nowiki> » dans votre requête ne sont pas fermés avec les paires « ]] » correspondantes.", "smw_misplacedsymbol": "Le symbole « $1 » a été utilisé à un endroit où il n’est pas utile.", "smw_unexpectedpart": "La partie « $1 » de la requête n’a pas été comprise. Les résultats peuvent être inattendus.", "smw_emptysubquery": "Certaines sous-requêtes ont une condition non valide.", "smw_misplacedsubquery": "Certaines sous-requêtes ont été utilisées à un endroit où aucune sous-requête n’est permise.", "smw_valuesubquery": "Les sous-requêtes se sont pas prises en charge pour les valeurs de la propriété « $1 ».", "smw_badqueryatom": "Une partie délimitée par « <nowiki>[[...]]</nowiki> » dans la requête n’a pas été comprise.", "smw_propvalueproblem": "La valeur de la propriété « $1 » n’a pas été comprise.", "smw_noqueryfeature": "Une fonctionnalité de requête n’est pas prise en charge par ce wiki et une partie de la requête a été éliminée ($1).", "smw_noconjunctions": "Les conjonctions dans les requêtes ne sont pas prises en charge par ce wiki et une partie de la requête a été éliminée ($1).", "smw_nodisjunctions": "Les disjonctions dans les requêtes ne sont pas prises en charge par ce wiki et une partie de la requête a été éliminée ($1).", "smw_querytoolarge": "L{{PLURAL:$2|a|es}} condition{{PLURAL:$2||s}} suivante{{PLURAL:$2||s}} de la requête n’{{PLURAL:$2|a|ont}} pas pu être évaluée{{PLURAL:$2||s}} en raison de restrictions de ce wiki sur la taille ou la profondeur de cette requête : <code>$1</code>.", "smw_notemplategiven": "Veuillez fournir une valeur pour le paramètre « modèle » afin que ce format de requête fonctionne.", "smw_db_sparqlqueryproblem": "Le résultat de la requête n’a pas pu être obtenu depuis la base de données SPARQL. Cette erreur peut être temporaire ou indiquer un bogue dans le logiciel de la base de données.", "smw_db_sparqlqueryincomplete": "Il s’est avéré trop difficile de répondre à la requête et celle-ci a été abandonnée. Certains résultats pourraient manquer. Si possible, essayez plutôt d’utiliser une requête plus simple.", "smw_type_header": "Propriétés de type « $1 »", "smw_typearticlecount": "Afficher {{PLURAL:$1|la|les $1}} propriété{{PLURAL:$1|}} utilisant ce type.", "smw_attribute_header": "Pages utilisant la propriété « $1 »", "smw_attributearticlecount": "Afficher {{PLURAL:$1|la page|les $1 pages}} utilisant cette propriété.", "smw-propertylist-subproperty-header": "Sous-propriétés", "smw-propertylist-redirect-header": "Synonymes", "smw-propertylist-error-header": "Pages avec des affectations erronées", "smw-propertylist-count": "Affichage {{PLURAL:$1|de l’entité associée|des $1 entités associées}}.", "smw-propertylist-count-with-restricted-note": "Afficher {{PLURAL:$1|l’entité associée|les $1 entités associées}} (davantage sont disponibles, mais l’affichage est limité à « $2 »).", "smw-propertylist-count-more-available": "Afficher {{PLURAL:$1|l’entité associée|les $1 entités associées}} (davantage sont disponibles).", "specialpages-group-smw_group": "MediaWiki Sémantique", "specialpages-group-smw_group-maintenance": "Maintenance", "specialpages-group-smw_group-properties-concepts-types": "Propriétés, concepts et types", "specialpages-group-smw_group-search": "Naviguer et rechercher", "exportrdf": "Exporter des pages en RDF", "smw_exportrdf_docu": "Cette page permet d’obtenir des données d’une page au format RDF. \nVeuillez entrer le nom des pages souhaitées dans la boîte de texte ci-dessous, un nom par ligne.", "smw_exportrdf_recursive": "Exporter toutes les pages pertinentes de manière récursive. Cette possibilité peut aboutir à un très grand nombre de résultats !", "smw_exportrdf_backlinks": "Exporter également toutes les pages qui renvoient à des pages exportées. \nProduit un RDF dans lequel la navigation est facilitée.", "smw_exportrdf_lastdate": "Ne pas exporter les pages non modifiées depuis le moment indiqué.", "smw_exportrdf_submit": "Exporter", "uriresolver": "Résolveur d’URI", "properties": "Propriétés", "smw-categories": "Liste des catégories", "smw_properties_docu": "Les propriétés suivantes sont utilisées sur ce wiki.", "smw_property_template": "$1 du type $2 ($3 utilisation{{PLURAL:$3||s}})", "smw_propertylackspage": "Toutes les propriétés devraient être décrites par une page !", "smw_propertylackstype": "Aucun type n’a été spécifié pour cette propriété (type actuellement supposé : $1).", "smw_propertyhardlyused": "Cette propriété est à peine utilisée sur ce wiki !", "smw-property-name-invalid": "La propriété $1 ne peut pas être utilisée (nom de propriété non valide).", "smw-property-name-reserved": "« $1 » a été listé comme un nom réservé et ne doit pas être utilisé en tant que propriété. La [https://www.semantic-mediawiki.org/wiki/Help:Property_naming page d’aide] suivante peut contenir des informations sur la raison pour laquelle ce nom a été réservé.", "smw-sp-property-searchform": "Afficher les propriétés qui contiennent :", "smw-sp-property-searchform-inputinfo": "L’entrée est sensible à la casse et, quand elle est utilisée pour le filtrage, seules les propriétés qui correspondent à la condition sont affichées.", "smw-special-property-searchform": "Afficher les propriétés qui contiennent :", "smw-special-property-searchform-inputinfo": "L’entrée est sensible à la casse et, quand elle est utilisée pour le filtrage, seules les propriétés qui correspondent à la condition sont affichées.", "smw-special-property-searchform-options": "Options", "smw-special-wantedproperties-filter-label": "Filtre :", "smw-special-wantedproperties-filter-none": "Aucun filtre", "smw-special-wantedproperties-filter-unapproved": "Non approuvé", "smw-special-wantedproperties-filter-unapproved-desc": "Option de filtrage utilisée en lien avec le type d’autorisation.", "concepts": "Concepts", "smw-special-concept-docu": "Un [https://www.semantic-mediawiki.org/wiki/Help:Concepts concept] peut être vu comme une «’catégorie dynamique’», c’est-à-dire comme une collection de pages qui ne sont pas créées manuellement, mais sont calculées par MediaWiki Sémantique depuis la description d’une requête fournie.", "smw-special-concept-header": "Liste des concepts", "smw-special-concept-count": "Le{{PLURAL:$1||s}} concept{{PLURAL:$1||s}} suivant{{PLURAL:$1||s}} {{PLURAL:$1|es|son}}t listé{{PLURAL:$1||s}}.", "smw-special-concept-empty": "Aucun concept trouvé.", "unusedproperties": "Propriétés inutilisées", "smw-unusedproperties-docu": "Cette page liste [https://www.semantic-mediawiki.org/wiki/Unused_properties les propriétés inutilisées] qui sont déclarées bien qu’aucune autre page ne les utilise. Pour un affichage différencié, voyez les pages spéciales [[Special:Properties|propriétés complètes]] ou [[Special:WantedProperties|demandées]].", "smw-unusedproperty-template": "$1 de type $2", "wantedproperties": "Propriétés demandées", "smw-wantedproperties-docu": "Cette page liste [https://www.semantic-mediawiki.org/wiki/Wanted_properties les propriétés demandées] qui sont utilisées dans le wiki mais qui n’ont pas de page qui les décrivent. Pour une vue différenciée, voyez les pages spéciales [[Special:Properties|propriétés complètes]] ou  [[Special:UnusedProperties|inutilisées]].", "smw-wantedproperty-template": "$1 ($2 utilisation{{PLURAL:$2||s}})", "smw-special-wantedproperties-docu": "Cette page liste [https://www.semantic-mediawiki.org/wiki/Wanted_properties les propriétés souhaitées] qui sont utilisées dans le wiki mais qui n’ont pas de page qui les décrivent. Pour une vue différenciée, voyez les pages spéciales de propriétés [[Special:Properties|complètes]] ou  [[Special:UnusedProperties|inutilisées]].", "smw-special-wantedproperties-template": "$1 ($2 utilisation{{PLURAL:$2||s}})", "smw_purge": "Purger", "smw-purge-update-dependencies": "MediaWiki Sémantique purge la page courante de certaines données obsolètes qu’il a détectées, ce qui nécessite une mise à jour en arrière-plan.", "smw-purge-failed": "MediaWiki Sémantique a essayé de purger la page, mais a <PERSON>choué", "types": "Types de données", "smw_types_docu": "Ceci est une liste des [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes types de données disponibles], pour chacun des [https://www.semantic-mediawiki.org/wiki/Help:Datatype types] représentant un ensemble unique d’attributs qui décrivent une valeur en termes de stockage et de caractéristiques d’affichage héritables pour une propriété affectée.", "smw-special-types-no-such-type": "« $1 » est inconnu ou n’a pas été déclaré comme type de données valide.", "smw-statistics": "Statistiques sémantiques", "smw-statistics-cached": "Statistiques sémantiques (en cache)", "smw-statistics-entities-total": "Entités (total)", "smw-statistics-entities-total-info": "Un nombre estimé de lignes d’entités. <PERSON><PERSON> comprend les propriétés, les concepts ou tout autre représentation d’objet enregistré qui nécessite une affectation d’identifiant.", "smw-statistics-property-instance": "Valeur{{PLURAL:$1||s}} de propriété (total)", "smw-statistics-property-total": "[[Special:Properties|Propriété{{PLURAL:$1||s}}]] (total)", "smw-statistics-property-total-info": "Le nombre total de propriétés enregistrées.", "smw-statistics-property-total-legacy": "Propriété{{PLURAL:$1||s}} (total)", "smw-statistics-property-used": "Propriété{{PLURAL:$1||s}} (utilisé{{PLURAL:$1||s}} avec au moins une valeur)", "smw-statistics-property-page": "Propriété{{PLURAL:$1||s}} (enregistrée{{PLURAL:$1||s}} avec une page)", "smw-statistics-property-page-info": "Compteur des propriétés qui ont une page dédiée et une description.", "smw-statistics-property-type": "Propriété{{PLURAL:$1||s}} (affectée{{PLURAL:$1||s}} à un type de données)", "smw-statistics-query-inline-legacy": "Requête{{PLURAL:$1||s}}", "smw-statistics-query-inline": "[[Property:Has query|Requête{{PLURAL:$1||s}}]] (intégrée{{PLURAL:$1||s}}, total)", "smw-statistics-query-format": "format de <code>$1</code>", "smw-statistics-query-size": "<PERSON><PERSON> requ<PERSON>", "smw-statistics-concept-count-legacy": "Concept{{PLURAL:$1||s}}", "smw-statistics-concept-count": "[[Special:Concepts|Concept{{PLURAL:$1||s}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|Possède {{PLURAL:$1|un|des}} sous-objet{{PLURAL:$1||s}}]]", "smw-statistics-subobject-count-legacy": "Sous-objet{{PLURAL:$1||s}}", "smw-statistics-datatype-count": "[[Special:Types|Type{{PLURAL:$1||s}} de donnée]]", "smw-statistics-error-count": "Valeur{{PLURAL:$1||s}} de propriété ([[Special:ProcessingErrorList|mauvaise{{PLURAL:$1||s}} annotation{{PLURAL:$1||s}}]])", "smw-statistics-error-count-legacy": "Valeur{{PLURAL:$1||s}} de propriété (mauvaise{{PLURAL:$1||s}} annotation{{PLURAL:$1||s}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities Entité{{PLURAL:$1||s}} désuète{{PLURAL:$1||s}}]", "smw-statistics-delete-count-info": "Entités qui ont été marquées à supprimer et qui doivent être éliminées régulièrement à l’aide des scripts de maintenance fournis.", "smw_uri_doc": "Le résolveur d’URI met en œuvre la [$1 Conclusion du TAG du W3C à propos de httpRange-14].\nIl assure qu'une représentation RDF (pour les machines) ou une page wiki (pour les humains) et fournie selon la requête.", "ask": "Recherche sémantique", "smw-ask-help": "Cette section contient des liens pour aider à expliquer comment utiliser la syntaxe <code>#ask</code> :\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Sélection des pages] décrit comment sélectionner les pages et construire les conditions ;\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Opérateurs de recherche] liste les opérateurs de recherche disponibles, y compris ceux des requêtes dans une plage et les jokers ;\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Affichage des informations] expose l’utilisation des déclarations d’affichage et des options de mise en forme des résultats.", "smw_ask_sortby": "Trier par colonne (facultatif)", "smw_ask_ascorder": "Croissant", "smw_ask_descorder": "Décroissant", "smw-ask-order-rand": "<PERSON>", "smw_ask_submit": "Trouver des résultats", "smw_ask_editquery": "Modifier la requête", "smw_add_sortcondition": "[Ajouter une condition de tri]", "smw-ask-sort-add-action": "Ajouter une condition de tri", "smw_ask_hidequery": "Masquer la requête (affichage compact)", "smw_ask_help": "Aide à la requête", "smw_ask_queryhead": "Condition", "smw_ask_printhead": "Sélection des données à imprimer", "smw_ask_printdesc": "(ajouter un nom de propriété par ligne)", "smw_ask_format_as": "Formater en :", "smw_ask_defaultformat": "par défaut", "smw_ask_otheroptions": "Autres options", "smw-ask-otheroptions-info": "Cette section contient des options qui modifient l’affichage des données. Placez votre souris sur les paramètres pour en voir la description.", "smw-ask-otheroptions-collapsed-info": "Veuillez utiliser l’icône « plus » pour afficher toutes les options disponibles", "smw_ask_show_embed": "Montrer le code intégré", "smw_ask_hide_embed": "Masquer le code intégré", "smw_ask_embed_instr": "Pour intégrer cette requête dans une page wiki, utilisez le code ci-dessous.", "smw-ask-delete": "<PERSON><PERSON><PERSON>", "smw-ask-sorting": "Tri", "smw-ask-options": "Options", "smw-ask-options-sort": "Options de tri", "smw-ask-format-options": "Format et options", "smw-ask-parameters": "Paramètres", "smw-ask-search": "<PERSON><PERSON><PERSON>", "smw-ask-debug": "Déboguer", "smw-ask-debug-desc": "Génère les informations de débogage de la requête", "smw-ask-no-cache": "Désactiver le cache de requête", "smw-ask-no-cache-desc": "Résultats sans le cache de requêtes", "smw-ask-result": "Résultat", "smw-ask-empty": "Effacer toutes les entrées", "smw-ask-download-link-desc": "Télécharger les résultats recherchés au format $1", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Aide pour le format sélectionné : $1", "smw-ask-condition-change-info": "La condition a été modifiée et le moteur de recherche a besoin d’exécuter à nouveau la requête pour obtenir des résultats qui correspondent aux nouvelles exigences.", "smw-ask-input-assistance": "Assistance à la saisie", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance L’assistance à la saisie] est fournie pour la sortie, le tri et le champ de condition. Le champ de condition requiert un des préfixes suivants :", "smw-ask-condition-input-assistance-property": "<code>p:</code> pour récupérer les suggestions de propriété (par ex. <code><nowiki>[[</nowiki>p:<PERSON> …<nowiki>]]</nowiki></code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> pour récupérer les suggestions de catégorie", "smw-ask-condition-input-assistance-concept": "<code>con:</code> pour récupérer les suggestions de concept", "smw-ask-format-change-info": "Le format a été modifié et la requête doit être réexécutée pour correspondre aux nouveaux paramètres et aux options de visualisation.", "smw-ask-format-export-info": "Le format sélectionné est un format d’exportation qui n’a pas de représentation visuelle, donc les résultats ne sont fournis qu’en téléchargement.", "smw-ask-query-search-info": "La requête <code><nowiki>$1</nowiki></code> a reçu une réponse de <code>$2</code> {{PLURAL:$3|0=|(depuis le cache)}} en $4 seconde{{PLURAL:$4||s}}.", "smw-ask-extra-query-log": "Journal de requête", "smw-ask-extra-other": "<PERSON><PERSON>", "searchbyproperty": "Rechercher par propriété", "processingerrorlist": "Liste des erreurs de traitement", "constrainterrorlist": "Liste d’erreurs de contrainte", "propertylabelsimilarity": "Compte-rendu de similitude des étiquettes de propriété", "missingredirectannotations": "Absence d’annotations de redirection", "smw-processingerrorlist-intro": "La liste suivante fournit une vue d’ensemble du [https://www.semantic-mediawiki.org/wiki/Processing_errors traitement des erreurs] qui sont apparues selon [https://www.semantic-mediawiki.org/ MediaWiki Sémantique]. Il est recommandé de suivre cette liste régulièrement et de corriger les annotations de valeur non valide.", "smw-constrainterrorlist-intro": "La liste suivante fournit une vue d’ensemble des [https://www.semantic-mediawiki.org/wiki/Constraint_errors erreurs de contrainte] qui sont apparues selon [https://www.semantic-mediawiki.org/ MediaWiki Sémantique]. Il est recommandé de suivre cette liste régulièrement et de corriger les annotations de valeur non valide.", "smw-missingredirects-intro": "La section suivante listera les pages qui ont des annotations de [https://www.semantic-mediawiki.org/wiki/Redirects redirection] manquantes dans MediaWiki Sémantique (en les comparant avec les informations stockées dans MediaWiki) ; pour restaurer ces annotations, il faut soit [https://www.semantic-mediawiki.org/wiki/Help:Purge purger] manuellement la page, soit lancer le script de maintenance <code>rebuildData.php</code> (avec l’option <code>--redirects</code>).", "smw-missingredirects-list": "Pages avec des annotations manquantes", "smw-missingredirects-list-intro": "Affichage de $1 page{{PLURAL:$1||s}} avec les annotations de redirection manquantes.", "smw-missingredirects-noresult": "Aucune annotation de redirection manquante trouvée.", "smw_sbv_docu": "Rechercher toutes les pages qui ont une propriété donnée avec une certaine valeur.", "smw_sbv_novalue": "Entrez une valeur correcte pour la propriété, ou bien consultez toutes les valeurs de la propriété pour « $1 ».", "smw_sbv_displayresultfuzzy": "Une liste de toutes les pages qui ont la propriété « $1 » avec la valeur « $2 ». \nPuisqu’il n’y a que quelques résultats, les valeurs proches sont également affichées.", "smw_sbv_property": "Propriété :", "smw_sbv_value": "Valeur :", "smw_sbv_submit": "Trouver des résultats", "browse": "Parcourir le wiki", "smw_browselink": "Parcourir les propriétés", "smw_browse_article": "Entrez le nom de la page à partir de laquelle commencer le parcours.", "smw_browse_go": "<PERSON><PERSON>", "smw_browse_show_incoming": "Afficher les propriétés qui pointent ici", "smw_browse_hide_incoming": "<PERSON><PERSON> les propriétés qui pointent ici", "smw_browse_no_outgoing": "Cette page n’a aucune propriété.", "smw_browse_no_incoming": "Aucune propriété ne pointe vers cette page.", "smw-browse-from-backend": "Les informations sont en cours de récupération depuis le serveur.", "smw-browse-intro": "Cette page fournit des détails concernant un sujet ou une instance d’entité, veuillez entrer le nom d’un objet à inspecter.", "smw-browse-invalid-subject": "La validation du sujet est revenue avec l’erreur « $1 ».", "smw-browse-api-subject-serialization-invalid": "Le sujet a un format de sérialisation non valide.", "smw-browse-js-disabled": "Il se peut que JavaScript soit désactivé ou non disponible. Nous recommandons d’utiliser un navigateur qui le prenne en charge. D’autres options sont discutées sur la page du paramètre de configuration [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Afficher les groupes", "smw-browse-hide-group": "Masquer les groupes", "smw-noscript": "Cette page ou cette action nécessite JavaScript pour fonctionner, veuille<PERSON> l’activer dans votre navigateur ou utiliser un navigateur qui le prend en charge afin que cette fonctionnalité puisse être fournie comme demandé. Pour plus d’assistance, veuillez consulter la page d’aide [https://www.semantic-mediawiki.org/wiki/Help:Noscript Noscript].", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "Libellé de la propriété inverse", "pageproperty": "Rechercher dans les propriétés de la page", "pendingtasklist": "Liste des tâches en attente", "facetedsearch": "Recherche à facettes", "smw_pp_docu": "Entrez soit une page et une propriété, soit seulement une propriété pour récupérer toutes les valeurs affectées.", "smw_pp_from": "De la page :", "smw_pp_type": "Propriété :", "smw_pp_submit": "Trouver des résultats", "smw-prev": "{{PLURAL:$1|précédent|$1 précédents}}", "smw-next": "{{PLURAL:$1|suivant|$1 suivants}}", "smw_result_prev": "Précédent", "smw_result_next": "Suivant", "smw_result_results": "Résultats", "smw_result_noresults": "Aucun résultat.", "smwadmin": "Tableau de bord de MediaWiki Sémantique", "smw-admin-statistics-job-title": "Statistiques des tâches", "smw-admin-statistics-job-docu": "Les statistiques des tâches affichent des informations sur les tâches programmées de MediaWiki Sémantique qui n’ont pas encore été exécutées. Le nombre de tâches peut être légèrement inexact ou contenir des tentatives en échec. Veuillez consulter le [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manuel] pour plus d’informations.", "smw-admin-statistics-querycache-title": "<PERSON><PERSON>", "smw-admin-statistics-querycache-disabled": "Le [https://www.semantic-mediawiki.org/wiki/QueryCache Cache de requêtes] n’a pas été activé sur ce wiki et, de ce fait, aucune statistique n’est disponible.", "smw-admin-statistics-querycache-legend": "Les statistiques de cache permettent de contenir les cumuls provisoires ainsi que les données dérivées, dont :\n* « misses », qui est le nombre total de tentatives pour récupérer des données du cache sans réponse disponible, forçant une récupération directe depuis le dépôt (base de données, triple entrepôt, etc.) ;\n* « deletes », qui est le nombre total d’opérations de sortie du cache (soit via une purge, soit par dépendance de requête) ;\n* « hits », qui contient le nombre de récupérations du cache depuis des sources, soit intégrées (requêtes appelées depuis une page du wiki), soit non intégrées (si activé, demandées par des pages comme Special:Ask ou l’API) ;\n* « medianRetrievalResponseTime », qui est une valeur indicative du temps médian de réponse (en secondes) pour les requêtes de récupération en cache ou non, pendant la durée du processus de collecte ;\n* « noCache », qui indique la quantité de requêtes non tentées (requêtes avec « limit=0 » ou avec l’option « no-cache », etc.) pour récupérer des résultats depuis le cache.", "smw-admin-statistics-section-explain": "La section fournit des statistiques supplémentaires pour les administrateurs.", "smw-admin-statistics-semanticdata-overview": "Vue d’ensemble", "smw-admin-permission-missing": "L’accès à cette page a été bloqué par manque d’autorisation ; veuil<PERSON>z consulter la page d’aide sur les [https://www.semantic-mediawiki.org/wiki/Help:Permissions autorisations] pour plus de détails sur les réglages nécessaires.", "smw-admin-setupsuccess": "Le moteur de stockage a été mis en place.", "smw_smwadmin_return": "Revenir à $1", "smw_smwadmin_updatestarted": "Un nouveau processus de rafraîchissement des données sémantiques a commencé.\nToutes les données stockées seront reconstruites, voire réparées si nécessaire.\nVous pouvez suivre la progression de la mise à jour sur cette page spéciale.", "smw_smwadmin_updatenotstarted": "Un processus de mise à jour est déjà en cours d’exécution.\nPas besoin d’en créer un autre.", "smw_smwadmin_updatestopped": "Tous les processus de mise à jour existants ont été arrêtés.", "smw_smwadmin_updatenotstopped": "Pour arrêter le processus de mise à jour en cours, vous devez activer la case à cocher pour indiquer que vous en êtes vraiment sûr{{GENDER:||e}}.", "smw-admin-docu": "Cette page spéciale vous aide pendant l’installation, la mise à jour, la maintenance et l’utilisation de <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> et comporte d’autres fonctions et tâches administratives ainsi que des statistiques.\nN’oubliez pas de sauvegarder vos données importantes avant d’exécuter des fonctions d’administration.", "smw-admin-environment": "Environnement logiciel", "smw-admin-db": "Configuration de la base de données", "smw-admin-db-preparation": "L’initialisation des tables est en cours et peut prendre un moment avant que soient affichés les résultats (cela dépend de la taille et des optimisations de tables possibles).", "smw-admin-dbdocu": "MediaWiki Sémantique a besoin de sa propre structure de base de données (indépendante de MediaWiki, de manière à ne pas affecter le reste de l’installation de MediaWiki) afin de stocker les données sémantiques.\nCette fonction d’installation peut être lancée plusieurs fois sans causer de dégâts, mais cela n’est nécessaire qu’une seule fois par installation ou mise à niveau.", "smw-admin-permissionswarn": "Si l’opération échoue à cause d’erreurs SQL, l’utilisateur de la base de données utilisée par votre wiki (vérifiez votre fichier « LocalSettings.php ») ne dispose probablement pas des droits suffisants.\nIl faut soit accorder à cet utilisateur le droit de créer ou supprimer des tables, soit saisir temporairement les identifiants du compte principal d’administration de votre base de données dans le fichier « LocalSettings.php », soit utiliser le script de maintenance <code>setupStore.php</code> qui peut utiliser les informations de connexion d’un administrateur.", "smw-admin-dbbutton": "Initialiser ou mettre à niveau les tables", "smw-admin-announce": "Annoncer votre wiki", "smw-admin-announce-text": "Si votre wiki est public, vous pouvez l’inscrire sur <a href=\"https://wikiapiary.com\">WikiApiary</a>, le wiki de suivi des wikis.", "smw-admin-deprecation-notice-title": "Avis d’obsolescence", "smw-admin-deprecation-notice-docu": "La section suivante contient des paramètres qui sont devenus désuets ou ont été supprimés, mais qui ont été détectés comme actifs sur ce wiki. Il est prévu qu’une prochaine version ne prenne plus en charge ces configurations.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> est désuet et sera supprimé de $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> supprimera (ou remplacera) {{PLURAL:$2|l’option suivante|les options suivantes}} :", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> est désuet et sera supprimé de $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> a été remplacé par <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> a été remplacé par <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "Option{{PLURAL:$2||s}} pour <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> :", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> est remplacé par <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> a été supprimé de $2", "smw-admin-deprecation-notice-title-notice": "Paramètres obsolètes", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Paramètres obsolètes</b> montrent les paramètres qui ont été détectés comme utilisés dans ce wiki et qui vont être supprimés ou modifiés dans une prochaine version.", "smw-admin-deprecation-notice-title-replacement": "Paramètres remplacés ou renommés", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Paramètres remplacés ou renommés</b> contient les paramètres qui ont été renommés ou modifiés par ailleurs et il est recommandé de mettre à jour immédiatement leur nom ou leur format.", "smw-admin-deprecation-notice-title-removal": "Paramètres supprimés", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Paramètres supprimés</b> identifie les paramètres qui ont été supprimés dans une version antérieure mais sont encore utilisés sur ce wiki.", "smw-admin-deprecation-notice-section-legend": "Légende", "smw-smwadmin-refresh-title": "Réparation de données et mise à jour", "smw_smwadmin_datarefresh": "Reconstruction des données", "smw_smwadmin_datarefreshdocu": "Il est possible de restaurer toutes les données pour MediaWiki Sémantique à partir du contenu actuel de ce wiki.\nCeci peut être utile pour réparer des données corrompues ou pour rafraîchir les données si le format interne a changé lors des mises à niveau.\nLa mise à jour est exécutée page par page et se sera pas achevée immédiatement.\nLa page suivante spécifie si une mise à jour est en cours d’exécution et vous permet de commencer ou d’arrêter celle-ci (à moins que cette fonctionnalité n’ait été désactivée par l’administrateur du site).", "smw_smwadmin_datarefreshprogress": "<strong>Une mise à jour est déjà en cours d’exécution.</strong>\nIl est normal qu’une mise à jour progresse lentement parce qu’elle ne rafraîchit les données que par petits tronçons, au fur et à mesure qu’un utilisateur accède au wiki.\nPour terminer cette mise à jour plus rapidement, vous pouvez invoquer le script de maintenance de MediaWiki <code>runJobs.php</code> (utilisez l’option <code>--maxjobs 1000</code> pour restreindre le nombre de mises à jour par script lancé).\nProgression estimée de la mise à jour actuelle :", "smw_smwadmin_datarefreshbutton": "Planifier la reconstruction des données", "smw_smwadmin_datarefreshstop": "<PERSON><PERSON><PERSON><PERSON> cette mise à jour", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, j’en suis sûr{{GENDER:$1||e}}.", "smw-admin-job-scheduler-note": "Les tâches (celles activées) dans cette section sont effectuées via la file des tâches pour éviter des situations de verrouillage complet pendant leur exécution. La [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue file des tâches] est responsable de l’exécution et il est critique que le script de maintenance <code>runJobs.php</code> ait une capacité appropriée (voir également le paramètre de configuration <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Élimination des entités désuètes", "smw-admin-outdateddisposal-intro": "Certaines activités (une modification d’un type de propriété, la suppression de pages wiki ou la correction de valeurs erronées) créeront [https://www.semantic-mediawiki.org/wiki/Outdated_entities des entités désuètes] et il est conseillé de les supprimer périodiquement pour libérer l’espace occupé par leur tables.", "smw-admin-outdateddisposal-active": "Une tâche d’élimination des entités désuètes a été planifiée.", "smw-admin-outdateddisposal-button": "Programmer un nettoyage", "smw-admin-feature-disabled": "Cette fonctionnalité a été désactivée sur ce wiki ; veuil<PERSON>z consulter la page d’aide sur les <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">paramètres</a> ou contacter un administrateur système.", "smw-admin-propertystatistics-title": "Reconstruction des statistiques de propriété", "smw-admin-propertystatistics-intro": "Reconstruit toutes les statistiques d’utilisation des propriétés et donc met à jour et corrige les [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count compteurs d’utilisation] des propriétés.", "smw-admin-propertystatistics-active": "Une tâche de reconstruction de propriété a été planifiée.", "smw-admin-propertystatistics-button": "Programmer une reconstruction de statistiques", "smw-admin-fulltext-title": "Reconstruction de la recherche en texte intégral", "smw-admin-fulltext-intro": "Reconstruit l’index de recherche à partir des tables de propriété avec un type de données où la « [https://www.semantic-mediawiki.org/wiki/Full-text recherche en texte intégral] » est activée. Les modifications des règles d’indexation (mots insignifiants modifiés, nouveau découpage des racines et affixes lexicaux, etc...) ou d’une table récemment ajoutée ou modifiée nécessitent de relancer cette tâche.", "smw-admin-fulltext-active": "Une tâche de reconstruction de la recherche en texte intégral a été planifiée.", "smw-admin-fulltext-button": "Programmer une reconstruction de l’index de recherche en texte intégral", "smw-admin-support": "<PERSON><PERSON><PERSON><PERSON> <PERSON>aide", "smw-admin-supportdocu": "Diverses ressources sont fournies pour vous aider en cas de problèmes :", "smw-admin-installfile": "Si vous rencontrez des problèmes lors de votre installation, commencez par regarder le guide en ligne dans le <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">fichier INSTALL</a> et la <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">page d’aide pour l’installation</a>.", "smw-admin-smwhomepage": "La documentation complète pour l’utilisation de MediaWiki Sémantique se trouve sur <b><a href=\"https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_-_Page_d%27accueil\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Les anomalies peuvent être signalées sur le <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">traceur d’anomalies</a>, la page de <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">signalement d’anomalies</a> fournit des indications sur la manière d’écrire un rapport d’anomalie efficace.", "smw-admin-questions": "Si vous avez d’autres questions ou des suggestions, joignez-vous à la discussion sur la <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">liste de discussion des utilisateurs</a> de MediaWiki Sémantique.", "smw-admin-other-functions": "Autres fonctions", "smw-admin-statistics-extra": "Fonctions statistiques", "smw-admin-statistics": "Statistiques", "smw-admin-supplementary-section-title": "Fonctions supplémentaires", "smw-admin-supplementary-section-subtitle": "Fonctions système prises en charge", "smw-admin-supplementary-section-intro": "Cette section fournit des fonctions supplémentaires au delà du domaine des activités de maintenance et il est possible que certaines fonctions listées (voir la [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentation]) soient restreintes ou indisponibles et donc inaccessibles sur ce wiki.", "smw-admin-supplementary-settings-title": "Configuration et paramètres", "smw-admin-supplementary-settings-intro": "<u>$1</u> affiche les paramètres qui définissent le comportement de MediaWiki Sémantique", "smw-admin-main-title": "MediaWiki Sémantique ▸ $1", "smw-admin-supplementary-operational-statistics-title": "Statistiques opérationnelles", "smw-admin-supplementary-operational-statistics-short-title": "statistiques opérationnelles", "smw-admin-supplementary-operational-statistics-intro": "Affiche un jeu étendu de <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Recherche et libération d’objet", "smw-admin-supplementary-idlookup-short-title": "recherche et suppression d’entité", "smw-admin-supplementary-idlookup-intro": "Prend en charge une fonction simple de <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Recherche des entités en doublon", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> pour trouver les entités qui sont marquées comme doublons dans la matrice des tables sélectionnées", "smw-admin-supplementary-duplookup-docu": "Cette page répertorie les entrées des tables sélectionnées qui ont été classées comme [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities doublons]. Les entrées en doublon ne doivent (le cas échéant) se produire que dans de rares occasions potentiellement provoquées par l’interruption d’une transaction de mise à jour ou l’échec d’une annulation.", "smw-admin-supplementary-operational-statistics-cache-title": "Statistiques du cache", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> affiche un ensemble sélectionné de statistiques relatives au cache", "smw-admin-supplementary-operational-table-statistics-title": "Statistiques de tables", "smw-admin-supplementary-operational-table-statistics-short-title": "statistiques de tables", "smw-admin-supplementary-operational-table-statistics-intro": "G<PERSON><PERSON> les <u>$1</u> pour un ensemble sélectionné de tables", "smw-admin-supplementary-operational-table-statistics-explain": "Cette section contient les statistiques de la table sélectionnée pour aider les administrateurs et les superviseurs de données à prendre des décisions éclairées sur l’état du serveur et du moteur de stockage.", "smw-admin-supplementary-operational-table-statistics-legend": "La légende décrit quelques unes des clés utilisées pour les statistiques de la table et comprend :", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code>, le nombre total de lignes dans une table ;", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> dernier identifiant actuellement utilisé\n* <code>duplicate_count</code> nombre de doublons trouvés dans <code>id_table</code> (voir aussi [[Special:SemanticMediaWiki/duplicate-lookup|Recherche d’entités en doublon]])\n* <code>rows.rev_count</code> nombre de lignes qui ont un <code>revision_id</code> affecté qui indique un lien direct à une page wiki\n* <code>rows.smw_namespace_group_by_count</code> nombre de lignes agrégées pour les espaces de noms utilisés dans la table\n* <code>rows.smw_proptable_hash.query_match_count</code> nombre de sous-objets de requête avec une référence de table correspondante\n* <code>rows.smw_proptable_hash.query_null_count</code> nombre de sous-objets de requête sans référence de table (référence non liée flottante)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> pourcentage de termes uniques (un faible pourcentage indique que des termes répétitifs occupent le contenu et l’index de la table)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> nombre de termes qui n’apparaissent qu’une seule fois\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> nombre de termes qui apparaissent plus d’une fois", "smw-admin-supplementary-elastic-version-info": "Version", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> affiche des détails sur les paramètres et les statistiques d’indexation", "smw-admin-supplementary-elastic-docu": "Cette page contient des informations sur les paramètres, les correspondances, la santé et les statistiques d’indexation liées à une grappe Elasticsearch connectée à MediaWiki Sémantique et son [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Fonctions prises en charge", "smw-admin-supplementary-elastic-settings-title": "Paramètres (pour les index)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> utilisé par ElasticSearch pour gérer les index de MediaWiki Sémantique", "smw-admin-supplementary-elastic-mappings-title": "Correspondances", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> pour lister les correspondances d’index et de champ", "smw-admin-supplementary-elastic-mappings-docu": "Cette page contient les détails de la correspondance des champs utilisés avec les index actuels. Il est recommandé de gérer les correspondances en tenant compte de <code>index.mapping.total_fields.limit</code> (spécifie le nombre maximal de champs autorisés pour un index).", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code> fait référence au nombre de champs intégrés indexés alors que <code>nested_fields</code> fait référence à nombre cumulé de champs additionnels affectés à un champ intégré pour prendre en charge des modèles de recherche structurés spécifiques.", "smw-admin-supplementary-elastic-mappings-summary": "Résumé", "smw-admin-supplementary-elastic-mappings-fields": "Correspondances de champ", "smw-admin-supplementary-elastic-nodes-title": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> affiche les statistiques sur les nœuds", "smw-admin-supplementary-elastic-indices-title": "Index utilisés", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> fournit une vue d’ensemble des index disponibles et de leurs statistiques", "smw-admin-supplementary-elastic-statistics-title": "Statistiques", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> affiche les statistiques au niveau des index", "smw-admin-supplementary-elastic-statistics-docu": "Cette page fournit une vision des statistiques d’index pour différentes opérations qui se produisent au niveau des index ; les statistiques renvoyées sont agrégées avec les agrégats primaires et totaux. La [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html page d’aide] contient une description détaillée des statistiques d’index disponibles.", "smw-admin-supplementary-elastic-status-replication": "État de réplication", "smw-admin-supplementary-elastic-status-last-active-replication": "Dernière réplication active : $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Intervalle de mise à jour : $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Arriéré des tâches de récupération : $1 (estimation)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Arriéré des tâches d’ingestion (de fichiers) : $1 (estimation)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Réplication verrouillée : $1 (reconstruction en cours)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Surveillance de réplication (active) : $1", "smw-admin-supplementary-elastic-replication-header-title": "État de réplication", "smw-admin-supplementary-elastic-replication-function-title": "Réplication", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> montre les informations concernant les réplications échouées", "smw-admin-supplementary-elastic-replication-docu": "Cette page fournit des informations sur l’[https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring état de réplication] des entités qui ont été signalées comme ayant des problèmes avec la grappe ElasticSearch. Il est recommandé d'analyser les entités listées et de purger le contenu afin de confirmer que le problème était temporaire.", "smw-admin-supplementary-elastic-replication-files-docu": "Il faut noter que pour la liste des fichiers, la tâche d’[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion ingestion de fichiers] doit être exécutée d’abord et doit achever son traitement.", "smw-admin-supplementary-elastic-replication-files": "Fichiers", "smw-admin-supplementary-elastic-replication-pages": "Pages", "smw-admin-supplementary-elastic-endpoints": "Points de terminaison", "smw-admin-supplementary-elastic-config": "Configurations", "smw-admin-supplementary-elastic-no-connection": "Le wiki est actuellement '''incapable''' d’établir une connexion à la grappe ElasticSearch, ve<PERSON><PERSON><PERSON> contacter l’administrateur du wiki pour investiguer sur le problème, car cela rend indisponible la capacité d’index et de requêtage du système.", "smw-list-count": "La liste contient $1 entrée{{PLURAL:$1||s}}.", "smw-property-label-uniqueness": "Le libellé « $1 » représente également au moins une autre propriété. Veuillez vous reporter à la [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness page d’aide] pour trouver comment résoudre ce problème.", "smw-property-label-similarity-title": "Compte rendu de similitude des libellés de propriété", "smw-property-label-similarity-intro": "<u>$1</u> calcule les similitudes pour les libellés de propriété existantes", "smw-property-label-similarity-threshold": "Seuil :", "smw-property-label-similarity-type": "Afficher l’identifiant de type", "smw-property-label-similarity-noresult": "Aucun résultat trouvé pour les options sélectionnées.", "smw-property-label-similarity-docu": "Cette page compare et affiche la [https://www.semantic-mediawiki.org/wiki/Property_similarity distance de similarité syntaxique] (à ne pas confondre avec la similarité sémantique ou lexicale) entre les libellés de propriété et signale ces libellés quand ils dépassent le seuil. Le rapport peut aider à filtrer les propriétés mal orthographiées ou équivalentes qui représentent le même concept (voir la page spéciale des [[Special:Properties|propriétés]] afin d’aider à clarifier le concept et l’utilisation des propriétés signalées). Le seuil peut être réglé pour soit élargir ou réduire la distance utilisée pour la correspondance approximée. <code>[[Property:$1|$1]]</code> est utilisé pour exclure des propriétés de l’analyse.", "smw-admin-operational-statistics": "Cette page contient des statistiques opérationnelles collectées dans ou par l’intermédiaire des fonctions relatives à MediaWiki Sémantique. Vous pouvez [[Special:Statistics|<b>trouver ici</b>]] une liste étendue des statistiques spécifiques à ce wiki.", "smw_adminlinks_datastructure": "Structure des données", "smw_adminlinks_displayingdata": "Affichage des données", "smw_adminlinks_inlinequerieshelp": "Aide sur les requêtes intégrées", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Nombre d’utilisations] estimé : {{PLURAL:$2|}}'''$2'''", "smw-property-indicator-type-info": "Propriété définie par l{{PLURAL:$1|’utilisateur|e système}}", "smw-property-indicator-last-count-update": "Nombre estimé d’utilisations.\nDernière mise à jour : $1", "smw-concept-indicator-cache-update": "Compteur en cache.\nDernière mise à jour : $1", "smw-createproperty-isproperty": "Ceci est une propriété de type $1.", "smw-createproperty-allowedvals": "L{{PLURAL:$1|a|es}} valeur{{PLURAL:$1||s}} autorisée{{PLURAL:$1||s}} pour cette propriété {{PLURAL:$1|es|son}}t :", "smw-paramdesc-category-delim": "Le délimiteur", "smw-paramdesc-category-template": "Un modèle pour mettre en forme les éléments", "smw-paramdesc-category-userparam": "Un paramètre à passer au modèle", "smw-info-par-message": "Message à afficher.", "smw-info-par-icon": "Icône à afficher : soit « info », soit « attention ».", "prefs-smw": "MediaWiki Sémantique", "prefs-general-options": "Options générales", "prefs-extended-search-options": "Recherche étendue", "prefs-ask-options": "Recherche sémantique", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki/fr MediaWiki Sémantique] et ses extensions associées fournissent des préférences individuelles pour un groupe sélectionné de fonctionnalités et fonctions. Une liste des paramètres individuels avec leur description et leurs caractéristiques est disponible sur cette [https://www.semantic-mediawiki.org/wiki/Help:User_preferences page d’aide (en anglais)] .", "smw-prefs-ask-options-tooltip-display": "Afficher le texte du paramètre comme une info-bulle sur la page spéciale du [[Special:Ask|constructeur de requêtes]] #ask.", "smw-prefs-ask-options-compact-view-basic": "Activer l’affichage compact de base", "smw-prefs-help-ask-options-compact-view-basic": "Si activé, affiche un ensemble réduit de liens sur la vue compacte de Special:Ask", "smw-prefs-general-options-time-correction": "Activer la correction des horodatages pour les pages spéciales en utilisant les préférences locales de l'utilisateur pour le [[Special:Preferences#mw-prefsection-rendering|fuseau horaire]].", "smw-prefs-general-options-jobqueue-watchlist": "Afficher la liste de suivi de la file des tâches dans ma barre personnelle", "smw-prefs-help-general-options-jobqueue-watchlist": "Si activé, affiche une [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist liste] sélectionnée de files de tâches suivies en attente, avec la taille estimée de leur file.", "smw-prefs-general-options-disable-editpage-info": "Désactiver le texte d’introduction sur la page de modification", "smw-prefs-general-options-disable-search-info": "Désactiver les informations d’aide à la syntaxe sur la page de recherche standard", "smw-prefs-general-options-suggester-textinput": "Activer l’assistance à la saisie pour les entités sémantiques", "smw-prefs-help-general-options-suggester-textinput": "Si activé, permet d’utiliser une [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance aide à la saisie] pour trouver des propriétés, des concepts et des catégories appropriées au contexte de saisie.", "smw-prefs-general-options-show-entity-issue-panel": "Afficher le panneau des problèmes d’entité", "smw-prefs-help-general-options-show-entity-issue-panel": "Si activé, exécute des vérifications d’intégrité sur chaque page et affiche le [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel panneau des problèmes d’entité].", "smw-prefs-factedsearch-profile": "Sélectionnez un profil par défaut [[Special:FacetedSearch|faceted search]]:", "smw-ui-tooltip-title-property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-quantity": "Conversion d’unité", "smw-ui-tooltip-title-info": "Informations", "smw-ui-tooltip-title-service": "Liens de service", "smw-ui-tooltip-title-warning": "Avertissement", "smw-ui-tooltip-title-error": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Paramètre", "smw-ui-tooltip-title-event": "Événement", "smw-ui-tooltip-title-note": "Note", "smw-ui-tooltip-title-legend": "Légende", "smw-ui-tooltip-title-reference": "Référence", "smw_unknowntype": "Le type « $1 » de cette propriété n’est pas valide", "smw-concept-cache-text": "Le concept a un total de $1 page{{PLURAL:$1||s}} et a été mis à jour la dernière fois le $2 à $3.", "smw_concept_header": "Page du concept « $1 »", "smw_conceptarticlecount": "Affichage de $1 page{{PLURAL:$1||s}} ci-dessous.", "smw-qp-empty-data": "Les données demandées n’ont pas pu être affichées à cause de certains critères de sélection insuffisants.", "right-smw-admin": "Accéder aux tâches d’administration (MediaWiki Sémantique)", "right-smw-patternedit": "Modifier l’accès pour gérer les motifs ou expressions rationnelles autorisés (MediaWiki Sémantique)", "right-smw-pageedit": "Modifier l’accès aux pages annotées avec <code>Est protégée contre la modification</code> (MediaWiki Sémantique)", "right-smw-schemaedit": "Modifier les [https://www.semantic-mediawiki.org/wiki/Help:Schema pages de schéma] (MediaWiki Sémantique)", "right-smw-viewjobqueuewatchlist": "Accéder à la fonctionnalité de [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist liste de suivi] de files de tâches (MediaWiki Sémantique)", "right-smw-viewentityassociatedrevisionmismatch": "Accéder aux informations sur une incohérence de révision associée à une entité (MediaWiki Sémantique)", "right-smw-vieweditpageinfo": "Voir l’[https://www.semantic-mediawiki.org/wiki/Help:Edit_help aide pour modifier] (MediaWiki Sémantique)", "restriction-level-smw-pageedit": "protégée (sauf pour les utilisateurs éligibles)", "action-smw-patternedit": "modifier les expressions rationnelles utilisées par MediaWiki Sémantique", "action-smw-pageedit": "modifier les pages annotées avec la propriété <code>Est protégée contre la modification</code> (MediaWiki Sémantique)", "group-smwadministrator": "Administrateurs (MediaWiki Sémantique)", "group-smwadministrator-member": "administrat{{GENDER:$1|eur|rice}} (MediaWiki Sémantique)", "grouppage-smwadministrator": "{{ns:project}}:Administrateurs (MediaWiki Sémantique)", "group-smwcurator": "Conservateurs (MediaWiki Sémantique)", "group-smwcurator-member": "conservat{{GENDER:$1|eur|rice}} (MediaWiki Sémantique)", "grouppage-smwcurator": "{{ns:project}}:Curateurs (MediaWiki Sémantique)", "group-smweditor": "Modificateurs (MediaWiki Sémantique)", "group-smweditor-member": "modificat{{GENDER:$1|eur|rice}} (MediaWiki Sémantique)", "grouppage-smweditor": "{{ns:project}}:<PERSON><PERSON><PERSON>ct<PERSON><PERSON> (Semantic MediaWiki)", "action-smw-admin": "accéder aux tâches d’administration de MediaWiki Sémantique", "action-smw-ruleedit": "modifier les pages de règle (MediaWiki Sémantique)", "smw-property-namespace-disabled": "La propriété [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks espace de noms] est désactivée et tenter de déclarer un type ou d’autres caractéristiques spécifiques des propriétés pour cette propriété n’est pas possible.", "smw-property-predefined-default": "« $1 » est une propriété prédéfinie de type $2.", "smw-property-predefined-common": "Cette propriété est pré-déployée (appelée aussi [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propriété spéciale]) et est accompagnée de droits d’administration supplémentaires, mais peut être utilisée exactement comme toute autre [https://www.semantic-mediawiki.org/wiki/Property propriété définie par l’utilisateur].", "smw-property-predefined-ask": "« $1 » est une propriété prédéfinie qui représente les méta-informations (sous la forme d’un [https://www.semantic-mediawiki.org/wiki/Subobject sous-objet]) au sujet de requêtes individuelles. Ceci est fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-asksi": "« $1 » est une propriété prédéfinie qui recueille le nombre de conditions utilisées dans une requête. Ceci est fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-askde": "« $1 » est une propriété prédéfinie qui informe sur la profondeur d’une requête. Ceci est fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-long-askde": "Ceci est une valeur numérique calculée d’après le niveau d’imbrication des sous-requêtes, les chaînes de propriétés et les éléments de description disponibles par l’exécution d’une requête restreinte par le paramètre de configuration <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "« $1 » est une propriété prédéfinie qui décrit les paramètres qui peuvent influencer un résultat de recherche. Ceci est fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-long-askpa": "Ceci fait partie de la collection de propriétés qui spécifient un [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler profil de recherche].", "smw-sp-properties-docu": "Cette page liste les [https://www.semantic-mediawiki.org/wiki/Property propriétés] et leur compteur d’utilisation disponible pour ce wiki. Pour avoir des statistiques de comptage à jour, il est recommandé de lancer régulièrement le script de maintenance des [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics statistiques de propriété]. Pour un affichage différencié, voyez les pages spéciales des [[Special:UnusedProperties|propriétés non utilisées]] ou [[Special:WantedProperties|souhaitées]].", "smw-sp-properties-cache-info": "Les données listées ont été récupérées depuis le [https://www.semantic-mediawiki.org/wiki/Caching cache] et leur dernière mise à jour date du $1.", "smw-sp-properties-header-label": "Liste des propriétés", "smw-admin-settings-docu": "Affiche une liste de tous les paramètres par défaut et des paramètres localisés qui se rapportent à l’environnement de MediaWiki Sémantique. Pour le détail des paramètres individuels, veuillez consulter la page d’aide sur la [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration].", "smw-sp-admin-settings-button": "Générer la liste des paramètres", "smw-admin-idlookup-title": "Recherche", "smw-admin-idlookup-docu": "Cette section décrit les détails techniques concernant des entités individuelles (page wiki, sous-objet, propriété, etc.) dans MediaWiki Sémantique. L’entrée peut être un identifiant numérique ou une valeur de chaîne correspondant au champ de recherche approprié, cependant tout identifiant de référence est relatif à MediaWiki Sémantique et non à un identifiant MediaWiki de page ou de révision.", "smw-admin-iddispose-title": "Élimination", "smw-admin-iddispose-docu": "Il fait noter que l’opération d’élimination n’est pas restreinte et supprimera l’entité du moteur de stockage en même temps que toutes ses références dans les tables associées, si elle est confirmée. Veuillez effectuer cette tâche avec <strong>précaution</strong> et seulement après avoir consulté la [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentation].", "smw-admin-iddispose-done": "L’identifiant « $1 » a été supprimé du serveur de stockage.", "smw-admin-iddispose-references": "L’identifiant « $1 » {{PLURAL:$2|n’a aucune|a au moins une}} référence active :", "smw-admin-iddispose-references-multiple": "Liste des correspondances avec au moins un enregistrement de référence actif.", "smw-admin-iddispose-no-references": "La recherche n’a pu trouver aucune entrée de table correspondant à « $1 ».", "smw-admin-idlookup-input": "Rechercher :", "smw-admin-objectid": "Identifiant :", "smw-admin-tab-general": "Vue d’ensemble", "smw-admin-tab-notices": "Avis d’obsolescence", "smw-admin-tab-maintenance": "Maintenance", "smw-admin-tab-supplement": "Fonctions supplémentaires", "smw-admin-tab-registry": "Registre", "smw-admin-tab-alerts": "<PERSON><PERSON><PERSON>", "smw-admin-alerts-tab-deprecationnotices": "Avis d’obsolescence", "smw-admin-alerts-tab-maintenancealerts": "Alertes de maintenance", "smw-admin-alerts-section-intro": "Cette section affiche les alertes et les avis liés aux paramètres, aux opérations et aux autres activités qui ont été classés comme nécessitant l’attention d’un administrateur ou d’un utilisateur ayant les droits appropriés.", "smw-admin-maintenancealerts-section-intro": "Les alertes et avis suivants doivent être résolus et, quoique pas indispensables, il est prévu qu’ils aident à améliorer la maintenabilité du système et des opérations.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Optimisation des tables", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Le système a trouvé que la dernière [https://www.semantic-mediawiki.org/wiki/Table_optimization optimisation de table] a été lancée il y a $2 jours (enregistrement du $1), ce qui dépasse les $3 jours du seuil de maintenance. Comme mentionné dans la documentation, lancer les optimisations permettra au planificateur de requêtes de mieux prendre ses décisions sur les requêtes ; il est donc suggéré de lancer l’optimisation des tables de façon régulière.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Entités obsolètes", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Le système a comptabilisé $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities entités obsolètes] et a atteint un niveau critique de maintenance sans surveillance qui dépasse le seuil de $2. Il est recommandé de lancer le script de maintenance [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>].", "smw-admin-maintenancealerts-invalidentities-alert-title": "Entités non valides", "smw-admin-maintenancealerts-invalidentities-alert": "Le système a fait correspondre $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|entité|entités}}] à un [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace espace de nom non maintenu] et il est recommandé de lancer le script de maintenance [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] ou [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>].", "smw-admin-deprecation-notice-section": "MediaWiki Sémantique", "smw-admin-configutation-tab-settings": "Paramètres", "smw-admin-configutation-tab-namespaces": "Espaces de noms", "smw-admin-configutation-tab-schematypes": "Types de schéma", "smw-admin-maintenance-tab-tasks": "Tâches", "smw-admin-maintenance-tab-scripts": "Scripts de maintenance", "smw-admin-maintenance-no-description": "Aucune description.", "smw-admin-maintenance-script-section-title": "Liste des scripts de maintenance disponibles", "smw-admin-maintenance-script-section-intro": "Les scripts de maintenance suivants nécessitent un administrateur et l’accès à la ligne de commande pour pouvoir exécuter les scripts listés.", "smw-admin-maintenance-script-description-dumprdf": "Export en RDF des triplets existants.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Ce script est utilisé pour gérer les caches de concept pour MediaWiki Sémantique, où il peut créer, supprimer et mettre à jour les caches sélectionnés.", "smw-admin-maintenance-script-description-rebuilddata": "Re<PERSON><PERSON><PERSON> toutes les données sémantiques dans la base de données, en bouclant à travers toutes les pages qui ont des données sémantiques.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Reconstruit l’index d’Elasticsearch (uniquement pour les installations qui utilisent <code>ElasticStore</code>), en bouclant sur toutes les entités qui ont des données sémantiques.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Chercher les entités manquantes dans Elasticsearch (uniquement pour les installations qui utilisent <code>ElasticStore</code>) et mettre en file d’attente les tâches appropriées de mise à jour.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Reconstruit l’index de recherche en texte brut <code>SQLStore</code> (pour les installations où le paramètre a été activé).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Reconstruit les statistiques d’utilisation pour toutes les entités de propriété.", "smw-admin-maintenance-script-description-removeduplicateentities": "Supprime les entités en doublon trouvées dans les tables sélectionnées qui n’ont aucune référence active.", "smw-admin-maintenance-script-description-setupstore": "Configure le serveur de stockage et de requête comme défini dans <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Met à jour le champ <code>smw_sort</code> dans <code>SQLStore</code> (conformément au paramètre [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "Renseigne le champ <code>smw_hash</code> pour les lignes sans valeur.", "smw-admin-maintenance-script-description-purgeentitycache": "Effacer les entrées dans le cache pour les entités connues et leurs données associées.", "smw-admin-maintenance-script-description-updatequerydependencies": "Mettre à jour les requêtes et les dépendances de requête (voir le paramètre [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore] ).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "<PERSON><PERSON><PERSON> les entités et liens de requête obsolètes.", "smw-admin-maintenance-script-description-runimport": "Remplir et importer du contenu automatiquement découvert depuis [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Scripts de mise à jour", "smw-admin-maintenance-script-section-rebuild": "Scripts de reconstruction", "smw-livepreview-loading": "Chargement en cours en cours...", "smw-sp-searchbyproperty-description": "Cette page fournit une simple [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces interface de navigation] pour trouver des entités décrites par une propriété et une valeur nommée. D’autres interfaces de recherche disponibles comprennent la [[Special:PageProperty|page recherche de propriété]], et le [[Special:Ask|constructeur de requêtes « ask »]].", "smw-sp-searchbyproperty-resultlist-header": "Liste de résultats", "smw-sp-searchbyproperty-nonvaluequery": "Une liste de valeurs qui ont la propriété « $1 » affectée.", "smw-sp-searchbyproperty-valuequery": "Une liste de pages qui ont la propriété « $1 » avec la valeur « $2 » annotée.", "smw-datavalue-number-textnotallowed": "« $1 » ne peut pas être affecté à un type de nombre déclaré avec la valeur $2.", "smw-datavalue-number-nullnotallowed": "« $1 » a retourné un « NULL » qui n’est pas une valeur numérique autorisée.", "smw-editpage-annotation-enabled": "Cette page prend en charge les annotations sémantiques au sein du texte (par exemple <nowiki>« [[Is specified as::World Heritage Site]] »</nowiki>) afin de bâtir du contenu structuré et requêtable fourni par MediaWiki Sémantique. Pour une description complète sur la façon d’utiliser les annotations ou la fonction d’analyseur « #ask », veuil<PERSON>z consulter les pages d’aide [https://www.semantic-mediawiki.org/wiki/Help:Getting_started Pour commencer], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation Annotation au sein du texte] et [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries Requêtes incorporées].", "smw-editpage-annotation-disabled": "Cette page n’autorise pas les annotations sémantiques au sein du texte en raison de restrictions sur l’espace de noms. Des détails sur la façon d’activer l’espace de noms peuvent être trouvés dans la page d’aide concernant la [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration].", "smw-editpage-property-annotation-enabled": "Cette propriété peut être étendue en utilisant des annotations sémantiques afin de spécifier un type de donnée (par exemple <nowiki>« [[Has type::Page]] »</nowiki>) ou d’autres déclarations secondaires (par exemple <nowiki>« [[Subproperty of::dc:date]] »</nowiki>). Pour une description sur la façon de faire pour enrichir cette page, veuillez consulter les pages d’aide concernant la [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration déclaration de propriété] ou la [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes liste des types de données disponibles].", "smw-editpage-property-annotation-disabled": "Cette propriété ne peut pas être étendue avec une annotation de type de données (par exemple <nowiki>« [[Has type::Page]] »</nowiki>) parce qu’elle est déjà prédéfinie (voyez la page d’aide concernant les [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propriétés spéciales] pour plus d’informations).", "smw-editpage-concept-annotation-enabled": "Ce concept peut être étendu en utilisant la fonction d’analyseur « #concept ». Pour une description sur la manière d’utiliser « #concept », voyez la page d’aide concernant les [https://www.semantic-mediawiki.org/wiki/Help:Concepts concepts].", "smw-search-syntax-support": "L’entrée recherchée accepte l’utilisation de la [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search syntaxe de requête sémantique] pour aider à trouver des résultats correspondants au moyen de MediaWiki Sémantique.", "smw-search-input-assistance": "L’[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance assistant de saisie] est également activé pour faciliter la présélection des propriétés et des catégories disponibles.", "smw-search-help-intro": "Une entrée <code><nowiki>[[ ... ]]</nowiki></code> signalera au processeur d’entrée d’utiliser le serveur de recherche MediaWiki Sémantique. Il faut noter que combiner <code><nowiki>[[ ... ]]</nowiki></code> avec une recherche de texte non structuré comme <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> n’est pas pris en charge.", "smw-search-help-structured": "Recherches structurées :\n* <code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (comme [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context contexte filtré])\n* <code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (avec un [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context contexte de recherche])", "smw-search-help-proximity": "Les recherches de proximité (une propriété étant inconnue, disponible '''uniquement''' pour les serveurs qui intègrent la recherche en texte intégral) :\n\n* <code><nowiki>[[in:lorem ipsum]]</nowiki></code> (rechercher tous les documents où « lorem » et « ipsum » ont été indexés)\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (faire correspondre « lorem ipsum » en tant qu’expression littérale)", "smw-search-help-ask": "Les liens suivants expliqueront comment utiliser la syntaxe <code>#ask</code> :\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Sélection des pages] décrit comment sélectionner des pages et construire des conditions ;\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Opérateurs de recherche] liste les opérateurs de recherche disponibles, y compris ceux pour les requêtes dans une plage et avec des jokers.", "smw-search-input": "Entrée et recherche", "smw-search-help-input-assistance": "L’[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance assistance à la saisie] est fournie pour le champ de saisie et nécessite d’utiliser un des préfixes suivants :\n\n* <code>p:</code> pour activer les suggestions de propriété (par ex. <code><nowiki>[[p:Has …</nowiki></code>) ;\n* <code>c:</code> pour activer les suggestions de catégorie ;\n* <code>con:</code> pour activer les suggestions de concept.", "smw-search-syntax": "Syntaxe", "smw-search-profile": "<PERSON><PERSON><PERSON>", "smw-search-profile-tooltip": "Rechercher des fonctions en relation avec MediaWiki Sémantique", "smw-search-profile-sort-best": "Meilleure correspondance", "smw-search-profile-sort-recent": "Le plus récent", "smw-search-profile-sort-title": "Titre", "smw-search-profile-extended-help-intro": "Le [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile profil étendu] pour Special:Search fournit un accès aux fonctions de recherche spécifiques à MediaWiki Sémantique et à son serveur de requête pris en charge.", "smw-search-profile-extended-help-sort": "Spécifie une préférence de tri pour l’affichage des résultats avec :", "smw-search-profile-extended-help-sort-title": "* « Titre » utilisera le titre de la page (ou le titre affiché) comme critère de tri.", "smw-search-profile-extended-help-sort-recent": "* « Le plus récent » affichera d’abord les entités les plus récemment modifiées (les sous-objets seront supprimés car ces entités ne sont pas annotées avec une [[Property:Modification date|date de modification]]).", "smw-search-profile-extended-help-sort-best": "* « Meilleure correspondance » triera les entités par [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy pertinence] selon les scores fournis par le serveur.", "smw-search-profile-extended-help-form": "Les formulaires sont fournis (s’ils sont maintenus) pour faire correspondre des cas d’utilisation spécifiques en exposant différentes propriétés et différents champs de saisie de valeur, afin de limiter le processus de saisie et de faciliter le traitement d’une requête de recherche pour les utilisateurs (voir $1).", "smw-search-profile-extended-help-namespace": "Le champ de sélection de l’espace de noms est masqué dès que le formulaire est sélectionné, mais il peut être rendu visible à l’aide du bouton « afficher/masquer ».", "smw-search-profile-extended-help-search-syntax": "Le champ de saisie de recherche prend en charge l’utilisation de la syntaxe <code>#ask</code> pour définir un contexte de recherche spécifique de MediaWiki Sémantique. Les expressions utiles comprennent :", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> pour trouver tout ce qui contient l’expression indiquée, ce qui est particulièrement utile quand le contexte de recherche ou les propriétés impliquées sont inconnus (par ex. <code>in:(lorem && ipsum)</code> est équivalent à <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> pour trouver tout ce qui contient l’expression indiquée exactement dans le même ordre", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> pour trouver toute une entité avec la propriété indiquée (par ex. <code>has:(Foo && Bar)</code> est équivalent à <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> pour exclure toute une entité qui contient l’expression indiquée", "smw-search-profile-extended-help-search-syntax-prefix": "* Des préfixes personnalisés supplémentaires sont disponibles et définis, tels que : $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Certaines expressions sont réservées, telles que : <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Certaines des opérations listées ne sont utiles qu’en relation avec un index en texte intégral activé ou avec l’ElasticStore.''", "smw-search-profile-extended-help-query": "A utilisé <code><nowiki>$1</nowiki></code> comme requête.", "smw-search-profile-extended-help-query-link": "Pour plus de détails, veuillez utiliser le $1.", "smw-search-profile-extended-help-find-forms": "formulaires disponibles", "smw-search-profile-extended-section-sort": "Trier par", "smw-search-profile-extended-section-form": "Formulaires", "smw-search-profile-extended-section-search-syntax": "Rechercher l’entrée", "smw-search-profile-extended-section-namespace": "Espace de noms", "smw-search-profile-extended-section-query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-link-caption-query": "constructeur de requête", "smw-search-show": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-hide": "Masquer", "log-name-smw": "Journal de MediaWiki Sémantique", "log-show-hide-smw": "$1 le journal de MediaWiki Sémantique", "logeventslist-smw-log": "Journal de MediaWiki Sémantique", "log-description-smw": "Activités pour les [https://www.semantic-mediawiki.org/wiki/Help:Logging types d’événement activés] qui ont été signalés par MediaWiki Sémantique et ses composants.", "logentry-smw-maintenance": "Événements liés à la maintenance émis par MediaWiki Sémantique", "smw-datavalue-import-unknown-namespace": "L’espace de noms d’importation « $1 » est inconnu. Assurez-vous que les détails d’importation OWL sont disponibles via [[MediaWiki:Smw import $1]].", "smw-datavalue-import-missing-namespace-uri": "Impossible de trouver un espace de nom d’URI « $1 » dans l’[[MediaWiki:Smw import $1|importation $1]].", "smw-datavalue-import-missing-type": "Aucune définition de type trouvée pour « $1 » dans l’[[MediaWiki:Smw import $2|import de $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|importer $1]]", "smw-datavalue-import-invalid-value": "« $1 » n’est pas un format valide et doit consister en « espace_de_nom »:« identifiant » (par ex. « foaf:name »).", "smw-datavalue-import-invalid-format": "La chaîne « $1 » doit pouvoir être divisée en quatre parties, mais le format n’a pas été compris.", "smw-property-predefined-impo": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui décrit une relation vers un [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabulaire importé].", "smw-property-predefined-type": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui décrit le [[Special:Types|type de donnée]] d’une propriété.", "smw-property-predefined-sobj": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui représente un constructeur de [https://www.semantic-mediawiki.org/wiki/Help:Container conteneur].", "smw-property-predefined-long-sobj": "Le conteneur permet d’accumuler les affectations des valeurs de propriété de la même façon qu’une page wiki normale, mais dans un espace d’entités différent qui sera lié au sujet incorporé.", "smw-property-predefined-errp": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui trace les erreurs d’entrée pour les valeurs d’annotation incorrectes.", "smw-property-predefined-long-errp": "Dans la plupart des cas, ceci est causé par une différence de type ou une restriction sur la [[Property:Allows value|valeur]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value « $1 »] est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui peut définir une liste de valeurs autorisées pour limiter l’ensemble des valeurs possibles d’une propriété.", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list « $1 »] est une propriété prédéfinie qui peut spécifier une référence à une liste contenant les valeurs permises pour limiter les affectations de valeur d’une propriété. Ceci est fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-datavalue-property-restricted-annotation-use": "La propriété « $1 » a une zone d’application restreinte et ne peut pas être utilisée comme propriété d’annotation par un utilisateur.", "smw-datavalue-property-restricted-declarative-use": "La propriété « $1 » est une propriété déclarative et peut être utilisée seulement sur une page de propriété ou de catégorie.", "smw-datavalue-property-create-restriction": "La propriété « $1 » n’existe pas et l’utilisateur ne dispose pas du droit « $2 » (voir [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode mode d’autorisation]) pour créer ou annoter des valeurs avec une propriété non approuvée.", "smw-datavalue-property-invalid-character": "« $1 » contient un caractère « $2 » répertorié comme faisant partie du libellé de la propriété et a conséquemment été classé comme non valide.", "smw-datavalue-property-invalid-chain": "L’utilisation de « $1 » comme chaîne de propriété n’est pas autorisée lors du processus d’annotation.", "smw-datavalue-restricted-use": "La valeur de donnée « $1 » est marquée à usage restreint.", "smw-datavalue-invalid-number": "« $1 » ne peut pas être interprété comme un nombre.", "smw-query-condition-circular": "Une condition circulaire possible a été détectée dans « $1 ».", "smw-query-condition-empty": "La description de la requête contient une condition vide.", "smw-types-list": "Liste des types de données", "smw-types-default": "« $1 » est un type de données intégré.", "smw-types-help": "De plus amples informations et des exemples peuvent être trouvés sur la [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 page d’aide].", "smw-type-anu": "« $1 » est une variante du type de données [[Special:Types/URL|URL]] principalement utilisée pour une déclaration d’exportation de type « owl:AnnotationProperty ».", "smw-type-boo": "« $1 » est un type de données de base servant à décrire une valeur vraie ou fausse.", "smw-type-cod": "« $1 » est une variante du type de données [[Special:Types/Text|Text]] à utiliser pour des textes techniques de longueur quelconque, comme du code source.", "smw-type-geo": "« $1 » est un type de données servant à décrire des emplacements géographiques et qui nécessite l’extension [https://www.semantic-mediawiki.org/wiki/Extension:Maps « Maps »] pour fournir une fonctionnalité étendue.", "smw-type-tel": "« $1 » est un type de données spécial servant à décrire des numéros de téléphone internationaux conformément à la RFC 3966.", "smw-type-txt": "« $1 » est un type de données de base servant à décrire des chaînes de caractères de longueur arbitraire.", "smw-type-dat": "« $1 » est un type de données de base servant à représenter des points dans le temps dans un format unifié.", "smw-type-ema": "« $1 » est un type de données spécial servant à représenter un courriel.", "smw-type-tem": "« $1 » est un type de données numérique spécial servant à représenter une température.", "smw-type-qty": "« $1 » est un type de données servant à décrire des quantités avec une représentation numérique et une unité de mesure.", "smw-type-rec": "« $1 » est un type de données conteneur qui spécifie une liste de propriétés typées dans un ordre fixe.", "smw-type-extra-tem": "Le schéma de conversion comprend des unités prises en charge telles que le kelvin ou les degrés Celsius, Fahrenheit et Rankine.", "smw-type-tab-properties": "Propriétés", "smw-type-tab-types": "Types", "smw-type-tab-type-ids": "Identifiants de type", "smw-type-tab-errors": "<PERSON><PERSON><PERSON>", "smw-type-primitive": "De base", "smw-type-contextual": "Contextuel", "smw-type-compound": "Composé", "smw-type-container": "Conteneur", "smw-type-no-group": "Non classé", "smw-special-pageproperty-description": "Cette page fournit une interface de navigation pour trouver toutes les valeurs d’une propriété et une page donnée. D’autres interfaces de recherche disponibles incluent la [[Special:SearchByProperty|recherche de propriété]] et le [[Special:Ask|constructeur de requête « ask »]].", "smw-property-predefined-errc": "« $1 » est une propriété prédéfinie fournie par  [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui représente les erreurs apparues en relation avec des annotations de valeurs impropres ou le traitement des entrées.", "smw-property-predefined-long-errc": "Les erreurs sont enregistrées dans un [https://www.semantic-mediawiki.org/wiki/Help:Container conteneur] qui peut inclure une référence vers la propriété qui a causé le problème.", "smw-property-predefined-errt": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et contenant une description textuelle d’une erreur.", "smw-subobject-parser-invalid-naming-scheme": "Un sous-objet défini par l’utilisateur contenait un schéma de nommage invalide. L’utilisation d’un point ($1) dans les cinq premiers caractères est réservée aux extensions. Vous pouvez définir un [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier identifiant nommé].", "smw-datavalue-record-invalid-property-declaration": "La définition de l’enregistrement contient la propriété « $1 », elle-même déclarée comme un type d’enregistrement, ce qui est interdit.", "smw-property-predefined-mdat": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui correspond à la date de la dernière modification d’un sujet.", "smw-property-predefined-cdat": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui correspond à la date de première révision d’un sujet.", "smw-property-predefined-newp": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui indique si un sujet est nouveau ou non.", "smw-property-predefined-ledt": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui contient le nom de la page de l’utilisateur qui a créé la dernière révision.", "smw-property-predefined-mime": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui décrit le type de MIME d’un fichier téléchargé.", "smw-property-predefined-media": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui décrit le type de média d’un fichier téléchargé.", "smw-property-predefined-askfo": "« $1 » est une propriété prédéfinie fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui contient le nom du format de résultat utilisé dans une requête.", "smw-property-predefined-askst": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui décrit les conditions de la requête sous forme de chaîne.", "smw-property-predefined-askdu": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui contient une valeur temporelle (en secondes) représentant le temps pris par la requête pour achever son exécution.", "smw-property-predefined-asksc": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui identifie des sources de requêtes alternatives (par ex. distantes, fédérées).", "smw-property-predefined-askco": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour décrire l’état d’une requête ou de ses composants.", "smw-property-predefined-long-askco": "Le nombre ou les chiffres attribués représentent un état interne codifié expliqué sur la [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler page d’aide].", "smw-property-predefined-prec": "« $1 » est une propriété prédéfinie qui décrit une [https://www.semantic-mediawiki.org/wiki/Help:Display_precision précision d’affichage] (en chiffres décimaux) pour les types de données numériques.", "smw-property-predefined-attch-link": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui recueille les liens des images et des fichiers inclus dans une page.", "smw-property-predefined-inst": "« $1 » est une propriété interne prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui stocke des informations de catégorie indépendamment de MediaWiki.", "smw-property-predefined-unit": "« $1 » est une propriété déclarative prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour définir les unités d’affichage des propriétés typées numériques.", "smw-property-predefined-long-unit": "Une liste séparée par des virgules permet de décrire les unités ou les formats à utiliser pour l’affichage.", "smw-property-predefined-conv": "« $1 » est une propriété déclarative prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour définir le facteur de conversion pour certaines unités d’une quantité physique.", "smw-property-predefined-serv": "« $1 » est une propriété déclarative prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour ajouter des liens de service à une propriété.", "smw-property-predefined-redi": "« $1 » est une propriété interne prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour enregistrer les redirections.", "smw-property-predefined-subp": "« $1 » est une propriété déclarative prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour définir qu’une propriété est une [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of sous-propriété] d’une autre.", "smw-property-predefined-subc": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour définir qu’une catégorie est une [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of sous-catégorie] d’une autre.", "smw-property-predefined-conc": "« $1 » est une propriété interne prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour définir un concept associé.", "smw-property-predefined-err-type": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour identifier un groupe ou une classe d’[https://www.semantic-mediawiki.org/wiki/Help:Processing_errors erreurs de traitement].", "smw-property-predefined-skey": "« $1 » est une propriété interne prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour conserver une référence de tri.", "smw-property-predefined-pplb": "« $1 » est une propriété déclarative prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour spécifier un [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label libellé préféré de propriété].", "smw-property-predefined-chgpro": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour contenir des informations de [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation propagation des modifications].", "smw-property-predefined-schema-link": " et est fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-format-schema": " et est fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-profile-schema": " et est fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-trans": " et est fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-trans-source": " et est fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-trans-group": " et est fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-cont-len": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour stocker des informations de longueur.", "smw-property-predefined-long-cont-len": "Elle est utilisée en lien avec [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (et le [https://www.semantic-mediawiki.org/Attachment_processor mécanisme de rattachement]) pour collecter et stocker des informations sur la taille d’un fichier ingéré (si elle est fournie).", "smw-property-predefined-cont-lang": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour stocker des informations de langue.", "smw-property-predefined-long-cont-lang": "Elle est utilisée en lien avec [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (et le [https://www.semantic-mediawiki.org/Attachment_processor traitement de rattachement]) pour collecter et stocker des informations sur la langue récupérée depuis un fichier ingéré (si elle est fournie).", "smw-property-predefined-cont-title": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour stocker des informations de titre.", "smw-property-predefined-long-cont-title": "Elle est utilisée en lien avec [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (et le [https://www.semantic-mediawiki.org/Attachment_processor traitement de rattachement]) pour collecter et stocker des informations sur le titre récupéré d’un fichier ingéré (si elle est fournie).", "smw-property-predefined-cont-author": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour stocker des informations sur l’auteur.", "smw-property-predefined-long-cont-author": "Elle est utilisée en lien avec [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (et le [https://www.semantic-mediawiki.org/Attachment_processor traitement de rattachement]) pour collecter et stocker des informations sur l’auteur d’un fichier ingéré (s’il est fourni).", "smw-property-predefined-cont-date": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour stocker des informations de date.", "smw-property-predefined-long-cont-date": "Elle est utilisée avec [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (et le [https://www.semantic-mediawiki.org/Attachment_processor traitement de rattachement]) pour collecter et stocker les informations sur la date récupérée d’un fichier ingéré (si elle est fournie).", "smw-property-predefined-cont-type": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour stocker des informations sur le type de fichier.", "smw-property-predefined-long-cont-type": "Elle est utilisée en lien avec [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (et le [https://www.semantic-mediawiki.org/Attachment_processor traitement de rattachement]) pour collecter et stocker les informations sur le type récupéré d’un fichier ingéré (s’il est fourni).", "smw-property-predefined-cont-keyw": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour représenter des mots-clés.", "smw-property-predefined-long-cont-keyw": "Elle est utilisée en lien avec [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (et le [https://www.semantic-mediawiki.org/Attachment_processor traitement de rattachement]) pour collecter et stocker les mots-clés récupérés d’un fichier ingéré (s’ils sont fournis).", "smw-property-predefined-file-attch": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour représenter un conteneur stockant des informations sur les pièces jointes.", "smw-property-predefined-long-file-attch": "Elle est utilisée en relation avec [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (et le [https://www.semantic-mediawiki.org/Attachment_processor traitement de rattachement]) pour collecter toutes les informations récupérables spécifiques au contenu depuis un fichier ingéré (si elles sont fournies).", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps L’extension « Maps »] n’a pas été détectée, donc « $1 » est restreint dans sa capacité de fonctionnement.", "smw-datavalue-monolingual-dataitem-missing": "Un élément attendu est manquant pour construire une valeur composée monolingue.", "smw-datavalue-languagecode-missing": "Pour l’annotation « $1 », l’analyseur n’a pas pu déterminer un code de langue (par ex. « foo@en »).", "smw-datavalue-languagecode-invalid": "« $1 » n’a pas été reconnu comme un code de langue pris en charge.", "smw-property-predefined-lcode": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui représente un code de langue au format BCP47.", "smw-type-mlt-rec": "« $1 » est un type de données [https://www.semantic-mediawiki.org/wiki/Help:Container conteneur] qui associe une valeur texte avec un [[Property:Language code|code de langue]] spécifique.", "smw-types-extra-mlt-lcode": "Le type de données {{PLURAL:$2|nécessite un|ne nécessite aucun}} code de langue (par ex. une annotation de valeur sans code de langue {{PLURAL:$2|n’est pas|est}} acceptée).", "smw-property-predefined-text": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui représente un texte de longueur quelconque.", "smw-property-predefined-pdesc": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui permet de décrire une propriété dans le contexte d’une langue.", "smw-property-predefined-list": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour définir une liste de propriétés utilisée avec une propriété de type [[Special:Types/Record|enregistrement]].", "smw-limitreport-intext-parsertime": "[SMW] Temps de l’analyseur annoté au sein du texte", "smw-limitreport-intext-postproctime": "[SMW] durée de post-traitement", "smw-limitreport-intext-parsertime-value": "$1 seconde{{PLURAL:$1||s}}", "smw-limitreport-intext-postproctime-value": "$1 seconde{{PLURAL:$1||s}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Temps de mise à jour de l’enregistrement (sur une purge de page)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 seconde{{PLURAL:$1||s}}", "smw_allows_pattern": "Cette page est censée contenir une liste de références (suivie [https://fr.wikipedia.org/wiki/Expression_rationnelle d’expressions rationnelles]) et être mise à disposition via la propriété [[Property:Allows pattern|Permettre les motifs]]. Pour modifier cette page, le droit <code>smw-patternedit</code> est nécessaire.", "smw-datavalue-allows-pattern-mismatch": "« $1 » a été classé comme non valide d’après l’expression rationnelle « $2 ».", "smw-datavalue-allows-pattern-reference-unknown": "La référence de motif « $1 » ne peut être mise en correspondance avec aucune entrée dans [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "La référence de liste « $1 » ne correspondait à aucune page [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "Il manque des éléments avec un marqueur de liste « * » dans le contenu de la liste « $1 ».", "smw-datavalue-feature-not-supported": "La fonctionnalité « $1 » n’est pas prise en charge ou a été désactivée sur ce wiki.", "smw-property-predefined-pvap": "« $1 » est une propriété prédéfinie qui peut spécifier une [[MediaWiki:Smw allows pattern|référence de motif]] pour appliquer une correspondance d’[https://fr.wikipedia.org/wiki/Expression_rationnelle expression rationnelle]. Ceci est fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique]", "smw-property-predefined-dtitle": "« $1 » est une propriété prédéfinie qui peut affecter un titre affiché distinct à une entité. Ceci est fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-pvuc": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour retreindre les affectations de valeur afin que chaque instance soit unique (ou une au plus).", "smw-property-predefined-long-pvuc": "L’unicité est établie quand deux valeurs n’ont pas la même représentation littérale et toute violation de cette contrainte sera catégorisée comme une erreur.", "smw-datavalue-constraint-uniqueness-violation": "La propriété « $1 » ne permet que des affectations de valeurs uniques et « $2 » a déjà été annoté dans le sujet « $3 ».", "smw-datavalue-constraint-uniqueness-violation-isknown": "La propriété « $1 » ne permet que des annotations à valeur unique, mais « $2 » contient déjà une valeur affectée. « $3 » viole la contrainte d’unicité.", "smw-datavalue-constraint-violation-non-negative-integer": "La propriété « $1 » a une contrainte « entier non négatif » et la valeur « $2 » ne respecte pas cette exigence.", "smw-datavalue-constraint-violation-must-exists": "La propriété « $1 » a une contrainte <code>must_exists</code> et la valeur « $2 » ne respecte pas cette exigence.", "smw-datavalue-constraint-violation-single-value": "La propriété « [[Property:$1|$1]] » a une contrainte <code>single_value</code> et la valeur « $2 » ne respecte pas cette exigence.", "smw-constraint-violation-uniqueness": "Une contrainte <code>unique_value_constraint</code> est affectée à la propriété « [[Property:$1|$1]] » qui ne permet que des affectations de valeur unique, mais la valeur d’annotation « $2 » a déjà été trouvée comme annotant le sujet « $3 ».", "smw-constraint-violation-uniqueness-isknown": "Une contrainte <code>unique_value_constraint</code> est affectée à la propriété « [[Property:$1|$1]] » qui ne permet que des valeurs d’annotation uniques, mais « $2 » contient déjà une valeur annotée avec « $3 », ce qui viole la contrainte d’unicité pour le sujet actuel.", "smw-constraint-violation-non-negative-integer": "Une contrainte <code>non_negative_integer</code> est affectée à la propriété « [[Property:$1|$1]] », mais la valeur d’annotation « $2 » viole l’exigence de la contrainte.", "smw-constraint-violation-must-exists": "Une contrainte <code>must_exists</code> est affectée à la propriété « [[Property:$1|$1]] », mais la valeur d’annotation « $2 » viole l’exigence de la contrainte.", "smw-constraint-violation-single-value": "Une contrainte <code>single_value</code> est affectée à la propriété « [[Property:$1|$1]] », mais la valeur d’annotation « $2 » viole l’exigence de la contrainte.", "smw-constraint-violation-class-shape-constraint-missing-property": "Une contrainte <code>shape_constraint</code> est affectée à la catégorie « [[:$1]] » avec une clé <code>property</code>, mais la propriété « $2 » exigée est manquante.", "smw-constraint-violation-class-shape-constraint-wrong-type": "Une contrainte <code>shape_constraint</code> est affectée à la catégorie « [[:$1]] » avec une clé <code>property_type</code>, mais la propriété « $2 » ne correspond pas au type de « $3 ».", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "Une contrainte <code>shape_constraint</code> est affectée à la catégorie « [[:$1]] » avec une clé <code>max_cardinality</code>, mais la propriété « $2 » ne correspond pas à la cardinalité de « $3 ».", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "Une contrainte <code>shape_constraint</code> est affectée à la catégorie « [[:$1]] » avec une clé <code>min_textlength</code>, mais la propriété « $2 » ne correspond pas à l’exigence de taille de « $3 ».", "smw-constraint-violation-class-mandatory-properties-constraint": "Une contrainte <code>mandatory_properties</code> est affectée à la catégorie « [[:$1]] » et nécessite les propriétés obligatoires suivantes : $2", "smw-constraint-violation-allowed-namespace-no-match": "Une contrainte <code>allowed_namespaces</code> est affectée à la propriété « [[Property:$1|$1]] » mais « $2 » viole l’exigence d’espace de noms, seuls les espaces de nom suivants sont autorisés : « $3 ».", "smw-constraint-violation-allowed-namespaces-requires-page-type": "La contrainte <code>allowed_namespaces</code> nécessite un type de page.", "smw-constraint-schema-category-invalid-type": "Le schéma annoté « $1 » n’est pas valide pour une catégorie, il nécessite un type « $2 ».", "smw-constraint-schema-property-invalid-type": "Le schéma annoté « $1 » n’est pas valide pour une propriété, il nécessite un type « $2 ».", "smw-constraint-error-allows-value-list": "« $1 » n’est pas dans la liste ($2) des [[Property:Allows value|valeurs autorisées]] pour la propriété « $3 ».", "smw-constraint-error-allows-value-range": "« $1 » n’est pas dans la plage de « $2 » spécifiée par la contrainte de [[Property:Allows value|valeur autorisée]] pour la propriété « $3 ».", "smw-property-predefined-boo": "« $1 » est un [[Special:Types/Boolean|type]] et une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour représenter des valeurs booléennes.", "smw-property-predefined-num": "« $1 » est un [[Special:Types/Number|type]] et une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour représenter des valeurs numériques.", "smw-property-predefined-dat": "« $1 » est un [[Special:Types/Date|type]] et une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour représenter des valeurs de date.", "smw-property-predefined-uri": "« $1 » est un [[Special:Types/URL|type]] et une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour représenter des valeurs d’URI/URL.", "smw-property-predefined-qty": "« $1 » est un [[Special:Types/Quantity|type]] et une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour représenter des valeurs de quantité.", "smw-datavalue-time-invalid-offset-zone-usage": "« $1 » contient un décalage et un identificateur de zone qui n’est pas pris en charge.", "smw-datavalue-time-invalid-values": "La valeur « $1 » contient des informations non interprétables sous la forme « $2 ».", "smw-datavalue-time-invalid-date-components-common": "« $1 » contient des informations non interprétables.", "smw-datavalue-time-invalid-date-components-dash": "« $1 » contient un tiret superflu ou d’autres caractères qui ne sont pas valides pour interpréter une date.", "smw-datavalue-time-invalid-date-components-empty": "« $1 » contient des composants vides.", "smw-datavalue-time-invalid-date-components-three": "« $1 » contient plus de trois composants exigés pour l’interprétation d’une date.", "smw-datavalue-time-invalid-date-components-sequence": "« $1 » contient une séquence qui ne peut pas être interprétée avec une matrice de correspondance disponible pour les composants de date.", "smw-datavalue-time-invalid-ampm": "« $1 » contient « $2 » en tant qu’élément de type heure et qui n’est pas valide pour une convention sur 12 heures.", "smw-datavalue-time-invalid-jd": "Impossible d’interpréter la valeur d’entrée « $1 » comme un nombre JD (jour julien) valide avec « $2 » signalé.", "smw-datavalue-time-invalid-prehistoric": "Impossible d’interpréter une valeur d’entrée « $1 » préhistorique. Par exemple, avoir spécifié un trop grand nombre d’années dans un modèle calendaire peut renvoyer des résultats inattendus dans un contexte préhistorique.", "smw-datavalue-time-invalid": "Impossible d’interpréter la valeur d’entrée « $1 » comme un composant d’horodatage valide avec « $2 » signalé.", "smw-datavalue-external-formatter-uri-missing-placeholder": "Il manque la variable substituée « $1 » dans l’URI du formateur.", "smw-datavalue-external-formatter-invalid-uri": "« $1 » est une URL non valide.", "smw-datavalue-external-identifier-formatter-missing": "Il manque à la propriété l’affectation d’une [[Property:External formatter uri|« URI de formateur externe »]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "L’identifiant externe « $1 » attend une substitution de plusieurs champs mais il manque à la valeur « $2 » actuelle au moins une valeur de paramètre pour répondre à l’exigence.", "smw-datavalue-keyword-maximum-length": "Le mot-clé dépasse la valeur maximale de $1 caractère{{PLURAL:$1||s}}.", "smw-property-predefined-eid": "« $1 » est un [[Special:Types/External identifier|type]] et une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour représenter des identifiants externes.", "smw-property-predefined-peid": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui représente un identifiant externe.", "smw-property-predefined-pefu": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] pour spécifier une ressource externe avec une variable substituée.", "smw-property-predefined-long-pefu": "L’URI doit comprendre une variable substituée qui sera ajustée avec la valeur d’un [[Special:Types/External identifier|identifiant externe]] pour former une référence de ressource valide.", "smw-type-eid": "« $1 » est une variante du type de données [[Special:Types/Text|Texte]] pour décrire des ressources externes (basées sur une URI) et qui exige des propriétés particulières pour déclarer une [[Property:External formatter uri|URI de formateur externe]].", "smw-property-predefined-keyw": "« $1 » est une propriété prédéfinie et un [[Special:Types/Keyword|type]] fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] qui normalise un texte et a une longueur de chaîne restreinte.", "smw-type-keyw": "« $1 » est une variante du type de données [[Special:Types/Text|Texte]] qui a une longueur restreinte avec une représentation normalisée de son contenu.", "smw-datavalue-stripmarker-parse-error": "La valeur fournie « $1 » contient des [https://en.wikipedia.org/wiki/Help:Strip_markers marqueurs de contenu masqué par MediaWiki] et ne peut donc pas être suffisamment analysée.", "smw-datavalue-parse-error": "La valeur indiquée « $1 » n’a pas été comprise.", "smw-datavalue-propertylist-invalid-property-key": "La liste de propriétés « $1 » contient une clé de propriété « $2 » non valide.", "smw-datavalue-type-invalid-typeuri": "Le type « $1 » ne peut pas être transformé en une représentation d’URI valide.", "smw-datavalue-wikipage-missing-fragment-context": "La valeur d’entrée de page wiki « $1 » ne peut pas être utilisée sans une page de contexte.", "smw-datavalue-wikipage-invalid-title": "La valeur d’entrée du type de page « $1 » contient des caractères non valides ou est incomplète et peut donc provoquer des résultats inattendus lors d’une requête ou d’un processus d’annotation.", "smw-datavalue-wikipage-property-invalid-title": "La propriété « $1 » (en tant que type de page) avec la valeur d’entrée « $2 » contient des caractères non valides ou est incomplète et peut donc provoquer des résultats inattendus lors d’une requête ou d’un processus d’annotation.", "smw-datavalue-wikipage-empty": "La valeur d’entrée de page wiki est vide (par ex. <code>[[SomeProperty::]], [[]]</code>) et ne peut donc pas être utilisée en tant que nom ni comme partie d’une condition de requête.", "smw-type-ref-rec": "« $1 » est un type de [https://www.semantic-mediawiki.org/wiki/Container conteneur] qui permet d’enregistrer des informations supplémentaires (par exemple, la provenance des données) sur une affectation de valeur.", "smw-datavalue-reference-outputformat": "$1 : $2", "smw-datavalue-reference-invalid-fields-definition": "Le type [[Special:Types/Reference|Référence]] attend qu’une liste de propriétés soit déclarée au moyen de l’attribut [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Possède des champs].", "smw-parser-invalid-json-format": "L’analyseur JSON a renvoyé un « $1 ».", "smw-property-preferred-label-language-combination-exists": "« $1 » ne peut pas être utilisé comme libellé préféré parce que la langue « $2 » est déjà affectée au libellé « $3 ».", "smw-clipboard-copy-link": "Copier le lien dans le presse-papiers", "smw-property-userdefined-fixedtable": "« $1 » a été configuré comme [https://www.semantic-mediawiki.org/wiki/Fixed_properties propriété fixe] et toute modification de sa [https://www.semantic-mediawiki.org/wiki/Type_declaration déclaration de type] a besoin soit d’exécuter <code>setupStore.php</code>, soit de terminer la tâche spéciale [[Special:SemanticMediaWiki|« Installation et mise à niveau de la base de données »]].", "smw-data-lookup": "Récupération des données...", "smw-data-lookup-with-wait": "La demande est en cours d’exécution et peut prendre un certain temps.", "smw-no-data-available": "Au<PERSON>ne donnée disponible.", "smw-property-req-violation-missing-fields": "Pour la propriété « $1 », il manque une déclaration des champs [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>], exigée pour ce type « $2 ».", "smw-property-req-violation-multiple-fields": "La propriété « $1 » contient de multiples déclarations des champs [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] (concurrentes dans ce cas), mais une seule est attendue pour ce type « $2 ».", "smw-property-req-violation-missing-formatter-uri": "Pour la propriété « $1 », il manque les détails de déclaration relatifs au type annoté, ce qui ne permet pas de définir la propriété <code>URI du formateur externe</code>.", "smw-property-req-violation-predefined-type": "La propriété « $1 », en tant que propriété prédéfinie, contient une déclaration de type « $2 » qui n’est pas compatible avec le type par défaut de cette propriété.", "smw-property-req-violation-import-type": "Une déclaration de type a été détectée qui est incompatible avec le type prédéfini du vocabulaire importé « $1 ». En général, il n’est pas nécessaire de déclarer un type parce que les informations sont récupérées lors de la définition de l’import.", "smw-property-req-violation-change-propagation-locked-error": "La propriété « $1 » a été modifiée et les entités affectées doivent être réévaluées à l’aide d’un processus de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation des changements]. La page de propriétés a été verrouillée jusqu’à ce que la mise à jour de la spécification principale soit terminée afin d’éviter des interruptions intermédiaires ou des spécifications contradictoires. Le processus peut prendre un moment avant que la page puisse être déverrouillée car cela dépend de la taille et de la fréquence de l’ordonnanceur de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue file d’attente des tâches].", "smw-property-req-violation-change-propagation-locked-warning": "La propriété « $1 » a été modifiée et les entités affectées doivent être réévaluées à l’aide d’un processus de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation des changements]. La mise à jour peut prendre un moment car cela dépend de la taille et de la fréquence de l’ordonnanceur de la  [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue file d’attente des tâches] et il est suggéré de reporter les modifications apportées à la propriété afin d’éviter des interruptions intermédiaires ou des spécifications contradictoires.", "smw-property-req-violation-change-propagation-pending": "Des mises à jour pour la [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation des changements] sont en attente ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue tâche{{PLURAL:$1||s}} estimée{{PLURAL:$1||s}}]) et il est recommandé d’attendre que le processus soit terminé avant de modifier une propriété, afin d’éviter des interruptions intermédiaires ou des spécifications contradictoires.", "smw-property-req-violation-missing-maps-extension": "MediaWiki Sémantique n’a pas pu détecter l’extension prérequise [https://www.semantic-mediawiki.org/wiki/Extension:Maps « Maps »], ce qui, en conséquence, limite la fonctionnalité de cette propriété (c.-à-d. empêche de stocker ou de traiter les données géographiques).", "smw-property-req-violation-type": "La propriété contient des spécifications de type concurrentes qui peuvent aboutir à des annotations de valeur non valides ; par conséquent il est attendu qu’un utilisateur lui assigne un type approprié.", "smw-property-req-error-list": "La propriété contient les erreurs ou avertissements suivants :", "smw-property-req-violation-parent-type": "La propriété « $1 » et la propriété parente affectée « $2 » ont des annotations de type différentes.", "smw-property-req-violation-forced-removal-annotated-type": "L’application obligatoire de [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance l’héritage du type du parent] a été activée, mais le type d’annotation pour la propriété « $1 » ne correspond pas au type « $2 » indiqué par son parent et a été modifié pour respecter cette exigence. Il est recommandé d’ajuster la définition de type dans la page pour que le message d’erreur et l’application obligatoire soient supprimés de cette propriété.", "smw-change-propagation-protection": "Cette page est verrouillée afin d’éviter des modifications accidentelles des données pendant l’exécution d’une mise à jour de [https://www.semantic-mediawiki.org/wiki/change_propagation propagation des changements]. Le processus peut prendre un moment avant que la page ne soit déverrouillée car cela dépend de la taille et de la fréquence du séquenceur de la [https://www.mediawiki.org/wiki/Special:MyLanguage/manual:Job_queue file d’attente des tâches].", "smw-category-change-propagation-locked-error": "La catégorie « $1 » a été modifiée et nécessite que les entités affectées soient réévaluées en suivant un processus [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation des changements]. En attendant, la page de catégorie a été verrouillée jusqu’à ce que la mise à jour de la spécification principale soit terminée, afin d’éviter des interruptions intermédiaires ou des spécifications contradictoires. Le processus peut prendre un moment avant que la page puisse être déverrouillée car cela dépend de la taille et de la fréquence du séquenceur de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue file d’attente des tâches].", "smw-category-change-propagation-locked-warning": "La catégorie « $1 » a été modifiée et nécessite que les entités affectées soient réévaluées en suivant un processus de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation des changements]. La mise à jour peut prendre un moment car cela dépend de la taille et de la fréquence du séquenceur de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue file d’attente des tâches], il est conseillé de différer les modifications de la catégorie afin d’éviter des interruptions intermédiaires ou des spécifications contradictoires.", "smw-category-change-propagation-pending": "Des mises à jour de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation des changements] sont en attente ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue tâche{{PLURAL:$1||s}} estimée{{PLURAL:$1||s}}]),  il est recommandé d’attendre que le traitement soit achevé pour des modifications d’une catégorie afin d’éviter des interruptions intermédiaires ou des spécifications contradictoires.", "smw-category-invalid-value-assignment": "« $1 » n’est pas reconnu comme une catégorie ou une annotation de valeur valide.", "protect-level-smw-pageedit": "Autoriser uniquement les utilisateurs qui ont le droit de modifier des pages (MediaWiki Sémantique)", "smw-create-protection": "La création de la propriété « $1 » est limitée aux seuls utilisateurs ayant le droit « $2 » (ou membres d’un [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups groupe d’utilisateurs] approprié) lorsque le [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode mode d’autorité] est activé.", "smw-create-protection-exists": "Les modifications de la propriété « $1 » sont limitées aux seuls utilisateurs ayant le droit « $2 » (ou membres d’un [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups groupe d’utilisateurs] approprié) lorsque le [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode mode d’autorité] est activé.", "smw-edit-protection": "Cette page est [[Property:Is edit protected|protégée]] pour éviter la modification accidentelle de données et ne peut être modifiée que par des utilisateurs ayant le droit de modification « $1 » (ou membres d’un [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups groupe d’utilisateurs] approprié).", "smw-edit-protection-disabled": "La protection contre la modification a été désactivée et « $1 » ne peut donc pas être utilisé pour protéger les pages de l’entité contre des modifications non autorisées.", "smw-edit-protection-auto-update": "MediaWiki Sémantique a mis à jour l’état de la protection conformément à la propriété « Est protégé contre la modification ».", "smw-edit-protection-enabled": "Protégé contre la modification (MediaWiki Sémantique)", "smw-patternedit-protection": "Cette page est protégée et ne peut être modifiée que par des utilisateurs ayant le [https://www.semantic-mediawiki.org/wiki/Help:Permissions droit] <code>smw-patternedit</code> approprié.", "smw-property-predefined-edip": "« $1 » est une propriété prédéfinie qui indique si la modification est protégée ou non. Ceci est fourni par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique].", "smw-property-predefined-long-edip": "Bien que n’importe quel utilisateur soit habilité à ajouter cette propriété à un sujet, seul un utilisateur avec un droit spécifique peut modifier ou supprimer la protection d’une entité, une fois celle-ci ajoutée.", "smw-query-reference-link-label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u<PERSON>", "smw-format-datatable-emptytable": "Aucune donnée disponible dans la table", "smw-format-datatable-info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées", "smw-format-datatable-infoempty": "Affichage de 0 à 0 sur 0 entrée", "smw-format-datatable-infofiltered": "(filtré sur _MAX_ entrées au total)", "smw-format-datatable-infothousands": "&#x202F;", "smw-format-datatable-lengthmenu": "Afficher _MENU_ entrées", "smw-format-datatable-loadingrecords": "Chargement en cours...", "smw-format-datatable-processing": "Traitement en cours...", "smw-format-datatable-search": "Rechercher :", "smw-format-datatable-zerorecords": "Aucun enregistrement correspondant trouvé", "smw-format-datatable-first": "Premier", "smw-format-datatable-last": "<PERSON><PERSON>", "smw-format-datatable-next": "Suivant", "smw-format-datatable-previous": "Précédent", "smw-format-datatable-sortascending": ": activer pour trier la colonne dans l’ordre croissant", "smw-format-datatable-sortdescending": ": activer pour trier la colonne en ordre décroissant", "smw-format-datatable-toolbar-export": "Exporter", "smw-category-invalid-redirect-target": "La catégorie « $1 » contient une cible de redirection non valide vers un espace de noms hors des catégories.", "smw-parser-function-expensive-execution-limit": "La fonction d’analyseur a atteint le temps limite autorisé pour son exécution (voir le paramètre de configuration [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "MediaWiki Sémantique actualise la page actuelle en fonction de certains traitements nécessaires après l’exécution des requêtes.", "apihelp-smwinfo-summary": "Module d’API pour récupérer des informations concernant les statistiques de MediaWiki Sémantique ainsi que d’autres méta-informations.", "apihelp-ask-summary": "Module d’API pour interroger MediaWiki Sémantique en utilisant le langage de requête « ask ».", "apihelp-askargs-summary": "Module d’API pour interroger MediaWiki Sémantique en utilisant le langage de requête « ask » avec une liste de conditions, de résultats et de paramètres.", "apihelp-browsebyproperty-summary": "Module d’API pour récupérer des informations concernant une propriété ou une liste de propriétés.", "apihelp-browsebysubject-summary": "Module d’API pour récupérer des informations concernant un sujet.", "apihelp-smwtask-summary": "Module d’API pour exécuter les tâches relatives à MediaWiki Sémantique (pour un usage interne uniquement, pas pour un usage public).", "apihelp-smwbrowse-summary": "Module API pour prendre en charge l’analyse des activités pour différents types d’entités dans MediaWiki Sémantique.", "apihelp-ask-parameter-api-version": "Format de sortie :\n;2:Format rétro-compatible utilisant {} pour la liste des résultats.\n;3:Format expérimental utilisant [] pour la liste des résultats.", "apihelp-smwtask-param-task": "Définit le type de tâche", "apihelp-smwtask-param-params": "Paramètres encodés en JSON qui correspondent à l’exigence de type de tâche sélectionnée", "smw-apihelp-smwtask-example-update": "Exemple d’exécution d’une tâche de mise à jour pour un sujet particulier :", "smw-api-invalid-parameters": "Paramètres non valides dans « $1 »", "smw-parser-recursion-level-exceeded": "Le niveau de $1 récursions a été dépassé lors d’un processus d’analyse. Il est suggéré de valider la structure du modèle ou d’ajuster si nécessaire le paramètre de configuration <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "Affichage de $1 page{{PLURAL:$1||s}} utilisant cette propriété.", "smw-property-page-list-search-count": "Affichage de $1 page{{PLURAL:$1||s}} utilisant cette propriété avec une valeur correspondant à « $2 ».", "smw-property-page-filter-note": "Le [https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter filtre de recherche] permet l’inclusion d’[https://www.semantic-mediawiki.org/wiki/Help:Query_expressions expressions de requête] telles que <code>~</code> ou <code>!</code>. Le [https://www.semantic-mediawiki.org/wiki/Query_engine moteur de requête] sélectionné peut aussi prendre en charge une correspondance insensible à la casse ou d’autres expressions courtes telles que :\n* <code>in:</code> le résultat doit contenir le terme, par ex. « <code>in:Foo</code> »\n* <code>not:</code> le résultat ne doit pas contenir le terme, par ex. « <code>not:Bar</code> »", "smw-property-reserved-category": "<PERSON><PERSON><PERSON><PERSON>", "smw-category": "<PERSON><PERSON><PERSON><PERSON>", "smw-datavalue-uri-invalid-scheme": "« $1 » n’a pas été répertorié comme schéma d’URI valide.", "smw-datavalue-uri-invalid-authority-path-component": "« $1 » a été identifié comme contenant un composant d’autorité ou de chemin d’accès « $2 » non valide.", "smw-browse-property-group-title": "Groupe de propriétés", "smw-browse-property-group-label": "Libellé du groupe de propriétés", "smw-browse-property-group-description": "Description du groupe de propriétés", "smw-property-predefined-ppgr": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui identifie les entités (principalement les catégories) utilisées comme instance de regroupement pour les propriétés.", "smw-filter": "Filtre", "smw-section-expand": "Développer la section", "smw-section-collapse": "Replier la section", "smw-ask-format-help-link": "format [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "Aide", "smw-cheat-sheet": "Aide-mémoire", "smw-personal-jobqueue-watchlist": "Liste de suivi de la file des tâches", "smw-personal-jobqueue-watchlist-explain": "Le nombre indique une estimation des entrées de la file des tâches en attente d’exécution.", "smw-property-predefined-label-skey": "Clé de tri", "smw-processing": "Traitement en cours...", "smw-loading": "Chargement en cours...", "smw-fetching": "Récupération en cours...", "smw-preparing": "Préparation en cours...", "smw-expand": "Développer", "smw-collapse": "Replier", "smw-copy": "<PERSON><PERSON><PERSON>", "smw-copy-clipboard-title": "Copie le contenu dans le presse-papiers", "smw-jsonview-expand-title": "Développe la vue JSON", "smw-jsonview-collapse-title": "Replie la vue JSON", "smw-jsonview-search-label": "Rechercher :", "smw-redirect-target-unresolvable": "Cette cible ne peut pas être résolue pour la raison « $1 »", "smw-types-title": "Type : $1", "smw-schema-namespace-editcontentmodel-disallowed": "La modification du modèle de contenu d’une [https://www.semantic-mediawiki.org/wiki/Help:Schema page de schéma] n’est pas autorisée.", "smw-schema-namespace-edit-protection": "Cette page est protégée et ne peut être modifiée que par des utilisateurs ayant le [https://www.semantic-mediawiki.org/wiki/Help:Permissions droit] <code>smw-schemaedit</code> approprié.", "smw-schema-namespace-edit-protection-by-import-performer": "Cette page a été importée par un [https://www.semantic-mediawiki.org/wiki/Import_performer utilisateur importateur] référencé, ce qui signifie que la modification du contenu de cette page est réservée à ces utilisateurs référencés.", "smw-schema-error-title": "Erreur{{PLURAL:$1||s}} de validation", "smw-schema-error-schema": "Le schéma de validation '''$1''' a trouvé les incohérences suivantes :", "smw-schema-error-miscellaneous": "Erreur diverse ($1)", "smw-schema-error-validation-json-validator-inaccessible": "Le validateur JSON « <b>$1</b> » n’est pas accessible (ou installé) et le fichier « $2 » n’a donc pas pu être examiné, ce qui empêche l’enregistrement ou la modification de la page actuelle.", "smw-schema-error-validation-file-inaccessible": "Le fichier de validation « $1 » n’est pas accessible.", "smw-schema-error-violation": "[« $1 », « $2 »]", "smw-schema-error-type-missing": "Il manque un type au contenu pour qu’il soit reconnu et utilisable dans l’[https://www.semantic-mediawiki.org/wiki/Help:Schema espace de noms de schéma].", "smw-schema-error-type-unknown": "Le type « $1 » n’est pas inscrit et ne peut donc pas être utilisé pour du contenu dans l’espace de noms  [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-error-json": "Erreur JSON : « $1 »", "smw-schema-error-input": "La validation des entrées a trouvé les problèmes suivants qui doivent être corrigés avant que le contenu puisse être enregistré. La  [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling page d’aide du schéma] peut fournir des conseils sur la manière de supprimer les incohérences ou de résoudre des problèmes de format des entrées.", "smw-schema-error-input-schema": "Le schéma de validation '''$1''' a trouvé les incohérences suivantes, qui doivent être corrigées avant que le contenu puisse être enregistré. La [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling page d’aide du schéma] peut fournir des conseils sur la manière de résoudre ces problèmes.", "smw-schema-error-title-prefix": "Ce type de schéma exige que le titre du schéma commence par le préfixe « $1 ».", "smw-schema-validation-error": "Le type « $1 » n’est pas enregistré et ne peut pas être utilisé pour le contenu dans l’espace de noms [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-validation-schema-title": "Schéma JSON", "smw-schema-summary-title": "Résumé", "smw-schema-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-schema-usage": "Utilisation", "smw-schema-type": "Type de schéma", "smw-schema-type-description": "Description de type", "smw-schema-description": "Description du schéma", "smw-schema-description-link-format-schema": "Ce type de schéma prend en charge la définition des caractéristiques pour créer des liens dépendants du contexte selon une propriété affectée du [[Property:Formatter schema|schéma de mise en forme]].", "smw-schema-description-search-form-schema": "Ce type de schéma prend en charge la définition des formulaires de saisie et des caractéristiques pour le profil de [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch recherche étendue], où il contient des instructions sur la manière de générer les champs de saisie, définir des espaces de noms par défaut ou déclarer des expressions de préfixe pour une requête de recherche.", "smw-schema-description-property-profile-schema": "Ce type de schéma prend en charge la définition d’un profil pour déclarer les caractéristiques de la propriété affectée et ses valeurs d’annotation.", "smw-schema-description-facetedsearch-profile-schema": "Ce type de schéma prend en charge la définition de profils utilisés dans le cadre de l'environnement [[Special:FacetedSearch|Faceted search]] .", "smw-schema-description-property-group-schema": "Ce type de schéma prend en charge la définition des [https://www.semantic-mediawiki.org/wiki/Help:Property_group groupes de propriété] pour aider à structurer l’interface de [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse navigation].", "smw-schema-description-property-constraint-schema": "Ceci prend en charge la définition de règles de contrainte pour une instance de propriété ainsi que des valeurs qui lui sont assignées.", "smw-schema-description-class-constraint-schema": "Ce type de schéma prend en charge la définition des règles de contrainte pour une instance de classe (autrement dit une catégorie).", "smw-schema-tag": "Balise{{PLURAL:$1||s}}", "smw-property-predefined-constraint-schema": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] et qui définit un schéma de contraintes.", "smw-property-predefined-schema-desc": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui stocke une description de schéma.", "smw-property-predefined-schema-def": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui stocke le contenu du schéma.", "smw-property-predefined-schema-tag": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pour identifier une collection de schémas.", "smw-property-predefined-long-schema-tag": "Un libellé qui identifie le schéma de contenus ou de caractéristiques similaires.", "smw-property-predefined-schema-type": "« $1 » est une propriété prédéfinie fournie par [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Sémantique] et qui décrit un type pour distinguer un groupe de schémas.", "smw-property-predefined-long-schema-type": "<PERSON>que [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type type] fournit sa propre interprétation des éléments de syntaxe et des règles applicables et peut être exprimé à l’aide d’un [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation schéma de validation].", "smw-ask-title-keyword-type": "Recherche par mot-clé", "smw-ask-message-keyword-type": "Ceci recherche les correspondances vérifiant la condition <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Impossible de se connecter à la cible distante « $1 ».", "smw-remote-source-disabled": "La source '''$1''' a désactivé la prise en charge des requêtes distantes !", "smw-remote-source-unmatched-id": "La source '''$1''' ne correspond pas à une version de MediaWiki Sémantique capable de prendre en charge une requête distante.", "smw-remote-request-note": "Le résultat est récupéré depuis la source distante '''$1''' et il est probable que le contenu généré contienne des informations qui ne sont pas disponibles depuis l’intérieur du wiki actuel.", "smw-remote-request-note-cached": "Le résultat est '''mis en cache''' depuis la source distante '''$1''' et il est probable que le contenu généré contienne des informations qui ne sont pas disponibles depuis l’intérieur du wiki actuel.", "smw-parameter-missing": "Le paramètre « $1 » est manquant.", "smw-property-tab-usage": "Utilisation", "smw-property-tab-profile-schema": "Schéma du profil", "smw-property-tab-redirects": "Synonymes", "smw-property-tab-subproperties": "Sous-propriétés", "smw-property-tab-errors": "Affectations impropres", "smw-property-tab-constraint-schema": "Schéma de contrainte", "smw-property-tab-constraint-schema-title": "Schéma compilé de contraintes", "smw-property-tab-specification": "... plus", "smw-concept-tab-list": "Liste", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-ask-tab-result": "Résultat", "smw-ask-tab-extra": "Suppléments", "smw-ask-tab-debug": "Débogage", "smw-ask-tab-code": "Code", "smw-install-incomplete-tasks-title": "Tâches d’administration incomplètes", "smw-install-incomplete-intro": "Il y a $2 tâche{{PLURAL:$2||s}} incomplète{{PLURAL:$2||s}} ou [[Special:PendingTaskList|en cours]] pour achever l{{PLURAL:$1|’installation|a mise à jour}} de [https://www.semantic-mediawiki.org MediaWiki Sémantique]. Un administrateur ou utilisateur avec des droits suffisants peut l{{PLURAL:$2|’a|les a}}chever. Cela doit être fait avant d’ajouter des nouvelles données pour éviter les incohérences.", "smw-install-incomplete-intro-note": "Ce message disparaîtra lorsque toutes les tâches auront été résolues.", "smw-pendingtasks-intro-empty": "Aucune tâche n’a été classée comme en attente, incomplète ou en suspens en relation avec MediaWiki Sémantique.", "smw-pendingtasks-intro": "Cette page fournit des informations sur les tâches qui ont été classées comme en attente, incomplètes ou exceptionnelle en relation avec Semantic MediaWiki.", "smw-pendingtasks-setup-no-tasks-intro": "L’installation (ou la mise à jour) a été complétée, il n’y a actuellement aucune tâche en attente ou exceptionnelle.", "smw-pendingtasks-tab-setup": "Installation", "smw-updateentitycollation-incomplete": "Le paramètre <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> a été récemment modifié et nécessite que le script <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> soit exécuté pour que les entités soient mises à jour et qu’elles contiennent les clés de tri correctes.", "smw-updateentitycountmap-incomplete": "Le champ <code>smw_countmap</code> a été ajouté dans une version récente et nécessite que le script <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> soit exécuté afin que les fonctions puissent accéder au contenu de ce champ.", "smw-populatehashfield-incomplete": "Le remplissage du champ <code>smw_hash</code> a été sauté lors de l’installation. Le script <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> doit être exécuté.", "smw-install-incomplete-populate-hash-field": "Le remplissage du champ <code>smw_hash</code> a été sauté lors de l’installation. Le script <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> doit être exécuté.", "smw-install-incomplete-elasticstore-indexrebuild": "L’<code>ElasticStore</code> a été sélectionné en tant que [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore magasin par défaut], mais l’extension n’a pu trouver aucun enregistrement indiquant que le script <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> a bien été exécuté. Veuillez exécuter le script comme indiqué.", "smw-elastic-rebuildelasticindex-run-incomplete": "L’<code>ElasticStore</code> a été sélectionné en tant que [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore magasin par défaut], mais l’extension n’a pu trouver aucun enregistrement indiquant que le script <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> a bien été exécuté. Veuillez exécuter le script comme indiqué.", "smw-pendingtasks-setup-intro": "L{{PLURAL:$1|’installation|a mise à jour}} de <b>MediaWiki Sémantique</b> a classé les tâches suivantes comme [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade incomplètes] et un administrateur (ou un utilisateur ayant les droits suffisants) doit résoudre ces tâches avant que les utilisateurs ne continuent à créer ou modifier du contenu.", "smw-pendingtasks-setup-tasks": "Tâches", "smw-filter-count": "Nombre de filtres", "smw-es-replication-check": "Vérification de la réplication (Elasticsearch)", "smw-es-replication-error": "Problème de réplication d’Elasticsearch", "smw-es-replication-file-ingest-error": "Problème d’ingestion de fichier", "smw-es-replication-maintenance-mode": "Maintenance d’Elasticsearch", "smw-es-replication-error-missing-id": "Le suivi de la réplication a trouvé que l’article « $1 » (identifiant : $2) manque sur le serveur Elasticsearch.", "smw-es-replication-error-divergent-date": "Le suivi de la réplication a trouvé que pour l’article « $1 » (identifiant : $2), la <b>date de modification</b> montre une divergence.", "smw-es-replication-error-divergent-date-short": "Les données de date suivantes ont été utilisées comme comparaison :", "smw-es-replication-error-divergent-date-detail": "Date de modification référencée :\n* Elasticsearch : $1 \n* Base de données : $2", "smw-es-replication-error-divergent-revision": "Le suivi de la réplication a trouvé que la <b>révision associée</b> pour l’article « $1 » (identifiant : $2) montre une divergence.", "smw-es-replication-error-divergent-revision-short": "Les données de révision associée suivantes ont été utilisées à des fins de comparaison :", "smw-es-replication-error-divergent-revision-detail": "Révisions associées référencées :  \n* Elasticsearch : $1\n* SQLStore : $2", "smw-es-replication-error-maintenance-mode": "La réplication d’Elasticsearch est actuellement restreinte car elle fonctionne en [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>mode de maintenance</b>], les modifications récemment apportées aux entités et aux pages ne sont <b>pas</b> immédiatement visibles et les résultats de recherche peuvent contenir des informations obsolètes.", "smw-es-replication-error-no-connection": "La surveillance de la réplication n’a pas pu effectuer de vérification car elle ne peut établir de connexion à la grappe Elasticsearch.", "smw-es-replication-error-bad-request-exception": "Le gestionnaire de connexion d’Elasticsearch a levé une exception de mauvaise requête (« erreur HTTP de conflit 400 ») indiquant un problème persistant lors des requêtes de réplication et de recherche.", "smw-es-replication-error-other-exception": "Le gestionnaire de connexion d’Elasticsearch a levé une exception : « $1 ».", "smw-es-replication-error-suggestions": "Il est suggéré de modifier ou de purger la page pour supprimer la divergence. Si le problème persiste, vérifiez la grappe Elasticsearch elle-même (allocateur, exceptions, espace disque, etc.).", "smw-es-replication-error-suggestions-maintenance-mode": "Il est suggéré de contacter l’administrateur du wiki pour vérifier si une [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild reconstruction d’index] est actuellement en cours ou si le <code>refresh_interval</code> (intervalle de rafraîchissement) n’a pas été réglé à la valeur par défaut attendue.", "smw-es-replication-error-suggestions-no-connection": "Il est suggéré de contacter l’administrateur du wiki et de signaler le problème « pas de connexion ».", "smw-es-replication-error-suggestions-exception": "Veuillez vérifier les journaux pour obtenir des informations sur l’état d’Elasticsearch et ses index, ainsi que les éventuels problèmes de mauvaise configuration.", "smw-es-replication-error-file-ingest-missing-file-attachment": "La surveillance de la réplication a trouvé qu’il manque une annotation de [[Property:File attachment|Fichier joint]] pour « $1 », ce qui indique que le moteur d’ingestion de fichiers n’a pas démarré ou n’est pas encore terminé.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Veuillez vous assurer que la tâche d’[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion ingestion de fichier] est programmée et exécutée avant que l’annotation et l’index du fichier ne soient rendus disponibles.", "smw-report": "Rapport", "smw-legend": "Légende", "smw-datavalue-constraint-schema-category-invalid-type": "Le schéma annoté « $1 » n’est pas valide pour une catégorie, il nécessite un type « $2 ».", "smw-datavalue-constraint-schema-property-invalid-type": "Le schéma annoté « $1 » n’est pas valide pour une propriété, il nécessite un type « $2 ».", "smw-entity-examiner-check": "Exécution {{PLURAL:$1|d’un examinateur|d’examinateurs}} en tâche de fond", "smw-entity-examiner-indicator": "Panneau des problèmes d’entité", "smw-entity-examiner-deferred-check-awaiting-response": "L’examinateur « $1 » attend actuellement une réponse du serveur.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "Contrainte", "smw-entity-examiner-associated-revision-mismatch": "Révision", "smw-entity-examiner-deferred-fake": "Simulation", "smw-entity-examiner-indicator-suggestions": "Lors de l’examen de l’entité, le{{PLURAL:$1||s}} problème{{PLURAL:$1||s}} suivant{{PLURAL:$1||s}} {{PLURAL:$1|a|ont}} été trouvé{{PLURAL:$1||s}}, il est conseillé de le{{PLURAL:$1||s}} examiner soigneusement et de prendre les actions appropriées.", "smw-indicator-constraint-violation": "Contrainte{{PLURAL:$1||s}}", "smw-indicator-revision-mismatch": "Révision", "smw-indicator-revision-mismatch-error": "La vérification de la [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner révision associée] a mis en évidence une incohérence entre la révision référencée dans MediaWiki et celle associée dans MediaWiki Sémantique pour cette entité.", "smw-indicator-revision-mismatch-comment": "Une incohérence indique en général qu’un certain processus a interrompu l’opération de stockage dans MediaWiki Sémantique. Il est recommandé de vérifier les journaux du serveur et de rechercher les exceptions ou autres échecs.", "smw-facetedsearch-intro-text": "La recherche facétique [https://www.semantic-mediawiki.org/wiki/Faceted_search Faceted Search] de Semantic MediaWiki fournit aux utilisateurs une interface simple pour réduire rapidement les résultats de requête d'une condition à l'aide de vues facéties créées à partir de propriétés et de catégories dépendantes.", "smw-facetedsearch-intro-tips": "*Utiliser <code>category:?</code> , <code>property:?</code> , ou <code>concept:?</code> pour trouver les catégories, propriétés ou concepts disponibles pour créer un ensemble de conditions \n*Utilisez la syntaxe #ask pour décrire une condition (par exemple<nowiki> <code>[[Category:Foo]]</code></nowiki> )\n* Utilisez « OU », « ET » ou d'autres expressions de requête pour créer des conditions complexes \n*Des expressions telles que <code>in:</code> ou <code>phrase:</code> peuvent être utilisées pour des correspondances en texte intégral ou des recherches non structurées, si l'option [https ://www.semantic-mediawiki.org/wiki/Query_engine Query Engine] prend en charge ces expressions", "smw-facetedsearch-profile-label-default": "Profil par défaut", "smw-facetedsearch-intro-tab-explore": "Explorer", "smw-facetedsearch-intro-tab-search": "<PERSON><PERSON><PERSON>", "smw-facetedsearch-explore-intro": "Sélectionner une collection et commencez à naviguer.", "smw-facetedsearch-profile-options": "Options de profil", "smw-facetedsearch-size-options": "options de pagination", "smw-facetedsearch-order-options": "Options de tri", "smw-facetedsearch-format-options": "Options d’affichage", "smw-facetedsearch-format-table": "<PERSON><PERSON>", "smw-facetedsearch-input-filter-placeholder": "Filtre…", "smw-facetedsearch-no-filters": "Aucun filtre.", "smw-facetedsearch-no-filter-range": "Pas de filtre ranger", "smw-facetedsearch-no-output": "Pour le format « $1 », aucune sortie n'était disponible.", "smw-facetedsearch-clear-filters": "Effacer {{PLURAL:$1|le filtre|les filtres}}", "smw-search-placeholder": "Recherche en cours...", "smw-listingcontinuesabbrev": "(suite)", "smw-showingresults": "Affichage d’au maximum <strong>$1</strong> résultat{{PLURAL:$1||s}} à partir du nº <strong>$2</strong>."}