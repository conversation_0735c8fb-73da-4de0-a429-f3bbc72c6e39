{"@metadata": {"authors": ["ArenaL5", "Athena in Wonderland", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "McDut<PERSON><PERSON>", "Nemo bis", "Prevert", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>osecal<PERSON>"]}, "smw-desc": "Fai o seu wiki máis accesible; para máquinas ''e'' humanos ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentación en liña])", "smw-error": "Erro", "smw-upgrade-error-title": "Erro » MediaWiki Semántico", "smw-semantics-not-enabled": "A funcionalidade de MediaWiki Semántica non está activada neste wiki.", "smw_viewasrdf": "Fonte de novas RDF", "smw_finallistconjunct": " e", "smw-factbox-head": "... máis sobre \"$1\"", "smw-factbox-facts": "Feitos", "smw-factbox-facts-help": "Amosa as declaracións e feitos creados por un usuario", "smw-factbox-facts-derived": "Feitos derivados", "smw-factbox-facts-derived-help": "Amosa os feitos que foron derivados de regras ou coa axuda doutras técnicas de razoamento", "smw_isspecprop": "Esta propiedade é especial neste wiki.", "smw-concept-cache-header": "<PERSON><PERSON> da caché", "smw-concept-cache-count": "A [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count caché de conceptos] contén  {{PLURAL:$1|'''unha''' entidade|'''$1''' entidades}} ($2).", "smw-concept-no-cache": "Caché non dispoñible.", "smw_concept_description": "Descrición do concepto \"$1\"", "smw_no_concept_namespace": "O conceptos só poden ser definidos nas páxinas que están no espazo de nomes Concepto:.", "smw_multiple_concepts": "Cada páxina de conceptos só pode conter unha definición dun concepto.", "smw_concept_cache_miss": "O concepto \"$1\" non pode ser usado desde que a configuración do wiki o require para calcular a desconexión. Se o problema non se resolve en breve, pregúntelle ao administrador do wiki para que o concepto poida estar dispoñible.", "smw_noinvannot": "Non se poden asignar os valores para inverter as propiedades.", "version-semantic": "Extensións semánticas", "smw_baduri": "<PERSON><PERSON><PERSON><PERSON><PERSON>, os enderezos URI da forma \"$1\" non están permitidos.", "smw_printername_count": "Contador de resultados", "smw_printername_csv": "Exportación en CSV", "smw_printername_dsv": "Exportación en DSV", "smw_printername_debug": "<PERSON><PERSON><PERSON> a pescuda (para expertos)", "smw_printername_embedded": "Contidos incrustados na páxina", "smw_printername_json": "Exportación en JSON", "smw_printername_list": "Lista", "smw_printername_plainlist": "Lista simple", "smw_printername_ol": "Lista numerada", "smw_printername_ul": "Lista con asteriscos", "smw_printername_table": "Táboa", "smw_printername_broadtable": "Táboa ampla", "smw_printername_template": "<PERSON><PERSON>", "smw_printername_templatefile": "Ficheiro de modelo", "smw_printername_rdf": "Exportación en RDF", "smw_printername_category": "Categoría", "validator-type-class-SMWParamSource": "texto", "smw-paramdesc-limit": "O número máximo de resultados a devolver", "smw-paramdesc-offset": "O desprazamento do primeiro resultado", "smw-paramdesc-headers": "<PERSON><PERSON> as cab<PERSON><PERSON><PERSON>/os nomes de propiedade", "smw-paramdesc-mainlabel": "A lapela para dar nome á páxina principal", "smw-paramdesc-link": "Amosar os valores como ligazóns", "smw-paramdesc-intro": "O texto a amosar antes dos resultados da pescuda, se houbese algún", "smw-paramdesc-outro": "O texto a amosar despois dos resultados da pescuda, se houbese algún", "smw-paramdesc-default": "O texto a amosar se non hai resultados para a pescuda", "smw-paramdesc-sep": "O separador entre resultados", "smw-paramdesc-propsep": "O separador entre as propiedades dunha entrada de resultado", "smw-paramdesc-valuesep": "O separador entre entre os valores para unha propiedade de resultado", "smw-paramdesc-showsep": "Amosar o separador ao comezo do ficheiro CSV (\"sep=<value>\")", "smw-paramdesc-distribution": "No canto de amosar tódolos valores, contar as s<PERSON>as ocorrencias e amosalas.", "smw-paramdesc-distributionsort": "Ordenar a distribución de valores por número de ocorrencias.", "smw-paramdesc-distributionlimit": "Limitar a distribución de valores á contaxe dalgúns valores soamente.", "smw-paramdesc-aggregation": "Especifique con que debe relacionarse o grupo", "smw-paramdesc-template": "O nome dun modelo co que amosar as impresións", "smw-paramdesc-columns": "O número de columnas nas que amosar os resultados", "smw-paramdesc-userparam": "O valor pasado en cada chamada de modelo, se se empregase algún modelo", "smw-paramdesc-class": "Unha clase adicional de CSS a definir para a lista", "smw-paramdesc-introtemplate": "O nome dun modelo a amosar antes dos resultados da pescuda, se houbese algún", "smw-paramdesc-outrotemplate": "O nome dun modelo a amosar despois dos resultados da pescuda, se houbese algún", "smw-paramdesc-embedformat": "A etiqueta HTML utilizada para definir as cabeceiras", "smw-paramdesc-embedonly": "Non amosar as cabeceiras", "smw-paramdesc-table-class": "Unha clase CSS adicional que establecer para a táboa", "smw-paramdesc-table-transpose": "Amosar as cabeceiras de táboa verticalmente e os resultados horizontalmente", "smw-paramdesc-rdfsyntax": "A sintaxe de RDF a usar", "smw-paramdesc-csv-sep": "Especifica un separador de columnas", "smw-paramdesc-csv-valuesep": "Especifica un separador de valores", "smw-paramdesc-csv-merge": "Fusionar filas e valores de columnas co mesmo identificador de suxeito (alias primeira columna)", "smw-paramdesc-csv-bom": "Engadir un BOM (carácter para indicar a «endianness») na parte superior do ficheiro de saída", "smw-paramdesc-dsv-separator": "O separador a empregar", "smw-paramdesc-dsv-filename": "O nome para o ficheiro DSV", "smw-paramdesc-filename": "O nome para o ficheiro de saída", "smw-smwdoc-description": "Amosa unha táboa de todos os parámetros que se poden empregar para un formato de resultados especificado xunto aos valores por defecto e descricións.", "smw-smwdoc-default-no-parameter-list": "Este formato de resultado non proporciona parámetros específicos de formato.", "smw-smwdoc-par-format": "O formato dos resultados do que amosar a documentación do parámetro.", "smw-smwdoc-par-parameters": "Os parámetros que amosar. \"specific\" para aqueles engadidos polo formato, \"base\" para aqueles dispoñibles en tódolos formatos e \"all\" para ambos.", "smw-paramdesc-sort": "Propiedade a partir da que ordenar a pescuda", "smw-paramdesc-order": "Orde da ordenación da pescuda", "smw-paramdesc-searchlabel": "Texto para continuar a procura", "smw-paramdesc-named_args": "Dalle nome ós argumentos pasados ó modelo", "smw-paramdesc-template-arguments": "Fixa a maneira na que se pasan os argumentos nomeados ó modelo", "smw-paramdesc-import-annotation": "Os datos suplementarios anotados serán copiados durante a análise dun suxeito", "smw-paramdesc-export": "Opción de exportación", "smw-paramdesc-prettyprint": "Unha saída cun formato xeitoso que amosa sangrías e liñas novas adicionais", "smw-paramdesc-json-unescape": "Saída para conter barras sen codificar e caracteres Unicode multibytes", "smw-paramdesc-json-type": "Tipo de serialización", "smw-paramdesc-source": "Fonte alternativa de pescuda", "smw-paramdesc-jsonsyntax": "Sintaxe JSON a utilizar", "smw-printername-feed": "Fonte de novas RSS e Atom", "smw-paramdesc-feedtype": "<PERSON><PERSON><PERSON> de fonte de novas", "smw-paramdesc-feedtitle": "O texto a usar como título da fonte de novas", "smw-paramdesc-feeddescription": "O texto a usar como descrición da fonte de novas", "smw-paramdesc-feedpagecontent": "<PERSON><PERSON><PERSON> da páxina a amosar coa fonte de novas", "smw-label-feed-description": "Fonte de novas $1 $2", "smw_iq_disabled": "As preguntas semánticas están deshabilitadas neste wiki.", "smw_iq_moreresults": "… máis resultados", "smw_parseerror": "O valor dado non foi entendido.", "smw_decseparator": ",", "smw_kiloseparator": ".", "smw_notitle": "\"$1\" non pode usarse como nome de páxina neste wiki.", "smw_noproperty": "\"$1\" non pode usarse como nome de propiedade neste wiki.", "smw_wrong_namespace": "Aquí só están permitidas as páxinas no espazo de nomes \"$1\".", "smw_manytypes": "<PERSON><PERSON><PERSON> dun tipo definido para a propiedade.", "smw_emptystring": "As cordas baleiras non están aceptadas.", "smw_notinenum": "\"$1\" non está na lista de [[Property:Allows value|valores permitidos]] ($2) para a propiedade  \"$3\".", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" non está na lista de [[Property:Allows value|valores permitidos]] ($2) para a propiedade  \"$3\".", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" non está no intervalo de \"$2\" especificado pola restrición [[Property:Allows value|permite valor]] para a propiedade \"$3\".", "smw_noboolean": "\"$1\" non é recoñecido como un valor booleano (verdadeiro/falso).", "smw_true_words": "verda<PERSON><PERSON>,v,si,s", "smw_false_words": "falso,f,non,n", "smw_nofloat": "\"$1\" non é un número.", "smw_infinite": "Os números tan longos como \"$1\" non están soportados.", "smw_unitnotallowed": "\"$1\" non está declarada como unha unidade de medida válida para esta propiedade.", "smw_nounitsdeclared": "Non hai unidades de medida declaradas para esta propiedade.", "smw_novalues": "Non se especificou ningún valor.", "smw_nodatetime": "A data \"$1\" non foi entendida.", "smw_toomanyclosing": "Parece que hai demasiados acontecementos de \"$1\" na pregunta.", "smw_noclosingbrackets": "Algún uso de \"<nowiki>[[</nowiki>\" na súa pregunta non foi pechado polo seu \"]]\" correspondente.", "smw_misplacedsymbol": "O símbolo \"$1\" foi usado nun lugar no que non era útil.", "smw_unexpectedpart": "A parte \"$1\" da pregunta non foi entendida.\nPode que os resultados non sexan os agardados.", "smw_emptysubquery": "Algunha subcuestión non ten unha condición válida.", "smw_misplacedsubquery": "Algunha subcuestión foi usada nun lugar onde non están permitidas.", "smw_valuesubquery": "As subcuestións non están soportadas para os valores da propiedade \"$1\".", "smw_badqueryatom": "Unha parte \"<nowiki>[[…]]</nowiki>\" da pregunta non foi entendida.", "smw_propvalueproblem": "O valor da propiedade \"$1\" non foi entendido.", "smw_noqueryfeature": "Algunha característica da pregunta non está soportada neste wiki, polo que unha parte foi excluída ($1).", "smw_noconjunctions": "As conxuncións nas preguntas non están soportadas neste wiki, polo que unha parte foi excluída ($1).", "smw_nodisjunctions": "Non están soportadas as disxuncións nas preguntas neste wiki e parte desta foi ignorada ($1).", "smw_querytoolarge": "{{PLURAL:$2|A seguinte condición da consulta <code>$1</code> non pode ser considerada|As seguintes $2 condicións da consulta <code>$1</code> non poden ser consideradas}} debido ás restricións do wiki para o tamaño ou profundidade das consultas.", "smw_notemplategiven": "Por favor, proporcione un valor para o parámetro “modelo” do formato desta pregunta para poder funcionar.", "smw_db_sparqlqueryproblem": "Non se puideron obter os resultado da pescuda da base de datos SPARQL. Este erro pode ser temporal ou indicar un erro no programa da base de datos.", "smw_db_sparqlqueryincomplete": "Resultou demasiado difícil responder á pescuda e cancelouse. É probable que falten algúns resultados. Se fose posible, intente facer unha consulta máis sinxela.", "smw_type_header": "Propiedades do tipo \"$1\"", "smw_typearticlecount": "Amosando $1 {{PLURAL:$1|propiedade|propiedades}} que {{PLURAL:$1|usa|usan}} este tipo.", "smw_attribute_header": "Páxinas que usan a propiedade \"$1\"", "smw_attributearticlecount": "Amosando $1 {{PLURAL:$1|páxina|páxinas}} que {{PLURAL:$1|usa|usan}} esta propiedade.", "smw-propertylist-subproperty-header": "Subpropiedades", "smw-propertylist-redirect-header": "Sinónimos", "smw-propertylist-error-header": "Páxinas con atribucións incorrectas", "smw-propertylist-count": "{{PLURAL:$1|Móstrase $1 entidade relacionada|Móstranse $1 entidades relacionadas}}.", "smw-propertylist-count-with-restricted-note": "{{PLURAL:$1|Móstrase a $1 entidade relacionada|Móstranse as $1 entidades relacionadas}} (hai máis dispoñibles pero a visualización está restrinxida a \"$2\").", "smw-propertylist-count-more-available": "Amosando $1 {{PLURAL:$1|entidade asociada|entidades asociadas}} (hai máis dispoñibles).", "exportrdf": "Exportar páxinas a RDF", "smw_exportrdf_docu": "Esta páxina permítelle obter datos dunha páxina en formato RDF.\nPara exportar páxinas, insira os títulos na caixa de embaixo (un título por liña).", "smw_exportrdf_recursive": "Exportar igualmente todas as páxinas relacionadas.\nDéase conta de que o resultado pode ser longo!", "smw_exportrdf_backlinks": "Exportar tamén todas as páxinas que se refiren ás páxinas exportadas.\nXera un RDF que se pode navegar.", "smw_exportrdf_lastdate": "Non exportar páxina que non tiveron cambios desde a data dada.", "smw_exportrdf_submit": "Exportar", "uriresolver": "Solucionador de URI", "properties": "Propiedades", "smw-categories": "Categorías", "smw_properties_docu": "Este wiki usa as se<PERSON><PERSON> propiedades.", "smw_property_template": "\"$1\" de tipo \"$2\" ($3 {{PLURAL:$3|uso|usos}})", "smw_propertylackspage": "<PERSON><PERSON> as propiedades deberían estar descritas nunha páxina!", "smw_propertylackstype": "Non foi especificado ningún tipo para esta propiedade (asúmese o tipo \"$1\" polo de agora).", "smw_propertyhardlyused": "<PERSON><PERSON> propiedade apenas ten uso neste wiki!", "smw-property-name-invalid": "Non se pode utilizar a propiedade \"$1\" (nome de propiedade non válido).", "smw-property-name-reserved": "«$1» marcouse como nome reservado e, polo tanto, non debe utilizarse como propiedade. É posible que esta [https://www.semantic-mediawiki.org/wiki/Help:Property_naming páxina de axuda] inclúa información sobre o motivo polo que este nome se reservou.", "smw-sp-property-searchform": "<PERSON><PERSON> as propiedades que conteñan:", "smw-sp-property-searchform-inputinfo": "A entrada distingue entre maiúsculas e minúsculas ao utilizala para filtrar; só se amosan as propiedades que coinciden coa condición.", "smw-special-property-searchform": "<PERSON><PERSON> as propiedades que conteñenː", "smw-special-property-searchform-inputinfo": "A entrada distingue entre maiúsculas e minúsculas ao utilizala para filtrar; só se amosan as propiedades que coinciden coa condición.", "smw-special-property-searchform-options": "Opcións", "smw-special-wantedproperties-filter-label": "Filtro:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "<PERSON> aprobar", "smw-special-wantedproperties-filter-unapproved-desc": "Opción de filtro usado en conexión co módulo de autoridade.", "concepts": "Conceptos", "smw-special-concept-docu": "Un [https://www.semantic-mediawiki.org/wiki/Help:Concepts concepto] pódese ver como unha \"categoría dinámica\", é dicir, como unha colección de páxinas que non están creadas manualmente, pero que son computadas por Semantic MediaWiki desde a descrición dunha pescuda proporcionada.", "smw-special-concept-header": "Lista de conceptos", "smw-special-concept-count": "{{PLURAL:$1|Está listado o seguinte concepto|Están listados os seguintes $1 conceptos}}.", "smw-special-concept-empty": "Non se atopou concepto ningún.", "unusedproperties": "Propiedades non usadas", "smw-unusedproperties-docu": "Esta páxina lista [https://www.semantic-mediawiki.org/wiki/Unused_properties propriedades non usada] que foron declaradas, aínda que ningunha páxina as utiliza. Para unha visión diferenciada, consulte as páxinas especiais con [[Special:Properties|tódalas propriedades]] ou coas [[Special:WantedProperties|propriedades solicitadas]].", "smw-unusedproperty-template": "\"$1\" de tipo \"$2\"", "wantedproperties": "Propiedades requiridas", "smw-wantedproperties-docu": "Esta páxina lista [https://www.semantic-mediawiki.org/wiki/Wanted_properties propiedades solicitadas] que son utilizadas na wiki, pero que non posúen unha páxina describíndoas. Para unha visión diferenciada, consulte as páxinas especiais con [[Special:Properties|tódalas propiedades]] ou coas [[Special:UnusedProperties|propiedades non utilizadas]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw-special-wantedproperties-docu": "Esta páxina lista [https://www.semantic-mediawiki.org/wiki/Wanted_properties propiedades solicitadas] que son utilizadas na wiki, pero que non posúen unha páxina describíndoas. Para unha visión diferenciada, consulte as páxinas especiais con [[Special:Properties|tódalas propiedades]] ou coas [[Special:UnusedProperties|propiedades non utilizadas]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw_purge": "<PERSON><PERSON><PERSON><PERSON>", "smw-purge-failed": "Fallou o refresco", "types": "Tipos", "smw_types_docu": "Lista dos [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tipos de datos dispoñibles] con cada [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipo] representando un conxunto único de atributos para describir un valor en termos características de almacenamento e visualización que son heredadas nunha propiedade asignada.", "smw-special-types-no-such-type": "\"$1\" é descoñecido ou non foi especificado como tipo de datos válido.", "smw-statistics": "Estatísticas semánticas", "smw-statistics-property-instance": "{{PLURAL:$1|Valor|Valores}} de propiedade (total)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Propiedade|Propiedades}}]] (total)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Propiedade|Propiedades}} (total)", "smw-statistics-property-used": "{{PLURAL:$1|Propiedade|Propiedades}} (utilizadas con polo menos un valor)", "smw-statistics-property-page": "{{PLURAL:$1|Propiedade|Propiedades}} ({{PLURAL:$1|rexistrada|rexistradas}} cunha páxina)", "smw-statistics-property-type": "{{PLURAL:$1|Propiedade|Propiedades}} ({{PLURAL:$1|asociada|asociadas}} a un tipo de datos)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Consulta|Consultas}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|<PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON>}}]]", "smw-statistics-query-size": "<PERSON><PERSON><PERSON>escu<PERSON>", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Concepto|Conceptos}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Concepto|Conceptos}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Subobxecto|Subobxectos}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Subobxecto|Subobxectos}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Tipo|Tipos}} de datos]]", "smw-statistics-error-count": "{{PLURAL:$1|<PERSON><PERSON> de propiedade|<PERSON><PERSON> de propiedade}}\n([[Special:ProcessingErrorList|{{PLURAL:$1|anotación incorrecta|anotacións incorrectas}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|<PERSON>or|Valores}} de propiedade ({{PLURAL:$1|anotación incorrecta|anotacións incorrectas}})", "smw-statistics-delete-count": "{{PLURAL:$1|Entidade obsoleta|Entidades obsoletas}} (marcada para borrado)", "smw_uri_doc": "O solucionador de URI pon en práctica o [$1 descubrimento da ETIQUETA de W3C en httpRange-14].\nVixía que os humanos non entren en sitios web.", "ask": "Procura <PERSON>", "smw-ask-help": "Esta sección contén algunhas ligazóns para axudar a explicar como utilizar a sintaxe <code>#ask</code>.\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selección de páxinas] describe como seleccionar páxinas e construír condicións.\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadores de procura] enumera os operadores de procura dispoñibles, incluídos os de consulta de intervalo e de caracteres comodíns.\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Mostra de información] expón o uso das declaracións de saída e as opcións de formatado.", "smw_ask_sortby": "Ordenar por columnas (opcional)", "smw_ask_ascorder": "Ascendente", "smw_ask_descorder": "Descendente", "smw-ask-order-rand": "<PERSON><PERSON> chou", "smw_ask_submit": "Atopar os resultados", "smw_ask_editquery": "Editar a pregunta", "smw_add_sortcondition": "[Engadir unha condición de ordenación]", "smw-ask-sort-add-action": "Engadir condición de ordenación", "smw_ask_hidequery": "Agochar a pescuda (vista compacta)", "smw_ask_help": "<PERSON><PERSON><PERSON> sobre as p<PERSON><PERSON>", "smw_ask_queryhead": "Condición", "smw_ask_printhead": "Selección dos datos a imprimir", "smw_ask_printdesc": "(engada un nome de propiedade por liña)", "smw_ask_format_as": "Formato de:", "smw_ask_defaultformat": "predeterminado", "smw_ask_otheroptions": "Outras opcións", "smw-ask-otheroptions-info": "Esta sección contén opcións que alteran os formatos de saída. As descricións dos parámetros pódense consultar pasando o rato por riba delas.", "smw-ask-otheroptions-collapsed-info": "Use a icona do signo máis para ollar todas as opcións dispoñibles", "smw_ask_show_embed": "Amosar o código incrustado", "smw_ask_hide_embed": "Agochar o código incrustado", "smw_ask_embed_instr": "Para incrustar esta pescuda en liña nunha páxina wiki use o seguinte código.", "smw-ask-delete": "<PERSON><PERSON><PERSON>", "smw-ask-sorting": "Ordenación", "smw-ask-options": "Opcións", "smw-ask-options-sort": "Opcións de ordenación", "smw-ask-format-options": "Formato e opcións", "smw-ask-parameters": "Parámetros", "smw-ask-search": "Procurar", "smw-ask-debug": "<PERSON><PERSON><PERSON>", "smw-ask-debug-desc": "Xera información para a revisión de erros das consultas", "smw-ask-no-cache": "Desactivar a caché de consultas", "smw-ask-no-cache-desc": "Resultados sen caché de consulta", "smw-ask-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-empty": "<PERSON><PERSON><PERSON> todas as entradas", "smw-ask-download-link-desc": "Descargar os resultados da consulta en formato $1", "smw-ask-format": "Formato", "smw-ask-format-selection-help": "Axuda co formato seleccionadoː $1", "smw-ask-condition-change-info": "A condición foi modificada e o motor de procuras precisa que a consulta volva ser executada para producir resultados que correspondan ós novos requisitos.", "smw-ask-input-assistance": "Asistencia de entrada", "smw-ask-condition-input-assistance": "Proporciónase [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance asistencia de entrada] para os campos de saída, de ordenación e de condición. O campo de condición precisa o uso dalgún dos seguintes prefixos:", "smw-ask-condition-input-assistance-property": "<code>p:</code> para obter as suxesti<PERSON>s de propiedades (exemplo: <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> para obter as suxestións de categorías", "smw-ask-condition-input-assistance-concept": "<code>con:</code> para obter as suxestións de conceptos", "smw-ask-format-change-info": "O formato foi modificado e é preciso para reexecutar a consulta para corresponderse ós novos parámetros e opcións de visualización.", "smw-ask-format-export-info": "O formato seleccionado é un formato de exportación que non ten representación visual, polo tanto os resultados só se proporcionan como descarga.", "smw-ask-query-search-info": "A consulta <code><nowiki>$1</nowiki></code> foi respondida polo {{PLURAL:$3|1=<code>$2</code> (desde a caché)|<code>$2</code> (desde a caché)|<code>$2</code>}} en $4 {{PLURAL:$4|segundo|segundos}}.", "searchbyproperty": "<PERSON><PERSON><PERSON> por propiedade", "processingerrorlist": "Lista de erros de tratamento", "propertylabelsimilarity": "Informe de similitude de etiquetas de propiedade", "smw-processingerrorlist-intro": "A seguinte lista proporciona unha visión dos [https://www.semantic-mediawiki.org/wiki/Processing_errors erros de tratamento] que apareceron en relación con [https://www.semantic-mediawiki.org/ MediaWiki Semántica]. Recoméndase vixiar esta lista de forma regular e corrixir as anotacións de valor incorrecto.", "smw_sbv_docu": "<PERSON><PERSON><PERSON> to<PERSON> as p<PERSON>xinas que teñen a propiedade e o valor dados.", "smw_sbv_novalue": "Por favor, insira un valor válido para a propiedade ou vexa todos os valores das propiedades para \"$1\".", "smw_sbv_displayresultfuzzy": "Unha lista con todas as páxinas que teñen a propiedade \"$1\" co valor \"$2\".\nComo houbo só uns poucos resultados, móstranse tamén os resultados próximos.", "smw_sbv_property": "Propiedade:", "smw_sbv_value": "Valor:", "smw_sbv_submit": "Atopar os resultados", "browse": "Explorar o wiki", "smw_browselink": "Explorar as propiedades", "smw_browse_article": "Insira o nome da páxina para comezar a navegación.", "smw_browse_go": "<PERSON>r", "smw_browse_show_incoming": "<PERSON><PERSON> as propiedades entrantes", "smw_browse_hide_incoming": "<PERSON><PERSON><PERSON><PERSON> as propiedades entrantes", "smw_browse_no_outgoing": "<PERSON>sta páxina non ten propiedades.", "smw_browse_no_incoming": "<PERSON><PERSON>un<PERSON> propiedade liga con esta páxina.", "smw-browse-from-backend": "Estase a recuperar a información do servidor.", "smw-browse-intro": "Esta páxina proporciona detalles sobre un tema ou instancia de entidade, por favor, indique o nome dun obxecto para ser inspeccionado.", "smw-browse-invalid-subject": "A validación do tema devolveu o erro \"$1\".", "smw-browse-api-subject-serialization-invalid": "O tema ten un formato de serialización incorrecto.", "smw-browse-js-disabled": "É probable que o Javascript estea desactivado ou non dispoñible, recomendamos usar un navegador que o soporte. Outras opcións coméntanse na páxina do parámetro de configuración [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Amosar grupos", "smw-browse-hide-group": "Ocultar grupos", "smw-noscript": "Esta páxina ou acción require Javascript para funcionar. Active o Javascript no seu navegador ou utilice un navegador que o suporte, para que esta funcionalidade poida ser servida e é proporcionada como solicitada. Para máis información, consulte a páxina de axuda [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "<PERSON><PERSON><PERSON><PERSON> da propiedade inversa", "pageproperty": "Procura de propiedades de páxinas", "smw_pp_docu": "Introduza unha páxina e propiedade, ou só unha propiedade, para obter tódolos valores asignados.", "smw_pp_from": "<PERSON> páxinaː", "smw_pp_type": "Propiedadeː", "smw_pp_submit": "Atopar os resultados", "smw_result_prev": "Anterior", "smw_result_next": "Se<PERSON><PERSON>", "smw_result_results": "Resul<PERSON><PERSON>", "smw_result_noresults": "Non hai resultados.", "smwadmin": "Funcións administrativas e de mantemento", "smw-admin-statistics-job-title": "Estatísticas de tarefas", "smw-admin-statistics-job-docu": "As estatísticas de tarefas amosan información sobre tarefas programadas do MediaWiki Semántico que aínda non foron executadas. O número de tarefas pode ter pequenas imprecisións ou conter intentos errados; consulte o [https://www.mediawiki.org/wiki/Manual:Job_queue manual] para máis información.", "smw-admin-statistics-querycache-title": "Caché das procuras", "smw-admin-statistics-querycache-disabled": "A [https://www.semantic-mediawiki.org/wiki/QueryCache ''cache'' de consultas] non foi activada nesta wiki, polo tanto non hai estatísticas dispoñíbeis.", "smw-admin-permission-missing": "Debido á falta dos permisos necesarios, bloqueouse o acceso a esta páxina, por favor, consulte a páxina de axuda sobre os [https://www.semantic-mediawiki.org/wiki/Help:Permissions permisos] para obter detalles das configuracións necesarias.", "smw-admin-setupsuccess": "O motor de almacenamento foi configurado.", "smw_smwadmin_return": "Volver a \"$1\"", "smw_smwadmin_updatestarted": "Comezou un novo proceso de actualización para refrescar os datos semánticos.\nTodos os datos almacenados serán reconstruídos ou reparados onde sexa necesario.\nPode seguir o proceso da actualización nesta páxina especial.", "smw_smwadmin_updatenotstarted": "Xa hai un proceso de actualización en curso.\nNon se pode crear outro.", "smw_smwadmin_updatestopped": "Todos os procesos de actualización existentes foron detidos.", "smw_smwadmin_updatenotstopped": "Para deter os procesos de actualización actuais, debe activar a caixa de verificación para indicar que está seguro de facelo.", "smw-admin-docu": "Esta páxina especial serve de axuda durante a instalación, actualización, mantemento e uso do <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> e ademais de proporcionar funcións administrativas e tarefas, así como estatísticas.\nLembre facer unha copia de seguridade dos datos antes de executar calquera función administrativa.", "smw-admin-environment": "Contorno de programación", "smw-admin-db": "Mantemento da base de datos", "smw-admin-db-preparation": "A inicialización da táboa está en curso e pode tardar algún tempo ata que se amosen os resultados, dependendo do tamaño da táboa e de posibles optimizacións da mesma.", "smw-admin-dbdocu": "Semantic MediaWiki require algunhas extensións da base de datos MediaWiki para almacenar os datos semánticos.\nA función de embaixo asegúrase de que a súa base de datos está configurada apropiadamente.\nOs cambios feitos neste paso non afectarán ao resto da base de datos MediaWiki, e poden ser desfeitos de xeito sinxelo se se desexa.\nEsta función de configuración pode ser executada múltiples veces sen facer ningún dano, pero só é necesaria unha vez na instalación ou actualización.", "smw-admin-permissionswarn": "Se a operación falla con erros SQL, probablemente o usuario da base de datos empregada polo seu wiki (comprobe o seu ficheiro \"LocalSettings.php\") non teña os permisos suficientes.\nFai falla conceder a este usuario os permisos para crear e eliminar táboas; temporalmente insira o rexistro da súa base de datos no ficheiro \"LocalSettings.php\", ou use o script de mantemento <code>setupStore.php</code>, que pode usar as credenciais dun administrador.", "smw-admin-dbbutton": "<PERSON><PERSON><PERSON><PERSON> ou <PERSON><PERSON><PERSON> as t<PERSON><PERSON><PERSON>", "smw-admin-announce": "Anuncia o teu wiki", "smw-admin-announce-text": "Se a súa wiki é pública, pode rexistrala en <a href=\"https://wikiapiary.com\">WikiApiary</a>, o catálogo wiki de wikis.", "smw-admin-deprecation-notice-title": "Avisos de obsolescencia", "smw-admin-deprecation-notice-docu": "A seguinte sección contén axustes que quedaron obsoletos ou están eliminados, pero que foron detectados activos neste wiki. Espérase que nunha versión futura se elimine o soporte para estas configuracións.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> está obsoleta e será eliminada de $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> eliminar<PERSON> (ou substituirá) {{PLURAL:$2|a opción seguinte|as opcións seguintes}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> está obsoleto e será eliminado na versión $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi substituído por <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|opción|opcións}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> está a ser substituído por <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi eliminado de $2", "smw-admin-deprecation-notice-title-notice": "Configuración obsoleta", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Configuración obsoleta</b> mostra aqueles axustes que se utilizan no wiki, pero que se van eliminar ou modificar nunha versión futura.", "smw-admin-deprecation-notice-title-replacement": "Configuración substituída ou renomeada", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Configuración substituída ou renomeada</b> contén aqueles axustes que foron renomeadas ou recibiron cambios doutro tipo e recoméndase que se actualice inmediatamente o seu nome ou o seu formato.", "smw-admin-deprecation-notice-title-removal": "Configuración eliminada", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Configuración eliminada</b> identifica aqueles axustes eliminados nunha versión anterior, pero que aínda se utilizan neste wiki.", "smw-smwadmin-refresh-title": "Reparación e actualización de datos", "smw_smwadmin_datarefresh": "Reparación dos datos", "smw_smwadmin_datarefreshdocu": "É posible restaurar todos os datos de Semantic MediaWiki baseados nos contidos actuais do wiki.\n<PERSON><PERSON> pode ser útil para reparar datos rotos ou para refrescar os datos se o formato interno cambiou debido a actualizacións do software.\nEsta actualización é executada páxina a páxina e non será completada inmediatamente.\nO seguinte amosa se unha actualización está en curso e permítelle comezalas ou detelas (a non ser que esta característica fose deshabilitada polo administrador do sitio).", "smw_smwadmin_datarefreshprogress": "<strong>Xa hai unha actualización en curso.</strong>\nÉ normal que os progresos de actualización vaian lentos, xa que só se refrescan os datos nos pequenos anacos nos que un usuario accede ao wiki.\nPara finalizar esta actualización máis rápido, pode invocar a escritura <code>runJobs.php</code> de mantemento de MediaWiki (use a opción <code>--maxjobs 1000</code> para restrinxir o número de actualizacións feitas nunha quenda).\nProgreso estimado da actualización actual:", "smw_smwadmin_datarefreshbutton": "Programar a reconstrución dos datos", "smw_smwadmin_datarefreshstop": "Deter esta actualización", "smw_smwadmin_datarefreshstopconfirm": "Si, estou {{GENDER:$1|seguro|segura}}.", "smw-admin-job-scheduler-note": "A maioría das actividades prográmanse como traballos para que as tarefas se executen por lotes no planificador de tarefas para planificar e completar o procesamento, polo que é crítico que os scripts de mantemento <code>runJobs.php</code> ou <code>$wgRunJobsAsync</code> se xestionen en consecuencia.", "smw-admin-outdateddisposal-title": "Eliminación das entidades obsoletas", "smw-admin-outdateddisposal-intro": "Algunhas actividades (o cambio dun tipo de propiedade, a eliminación de páxinas da wiki, ou a corrección de valores erróneos) provocarán [https://www.semantic-mediawiki.org/wiki/Outdated_entities entidades desactualizadas] e recoméndase que estas sexa eliminadas periodicamente para liberar o seu espazo nas táboas.", "smw-admin-outdateddisposal-active": "Planificouse unha tarefa de eliminación de entidades obsoletas.", "smw-admin-outdateddisposal-button": "Programar unha eliminación", "smw-admin-feature-disabled": "Esta funcionalidade está desactivada neste wiki, por favor consulte a páxina de axuda de <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">configuración</a> ou contacte cun administrador  de sistemas.", "smw-admin-propertystatistics-title": "Reconstrución das estatísticas das propiedade", "smw-admin-propertystatistics-intro": "Reconstrúe tódalas estatísticas de uso das propiedades e actualiza e corrixe os [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count  contadores de uso] das propiedades.", "smw-admin-propertystatistics-active": "Planificouse unha tarefa de reconstrución de propiedades.", "smw-admin-propertystatistics-button": "Programa unha reconstrución das estatísticas", "smw-admin-fulltext-title": "Reconstrución da procura de texto completo", "smw-admin-fulltext-intro": "Reconstrúe o índice de procura en base a táboas de propiedade cun tipo de dato activado [https://www.semantic-mediawiki.org/wiki/full-text procura de texto completo]. Cambios nas regras de indexado (stopwords modificadas, novo stemmer etc.) e/ou unha táboa nova ou cambiada requiren que esta tarefa se execute de novo.", "smw-admin-fulltext-active": "Planificouse unha tarefa de reconstrución da procura de texto completo.", "smw-admin-fulltext-button": "Programa unha reconstrución de texto completo", "smw-admin-support": "Obter asistencia", "smw-admin-supportdocu": "Diversos recursos proporciónanse para axudalo en caso de problemas:", "smw-admin-installfile": "Se experimenta problemas coa súa instalación, comece comprobando a guía no <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">ficheiro INSTALL</a> e a <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">páxina de instalación</a>.", "smw-admin-smwhomepage": "A documentación completa de usuario de Semantic MediaWiki está en <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Pode informar dos erros no <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">rexistro de problemas</a>, onde a páxina <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">para informar de erros</a> proporciona instrucións para crear informes de problemas eficaces.", "smw-admin-questions": "Se ten máis preguntas ou suxestións, únase á conversa na <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">lista de correo de usuarios</a>de Semantic MediaWiki ou a <a href=\"https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_chatroom\">sala de conversa</a>.", "smw-admin-other-functions": "Outras funcións", "smw-admin-supplementary-section-title": "Funcións suplementarias", "smw-admin-supplementary-section-subtitle": "Funcións básicas soportadas", "smw-admin-supplementary-section-intro": "Algunhas das funcións listadas nesta sección poden estar restrinxidas e, polo tanto, son inaccesibles neste wiki.", "smw-admin-supplementary-settings-title": "Configuración e axustes", "smw-admin-supplementary-settings-intro": "<u>$1</u> devolve unha lista colectiva de parámetros dispoñibles usados no MediaWiki Semántico", "smw-admin-supplementary-operational-statistics-title": "Estatísticas de funcionamento", "smw-admin-supplementary-operational-statistics-intro": "<u>$1</u> amosa un conxunto estendido de estatísticas", "smw-admin-supplementary-idlookup-title": "Procura e liberación da entidade", "smw-admin-supplementary-idlookup-intro": "<u>$1</u> contén funcións para procurar e liberar entidades individuais", "smw-admin-supplementary-duplookup-title": "Consulta de entidades duplicadas", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> para listar entradas que están categorizadas porque teñen duplicados na táboa de entidades", "smw-admin-supplementary-duplookup-docu": "Esta páxina lista entradas que foron categorizadas como duplicadas na [https://www.semantic-mediawiki.org/wiki/Help:Entity_table táboa de entidades]. As entradas duplicadas só deberían ocurrir (como moito) en raras ocasións potencialmente causadas por un proceso terminado durante unha actualización da base de datos ou unha transacción de reversión non concluída.", "smw-admin-supplementary-operational-statistics-cache-title": "Estatísticas da caché", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> amosa estatísticas relacionadas coa caché", "smw-admin-supplementary-elastic-intro": "<u>$1</u> informa sobre configuración e estatísticas de indexación", "smw-admin-supplementary-elastic-functions": "Funcións soportadas", "smw-admin-supplementary-elastic-settings-title": "Configuración (índices)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> usado por Elasticsearch para administrar índices de MediaWiki Semántica", "smw-admin-supplementary-elastic-mappings-title": "Correspondencias", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> para listar os índices e as correspondencias de campo", "smw-admin-supplementary-elastic-mappings-summary": "Resumo", "smw-admin-supplementary-elastic-mappings-fields": "Correspondencias de campos", "smw-admin-supplementary-elastic-nodes-title": "Nodos", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> amosa estatísticas de nodo", "smw-admin-supplementary-elastic-indices-title": "Índices", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> proporciona unha vista xeral de índices dispoñibles e das súas estatísticas", "smw-admin-supplementary-elastic-statistics-title": "Estatísticas", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> amosa estatísticas de nivel de índice", "smw-admin-supplementary-elastic-status-replication": "Estado de replicación", "smw-admin-supplementary-elastic-status-last-active-replication": "Última replicación activa: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Intervalo de actualización: $1", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replicación bloqueada: $1 (reconstrución en proceso)", "smw-admin-supplementary-elastic-replication-files": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-pages": "Páxinas", "smw-list-count": "A lista contén $1 {{PLURAL:$1|entrada|entradas}}.", "smw-property-label-uniqueness": "Atopouse unha correspondencia entre a etiqueta \"$1\" e polo menos outra representación de propiedade. Consulte a [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness páxina de axuda] sobre a resolución deste problema, por favor.", "smw-property-label-similarity-title": "Informe de similitude de etiquetas de propiedade", "smw-property-label-similarity-intro": "<u>$1</u> calcula a similitude das etiquetas de propiedade existentes", "smw-property-label-similarity-threshold": "Límiteː", "smw-property-label-similarity-type": "Amosa o identificador do tipo", "smw-property-label-similarity-noresult": "Non se atoparon resultados para as opcións seleccionadas.", "smw-property-label-similarity-docu": "Compara e notifica a [https://www.semantic-mediawiki.org/wiki/Property_similarity semellanza sintáctica] (non a semellanza semántica) entre dúas etiquetas de propiedades, o que pode axudar a filtrar propiedades mal escritas ou equivalentes que representen o mesmo concepto (consulte a páxina especial [[Special:Properties|Propiedades]] para clarificar os conceptos e os usos das propiedades notificadas). Pode axustarse o limiar para aumentar ou diminuír a distancia de semellanza. <code>[[Property:$1|$1]]</code> utilízase para excluír propiedades da análise.", "smw-admin-operational-statistics": "Esta páxina contén estatísticas operacionais recollidas en, ou por, funcións relacionadas coa MediaWiki Semántica. Pode atopar unha lista expandida de estatísticas específicas á wiki [[Special:Statistics|<b>aquí</b>]].", "smw_adminlinks_datastructure": "Estrutura dos datos", "smw_adminlinks_displayingdata": "<PERSON><PERSON><PERSON> os datos", "smw_adminlinks_inlinequerieshelp": "Axuda coas pescudas", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Número de usos] estimado: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Propiedade definida polo {{PLURAL:$1|usuario|sistema}}", "smw-property-indicator-last-count-update": "Estimación do número de usos\nÚltima actualización: $1", "smw-concept-indicator-cache-update": "Contador de cache\nÚltima actualización: $1", "smw-createproperty-isproperty": "Esta é unha propiedade de clase $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|O valor permitido|Os valores permitidos}} para esta propiedade {{PLURAL:$1|é|son}}:", "smw-paramdesc-category-delim": "O delimitador", "smw-paramdesc-category-template": "Un modelo co que dar formato aos elementos", "smw-paramdesc-category-userparam": "Un parámetro que pasar ao modelo", "smw-info-par-message": "A mensaxe a amosar.", "smw-info-par-icon": "A icona a amosar; ou ben \"info\" ou ben \"warning\".", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Opcións xerais", "prefs-ask-options": "Procura <PERSON>", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/?uselang=gl Semantic MediaWiki] (e as extensións relacionadas) permiten adaptar individualmente algunhas funcións seleccionadas. Por favor, consulte a [https://www.semantic-mediawiki.org/wiki/Help:User_preferences?uselang=gl páxina de axuda] para unha descrición detallada.", "smw-prefs-ask-options-tooltip-display": "Amosar o texto do parámetro como unha axuda informativa", "smw-prefs-ask-options-compact-view-basic": "Habilita a vista compacta básica", "smw-prefs-help-ask-options-compact-view-basic": "Se se activa, amosa un conxunto reducido de ligazóns na vista compacta Special:Ask.", "smw-prefs-general-options-time-correction": "Activar a corrección da hora para as páxinas especiais usando a preferencia do [[Special:Preferences#mw-prefsection-rendering|fuso horario]] local.", "smw-prefs-general-options-jobqueue-watchlist": "Amosar a lista de vixilancia da cola de tarefas na miña barra persoal", "smw-prefs-general-options-disable-editpage-info": "Desactivar o texto introdutorio da páxina de edición", "smw-prefs-general-options-suggester-textinput": "Activar a [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance asistencia de entrada] para as suxestións das entidades semánticas", "smw-ui-tooltip-title-property": "Propiedade", "smw-ui-tooltip-title-quantity": "Conversión de unidade", "smw-ui-tooltip-title-info": "Información", "smw-ui-tooltip-title-service": "Ligazóns de servizo", "smw-ui-tooltip-title-warning": "Aviso", "smw-ui-tooltip-title-error": "Erro", "smw-ui-tooltip-title-parameter": "Parámetro", "smw-ui-tooltip-title-event": "Evento", "smw-ui-tooltip-title-note": "<PERSON>a", "smw-ui-tooltip-title-legend": "Len<PERSON>", "smw-ui-tooltip-title-reference": "Referencia", "smw_unknowntype": "O tipo desta propiedade non é válido", "smw-concept-cache-text": "O concepto ten {{PLURAL:$1|unha páxina|un total de $1 páxinas}}. A súa última actualización foi o $3, $2.", "smw_concept_header": "Páxinas do concepto \"$1\"", "smw_conceptarticlecount": "A continuación {{PLURAL:$1|móstrase $1 páxina|móstranse $1 páxinas}}.", "smw-qp-empty-data": "Os datos solicitados poderían non amosarse debido a algúns criterios de selección insuficientes.", "right-smw-admin": "Acceder <PERSON><PERSON> tare<PERSON> administrativas (Semantic MediaWiki)", "right-smw-patternedit": "Acceso de edición para manter expresións regulares e patróns permitidos (MediaWiki Semántica)", "right-smw-pageedit": "Acceso de edición para páxinas con anotación <code>Protexida contra a edición</code> (MediaWiki Semántica)", "restriction-level-smw-pageedit": "protexida (só os usuarios elexibles)", "action-smw-patternedit": "editar expresións regulares utilizadas por MediaWiki Semántica", "action-smw-pageedit": "editar páxinas anotadas con <code>Protexida contra a edición</code> (MediaWiki Semántica)", "group-smwadministrator": "Administradores (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrador|administradora}} (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Administradores (Semantic MediaWiki)", "group-smwcurator": "Conservadores (MediaWiki Semántica)", "group-smwcurator-member": "{{GENDER:$1|conservador|conservadora}} (MediaWiki Semántica)", "grouppage-smwcurator": "{{ns:project}}:Conservadores (MediaWiki Semántica)", "action-smw-admin": "acceder <PERSON>s tarefas administrativas de Semantic MediaWiki", "action-smw-ruleedit": "editar páxinas de regra (MediaWiki Semántica)", "smw-property-predefined-default": "\"$1\" é unha propiedade predefinida de tipo $2.", "smw-property-predefined-common": "Esta propiedade está pre-implantada (tamén coñecida como [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propiedade especial]) e ven con privilexios administrativos adicionais pero só pode ser usada como calquera outra [https://www.semantic-mediawiki.org/wiki/Property propiedade definida polo usuario].", "smw-property-predefined-ask": "\"$1\" é unha propiedade predefinida que representa a meta información (na forma dun [https://www.semantic-mediawiki.org/wiki/Subobject subobxecto]) sobre pescudas individuais. E está proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-asksi": "\"$1\" é unha propiedade predefinida que  recolle o número de condicións utilizadas nunha pescuda. E está proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-askde": "\"$1\" é unha propiedade predefinida que informa sobre a profundidade (valor numérico calculado na base dun aniñamento de consultas e  a resolución de cadeas de propiedade) dunha consulta, e é proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-long-askde": "É un valor numérico calculado en base ó aniñamento de subconsultas, cadeas de propiedades, e os elementos de descrición dispoñibles coa execución dunha consulta baseada no parámetro de configuración <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "«$1» é unha propiedade predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica], que describe parámetros que inflúen no resultado dunha consulta.", "smw-property-predefined-long-askpa": "É parte dunha colección de propiedades que especifican un [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler perfil de consulta].", "smw-sp-properties-docu": "Esta páxina lista as [https://www.semantic-mediawiki.org/wiki/Property propiedades] e o seu número de usos dispoñible para este wiki. Para ver as estatísticas actualizadas recoméndase que o script de mantemento das [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics estatísticas das propiedades] se execute de forma regular. Para ver unha vista diferenciada, consulte as páxinas especiais das [[Special:UnusedProperties|propiedades sen uso]] ou as [[Special:WantedProperties|propiedades requiridas]].", "smw-sp-properties-cache-info": "Os datos da lista obtivéronse da [https://www.semantic-mediawiki.org/wiki/Caching caché], e actualizáronse o $1.", "smw-sp-properties-header-label": "Lista de propiedades", "smw-admin-settings-docu": "Amosa unha lista de tódolos parámetros predeterminados e localizados relevantes para o contorno de Semantic MediaWiki. Para obter os detalles de configuración individuais, consulte a páxina de axuda sobre a [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuración].", "smw-sp-admin-settings-button": "Xerar a lista de opcións", "smw-admin-idlookup-title": "Consulta", "smw-admin-idlookup-docu": "Solicita os detalles sobre un identificador interno de obxecto que representa unha entidade individual (páxina wiki, subobxecto, etc.) de MediaWiki Semántica. O identificador non debe confundirse co identificador da páxina de MediaWiki ou co identificador de revisión.", "smw-admin-iddispose-title": "Eliminación", "smw-admin-iddispose-docu": "Teña en conta que a operación de eliminación non ten restricións e eliminará unha entidade do motor de almacenamento, xunto con todas as súas referencias nas táboas dependentes, se se confirma. Realice esta tarefa con '''precaución''' e só despois de consultar a [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentación].", "smw-admin-iddispose-done": "O ID \"$1\" foi eliminado do servidor de almacenamento.", "smw-admin-iddispose-references": "Identificador \"$1\" con polo menos un rexistro activo de referencia de táboa.", "smw-admin-iddispose-references-multiple": "Lista de correspondencias con polo menos un rexistro de referencia activo.", "smw-admin-iddispose-no-references": "O identificador \"$1\" non foi atopado ou non contiña ningunha referencia.", "smw-admin-idlookup-input": "Procura:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Resumo", "smw-admin-tab-notices": "Avisos de obsolescencia", "smw-admin-tab-maintenance": "Mantemento", "smw-admin-tab-supplement": "Funcións suplementarias", "smw-admin-tab-registry": "<PERSON><PERSON><PERSON>", "smw-admin-maintenance-no-description": "Sen descrición.", "smw-livepreview-loading": "Cargando...", "smw-sp-searchbyproperty-description": "Esta páxina proporciona unha [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces interfase de navegación] simple para atopar entidades descritas por unha propiedade e un valor nomeado. Outras interfases de busca dispoñibles inclúen a [[Special:PageProperty|busca de propiedades de páxina ]], e o [[Special:Ask|xerador de consultas ask]].", "smw-sp-searchbyproperty-resultlist-header": "Lista de resultados", "smw-sp-searchbyproperty-nonvaluequery": "<PERSON>ha lista de valores que teñen a propiedade \"$1\" asignada.", "smw-sp-searchbyproperty-valuequery": "Unha lista de páxinas que teñen a propiedade \"$1\" co valor \"$2! anotado.", "smw-datavalue-number-textnotallowed": "\"$1\" non pode asignarse a un tipo de número declarado con valor $2.", "smw-datavalue-number-nullnotallowed": "\"$1\" devolveu \"NULL\" que non está permitido como número.", "smw-editpage-annotation-enabled": "Esta páxina permite anotacións semánticas no texto (p. ex. <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) para crear contido estruturado e consultable proporcionado para a MediaWiki Semántica. Para unha descrición detallada de como utilizar as anotacións ou a función de análise #ask consulte as páxinas de axuda [https://www.semantic-mediawiki.org/wiki/Help:Getting_started primeiros pasos], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation anotación no texto] ou [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries consultas no texto].", "smw-editpage-annotation-disabled": "Esta páxina non permite incluír anotacións semánticas no texto por restriccións do espazo de nomes. Ten máis detalles de como habilitar o espazo de nomes na páxina de axuda da [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuración].", "smw-editpage-property-annotation-enabled": "Esta propiedade pode ampliarse mediante anotacións semánticas para especificar un tipo de datos (p. ex.,<nowiki>\"[[</nowiki>Ten tipo::Páxina]]\") ou outras declaracións deste tipo (p. ex.,<nowiki>\"[[</nowiki>Subpropiedade de::dc:data]]\"). Para unha descrición sobre como aumentar esta páxina, consulte as páxinas de axuda de [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaración dunha propiedade] ou [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes lista de tipos de datos].<!--[[Has type::Page]], [[Subproperty of::dc:date]]-->", "smw-editpage-property-annotation-disabled": "Esta propiedade non pode ampliarse cunha anotación de tipo de dato (p.ex.<nowiki>\"[[</nowiki>Ten tipo::Páxina]]\") porque xa está predefinida  (vexa a páxina de axuda das [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propiedades especiais] para máis información). <!-- [[Has type::Page]]-->", "smw-editpage-concept-annotation-enabled": "Este concepto pode ampliarse usando a función de análise #concept. Para unha descrición de como usar #concept, consulte a páxina de axuda sobre os [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceptos].", "smw-search-syntax-support": "Nas procuras pódese utilizar a  [https://www.semantic-mediawiki.org/wiki/Help:Semantic_searchsintaxe de consultas semánticas] para axudar a atopar correspondencias usando a MediaWiki Semántica.", "smw-search-input-assistance": "O [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance asistente de entrada] está activado tamén, para permitir unha preselección de entidades dispoñibles.", "smw-search-input": "Entrada e procura", "smw-search-syntax": "Sintaxe", "smw-search-profile": "Extendida", "smw-search-profile-tooltip": "Busca funcións en conexión con MediaWiki Semántica", "smw-search-profile-sort-best": "<PERSON>lor <PERSON>", "smw-search-profile-sort-recent": "<PERSON><PERSON><PERSON>", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-sort": "Especifica unha preferencia de ordenamento para amosar os resultados con:", "smw-search-profile-extended-help-sort-title": "* \"Título\" usando o título da páxina (ou o título en pantalla) como criterio de ordenación", "smw-search-profile-extended-help-sort-best": "* \"Mellor correspondencia\" ordenará os resultados por [https://www.semantic-mediawiki.org/wiki/help:ElasticStore/Relevancy relevancia] baseándose nas puntuacións provistas por Elasticsearch", "smw-search-profile-extended-help-namespace": "A caixa de selección do espazo de nomes estará oculta en canto se seleccione un formulario, pero pode facerse visible de novo coa axuda do botón \"amosar/ocultar\".", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> para non coincidir con ningunha entidade que inclúa \"...\"", "smw-search-profile-extended-help-query": "<code><nowiki>$1</nowiki></code> usado como consulta.", "smw-search-profile-extended-help-query-link": "(Para máis detalles $1).", "smw-search-profile-extended-help-find-forms": "formularios dispoñibles", "smw-search-profile-extended-section-sort": "Ordenar por", "smw-search-profile-extended-section-form": "Formularios", "smw-search-profile-extended-section-search-syntax": "Entrada de procura", "smw-search-profile-extended-section-namespace": "Espazo de nomes", "smw-search-profile-extended-section-query": "Consulta", "smw-search-profile-link-caption-query": "xerador de consultas", "smw-search-show": "<PERSON><PERSON>", "smw-search-hide": "<PERSON><PERSON><PERSON><PERSON>", "log-name-smw": "Rexisto de MediaWiki Semántica", "log-show-hide-smw": "Rexistro de MediaWiki Semántica $1", "logeventslist-smw-log": "Rexisto de MediaWiki Semántica", "log-description-smw": "Actividades dos [https://www.semantic-mediawiki.org/wiki/Help:Logging tipos de evento habilitados]  que foron informados pola MediaWiki Semántica e os seus compoñentes.", "logentry-smw-maintenance": "Eventos relacionados co mantemento emitidos por MediaWiki Semántica", "smw-datavalue-import-unknown-namespace": "O espazo de nomes de importación \"$1\" é descoñecido. Por favor, asegúrese que os detalles de importación OWL están dispoñibles mediante [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Non se puido atopar un URI do espazo de nomes de \"$1\" na [[MediaWiki:Smw import $1|importación de $1]].", "smw-datavalue-import-missing-type": "Non se atopou definición de tipo para \"$1\" na [[MediaWiki:Smw import $2|importación de $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|importación de $1]]", "smw-datavalue-import-invalid-value": "\"$1\" non é un formato válido, debería ter o formato \"espazo de nome\":\"identificador\" (p.ex. \"foaf:nome\").", "smw-datavalue-import-invalid-format": "A cadea \"$1\" debía estar dividida en catro partes pero o formato non é correcto.", "smw-property-predefined-impo": "\"$1\" é unha propiedade predefinida que describe a relación cun [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabulario importado]. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-type": "\"$1\" é unha propiedade predefinida que describe o [[Special:Types|tipo de datos]] dunha propiedade. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-sobj": "\"$1\" é unha propiedade predefinida que representa un construtor [https://www.semantic-mediawiki.org/wiki/Help:Container contedor]. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-long-sobj": "O contedor permite acumular asignacións dos valores de propiedade do mesmo xeito a como se fai nunha páxina wiki normal pero cun espazo de entidades diferente que será ligado ó suxeito incorporado.", "smw-property-predefined-errp": "\"$1\" é unha propiedade predefinida que rastrexa erros de entrada na procura de valores de anotación incorrectos. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-long-errp": "Na maioría dos casos está provocado por un erro de correspondencia de tipos ou unha restrición de [[Property:Allows value|valore]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value\"$1\"] é unha propiedade predefinida que pode definir unha lista de valores posibles para restrinxir a asignación de valores dunha propiedade. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list \"$1\"] é unha propiedade predefinida que pode especificar unha referencia a unha lista que contén valores permitidos para restrinxir as asignacións de valor para unha propiedade, é proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-datavalue-property-restricted-annotation-use": "A propiedade «$1» ten unha zona de aplicación limitada e os usuarios non poden utilizala como propiedade de anotación.", "smw-datavalue-property-restricted-declarative-use": "A propiedade «$1» é unha propiedade declarativa e só pode utilizarse en páxinas de propiedade ou de categoría.", "smw-datavalue-property-create-restriction": "A propiedade \"$1\" non existe e o usuario non ten o permiso \"$2\" (consulte [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade]) para crear ou anotar valores cunha propiedade non aprobada.", "smw-datavalue-property-invalid-character": "«$1» contén un caracter listado «$2» como unha parte da etiqueta de propiedade e  polo tanto foi clasificado como non válido.", "smw-datavalue-property-invalid-chain": "O uso de «$1» como unha cadea de propiedade non está permitido durante o proceso de anotación.", "smw-datavalue-restricted-use": "O valor de datos \"$1\" foi marcado para uso restrinxido.", "smw-datavalue-invalid-number": "Non se pode interpretar \"$1\" coma un número.", "smw-query-condition-circular": "Detectouse unha posible condición circular en \"$1\".", "smw-query-condition-empty": "A descrición da consulta ten unha condición baleira.", "smw-types-list": "Lista de tipos de datos", "smw-types-default": "\"$1\" é un tipo de datos predefinido.", "smw-types-help": "Máis información e exemplos poden atoparse nesta [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 páxina de axuda].", "smw-type-anu": "\"$1\" é unha variante do tipo de datos [[Special:Types/URL|URL]] que se usa principalmente para declaración de exportación de ''owl:AnnotationProperty''.", "smw-type-boo": "\"$1\" é un tipo de dato básico para describir un valor certo/falso.", "smw-type-cod": "\"$1\" é unha variante do tipo de dato  [[Special:Types/Text|Texto]] que é usada para textos técnicos de lonxitude variable, como listados de código fonte.", "smw-type-geo": "\"$1\" é un tipo de dato que describe localizacións xeográficas e require a [https://www.semantic-mediawiki.org/wiki/Extension:Maps extensión \"Maps\"] para ofrecer máis funcións.", "smw-type-tel": "\"$1\" é un tipo de datos especial para describir números de teléfono internacionais segundo o RFC 3966.", "smw-type-txt": "\"$1\" é un tipo de datos básico para describir cadeas de texto de lonxitude variable.", "smw-type-dat": "\"$1\" é un tipo de dato básico para representar puntos no tempo nun formato unificado.", "smw-type-ema": "\"$1\" é un tipo de datos especial para representar un correo electrónico.", "smw-type-tem": "\"$1\" é un tipo de datos numérico especial para representar unha temperatura.", "smw-type-qty": "\"$1\" é un tipo de datos para describir cantidades con unha representación numérica e unha unidade de medida.", "smw-type-rec": "\"$1\" é un tipo de datos contedor que especifica unha lista de propiedades con tipos, nunha orde fixa.", "smw-type-extra-tem": "O esquema de conversión inclúe as unidades soportadas, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Fahrenheit e Rankine.", "smw-type-tab-properties": "Propiedades", "smw-type-tab-types": "Tipos", "smw-type-tab-errors": "<PERSON><PERSON><PERSON>", "smw-type-primitive": "Básico", "smw-type-contextual": "Contextual", "smw-type-compound": "Composto", "smw-type-container": "<PERSON><PERSON><PERSON>", "smw-type-no-group": "<PERSON> clas<PERSON>", "smw-special-pageproperty-description": "Esta páxina proporciona unha interface de navegación para atopar tódolos valores dunha propiedade nunha páxina dada. Outras interfaces de procura dispoñibles inclúen a [[Special:SearchByProperty|procura de propiedades]] e o [[Special:Ask|construtor de preguntas]].", "smw-property-predefined-errc": "\"$1\" é unha propiedade predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] e representa erros que aparecen en conexión con anotacións de valores impropios ou do procesamento da entrada.", "smw-property-predefined-long-errc": "Os erros son recollidos nun [https://www.semantic-mediawiki.org/wiki/Help:Container contedor] que pode incluír unha referencia á propiedade que causou o problema.", "smw-property-predefined-errt": "\"$1\" é unha propiedade predefinida que contén unha descrición textual dun erro. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-subobject-parser-invalid-naming-scheme": "Un subobxecto definido polo usuario contiña un esquema de nomeado incorrecto. O uso dun punto ($1) dentro dos primeiros cinco caracteres está reservado para as extensións. Vostede pode definir un [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier identificador nomeado].", "smw-datavalue-record-invalid-property-declaration": "A definición de rexistro contén a propiedade \"$1\" que está declarada como tipo rexistro e iso non está permitido.", "smw-property-predefined-mdat": "\"$1\" é unha propiedade predefinida que corresponde á data da última modificación dun asunto. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-cdat": "\"$1\" é unha propiedade predefinida que corresponde á data da primeira modificación dun asunto. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-newp": "\"$1\" é unha propiedade predefinida que indica se un asunto é novo ou non. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-ledt": "\"$1\" é unha propiedade predefinida que contén o nome da páxina do usuario que fixo a última modificación. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-mime": "\"$1\" é unha propiedade predefinida que describe o tipo MIME dun ficheiro subido. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-media": "\"$1\" é unha propiedade predefinida que describe o tipo multimedia dun ficheiro subido. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-askfo": "\"$1\" é unha propiedade predefinida que contén o nome do formato de resultado usado nunha consulta. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-askst": "\"$1\" é unha propiedade predefinida que describe as condicións da consulta como unha cadea. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-askdu": "\"$1\" é unha propiedade predefinida que contén o valor de tempo (en segundos) que a consulta requiriu para completar a súa execución. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "«$1» é unha propiedade predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica], que identifica fontes de consulta alternativas (por exemplo, fontes remotas ou federadas).", "smw-property-predefined-askco": "\"$1\" é unha propiedade predefinida proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] para describir o estado dunha consulta ou dos seus compoñentes.", "smw-property-predefined-long-askco": "O número ou números asignados representan un estado codificado interno que está explicado na [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler p<PERSON><PERSON><PERSON> de axuda].", "smw-property-predefined-prec": "\"$1\" é unha propiedade predefinida que describe unha [https://www.semantic-mediawiki.org/wiki/Help:Display_precision precisión de mostra] (en díxitos decimais) para tipos de datos numéricos.", "smw-types-extra-geo-not-available": "A [https://www.semantic-mediawiki.org/wiki/Extension:Maps extensión \"Maps\"] non foi detectada polo que \"$1\" está restrinxido nas súas capacidades de funcionamento.", "smw-datavalue-monolingual-dataitem-missing": "Falta un elemento esperado para construír un valor composto monolingüe.", "smw-datavalue-languagecode-missing": "Para a anotación \"$1\", o analizador non foi capaz de determinar un código de lingua (p.ex. \"foo@en\").", "smw-datavalue-languagecode-invalid": "Non se recoñece \"$1\" como un código de lingua admitido.", "smw-property-predefined-lcode": "\"$1\" é unha propiedade predefinida que representa un código de lingua con formato BCP47. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-type-mlt-rec": "\"$1\" é un tipo de datos [https://www.semantic-mediawiki.org/wiki/Help:Container contedor] que asocia un valor de texto cun [[Property:Language code|código de lingua]] específico.", "smw-types-extra-mlt-lcode": "O tipo de datos{{PLURAL:$2|require|non require}} un código de lingua (ou sexa, {{PLURAL:$2|non se acepta| acéptase}} unha anotación de valor sen un código de lingua).", "smw-property-predefined-text": "\"$1\" é unha propiedade predefinida que representa texto de lonxitude arbitraria. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-pdesc": "\"$1\" é unha propiedade predefinida que permite describir unha propiedade en contexto dun idioma. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "\"$1\" é unha propiedade predefinida para definir unha lista de propiedades usadas cunha propiedade de tipo [[Special:Types/Record|rexistro]]. É  proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Tempo de analise de notas en texto", "smw-limitreport-intext-postproctime": "[SMW] duración de post-procesamento", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Tempo de actualización de almacenamento (na purga de páxinas)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw_allows_pattern": "Esta páxina debería conter unha lista de referencias (seguidas por [https://gl.wikipedia.org/wiki/Expresi%C3%B3n_regular expresións regulares]) para ser postas a disposición vía a propiedade [[Property:Allows pattern|Permitir as modificacións]]. Para editar esta páxina é preciso o permiso <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "A expresión regular \"$2\" clasificou a \"$1\" como non válida.", "smw-datavalue-allows-pattern-reference-unknown": "A referencia de patrón \"$1\" non pode corresponderse cunha entrada en [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "A referencia de lista \"$1\" non se corresponde cunha páxina de [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "O contido da lista \"$1\" non contén elementos co marcador de lista *.", "smw-datavalue-feature-not-supported": "A funcionalidade \"$1\" non é compatible ou foi desactivada neste wiki.", "smw-property-predefined-pvap": "\"$1\" é unha propiedade predefinida que pode especificar unha [[MediaWiki:Smw allows pattern|referencia de patrón]] para facer coincidir [https://gl.wikipedia.org/wiki/Expresi%C3%B3n_regular expresións regulares]. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-dtitle": "\"$1\" é unha propiedade predefinida que pode asignar un título de visualización diferente a unha entidade. É proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-pvuc": "\"$1\" é unha propiedade predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica], que restrinxe as asignacións de valores para cada instancia a valores únicos (ou un polo menos).", "smw-property-predefined-long-pvuc": "A unicidade está establecida cando dous valores non son iguais na súa representación literal, e toda violación desta constante será considerada coma un erro.", "smw-datavalue-constraint-uniqueness-violation": "A propiedade \"$1\" só permite a asignación de valores únicos e \"$2\" xa está anotado no campo \"$3\".", "smw-datavalue-constraint-uniqueness-violation-isknown": "A propiedade ''$1'' só permite anotacións de valores únicos, ''$2'' xa contén un valor asignado. ''$3'' viola a restrición de exclusividade.", "smw-property-predefined-boo": "\"$1\" é un [[Special:Types/Boolean|tipo]] e propiedade predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] para representar valores booleanos.", "smw-property-predefined-num": "\"$1\" é un [[Special:Types/Number|tipo]] e propiedade predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] para representar valores numéricos.", "smw-property-predefined-dat": "\"$1\" é un [[Special:Types/Date|tipo]] e propiedade predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] para representar valores de datas.", "smw-property-predefined-uri": "\"$1\" é un [[Special:Types/URL|tipo]] e propiedade predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] para representar valores de URI/URL.", "smw-property-predefined-qty": "\"$1\" é un [[Special:Types/Quantity|tipo]] e propiedade predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] para representar valores de cantidade.", "smw-datavalue-time-invalid-offset-zone-usage": "\"$1\" contén un desprazamento e un identificador de zona que non está soportado.", "smw-datavalue-time-invalid-values": "O valor \"$1\" contén información non interpretable na forma \"$2\".", "smw-datavalue-time-invalid-date-components-common": "\"$1\" contén algunha información non interpretable.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\" contén un guión innecesario ou outros caracteres que non son válidos para a interpretación dunha data.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" contén algúns componentes baleiros.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" contén máis de tres componentes, que son necesarios para a interpretación dunha data.", "smw-datavalue-time-invalid-date-components-sequence": "\"$1\" contén unha secuencia que non puido interpretarse contra unha matriz de correspondencia dispoñible para os compoñentes dunha data.", "smw-datavalue-time-invalid-ampm": "\"$1\" contén \"$2\" como un elemento horario que é inválido para o formato convencional de 12 horas.", "smw-datavalue-time-invalid-jd": "Imposible interpretar o valor de entrada \"$1\" como un número válido JD (día xuliano) con \"$2\" sendo informado.", "smw-datavalue-time-invalid-prehistoric": "Imposible interpretar un valor de entrada prehistórico \"$1\". Por exemplo, se especifica máis que os anos nun modelo de calendario pode devolver resultados incorrectos nun contexto prehistórico.", "smw-datavalue-time-invalid": "Imposible interpretar o valor da entrada \"$1\" como unha data ou un compoñente de tempo válidos con \"$2\" sendo informado.", "smw-datavalue-external-formatter-uri-missing-placeholder": "Falta o espazo reservado \"$1\" no URI do formatador.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" é un enderezo URL non válido.", "smw-datavalue-external-identifier-formatter-missing": "<PERSON> propiedade fáltalle a atribución dun [[Property:External formatter uri|\"formatador externo de URI\"]].", "smw-datavalue-keyword-maximum-length": "A palabra chave superou a lonxitud máxima de $1 {{PLURAL:$1|carácter|caracteres}}", "smw-property-predefined-eid": "\"$1\" é un [[Special:Types/External identifier|tipo]] e propiedade predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] para representar identificadores externos.", "smw-property-predefined-peid": "\"$1\" é unha propiedade predefinida que especifica un identificador externo. E está proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica].", "smw-property-predefined-pefu": "\"$1\" é unha propiedade predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica] para especificar un recurso externo cun espazo reservado.", "smw-property-predefined-long-pefu": "O URI espérase que conteña un parámetro posicional que será axustado cun valor dun [[Special:Types/External identifier|external identifier|identificador externo]] para formar unha referencia de recurso válida.", "smw-type-eid": "\"$1\" é unha variante do tipo de dato [[Special:Types/Text|Texto]] para describir recursos externos (baseados en URIs) e require propriedades particulares para declarar un [[Property:External formatter uri|URI de formatador externo]].", "smw-property-predefined-keyw": "«$1» é unha propiedade predefina e un [[Special:Types/Keyword|tipo]], proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica], que normaliza un texto e ten unha lonxitude de caracteres reducida.", "smw-type-keyw": "«$1» é unha variante do tipo de dato [[Special:Types/Text|texto]] que ten unha lonxitude de caracteres limitada e normaliza a representación do seu contido.", "smw-datavalue-stripmarker-parse-error": "O valor indicado \"$1\" contén [https://en.wikipedia.org/wiki/Help:Strip_markers marcadores de banda] e polo tanto non pode ser analizado abondo.", "smw-datavalue-parse-error": "O valor dado \"$1\" non foi entendido.", "smw-datavalue-propertylist-invalid-property-key": "A lista de propiedades \"$1\" contén unha clave de propiedade \"$2\" non válida.", "smw-datavalue-type-invalid-typeuri": "O tipo \"$1\" non pode ser transformado nunha representación URI válida.", "smw-datavalue-wikipage-missing-fragment-context": "O valor de entrada da wikipáxina \"$1\" non ser usado sen unha páxina de contexto.", "smw-datavalue-wikipage-invalid-title": "O valor de entrada do tipo de páxina \"$1\" contén caracteres non válidos ou está incompleta e polo tanto pode provocar resultados inesperados nunha busca ou nun proceso de anotación.", "smw-datavalue-wikipage-property-invalid-title": "A propiedade \"$1\" (como o tipo de páxina) co valor de entrada \"$2\" contén caracteres incorrectos ou está incompleta, e por iso pode producir resultados inesperados durante unha consulta ou nun proceso de anotación.", "smw-datavalue-wikipage-empty": "O valor de entrada da wikipáxina está baleiro (p.ex. <code>[[SomeProperty::]], [[]]</code>) e non pode usarse como nome ou como parte dunha condición de busca.", "smw-type-ref-rec": "\"$1\" é un tipo de [https://www.semantic-mediawiki.org/wiki/Container contedor] que permite gardar información adicional (p.ex. procedencia dos datos) sobre a asignación dun valor.", "smw-datavalue-reference-outputformat": "$1: $2", "smw-datavalue-reference-invalid-fields-definition": "O tipo [[Special:Types/Reference|Referencia]] espera unha lista de propiedades para ser declarado usando a propiedade [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Ten campos].", "smw-parser-invalid-json-format": "O analizador de JSON devolveu un \"$1\".", "smw-property-preferred-label-language-combination-exists": "\"$1\" non pode usarse como etiqueta preferida por a lingua \"$2\" xa está asignada á etiqueta \"$3\".", "smw-clipboard-copy-link": "Copiar ligazón no portapapeis", "smw-property-userdefined-fixedtable": "\"$1\" estaba configurada como [https://www.semantic-mediawiki.org/wiki/Fixed_properties propiedade fixa] e calquera modificación da [https://www.semantic-mediawiki.org/wiki/Type_declaration declaración do seu tipo] precisa executar <code>setupStore.php</code> ou completar a tarefa especial [[Special:SemanticMediaWiki|\"Instalación e actualización da base de datos\"]].", "smw-data-lookup": "A recuperar os datos...", "smw-data-lookup-with-wait": "A solicitude está a procesarse e pode levar algo de tempo.", "smw-no-data-available": "Non hai datos dispoñibles.", "smw-property-req-violation-missing-fields": "<PERSON> propiedade «$1» fáltanlle detalles de declaración para o tipo anotado «$2» ó non definir a propiedade <code>Ten campos</code>.", "smw-property-req-violation-missing-formatter-uri": "<PERSON> propiedade «$1» fáltanlle detalles de declaración para o tipo anotado porque non foi defina a propiedade <code>URI do formatador externo</code>.", "smw-property-req-violation-predefined-type": "A propiedade \"$1\" é unha propiedade predefinida que contén unha declaración de tipo \"$2\" que é incompatible co tipo por defecto desta propiedade.", "smw-property-req-violation-import-type": "Detectouse unha declaración de tipo que non é compatible co tipo predefinido do vocabulario importado \"$1\". En xeral, non é necesario declarar un tipo porque a información obtense da definición da importación.", "smw-property-req-violation-change-propagation-locked-error": "A propiedade «$1» foi alterada e precisa que as entidades asignadas sexan reavaliadas usando un proceso de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagación de cambios]. A páxina da propiedade foi protexida ata que a actualización de especificación primaria estea completada para previr interrupcións intermedias ou especificacións contraditorias. O proceso pode levar un momento antes de que a páxina poida ser desprotexida, isto dependerá do tamaño e a frecuencia do planificador da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cola de traballo] do planificador.", "smw-category-invalid-value-assignment": "\"$1\" non é recoñecido como unha categoría válida ou unha anotación válida.", "protect-level-smw-pageedit": "Permitir unicamente usuarios co permiso de edición de páxinas (Semantic MediaWiki)", "smw-create-protection": "A creación da propiedade «$1» está restrinxida ós usuarios que conten co dereito «$2» axeitado (ou pertenzan ó [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuarios]), mentres estea activado o [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade].", "smw-create-protection-exists": "As modificacións á propiedade «$1» están restrinxidas ós usuarios que conten co dereito «$2» axeitado (ou pertenzan ó [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuarios]), mentres estea activado o [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade].", "smw-edit-protection": "Esta páxina está [[Property:Is edit protected|protexida]] para impedir a modificación accidental de datos e só pode ser editada por usuarios co dereito de edición (\"$1\") ou que estean no [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuarios] axeitado.", "smw-edit-protection-disabled": "A protección contra edicións foi desactivada, polo tanto \"$1\" non pode ser usada para protexer páxinas de entidades contra edicións non autorizadas.", "smw-edit-protection-auto-update": "Semantic MediaWiki actualizou o estado de protección de acordo coa propiedade «Ten protección de modificación».", "smw-edit-protection-enabled": "Protexido contra modificación (Semantic MediaWiki)", "smw-patternedit-protection": "Esta páxina está protexida e só pode ser editada por usuarios co <code>smw-patternedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions permiso] axeitado.", "smw-property-predefined-edip": "\"$1\" é unha propiedade predefinida proporcionada por  [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] para indicar se a modificación está protexida ou non.", "smw-property-predefined-long-edip": "A<PERSON>da que calquera usuario pode engadir esta propiedade a un tema, só aqueles que posúan un privilexio especial poden editar ou revogar a protección dunha entidade despois de que lle sexa engadida.", "smw-query-reference-link-label": "Referencia de consulta", "smw-format-datatable-emptytable": "Non hai datos dispoñibles na táboa", "smw-format-datatable-info": "Amosando _START_ to _END_ of _TOTAL_ entradas", "smw-format-datatable-infoempty": "Amosando de 0 a 0 de 0 entradas", "smw-format-datatable-infofiltered": "(filtradas dun _MAX_ entradas do total)", "smw-format-datatable-lengthmenu": "Amosar _MENU_ entradas", "smw-format-datatable-loadingrecords": "Cargando...", "smw-format-datatable-processing": "Procesando...", "smw-format-datatable-search": "Procurar:", "smw-format-datatable-zerorecords": "Non se atoparon rexistros que coincidisen", "smw-format-datatable-first": "<PERSON><PERSON>", "smw-format-datatable-last": "Último", "smw-format-datatable-next": "Se<PERSON><PERSON>", "smw-format-datatable-previous": "Anterior", "smw-format-datatable-sortascending": ": activar para ordenar a columna ascendentemente", "smw-format-datatable-sortdescending": ": activar para ordenar a columna descendentemente", "smw-format-datatable-toolbar-export": "Exportar", "smw-category-invalid-redirect-target": "A categoría «$1» contén un obxectivo de redirección non válido a un espazo de nomes que non é de categoría.", "smw-parser-function-expensive-execution-limit": "A función do analizador sintáctico alcanzou o tempo límite autorizado para a súa execución (vexa o parámetro de configuración [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "MediaWiki Semántica lanzará unha actualización desta páxina porque son precisos algúns procesamentos antes da consulta.", "apihelp-smwinfo-summary": "Módulo da API para obter información sobre estatísticas e outra meta información do MediaWiki Semántico.", "apihelp-ask-summary": "Módulo da API para consultar a MediaWiki Semántica usando a linguaxe de preguntas.", "apihelp-askargs-summary": "Módulo da API para realizar consultas en MediaWiki Semántica mediante a lenguaxe Ask en forma de lista de condicións, visualizacións e parámetros.", "apihelp-browsebyproperty-summary": "<PERSON><PERSON><PERSON>lo da API para obter información sobre unha propiedade ou lista de propiedades.", "apihelp-browsebysubject-summary": "Módulo da API para obter informaións sobre un tema.", "apihelp-smwtask-summary": "Módulo da API para executar tarefas relacionadas co MediaWiki Semántico.", "apihelp-smwbrowse-summary": "Módulo da API de MediaWiki Semántica para engadir compatibilidade a certas actividades de navegación.", "smw-api-invalid-parameters": "Parámetros non válidos: \"$1\"", "smw-property-page-list-count": "{{PLURAL:$1|Móstrase $1 páxina que utiliza|Móstranse $1 páxinas que utilizan}} esta propiedade.", "smw-property-page-list-search-count": "{{PLURAL:$1|Móstrase $1 páxina que utiliza|Móstranse $1 páxinas que utilizan}} esta propiedade cunha correspondencia de valor «$2».", "smw-property-reserved-category": "Categoría", "smw-category": "Categoría", "smw-datavalue-uri-invalid-scheme": "Non se incluíu «$1» nos esquemas de URI válidos.", "smw-browse-property-group-title": "Grupo de propiedades", "smw-browse-property-group-label": "Etiqueta do grupo de propiedades", "smw-browse-property-group-description": "Descrición do grupo de propiedades", "smw-property-predefined-ppgr": "«$1» é unha propiedade predefinida, proporcionada pola [https://www.semantic-mediawiki.org/wiki/Help:Special_properties MediaWiki Semántica], que identifica entidades (categorías, principalmente) empregadas como instancias de agrupamento para as propiedades.", "smw-filter": "Filtro", "smw-section-expand": "Expandir a sección", "smw-section-collapse": "Contraer a sección", "smw-ask-format-help-link": "Formato [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "Axuda", "smw-cheat-sheet": "Folla de referencia", "smw-personal-jobqueue-watchlist": "Tarefas pendentes da lista de vixilancia", "smw-property-predefined-label-skey": "Chave de ordenación", "smw-processing": "Procesando...", "smw-loading": "Cargando...", "smw-preparing": "Preparando...", "smw-expand": "Expandir", "smw-collapse": "<PERSON><PERSON><PERSON>", "smw-copy": "Copiar", "smw-copy-clipboard-title": "Copia o contido ó portapapeis", "smw-jsonview-expand-title": "Expande a vista JSON", "smw-jsonview-collapse-title": "Contrae a vista JSON", "smw-redirect-target-unresolvable": "O destino é irresoluble polo motivo «$1»", "smw-types-title": "Tipo: $1", "smw-schema-error-title": "{{PLURAL:$1|Erro|<PERSON>rros}} de validación.", "smw-schema-title": "Esquema", "smw-schema-type": "T<PERSON><PERSON> de <PERSON>", "smw-schema-tag": "{{PLURAL:$1|Etiqueta|Etiquetas}}", "smw-ask-title-keyword-type": "Procura de palabras clave", "smw-ask-message-keyword-type": "Esta procura coincide coa condición <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Non foi posible establecer conexión co destino remoto \"$1\".", "smw-remote-source-disabled": "A fonte '''$1''' desactivou as solicitudes remotas.", "smw-remote-source-unmatched-id": "A fonte '''$1''' non concorda coa versión de MediaWiki Semántica que pode soportar solicitudes remotas.", "smw-remote-request-note": "O resultado recupérase a partir da fonte remota '''$1''' e é probable que o contido xerado conteña información non dispoñible na wiki actual.", "smw-parameter-missing": "Falta o parámetro \"$1\".", "smw-property-tab-usage": "<PERSON><PERSON>", "smw-property-tab-redirects": "Sinónimos", "smw-property-tab-subproperties": "Subpropiedades", "smw-property-tab-errors": "Asignacións incorrectas", "smw-property-tab-specification": "... mais", "smw-concept-tab-list": "Lista", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-ask-tab-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-extra": "Extras", "smw-ask-tab-debug": "Depuración", "smw-ask-tab-code": "Código", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "{{PLURAL:$1|Móstrase <strong>1</strong> resultado|Móstranse <strong>$1</strong> resultados}}, comezando polo número <strong>$2</strong>."}