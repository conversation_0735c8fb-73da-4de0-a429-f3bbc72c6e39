{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://www.semantic-mediawiki.org/wiki/Help:Schema/Type/PROPERTY_CONSTRAINT_SCHEMA", "type": "object", "title": "Property constraint validation schema", "required": ["type", "constraints"], "properties": {"type": {"$id": "#/properties/type", "type": "string", "enum": ["PROPERTY_CONSTRAINT_SCHEMA"], "title": "Schema type", "default": "PROPERTY_CONSTRAINT_SCHEMA"}, "title_prefix": {"$id": "#/properties/title_prefix", "type": "string", "enum": ["Constraint"], "title": "Title prefix"}, "tags": {"$id": "#/properties/tags", "type": "array", "title": "tags", "default": null, "items": {"$id": "#/properties/tags/items", "type": "string", "title": "tags, keywords etc.", "default": "", "pattern": "^(.*)$"}}, "constraints": {"$id": "#/properties/constraints", "type": "object", "title": "constraint rules", "minProperties": 1, "propertyNames": {"pattern": "^[A-Za-z_][A-Za-z0-9_]*$"}, "properties": {"allowed_namespaces": {"$ref": "#/definitions/allowed_namespaces"}, "unique_value_constraint": {"$ref": "#/definitions/unique_value_constraint"}, "single_value_constraint": {"$ref": "#/definitions/single_value_constraint"}, "custom_constraint": {"$ref": "#/definitions/custom_constraint"}, "non_negative_integer": {"$ref": "#/definitions/non_negative_integer"}, "must_exists": {"$ref": "#/definitions/must_exists"}}, "additionalProperties": false}}, "definitions": {"allowed_namespaces": {"$id": "#/definitions/allowed_namespaces", "type": "array", "title": "Specifies allowed namespaces", "minItems": 1, "items": {"type": "string", "title": "namespace", "default": "", "pattern": "^(NS_|SMW_NS_)(.*)$"}}, "unique_value_constraint": {"$id": "#/definitions/unique_value_constraint", "type": "boolean", "title": "Specifies that values should be unique across the wiki, that the value is likely to be different (distinct) from all other items", "default": false}, "single_value_constraint": {"$id": "#/definitions/single_value_constraint", "type": "boolean", "title": "Specifies that the property expects only a single value per entity", "default": false}, "custom_constraint": {"$id": "#/definitions/custom_constraint", "type": "object", "title": "Specifies custom constraints to be implemented by a user"}, "non_negative_integer": {"$id": "#/definitions/non_negative_integer", "type": "boolean", "title": "Specifies that values are derived from integer with the minimum inclusive to be 0", "default": false}, "must_exists": {"$id": "#/definitions/must_exists", "type": "boolean", "title": "Specifies that the annotated value must exists to be valid", "default": false}}}