{"@metadata": {"authors": ["555", "Agripinoduarte", "Amgauna", "Athena in Wonderland", "Cainamarques", "Dr03ramos", "<PERSON>", "Eduardo.mps", "Eduardoad<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "GKnedo", "Giro720", "<PERSON>", "He7d3r", "Helder.wiki", "Heldergeovane", "Jaideraf", "Kghbln", "Leonardo<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Blade", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Nemo bis", "Opraco", "<PERSON>", "Re demz", "Rhcastilhos", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tks4Fish", "Trigonometria87", "Waldir", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"]}, "smw-desc": "<PERSON><PERSON><PERSON> seu wiki mais acessível - para máquinas ''e'' humanos ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentação online])", "smw-error": "Erro", "smw-upgrade-error": "O [https://www.semantic-mediawiki.org/ Semantic MediaWiki] foi instalado e ativado, mas falta uma [https://www.semantic-mediawiki.org/wiki/Help:Upgrade chave de atualização] apropriada.", "smw-upgrade-release": "Vers<PERSON>", "smw-upgrade-progress": "Progresso", "smw-upgrade-progress-explain": "É difícil antecipar quando a atualização estará concluída porque depende do tamanho do repositório de dados e do ''hardware'' disponível, e pode demorar algum tempo para concluir a atualização de wikis grandes.\n\nPara obter mais informações acerca do progresso da atualização, contacte o seu administrador local, por favor.", "smw-upgrade-progress-create-tables": "<PERSON><PERSON><PERSON> (ou atualizando) tabelas e índices...", "smw-upgrade-progress-post-creation": "Executando tarefas de pós-criação...", "smw-upgrade-progress-table-optimization": "Executando otimização de tabelas...", "smw-upgrade-progress-supplement-jobs": "Adicionando tarefas suplementares...", "smw-upgrade-error-title": "Erro » Semantic MediaWiki", "smw-upgrade-error-why-title": "Por que eu vejo esta página?", "smw-upgrade-error-why-explain": "A estrutura interna do banco de dados do Semantic MediaWiki foi alterada e necessita alguns ajustes para estar totalmente funcional. Pode haver vários motivos incluindo: \n* Propriedades fixas adicionais (requer configuração de tabelas) foram adicionadas\n* Uma atualização contém algumas mudanças nas tabelas ou índices tornando uma interceptação obrigatória antes de acessar os dados\n* Alterações no mecanismo de armazenamento ou consulta", "smw-upgrade-error-how-title": "Como corrigir este erro?", "smw-upgrade-error-how-explain-admin": "Um administrador (ou qualquer pessoa com privilégios de administrador) tem de executar o ''script'' de manutenção [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] do MediaWiki ou o [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] do Semantic MediaWiki.", "smw-upgrade-error-how-explain-links": "Para obter mais ajuda também pode consultar as seguintes páginas:\n* Instruções de [https://www.semantic-mediawiki.org/wiki/Help:Installation instalação]\n* Página de ajuda sobre [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting resolução de problemas]", "smw-extensionload-error-why-title": "Por que eu vejo esta página?", "smw-extensionload-error-why-explain": "A extensão <b>não</b> foi carregado usando <code>enableSemantics</code> e, em vez disso, ativado por outros meios, como usar <code>wfLoadExtension( 'SemanticMediaWiki' )</code> diretamente.", "smw-extensionload-error-how-title": "Como corrijo esse erro?", "smw-extensionload-error-how-explain": "Para ativar a extensão e evitar problemas com declarações de espaços nominais e configurações pendentes, é necessário usar <code>enableSemantics</code> que garantirá que as variáveis necessárias sejam definidas antes de carregar a extensão <code>ExtensionRegistry</code>. \n\nPor favor, dê uma olhada na página de ajuda [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] para obter mais assistência.", "smw-upgrade-maintenance-title": "Atualizar e manutenção » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Por que eu vejo esta página?", "smw-upgrade-maintenance-note": "Neste momento o sistema está a sofrer uma [https://www.semantic-mediawiki.org/wiki/Help:Upgrade atualização] da extensão [https://www.semantic-mediawiki.org/ Semantic MediaWiki] e do respetivo repositório de dados, e solicitamos a sua paciência de forma a permitir que a manutenção prossiga antes que a wiki seja novamente tornada acessível.", "smw-upgrade-maintenance-explain": "A extensão tenta minimizar o impacto e o período de indisponibilidade, adiando a maior parte das suas tarefas de manutenção para depois do <code>update.php</code> mas é preciso que algumas alterações relacionadas com a base de dados terminem primeiro para evitar inconsistências de dados. Estas alterações podem incluir: \n* Alteração da estrutura de tabelas, como a adição de campos novos ou a alteração de campos existentes \n* Alteração ou adição de índices sobre tabelas\n* Executar otimizações de tabela (quando ativado)", "smw-semantics-not-enabled": "A funcionalidade do Semantic MediaWiki não foi ativada neste wiki.", "smw_viewasrdf": "Feed RDF", "smw_finallistconjunct": " e", "smw-factbox-head": "... mais sobre \"$1\"", "smw-factbox-facts": "<PERSON><PERSON>", "smw-factbox-facts-help": "Mostra declarações e fatos que foram criados por um usuário", "smw-factbox-attachments": "Anexos", "smw-factbox-attachments-value-unknown": "N/A", "smw-factbox-attachments-is-local": "É local", "smw-factbox-attachments-help": "Mostra anexos disponíveis", "smw-factbox-facts-derived": "<PERSON><PERSON> deriva<PERSON>", "smw-factbox-facts-derived-help": "Mostra fatos que foram derivados de regras ou com a ajuda de outras técnicas de raciocínio", "smw_isspecprop": "Esta propriedade é uma propriedade especial neste wiki.", "smw-concept-cache-header": "Uso do cache", "smw-concept-cache-count": "O [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count cache do conceito] contém {{PLURAL:$1|'''uma''' entidade|'''$1''' entidades}} ($2).", "smw-concept-no-cache": "Sem cache disponível.", "smw_concept_description": "Descrição do conceito \"$1\"", "smw_no_concept_namespace": "Conceitos só podem ser definidos em páginas no domínio Conceito:.", "smw_multiple_concepts": "Cada página de conceito pode ter apenas uma definição de conceito.", "smw_concept_cache_miss": "O conceito \"$1\" não pode ser utilizado neste momento, uma vez que a configuração deste wiki necessita ser refeita off-line.\nCaso o problema não seja resolvido automaticamente dentro de algum tempo, peça a um administrador deste wiki que este conceito seja disponibilizado.", "smw_noinvannot": "Valores não podem ser atribuídos a propriedades inversas.", "version-semantic": "Extensões semânticas", "smw_baduri": "URIs na forma de \"$1\" não são permitidos.", "smw_printername_count": "Contar resultados", "smw_printername_csv": "Exportação em CSV", "smw_printername_dsv": "Exportação em DSV", "smw_printername_debug": "Depurar consulta (para especialistas)", "smw_printername_embedded": "Incorporar o conteúdo da página", "smw_printername_json": "Exportação em JSON", "smw_printername_list": "Lista", "smw_printername_plainlist": "Lista simples", "smw_printername_ol": "Lista numerada", "smw_printername_ul": "Lista com marcadores", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "Tabela ampla", "smw_printername_template": "Predefinição", "smw_printername_templatefile": "Arquivo da predefinição", "smw_printername_rdf": "Exportação em RDF", "smw_printername_category": "Categoria", "validator-type-class-SMWParamSource": "texto", "smw-paramdesc-limit": "Número máximo de resultados na exibição", "smw-paramdesc-offset": "Posição do primeiro resultado", "smw-paramdesc-headers": "Mostrar os nomes dos cabeçalhos/propriedades", "smw-paramdesc-mainlabel": "Nome dado a coluna das páginas principais", "smw-paramdesc-link": "Apresentar os valores na forma de links", "smw-paramdesc-intro": "Texto a apresentar antes dos resultados da consulta, caso existam", "smw-paramdesc-outro": "Texto a apresentar após os resultados da consulta, caso existam", "smw-paramdesc-default": "Texto a apresentar caso não haja resultados para a consulta", "smw-paramdesc-sep": "O separador entre resultados", "smw-paramdesc-propsep": "O separador entre as propriedades da entrada de um resultado", "smw-paramdesc-valuesep": "O separador entre os valores para uma propriedade de um resultado", "smw-paramdesc-showsep": "Exibir separador no começo do arquivo CSV (\"sep=<valor>\")", "smw-paramdesc-distribution": "Em vez de exibir todos os valores, conte suas ocorrências e mostre-as.", "smw-paramdesc-distributionsort": "Alfabete a distribuição do valor pela contagem de ocorrência.", "smw-paramdesc-distributionlimit": "Limite a distribuição do valor para a contagem de apenas alguns valores.", "smw-paramdesc-aggregation": "Especifique a que a agregação deve se relacionar", "smw-paramdesc-template": "Nome da predefinição com a qual são exibidos os resultados", "smw-paramdesc-columns": "O número de colunas nas quais exibir resultados", "smw-paramdesc-userparam": "Valor fornecido a cada chamada da predefinição, se uma predefinição for utilizada", "smw-paramdesc-class": "Uma classe CSS adicional para ser definida para a lista", "smw-paramdesc-introtemplate": "Nome de uma predefinição para apresentar antes dos resultados da consulta, se existirem", "smw-paramdesc-outrotemplate": "Nome de uma predefinição para apresentar após os resultados da consulta, se existirem", "smw-paramdesc-embedformat": "Elemento HTML utilizado para definir cabeçalhos", "smw-paramdesc-embedonly": "Não exibir cabeçalhos", "smw-paramdesc-table-class": "Uma classe CSS adicional para colocar em uma tabela", "smw-paramdesc-table-transpose": "Exibe verticalmente os cabeçalhos das tabelas e horizontalmente os resultados.", "smw-paramdesc-prefix": "Controla a exibição de domínios nos valores de propriedades", "smw-paramdesc-rdfsyntax": "A sintaxe RDF a ser utilizada", "smw-paramdesc-csv-sep": "Especifica um separador de colunas", "smw-paramdesc-csv-valuesep": "Especifica um separador de valores", "smw-paramdesc-csv-merge": "Fundir os valores de linhas e colunas que têm um identificador de assunto idêntico (aliás, primeira coluna)", "smw-paramdesc-csv-bom": "Adicionar um BOM (carácter para indicar a \"endianness\") no topo do arquivo de saída", "smw-paramdesc-dsv-separator": "Separado a ser utilizado", "smw-paramdesc-dsv-filename": "Nome para o arquivo DSV", "smw-paramdesc-filename": "O nome para o arquivo de saída", "smw-smwdoc-description": "Mostra uma tabela com todos os parâmetros que podem ser utilizados para o formato de resultado específico junto aos valores padrão e as descrições.", "smw-smwdoc-default-no-parameter-list": "Este formato de resultado não fornece parâmetros específicos do formato.", "smw-smwdoc-par-format": "O formato de resultados para o qual será apresentada a documentação dos parâmetros.", "smw-smwdoc-par-parameters": "Quais parâmetros mostrar. \"specific\" para aqueles adicionados pelo formato, \"base\" para aqueles disponíveis em todos os formatos e \"all\" para ambos.", "smw-paramdesc-sort": "Propriedade para classificar a consulta", "smw-paramdesc-order": "Ordem da classificação da consulta", "smw-paramdesc-searchlabel": "Texto para continuar a busca", "smw-paramdesc-named_args": "Nomeie os argumentos passados para a predefinição", "smw-paramdesc-template-arguments": "Configura como os argumentos nomeados são passados à predefinição", "smw-paramdesc-import-annotation": "Dados marcados adicionais serão copiados durante o parsing do sujeito.", "smw-paramdesc-export": "Opção de exportação", "smw-paramdesc-prettyprint": "Uma saída de pretty-print que exibe indentações e novas linhas adicionais", "smw-paramdesc-json-unescape": "O resultado conterá barras não escapadas e caracteres Unicode multibyte", "smw-paramdesc-json-type": "Tipo de serialização", "smw-paramdesc-source": "Fonte alternativa de consulta", "smw-paramdesc-jsonsyntax": "Sintaxe JSON para ser utilizada", "smw-printername-feed": "Feeds RSS e Atom", "smw-paramdesc-feedtype": "Tipo de feed", "smw-paramdesc-feedtitle": "Texto a ser utilizado como título do feed", "smw-paramdesc-feeddescription": "Texto a ser utilizado como descrição do feed", "smw-paramdesc-feedpagecontent": "Conte<PERSON><PERSON> da página a ser exibido com o feed", "smw-label-feed-description": "$1 $2 feed", "smw-paramdesc-mimetype": "O tipo de mídia (MIME type) para o arquivo de saída.", "smw_iq_disabled": "As consultas semânticas foram desativadas neste wiki", "smw_iq_moreresults": "… mais resultados", "smw_parseerror": "O valor fornecido não foi compreendido.", "smw_decseparator": ",", "smw_kiloseparator": ".", "smw_notitle": "\"$1\" não pode ser utilizado como nome de página neste wiki.", "smw_noproperty": "\"$1\" não pode ser utilizado como um nome de propriedade neste wiki.", "smw_wrong_namespace": "Apenas páginas no domínio \"$1\" são permitidas aqui.", "smw_manytypes": "Mais de um tipo definido para a propriedade.", "smw_emptystring": "Cadeias de caracteres vazias não são aceitas.", "smw_notinenum": "\"$1\" não está na lista ($2) dos [[Property:Allows value|valores permitidos]] para a propriedade \"$3\".", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" não está na lista ($2) dos [[Property:Allows value|valores permitidos]] para a propriedade \"$3\".", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" não está no intervalo de \"$2\" especificado pela restrição [[Property:Allows value|permite valor]] para a propriedade \"$3\".", "smw-constraint-error": "Problema de restrição", "smw-constraint-error-suggestions": "<PERSON><PERSON><PERSON><PERSON> as violações e propriedades listadas junto com seus valores anotados para garantir que todos os requisitos de restrição sejam atendidos.", "smw-constraint-error-limit": "A lista conterá no máximo $1 de violações.", "smw_noboolean": "\"$1\" não é reconhecido como um valor <PERSON> (verdadeiro/falso).", "smw_true_words": "verda<PERSON><PERSON>,v,sim,s,true,t,yes,y", "smw_false_words": "falso,f,não,nao,n,false,no", "smw_nofloat": "\"$1\" não é um número.", "smw_infinite": "Números tão grandes como \"$1\" não são suportados.", "smw_unitnotallowed": "\"$1\" não está declarado como unidade de medida válida para esta propriedade.", "smw_nounitsdeclared": "Não foi declarada nenhuma unidade de medida para esta propriedade.", "smw_novalues": "Não foram especificados valores.", "smw_nodatetime": "A data “$1” não foi compreendida.", "smw_toomanyclosing": "Parece haver muitas ocorrências de \"$1\" na consulta.", "smw_noclosingbrackets": "Um uso de \"<nowiki>[[</nowiki>\" na sua consulta não foi fechada por um \"]]\" correspondente.", "smw_misplacedsymbol": "O símbolo \"$1\" foi usado num local onde não é útil.", "smw_unexpectedpart": "A parte \"$1\" da consulta não foi compreendida.\nOs resultados podem não ser os esperados.", "smw_emptysubquery": "Alguma subconsulta não tem uma condição válida.", "smw_misplacedsubquery": "Uma subconsulta foi utilizada em um local onde não são permitidas subconsultas.", "smw_valuesubquery": "Subconsultas não suportadas para valores da propriedade \"$1\".", "smw_badqueryatom": "Alguma parte \"<nowiki>[[…]]</nowiki>\" da consulta não foi compreendida.", "smw_propvalueproblem": "O valor da propriedade \"$1\" não foi compreendido.", "smw_noqueryfeature": "Uma característica da consulta não foi suportada neste wiki e parte da consulta foi descartada ($1).", "smw_noconjunctions": "Conjunções em consultas não são suportadas neste wiki e parte da consulta foi descartada ($1).", "smw_nodisjunctions": "Disjunções em consultas não são suportadas neste wiki e parte da consulta foi descartada ($1).", "smw_querytoolarge": "Não foi possível considerar {{PLURAL:$2|a seguinte condição|as seguintes $2 condições}} da consulta, devido às restrições do wiki para o tamanho e profundidade das consultas: <code>$1</code>.", "smw_notemplategiven": "Providencie um valor para o parâmetro \"predefinição\" para o formato desta consulta funcionar.", "smw_db_sparqlqueryproblem": "O resultado da consulta não pôde ser obtido do banco de dados SPARQL. Este erro pode ser temporário ou indicar um defeito no software de banco de dados.", "smw_db_sparqlqueryincomplete": "A consulta revelou-se difícil e foi interrompida. Alguns resultados podem estar faltando. Se possível, tente utilizar uma consulta mais simples.", "smw_type_header": "Propriedades do tipo \"$1\"", "smw_typearticlecount": "Exibindo $1 {{PLURAL:$1|propriedade que utiliza|propriedades que utilizam}} este tipo.", "smw_attribute_header": "Páginas que utilizam a propriedade \"$1\"", "smw_attributearticlecount": "Exibindo $1 {{PLURAL:$1|página que utiliza|páginas que utilizam}} esta propriedade.", "smw-propertylist-subproperty-header": "Subpropriedades", "smw-propertylist-redirect-header": "Sinônimos", "smw-propertylist-error-header": "Páginas com atribuições indevidas", "smw-propertylist-count": "Exibindo $1 {{PLURAL:$1|entidade relacionada|entidades relacionadas}}.", "smw-propertylist-count-with-restricted-note": "Exibindo $1 {{PLURAL:$1|entidade relacionada|entidades relacionadas}} (um número maior existe, mas a exibição está restrita à \"$2\").", "smw-propertylist-count-more-available": "Exibindo $1 {{PLURAL:$1|entidade relacionada|entidades relacionadas}} (há mais disponíveis).", "specialpages-group-smw_group": "Semantic MediaWiki", "specialpages-group-smw_group-maintenance": "Manutenção", "specialpages-group-smw_group-properties-concepts-types": "Propriedades, conceitos e tipos", "specialpages-group-smw_group-search": "Navegação e pesquisa", "exportrdf": "Exportar páginas para RDF", "smw_exportrdf_docu": "Esta página permite que você obtenha dados de uma página no formato RDF.\nPara exportar páginas, introduza os seus títulos na caixa de texto abaixo, um título por linha.", "smw_exportrdf_recursive": "Exportar recursivamente todas as páginas relacionadas.\nNote que o resultado pode ser volumoso!", "smw_exportrdf_backlinks": "Também exporta todas as páginas que se referem as páginas exportadas.\nGera RDF navegável.", "smw_exportrdf_lastdate": "Não exporte páginas que não foram alteradas desde o tempo dado.", "smw_exportrdf_submit": "Exportar", "uriresolver": "Resolvedor de URIs", "properties": "<PERSON><PERSON><PERSON><PERSON>", "smw-categories": "Categorias", "smw_properties_docu": "As se<PERSON><PERSON> propriedades são utilizadas neste wiki.", "smw_property_template": "$1 do tipo $2 ($3 {{PLURAL:$3|uso|usos}})", "smw_propertylackspage": "<PERSON><PERSON> as propriedades devem ser descritas por uma página!", "smw_propertylackstype": "Nenhum tipo foi especificado para esta propriedade (assumindo tipo $1 por enquanto).", "smw_propertyhardlyused": "Esta propriedade é muito pouco utilizada neste wiki!", "smw-property-name-invalid": "A propriedade $1 não pode ser utilizada (nome de propriedade inválido).", "smw-property-name-reserved": "\"$1\" estava listado como sendo um nome reservado e não deve ser usado como propriedade. Talvez a seguinte [https://www.semantic-mediawiki.org/wiki/Help:Property_naming página de ajuda] contenha informação sobre a razão pela qual o nome estava reservado.", "smw-sp-property-searchform": "<PERSON><PERSON><PERSON> propried<PERSON> que contenham:", "smw-sp-property-searchform-inputinfo": "O texto de entrada é sensível a maiúsculas e, quando utilizado para a filtragem, somente as propriedades que correspondem a condição serão exibidas.", "smw-special-property-searchform": "<PERSON><PERSON><PERSON> propried<PERSON> que contenham:", "smw-special-property-searchform-inputinfo": "O texto de entrada é sensível a maiúsculas e minúsculas, quando utilizado para a filtragem, somente as propriedades que correspondem a condição serão exibidas.", "smw-special-property-searchform-options": "Opções", "smw-special-wantedproperties-filter-label": "Filtro:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Não aprovada", "smw-special-wantedproperties-filter-unapproved-desc": "Opção de filtro utilizada em relação ao modo de autoridade.", "concepts": "Conceitos", "smw-special-concept-docu": "Um [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceito] pode ser visto como uma \"categoria dinâmica\", isto é, uma coleção de páginas que não é criada manualmente, mas automaticamente computada pelo Semantic MediaWiki a partir de uma descrição de uma dada consulta.", "smw-special-concept-header": "Lista de conceitos", "smw-special-concept-count": "{{PLURAL:$1|O seguinte conceito está sendo listado.|Os seguintes $1 conceitos estão sendo listados.}}", "smw-special-concept-empty": "Nenhum conceito foi encontrado.", "unusedproperties": "Propriedades não utilizadas", "smw-unusedproperties-docu": "Esta página lista [https://www.semantic-mediawiki.org/wiki/Unused_properties propriedades não utilizadas] que foram declaradas, embora nenhuma outra página as utilize. Para uma visão diferenciada, consulte as páginas especiais com [[Special:Properties|todas as propriedades]] ou com as [[Special:WantedProperties|propriedades em falta]].", "smw-unusedproperty-template": "$1 de tipo $2", "wantedproperties": "<PERSON><PERSON><PERSON><PERSON> pedidas", "smw-wantedproperties-docu": "Esta página lista [https://www.semantic-mediawiki.org/wiki/Wanted_properties propriedades em falta] que são utilizadas no wiki, mas que não possuem uma página que as descreva. Para uma visão diferenciada, consulte as páginas especiais com [[Special:Properties|todas as propriedades]] ou com as [[Special:UnusedProperties|propriedades não utilizadas]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw-special-wantedproperties-docu": "Esta página lista [https://www.semantic-mediawiki.org/wiki/Wanted_properties propriedades em falta] que são utilizadas no wiki, mas que não possuem uma página que as descreva. Para uma visão diferenciada, consulte as páginas especiais com [[Special:Properties|todas as propriedades]] ou com as [[Special:UnusedProperties|propriedades não utilizadas]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw_purge": "<PERSON><PERSON><PERSON><PERSON>", "smw-purge-update-dependencies": "O Semantic MediaWiki está limpando a página atual devido a algumas dependências desatualizadas que ele detectou que requerem uma atualização.", "smw-purge-failed": "O Semantic MediaWiki tentou limpar a página, mas falhou", "types": "Tipos", "smw_types_docu": "Lista de [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tipos de dados disponíveis] com cada [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipo] representando um conjunto único de atributos para descrever um valor em termos de características de armazenamento e exibição que são hereditários a uma propriedade atribuída.", "smw-special-types-no-such-type": "\"$1\" é desconhecido ou não foi especificado como tipo de dados válido.", "smw-statistics": "Estatísticas de semântica", "smw-statistics-cached": "Estatísticas semânticas (em cache)", "smw-statistics-entities-total": "Entidades (total)", "smw-statistics-entities-total-info": "Uma contagem estimada de linhas de entidades. Ela inclui propriedades, conceitos ou qualquer outra representação de objeto registrado que requer uma atribuição de ID.", "smw-statistics-property-instance": "{{PLURAL:$1|<PERSON><PERSON> de propriedade|<PERSON><PERSON> de propriedade}} (total)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Propriedade|Propriedades}}]] (total)", "smw-statistics-property-total-info": "O total de propriedades registradas.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Propriedade|Propriedades}} (total)", "smw-statistics-property-used": "{{PLURAL:$1|Propriedade|Propriedades}} (utilizada(s) com pelo menos um valor)", "smw-statistics-property-page": "{{PLURAL:$1|Propriedade|Propriedades}} (registrada com a página)", "smw-statistics-property-page-info": "Contagem para propriedades que possuam uma página dedicada e uma descrição.", "smw-statistics-property-type": "{{PLURAL:$1|Propriedade|Propriedades}} (atribuída ao tipo de dado)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Consulta|Consultas}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Consulta|Consultas}}]] (incorporada(s), total)", "smw-statistics-query-format": "formato <code>$1</code>", "smw-statistics-query-size": "<PERSON><PERSON><PERSON>", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Conceito|Conceitos}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Conceito|Conceitos}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Subobjeto|Subobjetos}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Subobjeto|Subobjetos}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Tipo de dados|Tipos de dados}}]]", "smw-statistics-error-count": "{{PLURAL:$1|<PERSON><PERSON> de propriedade|<PERSON>ores de propriedade}} ([[Special:ProcessingErrorList|{{PLURAL:$1|marcação imprópria|marcações impróprias}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|<PERSON>or de propriedade|Valores de propriedades}} ({{PLURAL:$1|marcação incorreta|marcações incorretas}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Entidade desatualizada|Entidades desatualizadas}}]", "smw-statistics-delete-count-info": "Entidades que foram marcadas para remoção e que devem ser eliminadas regularmente usando os scripts de manutenção fornecidos.", "smw_uri_doc": "O resolvedor de URIs implementa a [$1 descoberta TAG do W3C sobre o httpRange-14].\nEle certifica-se de que uma representação RDF (para máquinas) ou uma página wiki (para humanos) seja entregue dependendo da requisição.", "ask": "Busca semântica", "smw-ask-help": "Esta seção contém algumas hiperligações que explicam como usar a sintaxe <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecionar páginas] descreve como selecionar páginas e construir condições\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadores de pesquisa] lista os operadores de pesquisa disponíveis, incluindo os para consultas de intervalos e consultas com caracteres de substituição\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Apresentar informação] descreve o uso de instruções de exibição e de opções de formatação", "smw_ask_sortby": "Ordenar por coluna (opcional)", "smw_ask_ascorder": "Ascendente", "smw_ask_descorder": "Descendente", "smw-ask-order-rand": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Buscar resultados", "smw_ask_editquery": "Editar consulta", "smw_add_sortcondition": "[Adicionar condição de ordenação]", "smw-ask-sort-add-action": "Adicionar condição de ordenação", "smw_ask_hidequery": "Esconder consulta (visualização compacta)", "smw_ask_help": "Ajuda sobre consultas", "smw_ask_queryhead": "Condição", "smw_ask_printhead": "Seleção de dados adicionais", "smw_ask_printdesc": "(adicionar um nome de propriedade por linha)", "smw_ask_format_as": "Formatar como:", "smw_ask_defaultformat": "<PERSON><PERSON><PERSON>", "smw_ask_otheroptions": "Outras opções", "smw-ask-otheroptions-info": "Esta seção contém opções que alteram as declarações de exibição. As descrições dos parâmetros podem ser visualizadas posicionando o cursor sobre elas.", "smw-ask-otheroptions-collapsed-info": "Por favor, utilize o ícone de adição para visualizar todas as opções disponíveis", "smw_ask_show_embed": "Mostrar código embutido", "smw_ask_hide_embed": "Ocultar código embutido", "smw_ask_embed_instr": "Para embutir esta consulta em uma página wiki, utilize o código abaixo.", "smw-ask-delete": "Remover", "smw-ask-sorting": "Alfabetação", "smw-ask-options": "Opções", "smw-ask-options-sort": "Opções de ordenação", "smw-ask-format-options": "Formato e opções", "smw-ask-parameters": "Parâmetros", "smw-ask-search": "Busca", "smw-ask-debug": "<PERSON><PERSON><PERSON>", "smw-ask-debug-desc": "Gera informação de depuração da consulta", "smw-ask-no-cache": "Desativar cache de consultas", "smw-ask-no-cache-desc": "Resultados sem cache de consulta", "smw-ask-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-empty": "<PERSON><PERSON> as entradas", "smw-ask-download-link-desc": "Baixar os resultados da consulta no formato $1", "smw-ask-format": "Formato", "smw-ask-format-selection-help": "Ajuda com o formato selecionado: $1", "smw-ask-condition-change-info": "A condição foi alterada e o mecanismo de pesquisa deve executar a consulta novamente para produzir resultados que correspondam aos novos requisitos.", "smw-ask-input-assistance": "Assistência de preenchimento", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Uma ajuda é fornecida para a introdução dos dados] nos campos de exibição, ordenação e condição. O campo de condição requer o uso de um dos seguintes prefixos:", "smw-ask-condition-input-assistance-property": "<code>p:</code> para obter as sugest<PERSON><PERSON> de propriedades (p. ex.: <code>[[p:Tem ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> para obter as sugestões de categorias", "smw-ask-condition-input-assistance-concept": "<code>con:</code> para obter as sugestões de conceitos", "smw-ask-format-change-info": "O formato foi modificado e a consulta precisa ser executada novamente para produzir resultados que correspondam aos novos parâmetros e às opções de visualização.", "smw-ask-format-export-info": "O formato selecionado é um formato de exportação que não tem representação visual, portanto, os resultados só são fornecidos como download.", "smw-ask-query-search-info": "A consulta <code><nowiki>$1</nowiki></code> foi respondida pelo {{PLURAL:$3|1=<code>$2</code> (pelo cache)|<code>$2</code> (pelo cache)|<code>$2</code>}} em $4 {{PLURAL:$4|segundo|segundos}}.", "smw-ask-extra-query-log": "Registro de consulta", "smw-ask-extra-other": "Outro", "searchbyproperty": "Busca por propriedade", "processingerrorlist": "Lista de erros de processamento", "constrainterrorlist": "Lista de erros de restrição", "propertylabelsimilarity": "Relatório de similaridade de nome de propriedade", "missingredirectannotations": "Anotações de redirecionamento ausentes", "smw-processingerrorlist-intro": "A seguinte lista fornece uma perspectiva geral dos [https://www.semantic-mediawiki.org/wiki/Processing_errors erros de processamento] relacionadas ao [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Recomenda-se monitorar esta lista regularmente e corrigir as anotações inválidas de valores.", "smw-constrainterrorlist-intro": "A seguinte lista fornece uma perspectiva geral dos [https://www.semantic-mediawiki.org/wiki/Constraint_errors erros de restrição] relacionadas ao [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Recomenda-se monitorar esta lista regularmente e corrigir as anotações inválidas de valores.", "smw-missingredirects-intro": "A seguinte secção lista as páginas que têm anotações de [https://www.semantic-mediawiki.org/wiki/Redirects redirecionamento] em falta no Semantic MediaWiki (comparando com a informação armazenada no MediaWiki) e permite restaurar essas anotações quer [https://www.semantic-mediawiki.org/wiki/Help:Purge purgando] manualmente a página quer executando o ''script'' de manutenção <code>rebuildData.php</code> (com a opção <code>--redirects</code>).", "smw-missingredirects-list": "Páginas com anotações ausentes", "smw-missingredirects-list-intro": "Mostrando $1 {{PLURAL:$1|página|páginas}} com anotações de redirecionamento ausentes.", "smw-missingredirects-noresult": "Nenhuma anotação de redirecionamento ausente foi encontrada.", "smw_sbv_docu": "Busca por todas as páginas que possuem uma dada propriedade e um dado valor.", "smw_sbv_novalue": "Introduza um valor válido para a propriedade, ou veja todos os valores da propriedade \"$1\".", "smw_sbv_displayresultfuzzy": "Uma lista de todas as páginas que têm a propriedade \"$1\" com valor \"$2\".\nUma vez que houve poucos resultados, também são apresentados valores aproximados.", "smw_sbv_property": "Propriedade:", "smw_sbv_value": "Valor:", "smw_sbv_submit": "Procurar resultados", "browse": "Navegar pelo wiki", "smw_browselink": "Navegar pelas propriedades", "smw_browse_article": "Introduza o nome da página a partir da qual deseja começar a navegar.", "smw_browse_go": "<PERSON>r", "smw_browse_show_incoming": "<PERSON><PERSON> propried<PERSON> recebidas", "smw_browse_hide_incoming": "<PERSON><PERSON><PERSON><PERSON> propried<PERSON> recebidas", "smw_browse_no_outgoing": "<PERSON>sta página não possui propriedades.", "smw_browse_no_incoming": "Nenhuma propriedade aponta para esta página.", "smw-browse-from-backend": "As informações estão sendo carregadas.", "smw-browse-intro": "Esta página fornece detalhes sobre um sujeito ou  entidade, por favor, digite o nome de um objeto a ser inspecionado.", "smw-browse-invalid-subject": "A validação do sujeito terminou com um erro \"$1\".", "smw-browse-api-subject-serialization-invalid": "O sujeito tem um formato de serialização inválido.", "smw-browse-js-disabled": "É provável que o JavaScript esteja desativado ou indisponível. Recomendamos que utilize um navegador com suporte. Outras opções são fornecidas na página de configuração do parâmetro [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Mostrar grupos", "smw-browse-hide-group": "Ocultar grupos", "smw-noscript": "Esta página ou ação requer JavaScript para funcionar. Por favor habilite o JavaScript em seu navegador ou utilize um navegador que o suporte para que essa funcionalidade possa ser oferecida conforme requisição. Para mais informações, consulte a  página de ajuda [https://www.semantic-mediawiki.org/wiki/Help:Noscript Noscript].", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "Nome de propriedade inversa", "pageproperty": "Busca por propriedade de página", "pendingtasklist": "Lista de tarefas pendentes", "facetedsearch": "Busca facetada", "smw_pp_docu": "Introduza uma página e propriedade, ou só uma propriedade, para obter todos os valores atribuídos.", "smw_pp_from": "Da página:", "smw_pp_type": "Propriedade:", "smw_pp_submit": "Buscar resultados", "smw-prev": "{{PLURAL:$1|anterior|$1 anteriores}}", "smw-next": "{{PLURAL:$1|próximo|próximos $1}}", "smw_result_prev": "Anterior", "smw_result_next": "Próximo", "smw_result_results": "Resul<PERSON><PERSON>", "smw_result_noresults": "Sem resultados.", "smwadmin": "Painel de controle do Semantic MediaWiki", "smw-admin-statistics-job-title": "Estatísticas da fila de execução", "smw-admin-statistics-job-docu": "As estatísticas de tarefas apresentam informação sobre tarefas agendadas do Semantic MediaWiki que ainda não foram executadas. O número de tarefas pode ter uma pequena imprecisão ou conter tentativas falhadas. Por favor consulte o [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manual] para mais informações.", "smw-admin-statistics-querycache-title": "<PERSON><PERSON> de <PERSON>", "smw-admin-statistics-querycache-disabled": "O [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] não está habilitado neste wiki e, portanto, não há estatísticas disponíveis.", "smw-admin-statistics-querycache-legend": "As estatísticas da ''cache'' irão conter dados acumulados provisórios e derivados, incluindo:\n* \"misses\" (falhas) o número total de tentativas de obter dados da ''cache'' com respostas inatingíveis, forçando a obtenção direta do repositório (base de dados, repositório de triplas, etc.)\n* \"deletes\" (eliminações) o número total de operações de despejo da ''cache'' (por dependências de purga ou de consulta)\n* \"hits\" (acertos) contém o número de obtenções de dados da ''cache'', tendo por fonte quer consultas incorporadas (consultas feitas a partir de uma página da wiki) quer não incorporadas (pedidas por páginas como Special:Ask ou pela API, se permitido)\n* \"medianRetrievalResponseTime\" (mediana do tempo de resposta) um valor indicativo da mediana do tempo de resposta (em segundos) para pedidos de obtenção servidos, ou não, pela ''cache'' durante o período de execução do processo de recolha de dados\n* \"noCache\" (sem ''cache'') indica a quantidade de pedidos sem tentativa de obter dados da ''cache'' (consultas com limite=0, opção 'sem ''cache''', etc.)", "smw-admin-statistics-section-explain": "A seção fornece estatísticas adicionais para administradores.", "smw-admin-statistics-semanticdata-overview": "Visão geral", "smw-admin-permission-missing": "O acesso a esta página está bloqueado devido à falta de permissões. Por favor, consulte a página de ajuda sobre [https://www.semantic-mediawiki.org/wiki/Help:Permissions permissões] para detalhes sobre as configurações necessárias.", "smw-admin-setupsuccess": "O mecanismo de armazenamento foi configurado.", "smw_smwadmin_return": "Voltar para $1", "smw_smwadmin_updatestarted": "Um novo processo de atualização para atualizar os dados semânticos foi iniciado.\nTodos os dados armazenados serão reconstruídos ou reparados quando necessário.\nVocê pode acompanhar o progresso de atualização nesta página especial.", "smw_smwadmin_updatenotstarted": "Já existe um processo de atualização em execução.\nNão foi criado outro.", "smw_smwadmin_updatestopped": "Todos os processos de atualização existentes foram parados.", "smw_smwadmin_updatenotstopped": "Para parar o processo de atualização em execução, você precisa ativar a caixa de seleção para indicar que realmente tem certeza.", "smw-admin-docu": "Esta página especial te ajuda durante a instalação, atualização ou manutenção do <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a>. Ela também fornece funções administrativas, tarefas e estatísticas. \nLembre-se de efetuar cópias de segurança dos dados importantes antes de executar funções administrativas.", "smw-admin-environment": "Ambiente de software", "smw-admin-db": "Configuração do banco de dados", "smw-admin-db-preparation": "A inicialização da tabela está em curso e pode demorar algum tempo até que os resultados sejam apresentados, dependendo do tamanho e de possíveis otimizações da tabela.", "smw-admin-dbdocu": "O Semantic MediaWiki requer sua própria estrutura de banco de dados (e é independente do MediaWiki, portanto, não afeta o resto da instalação do MediaWiki), a fim de armazenar os dados semânticos.\nEsta função de instalação pode ser executada várias vezes sem causar quaisquer danos, mas é necessária apenas uma vez na instalação ou na atualização.", "smw-admin-permissionswarn": "Se a operação falhar com erros de SQL, provavelmente o usuário da base de dados utilizado pelo seu wiki (consulte o seu arquivo LocalSettings.php) não possui permissões suficientes.\nConceda a esse usuário permissões adicionais para criar e eliminar tabelas, introduza temporariam<PERSON> as credenciais do seu superusuário (<i>root</i>) da base de dados em LocalSettings.php, ou use o ''script'' de manutenção <code>setupStore.php</code>, o qual pode utilizar as credenciais de um administrador.", "smw-admin-dbbutton": "<PERSON><PERSON><PERSON><PERSON> ou at<PERSON><PERSON><PERSON> tabelas", "smw-admin-announce": "Anuncie seu wiki", "smw-admin-announce-text": "Se seu wiki é público, você pode registrá-lo no <a href=\"https://wikiapiary.com\">WikiApiary</a>, o wiki que acompanha outros wikis.", "smw-admin-deprecation-notice-title": "Avisos de depreciação", "smw-admin-deprecation-notice-docu": "A seção seguinte contém configurações que foram depreciadas ou removidas mas foram detectadas como estando ativas neste wiki. É esperado que qualquer versão futura remova o suporte para estas configurações.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> está depreciado e será removido em $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> irá remover (ou substituir) {{PLURAL:$2|a seguinte opção|as seguintes opções}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> foi descontinuado e será removido na versão $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi substituído por <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi substituído por <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "{{PLURAL:$2|Opção|Opções}} de <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> está sendo substituído por <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> foi removido em $2", "smw-admin-deprecation-notice-title-notice": "Configurações obsoletas", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Configurações obsoletas</b> mostra as configurações que foram detectadas para serem usadas neste wiki e estão planejadas para serem removidas ou alteradas em uma versão futura.", "smw-admin-deprecation-notice-title-replacement": "Configurações substituídas ou renomeadas", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Configurações substituídas ou renomeadas</b> contém configurações que foram renomeadas ou modificadas e é recomendável atualizar imediatamente seu nome ou formato.", "smw-admin-deprecation-notice-title-removal": "Configurações removidas", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Configurações removidas</b> identifica as configurações que foram removidas em uma versão anterior, mas foram detectadas para serem usadas nessa wiki.", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw-smwadmin-refresh-title": "Reparação e atualização dos dados", "smw_smwadmin_datarefresh": "Reparação dos dados", "smw_smwadmin_datarefreshdocu": "É possível restaurar todos os dados do Semantic MediaWiki baseado no conteúdo atual do wiki.\n<PERSON>to pode ser útil para reparar dados corrompidos ou para atualizar os dados se o formato interno tiver sido alterado devido a alguma atualização do software.\nA atualização é executada página a página e não ficará completa de imediato.\nAbaixo é mostrado se uma atualização está em processo e permite-lhe iniciar ou parar atualizações (a menos que esta funcionalidade tenha sido desativada por um administrador do site).", "smw_smwadmin_datarefreshprogress": "<strong>Uma atualização já se encontra em progresso.</strong>\nÉ normal que a atualização progrida lentamente já que apenas atualiza dados em pequenos blocos de cada vez quando um usuário acessa o wiki.\nPara terminar esta atualização mais rapidamente, você pode executar o script de manutenção do MediaWiki <code>runJobs.php</code> (use a opção <code>--maxjobs 1000</code> para restringir o número de atualizações feitas em um bloco).\nProgresso estimado da atualização corrente:", "smw_smwadmin_datarefreshbutton": "Programar reconstrução de dados", "smw_smwadmin_datarefreshstop": "Parar esta atualização", "smw_smwadmin_datarefreshstopconfirm": "<PERSON>m, tenho {{GENDER:$1|certeza}}.", "smw-admin-job-scheduler-note": "Tare<PERSON>s (aquelas habilitadas) nesta seção são executadas via fila de processos para evitar situações de travamento durante a sua execução. A [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de processos] é responsável pelo processamento, assim sendo,  é crítico que o script de manutenção <code>runJobs.php</code> tenha uma capacidade adequada (veja também o parâmetro de configuração <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Eliminação de entidades obsoletas", "smw-admin-outdateddisposal-intro": "Algumas atividades (a alteração de um tipo de propriedade, a remoção de páginas wiki ou a correção de valores em erro) resultam em [https://www.semantic-mediawiki.org/wiki/Outdated_entities entidades desatualizadas] e é recomendado que estas sejam removidas periodicamente para liberar espaço nas tabelas associadas.", "smw-admin-outdateddisposal-active": "Foi agendado um processo de eliminação de entidades desatualizadas.", "smw-admin-outdateddisposal-button": "Agendar eliminação", "smw-admin-feature-disabled": "Esta funcionalidade foi desativada neste wiki. Consulte a página de ajuda das <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">configurações</a> ou contate o administrador do sistema.", "smw-admin-propertystatistics-title": "Reparação de estatísticas de propriedades", "smw-admin-propertystatistics-intro": "Reconstrói todas as estatísticas de utilização de propriedades e nesse processo atualiza e corrige a [https://www.semantic-mediawiki.org/wiki/help:Property_usage_count contagem de uso] das propriedades.", "smw-admin-propertystatistics-active": "Uma tarefa de reconstrução de estatísticas de propriedade foi agendada.", "smw-admin-propertystatistics-button": "Agendar reparação de estatísticas", "smw-admin-fulltext-title": "Reparação de texto completo", "smw-admin-fulltext-intro": "Reconstrói o índice de pesquisas com base nas tabelas de propriedade, usando um tipo de dados que suporta a [https://www.semantic-mediawiki.org/wiki/full-text pesquisa de texto completo]. Alterações nas normas de indexação (''stopwords'' alteradas, novo ''stemmer'', etc.) ou uma tabela nova ou alterada requerem que este processo volte a ser executado.", "smw-admin-fulltext-active": "Uma tarefa de reconstrução de pesquisa de texto completo foi agendada.", "smw-admin-fulltext-button": "Agendar reparação de texto completo", "smw-admin-support": "Obtendo suporte", "smw-admin-supportdocu": "Vários recursos estão disponíveis para lhe ajudar em caso de problemas:", "smw-admin-installfile": "Se tiver problemas com a sua instalação, comece por rever as orientações no <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">arquivo INSTALL</a> e na <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">página de instalação</a>.", "smw-admin-smwhomepage": "A documentação completa para o usuário do Semantic MediaWiki está em <b><a href=\"https://www.semantic-mediawiki.org/wiki/P%C3%A1gina_principal\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Bugs podem ser reportados no <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>, a página para <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">reportar bugs</a> fornece instruções para criar relatórios eficazes.", "smw-admin-questions": "Se tiver mais questões ou sugestões, junte-se <PERSON> <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">lista de discussão.", "smw-admin-other-functions": "Outras funções", "smw-admin-statistics-extra": "Estatísticas de funções", "smw-admin-statistics": "Estatísticas", "smw-admin-supplementary-section-title": "Funções suplementares", "smw-admin-supplementary-section-subtitle": "Funções centrais suportadas", "smw-admin-supplementary-section-intro": "Esta seção fornece funções adicionais além do escopo de manutenção e é possível que algumas funções listadas na [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentação] estejam restritas ou indisponíveis e, portanto, inacessível neste wiki.", "smw-admin-supplementary-settings-title": "Definições e configurações", "smw-admin-supplementary-settings-intro": "<u>$1</u> mostra parâmetros que definem o comportamento do Semantic MediaWiki", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Estatísticas operacionais", "smw-admin-supplementary-operational-statistics-short-title": "estatísticas operacionais", "smw-admin-supplementary-operational-statistics-intro": "Exibe um conjunto estendido de <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Pesquisa e eliminação de entidades", "smw-admin-supplementary-idlookup-short-title": "pesquisa e eliminação de entidade", "smw-admin-supplementary-idlookup-intro": "Suporta uma simples função de <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Consulta de entidades duplicadas", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> para encontrar entidades categorizadas como duplicatas para a matriz de tabelas selecionada", "smw-admin-supplementary-duplookup-docu": "Esta página lista entradas de tabelas selecionadas que foram categorizadas como duplicadas. Entradas duplicadas devem (se de todo) ocorrer apenas em raras ocasiões potencialmente causadas por uma atualização finalizada ou por uma transação de reversão malsucedida.", "smw-admin-supplementary-operational-statistics-cache-title": "Estatísticas de Cache", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> mostra um conjunto selecionado de estatísticas relacionadas ao cache", "smw-admin-supplementary-operational-table-statistics-title": "Estatísticas da tabela", "smw-admin-supplementary-operational-table-statistics-short-title": "estatísticas da tabela", "smw-admin-supplementary-operational-table-statistics-intro": "Gera <u>$1</u> para um conjunto selecionado de tabelas", "smw-admin-supplementary-operational-table-statistics-explain": "Esta seção contém estatísticas de tabelas selecionadas para ajudar administradores e curadores de dados a tomar decisões informadas sobre esse estado do back-end e do mecanismo de armazenamento.", "smw-admin-supplementary-operational-table-statistics-legend": "A legenda descreve algumas das chaves usadas para as estatísticas de tabelas e inclui:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> número total de linhas em uma tabela", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> último identificador atualmente em uso\n* <code>duplicate_count</code> número de duplicados encontrados na tabela id_table (ver também [[Special:SemanticMediaWiki/duplicate-lookup|Pesquisa de entidades duplicadas]]) \n* <code>rows.rev_count</code> número de linhas que têm um identificador de revisão (revision_id) atribuído que indica um hiperligação direta para uma página do wiki\n* <code>rows.smw_namespace_group_by_count</code> números de linhas agregadas para espaços nominais usados na tabela\n* <code>rows.smw_proptable_hash.query_match_count</code> número de subobjetos de consulta com uma referência de tabela correspondente \n* <code>rows.smw_proptable_hash.query_null_count</code> número de subobjetos de consulta sem uma referência de tabela (sem hiperligação, referência flutuante)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> porcentagem dos termos que são únicos (uma taxa de percentagem baixa indica que termos repetidos ocupam a tabela de conteúdo e índice)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> número de termos que aparecem somente uma vez \n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> número de termos que aparecem mais de uma vez", "smw-admin-supplementary-elastic-version-info": "Vers<PERSON>", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> exibe detalhes sobre configurações e estatísticas de índice", "smw-admin-supplementary-elastic-docu": "Esta página contém informações sobre configurações, mapeamentos, situação e estatísticas de índices relacionados ao cluster do Elasticsearch que está conectado ao Semantic MediaWiki e seu [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Funções suportadas", "smw-admin-supplementary-elastic-settings-title": "Configuraç<PERSON><PERSON> (índices)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> utilizado pelo Elasticsearch para gerenciar os índices do Semantic MediaWiki", "smw-admin-supplementary-elastic-mappings-title": "Mapeamentos", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> para listar índices e mapeamentos de campo", "smw-admin-supplementary-elastic-mappings-docu": "Esta página contém detalhes do mapeamento de campos usados pelo índice atual. É recomendado o monitoramento dos mapeamentos em conjunto com o limite <code>index.mapping.total_fields.limit</code> (especifica o número máximo de campos num índice permitido).", "smw-admin-supplementary-elastic-mappings-docu-extra": "O <code>property_fields</code> se refere a contagem de campos principais indexados enquanto o  <code>nested_fields</code> se refere a uma contagem acumulada de campos adicionais atribuídos a um campo principal para suportar padrões de busca estruturados específicos.", "smw-admin-supplementary-elastic-mappings-summary": "Resumo", "smw-admin-supplementary-elastic-mappings-fields": "Mapeamentos de campos", "smw-admin-supplementary-elastic-nodes-title": "Nodes", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> exibe estatísticas dos nodes", "smw-admin-supplementary-elastic-indices-title": "Índices", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> fornece uma visão geral dos índices disponíveis e suas estatísticas", "smw-admin-supplementary-elastic-statistics-title": "Estatísticas", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> exibe o nível de estatística dos índices", "smw-admin-supplementary-elastic-statistics-docu": "Esta página fornece dados sobre estatísticas de índices  para diferentes operações que estão acontecendo em um nível de índice, os dados retornados são agrupados com agrupamentos primários e totais. A [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html página de ajuda] contém uma descrição detalhada das estatísticas de índice disponíveis.", "smw-admin-supplementary-elastic-status-replication": "Status da replicação", "smw-admin-supplementary-elastic-status-last-active-replication": "Última replicação ativa: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Intervalo de atualização: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Tarefas de recuperação em atraso: $1 (estimativa)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Tarefas de ingestão (de arquivos) em atraso: $1 (estimativa)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replicação trancada: $1 (recriação em progresso)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Monitoramento de replicação (ativo): $1", "smw-admin-supplementary-elastic-replication-header-title": "Status de replicação", "smw-admin-supplementary-elastic-replication-function-title": "Replicação", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> mostra informações sobre replicações com falha", "smw-admin-supplementary-elastic-replication-docu": "Esta página fornece informação sobre o  [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring estado de replicação] das entidades reportadas como tendo problemas com o ''cluster'' Elasticsearch. É recomendado rever as entidades listadas e purgar o conteúdo para confirmar que era um problema temporário.", "smw-admin-supplementary-elastic-replication-files-docu": "Deve ser notado que, para a lista de arquivos, é obrigatório que a tarefa de [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion ingestão de arquivos] seja executada primeiro e conclua o seu processamento.", "smw-admin-supplementary-elastic-replication-files": "<PERSON>r<PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-endpoints": "Pontos de terminação", "smw-admin-supplementary-elastic-config": "Configurações", "smw-admin-supplementary-elastic-no-connection": "De momento, a wiki '''não consegue''' estabelecer uma ligação ao ''cluster'' Elasticsearch; contacte o administrador da wiki para investigar o problema, por favor, porque este torna indisponíveis as capacidades de indexação e consulta do sistema.", "smw-list-count": "A lista contém $1 {{PLURAL:$1|item|itens}}.", "smw-property-label-uniqueness": "O nome \"$1\" foi correspondido por pelo menos uma outra representação de propriedade. Por favor, consulte a [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness página de ajuda] sobre como resolver este problema.", "smw-property-label-similarity-title": "Relatório de similaridade de nome de propriedade", "smw-property-label-similarity-intro": "<u>$1</u> calcula as semelhanças de nomes de propriedade existentes", "smw-property-label-similarity-threshold": "Limite:", "smw-property-label-similarity-type": "Exibir o tipo de ID", "smw-property-label-similarity-noresult": "Nenhum resultado foi encontrado para as opções selecionadas.", "smw-property-label-similarity-docu": "Esta página compara a [https://www.semantic-mediawiki.org/wiki/Property_similarity semelhança sintática] (não confundir com uma semelhança semântica ou lexical) entre os nomes das propriedades e reporta-as se excederem o limite. O relatório pode ajudar a filtrar propriedades com erros ortográficos ou propriedades equivalentes que representam o mesmo conceito (ver a página especial [[Special:Properties|propriedades]] para clarificar o conceito e a utilização das propriedades reportadas). O limite pode ser ajustado para aumentar ou reduzir a distância usada para a correspondência por aproximação. <code>[[Property:$1|$1]]</code> é usado para isentar propriedades desta análise.", "smw-admin-operational-statistics": "Esta página contém estatísticas operacionais coletadas em ou a partir de funções relacionadas ao Semantic MediaWiki. Uma extensa lista de estatísticas específicas do wiki pode ser encontrada [[Special:Statistics|<b>aqui</b>]].", "smw_adminlinks_datastructure": "Estrutura de dados", "smw_adminlinks_displayingdata": "Exibição de dados", "smw_adminlinks_inlinequerieshelp": "Ajuda para consultas inline", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Número de utilizações] estimado: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Propriedade definida pelo {{PLURAL:$1|usuário|sistema}}", "smw-property-indicator-last-count-update": "Contagem estimada de uso\nÚltima atualização: $1", "smw-concept-indicator-cache-update": "Contagem da cache\nÚltima atualização: $1", "smw-createproperty-isproperty": "É uma propriedade do tipo $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|O valor permitido para esta propriedade é|Os valores permitidos para esta propriedade são}}:", "smw-paramdesc-category-delim": "O delimitador", "smw-paramdesc-category-template": "Uma predefinição para formatar os itens com", "smw-paramdesc-category-userparam": "Um parâmetro para passar para a predefinição", "smw-info-par-message": "Mensagem a ser exibida.", "smw-info-par-icon": "Ícone para mostrar, \"info\" ou \"aviso\".", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Opções gerais", "prefs-extended-search-options": "Pesquisa avançada", "prefs-ask-options": "Busca semântica", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ O Semantic MediaWiki] (e extensões associadas) fornece personalização individual para um grupo de funções selecionadas. Uma lista de configurações individuais com suas descrições e características está disponível na seguinte [https://www.semantic-mediawiki.org/wiki/Help:User_preferences página de ajuda].", "smw-prefs-ask-options-tooltip-display": "Exibir o texto do parâmetro como uma dica no [[Special:Ask|construtor de consultas]] #ask.", "smw-prefs-ask-options-compact-view-basic": "Ativar visão compacta básica", "smw-prefs-help-ask-options-compact-view-basic": "Se ativado, apresenta um conjunto reduzido de hiperligações na vista compacta Special:Ask.", "smw-prefs-general-options-time-correction": "Ativar a correção da hora para páginas especiais utilizando a preferência do [[Special:Preferences#mw-prefsection-rendering|fuso horário]] local", "smw-prefs-general-options-jobqueue-watchlist": "Mostrar a lista de vigilância da fila de tarefas na minha barra pessoal", "smw-prefs-help-general-options-jobqueue-watchlist": "Se ativado, mostra uma [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist lista] de tarefas selecionadas pendentes, com os respetivos tamanhos de fila estimados.", "smw-prefs-general-options-disable-editpage-info": "Desativar o texto introdutório na edição da página", "smw-prefs-general-options-disable-search-info": "<PERSON>ativar as informações de suporte da sintaxe na página de pesquisa padrão", "smw-prefs-general-options-suggester-textinput": "Ativar o auxiliar de preenchimento para as entidades semânticas", "smw-prefs-help-general-options-suggester-textinput": "Se ativado, permite usar um [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance auxiliar de preenchimento] para encontrar propriedades, conceitos e categorias num contexto de entrada.", "smw-prefs-general-options-show-entity-issue-panel": "Mostrar o painel de problemas de entidades", "smw-prefs-help-general-options-show-entity-issue-panel": "Se ativado, executa verificações de integridade em cada página e mostra o [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel painel de problemas de entidades].", "smw-prefs-factedsearch-profile": "Seleciona um perfil padrão de  [[Special:FacetedSearch|busca facetada]]", "smw-ui-tooltip-title-property": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-quantity": "Conversão de unidade", "smw-ui-tooltip-title-info": "Informação", "smw-ui-tooltip-title-service": "Links de serviço", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-error": "Erro", "smw-ui-tooltip-title-parameter": "Parâmetro", "smw-ui-tooltip-title-event": "Evento", "smw-ui-tooltip-title-note": "<PERSON>a", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "Referência", "smw_unknowntype": "O tipo \"$1\" desta propriedade é inválido", "smw-concept-cache-text": "O conceito possui um total de $1 {{PLURAL:$1|página|páginas}} e foi atualizado pela última vez às $3 em $2.", "smw_concept_header": "Páginas do conceito \"$1\"", "smw_conceptarticlecount": "Exibindo {{PLURAL:$1|uma página|$1 páginas}}.", "smw-qp-empty-data": "Os dados requisitados não puderam ser exibidos devido a algum critério de seleção insuficiente.", "right-smw-admin": "Acesso às tarefas de administração (Semantic MediaWiki)", "right-smw-patternedit": "Acesso de edição para manter expressões regulares e padrões permitidos (Semantic MediaWiki)", "right-smw-pageedit": "Acesso de edição para páginas marcadas com <code>Is edit protected</code> (Semantic MediaWiki)", "right-smw-schemaedit": "Edição de [https://www.semantic-mediawiki.org/wiki/Help:Schema páginas de esquema] (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "<PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist lista de vigiados da fila de tarefas] (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Acesso à informações sobre não correspondência entre revisões associadas a uma entidade (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "Ver [https://www.semantic-mediawiki.org/wiki/Help:Edit_help ajuda de edição] (Semantic MediaWiki)", "restriction-level-smw-pageedit": "protegida (somente usuários com permissão)", "action-smw-patternedit": "editar expressões regulares utilizadas pelo Semantic MediaWiki", "action-smw-pageedit": "editar páginas anotadas com <code>É protegida de edição</code> (Semantic MediaWiki)", "group-smwadministrator": "Administradores (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrador|administradora|administrador(a)}} (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Administradores (Semantic MediaWiki)", "group-smwcurator": "Curadores (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|curador|curadora}} (Semantic MediaWiki)", "grouppage-smwcurator": "{{ns:project}}:Curators (Semantic MediaWiki)", "group-smweditor": "Editores (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|editor|editora}} (Semantic MediaWiki)", "grouppage-smweditor": "{{ns:project}}:Editores (Semantic MediaWiki)", "action-smw-admin": "<PERSON><PERSON><PERSON> as tarefas de administração do Semantic MediaWiki", "action-smw-ruleedit": "editar páginas de regras (Semantic MediaWiki)", "smw-property-namespace-disabled": "O [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks domínio] Propriedade está desabilitado, declarar um tipo de dado ou outras características específicas de propriedade para esta propriedade não é possível.", "smw-property-predefined-default": "\"$1\" é uma propriedade predefinida do tipo $2.", "smw-property-predefined-common": "Esta propriedade é predefinida (também chamada de [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propriedade especial]) e vem com privilégios administrativos adicionais, mas pode ser usada como qualquer outra [https://www.semantic-mediawiki.org/wiki/Property propriedade definida pelo usuário].", "smw-property-predefined-ask": "\"$1\" é uma propriedade predefinida que representa metadados (na forma de um [https://www.semantic-mediawiki.org/wiki/Subobject subobjeto]) sobre consultas individuais, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "\"$1\" é uma propriedade predefinida (também conhecida como [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propriedade especial]) que coleta o número de condições utilizadas em uma consulta, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "\"$1\" é uma propriedade predefinida que informa sobre a profundidade de uma consulta, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "É um valor numérico calculado com base nas consultas intercaladas, nas cadeias de propriedades e nos elementos descritivos disponíveis, sendo que a execução de cada consulta está restringida pelo parâmetro de configuração <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "\"$1\" é uma propriedade predefinida que descreve parâmetros que influenciam um resultado de consulta, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "É parte de um conjunto de propriedades que especificam um [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler perfil de consulta].", "smw-sp-properties-docu": "Esta página lista as [https://www.semantic-mediawiki.org/wiki/Property propriedades] e suas frequências de uso neste wiki. Para uma estatística de contagem atualizada é recomendável que o script de manutenção de [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics estatísticas de propriedades] seja executado frequentemente. Para uma visualização diferenciada, veja a página especial [[Special:UnusedProperties|propriedades não utilizadas]] ou [[Special:WantedProperties|propriedades desejadas]].", "smw-sp-properties-cache-info": "Os dados listados foram extraídos do [https://www.semantic-mediawiki.org/wiki/Caching cache] e atualizados pela última vez em $1.", "smw-sp-properties-header-label": "Lista de propriedades", "smw-admin-settings-docu": "Exibe uma lista contendo todas as configurações padrão e locais que são relevantes ao ambiente do Semantic MediaWiki.\nPara detalhes sobre configurações individuais, por favor, consulte a [https://www.semantic-mediawiki.org/wiki/Help:Configuration página de ajuda sobre configuração].", "smw-sp-admin-settings-button": "Gerar lista de configurações", "smw-admin-idlookup-title": "Procurar", "smw-admin-idlookup-docu": "Esta seção mostra detalhes técnicos sobre uma entidade individual (wikipage, subobjeto, propriedade, etc.) em Semantic MediaWiki. A entrada pode ser um ID numérico ou um valor de cadeia para corresponder ao campo de pesquisa relevante, no entanto, qualquer referência de ID refere-se ao Semântica MediaWiki e não à página do MediaWiki ou ao ID de revisão.", "smw-admin-iddispose-title": "Eliminação", "smw-admin-iddispose-docu": "Deve-<PERSON> observar que a operação de descarte é irrestrita e removerá a entidade do mecanismo de armazenamento juntamente com todas as suas referências nas tabelas pendentes, realize esta operação com '''cuidado''' e somente após consultar a [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentação].", "smw-admin-iddispose-done": "O ID \"$1\" foi removido do mecanismo de armazenamento.", "smw-admin-iddispose-references": "O identificador \"$1\" {{PLURAL:$2|não tem nenhuma|tem pelo menos uma}} referência ativa.", "smw-admin-iddispose-references-multiple": "Lista de correspondências com pelo menos um registro de referência ativo.", "smw-admin-iddispose-no-references": "A pesquisa não conseguiu corresponder \"$1\" a uma entrada na tabela.", "smw-admin-idlookup-input": "Pesquisar:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Visão geral", "smw-admin-tab-notices": "Avisos de descontinuação", "smw-admin-tab-maintenance": "Manutenção", "smw-admin-tab-supplement": "Funções suplementares", "smw-admin-tab-registry": "Registro", "smw-admin-tab-alerts": "<PERSON><PERSON><PERSON>", "smw-admin-alerts-tab-deprecationnotices": "Avisos de depreciação", "smw-admin-alerts-tab-maintenancealerts": "Alertas de manutenção", "smw-admin-alerts-section-intro": "Esta seção mostra alertas e avisos relacionados a configurações, operações e outras atividades que foram classificadas para exigir atenção de um administrador ou usuário com direitos apropriados.", "smw-admin-maintenancealerts-section-intro": "Os seguintes alertas e avisos devem ser resolvidos e, embora não sejam essenciais, espera-se que ajude a melhorar a manutenção do sistema e operacional.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Otimização de tabela", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "O sistema descobriu que a [https://www.semantic-mediawiki.org/wiki/Table_optimization otimização de tabelas] foi executada $2 dias atrás (recorde de $1) o que excede os $3 dias do limite de manutenção. Como mencionado na documentação, a execução de otimizações permitirá que o planejador de consultas tome decisões melhores sobre consultas. Assim, sugere-se executar a otimização de tabelas de modo regular.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Entidades desatualizadas", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "O sistema encontrou $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities entidades desatualizadas] e alcançou um nível crítico de manutenções não atendidas, excedendo o limite de $2. É recomendado executar o script de manutenção [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>].", "smw-admin-maintenancealerts-invalidentities-alert-title": "Entidades inválidas", "smw-admin-maintenancealerts-invalidentities-alert": "O sistema encontrou $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|entidade|entidades}}] para um [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace domínio descontinuado], é recomendado executar o script de manutenção [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] or [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>].", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Configurações", "smw-admin-configutation-tab-namespaces": "Espaços nominais", "smw-admin-configutation-tab-schematypes": "Tipos de esquema", "smw-admin-maintenance-tab-tasks": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-maintenance-tab-scripts": "Scripts de manutenção", "smw-admin-maintenance-no-description": "Sem descrição.", "smw-admin-maintenance-script-section-title": "Lista de scripts de manutenção disponíveis", "smw-admin-maintenance-script-section-intro": "Os scripts de manutenção a seguir requerem um administrador e acesso à linha de comando para poder executar scripts listados.", "smw-admin-maintenance-script-description-dumprdf": "Exportação RDF de triplas existentes.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Este script é usado para gerenciar as ''caches'' de conceitos do Semantic MediaWiki, sendo ele que pode criar, remover e atualizar ''caches'' selecionadas.", "smw-admin-maintenance-script-description-rebuilddata": "Re<PERSON>ria todos os dados semânticos na base de dados, percorrendo todas as páginas que possam ter dados semânticos.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Reconstrói o índice Elasticsearch (só nas instalações que usam o <code>ElasticStore</code>), percorrendo to<PERSON> as entidades que têm dados semânticos.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Encontrar as entidades em falta no Elasticsearch (só nas instalações que usam o <code>ElasticStore</code>) e agendar os processos de atualização apropriados.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Reconstrói o índice de pesquisa de texto completo <code>SQLStore</code> (nas instalações onde a configuração foi ativada).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Re<PERSON>ria as estatísticas de uso para todas as entidades de propriedade.", "smw-admin-maintenance-script-description-removeduplicateentities": "Remove entidades duplicadas encontradas em tabelas selecionadas que não possuem referências ativas.", "smw-admin-maintenance-script-description-setupstore": "Configura o modo de armazenamento e consulta definido em <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Atualiza o campo <code>smw_sort</code> do <code>SQLStore</code> (de acordo com a configuração [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "Popula o campo <code>smw_hash</code> das linhas em que este não tem um valor.", "smw-admin-maintenance-script-description-purgeentitycache": "Eliminar entradas de cache para entidades conhecidas e seus dados associados.", "smw-admin-maintenance-script-description-updatequerydependencies": "Atualizar consultas e dependências das consultas (ver a configuração [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Descarte entidades desatualizadas e links de consulta.", "smw-admin-maintenance-script-description-runimport": "Importa o conteúdo autodescoberto a partir de [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Scripts de atualização", "smw-admin-maintenance-script-section-rebuild": "Scripts de reconstrução", "smw-livepreview-loading": "Carregando...", "smw-sp-searchbyproperty-description": "Esta página fornece uma simples [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces interface de navegação] para encontrar entidades descritas por uma propriedade e um determinado valor. Outras interfaces de busca disponíveis incluem a página de [[Special:PageProperty|busca por propriedade]] e o [[Special:Ask|construtor de consultas]].", "smw-sp-searchbyproperty-resultlist-header": "Lista de resultados", "smw-sp-searchbyproperty-nonvaluequery": "Uma lista de valores que possuem a propriedade \"$1\" atribuída.", "smw-sp-searchbyproperty-valuequery": "Uma lista de páginas que possuem a propriedade \"$1\" com o valor \"$2\" atribuído.", "smw-datavalue-number-textnotallowed": "\"$1\" não pode ser atribuído a uma declaração do tipo número com o valor $2.", "smw-datavalue-number-nullnotallowed": "\"$1\" retornou o valor \"NULL\" (nulo), que não é permitido como um número.", "smw-editpage-annotation-enabled": "Esta página suporta marcações semânticas no texto (por exemplo, <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) para construir conteúdo estruturado e recuperável por consultas providas pelo Semantic MediaWiki. Para uma descrição completa sobre como utilizar marcações ou a função #ask, por favor, leia as páginas de ajuda: [https://www.semantic-mediawiki.org/wiki/Help:Getting_started primeiros passos], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation marcação no texto] ou [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries consultas embutidas].", "smw-editpage-annotation-disabled": "Esta página não está habilitada para marcações semânticas no texto devido à restrições de namespace. Detalhes sobre como habilitar um namespace podem ser encontrados na página de ajuda sobre [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuração].", "smw-editpage-property-annotation-enabled": "Esta propriedade pode ser extendida utilizando marcações semânticas para especificar o tipo de dados (por exemplo <nowiki>\"[[Has type::Page]]\"</nowiki>) ou outras declarações suportadas (por exemplo <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). Para uma descrição sobre como melhorar esta página, veja a [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaração de uma propriedade] ou a página de ajuda que [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes lista os tipos de dados disponíveis].", "smw-editpage-property-annotation-disabled": "Esta propriedade não pode ser extendida com a marcação de tipo de dados (por exemplo, <nowiki>\"[[Has type::Page]]\"</nowiki>) já que ela é predefinida (para mais informações, veja a página de ajuda sobre [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propriedades especiais]).", "smw-editpage-concept-annotation-enabled": "Este conceito pode ser extendido utilizando a função #concept. Veja a página de ajuda [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceitos].", "smw-search-syntax-support": "A entrada da pesquisa permite o uso da   [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search sintaxe de consulta semântica] para encontrar correspondências usando o Semantic MediaWiki.", "smw-search-input-assistance": "O [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance ajudante de preenchimento] também é habilitado para facilitar a pré-seleção de propriedades e categorias disponíveis.", "smw-search-help-intro": "Uma entrada com a forma <code><nowiki>[[ ... ]]</nowiki></code> assinala ao processador da entrada que deve usar o motor de pesquisa do Semantic MediaWiki. Note que a combinação de <code><nowiki>[[ ... ]]</nowiki></code> com uma pesquisa de texto não estruturado, como <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code>, não é suportada.", "smw-search-help-structured": "Pesquisas estruturadas:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (como [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context contexto filtrado])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (com um [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context contexto de consulta])", "smw-search-help-proximity": "Pesquisas aproximidas (uma propriedade ser desconhecida, '''só''' disponível para aqueles motores de pesquisa que fornecem uma integração com a pesquisa de texto completo):\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code> (pesquisar em todos os documentos os termos \"lorem\" e \"ipsum\" que tenham sido indexados)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (pesquisar correspondências com \"lorem ipsum\" como frase)", "smw-search-help-ask": "As seguintes hiperligações explicam como usar a sintaxe <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecionar páginas] descreve como selecionar páginas e construir condições\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadores de pesquisa] lista os operadores de pesquisa disponíveis, incluindo os para consultas de intervalos e consultas com caracteres de substituição", "smw-search-input": "Introdução e pesquisa", "smw-search-help-input-assistance": "É fornecida uma [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance ajuda de introdução] para o campo de entrada e requer que seja usado um dos seguintes prefixos:\n\n*<code>p:</code> para ativar as sugestões de propriedades (p. ex.: <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> para ativar as sugestões de categorias\n\n*<code>con:</code> para ativar as sugestões de conceitos", "smw-search-syntax": "Sintaxe", "smw-search-profile": "Estendida", "smw-search-profile-tooltip": "Funções de pesquisa em conexão com a Semantic MediaWiki", "smw-search-profile-sort-best": "<PERSON><PERSON>", "smw-search-profile-sort-recent": "<PERSON><PERSON>e", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-intro": "O [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile perfil extendido] da página Special:Search dá acesso a funções de pesquisa específicas da extensão Semantic MediaWiki e do seu servidor de consultas suportado.", "smw-search-profile-extended-help-sort": "Especifica uma preferência de ordenação para a apresentação do resultado com:", "smw-search-profile-extended-help-sort-title": "*\"Título\" usando o título da página (ou título de apresentação) como critério de ordenação", "smw-search-profile-extended-help-sort-recent": "*\"Mais recente\" mostrar<PERSON> primeiro as entidades modificadas mais recentemente (as entidades subobjetos são suprimidas porque essas entidades não estão anotadas com uma [[Property:Modification date|data de modificação]])", "smw-search-profile-extended-help-sort-best": "*\"Melhor correspondência\" ordenará as entidades por [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy relevância] com base em classificações fornecidas pelo servidor", "smw-search-profile-extended-help-form": "São disponibilizados formulários (se estes forem mantidos) para certos casos específicos de utilização. Os formulários podem expor diferentes campos de propriedade e de entrada de valor, para limitar o processo de introdução e facilitar a criação de pedidos de pesquisa (ver $1).", "smw-search-profile-extended-help-namespace": "A caixa de seleção do espaço nominal/domínio será ocultada logo que um formulário for selecionado mas pode ser tornada visível com a ajuda do botão \"exibir/esconder\".", "smw-search-profile-extended-help-search-syntax": "O campo de entrada da pesquisa permite o uso da sintaxe <code>#ask</code> para definir um contexto de pesquisa específico do Semantic MediaWiki. Algumas expressões úteis:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> para encontrar tudo o que contenha \"...\" e é especialmente útil quando não se conhece o contexto da pesquisa ou as propriedades envolvidas (por exemplo, <code>in:(lorem && ipsum)</code> é equivalente a <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> para encontrar tudo o que contenha \"...\" exatamente na mesma ordem", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> para corresponder com qualquer entidade com uma propriedade \"...\" (por exemplo, <code>has:(Foo && Bar)</code> é equivalente a <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> para não corresponder com nenhuma entidade que inclui \"...\"", "smw-search-profile-extended-help-search-syntax-prefix": "* Estão disponíveis e definidos prefixos personalizados adicionais, como: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Algumas expressões estão reservadas, como: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Algumas das operações listadas só são úteis em ligação com um índice ativado de texto completo ou com o ElasticStore.''", "smw-search-profile-extended-help-query": "<code><nowiki>$1</nowiki></code> foi usado como consulta.", "smw-search-profile-extended-help-query-link": "Para mais detalhes, por favor use o $1.", "smw-search-profile-extended-help-find-forms": "formulá<PERSON>s disponí<PERSON>is", "smw-search-profile-extended-section-sort": "Ordenar por", "smw-search-profile-extended-section-form": "Formulários", "smw-search-profile-extended-section-search-syntax": "Entrada de pesquisa", "smw-search-profile-extended-section-namespace": "Espaço nominal", "smw-search-profile-extended-section-query": "Consulta", "smw-search-profile-link-caption-query": "construtor de consulta", "smw-search-show": "<PERSON><PERSON><PERSON>", "smw-search-hide": "Esconder", "log-name-smw": "Log do Semantic MediaWiki", "log-show-hide-smw": "$1 o registo do Semantic MediaWiki", "logeventslist-smw-log": "Registo do Semantic MediaWiki", "log-description-smw": "Atividades para [https://www.semantic-mediawiki.org/wiki/Help:Logging tipos de eventos habilitados] que são reportados pelo Semantic MediaWiki e seus componentes.", "logentry-smw-maintenance": "Eventos relacionados à manutenção emitidos pelo Semantic MediaWiki.", "smw-datavalue-import-unknown-namespace": "O namespace \"$1\" a ser importado é desconhecido. Por favor, tenha certeza de que os detalhes da importação OWL estão disponíveis via [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Não foi possível encontrar um URI de namespace \"$1\" na [[MediaWiki:Smw import $1|página de importação $1]].", "smw-datavalue-import-missing-type": "Nenhuma definição de tipo foi encontrada para \"$1\" na [[MediaWiki:Smw import $2|página de importação $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|importado de $1]]", "smw-datavalue-import-invalid-value": "\"$1\" não é um formato válido, a formato esperado consiste em \"namespace\":\"identificador\" (por exemplo: \"foaf:name\").", "smw-datavalue-import-invalid-format": "Era esperado que o texto \"$1\" estivesse dividido em quatro partes, mas o formato não foi compreendido.", "smw-property-predefined-impo": "\"$1\" é uma propriedade predefinida que descreve uma relação a um [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabulário importado], é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "\"$1\" é uma propriedade predefinida que descreve o [[Special:Types|tipo de dado]] de uma propriedade, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "\"$1\" é uma propriedade predefinida que representa um \"[https://www.semantic-mediawiki.org/wiki/Help:Container container]\", é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "A estrutura recipiente permite acumular atribuições de valores a propriedades e é semelhante a uma página wiki normal, porém, num espaço de entidades diferente, estando ligada ao sujeito que a incorpora.", "smw-property-predefined-errp": "\"$1\" é uma propriedade predefinida que registra erros de entrada em anotações de valor irregular e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "Na maioria dos casos, a causa é uma não correspondência de tipos ou uma restrição do [[Property:Allows value|valor]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] é uma propriedade predefinida que pode definir uma lista de valores permitidos para restringir a atribuição de valores para uma determinada propriedade. É fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list \"$1\"] é uma propriedade predefinida que pode especificar uma referência a uma lista de valores permitidos para restringir a atribuição de valores a uma propriedade, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "A propriedade \"$1\" tem uma área de aplicação restrita e não pode ser usada como propriedade de anotação por um utilizador.", "smw-datavalue-property-restricted-declarative-use": "A propriedade \"$1\" é uma propriedade declarativa e só pode ser usada numa propriedade ou página de categoria.", "smw-datavalue-property-create-restriction": "A propriedade \"$1\" não existe e o usuário não possui a permissão \"$2\" (veja o [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade]) para criar ou atribuir valores à uma propriedade não aprovada.", "smw-datavalue-property-invalid-character": "\"$1\" contém um caractere \"$2\" listado como parte do nome da propriedade e, portanto, foi classificado como inválido.", "smw-datavalue-property-invalid-chain": "Utilizar \"$1\" como uma sequência de propriedades não é permitido durante o processo de marcação.", "smw-datavalue-restricted-use": "O valor de dado \"$1\" foi marcado para uso restrito.", "smw-datavalue-invalid-number": "\"$1\" não pode ser interpretado como um número.", "smw-query-condition-circular": "Uma possível condição circular foi detectada em \"$1\".", "smw-query-condition-empty": "A descrição da consulta tem uma condição vazia.", "smw-types-list": "Lista de tipos de dados", "smw-types-default": "\"$1\" é um tipo de dado predefinido.", "smw-types-help": "Mais informações e exemplos podem ser encontrados na [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 página de ajuda].", "smw-type-anu": "\"$1\" é uma variante do tipo de dados [[Special:Types/URL|URL]] e é utilizada, na maior parte das vezes, para exportar uma declaração ''owl:AnnotationProperty''.", "smw-type-boo": "\"$1\" é um tipo de dado básico para descrever um valor verdadeiro ou falso.", "smw-type-cod": "\"$1\" é uma variante do tipo de dados [[Special:Types/Text|Texto]] para ser utilizada com textos técnicos de tamanho arbitrário, tais como listagens de código fonte.", "smw-type-geo": "«$1» é um tipo de dado que descreve localizações geográficas e requer a extensão [https://www.semantic-mediawiki.org/wiki/Extension:Maps «Mapas»] para fornecer uma funcionalidade estendida.", "smw-type-tel": "\"$1\" é um tipo de dado especial para descrever números internacionais de telefone de acordo com a RFC 3966.", "smw-type-txt": "\"$1\" é um tipo de dado básico para descrever textos de tamanho arbitrário.", "smw-type-dat": "\"$1\" é um tipo de dado básico para representar pontos no tempo em um formato unificado.", "smw-type-ema": "\"$1\" é um tipo de dados especial para representar um email.", "smw-type-tem": "\"$1\" é um tipo de dados numérico especial para representar uma temperatura.", "smw-type-qty": "\"$1\" é um tipo de dados para descrever quantidades com uma representação numérica e uma unidade de medida.", "smw-type-rec": "\"$1\" é um tipo de dados contêiner que especifica uma lista de propriedades com tipos em uma ordem fixa.", "smw-type-extra-tem": "O esquema de conversão inclui unidades suportadas, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Fahrenheit e Rankine.", "smw-type-tab-properties": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-tab-types": "Tipos", "smw-type-tab-type-ids": "Identificadores de tipos", "smw-type-tab-errors": "<PERSON><PERSON><PERSON>", "smw-type-primitive": "Básico", "smw-type-contextual": "Contextual", "smw-type-compound": "Composto", "smw-type-container": "Container", "smw-type-no-group": "Não classificado", "smw-special-pageproperty-description": "Esta página fornece uma interface de navegação para encontrar todos os valores de uma propriedade e uma determinada página. Entre as outras interfaces de pesquisa disponíveis, incluem-se a [[Special:SearchByProperty|pesquisa de propriedades]] e o [[Special:Ask|construtor de consultas ''ask'']].", "smw-property-predefined-errc": "\"$1\" é uma propriedade predefinida fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] que apresenta os erros que aparecem em relação às marcações impróprias de valores ou de erros de processamento de entrada desses valores.", "smw-property-predefined-long-errc": "Os erros são coletados em uma [https://www.semantic-mediawiki.org/wiki/Help:Container estrutura recipiente] que pode incluir uma referência à propriedade que causou a discrepância.", "smw-property-predefined-errt": "\"$1\" é uma propriedade predefinida que contém uma descrição textual de um erro, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Um subobjeto definido por um usuário utilizou um esquema de nome inválido. A notação com ponto ($1) nos primeiros cinco caracteres está reservada para o uso exclusivo pelas extensões. Você pode configurar um  [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier identificador nomeado].", "smw-datavalue-record-invalid-property-declaration": "O definição do registro contém a propriedade \"$1\" que está, por sua vez, declarada também como sendo do tipo registro e isso não é permitido.", "smw-property-predefined-mdat": "\"$1\" é uma propriedade predefinida que corresponde à data da última modificação, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "\"$1\" é uma propriedade predefinida que corresponde à data da primeira revisão, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "\"$1\" é uma propriedade predefinida que indica se um sujeito é novo ou não, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "\"$1\" é uma propriedade predefinida que contém o nome da página do utilizador que criou a última revisão, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "\"$1\" é uma propriedade predefinida que descreve o tipo MIME de um arquivo carregado, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "\"$1\" é uma propriedade predefinida que descreve o tipo de mídia de um arquivo carregado, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "\"$1\" é uma propriedade predefinida que contém o nome do formato de resultado usado em uma consulta, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "\"$1\" é uma propriedade predefinida que descreve as condições da consulta em forma de texto, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "\"$1\" é uma propriedade predefinida que contém o tempo (em segundos) que a execução da consulta demorou, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "\"$1\" é uma propriedade predefinida, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki ], que identifica fontes alternativas de consulta (por exemplo, fontes remotas, federadas).", "smw-property-predefined-askco": "\"$1\" é uma propriedade predefinida fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] para descrever o estado de uma consulta ou dos seus componentes.", "smw-property-predefined-long-askco": "O número ou números atribuídos representam um estado interno codificado que é explicado na [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler págin<PERSON> de ajuda].", "smw-property-predefined-prec": "\"$1\" é uma propriedade predefinida que descreve a [https://www.semantic-mediawiki.org/wiki/Help:Display_precision precisão de apresentação] (em casas decimais) para os tipos de dados numéricos.", "smw-property-predefined-attch-link": "\"$1\" é uma propriedade predefinida que coleta links de arquivos e imagens incorporados encontrados em uma página e é fornecida por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-inst": "\"$1\" é uma propriedade interna predefinida que armazena informações de categoria independentes do MediaWiki, fornecida pelo  [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-unit": "\"$1\" é uma propriedade declarativa predefinida para definir unidades de exibição para propriedades de tipo numéricas, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-unit": "Uma lista separada por vírgulas permite descrever unidades ou formatos para serem usados para a exibição.", "smw-property-predefined-conv": "\"$1\" é uma propriedade declarativa predefinida para definir um fator de conversão para alguma unidade de quantidade física, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-serv": "\"$1\" é uma propriedade declarativa predefinida para adicionar links de serviços para uma propriedade, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-redi": "\"$1\" é uma propriedade interna predefinida para registrar redirecionamentos, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subp": "\"$1\" é uma propriedade declarativa predefinida para definir que uma propriedade é uma [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of subpropriedade de] outra, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subc": "\"$1\" é uma propriedade predefinida para definir que uma categoria é uma [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of subcategoria de] outra, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-conc": "\"$1\" é uma propriedade interna predefinida para definir um conceito associado, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-err-type": "\"$1\" é uma propriedade predefinida para identificar um grupo ou classe de [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors erros de processamento], é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-skey": "\"$1\" é uma propriedade interna predefinida para armazenar uma referência de ordenação, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pplb": "\"$1\" é uma propriedade declarativa predefinida para especificar um [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label rótulo preferido para a propriedade], é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-chgpro": "\"$1\" é uma propriedade predefinida para armazenar informações de [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation propagação de mudanças], é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-link": ", <PERSON> fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-format-schema": ", <PERSON> fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-profile-schema": ", <PERSON> fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans": ", <PERSON> fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-source": ", <PERSON> fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-group": ", <PERSON> fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cont-len": "\"$1\" é uma propriedade predefinida para armazenar informações de tamanho, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-len": "É usada em conexão com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coletar e armazenar informações de tamanho obtidas a partir de um arquivo submetido (se fornecido).", "smw-property-predefined-cont-lang": "\"$1\" é uma propriedade predefinida para armazenar informação de idioma, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-lang": "É usada em conexão com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coletar e armazenar informação de idioma obtida a partir de um arquivo submetido (se fornecido).", "smw-property-predefined-cont-title": "\"$1\" é uma propriedade predefinida para armazenar informação de título, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-title": "É usada em conexão com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coletar e armazenar informação de título obtida a partir de um arquivo submetido (se fornecido).", "smw-property-predefined-cont-author": "\"$1\" é uma propriedade predefinida para armazenar informação de autoria, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-author": "É usada em conexão com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coletar e armazenar informação de autoria obtida a partir de um arquivo submetido (se fornecido).", "smw-property-predefined-cont-date": "\"$1\" é uma propriedade predefinida para armazenar informação de data, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-date": "É usada em conexão com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coletar e armazenar informação de data obtida a partir de um arquivo submetido (se fornecido).", "smw-property-predefined-cont-type": "\"$1\" é uma propriedade predefinida para armazenar informação de tipo de arquivo, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-type": "É usada em conexão com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coletar e armazenar informação de tipo de arquivo obtida a partir de um arquivo submetido (se fornecido).", "smw-property-predefined-cont-keyw": "\"$1\" é uma propriedade predefinida para representar palavras-chave, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-keyw": "É usada em conexão com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coletar e armazenar palavras-chave obtidas a partir de um arquivo submetido (se fornecido).", "smw-property-predefined-file-attch": "\"$1\" é uma propriedade predefinida para representar um container que armazena informação de anexo, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-file-attch": "É usada em conexão com o [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e o [https://www.semantic-mediawiki.org/Attachment_processor processador de anexos]) para coletar toda informação específica de conteúdo obtidas a partir de um arquivo submetido (se fornecido).", "smw-types-extra-geo-not-available": "Não foi detetada a extensão [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Mapas\"], portanto, \"$1\" está restringido na sua capacidade operacional.", "smw-datavalue-monolingual-dataitem-missing": "Um item esperado para construir um valor composto monolíngue está faltando.", "smw-datavalue-languagecode-missing": "Para a marcação \"$1\" o analisador sintático não conseguiu determinar o código de idioma (exemplo: \"foo@en\").", "smw-datavalue-languagecode-invalid": "\"$1\" não foi reconhecido como um código de idioma válido.", "smw-property-predefined-lcode": "\"$1\" é uma propriedade predefinida que representa um código de idioma formatado BCP47, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "\"$1\" é um tipo de dados [https://www.semantic-mediawiki.org/wiki/Help:Container recipiente] (''container'') que associa um texto a um [[Property:Language code|código de idioma]] específico.", "smw-types-extra-mlt-lcode": "O tipo de dados {{PLURAL:$2|requer|não requer}} um código de idioma (ou seja, uma marcação de valor sem um código de idioma {{PLURAL:$2|não é|é}} aceito).", "smw-property-predefined-text": "\"$1\" é uma propriedade predefinida que representa um texto de comprimento arbitrário, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "\"$1\" é uma propriedade predefinida que permite descrever uma propriedade no contexto de um idioma, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "\"$1\" é uma propriedade predefinida para definir uma lista de propriedades utilizadas com uma propriedade do tipo [[Special:Types/Record|registro]], é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] In-text annotation parser time", "smw-limitreport-intext-postproctime": "[SMW] tempo pós-processamento", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Store update time (on page purge)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw_allows_pattern": "É esperado que esta página contenha uma lista de referências (seguidas por [https://pt.wikipedia.org/wiki/Express%C3%A3o_regular expressões regulares]) que serão disponibilizadas pela propriedade [[Property:Allows pattern|Allows pattern]]. Para editar esta página a permissão <code>smw-patternedit</code> é necessária.", "smw-datavalue-allows-pattern-mismatch": "\"$1\" foi considerado inválido pela expressão regular \"$2\".", "smw-datavalue-allows-pattern-reference-unknown": "O padrão de referência \"$1\" não foi encontrado na página [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "A lista de referência \"$1\" não foi correspondida em uma página [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "Está faltando itens com o marcador * no conteúdo da lista \"$1\".", "smw-datavalue-feature-not-supported": "A funcionalidade \"$1\" não é suportada ou foi desativada neste wiki.", "smw-property-predefined-pvap": "\"$1\" é uma propriedade predefinida que pode especificar uma [[MediaWiki:Smw allows pattern|referência de padrão permitido]] para aplicar uma [https://pt.wikipedia.org/wiki/Express%C3%A3o_regular expressão regular], é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "\"$1\" é uma propriedade predefinida que pode atribuir a uma entidade um título de apresentação distinto, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "\"$1\" é uma propriedade predefinida, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para restringir a atribuição de valores a valores únicos (ou a um, no máximo) para cada instância.", "smw-property-predefined-long-pvuc": "A unicidade é estabelecida quando dois valores não são iguais em suas representações literais e qualquer violação desta restrição será categorizada como erro.", "smw-datavalue-constraint-uniqueness-violation": "A propriedade \"$1\" só permite a atribuição de valores únicos e \"$2\" já foi atribuído no sujeito \"$3\".", "smw-datavalue-constraint-uniqueness-violation-isknown": "A propriedade \"$1\" só permite anotações de valores únicos, ''$2'' já contém um valor atribuído. \"$3\" viola a restrição de unicidade.", "smw-datavalue-constraint-violation-non-negative-integer": "A propriedade \"$1\" tem uma restrição \"inteiro não negativo\" e o valor ''$2'' viola esse requisito.", "smw-datavalue-constraint-violation-must-exists": "A propriedade \"$1\" tem uma restrição <code>must_exists</code> e um valor ''$2'' que viola aquele requisito.", "smw-datavalue-constraint-violation-single-value": "A propriedade \"[[Property:$1|$1]]\" tem uma restrição <code>single_value</code> e o valor \"$2\" viola aquele requisito.", "smw-constraint-violation-uniqueness": "Uma restrição <code>unique_value_constraint</code> está atribuída a propriedade \"[[Property:$1|$1]]\" que somente permite a atribuição de valores únicos. A anotação de valor ''$2'' já foi encontrada no sujeito \"$3\".", "smw-constraint-violation-uniqueness-isknown": "Uma restrição <code>unique_value_constraint</code> está atribuída a propriedade \"[[Property:$1|$1]]\", assim, ela somente permite a atribuição de valores únicos. ''$2'' já contém um valor anotado com \"$3\", violando a restrição de unicidade para o sujeito atual.", "smw-constraint-violation-non-negative-integer": "Uma restrição <code>non_negative_integer</code> está atribuída a propriedade \"[[Property:$1|$1]]\" e o valor ''$2'' está violando esse requisito.", "smw-constraint-violation-must-exists": "Uma limitação <code>must_exists</code> assinada à propriedade \"[[Property:$1|$1]]\" e ao valor de anotação ''$2'' está violando o requisito de restrição.", "smw-constraint-violation-single-value": "Uma restrição <code>single_value</code> está atribuída a propriedade \"[[Property:$1|$1]]\" e o valor \"$2\" esta violando esse requisito.", "smw-constraint-violation-class-shape-constraint-missing-property": "Uma <code>shape_constraint</code> está atribuída à categoria \"[[:$1]]\" com uma chave <code>property</code>, a propriedade requerida \"$2\" está faltando.", "smw-constraint-violation-class-shape-constraint-wrong-type": "Uma <code>shape_constraint</code> está atribuída à categoria \"[[:$1]]\" com uma chave de <code>property_type</code>, a propriedade \"$2\" não corresponde ao tipo de \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "Uma <code>shape_constraint</code> está atribuída à categoria \"[[:$1]]\" com uma chave de <code>max_cardinality</code>, a propriedade \"$2\" não corresponde a cardinalidade de \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "Uma <code>shape_constraint</code> está atribuída à categoria \"[[:$1]]\" com uma chave de <code>min_textlength</code>, a \n propriedade \"$2\" não corresponde ao requisito de tamanho \"$3\".", "smw-constraint-violation-class-mandatory-properties-constraint": "Uma restrição de <code>mandatory_properties</code> está atribuída à categoria \"[[:$1]]\" e requer as seguin<PERSON> propriedades obrigatórias: $2", "smw-constraint-violation-allowed-namespace-no-match": "Uma restrição de <code>allowed_namespaces</code> está atribuída à propriedade \"[[Property:$1|$1]]\" e \"$2\" viola o requisito de namespace, somente os seguintes namespaces são permitidos: \"$3\".", "smw-constraint-violation-allowed-namespaces-requires-page-type": "A restrição de <code>allowed_namespaces</code> requer um tipo de página.", "smw-constraint-schema-category-invalid-type": "O esquema anotado \"$1\" é inválido para uma categoria; requer um tipo \"$2\".", "smw-constraint-schema-property-invalid-type": "O esquema anotado \"$1\" é inválido para uma propriedade; requer um tipo \"$2\".", "smw-constraint-error-allows-value-list": "\"$1\" não está na lista ($2) de [[Property:Allows value|valores permitidos]] para a propriedade \"$3\".", "smw-constraint-error-allows-value-range": "\"$1\" não está dentro do intervalo de \"$2\" especificado pela restrição de [[Property:Allows value|permissão de valores]] para a propriedade \"$3\".", "smw-property-predefined-boo": "\"$1\" é um [[Special:Types/Boolean|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] para representar valores booleanos.", "smw-property-predefined-num": "\"$1\" é um [[Special:Types/Number|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores numéricos.", "smw-property-predefined-dat": "\"$1\" é um [[Special:Types/Date|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores de datas.", "smw-property-predefined-uri": "\"$1\" é um [[Special:Types/URL|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores URI/URL.", "smw-property-predefined-qty": "\"$1\" é um [[Special:Types/Quantity|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores de quantidade.", "smw-datavalue-time-invalid-offset-zone-usage": "\"$1\" contém um offset e um identificador de zona, o que não é suportado.", "smw-datavalue-time-invalid-values": "O valor \"$1\" contém uma informação que não pode ser interpretada na forma de \"$2\".", "smw-datavalue-time-invalid-date-components-common": "\"$1\" contém alguma informação que não pode ser interpretada.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\" contém um travessão, traço de ligação extrínseco ou outros caracteres que são inválidos para a interpretação de uma data.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" contém alguns componentes vazios.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" contém mais de três componentes necessários para a interpretação de uma data.", "smw-datavalue-time-invalid-date-components-sequence": "\"$1\" contém uma sequência que não foi possível interpretar contra uma matriz de correspondências disponível para componentes de datas.", "smw-datavalue-time-invalid-ampm": "\"$1\" contém \"$2\" como elemento de hora, o que é inválido no formato de relógio de 12 horas.", "smw-datavalue-time-invalid-jd": "Não foi possível interpretar o valor \"$1\" como um dia válido no calend<PERSON><PERSON>, \"$2\" pode apresentar o problema.", "smw-datavalue-time-invalid-prehistoric": "Não é possível interpretar o valor de entrada pré-histórico \"$1\". <PERSON>r exemplo, especificar mais do que anos, ou do que um modelo de calendário, poderá produzir resultados inesperados num contexto pré-histórico.", "smw-datavalue-time-invalid": "Não foi possível interpretar o valor \"$1\" como uma data válida ou como um componente de tempo, \"$2\" pode apresentar o problema.", "smw-datavalue-external-formatter-uri-missing-placeholder": "O espaço reservado \"$1\" está faltando no formatador do URI.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" é um URL inválido.", "smw-datavalue-external-identifier-formatter-missing": "Falta à propriedade a atribuição de um [[Property:External formatter uri|\"Formatador de URI externo\"]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "O identificador externo \"$1\" espera uma substituição de vários campos, mas o valor atual de \"$2\" não possui pelo menos um parâmetro de valor para corresponder ao requisito.", "smw-datavalue-keyword-maximum-length": "A palavra-chave excedeu o tamanho máximo de $1 {{PLURAL:$1|carácter|caracteres}}.", "smw-property-predefined-eid": "\"$1\" é um [[Special:Types/External identifier|tipo]] e uma propriedade predefinida, fornecidos pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar identificadores externos.", "smw-property-predefined-peid": "\"$1\" é uma propriedade predefinida que especifica um identificador externo, é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pefu": "\"$1\" é uma propriedade predefinida fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] para especificar um recurso externo com um espaço reservado.", "smw-property-predefined-long-pefu": "Espera-se que o URI contenha um espaço reservado que será ajustado com um valor de [[Special:Types/External identifier|identificador externo]] para formar uma referência de recurso válida.", "smw-type-eid": "\"$1\" é uma variante do tipo de dados [[Special:Types/Text|Texto]] para descrever recursos externos (baseados em URI) e requer propriedades atribuídas para declarar um [[Property:External formatter uri|formatador de URI externo]].", "smw-property-predefined-keyw": "\"$1\" é uma propriedade predefinida e um [[Special:Types/Keyword|tipo]], fornecidas pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que normalizam um texto e têm um tamanho restrito de caracteres.", "smw-type-keyw": "\"$1\" é uma variante de tipo de dados  [[Special:Types/Text|Texto]] que tem um tamanho restrito de caracteres com uma representação de conteúdo normalizada.", "smw-datavalue-stripmarker-parse-error": "O valor fornecido, \"$1\", contém [https://en.wikipedia.org/wiki/Help:Strip_markers marcadores do analisador sintático] e, portanto, não pode ser suficientemente analisado.", "smw-datavalue-parse-error": "O valor fornecido \"$1\" não foi compreendido.", "smw-datavalue-propertylist-invalid-property-key": "A lista de propriedades \"$1\" continha uma chave de propriedade inválida \"$2\".", "smw-datavalue-type-invalid-typeuri": "Não foi possível transformar \"$1\" numa representação válida de URI.", "smw-datavalue-wikipage-missing-fragment-context": "O valor de entrada \"$1\" da página wiki não pode ser usado sem uma página de contexto.", "smw-datavalue-wikipage-invalid-title": "O valor de entrada \"$1\" da página contém caracteres inválidos ou está incompleto e, portanto, pode causar resultados inesperados durante uma consulta ou processo de anotação.", "smw-datavalue-wikipage-property-invalid-title": "Propriedade \"$1\" (como tipo de página) com valor de entrada \"$2\" contém caracteres inválidos ou está incompleto e, portanto, pode causar resultados inesperados durante uma consulta ou processo de anotação.", "smw-datavalue-wikipage-empty": "O valor de entrada de página está vazio (por exemplo, <code>[[SomeProperty::]], [[]]</code>) e, portanto, não pode ser usado como nome ou como parte da condição de uma consulta.", "smw-type-ref-rec": "\"$1\" é um tipo de dados [https://www.semantic-mediawiki.org/wiki/Container recipiente] (''container'') que permite registrar informação adicional sobre a atribuição de um valor (por exemplo, dados de proveniência).", "smw-datavalue-reference-invalid-fields-definition": "O tipo [[Special:Types/Reference|Referência]] espera que seja declarada uma lista de propriedades utilizando a propriedade [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Has fields].", "smw-parser-invalid-json-format": "O analisador JSON terminou com um erro \"$1\".", "smw-property-preferred-label-language-combination-exists": "\"$1\" não pode ser usado como rótulo preferido porque o idioma \"$2\" já está atribuído ao rótulo \"$3\".", "smw-clipboard-copy-link": "Copiar link para a área de transferência", "smw-property-userdefined-fixedtable": "\"$1\" estava configurada como [https://www.semantic-mediawiki.org/wiki/Fixed_properties propriedade fixa] e qualquer modificação em sua [https://www.semantic-mediawiki.org/wiki/Type_declaration declaração de tipo] requer: ou que seja executado <code>setupStore.php</code>, ou que seja completada a tarefa especial [[Special:SemanticMediaWiki|\"Instalação e atualização da base de dados\"]].", "smw-data-lookup": "Obtendo dados...", "smw-data-lookup-with-wait": "A requisição está sendo processada e pode levar algum tempo.", "smw-no-data-available": "Nenhum dado disponível.", "smw-property-req-violation-missing-fields": "Faltam à propriedade \"$1\" a requerida declaração [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] para o tipo de dados \"$2\".", "smw-property-req-violation-multiple-fields": "A propriedade \"$1\" contém múltiplas (e, portanto, conflituosas) declarações [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>], apenas uma é esperada para este tipo de dado \"$2\".", "smw-property-req-violation-missing-formatter-uri": "Faltam à propriedade \"$1\" detalhes de declaração para o tipo de dados porque não foi definida a propriedade <code>External formatter URI</code>.", "smw-property-req-violation-predefined-type": "A propriedade \"$1\", como propriedade predefinida, contém uma declaração de tipo \"$2\" que é incompatível com o tipo de dados padrão desta propriedade.", "smw-property-req-violation-import-type": "Uma declaração de tipo de dados foi detectada, o que é incompatível com o tipo de dados predefinido importado pelo vocabulário \"$1\". Em geral, não é necessário declarar um tipo de dados porque essa informação provém da definição de importação.", "smw-property-req-violation-change-propagation-locked-error": "A propriedade \"$1\" foi alterada e requer que as entidades atribuídas sejam reavaliadas usando um processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações]. A página da propriedade foi trancada até que a atualização da especificação primária esteja finalizada para impedir interrupções intermediárias ou especificações contraditórias. O processo pode demorar algum tempo até que a página possa ser destrancada porque depende do tamanho e frequência da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas].", "smw-property-req-violation-change-propagation-locked-warning": "A propriedade \"$1\" foi alterada e requer que as entidades atribuídas sejam reavaliadas usando um processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações]. A atualização pode demorar algum tempo porque depende do tamanho e frequência da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas] e é sugerido que as alterações da propriedade sejam adiadas para impedir interrupções intermediárias ou especificações contraditórias.", "smw-property-req-violation-change-propagation-pending": "Estão pendentes atualizações devidas à [https://www.semantic-mediawiki.org/wiki/Special:MyLanguage/Change_propagation propagação de alterações] ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|tarefa estimada|tarefas estimadas}}]) e é recomendado que propriedades não sejam alteradas até que o processo esteja finalizado para impedir interrupções intermediárias ou especificações contraditórias.", "smw-property-req-violation-missing-maps-extension": "O Semantic MediaWiki não conseguiu detectar a extensão [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"], que é um pré-requisito e, consequentemente,  limita a funcionalidade (isto é, torna incapaz o armazenamento ou o processamento de dados geográficos) desta propriedade.", "smw-property-req-violation-type": "A propriedade contém especificações do tipo concorrentes que podem resultar em anotações de valor inválido, portanto, é esperado que um usuário atribua um tipo apropriado.", "smw-property-req-error-list": "A propriedade contém os seguintes erros ou avisos:", "smw-property-req-violation-parent-type": "A propriedade \"$1\" e a propriedade pai atribuída \"$2\" têm anotações de tipos diferentes.", "smw-property-req-violation-forced-removal-annotated-type": "A aplicação da [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance herança obrigatória do tipo da propriedade mãe] está ativada, o tipo anotado para a propriedade \"$1\" não corresponde ao tipo \"$2\" da sua propriedade mãe e foi alterado para refletir aquele requisito. É recomendado ajustar a definição do tipo na página, para que a mensagem de erro e aplicação obrigatória sejam removidas desta propriedade.", "smw-change-propagation-protection": "Esta página está trancada para evitar modificações acidentais dos dados durante uma atualização devida à [https://www.semantic-mediawiki.org/wiki/change_propagation propagação de alterações]. O processo pode demorar algum tempo a destrancar a página porque depende do tamanho e da frequência do agendador da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas].", "smw-category-change-propagation-locked-error": "A categoria \"$1\" foi alterada e requer que as entidades atribuídas sejam reavaliadas usando um processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações]. Entretanto, a página da categoria foi trancada até que a atualização da especificação primária esteja finalizada para impedir interrupções intermediárias ou especificações contraditórias. O processo pode demorar algum tempo até que a página possa ser destrancada porque depende do tamanho e frequência da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas].", "smw-category-change-propagation-locked-warning": "A categoria \"$1\" foi alterada e requer que as entidades atribuídas sejam reavaliadas usando um processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações]. A atualização pode demorar algum tempo porque depende do tamanho e frequência da [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue fila de tarefas] e é sugerido que adie alterações à categoria para impedir interrupções intermediárias ou especificações contraditórias.", "smw-category-change-propagation-pending": "Estão pendentes atualizações devidas à [https://www.semantic-mediawiki.org/wiki/Change_propagation propagação de alterações] ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|tarefa estimada|tarefas estimadas}}]) e é recomendado que aguarde até que o processo esteja finalizado antes de fazer alterações à categoria, para impedir interrupções intermediárias ou especificações contraditórias.", "smw-category-invalid-value-assignment": "\"$1\" não é reconhecido como categoria válida ou anotação de valor.", "protect-level-smw-pageedit": "Per<PERSON><PERSON> somente usuários com a permissão de edição de página (Semantic MediaWiki)", "smw-create-protection": "A criação da propriedade \"$1\" está restrita aos usuários que possuem a permissão \"$2\" (ou que estão em um [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuário] que possua tal permissão), enquanto o [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade] estiver ativo.", "smw-create-protection-exists": "A alteração da propriedade \"$1\" está restrita aos usuários com a permissão \"$2\" (ou que estão em um [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuário] que possua tal permissão), enquanto o [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridade] estiver ativo.", "smw-edit-protection": "Esta página está [[Property:Is edit protected|protegida]] para impedir a modificação acidental de dados e só pode ser editada por usuários com a permissão de edição \"$1\" apropriada ou que esteja em um [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuário] com tal permissão.", "smw-edit-protection-disabled": "A proteção contra edições foi desativada, portanto, a propriedade \"$1\" não pode ser usada para proteger páginas de entidades contra edições não autorizadas.", "smw-edit-protection-auto-update": "O Semantic MediaWiki atualizou a situação de proteção de acordo com a propriedade \"Is edit protected\".", "smw-edit-protection-enabled": "Protegida de edição (Semantic MediaWiki)", "smw-patternedit-protection": "Esta página está protegida e só pode ser editada pelos usuários com a [https://www.semantic-mediawiki.org/wiki/Help:Permissions permissão] <code>smw-patternedit</code>.", "smw-property-predefined-edip": "\"$1\" é uma propriedade predefinida, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para indicar se a página está protegida contra edições ou não.", "smw-property-predefined-long-edip": "Embora qualquer usuário possa adicionar esta propriedade a um sujeito, só um usuário com uma permissão específica pode editar ou revogar a proteção de uma entidade após essa proteção ter sido adicionada.", "smw-query-reference-link-label": "Referência de consulta", "smw-format-datatable-emptytable": "Nenhum dado disponível na tabela", "smw-format-datatable-info": "Exibindo _START_ até _END_ de _TOTAL_ linhas", "smw-format-datatable-infoempty": "Exibindo 0 até 0 de 0 linhas", "smw-format-datatable-infofiltered": "(filtrado a partir de _MAX_ linhas totais)", "smw-format-datatable-infothousands": ".", "smw-format-datatable-lengthmenu": "Exibir _MENU_ linhas", "smw-format-datatable-loadingrecords": "Carregando...", "smw-format-datatable-processing": "Processando…", "smw-format-datatable-search": "Pesquisar:", "smw-format-datatable-zerorecords": "Nenhuma correspondência encontrada", "smw-format-datatable-first": "<PERSON><PERSON>", "smw-format-datatable-last": "Último", "smw-format-datatable-next": "Próximo", "smw-format-datatable-previous": "Anterior", "smw-format-datatable-sortascending": ": ativar ordenação ascendente da coluna", "smw-format-datatable-sortdescending": ": ativar ordenação descendente da coluna", "smw-format-datatable-toolbar-export": "Exportar", "smw-category-invalid-redirect-target": "A categoria \"$1\" contém um alvo de redirecionamento inválido para um namespace que não é de categoria.", "smw-parser-function-expensive-execution-limit": "A função do analisador sintático atingiu o limite para execuções de funções exigentes (consulte o parâmetro de configuração [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "O Semantic MediaWiki está atualizando a página atual com base na condição de algum processamento pós-consulta necessário.", "apihelp-smwinfo-summary": "Módulo da API para obter informação sobre estatísticas e outra meta informação do Semantic MediaWiki.", "apihelp-ask-summary": "Módulo da API para consultar o Semantic MediaWiki usando a linguagem \"ask\".", "apihelp-askargs-summary": "Módulo da API para consultar o Semantic MediaWiki usando a linguagem \"ask\" na forma de lista de condições, propriedades a serem mostradas e parâmetros.", "apihelp-browsebyproperty-summary": "Módulo da API para obter informação sobre uma propriedade ou lista de propriedades.", "apihelp-browsebysubject-summary": "Módulo da API para obter informação sobre um sujeito.", "apihelp-smwtask-summary": "Módulo da API para executar tarefas relacionadas ao Semantic MediaWiki (somente para uso interno, não para uso público).", "apihelp-smwbrowse-summary": "Módulo de API para dar suporte a atividades de navegação para diferentes tipos de entidade na Semantic MediaWiki.", "apihelp-ask-parameter-api-version": "Formatação da saída:\n;2:Formato compatível com versões anteriores, usando {} para a lista de resultados.\n;3:Formato experimental, usando [] como lista de resultados.", "apihelp-smwtask-param-task": "Define o tipo de tarefa", "apihelp-smwtask-param-params": "Parâmetros codificados em JSON que correspondem ao requisito de tipo de tarefa selecionada", "smw-apihelp-smwtask-example-update": "Exemplo de execução de uma tarefa de atualização para um assunto específico:", "smw-api-invalid-parameters": "Parâ<PERSON><PERSON> inválid<PERSON>, \"$1\"", "smw-parser-recursion-level-exceeded": "O nível de $1 recursões foi excedido durante um processo de análise sintática. Sugere-se que valide a estrutura de predefinições, ou que ajuste o parâmetro de configuração <code>$maxRecursionDepth</code> se necessário.", "smw-property-page-list-count": "Exibindo $1 {{PLURAL:$1|página que utiliza|páginas que utilizam}} esta propriedade.", "smw-property-page-list-search-count": "A apresentar {{PLURAL:$1|uma página que usa|$1 páginas que usam}} esta propriedade com uma correspondência de valor \"$2\".", "smw-property-page-filter-note": "O [https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter filtro de busca] permite a inclusão de [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions expressões de consulta] tais como <code>~</code> ou <code>!</code>. O  [https://www.semantic-mediawiki.org/wiki/Query_engine mecanismo de consulta] selecionado pode também suportar buscas insensíveis a maiúsculas e minúsculas ou outras pequenas expressões como:\n\n* <code>in:</code> o resultado deve incluir o termo, por exemplo, '<code>in:Foo</code>'\n\n* <code>not:</code> o resultado não deve incluir o termo, por exemplo, '<code>not:Bar</code>'", "smw-property-reserved-category": "Categoria", "smw-category": "Categoria", "smw-datavalue-uri-invalid-scheme": " \"$1\" não foi listado como um esquema URI válido.", "smw-datavalue-uri-invalid-authority-path-component": "\"$1\" foi identificado para conter uma autoridade ou um componente de caminho \"$2\" inválido.", "smw-browse-property-group-title": "Grupo de propriedades", "smw-browse-property-group-label": "Nome do grupo de propriedades", "smw-browse-property-group-description": "Descrição do grupo de propriedades", "smw-property-predefined-ppgr": "\"$1\" é uma propriedade predefinida que identifica as entidades (principalmente as categorias) que são utilizadas como instâncias de agrupamento para as propriedades, e é fornecida pela extensão [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-filter": "Filtro", "smw-section-expand": "Expandir a seção", "smw-section-collapse": "Reduzir a seção", "smw-ask-format-help-link": "Formato [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Auxiliar", "smw-personal-jobqueue-watchlist": "Listagem de filas de trabalho", "smw-personal-jobqueue-watchlist-explain": "Os números indicam uma estimativa das entradas da fila de tarefas que aguardam execução.", "smw-property-predefined-label-skey": "Chave de ordenação", "smw-processing": "Processando...", "smw-loading": "Carregando...", "smw-fetching": "Buscando...", "smw-preparing": "Preparando...", "smw-expand": "Expandir", "smw-collapse": "<PERSON><PERSON><PERSON><PERSON>", "smw-copy": "Copiar", "smw-copy-clipboard-title": "Copia o conteúdo para a área de transferência", "smw-jsonview-expand-title": "Expande a exibição JSON", "smw-jsonview-collapse-title": "Recolhe a exibição JSON", "smw-jsonview-search-label": "Pesquisar:", "smw-redirect-target-unresolvable": "O destino é irresolúvel pela razão \"$1\"", "smw-types-title": "Tipo: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Não é permitido alterar o modelo de conteúdo de uma [https://www.semantic-mediawiki.org/wiki/Help:Schema página de esquema].", "smw-schema-namespace-edit-protection": "Esta página está protegida e só pode ser editada por utilizadores com a [https://www.semantic-mediawiki.org/wiki/Help:Permissions permissão] <code>smw-schemaedit</code> adequada.", "smw-schema-namespace-edit-protection-by-import-performer": "Esta página foi importada por um [https://www.semantic-mediawiki.org/wiki/Import_performer usuário de importação] listado e isso significa que a alteração desta página está restrita somente a estes usuários listados.", "smw-schema-error-title": "{{PLURAL:$1|erro|erros}} de validação", "smw-schema-error-schema": "O esquema de validação '''$1''' encontrou as seguintes inconsistências:", "smw-schema-error-miscellaneous": "Erro diverso ($1)", "smw-schema-error-validation-json-validator-inaccessible": "O validador de JSON \"<b>$1</b>\" não está acessível (ou instalado) e é a razão do porque o arquivo \"$2\" não pode ser examinado, o que evita que a página atual seja alterada ou gravada.", "smw-schema-error-validation-file-inaccessible": "O arquivo de validação \"$1\" é inacessível.", "smw-schema-error-type-missing": "Falta um tipo ao conteúdo para este ser reconhecido e utilizável no [https://www.semantic-mediawiki.org/wiki/Help:Schema espaço nominal/domínio do esquema].", "smw-schema-error-type-unknown": "O tipo \"$1\" não está registado e, portanto, não pode ser usado para conteúdo no espaço nominal do [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema]", "smw-schema-error-json": "Erro JSON: \"$1\"", "smw-schema-error-input": "A validação da entrada encontrou os seguintes problemas, que precisam de ser endereçados antes de poder gravar o conteúdo. A página de [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling ajuda sobre esquemas] pode conter alguns conselhos sobre como remover inconsistências ou resolver problemas com o esquema da entrada.", "smw-schema-error-input-schema": "O esquema de validação '''$1''' encontrou as seguintes inconsistências, e estas precisam de ser endereçadas antes de poder gravar o conteúdo. A página de [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling ajuda sobre esquemas] pode conter alguns conselhos sobre como resolver estes problemas.", "smw-schema-error-title-prefix": "Esse tipo de esquema requer que o título do esquema comece com um prefixo \"$1\".", "smw-schema-validation-error": "O tipo \"$1\" não está registado e, portanto, não pode ser usado para conteúdo no espaço nominal do [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema]", "smw-schema-validation-schema-title": "Esquema JSON", "smw-schema-summary-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-schema-title": "Esquema", "smw-schema-usage": "<PERSON><PERSON>", "smw-schema-type": "T<PERSON><PERSON> de <PERSON>", "smw-schema-type-description": "Descrição de tipo", "smw-schema-description": "Descrição do esquema", "smw-schema-description-link-format-schema": "Este tipo de esquema permite a definição de características para criar hiperligações dependentes do contexto com relação a uma propriedade atribuída de [[Property:Formatter schema|esquema formatador]].", "smw-schema-description-search-form-schema": "Este tipo de esquema permite a definição de formulários de entrada, e características para o perfil de [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch pesquisa avançada], onde este contém instruções para gerar campos de introdução de dados, definir espaços nominais/domínios padrão, ou declarar expressões prefixo para um pedido de pesquisa.", "smw-schema-description-property-profile-schema": "Esse tipo de esquema suporta a definição de um perfil para declarar características para a propriedade atribuída e seus valores de anotação.", "smw-schema-description-facetedsearch-profile-schema": "Este tipo de esquema suporta a definição de perfis usados como parte do ambiente da [[Special:FacetedSearch|Busca facetada]]", "smw-schema-description-property-group-schema": "Este tipo de esquema suporta a definição de [https://www.semantic-mediawiki.org/wiki/Help:Property_group grupos de propriedades] para ajudarem a estruturar a interface de [https://www.semantic-mediawiki.org/wiki/Help:Special:B<PERSON>e navegação].", "smw-schema-description-property-constraint-schema": "Isso suporta a definição de regras de restrição para uma instância de propriedade, bem como os valores atribuídos a ela.", "smw-schema-description-class-constraint-schema": "Este tipo de esquema permite a definição de regras de restrição para uma instância de classe (também designada categoria).", "smw-schema-tag": "{{PLURAL:$1|Etiqueta|Etiquetas}}", "smw-property-predefined-constraint-schema": "\"$1\" é uma propriedade predefinida que define um esquema de restrição e é fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-desc": "\"$1\" é uma propriedade predefinida que armazena uma descrição de esquema, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-def": "\"$1\" é uma propriedade predefinida que que armazena o conteúdo do esquema, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-tag": "\"$1\" é uma propriedade predefinida fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] para identificar um conjunto de esquemas.", "smw-property-predefined-long-schema-tag": "Uma etiqueta que identifica esquemas de conteúdos ou características semelhantes.", "smw-property-predefined-schema-type": "\"$1\" é uma propriedade predefinida que descreve um tipo para distinguir um grupo de esquemas, fornecida pelo [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-schema-type": "Cada [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type tipo] fornece a sua própria interpretação dos elementos sintáticos e das regras de aplicação, e pode ser expresso com a ajuda de um [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation esquema de validação].", "smw-ask-title-keyword-type": "Pesquisa por palavra-chave", "smw-ask-message-keyword-type": "Esta pesquisa coincide com a condição <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Não foi possível conectar ao alvo remoto \"$1\".", "smw-remote-source-disabled": "A fonte \"$1\" desabilitou o suporte às solicitações remotas!", "smw-remote-source-unmatched-id": "A fonte '''$1''' não corresponde a uma versão do Semantic MediaWiki que suporte uma solicitação remota.", "smw-remote-request-note": "O resultado é buscado a partir da fonte remota '''$1''' e é provável que o conteúdo gerado contenha informações que não estão disponíveis no wiki atual.", "smw-remote-request-note-cached": "O resultado é '''armazenado em cache''' a partir da fonte remota '''$1''' e é provável que o conteúdo gerado contenha informações que não estão disponíveis no wiki atual.", "smw-parameter-missing": "O parâmetro \"$1\" está ausente.", "smw-property-tab-usage": "<PERSON><PERSON>", "smw-property-tab-profile-schema": "Esquema de perfil", "smw-property-tab-redirects": "Sinônimos", "smw-property-tab-subproperties": "Subpropriedades", "smw-property-tab-errors": "Atribuições impróprias", "smw-property-tab-constraint-schema": "Esquema de restrição", "smw-property-tab-constraint-schema-title": "Esquema de restrição compilado", "smw-property-tab-specification": "... mais", "smw-concept-tab-list": "Lista", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-ask-tab-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-extra": "Extra", "smw-ask-tab-debug": "<PERSON><PERSON><PERSON>", "smw-ask-tab-code": "Código", "smw-install-incomplete-tasks-title": "Tarefas de administração incompletas", "smw-install-incomplete-intro": "Há $2 {{PLURAL:$2|tarefa incompleta ou [[Special:PendingTaskList|pendente]]|tarefas incompletas ou [[Special:PendingTaskList|pendentes]]}} para finalizar a {{PLURAL:$1|instalação|atualização}} do [https://www.semantic-mediawiki.org Semantic MediaWiki]. Um administrador ou usuário com permissões suficientes pode {{PLURAL:$2|completá-la|completá-las}}. Isso deve ser feito antes da adição de novos dados para evitar inconsistências.", "smw-install-incomplete-intro-note": "Esta mensagem desaparecerá após todas as tarefas relevantes terem sido resolvidas.", "smw-pendingtasks-intro-empty": "Nenhuma tarefa foi classificada como pendente, incompleta ou pendente em conexão com o Semantic MediaWiki.", "smw-pendingtasks-intro": "Esta página fornece informações sobre tarefas que foram classificadas como pendentes, incompletas ou pendentes em conexão com o Semantic MediaWiki.", "smw-pendingtasks-setup-no-tasks-intro": "A instalação (ou atualização) foi concluída, atualmente não há tarefas pendentes ou pendentes.", "smw-pendingtasks-tab-setup": "Configuração", "smw-updateentitycollation-incomplete": "A configuração <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> foi alterada recentemente e requer que o script <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> seja executado para que as entidades sejam atualizadas e contenham o valor do campo de ordenação correto.", "smw-updateentitycountmap-incomplete": "O campo <code>smw_countmap</code> foi adicionado em uma versão recente e requer que o script <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> seja executado para que as funções possam acessar o conteúdo deste campo.", "smw-populatehashfield-incomplete": "O passo de preenchimento do campo <code>smw_hash</code> foi pulado durante a configuração. É necessário executar o ''script'' <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-populate-hash-field": "O passo de preenchimento do campo <code>smw_hash</code> foi pulado durante a configuração. É necessário executar o ''script'' <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code>.", "smw-install-incomplete-elasticstore-indexrebuild": "O <code>ElasticStore</code> foi selecionado como [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore armazenamento padrão], no entanto, a extensão foi incapaz de encontrar qualquer registro de que o script <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> foi executado, por favor, execute o script conforme as instruções.", "smw-elastic-rebuildelasticindex-run-incomplete": "O <code>ElasticStore</code> foi selecionado como [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore armazenamento padrão], no entanto, a extensão foi incapaz de encontrar qualquer registro de que o script <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> foi executado, por favor, execute o script conforme as instruções.", "smw-pendingtasks-setup-intro": "A {{PLURAL:$1|instalação|atualização}} do <b>Semantic MediaWiki</b> classificou as seguintes tarefas como [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade incompletas] e um administrador (ou usuário com permissões suficientes) é esperado para resolver essas tarefas antes que os usuários continuem a criar ou a alterar o conteúdo.", "smw-pendingtasks-setup-tasks": "<PERSON><PERSON><PERSON><PERSON>", "smw-filter-count": "Contagem de filtros", "smw-es-replication-check": "Situação da replicação (Elasticsearch)", "smw-es-replication-error": "Problema de replicação do Elasticsearch", "smw-es-replication-file-ingest-error": "Problema na entrada de arquivo", "smw-es-replication-maintenance-mode": "Manutenção do Elasticsearch", "smw-es-replication-error-missing-id": "O monitoramento de replicação descobriu que o artigo \"$1\" (ID: $2) está faltando no back-end do Elasticsearch.", "smw-es-replication-error-divergent-date": "O monitoramento de replicação descobriu que, para o artigo \"$1\" (ID: $2), a <b>data de modificação</b> mostra uma discrepância.", "smw-es-replication-error-divergent-date-short": "As seguintes informações de data foram usadas para comparação:", "smw-es-replication-error-divergent-date-detail": "Data de modificação referenciada:\n*Elasticsearch: $1 \n*Database: $2", "smw-es-replication-error-divergent-revision": "O monitoramento de replicação descobriu que, para o artigo \"$1\" (ID: $2), a data de modificação mostra uma discrepância.", "smw-es-replication-error-divergent-revision-short": "Os seguintes dados de revisão associados foram usados para comparação:", "smw-es-replication-error-divergent-revision-detail": "Revisão associada referenciada:\n*Elasticsearch: $1 \n*Database: $2", "smw-es-replication-error-maintenance-mode": "A replicação do Elasticsearch está atualmente restrita porque está operando em um [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>modo de manutenção</b>], alterações em entidades e páginas <b>não</b> estão imediatamente visíveis e os resultados de consultas podem conter informações desatualizadas.", "smw-es-replication-error-no-connection": "O monitoramento de replicação não pode executar nenhuma verificação, pois não pode estabelecer uma conexão com o cluster Elasticsearch.", "smw-es-replication-error-bad-request-exception": "O manipulador de conexão do Elasticsearch lançou uma exceção de solicitação incorreta (\"Erro 400 de conflito HTTP\") indicando um problema contínuo durante solicitações de replicação e pesquisa.", "smw-es-replication-error-other-exception": "O manipulador de conexões do Elasticsearch lançou uma exceção: \"$1\".", "smw-es-replication-error-suggestions": "Sugere-se editar ou limpar a página para remover a discrepância. Se o problema persistir, verifique o próprio cluster do Elasticsearch (alocador, exceções, espaço em disco, etc.).", "smw-es-replication-error-suggestions-maintenance-mode": "Sugere-se entrar em contato com o administrador da wiki para verificar se está a ocorrer uma [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild recriação do índice] ou se o <code>refresh_interval</code> não foi definido com o valor por omissão esperado.", "smw-es-replication-error-suggestions-no-connection": "É sugerido que contacte o administrador da wiki e reporte o problema \"falta de ligação\".", "smw-es-replication-error-suggestions-exception": "Por favor, verifique os registros para obter informações sobre o status do Elasticsearch, seus índices e possíveis problemas de configuração incorreta.", "smw-es-replication-error-file-ingest-missing-file-attachment": "O monitoramento de replicação descobriu que \"$1\" está faltando uma marcação de [[Property:File attachment|carregamento de arquivo]] indicando que o processador de entrada de arquivo não foi iniciado ou não está finalizado.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Por favor, assegure-se de que a tarefa de [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion submissão de arquivos] esteja programada e seja executada antes que a anotação que e o índice do arquivo estejam disponíveis.", "smw-report": "Reportar", "smw-legend": "<PERSON>a", "smw-datavalue-constraint-schema-category-invalid-type": "O esquema anotado \"$1\" é inválido para uma categoria; requer um tipo \"$2\".", "smw-datavalue-constraint-schema-property-invalid-type": "O esquema anotado \"$1\" é inválido para uma propriedade; requer um tipo \"$2\".", "smw-entity-examiner-check": "Executando {{PLURAL:$1|um examinador|examinadores}} em segundo plano", "smw-entity-examiner-indicator": "Painel de problema da entidade", "smw-entity-examiner-deferred-check-awaiting-response": "O examinador \"$1\" está aguardando uma resposta do back-end.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "Restrição", "smw-entity-examiner-associated-revision-mismatch": "<PERSON><PERSON><PERSON>", "smw-entity-examiner-deferred-fake": "<PERSON><PERSON><PERSON>", "smw-entity-examiner-indicator-suggestions": "Como parte de um exame de entidade, {{PLURAL:$1|o seguinte problema foi encontrado|os seguintes problemas foram encontrados}}, sugere-se uma revisão cuidadosa {{PLURAL:$1|dele|deles}} e {{PLURAL:$1|uma ação apropriada|ações apropriadas}}.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Restrição|Restrições}}", "smw-indicator-revision-mismatch": "<PERSON><PERSON><PERSON>", "smw-indicator-revision-mismatch-error": "A verificação da [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner revisão associada] encontrou uma incompatibilidade entre a revisão mencionada no MediaWiki e a que está sendo associada no Semantic MediaWiki para esta entidade.", "smw-indicator-revision-mismatch-comment": "Uma incompatibilidade normalmente indica que algum processo interrompeu a operação de armazenamento do Semantic MediaWiki. É recomendado revisar os logs do servidor e procurar por erros ou outras falhas.", "smw-facetedsearch-intro-text": "A [https://www.semantic-mediawiki.org/wiki/Faceted_search <b>Busca facetada</b>] do Semantic MediaWiki fornece aos usuários uma simples interface para rapidamente especificar resultados de consultas a partir de uma condição com a ajuda de filtros criados por propriedades e categorias dependentes.", "smw-facetedsearch-intro-tips": "* Use <code>category:?</code>, <code>property:?</code>, ou <code>concept:?</code> para encontrar categorias, propriedades ou conceitos disponíveis para criar um conjunto de condições\n* Use a sintaxe #ask para descrever uma condição (por exemplo: <code><nowiki>[[Category:Foo]]</nowiki></code>)\n* Use \"OR\", \"AND\" ou outras expressões de consultas para criar condições complexas\n* Expressões como <code>in:</code> ou <code>phrase:</code> podem ser usadas para correspondências em texto completo ou buscas não estruturadas, se o [https://www.semantic-mediawiki.org/wiki/Query_engine motor de consultas] suportar tais expressões", "smw-facetedsearch-profile-label-default": "<PERSON><PERSON><PERSON>", "smw-facetedsearch-intro-tab-explore": "Explorar", "smw-facetedsearch-intro-tab-search": "<PERSON><PERSON><PERSON><PERSON>", "smw-facetedsearch-explore-intro": "Selecione uma coleção e comece a navegar.", "smw-facetedsearch-profile-options": "Opções de perfil", "smw-facetedsearch-size-options": "Opções de paginação", "smw-facetedsearch-order-options": "Opções de ordenação", "smw-facetedsearch-format-options": "Opções de exibição", "smw-facetedsearch-format-table": "<PERSON><PERSON><PERSON>", "smw-facetedsearch-input-filter-placeholder": "Filtrar por...", "smw-facetedsearch-no-filters": "Sem filtros.", "smw-facetedsearch-no-filter-range": "Sem extensão de filtro.", "smw-facetedsearch-no-output": "Para o formato selecionado \"$1\", nenhuma saída estava disponível.", "smw-facetedsearch-clear-filters": "Remover {{PLURAL:$1|filtro|filtros}}", "smw-search-placeholder": "Pesquisar...", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "A seguir {{PLURAL:$1|é mostrado '''um''' resultado|são mostrados até '''$1''' resultados}}, iniciando no '''$2'''º."}