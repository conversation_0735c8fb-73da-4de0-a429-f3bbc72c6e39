{"@metadata": {"authors": ["Adam78", "Bencemac", "<PERSON>", "David92003", "Dj", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TK-999", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "아라"]}, "smw-desc": "A wiki elérhe<PERSON><PERSON><PERSON><PERSON> tétel<PERSON> – gépek ''és'' ember<PERSON> s<PERSON> is ([https://www.semantic-mediawiki.org/wiki/Help:User_manual online dokumentáció])", "smw-error": "Hiba", "smw_viewasrdf": "RDF hírcsatorna", "smw_finallistconjunct": ", és", "smw_isspecprop": "Ez a tulajdonság különleges ebben a wikiben.", "smw_concept_description": "A(z) „$1” koncepció leírása", "smw_no_concept_namespace": "Koncepciókat csak a ''<PERSON><PERSON><PERSON><PERSON>ó:'' névtérben levő lapokon lehet megadni.", "smw_multiple_concepts": "Minden koncepció lapon csak egy koncepció definíciója szerepelhet.", "smw_concept_cache_miss": "A(z) „$1” koncepció nem használható pill<PERSON>, mivel a wiki konfigurációja szerint kapcsolat nélküli módban kell kiszámítani.\nHa a probléma nem szűnik meg bizonyos id<PERSON>teltével, kérd az oldal adminisztrátorát hogy tegye elérhetővé a koncepciót.", "smw_noinvannot": "Inverz tulajdonságokhoz nem lehet értékeket rendelni.", "version-semantic": "Szemantikai kiterjesztések", "smw_baduri": "„$1” formájú URI-k nem engedélyezettek.", "smw_printername_count": "<PERSON><PERSON><PERSON><PERSON><PERSON> megszámlálása", "smw_printername_csv": "CSV exportálás", "smw_printername_dsv": "DSV formátumú exportálás", "smw_printername_debug": "Lekérdezés hibakeresése (szakértőknek)", "smw_printername_embedded": "Lap tartalmának beágyazása", "smw_printername_json": "JSON exportálás", "smw_printername_list": "Lista", "smw_printername_ol": "Számozott lista", "smw_printername_ul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lista", "smw_printername_table": "T<PERSON>b<PERSON><PERSON><PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_template": "Sablon", "smw_printername_rdf": "RDF formátumú exportálás", "smw_printername_category": "Kategória", "validator-type-class-SMWParamSource": "szöveg", "smw-paramdesc-limit": "Legfeljebb ennyi eredmény megjelenítése", "smw-paramdesc-headers": "Fejlécek/tulajdonságnevek megjelenítése", "smw-paramdesc-mainlabel": "A kezdőlap nevének címkéje", "smw-paramdesc-link": "Az értékek hivatkozásként jelenjenek meg", "smw-paramdesc-intro": "A lekérdezés eredményei előtt megjelenő szöveg", "smw-paramdesc-outro": "A lekérdezés eredményei után megjelenő szöveg", "smw-paramdesc-default": "Megjelenítend<PERSON> szöveg, ha a lekérdezésnek nincs eredménye", "smw-paramdesc-sep": "<PERSON>z eredmények közti elválasztó", "smw-paramdesc-distribution": "Az összes érték megjelenítése helyett számolja meg az előfordulásukat, és mutassa a<PERSON>.", "smw-paramdesc-distributionsort": "Értékszórás rendezése előfordulások száma szerint.", "smw-paramdesc-distributionlimit": "Értékszórás korlátozása néhány érték előfordulásának számára", "smw-paramdesc-template": "<PERSON><PERSON> sa<PERSON> neve, am<PERSON><PERSON> a kiírás", "smw-paramdesc-columns": "A keresési eredmények oszlopainak száma", "smw-paramdesc-introtemplate": "A lekérdezés eredményei <PERSON>tt (ha vannak) megjelenítendő sablon neve", "smw-paramdesc-outrotemplate": "A lekérdezés eredményei után (ha vannak) megjelenítendő sablon neve", "smw-paramdesc-embedformat": "A címsorokhoz használt HTML-tag", "smw-paramdesc-embedonly": "Fejlécek kikapcsolása", "smw-paramdesc-table-class": "A táblázathoz beállítandó extra CSS osztály", "smw-paramdesc-rdfsyntax": "A használandó RDF-változat", "smw-paramdesc-csv-sep": "Add meg az oszlopelválasztó-jelet", "smw-paramdesc-dsv-separator": "A használandó elválasztójel", "smw-paramdesc-dsv-filename": "A DSV-fájl neve", "smw-smwdoc-description": "<PERSON><PERSON> t<PERSON>lázatot jelenít meg azon összes paraméterrel, amelyek az alapértékekkel és -leírásokkal együtt használhatóak a megadott célformátumban.", "smw-smwdoc-par-format": "A célfrm<PERSON>tum, amelynek paramétereinek dokumentációját meg kell jeleníteni.", "smw-smwdoc-par-parameters": "A megjelenítendő paraméterek. \"specific\" a formátum által hozzáadottakhoz, \"base\" a minden formátumban elérhetőkhöz és \"all\" mindkét fajtához.", "smw-paramdesc-sort": "A t<PERSON>, amely szerint rendezni kell a lekérdezést", "smw-paramdesc-order": "A lekérdezések rendezési sorrendje", "smw-paramdesc-searchlabel": "Szöveg a keresés folytatásához", "smw-paramdesc-named_args": "Nevezd meg a sablonba átküldendő argumentumokat", "smw_iq_disabled": "A szemantikus lekérdezések le vannak tiltva ezen a wikin.", "smw_iq_moreresults": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_parseerror": "A megadott érték nem érthető.", "smw_notitle": "A(z) „$1” nem has<PERSON> lap nevek<PERSON>t ebben a wikiben.", "smw_noproperty": "A(z) \"$1\" nem használható tulajdonság neveként ebben a wikiben.", "smw_wrong_namespace": "Csak a(z) „$1” névtérbeli lapok engedélyezettek itt.", "smw_manytypes": "<PERSON><PERSON><PERSON><PERSON><PERSON> több megadott típus a tulajdonsághoz.", "smw_emptystring": "Üres sztringek nem elfogadhatóak.", "smw_notinenum": "„$1” nincs az engedélyezett értékek listájában ($2) a „$3” tulajdonságnak.", "smw_noboolean": "„$1” nem értelmezhető mint logikai (igaz/hamis) érték", "smw_true_words": "igaz,igen,i", "smw_false_words": "hamis,nem,n", "smw_nofloat": "„$1” nem egy szám.", "smw_infinite": "Ilyen nagy számok („$1”) nem támogatottak.", "smw_unitnotallowed": "A(z) \"$1\" nem érvényes mértékegysége ennek a tulajdonságnak.", "smw_nounitsdeclared": "<PERSON><PERSON><PERSON><PERSON><PERSON>va mértékegység ehhez a tulajdonsághoz.", "smw_novalues": "Nincsenek megadva értékek.", "smw_nodatetime": "A(z) „$1” dátum nem értelmezhető.", "smw_toomanyclosing": "A(z) „$1” túl so<PERSON> fordul elő a lekérdezésben.", "smw_noclosingbrackets": "A lekérdezésben szerepelnek nyitó szögletes zárójelek „(<nowiki>[[</nowiki>)” a lezáró párju<PERSON> („]]”) nélkül.", "smw_misplacedsymbol": "A(z) „$1” szimbólum egy olyan helyen volt használva, ahol nincs haszna.", "smw_unexpectedpart": "A lekérdezés „$1” része nem értelmezhető.\nAz eredmények eltérhetnek a várttól.", "smw_emptysubquery": "<PERSON><PERSON><PERSON> allekérdezés nem tartalmaz érvényes felt<PERSON>t.", "smw_misplacedsubquery": "Allekérdezés volt egy olyan he<PERSON>, ahol nem engedélyezettek az allekérdezések.", "smw_valuesubquery": "A(z) „$1” tulajdonság értékeinél nem támogatottak az allekérdezések.", "smw_badqueryatom": "A lekérdezés egy r<PERSON> („<nowiki>[[…]]</nowiki>”) nem sikerült értelmezni.", "smw_propvalueproblem": "A(z) „$1” tulajdonság értéke nem értelmezhető.", "smw_noqueryfeature": "A lekérdezés egyes részei nem támogatottak ebben a wikiben, így a lekérdezés egy része el lett dobva ($1).", "smw_noconjunctions": "A konjunkció a lekérdezésekben nem támogatott ebben a wikiben, így a lekérdezés egy része el lett dobva ($1).", "smw_nodisjunctions": "A diszjunkció a lekérdezésekben nem támogatott ebben a wikiben, így a lekérdezés egy része el lett dobva ($1).", "smw_querytoolarge": "A következő {{PLURAL:$2|lekérdezés-feltétel nem lett|lekérdezés-feltételek nem lettek}} figyelembe véve a wikin érvényes lekérdezésméret- vagy -mélység-korlátozások miatt: <code>$1</code>.", "smw_notemplategiven": "Adj értéket a lekérdezés „paramétersablonjának”, hogy működjön ez a lekérdezésformátum.", "smw_db_sparqlqueryproblem": "A lekérdezés eredméynét nem tudtuk begyűjteni a SPARQL adatbázisból. Ez a hiba ideiglenes, de az adatbázisszoftver hibáj<PERSON><PERSON> is j<PERSON><PERSON><PERSON><PERSON>.", "smw_db_sparqlqueryincomplete": "A lekérdezés megválaszolása túl nehéznek bizonyult és megszakadt. Néhány eredmény hiányozhat. Ha lehet, próbálkozz egyszerűbb lekérdezéssel.", "smw_type_header": "A(z) „$1” t<PERSON><PERSON> t<PERSON>", "smw_typearticlecount": "{{PLURAL:$1|Egy|$1}} tulajdonság megjelenítése ezen típus hasz<PERSON>.", "smw_attribute_header": "A(z) „$1” tulajdonságot haszná<PERSON>ó <PERSON>ok", "smw_attributearticlecount": "{{PLURAL:$1|Egy|$1}} lap megjelení<PERSON><PERSON> ezen tulajdons<PERSON>g <PERSON>.", "specialpages-group-smw_group": "Szemantikus MediaWiki", "exportrdf": "Lapok exportálása RDF-be", "smw_exportrdf_docu": "Ez a lap lehetőséget teremt adatok beszerzésére egy lapról RDF formátumban.\nA lapok exportálásához írd be a címeiket az alábbi szövegdobozba, soronként egyet.", "smw_exportrdf_recursive": "Az összes kapcsolódó lap rekurzív exportálása.\nAz eredmény elég nagy lehet!", "smw_exportrdf_backlinks": "Kimenti az összes lapot, ami hivatkozik az exportált lapokra.\nBöngészhető RDF-et készít.", "smw_exportrdf_lastdate": "<PERSON>e exportá<PERSON><PERSON>, amelyek nem változtak mióta a megadott időpont óta.", "smw_exportrdf_submit": "Exportálás", "uriresolver": "URI feloldó", "properties": "Tulajdonságok", "smw-categories": "Kate<PERSON><PERSON><PERSON><PERSON>", "smw_properties_docu": "A wikiben az alábbi tulajdonságok használatosak.", "smw_property_template": "$2 típusú $1 ($3 használat)", "smw_propertylackspage": "Minden tulajdonságnak kell hogy legyen leírólapja!", "smw_propertylackstype": "Nincs típus megadva ehhez a tulajdonsághoz ($1 típus feltételezése).", "smw_propertyhardlyused": "Ez a tulajdonság aligha használatos a wikiben!", "smw-sp-property-searchform": "A következőt tartalmazó tulajdonságok listázása:", "unusedproperties": "<PERSON><PERSON>", "smw-unusedproperties-docu": "A következő tulajdonságok habár l<PERSON>k, de egyetlen lap sem használja ő<PERSON>.", "smw-unusedproperty-template": "$2 típusú $1", "wantedproperties": "<PERSON><PERSON><PERSON>", "smw-wantedproperties-docu": "A következő tulajdonságok használva vannak a wikiben, azoban még nem rendelkeznek leí<PERSON><PERSON> lappal.", "smw-wantedproperty-template": "$1 ($2 he<PERSON><PERSON>)", "smw_purge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "types": "Típusok", "smw_types_docu": "Ez a tulajdonságokhoz rendelhető adattípusok listáját tartalmazza.", "smw-statistics": "Szemantikus statisztikák", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Adattípus|Adattípusok}}]]", "smw_uri_doc": "Az URI-feloldó implementálja a [$1 W3C TAG finding on httpRange-14]-ben me<PERSON>.\nBiztosítja, hogy az emberek ne váljanak weboldalakká.", "ask": "Szemantikus keresés", "smw_ask_sortby": "Rendezés oszlopok szerint (nem kötelező)", "smw_ask_ascorder": "Növekvő", "smw_ask_descorder": "Csökkenő", "smw_ask_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "smw_ask_editquery": "Lekérdezés szerkesztése", "smw_add_sortcondition": "[Rendezési feltétel hozzáadása]", "smw_ask_hidequery": "Lekérdezés elrejtése (kompakt nézet)", "smw_ask_help": "Lekérdezések súgója", "smw_ask_queryhead": "Feltétel", "smw_ask_printhead": "További megjelenítendő adatok", "smw_ask_printdesc": "(egy tula<PERSON> adj meg soron<PERSON>)", "smw_ask_format_as": "Formázás mint:", "smw_ask_defaultformat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_otheroptions": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_show_embed": "Beágyazási kód megjelenítése", "smw_ask_hide_embed": "Beágyazási kód <PERSON>", "smw_ask_embed_instr": "Ha egy wikilapra szeretnéd beilleszteni ezt a lekérdezést, másold be az alábbi kódo<PERSON>.", "smw-ask-delete": "Eltávolítás", "smw-ask-sorting": "Rendez<PERSON>", "smw-ask-search": "Keresés", "searchbyproperty": "Keres<PERSON> t<PERSON> szer<PERSON>", "smw_sbv_docu": "Az összes olyan lap megkeresése, ami a megadott tulajdonsággal és értékkel rendelkezik.", "smw_sbv_novalue": "Add meg a tulajdonság egy érvényes értékét, vagy nézd meg a(z) „$1” összes tulajdonság-értékét.", "smw_sbv_displayresultfuzzy": "Az összes olyan lap <PERSON>, melyeknél a(z) „$1” tulajdonság a(z) „$2” értéket veszi fel.\n<PERSON><PERSON> csak néhány ta<PERSON>, a közeli értékek is meg vannak jelenítve.", "smw_sbv_property": "Tulajdonság:", "smw_sbv_value": "Érték:", "smw_sbv_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "browse": "Wiki tallózása", "smw_browselink": "Tulajdonságok böngészése", "smw_browse_article": "Add meg a lap nev<PERSON><PERSON>, ahonnan el szeretnéd kezdeni a böngészést.", "smw_browse_go": "<PERSON><PERSON>", "smw_browse_show_incoming": "Bejövő tulajdonságok mutatása", "smw_browse_hide_incoming": "Bejövő tulajdonságok elrejtése", "smw_browse_no_outgoing": "<PERSON><PERSON>ez a laphoz nem tartoznak tulajdonságok.", "smw_browse_no_incoming": "<PERSON><PERSON> t<PERSON> sem hivatkozik erre a lapra.", "smw_inverse_label_default": "$1", "smw_inverse_label_property": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> címkéjének felcserélése", "pageproperty": "Laptulajdon<PERSON><PERSON><PERSON> k<PERSON>", "smw_pp_docu": "Vagy írj be egy oldalt és egy tulajdon<PERSON>, vagy csak egy tulajdon<PERSON><PERSON><PERSON> ahhoz, hogy visszahozz minden hozzárendelt értéket-", "smw_pp_from": "Ettől a laptól:", "smw_pp_type": "Tulajdonság:", "smw_pp_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "smw_result_prev": "Előző", "smw_result_next": "Következő", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "<PERSON><PERSON><PERSON>.", "smwadmin": "Adminisztrációs és karbantartási funkciók", "smw-admin-setupsuccess": "A tárolómotor be lett állítva.", "smw_smwadmin_return": "Vissza ide: $1", "smw_smwadmin_updatestarted": "Egy új frissítő folyamat indult a szemantikus adatok frissítése céljából.\nMinden tárolt adat újra elő lesz állítva, és javítva lesz, ahol szükséges.\nA frissítési folyamat előrehaladását ezen a speciális lapon követheted figyelemmel.", "smw_smwadmin_updatenotstarted": "Már fut egy frissítő folyamat.\nNem kell ú<PERSON> készíteni.", "smw_smwadmin_updatestopped": "Minden futó frissítési foly<PERSON>.", "smw_smwadmin_updatenotstopped": "Az éppen futó frissítési folyamat megszakításához aktiválnod kell a jelölőnégyzetet, hogy j<PERSON>z<PERSON>, tényleg biztos vagy benne.", "smw-admin-docu": "Ez a speciális lap segít a <a href=\"https://semantic-mediawiki.org\">Szemantikus MediaWiki</a> telepítése, karbantartása, használata és frissítése során.\nNe felejts el másolatot készíteni az értékes adatokról az adminisztratív funkciók használata előtt!", "smw-admin-db": "Adatbázis-karbantartás", "smw-admin-dbdocu": "A Szemantikus MediaWikinek szüksége van néhány kiterjesztésre az adatbázison a szemantikus adatok tárolásához.\nAz alábbi funkció ellenőrzi, hogy az adatbázis megfelelően be van-e állítva.\nAz ezen lépés során végrehajtott változások nincsenek hatással a MediaWiki adatbázisának többi részére, és egyszerűen visszavonható, amennyiben szükséges.\nEz a telepítési lépés többször is végrehajtható, an<PERSON><PERSON><PERSON><PERSON>, hogy b<PERSON><PERSON><PERSON><PERSON> kárt tenne, de csak egyszer szükséges a telepítés vagy frissítés során.", "smw-admin-permissionswarn": "Ha a művelet SQL-hib<PERSON><PERSON> leáll, a wikid által használt adatbázis-felhasz<PERSON><PERSON>ó (amit a LocalSettings.php-ben adtál meg) valószínűleg nem rendelkezik a megfelelő jogosultságokkal.\nAdj a felhasználó számára táblák készítéséhez és törléséhez jogosultságot, ideiglenesen add meg a root fiók adatait, vagy használd az <code>setupStore.php</code> karbantart<PERSON> parancsfájlt, ami egy adminisztrátor adataival lép be.", "smw-admin-dbbutton": "Táblák inicializálása vagy frissí<PERSON>se", "smw-admin-announce": "Wiki bejelentése", "smw_smwadmin_datarefresh": "Adatok javítása és aktualizálása", "smw_smwadmin_datarefreshdocu": "A wiki jelenlegi tartalma alapján lehetőség van az összes Szemantikus MediaWiki-adat helyreállítására.\nEz hasznos lehet a sérült adatok javításakor, vagy az adatok frissítésekor, ha a belső formátum megváltozott szoftverfrissítés miatt.\nA frissítés oldalról oldalra van végrehajtva, és nem lesz azonnal kész.\nAlább látható, hogy jelenleg folyamatban van-e ilyen frissítés, és elindíthatod vagy leállíthatod a frissítéseket (kivéve, ha az oldal adminisztrátora letiltotta ezt a lehetőséget).", "smw_smwadmin_datarefreshprogress": "<strong><PERSON><PERSON><PERSON><PERSON><PERSON> foly<PERSON>.</strong>\n<PERSON><PERSON><PERSON><PERSON>, ha a friss<PERSON>tés lassan halad, hiszen az adatok csak egy kis részét frissíti minden alkalommal, ha egy fel<PERSON>z<PERSON><PERSON><PERSON> hozzáfér a wikihez.\nHa gyorsabban be szeretnéd fejezni a frissítést, hívd meg a <code>runJobs.php</code> karbantartó parancsfájlt (használd a <code>--maxjobs 1000</code> kapcsolót az egy menetben végrehajtott frissítések korlátozásához).\nA jelenlegi frissítés becsült előrehaladása:", "smw_smwadmin_datarefreshbutton": "Adatok frissítésének ütemzése", "smw_smwadmin_datarefreshstop": "Frissí<PERSON>s <PERSON>", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, {{GENDER:$1|biztos}} vag<PERSON>k benne.", "smw-admin-support": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>rek", "smw-admin-supportdocu": "K<PERSON>lönb<PERSON><PERSON><PERSON>, amelyek segíthetnek problémák esetén:", "smw-admin-installfile": "Ha problémákat észlelsz a telepítéssel, el<PERSON><PERSON><PERSON><PERSON> az útmutatót az <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">INSTALL fájlban</a> és a <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">telepítési oldalon</a>!", "smw-admin-smwhomepage": "A Szemantikus MediaWiki teljes felhasználói dokumentációja a <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b> c<PERSON><PERSON>.", "smw-admin-bugsreport": "A hibákat a <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHubon</a> le<PERSON>t jelente<PERSON>.", "smw-admin-questions": "Ha további kérdéseid vagy java<PERSON>d van<PERSON>, csatlakozz a <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">Szemantikus MediaWiki felhasználói fórumán</a> f<PERSON><PERSON>.", "smw_adminlinks_datastructure": "Adatstruktúra", "smw_adminlinks_displayingdata": "Adatok megjeleníté<PERSON>", "smw_adminlinks_inlinequerieshelp": "Szövegbeli lekérdezés-súgó", "smw-createproperty-isproperty": "<PERSON>z egy „$1” típus<PERSON> tulajdonság.", "smw-createproperty-allowedvals": "A tulajdonság a következő {{PLURAL:$1|értéket|értékeket}} veheti fel:", "smw-paramdesc-category-delim": "A határolójel", "smw-paramdesc-category-template": "Az elemeket formázandó sablon", "smw-info-par-message": "Megjelenítendő üzenet.", "smw-info-par-icon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jel, \"info\" vagy \"warning.\"", "prefs-smw": "Szemantikus MediaWiki", "prefs-ask-options": "Szemantikus keresési beállítások", "smw-prefs-ask-options-tooltip-display": "Paraméterszöveg megjelenítése információs buborék formáj<PERSON>", "smw-ui-tooltip-title-property": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-quantity": "Mértékegység-átváltás", "smw-ui-tooltip-title-info": "Információ", "smw-ui-tooltip-title-warning": "Figyelmeztetés", "smw-ui-tooltip-title-parameter": "Paraméter", "smw-ui-tooltip-title-event": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-note": "Megjegyzés", "smw-ui-tooltip-title-legend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_unknowntype": "A tulajdonság „$1” típusa érvénytelen", "smw_concept_header": "A(z) „$1” koncepcióhoz tartozó lapok", "smw_conceptarticlecount": "Alább $1 lap látszik.", "group-smwadministrator": "Adminisztrátorok (Szemantikus MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Adminisz<PERSON>átorok (Szemantikus MediaWiki)", "smw-sp-properties-header-label": "Tulajdonságok listája", "smw-admin-idlookup-input": "Keresés:", "smw-admin-objectid": "Azonosító:", "smw-admin-configutation-tab-settings": "Beállítások", "smw-admin-configutation-tab-namespaces": "Névterek", "smw-admin-maintenance-tab-scripts": "Karbantartó szkriptek", "smw-livepreview-loading": "Betöltés…", "smw-datavalue-allows-value-list-missing-marker": "A(z) „$1” lista tartalmában nincsenek elemek * listajelzővel.", "smw-schema-error-json": "JSON hiba: \"$1\"", "smw-schema-usage": "<PERSON><PERSON><PERSON><PERSON>", "smw-listingcontinuesabbrev": "folyt.", "smw-showingresults": "Lent '''{{PLURAL:$1|egy|$1}}''' tal<PERSON><PERSON> l<PERSON>, az eleje '''$2'''."}