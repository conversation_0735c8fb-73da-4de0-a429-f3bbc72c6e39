{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://www.semantic-mediawiki.org/wiki/Help:Schema/Type/PROPERTY_PROFILE_SCHEMA", "type": "object", "title": "Property profile validation schema", "required": ["type", "profile"], "properties": {"type": {"$id": "#/properties/type", "type": "string", "enum": ["PROPERTY_PROFILE_SCHEMA"], "title": "Schema type", "default": "PROPERTY_PROFILE_SCHEMA"}, "title_prefix": {"$id": "#/properties/title_prefix", "type": "string", "enum": ["Profile"], "title": "Title prefix"}, "manifest_version": {"$id": "#/properties/manifest_version", "type": "number", "title": "Manifest version", "default": 1}, "tags": {"$id": "#/properties/tags", "type": "array", "title": "tags", "default": null, "items": {"$id": "#/properties/tags/items", "type": "string", "title": "tags, keywords etc.", "default": "", "pattern": "^(.*)$"}}, "profile": {"$id": "#/properties/profile", "type": "object", "title": "Available profile options", "minProperties": 1, "properties": {"sequence_map": {"$ref": "#/definitions/sequence_map"}, "range_group": {"$ref": "#/definitions/range_group"}, "range_control": {"$ref": "#/definitions/range_control"}}, "additionalProperties": false}}, "definitions": {"sequence_map": {"$id": "#/definitions/sequence_map", "type": "boolean", "default": false, "title": "Whether to store annotation values in the order of the input or not"}, "range_group": {"$id": "#/definitions/range_group", "type": "object", "patternProperties": {"^(.*)$": {"$ref": "#/definitions/range_group_field_value"}}}, "range_group_field_value": {"$id": "#/definitions/range_group_field_value", "type": "string", "default": false, "pattern": "^(.*)(\\.\\.\\.)(.*)$"}, "range_control": {"$id": "#/definitions/range_control", "type": "object", "properties": {"min_interval": {"type": "number", "title": "Number to define the minimum range required for the values"}, "step_size": {"type": "number", "title": "Specifies the size of increment between values"}, "precision": {"type": "number", "title": "Specifies a number of digits after the decimal point"}, "uncertainty": {"type": "number", "title": "Specifies a number of uncertainty for the minimum and maximum range value"}}, "additionalProperties": false, "required": ["min_interval", "step_size", "precision"]}}}