{"@metadata": {"authors": ["Ciencia Al Poder", "<PERSON><PERSON><PERSON><PERSON>", "Ivanhercaz"]}, "smw-desc": "Hacemos que tu wiki sea más accesible para las máquinas ''y'' las personas ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentación en línea]).", "smw-semantics-not-enabled": "No se habilitó el funcionamiento de Semantic MediaWiki para esta wiki.", "smw_viewasrdf": "Feed RDF", "smw_finallistconjunct": "y", "smw-factbox-head": "… más sobre «$1»", "smw-factbox-facts": "Datos", "smw-factbox-facts-help": "Mostrar declaraciones y datos que han sido creados por un usuario", "smw-factbox-facts-derived": "<PERSON><PERSON> derivados", "smw-factbox-facts-derived-help": "Mostrar datos que han sido derivados a partir de reglas o con la ayuda de otras técnicas de razonamiento", "smw_isspecprop": "Esta propiedad es una propiedad especial en esta wiki.", "smw-concept-cache-header": "Uso de la antememoria", "smw-concept-cache-count": "La [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count antememoria de conceptos] contiene {{PLURAL:$1|'''una''' entidad|'''$1''' entidades}} ($2).", "smw-concept-no-cache": "No hay antememoria disponible.", "smw_concept_description": "Descripción del concepto «$1»", "smw_no_concept_namespace": "Los conceptos solo pueden definirse en páginas del espacio de nombres Concepto:", "smw_multiple_concepts": "Cada página de concepto solamente puede tener una definición de concepto.", "smw_concept_cache_miss": "No se puede utilizar el concepto «$1» en este momento, ya que la configuración de la wiki exige que se compute sin conexión.\nSi el problema persiste después de que pase un tiempo, solicita al administrador del sitio que el concepto esté disponible.", "smw_noinvannot": "Los valores no pueden asignarse a propiedades invertidas.", "version-semantic": "Extensiones semánticas", "smw_baduri": "No están permitidas las URIs con la forma «$1».", "smw_printername_count": "Contar resultados", "smw_printername_csv": "Exportar CSV", "smw_printername_dsv": "Exportar DSV", "smw_printername_debug": "Consulta de depuración (para expertos)", "smw_printername_embedded": "Incrustar el contenido de la página", "smw_printername_json": "Exportar JSON", "smw_printername_list": "Lista", "smw_printername_ol": "Enumeración", "smw_printername_ul": "Lista viñetada", "smw_printername_table": "Tabla", "smw_printername_broadtable": "Tabla ancha", "smw_printername_template": "Plantilla", "smw_printername_rdf": "Exportar RDF", "smw_printername_category": "Categoría", "validator-type-class-SMWParamSource": "texto", "smw-paramdesc-limit": "La cantidad máxima de resultados a devolver", "smw-paramdesc-offset": "La compensación del primer resultado", "smw-paramdesc-headers": "Muestra los nombres de los encabezados/propiedades", "smw-paramdesc-mainlabel": "La etiqueta a asignar al nombre de la página principal", "smw-paramdesc-link": "Muestra los valores como enlaces", "smw-paramdesc-intro": "Si lo hubiese, el texto a mostrar antes de los resultados de la consulta", "smw-paramdesc-outro": "Si lo hubiese, el texto a mostrar después de los resultados de la consulta", "smw-paramdesc-default": "El texto a mostrar si la consulta no tiene resultados", "smw-paramdesc-sep": "El separador para los valores", "smw-paramdesc-showsep": "Mostrar el separador al comienzo del archivo CSV (\"sep=<valor>\")", "smw-paramdesc-distribution": "En vez de mostrar sus valores, contar y mostrar sus ocurrencias.", "smw-paramdesc-distributionsort": "Ordenar la distribución de valores por número de ocurrencias.", "smw-paramdesc-distributionlimit": "Limitar la distribución de valores solo al conteo de algunos valores.", "smw-paramdesc-aggregation": "Especifica con que debería relacionarse el conjunto", "smw-paramdesc-template": "El nombre de una plantilla con la que mostrar el listado", "smw-paramdesc-columns": "El número de columnas en las que mostrar los resultados (por defecto es $1)", "smw-paramdesc-userparam": "Si se utiliza una plantilla, es un valor pasado en cada llamada de plantilla"}