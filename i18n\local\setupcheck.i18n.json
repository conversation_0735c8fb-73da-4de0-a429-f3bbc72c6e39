{"$messages": "Contains messages and keys used within this JSON to avoid duplicate text representations", "smw-setupcheck-dependency-error": {"en": "Dependency error"}, "smw-setupcheck-registry-error": {"en": "Registry error"}, "smw-setupcheck-error": {"en": "Error"}, "smw-setupcheck-maintenance": {"en": "Maintenance"}, "smw-setupcheck-release": {"en": "Version"}, "smw-setupcheck-code": {"en": "Code"}, "smw-setupcheck-progress": {"en": "Progress"}, "smw-setupcheck-db-requirement": {"en": "Requirement"}, "smw-setupcheck-db-title": {"en": "Database type"}, "smw-setupcheck-db-current-title": {"en": "Current version"}, "smw-setupcheck-db-minimum-title": {"en": "Minimum version"}, "smw-setupcheck-stack-trace": {"en": "Stack trace"}, "smw-setupcheck-how-to-fix-error": {"en": "How can I fix this error?"}, "smw-setupcheck-why-this-page": {"en": "Why do I see this page?"}, "smw-setupcheck-temporarily-disable": {"en": "To temporarily disable this error output and return to a working MediaWiki (without Semantic MediaWiki), remove the <code>enableSemantics</code> from your <code>LocalSettings.php</code>."}, "smw-setupcheck-requires-db-minimum-version": {"en": "During the installation, the setup check found that the minimum requirement for the database environment defined by Semantic MediaWiki didn't match with the version available and is the reason why the installation of Semantic MediaWiki was aborted."}, "smw-setupcheck-dependency-requires-semanticmediawiki": {"en": "The reported extension requires Semantic MediaWiki to be enabled which is currently <b>not</b> the case thus adding <code>enableSemantics</code> to <code>LocalSettings.php</code> should resolve the dependency error."}, "smw-setupcheck-dependency-multiple-requires-semanticmediawiki": {"en": "The reported extensions require Semantic MediaWiki to be enabled which is currently <b>not</b> the case thus adding <code>enableSemantics</code> to <code>LocalSettings.php</code> should resolve the dependency error."}, "smw-setupcheck-dependency-error-multiple": {"en": "It was reported that multiple extensions have failed their dependencies including:"}, "smw-setupcheck-enablesemantics-assistance": {"en": "Information about <a href='https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics'>enableSemantics</a> is available on the corresponding help page, you may also ask the <a href='https://www.semantic-mediawiki.org/wiki/Help:Getting_support'>project support</a> for assistance."}, "smw-setupcheck-remove-wfloadextension": {"en": "Removing <code>wfLoadExtension( 'SemanticMediaWiki' )</code> from <code>LocalSettings.php</code> is sufficient to avoid any conflicts with <code>enableSemantics</code> and ensures that required variables and parameters are setup correctly."}, "smw-setupcheck-administrator-assistance": {"en": "An administrator (or any person with administrator rights) has to run either MediaWiki's <a href='https://www.mediawiki.org/wiki/Manual:Update.php'>update.php</a> or Semantic MediaWiki's <a href='https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php'>setupStore.php</a> maintenance script."}, "smw-setupcheck-administrator-contact-for-information": {"en": "Please contact your local administrator to get more information about the progress."}, "smw-setupcheck-install-assistance": {"en": "You may also consult the following pages for further assistance:<ul><li><a href='https://www.semantic-mediawiki.org/wiki/Help:Installation'>Installation</a> instructions</li><li><a href='https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting'>Troubleshooting</a> help page</li></ul>"}, "smw-setupcheck-requires-software-update": {"en": "The issue can only be resolved by updating the software (or environment) to match the requirement."}, "smw-setupcheck-invalid-upgrade-key": {"en": "<a href='https://www.semantic-mediawiki.org/'>Semantic MediaWiki</a> was installed and enabled but is missing an appropriate <a href='https://www.semantic-mediawiki.org/wiki/Help:Upgrade_key'>upgrade key</a>."}, "smw-setupcheck-reason-for-invalid-upgrade-key": {"en": "Semantic MediaWiki's internal database structure has changed and requires some adjustments to be fully functional. There can be several reasons including:<ul><li>Changes to the list of fixed properties and may require additional table(s)</li><li>Changes to the overall table structure or indices requirements</li><li>Changes to the selected storage or query engine</li><li>Changes to the required <a href='https://www.semantic-mediawiki.org/wiki/Entity_collation'>entity collation</a></li></ul>"}, "smw-setupcheck-registry-enablesemantics-conflict": {"en": "<code>LocalSettings.php</code> contains both <code>enableSemantics</code> and <code>wfLoadExtension( 'SemanticMediaWiki' )</code> forcing the ExtensionRegistry to abort the registration process."}, "smw-setupcheck-registry-invalid-wfloadextension-use": {"en": "<code>wfLoadExtension( 'SemanticMediaWiki' )</code> was used to enable Semantic MediaWiki which conflicts with <code>enableSemantics</code>."}, "smw-setupcheck-stack-trace-abort-condition": {"en": "The following stack trace may indicate which program (or extension) caused an abort condition."}, "smw-setupcheck-registry-invalid-access": {"en": "Some program (or extension) tried to access function(s) of Semantic MediaWiki without being correctly enabled."}, "smw-setupcheck-registry-requires-enablesemantics": {"en": "To use Semantic MediaWiki or functions of it, it is necessary to add <code>enableSemantics</code> to your <code>LocalSettings.php</code> which will ensure required variables and parameters are setup before programs can continue to work and make use of it."}, "smw-setupcheck-config-profile": {"en": "Please check the spelling and location of available <a href='https://www.semantic-mediawiki.org/wiki/Config_preloading'>default profiles</a> used as part of the <code>enableSemantics</code> declaration in your <code>LocalSettings.php</code>."}, "smw-setupcheck-in-maintenance-mode": {"en": "The system is currently undergoing an <a href='https://www.semantic-mediawiki.org/wiki/Help:Upgrade'>upgrade</a> of the <a href='https://www.semantic-mediawiki.org/'>Semantic MediaWiki</a> extension together with its data repository and we would like to ask you for your patience and allow the maintenance to continue before the wiki can be made accessible again."}, "smw-setupcheck-reason-for-in-maintenance": {"en": "The extension tries to minimize the impact and downtime by deferring most of its maintenance tasks to occur after the <code>update.php</code> run but some database related changes are required to finish first to avoid data inconsistencies, it may include:<ul><li>Changing table structures such as adding new or modify existing fields</li><li>Changing or adding table indices</li><li>Running optimization (table optimization etc.) or import tasks</li></ul>"}, "smw-setupcheck-estimation-of-completion": {"en": "An estimation as to when the upgrade is going to be finished is difficult to predict as it depends on the size of the data repository and the available hardware and can take a moment for larger wikis to complete."}, "smw-setupcheck-progress-key-create-tables": {"en": "Creating (or updating) tables and indices ..."}, "smw-setupcheck-progress-key-post-creation": {"en": "Running post creation tasks ..."}, "smw-setupcheck-progress-key-table-optimization": {"en": "Running table optimizations ..."}, "smw-setupcheck-progress-key-supplement-jobs": {"en": "Adding supplement jobs ..."}}