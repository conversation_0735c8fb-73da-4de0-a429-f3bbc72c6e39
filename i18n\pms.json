{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "smw-desc": "Rende soa wiki pì acessìbil - për le màchine ''e'' j'uman ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentassion an linia])", "smw_viewasrdf": "Fluss RDF", "smw_finallistconjunct": "e", "smw_isspecprop": "Costa propietà a l'é na propietà special ëd costa wiki.", "smw_concept_description": "Descrission dël concet «$1»", "smw_no_concept_namespace": "Ij concet a peulo mach esse definì ant le pàgine dlë spassi nominal Concept:.", "smw_multiple_concepts": "Minca pàgina ëd concet a peul avèj mach na definission.", "smw_concept_cache_miss": "Ël concet «$1» a peul pa esse dovrà ant ës moment, p<PERSON><PERSON>è la configurassion ëd la wiki a ciama ch'a sia calcolà fòra ëd linia.\nSe ël problema a van nen via an chèich moment, ciamé a l'aministrator ëd sò sit ëd rende 's concet disponìbil.", "smw_noinvannot": "Ij valor a peulo pa esse assignà a le proprietà anverse.", "version-semantic": "Estension semàntiche", "smw_baduri": "URI dla forma \"$1\" a son pa p<PERSON><PERSON><PERSON><PERSON>.", "smw_printername_count": "Conta dj'<PERSON><PERSON><PERSON>", "smw_printername_csv": "esportassion an CSV", "smw_printername_dsv": "esportassion an DSV", "smw_printername_debug": "Arcesta d'eliminassion dij bigat (për gent pràtica)", "smw_printername_embedded": "Contù dle pàgine mojà", "smw_printername_json": "esportassion an JSON", "smw_printername_list": "Lista", "smw_printername_ol": "Enumerassion", "smw_printername_ul": "<PERSON><PERSON>", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON>ula est<PERSON>", "smw_printername_template": "Stamp", "smw_printername_rdf": "Esportassion RDF", "smw_printername_category": "Categorìa", "validator-type-class-SMWParamSource": "test", "smw-paramdesc-limit": "Ël màssim nùmer d'arzultà da smon-e", "smw-paramdesc-offset": "La diferensa dël prim arzultà", "smw-paramdesc-headers": "Mostré j'antestassion/ij nòm dle proprietà", "smw-paramdesc-mainlabel": "L'etichëtta da dé al nòm ëd la pàgina prinsipal", "smw-paramdesc-link": "Smon-e ij valor com dle liure", "smw-paramdesc-intro": "Ël test da mostré prima dj'arz<PERSON>à dl'arcesta, s'a-i na j'é", "smw-paramdesc-outro": "Ël test da mostré apress a j'arzultà dl'arcesta, s'a-i na j'é", "smw-paramdesc-default": "Ël test da mostré s'a-i é gnun arzultà a l'arcesta", "smw-paramdesc-sep": "Ël separator për ij valor", "smw-paramdesc-showsep": "Smon-e ël separator an testa a l'archivi CSV (\"sep=<value>\")", "smw-paramdesc-distribution": "Nopà ëd visualisé tùit ij valor, conté soe ocorense, e mostreje.", "smw-paramdesc-distributionsort": "Ordiné la distribussion dij valor për nùmer d'ocorense.", "smw-paramdesc-distributionlimit": "Limité la distribussion dij valor al cont ëd mach chèich valor.", "smw-paramdesc-template": "Ël nòm ëd në stamp con ël qual visualisé j'arzultà", "smw-paramdesc-columns": "Ël nùmer ëd co<PERSON> andoa mostré j'arzultà (lë stàndard a l'é $1)", "smw-paramdesc-userparam": "Un valor passà an mincadun-a ciamà dë stamp, se në stamp a l'é dovrà", "smw-paramdesc-introtemplate": "Ël nòm ëd në stamp da mostré prima dj'arz<PERSON>à dl'arserca, s'a-i në j'é", "smw-paramdesc-outrotemplate": "Ël nòm ëd në stamp da mostré apress ëd j'arzultà dl'arserca, s'a-i në j'é", "smw-paramdesc-embedformat": "L'etichëtta HTML dovrà për definì j'antestassion", "smw-paramdesc-embedonly": "<PERSON><PERSON><PERSON>e nen j'antestassion", "smw-paramdesc-table-class": "Na classa CSS adissional da amposté për la tàula", "smw-paramdesc-rdfsyntax": "La sintassi RDF da dovré", "smw-paramdesc-csv-sep": "Ël separator da dovré", "smw-paramdesc-dsv-separator": "Ël separator da dovré", "smw-paramdesc-dsv-filename": "Ël nòm për l'archivi DSV", "smw-smwdoc-description": "A mostra na tàula ëd tùit ij paràmetr che a peulo esse dovrà për ël formà d'arzultà specificà ansema con dij valor e dle descrission predefinì.", "smw-smwdoc-par-format": "Ël formà dl'arzultà për mostré la documentassion d'un paràmetr.", "smw-smwdoc-par-parameters": "Che paràmetr mostré. «specific» për coj giontà dal formà, «base» për coj disponìbij an tùit ij formà, e «all» për tùit doi.", "smw-paramdesc-sort": "Propietà conforma a la qual ordiné l'arcesta", "smw-paramdesc-order": "<PERSON><PERSON><PERSON> ëd la selession dl'arcesta", "smw-paramdesc-searchlabel": "Ël test për continué l'arserca", "smw-paramdesc-named_args": "Nòmina j'argoment passà a lë stamp", "smw-paramdesc-export": "Opsion d'esportassion", "smw-paramdesc-prettyprint": "Na stampa bin fàita che a visualisa ëd neuve righe e andentassion", "smw-paramdesc-source": "Sorgiss d'arcesta alternativa", "smw-paramdesc-jsonsyntax": "Sintassi JSON da dovré", "smw-printername-feed": "Fluss RSS e Atom", "smw-paramdesc-feedtype": "<PERSON><PERSON><PERSON> ëd fluss", "smw-paramdesc-feedtitle": "Ël test da mostré com tìtol dël fluss", "smw-paramdesc-feeddescription": "Ël test da dovré com descrission dël fluss", "smw-paramdesc-feedpagecontent": "Contnù ëd la pàgina da smon-e con ël fluss", "smw-label-feed-description": "$1 $2 fluss", "smw_iq_disabled": "J'arceste semàntiche a son ëstàite disabilità për sta wiki-sì.", "smw_iq_moreresults": "... <PERSON><PERSON><PERSON>", "smw_parseerror": "Ël valor dàit a l'é pa stàit capì.", "smw_notitle": "''$1'' a peul pa esse dovrà com nòm ëd pàgina an sta wiki-sì.", "smw_noproperty": "''$1'' a peul pa esse dovrà com nòm ëd propietà an sta wiki-sì.", "smw_wrong_namespace": "<PERSON>h le pàgine ant lë spassi nominal ''$1'' a son p<PERSON><PERSON><PERSON><PERSON> am<PERSON>.", "smw_manytypes": "Pi che un tipo definì për proprietà.", "smw_emptystring": "<PERSON><PERSON> veuide a son pa acetà.", "smw_notinenum": "''$1'' a l'é pa ant la lista dij valor possìbij ($2) për sta proprietà-sì.", "smw_noboolean": "''$1'' a l'é pa arconossù 'me valor <PERSON> (ver/f<PERSON><PERSON>).", "smw_true_words": "ver,v,é!,é", "smw_false_words": "fàuss,f,nò,n", "smw_nofloat": "''$1'' a l'é pa un nùmer", "smw_infinite": "<PERSON><PERSON><PERSON> gr<PERSON>ss com ''$1'' a son pa mantnù.", "smw_unitnotallowed": "\"$1\" a l'é pa diciarà com unità dë mzura bon-a për costa proprietà.", "smw_nounitsdeclared": "Gnun-e unità dë mzura a son stàite diciarà për costa propietà.", "smw_novalues": "Pa gnun valor specificà.", "smw_nodatetime": "La data ''$1'' a l'é pa stàita capìa.", "smw_toomanyclosing": "A smija ch'a-i sio tròpe ocorense ëd ''$1'' ant l'arcesta.", "smw_noclosingbrackets": "<PERSON><PERSON><PERSON> usagi ëd \"<nowiki>[[</nowiki>\" an soa arcesta a son pa stàit sarà da un corëspondent \"]]\".", "smw_misplacedsymbol": "Ël sìmbol \"$1\" a l'era dovrà ant un pòst andoa a l'é pa ùtil.", "smw_unexpectedpart": "La part \"$1\" ëd l'arcesta a l'é pa stàita capìa.\nJ'arzultà a peulo esse pa coma spetà.", "smw_emptysubquery": "<PERSON><PERSON><PERSON> sot-arcesta a l'ha ëd condission pa bon-e.", "smw_misplacedsubquery": "<PERSON><PERSON><PERSON> sot-arcesta a l'era dovrà ant un pòst andoa gnun-e sot-arceste a j'ero përm<PERSON>ttùe.", "smw_valuesubquery": "Sot-arcesta pa mantnùa për ij valor ëd la proprietà \"$1\".", "smw_badqueryatom": "Chèich part \"<nowiki>[[…]]</nowiki>\" ëd l'arcesta a l'é pa stàita capìa.", "smw_propvalueproblem": "Ël valor ëd la proprietà \"$1\" a l'é pa stàit capì.", "smw_noqueryfeature": "Chèica funsion ëd l'arcesta a l'é pa mantnù su sta wiki-sì e part ëd l'arcesta a l'é stàita sautà ($1).", "smw_noconjunctions": "Le congiunsion ant j'arcesta a son pa mantnùe su sta wiki-sì e part ëd l'arcesta a l'é stàita sautà ($1).", "smw_nodisjunctions": "Le disgiunsion ant j'arceste a son pa apogià su sta wiki-sì e part ëd l'arcesta a l'é stàita sautà ($1).", "smw_querytoolarge": "Le condission ëd l'arcesta sì-sota a son pa stàite considerà an rason dla le restrission ëd la wiki ant la dimension o profondità dj'arceste: $1.", "smw_notemplategiven": "Dà un valor për ë<PERSON> par<PERSON>r \"stamp\" an manera che sto formà-sì d'arcesta a travaja.", "smw_db_sparqlqueryproblem": "L'arzultà dl'anterogassion a l'ha pa podù esse otnù da la base ëd dàit SPARQL. S'eror a peul esse temporani o andiché n'eror ant ël programa dla base ëd dàit.", "smw_db_sparqlqueryincomplete": "Rësponde a l'arcesta a l'é arzultà tròp complicà e a l'é stàit abortì. Ch<PERSON>ich arzultà a podrìo manché. Se possìbil, ch'a preuva nopà a dovré n'arcesta pi sempia.", "smw_type_header": "Proprietà dël tipo \"$1\".", "smw_typearticlecount": "Mostré $1 {{PLURAL:$1|la proprietà|le proprietà}} ch'a deuvro sto tipo-sì.", "smw_attribute_header": "Pàgine ch'a deuvro la proprietà \"$1\".", "smw_attributearticlecount": "Smon-e $1 {{PLURAL:$1|la pàgine|le pàgine}} ch'a deuvro sta proprietà-sì.", "exportrdf": "Espòrta pàgine an RDF", "smw_exportrdf_docu": "Sta pàgina-sì a-j p<PERSON><PERSON><PERSON><PERSON> d'oten-e dat da na pàgina an formà RDF.\nPër esporté dle pàgine, ch'a anserissa ij tìtoj ant la casela ëd test sì-sota, un tìtol për linia.", "smw_exportrdf_recursive": "Esporté ricorsivament tute le pàgine corelà.\nNoté che l'arzultà a podrìa esse motobin gròss!", "smw_exportrdf_backlinks": "Esporté ëdcò tute le pàgine ch'a s'arferisso a le pàgine esportà.\nA génera RDF navigàbij.", "smw_exportrdf_lastdate": "Espòrta pa pàgine che a son pa stàite cangià dal moment specificà.", "smw_exportrdf_submit": "Esporté", "uriresolver": "<PERSON>rz<PERSON><PERSON>or d'U<PERSON>", "properties": "Proprietà", "smw-categories": "Categorìe", "smw_properties_docu": "Le proprietà sì-sota a son dovrà ant la wiki.", "smw_property_template": "$1 ëd tipo $2 ($3 {{PLURAL:$3|utilisassion}})", "smw_propertylackspage": "Tute le proprietà a dovrìo esse descrivùe da na pàgina!", "smw_propertylackstype": "Gnun tipo a l'é stàit specificà për sta proprietà-sì (as fa cont ch'a sia ëd tipo $1, për adess).", "smw_propertyhardlyused": "Sta proprietà-sì a l'é dovrà apen-a ant la wiki!", "unusedproperties": "Proprietà pa dovrà", "smw-unusedproperties-docu": "Le proprietà sì-sota a esisto, bele che gnun-e àutre pàgine a-j deuvro.", "smw-unusedproperty-template": "$1 ëd tipo $2", "wantedproperties": "Proprietà vorsùe", "smw-wantedproperties-docu": "Le proprietà sì-sota a son dovrà ant la wiki ma a l'han ancó pa na pàgina për descrivje.", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|usagi|usagi}})", "smw_purge": "Rinfrësca", "types": "Tipo", "smw_types_docu": "Cola sì-sota a l'é na lista ëd tùit ij tipo ëd dat che a peulo esse assignà a la proprietà.", "smw_uri_doc": "L'arzolvidor d'URI a realisa la [$1 conclusion dël TAG dël W3C a propòsit ëd httpRange-14].\nA fa an manera che j'uman a dvento pa ëd sit ëd la Ragnà.", "ask": "Arserca semàntica", "smw_ask_sortby": "<PERSON><PERSON><PERSON> (opsional)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_descorder": "Calant", "smw_ask_submit": "<PERSON><PERSON><PERSON>", "smw_ask_editquery": "Modifiché l'arcesta", "smw_add_sortcondition": "[Gionta condission d'órdin]", "smw_ask_hidequery": "Stërmé l'arcesta", "smw_ask_help": "Agiut an sj'arceste", "smw_ask_queryhead": "Anterogassion", "smw_ask_printhead": "Dat adissionaj da smon-e", "smw_ask_printdesc": "(gionta un nòm ëd proprietà për linia);", "smw_ask_format_as": "Ampaginé com:", "smw_ask_defaultformat": "st<PERSON><PERSON><PERSON>", "smw_ask_otheroptions": "Àutre opsion", "smw-ask-otheroptions-info": "Costa session a conten dj'opsion che a modìfico j'istrussion dë stampa. Le descrission dij paràmetr a peulo esse vëddùe passandje dzora con ël rat.", "smw-ask-otheroptions-collapsed-info": "<PERSON><PERSON><PERSON>, ch'a deuvra la plancia «pi» për vëdde tute j'opsion disponìbij", "smw_ask_show_embed": "Smon-e <PERSON><PERSON> c<PERSON>des antern", "smw_ask_hide_embed": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> antern", "smw_ask_embed_instr": "<PERSON><PERSON><PERSON> anclude costa anterogassion an linia ant na pàgina wiki, ch'a deuvra ël còdes sì-sota.", "smw-ask-delete": "[<PERSON><PERSON><PERSON>]", "smw-ask-sorting": "<PERSON><PERSON><PERSON>", "smw-ask-format-selection-help": "Për na descrission detajà, për piasì ch'a vìsita la pàgina d'agiut $1.", "searchbyproperty": "<PERSON><PERSON> p<PERSON>", "smw_sbv_docu": "Ser<PERSON> për tute le pàgine che a l'han proprietà e valor dàit.", "smw_sbv_novalue": "Ch'a anserissa un valor bon për la proprietà, o ch'a varda tùit ij valor ëd le proprietà për \"$1\".", "smw_sbv_displayresultfuzzy": "Na lista ëd tute le pàgine che a l'han la proprietà \"$1\" con valor \"$2\".\nDagià ch'a-i son ëst<PERSON>je mach pò<PERSON> a<PERSON>, a son s<PERSON><PERSON> ëdcò ij valor davzin.", "smw_sbv_property": "Propietà:", "smw_sbv_value": "Valor:", "smw_sbv_submit": "<PERSON><PERSON><PERSON>", "browse": "Varda wiki", "smw_browselink": "Varda propietà", "smw_browse_article": "Ch'a anseriss ël nòm ëd la pàgina da andoa ancaminé a vardé.", "smw_browse_go": "Va", "smw_browse_show_incoming": "smon-e le propietà che a colego ambelessì", "smw_browse_hide_incoming": "st<PERSON><PERSON><PERSON> le propietà ch'a colego ambelessì", "smw_browse_no_outgoing": "Sta pàgina-sì a l'ha pa ëd propietà.", "smw_browse_no_incoming": "Gnun-a propietà a colega a sta pàgina-sì.", "smw_inverse_label_default": "$1 ëd", "smw_inverse_label_property": "Etichëtta dla propietà anversa", "pageproperty": "Serca propietà dla pàgina", "smw_pp_docu": "Serca tùit ij valor ëd na propietà su na pàgina dàita.", "smw_pp_from": "Da pàgina", "smw_pp_type": "Propietà", "smw_pp_submit": "Smon-<PERSON>'<PERSON>", "smw_result_prev": "Prima", "smw_result_next": "Apress", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "<PERSON> gnun a<PERSON>.", "smwadmin": "Funsion d'aministrator p<PERSON>r Semantic MediaWiki", "smw-admin-setupsuccess": "Ël motor ëd memorisassion a l'é stàit anstalà për da bin.", "smw_smwadmin_return": "Torna a $1", "smw_smwadmin_updatestarted": "Un process neuv për rinfrësché ij dat semàntich a l'é stàit fàit parte.\nTùit ij dat memorisà a saran rifàit o riparà andoa a-i é dabzògn.\nA peul trové l'andament ëd la modìfica an sta pàgina special-sì.\n\nArtorn a $1.", "smw_smwadmin_updatenotstarted": "A-i é già un process ëd modìfica an camin.\nCreene nen n'àutr.\n\nArtorna a $1.", "smw_smwadmin_updatestopped": "Tùit ij process ëd modìfica esistent a son ëstàit fërmà.\n\nArtorna a $1.", "smw_smwadmin_updatenotstopped": "<PERSON><PERSON><PERSON> fë<PERSON> process ëd modìfica an cors, a dev ativé la casela për indiché ch'a l'é pròpi sicur.\n\nTorné andré a $1.", "smw-admin-docu": "Sta pàgina special-sì a lo giuta durant l'instalassion e l'agiornament ëd <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a>.\nCh'as visa ëd salvé ij dat ëd valor prima ëd fé le funsion aministrative.", "smw-admin-db": "Anstalassion e agiornament dla base ëd dàit", "smw-admin-dbdocu": "Semantic MediaWiki a l'ha dabzògn ëd chèiche estension a la base ëd dàit ëd MediaWiki për memorisé ij dat semàntich.\nLa funsion sì-sota a assicura che soa base ëd dàit a l'é ampostà për da bin.\nIj cangiament fàit an cost pass-sì a toco pa ël rest dla base ëd dàit ëd MediaWiki, e a l'é bel fé torné andré si un a veul.\nSta funsion d'ampostassion-sì a peul esse fàita marcé vàire vire sensa fé 'd dann, ma a-i n'a j'é dabzògn mach na vira për l'anstalassion o l'agiornament.", "smw-admin-permissionswarn": "Se l'operassion a faliss con dj'eror <PERSON>, l'utent dla base ëd dàit dovrà da soa wiki (ch'a contròla sò LocalSettings.php) probabilment a l'ha pa basta 'd përmess.\nO bin ch'a-j daga a st'utent dij përmess adissionaj për creé e scancelé 'd tàule, temporaneament ch'a anserissa l'identificativ dël cont rèis ëd soa base ëd dàit an LocalSettings.php, opura ch'a deuvra ël copion ëd manteniment <code>setupStore.php</code> ch'a peul dovré le credensiaj ëd n'aministrator.", "smw-admin-dbbutton": "Inissialisé o agiorné le tàule", "smw-admin-announce": "Anonsia toa wiki", "smw_smwadmin_datarefresh": "Riparassion ëd dat e agiornament", "smw_smwadmin_datarefreshdocu": "A l'é possìbil ripristiné tùit ij dat Semantic MediaWiki basà dzora al contnù corent ëd la wiki.\nSòn a peul ven-e a taj për riparé dat rot o për rinfrësché dat se ël formà antern a l'é cangià për chèich agiornament dël programa.\nLa modìfica a l'é fàita pàgina për pàgina e a sarà pa completa sùbit.\nLòn ch'a-i é sì-sota a mostra se na modìfica a l'é an cors e a-j përmët ëd fé parte o fërmé le modìfiche (gavà che sta possibilità a sia stàita disabilità da l'aministrator dël sit).", "smw_smwadmin_datarefreshprogress": "<strong>na modìfica a l'é già an cors.</strong>\nA l'é normal che la modìfica a von-a anans mach pian përchè a rinfr<PERSON><PERSON> dat an cite partìe minca vira che n'utent a intra ant la wiki.\nPër fé finì sta modìfica pi an pressa, a peul ciamé ël copion ëd manteniment MediaWiki <code>runJobs.php</code> (ch'a deuvra l'opsion <code>--maxjobs 1000</code> për strenze ël nùmer ëd modìfiche fàite ant un lòt).\nAvansament stimà dla modìfica corenta:", "smw_smwadmin_datarefreshbutton": "An<PERSON><PERSON>-a a agiorné ij dat", "smw_smwadmin_datarefreshstop": "Ferma sto agiornament-sì", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, i son sicur.", "smw-admin-support": "Oteniment ëd n'agiut", "smw-admin-supportdocu": "Vàire arsorse a peulo giutelo an cas ëd problema:", "smw-admin-installfile": "S'a treuva ëd problema con soa anstalassion, ch'a ancamin-a a controlé le linie guida ant l'<a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">archivi INSTALL</a>.", "smw-admin-smwhomepage": "La documentassion utent completa ëd Semantich WikiMedia a l'é a <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Ij bigat a peulo esse arportà a <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>.", "smw-admin-questions": "S'a l'ha d'àutre chestion o sugeriment, ch'a vada a la discussion dzora a la <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">piassa dj'utent ëd Semantic MediaWiki</a>.", "smw_adminlinks_datastructure": "Strutura dij dat", "smw_adminlinks_displayingdata": "Visualisé ij dat", "smw_adminlinks_inlinequerieshelp": "Agiut an sj'anterogassion an linia", "smw-createproperty-isproperty": "Costa-sì a l'é na proprietà ëd sòrt $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Ël valor përmëttù për sta proprietà-sì a l'é|Ij valor përmëttù për sta proprietà-sì a son}}:", "smw-paramdesc-category-delim": "Ël delimitador", "smw-paramdesc-category-template": "Në stamp për formaté j'element con", "smw-paramdesc-category-userparam": "Un paràmetr për passé a lë stamp", "smw-info-par-message": "M<PERSON><PERSON>gi da visualisé.", "smw-info-par-icon": "<PERSON><PERSON>, o «info» opura «warning».", "prefs-smw": "MediaWiki Semàntica", "prefs-ask-options": "Opsion d'arserca Semàntica", "smw-prefs-intro-text": "J'opsion sì-sota a son smon<PERSON>e da [https://www.semantic-mediawiki.org/ Semantic MediaWiki] (o soe estension) për abilité na përsonalisassion andividual ëd funsion selessionà. Për savèjne ëd pi, për piasì ch'a consulta sta [https://www.semantic-mediawiki.org/wiki/Help:User_preferences session d'agiut].", "smw-prefs-ask-options-tooltip-display": "Smon-e ël test dël paràmetr com na nivolëtta d'anformassion", "smw-ui-tooltip-title-property": "Propietà", "smw-ui-tooltip-title-quantity": "Quantità", "smw-ui-tooltip-title-info": "Anformassion", "smw-ui-tooltip-title-service": "Colegament ë<PERSON> servis<PERSON>", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Paràmetr", "smw-ui-tooltip-title-event": "Eveniment", "smw-ui-tooltip-title-note": "Nò<PERSON>", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw_unknowntype": "La sòrt ëd costa propietà a l'é pa bon-a.", "smw_concept_header": "Pàgine dël concet \"$1\"", "smw_conceptarticlecount": "Smon-e $1 {{PLURAL:$1|pàgina|pàgine}} che a aparten-o a col concet.", "smw-livepreview-loading": "Antramentr ch'as caria…", "smw-listingcontinuesabbrev": "anans", "smw-showingresults": "Ambelessì-sota a treuva fin a {{PLURAL:$1|'''1'''|'''$1'''}} a<PERSON><PERSON><PERSON>, a parte dal nùmer #'''$2'''."}