{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "Mal<PERSON>aya", "McDut<PERSON><PERSON>", "아라"]}, "smw-desc": "Pro render tu wiki plus accessibile – a machinas '''e''' a humanos ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentation in linea])", "smw-error": "Error", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] ha essite installate e activate ma il manca un [https://www.semantic-mediawiki.org/wiki/Help:Upgrade clave de actualisation] appropriate.", "smw-upgrade-release": "Edition", "smw-upgrade-progress": "Progresso", "smw-upgrade-progress-explain": "Es difficile anticipar quando le actualisation essera concludite perque isto depende del dimension del repositorio de datos e del material disponibile, e pote prender un momento pro terminar se pro le wikis plus grande.\n\nPer favor contacta tu administrator local pro obtener plus information sur le progresso.", "smw-upgrade-progress-create-tables": "Creation (o actualisation) de tabellas e indices…", "smw-upgrade-progress-post-creation": "Execution de cargas post creation…", "smw-upgrade-progress-table-optimization": "Optimisation de tabellas…", "smw-upgrade-progress-supplement-jobs": "Addition de travalios supplementari…", "smw-upgrade-error-title": "Error » Semantic MediaWiki", "smw-upgrade-error-why-title": "Proque vide io iste pagina?", "smw-upgrade-error-why-explain": "Le structura de base de datos interne de Semantic MediaWiki ha cambiate e require alcun adjustamentos pro esser totalmente functional. Il pote haber varie rationes pro isto, per exemplo:\n* Proprietates fixe ha essite addite (necessitante un configuration additional del tabella)\n* Un actualisation contine certe cambiamentos a tabellas o indices que rende un interception obligatori ante le accesso al datos\n* Cambiamentos al motor de immagazinage o de consultas", "smw-upgrade-error-how-title": "Como corrige io iste error?", "smw-upgrade-error-how-explain-admin": "Un administrator (o qualcunque persona con derectos de administrator) debe executar un del duo scripts de mantenentia, sia [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] de MediaWiki, sia [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] de Semantic MediaWiki.", "smw-upgrade-error-how-explain-links": "Tu pote etiam consultar le sequente paginas pro ulterior assistentia:\n* Instructiones de [https://www.semantic-mediawiki.org/wiki/Help:Installation installation]\n* Pagina de adjuta pro le [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting resolution de problemas]", "smw-extensionload-error-why-title": "Proque vide io iste pagina?", "smw-extensionload-error-why-explain": "Le extension <b>non</b> ha essite cargate usante <code>enableSemantics</code> ma ha essite activate per altere medio, p.ex. le uso directe de <code>wfLoadExtension( 'SemanticMediaWiki' )</code>.", "smw-extensionload-error-how-title": "Como corrige io iste error?", "smw-extensionload-error-how-explain": "Pro activar le extension e evitar problemas con declarationes de spatio de nomines e configurationes pendente, es necessari usar <code>enableSemantics</code>, que garantira que le variabiles obligatori es definite ante de cargar le extension via <code>ExtensionRegistry</code>.\n\nPer favor consulta le pagina de adjuta [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] pro plus adjuta.", "smw-upgrade-maintenance-title": "Mantenentia » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Proque vide io iste pagina?", "smw-upgrade-maintenance-note": "Le systema es actualmente in curso de [https://www.semantic-mediawiki.org/wiki/Help:Upgrade actualisation] del extension [https://www.semantic-mediawiki.org/ Semantic MediaWiki] insimul con su repositorio de datos, e nos volerea sollicitar tu patientia pro permitter que le mantenentia continue ante que le wiki pote esser accessibile de novo.", "smw-upgrade-maintenance-explain": "Le extension essaya de minimisar le impacto e le tempore de inactivitate postponente le major parte de su cargas de mantenentia post le <code>update.php</code>, ma alcun modificationes associate al bases de datos debe esser concludite primo pro evitar incongruentias de datos. Istes pote includer:\n* Cambiar structuras de tabella como adder nove campos o modificar existentes\n* Cambiar o adder indices de tabella\n* Exequer optimisationes de tabellas (si activate)", "smw-semantics-not-enabled": "Le functionalitate de MediaWiki semantic non ha essite activate pro iste wiki.", "smw_viewasrdf": "Syndication RDF", "smw_finallistconjunct": ", e", "smw-factbox-head": "... plus sur \"$1\"", "smw-factbox-facts": "Factos", "smw-factbox-facts-help": "Monstra declarationes e factos que ha essite create per un usator", "smw-factbox-attachments": "Annexos", "smw-factbox-attachments-value-unknown": "N/D", "smw-factbox-attachments-is-local": "Es local", "smw-factbox-attachments-help": "Monstra annexos disponibile", "smw-factbox-facts-derived": "Factos derivate", "smw-factbox-facts-derived-help": "Monstra factos que ha essite derivate ab regulas o con le adjuta de altere technicas de rationamento", "smw_isspecprop": "Iste proprietate es special in iste wiki.", "smw-concept-cache-header": "Uso del cache", "smw-concept-cache-count": "Le [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count cache de conceptos] contine {{PLURAL:$1|'''un''' entitate|'''$1''' entitates}} ($2).", "smw-concept-no-cache": "Nulle cache disponibile.", "smw_concept_description": "Description del concepto \"$1\"", "smw_no_concept_namespace": "Le conceptos pote solmente esser definite in paginas in le spatio de nomines Concept:.", "smw_multiple_concepts": "Cata pagina de concepto pote haber un sol definition de concepto.", "smw_concept_cache_miss": "Le concepto \"$1\" non pote esser usate al momento, post que le configuration del wiki require que illo sia computate foras de linea. Si le problema non dispare post alcun tempore, demanda al administrator de tu sito de render disponibile iste concepto.", "smw_noinvannot": "Le valores non pote esser assignate al proprietates inverse.", "version-semantic": "Extensiones semantic", "smw_baduri": "Le adresses URI del forma \"$1\" non es permittite.", "smw_printername_count": "Contar resultatos", "smw_printername_csv": "Exportation in CSV", "smw_printername_dsv": "Exportation in DSV", "smw_printername_debug": "Consulta de debugging (pro expertos)", "smw_printername_embedded": "Incastrar contento de pagina", "smw_printername_json": "Exportation in JSON", "smw_printername_list": "Lista", "smw_printername_plainlist": "Lista simple", "smw_printername_ol": "Lista numerate", "smw_printername_ul": "Lista a punctos", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "Tabella large", "smw_printername_template": "Patrono", "smw_printername_templatefile": "File de patrono", "smw_printername_rdf": "Exportation in RDF", "smw_printername_category": "Categoria", "validator-type-class-SMWParamSource": "texto", "smw-paramdesc-limit": "Le numero maxime de resultatos a retornar", "smw-paramdesc-offset": "Le position del prime resultato", "smw-paramdesc-headers": "Monstrar le capites/nomines de proprietate", "smw-paramdesc-mainlabel": "Le etiquetta a dar al nomine del pagina principal", "smw-paramdesc-link": "Monstrar valores como ligamines", "smw-paramdesc-intro": "Le texto a monstrar ante le resultatos del consulta, si existe", "smw-paramdesc-outro": "Le texto a monstrar post le resultatos del consulta, si existe", "smw-paramdesc-default": "Le texto a monstrar si le consulta non produce resultatos", "smw-paramdesc-sep": "Le separator inter resultatos", "smw-paramdesc-propsep": "Le separator inter le proprietates de un entrata de resultato", "smw-paramdesc-valuesep": "Le separator inter le valores de un proprietate de un resultato", "smw-paramdesc-showsep": "Monstrar separator al initio del file CSV (\"sep=<value>\")", "smw-paramdesc-distribution": "In loco de presentar tote le valores, contar lor occurrentias, e monstrar istes.", "smw-paramdesc-distributionsort": "Ordinar le distribution de valores per numero de occurrentias.", "smw-paramdesc-distributionlimit": "Limitar le distribution de valores al numero de solmente alcun valores.", "smw-paramdesc-aggregation": "Specifica a que le aggregation debe esser associate", "smw-paramdesc-template": "Le nomine de un patrono con le qual presentar le impressiones", "smw-paramdesc-columns": "Le numero de columnas in le quales presentar resultatos", "smw-paramdesc-userparam": "Un valor passate a cata appello de patrono, si un patrono es usate", "smw-paramdesc-class": "Un classe CSS additional a definir pro le lista", "smw-paramdesc-introtemplate": "Le nomine de un patrono a monstrar ante le resultatos del consulta, si existe", "smw-paramdesc-outrotemplate": "Le nomine de un patrono a monstrar post le resultatos del consulta, si existe", "smw-paramdesc-embedformat": "Le etiquetta HTML usate pro definir capites", "smw-paramdesc-embedonly": "Non monstrar capites", "smw-paramdesc-table-class": "Un classe CSS additional a definir pro le tabella", "smw-paramdesc-table-transpose": "Monstrar le capites del tabella verticalmente e le resultatos horizontalmente", "smw-paramdesc-prefix": "Controlar le apparentia del spatio de nomines in impressiones", "smw-paramdesc-rdfsyntax": "Le syntaxe RDF a usar", "smw-paramdesc-csv-sep": "Specifica un separator de columnas", "smw-paramdesc-csv-valuesep": "Specifica un separator de valores", "smw-paramdesc-csv-merge": "Fusionar le valores de lineas e columnas con un identificator de subjecto identic (i.e. le prime columna)", "smw-paramdesc-csv-bom": "Adder un BOM (character que Marca le Ordine del Bytes) al initio del file producite", "smw-paramdesc-dsv-separator": "Le separator a usar", "smw-paramdesc-dsv-filename": "Le nomine del file DSV", "smw-paramdesc-filename": "Le nomine del file producite", "smw-smwdoc-description": "Presenta un tabella de tote le parametros que pote esser usate pro le formato de resultatos specificate, insimul con lor valores predefinite e lor descriptiones.", "smw-smwdoc-default-no-parameter-list": "Iste formato de resultato non forni parametros specific al formato.", "smw-smwdoc-par-format": "Le formato de resultatos pro le qual presentar le documentation de parametros.", "smw-smwdoc-par-parameters": "Le parametros a monstrar: \"specific\" pro illos addite per le formato, \"base\" pro illos disponibile in tote le formatos, e \"all\" pro ambes.", "smw-paramdesc-sort": "Proprietate per le qual ordinar le consulta", "smw-paramdesc-order": "Ordinamento del consulta", "smw-paramdesc-searchlabel": "Texto pro continuar le recerca", "smw-paramdesc-named_args": "Nominar le parametros passate al patrono", "smw-paramdesc-template-arguments": "Defini le maniera in que le argumentos nominate es passate al patrono", "smw-paramdesc-import-annotation": "Altere datos annotate debe esser copiate durante le analyse syntactic del subjecto", "smw-paramdesc-export": "Option de exportation", "smw-paramdesc-prettyprint": "Imprimer in disposition plus nette con additional indentationes e saltos de linea", "smw-paramdesc-json-unescape": "Le output continera barras oblique non escappate e characteres Unicode multibyte", "smw-paramdesc-json-type": "Typo de serialisation", "smw-paramdesc-source": "Fonte alternative de consultas", "smw-paramdesc-jsonsyntax": "Syntaxe JSON a usar", "smw-printername-feed": "Syndication RSS e Atom", "smw-paramdesc-feedtype": "Typo de syndication", "smw-paramdesc-feedtitle": "Le texto a usar como titulo del syndication", "smw-paramdesc-feeddescription": "Le texto a usar como description del syndication", "smw-paramdesc-feedpagecontent": "Le contento del pagina a monstrar con le syndication", "smw-label-feed-description": "Syndication $2 $1", "smw-paramdesc-mimetype": "Le typo de multimedia (typo MIME) pro le file resultante", "smw_iq_disabled": "Le consultas semantic ha essite disactivate pro iste wiki.", "smw_iq_moreresults": "… ulterior resultatos", "smw_parseerror": "Le valor date non esseva comprendite.", "smw_decseparator": ",", "smw_kiloseparator": "&nbsp;", "smw_notitle": "\"$1\" non pote esser usate como nomine de pagina in iste wiki.", "smw_noproperty": "\"$1\" non pote esser usate como nomine de proprietate in iste wiki.", "smw_wrong_namespace": "Solmente le paginas in le spatio de nomines \"$1\" es permittite hic.", "smw_manytypes": "Plus de un typo definite pro proprietate.", "smw_emptystring": "Series de characteres vacue non es acceptate.", "smw_notinenum": "\"$1\" non es in le lista de [[Property:Allows value|valores permittite]] ($2) pro le proprietate \"$3\".", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" non es in le lista de [[Property:Allows value|valores permittite]] ($2) pro le proprietate \"$3\".", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" non es in le intervallo \"$2\" specificate per le restriction de [[Property:Allows value|valores permittite]] del proprietate \"$3\".", "smw-constraint-error": "Problema de restriction", "smw-constraint-error-suggestions": "Per favor verifica le violationes e proprietates listate insimul con lor valores annotate pro assecurar que tote le exigentias del restriction es satisfacite.", "smw-constraint-error-limit": "Le lista continera un maximo de $1 violationes.", "smw_noboolean": "\"$1\" non es recognoscite como un valor boolean (ver/false).", "smw_true_words": "ver,v,si,s", "smw_false_words": "false,f,no,n", "smw_nofloat": "\"$1\" non es un numero.", "smw_infinite": "Le numeros tanto grande como \"$1\" non es supportate.", "smw_unitnotallowed": "\"$1\" non es declarate como unitate de mesura valide pro iste proprietate.", "smw_nounitsdeclared": "Nulle unitate de mesura ha essite declarate pro iste proprietate.", "smw_novalues": "Nulle valor specificate.", "smw_nodatetime": "Le data \"$1\" non esseva comprendite.", "smw_toomanyclosing": "Il pare haber troppo de occurrentias de \"$1\" in le consulta.", "smw_noclosingbrackets": "Alcun uso de \"<nowiki>[[</nowiki>\" in tu consulta non esseva claudite per un correspondente \"]]\".", "smw_misplacedsymbol": "Le symbolo \"$1\" esseva usate in un loco ubi illo non es utile.", "smw_unexpectedpart": "Le parte \"$1\" del consulta non esseva comprendite.\nLe resultatos pote non esser como expectate.", "smw_emptysubquery": "Alcun subconsulta ha nulle condition valide.", "smw_misplacedsubquery": "Alcun subconsulta esseva usate in un loco ubi nulle subconsultas es permittite.", "smw_valuesubquery": "Le subconsultas non es supportate pro valores del proprietate \"$1\".", "smw_badqueryatom": "Alcun parte \"<nowiki>[[…]]</nowiki>\" del consulta non esseva comprendite.", "smw_propvalueproblem": "Le valor del proprietate \"$1\" non esseva comprendite.", "smw_noqueryfeature": "Alcun functionalitate de consulta non esseva supportate in iste wiki; un parte del consulta ha essite omittite ($1).", "smw_noconjunctions": "Le conjunctiones in consultas non es supportate in iste wiki; un parte del consulta ha essite ommittite ($1).", "smw_nodisjunctions": "Le disjunctiones in consultas non es supportate in iste wiki; un parte del consulta ha essite omittite ($1).", "smw_querytoolarge": "Le sequente {{PLURAL:$2|condition|conditiones}} de consulta non poteva esser considerate a causa del restrictiones de iste wiki concernente le grandor o profunditate del consultas: <code>$1</code>.", "smw_notemplategiven": "Forni un valor pro le parametro \"template\" pro facer functionar iste formato de consulta.", "smw_db_sparqlqueryproblem": "Le resultato del consulta non poteva esser obtenite del base de datos SPARQL. Iste error pote esser temporari o pote indicar un defecto in le software del base de datos.", "smw_db_sparqlqueryincomplete": "Responder al consulta provava troppo difficile e ha essite abortate. Alcun resultatos pote mancar. Si possibile, tenta usar un consulta plus simple.", "smw_type_header": "Proprietates del typo \"$1\"", "smw_typearticlecount": "Presentation de $1 {{PLURAL:$1|proprietate|proprietates}} que usa iste typo.", "smw_attribute_header": "Paginas que usa le proprietate \"$1\"", "smw_attributearticlecount": "Presentation de $1 {{PLURAL:$1|pagina|paginas}} que usa iste proprietate.", "smw-propertylist-subproperty-header": "Subproprietates", "smw-propertylist-redirect-header": "Synonymos", "smw-propertylist-error-header": "Paginas con assignationes improprie", "smw-propertylist-count": "Es monstrate $1 {{PLURAL:$1|entitate|entitates}} associate.", "smw-propertylist-count-with-restricted-note": "Es monstrate $1 {{PLURAL:$1|entitate|entitates}} associate (il ha plus, ma le presentation es limitate a \"$2\").", "smw-propertylist-count-more-available": "Es monstrate $1 {{PLURAL:$1|entitate|entitates}} associate (il ha alteres disponibile).", "specialpages-group-smw_group": "MediaWiki semantic", "specialpages-group-smw_group-maintenance": "Mantenentia", "specialpages-group-smw_group-properties-concepts-types": "Proprietates, conceptos e typos", "specialpages-group-smw_group-search": "Navigar e cercar", "exportrdf": "Exportar paginas verso RDF", "smw_exportrdf_docu": "Iste pagina permitte obtener le datos de un pagina in formato RDF.\nPro exportar paginas, entra le titulos in le quadro de texto in basso, un titulo per linea.", "smw_exportrdf_recursive": "Exportar recursivemente tote le paginas associate.\nNota que le resultato pote esser grande!", "smw_exportrdf_backlinks": "Exportar equalmente tote le paginas con referentias al paginas exportate.\nGenera un RDF navigabile.", "smw_exportrdf_lastdate": "Non exportar paginas que non esseva cambiate post le momento specificate.", "smw_exportrdf_submit": "Exportar", "uriresolver": "Resolvitor de URI", "properties": "Proprietates", "smw-categories": "Categorias", "smw_properties_docu": "Le sequente proprietates es usate in le wiki.", "smw_property_template": "$1 del typo $2 ($3 {{PLURAL:$3|uso|usos}})", "smw_propertylackspage": "Tote le proprietates debe esser describite per un pagina!", "smw_propertylackstype": "Nulle typo esseva specificate pro iste proprietate (es assumite le typo $1 pro le momento).", "smw_propertyhardlyused": "Iste proprietate es a pena usate in iste wiki!", "smw-property-name-invalid": "Le proprietate $1 non pote esser usate (nomine de proprietate invalide).", "smw-property-name-reserved": "\"$1\" es listate como nomine reservate e non debe esser usate como proprietate. Iste [https://www.semantic-mediawiki.org/wiki/Help:Property_naming pagina de adjuta] pote continer information explicante proque iste nomine ha essite reservate.", "smw-sp-property-searchform": "Mon<PERSON>r proprietates que contine:", "smw-sp-property-searchform-inputinfo": "Le entrata distingue inter majusculas e minusculas. Quando usate pro filtration, solmente le proprietates que corresponde al condition es monstrate.", "smw-special-property-searchform": "Mon<PERSON>r proprietates que contine:", "smw-special-property-searchform-inputinfo": "Le entrata distingue inter majusculas e minusculas. Quando usate pro filtration, solmente le proprietates que corresponde al condition es monstrate.", "smw-special-property-searchform-options": "Optiones", "smw-special-wantedproperties-filter-label": "Filtro:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Non approbate", "smw-special-wantedproperties-filter-unapproved-desc": "Option de filtrage usate in connexion con le modo de autoritate.", "concepts": "Conceptos", "smw-special-concept-docu": "Un [https://www.semantic-mediawiki.org/wiki/Help:Concepts concept] pote esser reguardate como \"categoria dynamic\", i.e. como collection de paginas que non es create manualmente, ma que es computate per Semantic MediaWiki a partir de un description de un consulta fornite.", "smw-special-concept-header": "Lista de conceptos", "smw-special-concept-count": "Le sequente {{PLURAL:$1|concepto|$1 conceptos}} es listate.", "smw-special-concept-empty": "<PERSON><PERSON>e concepto trovate.", "unusedproperties": "Proprietates non usate", "smw-unusedproperties-docu": "Iste pagina lista le [https://www.semantic-mediawiki.org/wiki/Unused_properties proprietates non usate] que es declarate ben que nulle altere pagina los utilisa. Pro un vista differentiate, vide le paginas [[Special:Properties|tote le proprietates]] o [[Special:WantedProperties|proprietates desirate]].", "smw-unusedproperty-template": "$1 del typo $2", "wantedproperties": "Proprietates desirate", "smw-wantedproperties-docu": "Iste pagina lista le [https://www.semantic-mediawiki.org/wiki/Wanted_properties proprietates desirate] que es usate in le wiki ma que non ha un pagina que los describe. Pro un vista differentiate, vide le paginas [[Special:Properties|tote le proprietates]] o  [[Special:UnusedProperties|proprietates non usate]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw-special-wantedproperties-docu": "Iste pagina lista le [https://www.semantic-mediawiki.org/wiki/Wanted_properties proprietates desirate] que es usate in le wiki ma non ha un pagina que los describe. Pro un vista differentiate, vide le paginas special listante [[Special:Properties|tote le proprietates]] o le  [[Special:UnusedProperties|proprietates non usate]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw_purge": "<PERSON><PERSON><PERSON><PERSON>", "smw-purge-update-dependencies": "Semantic MediaWiki purga le pagina actual a causa de alcun dependentias obsolete que illo ha detegite, que necessita un actualisation.", "smw-purge-failed": "Semantic MediaWiki ha tentate a purgar le pagina ma ha fallite", "types": "Typos", "smw_types_docu": "Lista del [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes typos de datos disponibile] in que cata [https://www.semantic-mediawiki.org/wiki/Help:Datatype typo] representa un insimul unic de attributos pro describer un valor in terminos de characteristicas de immagazinage e visualisation que se heredita in un proprietate assignate.", "smw-special-types-no-such-type": "\"$1\" es incognite o non ha essite specificate como typo de datos valide.", "smw-statistics": "Statisticas semantic", "smw-statistics-cached": "Statisticas semantic (in cache)", "smw-statistics-entities-total": "Entitates (total)", "smw-statistics-entities-total-info": "Un numero approximative de lineas de entitates. Illo include proprietates, conceptos, o qualcunque altere representation de objecto registrate que require le assignation de un identificator.", "smw-statistics-property-instance": "Valor{{PLURAL:$1||es}} de proprietate (total)", "smw-statistics-property-total": "[[Special:Properties|Proprietate{{PLURAL:$1||s}}]] (total)", "smw-statistics-property-total-info": "Le total de proprietates registrate.", "smw-statistics-property-total-legacy": "Proprietate{{PLURAL:$1||s}} (total)", "smw-statistics-property-used": "Proprietate{{PLURAL:$1||s}} (usate con al minus un valor)", "smw-statistics-property-page": "Proprietate{{PLURAL:$1||s}} (registrate con un pagina)", "smw-statistics-property-page-info": "Numero de proprietates que ha un pagina dedicate e un description.", "smw-statistics-property-type": "Proprietate{{PLURAL:$1||s}} (assignate a un typo de datos)", "smw-statistics-query-inline-legacy": "Consulta{{PLURAL:$1||s}}", "smw-statistics-query-inline": "[[Property:Has query|Consulta{{PLURAL:$1||s}}]] (incastrate, total)", "smw-statistics-query-format": "formato <code>$1</code>", "smw-statistics-query-size": "Dimension del consulta", "smw-statistics-concept-count-legacy": "Concepto{{PLURAL:$1||s}}", "smw-statistics-concept-count": "[[Special:Concepts|Concepto{{PLURAL:$1||s}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|Subobjecto{{PLURAL:$1||s}}]]", "smw-statistics-subobject-count-legacy": "Subobjecto{{PLURAL:$1||s}}", "smw-statistics-datatype-count": "[[Special:Types|Typo{{PLURAL:$1||s}} de datos]]", "smw-statistics-error-count": "Valor{{PLURAL:$1||es}} de proprietate ([[Special:ProcessingErrorList|annotation{{PLURAL:$1||es}} incorrecte]])", "smw-statistics-error-count-legacy": "Valor{{PLURAL:$1||es}} de proprietate (annotation{{PLURAL:$1||es}} incorrecte)", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities Entitate{{PLURAL:$1||s}} obsolete]", "smw-statistics-delete-count-info": "Entitates que ha essite marcate pro remotion debe esser eliminate regularmente usante le scripts de mantenentia fornite.", "smw_uri_doc": "Le resolvitor de URL implementa le [$1 conclusion del Gruppo de Architectura Technic del W3C a proposito de httpRange-14].\nIllo assecura le livration de un representation in RDF (pro machinas) o un pagina wiki (pro humanos), dependente del requesta.", "ask": "Recerca semantic", "smw-ask-help": "Iste section contine alcun ligamines pro adjutar a explicar como usar le syntaxe <code>#ask</code>:\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Seliger paginas] describe como seliger paginas e construer conditiones;\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operatores de recerca] lista le operatores de recerca disponibile, includente illos pro consultas que usa intervallos o metacharacteres;\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Monstrar information] explica le uso de declarationes de sortita e optiones de formatation.", "smw_ask_sortby": "Ordinar per columna (optional)", "smw_ask_ascorder": "Ascendente", "smw_ask_descorder": "Descendente", "smw-ask-order-rand": "<PERSON><PERSON><PERSON>", "smw_ask_submit": "Cercar resultatos", "smw_ask_editquery": "Modificar consulta", "smw_add_sortcondition": "[Adder condition de ordinamento]", "smw-ask-sort-add-action": "Adder condition de ordinamento", "smw_ask_hidequery": "Celar consulta (vista compacte)", "smw_ask_help": "Adjuta sur consultas", "smw_ask_queryhead": "Condition", "smw_ask_printhead": "Selection de datos a monstrar", "smw_ask_printdesc": "(adde un nomine de proprietate per linea)", "smw_ask_format_as": "Formatar in:", "smw_ask_defaultformat": "predefinition", "smw_ask_otheroptions": "Altere optiones", "smw-ask-otheroptions-info": "Iste section contine optiones que altera declarationes de sortita. Le descriptiones de parametros pote esser vidite passante le mouse supra illos.", "smw-ask-otheroptions-collapsed-info": "Per favor usa le icone de plus (+) pro vider tote le optiones disponibile", "smw_ask_show_embed": "<PERSON><PERSON>r codice de incastrar", "smw_ask_hide_embed": "Celar codice de incastrar", "smw_ask_embed_instr": "Pro incastrar iste consulta in linea in un pagina wiki usa le codice sequente.", "smw-ask-delete": "Remover", "smw-ask-sorting": "Ordinamento", "smw-ask-options": "Optiones", "smw-ask-options-sort": "Optiones de ordinamento", "smw-ask-format-options": "Formato e optiones", "smw-ask-parameters": "Parametros", "smw-ask-search": "Recerca", "smw-ask-debug": "<PERSON><PERSON><PERSON> problema", "smw-ask-debug-desc": "Genera information de depuration de consultas", "smw-ask-no-cache": "Disactivar cache de consultas", "smw-ask-no-cache-desc": "Resultatos sin cache de consultas", "smw-ask-result": "Resultato", "smw-ask-empty": "Rader tote le entratas", "smw-ask-download-link-desc": "Discargar le resultatos del consulta in formato $1", "smw-ask-format": "Formato", "smw-ask-format-selection-help": "Adjuta con le formato seligite: $1", "smw-ask-condition-change-info": "Le condition ha essite alterate e le motor de recerca debe re-executar le consulta pro producer resultatos que corresponde al nove requisitos.", "smw-ask-input-assistance": "Assistentia de entrata", "smw-ask-condition-input-assistance": "Le [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance assistentia de entrata] es fornite pro le campos de impression, ordinamento e condition. Le campo de condition require un del prefixos sequente:", "smw-ask-condition-input-assistance-property": "<code>p:</code> pro obtener suggestiones de proprietate (p.ex. <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> pro obtener suggestiones de categoria", "smw-ask-condition-input-assistance-concept": "<code>con:</code> pro obtener suggestiones de concepto", "smw-ask-format-change-info": "Le formato ha essite modificate e le consulta debe esser executate de novo pro corresponder al nove parametros e optiones de visualisation.", "smw-ask-format-export-info": "Le formato seligite es un formato de exportation que non ha representation visual, pro isto le resultatos es solmente fornite como discargamento.", "smw-ask-query-search-info": "Le consulta <code><nowiki>$1</nowiki></code> ha essite respondite per le {{PLURAL:$3|1=<code>$2</code> (ab cache)|<code>$2</code> (ab cache)|<code>$2</code>}} in $4 secunda{{PLURAL:$4||s}}.", "smw-ask-extra-query-log": "Registro de consultas", "smw-ask-extra-other": "Altere", "searchbyproperty": "Cercar per proprietate", "processingerrorlist": "Lista de errores de processamento", "constrainterrorlist": "Lista de errores de restriction", "propertylabelsimilarity": "Reporto de similaritates de etiquettas de proprietate", "missingredirectannotations": "Annotationes de redirection mancante", "smw-processingerrorlist-intro": "Le lista sequente forni un vista general sur le [https://www.semantic-mediawiki.org/wiki/Processing_errors errores de processamento] que ha apparite in connexion con [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Es recommendate surveliar iste lista regularmente e corriger annotationes de valor invalide.", "smw-constrainterrorlist-intro": "Le lista sequente forni un vista general sur le [https://www.semantic-mediawiki.org/wiki/Constraint_errors errores de restriction] que ha apparite in connexion con [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Es recommendate surveliar iste lista regularmente e corriger annotationes de valor invalide.", "smw-missingredirects-intro": "Le sequente section listara paginas in le quales manca annotationes de [https://www.semantic-mediawiki.org/wiki/Redirects redirection] in Semantic MediaWiki (comparante con information immagazinate in MediaWiki). Pro restaurar iste annotationes, tu pote [https://www.semantic-mediawiki.org/wiki/Help:Purge purgar] le pagina manualmente o exequer le script de mantenentia <code>rebuildData.php</code> (con option <code>--redirects</code>).", "smw-missingredirects-list": "Paginas con annotationes mancante", "smw-missingredirects-list-intro": "Appare $1 pagina{{PLURAL:$1||s}} con annotationes de redirection mancante.", "smw-missingredirects-noresult": "Necun annotation de redirection mancante trovate.", "smw_sbv_docu": "Cercar tote le paginas que ha un proprietate e valor date.", "smw_sbv_novalue": "Entra un valor valide pro le proprietate, o vide tote le valores possibile del proprietate \"$1\".", "smw_sbv_displayresultfuzzy": "Un lista de tote le paginas que ha le proprietate \"$1\" con valor \"$2\".\nPost que il ha pauc resultatos, le valores proxime es equalmente monstrate.", "smw_sbv_property": "Proprietate:", "smw_sbv_value": "Valor:", "smw_sbv_submit": "Cercar resultatos", "browse": "Percurrer le wiki", "smw_browselink": "<PERSON><PERSON><PERSON> proprietates", "smw_browse_article": "Entra le nomine del pagina ab le qual tu vole initiar le exploration.", "smw_browse_go": "Va", "smw_browse_show_incoming": "Mon<PERSON>r proprietates entrante", "smw_browse_hide_incoming": "Celar proprietates entrante", "smw_browse_no_outgoing": "Iste pagina non ha proprietates.", "smw_browse_no_incoming": "Nulle proprietate ha un ligamine a iste pagina.", "smw-browse-from-backend": "Le information es in curso de recuperation del servitor.", "smw-browse-intro": "Iste pagina forni detalios sur un subjecto o un instantia de entitate. Per favor entra le nomine de un objecto a inspectar.", "smw-browse-invalid-subject": "Le validation del subjecto ha retornate con un error \"$1\".", "smw-browse-api-subject-serialization-invalid": "Le subjecto ha un formato de serialisation non valide.", "smw-browse-js-disabled": "Pare que JavaScript es disactivate o indisponibile. Es recommendate usar un navigator que lo supporta. Altere optiones es discutite sur le pagina del parametro de configuration [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "Mon<PERSON>r gruppos", "smw-browse-hide-group": "<PERSON>lar gruppos", "smw-noscript": "Iste pagina o action require JavaScript pro functionar. Per favor activa JavaScript in tu navigator o usa un navigator que lo supporta, de sorta que le functionalitate pote esser fornite como demandate. Pro ulterior assistentia, per favor consulta le pagina de adjuta [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "Etiquetta de proprietate inverse", "pageproperty": "Recerca de proprietates de paginas", "pendingtasklist": "Lista de cargas pendente", "facetedsearch": "Recerca in faciettas", "smw_pp_docu": "Entra un pagina e un proprietate, o entra solmente un proprietate pro obtener tote le valores assignate.", "smw_pp_from": "Del pagina:", "smw_pp_type": "Proprietate:", "smw_pp_submit": "Cercar resultatos", "smw-prev": "$1 {{PLURAL:$1|precedente|precedentes}}", "smw-next": "$1 {{PLURAL:$1|sequente|sequentes}}", "smw_result_prev": "Precedente", "smw_result_next": "Se<PERSON>e", "smw_result_results": "Resultatos", "smw_result_noresults": "<PERSON>ulle resultato.", "smwadmin": "Tabuliero de instrumentos pro Semantic MediaWiki", "smw-admin-statistics-job-title": "Statisticas de travalios", "smw-admin-statistics-job-docu": "Le statisticas de travalios monstra information sur le travalios programmate de Semantic MediaWiki que non ha ancora essite exequite. Le numero de travalios pote esser legiermente inexacte o continer tentativas fallite. Per favor consulta le [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manual] pro ulterior informationes.", "smw-admin-statistics-querycache-title": "<PERSON><PERSON> de <PERSON>", "smw-admin-statistics-querycache-disabled": "Le [https://www.semantic-mediawiki.org/wiki/QueryCache cache de consultas] non ha essite activate sur iste wiki, e pro isto nulle statisticas es disponibile.", "smw-admin-statistics-querycache-legend": "Le statisticas del cache debe continer datos provisional cumulative e derivate, includente:\n* \"misses\" es le total de tentativas de obtener datos del cache con responsas inattingibile, fortiante un recuperation directe del repositorio (base de datos, triple-store, etc.)\n* \"deletes\" es le numero total de operationes de eviction del cache (sia per medio de un purga, sia per un dependentia de consulta)\n* \"hits\" contine le numero de recuperationes del cache de fontes incastrate (consultas appellate a partir de un pagina wiki) o non incastrate (si activate, requestate per paginas como Special:Ask o per le API)\n* \"medianRetrievalResponseTime\" es un valor de orientation del tempore de responsa median (in secundas) pro requestas de recuperation del cache e non del cache durante le tempore del processo de collection\n* \"noCache\" indica le numero de requestas sin tentativa de obtener resultatos del cache (consultas limit=0, option 'no-cache' etc.)", "smw-admin-statistics-section-explain": "Le section forni statisticas additional pro administratores.", "smw-admin-statistics-semanticdata-overview": "Vista general", "smw-admin-permission-missing": "Le accesso a iste pagina ha essite blocate a causa de permissiones mancante. Per favor consulta le pagina de adjuta sur le [https://www.semantic-mediawiki.org/wiki/Help:Permissions permissiones] pro detalios sur le configurationes necessari.", "smw-admin-setupsuccess": "Le motor de immagazinage ha essite configurate.", "smw_smwadmin_return": "Retornar a $1", "smw_smwadmin_updatestarted": "Un nove processo de actualisation pro le refrescamento del datos semantic ha essite comenciate.\nTote le datos immagazinate essera reconstruite o reparate ubi necessari.\nTu pote sequer le progresso del actualisation in iste pagina special.", "smw_smwadmin_updatenotstarted": "Il ha jam un processo de actualisation in curso.\nNon es possibile crear un altere.", "smw_smwadmin_updatestopped": "Tote le processos de actualisation existente ha essite stoppate.", "smw_smwadmin_updatenotstopped": "Pro stoppar le processo de actualisation in curso, tu debe marcar le quadrato pro indicar que tu es absolutemente secur de voler facer isto.", "smw-admin-docu": "Iste pagina special te adjuta durante le installation, actualisation, mantenentia e uso de <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> e forni anque altere functionalitate administrative assi como statisticas.\nNon oblida de facer un copia de reserva del datos importante ante de executar functiones administrative.", "smw-admin-environment": "Ambiente de software", "smw-admin-db": "Configuration del base de datos", "smw-admin-db-preparation": "Le initialisation del tabella es in curso e pote prender un momento ante que le resultatos es monstrate, dependente del dimension e del possibile optimisationes del tabella.", "smw-admin-dbdocu": "Semantic MediaWiki require su proprie structura del base de datos pro immagazinar le datos semantic. (Iste structura es independente de MediaWiki, dunque non affecta le resto del installation.)\nIste function de installation pote esser executate plure vices sin causar damno, ma es necessari solmente un vice post cata installation o actualisation.", "smw-admin-permissionswarn": "Si le operation falle con errores SQL, le usator del base de datos que tu wiki usa (verifica tu file \"LocalSettings.php\") probabilemente non ha permissiones sufficiente.\nO concede a iste usator le permissiones additional de crear e deler tabellas, o insere temporarimente in le file \"LocalSettings.php\" le credentiales pro accesso \"root\" a tu base de datos, o usa le script de mantenentia <code>setupStore.php</code>, le qual pote usar le credentiales de un administrator.", "smw-admin-dbbutton": "<PERSON><PERSON><PERSON> o <PERSON><PERSON>r tabellas", "smw-admin-announce": "Annunciar tu wiki", "smw-admin-announce-text": "Si tu wiki es public, tu pote registrar lo sur <a href=\"https://wikiapiary.com\">WikiApiary</a>, le wiki de traciamento de wikis.", "smw-admin-deprecation-notice-title": "Avisos de obsolescentia", "smw-admin-deprecation-notice-docu": "Le sequente section contine parametros que ha essite declarate obsolescente o eliminate, ma que ha essite detegite como active sur iste wiki. Es previste que un version futur removera le supporto de iste configurationes.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> es obsolescente e essera removite in $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> removera (o reimplaciara) le sequente option{{PLURAL:$2||es}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> es obsolescente e essera removite in $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ha essite reimplaciate per <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ha essite reimplaciate per <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "Option{{PLURAL:$2||es}} pro <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> es reimplciate per <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ha essite removite in $2", "smw-admin-deprecation-notice-title-notice": "Parametros obsolescente", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Parametros obsolescente</b> monstra parametros que ha essite detegite como usate sur iste wiki e que essera removite o cambiate in un version futur.", "smw-admin-deprecation-notice-title-replacement": "Parametros reimplaciate o renominate", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Parametros reimplaciate o renominate</b> contine parametros que ha essite renominate o alteremente modificate e es recommendate actualisar immediatemente lor nomine o formato.", "smw-admin-deprecation-notice-title-removal": "Parametros removite", "smw-admin-deprecation-notice-title-removal-explanation": "Parametros removite<b></b> identifica le parametros que ha essite removite in un version anterior, ma que ha essite detegite como usate sur iste wiki.", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw-smwadmin-refresh-title": "Reparation e actualisation de datos", "smw_smwadmin_datarefresh": "Reconstruction de datos", "smw_smwadmin_datarefreshdocu": "Es possibile restaurar tote le datos de Semantic MediaWiki a base del conento actual del wiki.\nIsto pote esser utile pro reparar datos corrumpite o pro refrescar le datos si le formato interne ha cambiate a causa de alcun actualisation de software.\nLe actualisation es executate pagina a pagina e non essera completate immediatemente.\nLo sequente monstra si un actualisation es in progresso e permitte comenciar o stoppar le actualisationes (a minus que iste function ha essite disactivate per le administrator del sito).", "smw_smwadmin_datarefreshprogress": "<strong>Un actualisation es ja in curso.</strong>\nEs normal que le actualisation progrede lentemente post que illo refresca datos solmente in micre pecias cata vice que un usator accede al wiki.\nPro completar iste actualisation plus rapidemente, tu pote invocar le script de mantenentia de MediaWiki <code>runJobs.php</code> (usa le option <code>--maxjobs 1000</code> pro restringer le numero de actualisationes facite in un lot).\nProgresso estimate del actualisation currente:", "smw_smwadmin_datarefreshbutton": "Planificar le reconstruction del datos", "smw_smwadmin_datarefreshstop": "Stoppar iste actualisation", "smw_smwadmin_datarefreshstopconfirm": "Si, io es {{GENDER:$1|secur}}.", "smw-admin-job-scheduler-note": "Le cargas (illos que es activate) in iste section es realisate via le cauda de actiones pro evitar situationes de blocage durante lor execution. Le [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cauda de actiones] es responsabile del processamento e il es de importantia critic que le script de mantenentia <code>runJobs.php</code> ha un capacitate appropriate (vide etiam le parametro de configuration <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Elimination de entitates obsolete", "smw-admin-outdateddisposal-intro": "Alcun activitates (un cambio de un typo de proprietate, le remotion de paginas wiki, o le correction de valores erronee) resultara in [https://www.semantic-mediawiki.org/wiki/Outdated_entities entitates obsolete] e es recommendate remover los periodicamente pro liberar le spatio occupate per lor tabellas.", "smw-admin-outdateddisposal-active": "Un travalio de elimination de entitates obsolete ha essite programmate.", "smw-admin-outdateddisposal-button": "Programmar elimination", "smw-admin-feature-disabled": "Iste function ha essite disactivate sur iste wiki. Per favor consulta le pagina de adjuta sur le <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">parametros</a> o contacta le administrator del systema.", "smw-admin-propertystatistics-title": "Reconstruction del statisticas de proprietates", "smw-admin-propertystatistics-intro": "Reconstrue tote le statisticas de uso de proprietates e actualisa e corrige le [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count contator de usos] de proprietates.", "smw-admin-propertystatistics-active": "Un travalio de reconstruction del statisticas de proprietates ha essite programmate.", "smw-admin-propertystatistics-button": "Programmar reconstruction del statisticas", "smw-admin-fulltext-title": "Reconstruction del recerca de texto complete", "smw-admin-fulltext-intro": "Reconstrue le indice de recerca a partir de tabellas de proprietates con un typo de datos de [https://www.semantic-mediawiki.org/wiki/Full-text recerca de texto complete] activate. Cambiamentos in le regulas del indice (cambiamentos de parolas non significative, un lemmatisator nove, etc.) e/o un tabella novemente addite o alterate necessita exequer iste travalio de novo.", "smw-admin-fulltext-active": "Un travalio de reconstruction del recerca de texto complete ha essite programmate.", "smw-admin-fulltext-button": "Programmar reconstruction del recerca de texto complete", "smw-admin-support": "Obtener supporto", "smw-admin-supportdocu": "Plure ressources es disponibile pro adjutar te in caso de problemas:", "smw-admin-installfile": "Si tu incontra problemas con tu installation, comencia per verificar le directivas in le <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">file INSTALL</a> e le <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">pagina de installation</a>.", "smw-admin-smwhomepage": "Le documentation de usator complete de Semantic MediaWiki se trova a <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Defectos pote esser reportate in le <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">traciator de problemas</a>. Le pagina <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">signalar problemas</a> explica como reportar un problema de maniera effective.", "smw-admin-questions": "Si tu ha altere questiones o suggestiones, participa al <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">lista de discussion</a> pro le usatores de Semantic MediaWiki.", "smw-admin-other-functions": "Altere functiones", "smw-admin-statistics-extra": "Functiones statistic", "smw-admin-statistics": "Statisticas", "smw-admin-supplementary-section-title": "Functiones supplementari", "smw-admin-supplementary-section-subtitle": "Functiones central supportate", "smw-admin-supplementary-section-intro": "Iste section forni functiones additional ultra le ambito de activitates de mantenentia e il es possibile que alcun functiones que es listate (vide le [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentation]) es restricte o indisponibile e pro isto inaccessibile sur iste wiki.", "smw-admin-supplementary-settings-title": "Configuration e parametros", "smw-admin-supplementary-settings-intro": "<u>$1</u> monstra parametros que defini le comportamento de Semantic MediaWiki", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Statisticas operational", "smw-admin-supplementary-operational-statistics-short-title": "statisticas operational", "smw-admin-supplementary-operational-statistics-intro": "Monstra un insimul extendite de <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Recerca e elimination de entitates", "smw-admin-supplementary-idlookup-short-title": "recerca e elimination de entitates", "smw-admin-supplementary-idlookup-intro": "Supporta un function simple de <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Recerca de entitates duplicate", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> pro trovar entitates que es categorisate como duplicatos pro le matrice de tabellas seligite", "smw-admin-supplementary-duplookup-docu": "Iste pagina lista entratas de tabellas selecte que ha essite categorisate como [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities duplicatos]. Entratas duplicate (si occurre del toto) debe occurrer solmente in occasiones rar, potentialmente causate per un actualisation interrumpite o un transaction de revocation (''rollback'') non succedite.", "smw-admin-supplementary-operational-statistics-cache-title": "Statisticas del cache", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> monstra un insimul selecte de statisticas concernente le cache", "smw-admin-supplementary-operational-table-statistics-title": "Statisticas de tabellas", "smw-admin-supplementary-operational-table-statistics-short-title": "statisticas de tabellas", "smw-admin-supplementary-operational-table-statistics-intro": "Genera <u>$1</u> pro un insimul seligite de tabellas", "smw-admin-supplementary-operational-table-statistics-explain": "Iste section contine statisticas de tabellas selecte pro adjutar administratores e curatores de datos a prender decisiones informate sur le stato del servitor e del motor de immagazinage.", "smw-admin-supplementary-operational-table-statistics-legend": "Le legenda describe alcunes del claves usate pro le statisticas de tabellas e include:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> numero total de lineas in un tabella", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> le ultime identificator actualmente in uso\n* <code>duplicate_count</code> numero de duplicatos trovate in le id_table (vide etiam [[Special:SemanticMediaWiki/duplicate-lookup|Recerca de entitates duplicate]])\n* <code>rows.rev_count</code> numero de lineas que ha un revision_id assignate, indicante un ligamine directe a un pagina wiki\n* <code>rows.smw_namespace_group_by_count</code> numeros de lineas aggregate pro spatios de nomines usate in le tabella\n* <code>rows.smw_proptable_hash.query_match_count</code> numero de subobjectos de consulta con un referentia de tabella correspondente\n* <code>rows.smw_proptable_hash.query_null_count</code> numero de subobjectos de consulta sin referentia de tabella (disligate, referentia flottante)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> percentage de terminos que es unic (un percentage basse indica que terminos repetitive occupa le contento de tabella e indice)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> numero de terminos que appare solmente un vice\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> numero de terminos que appare plus de un vice", "smw-admin-supplementary-elastic-version-info": "Version", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> monstra detalios sur parametros e statisticas de indice", "smw-admin-supplementary-elastic-docu": "Iste pagina contine informationes sur parametros, mappamentos, sanitate, e statisticas de indice associate a un racemo Elasticsearch connectite a Semantic MediaWiki e su [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Functiones supportate", "smw-admin-supplementary-elastic-settings-title": "Parametros (indices)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> usate per Elasticsearch pro gerer indices de Semantic MediaWiki", "smw-admin-supplementary-elastic-mappings-title": "Mappamentos", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> pro listar indices e mappamentos de campos", "smw-admin-supplementary-elastic-mappings-docu": "Iste pagina contine detalios de mappamento de campos usate per le indice actual. Es recommendate surveliar le mappamentos in connexion con le <code>index.mapping.total_fields.limit</code> (specifica le numero maxime de campos permittite in un indice).", "smw-admin-supplementary-elastic-mappings-docu-extra": "Le <code>property_fields</code> se refere al numero de campos central indexate, e le <code>nested_fields</code> se refere al numero accumulate de campos additional assignate a un campo central pro supportar patronos de recerca structurate specific.", "smw-admin-supplementary-elastic-mappings-summary": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-mappings-fields": "Mappamentos de campos", "smw-admin-supplementary-elastic-nodes-title": "Nodos", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> monstra statisticas de nodos", "smw-admin-supplementary-elastic-indices-title": "Indices", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> forni un vista general de indices disponibile e lor statisticas", "smw-admin-supplementary-elastic-statistics-title": "Statisticas", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> monstra statisticas a nivello de indice", "smw-admin-supplementary-elastic-statistics-docu": "Iste pagina forni un vision del statisticas de indices pro differente operationes que occurre a un nivello de indice, le statisticas retornate es aggregate con aggregationes primari e total. Le [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html pagina de adjuta] contine un description detaliate de statisticas de indices disponibile.", "smw-admin-supplementary-elastic-status-replication": "Stato de replication", "smw-admin-supplementary-elastic-status-last-active-replication": "Ultime replication active: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Intervallo de refrescamento: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Travalios de recuperation pendente: $1 (estimation)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Travalios de ingestion (de files) pendente: $1 (estimation)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replication blocate: $1 (reconstruction in curso)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Surveliantia de replication (active): $1", "smw-admin-supplementary-elastic-replication-header-title": "Stato de replication", "smw-admin-supplementary-elastic-replication-function-title": "Replication", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> monstra information sur replicationes fallite", "smw-admin-supplementary-elastic-replication-docu": "Iste pagina forni information sur le [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring stato de replication] de entitates que ha essite reportate de haber problemas con le racemo Elasticsearch. Es recommendate revider le entitates listate e purgar le contento pro confirmar que se tractava de un problema temporari.", "smw-admin-supplementary-elastic-replication-files-docu": "Debe esser notate que, pro le lista de files, es obligatori que le travalio de [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion ingestion de files] se executa primo e conclude su processamento.", "smw-admin-supplementary-elastic-replication-files": "Files", "smw-admin-supplementary-elastic-replication-pages": "Pa<PERSON><PERSON>", "smw-admin-supplementary-elastic-endpoints": "Punctos final", "smw-admin-supplementary-elastic-config": "Configurationes", "smw-admin-supplementary-elastic-no-connection": "Le wiki es actualmente '''incapace''' de establir un connexion con le racemo Elasticsearch. Per favor contacta le administrator del wiki pro investigar le problema, perque illo rende indisponibile le capacitate de indexation e de recerca del systema.", "smw-list-count": "Le lista contine $1 entrata{{PLURAL:$1||s}}.", "smw-property-label-uniqueness": "Le etiquetta \"$1\" coincideva con al minus un altere representation de proprietate. Per favor consulta le [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness pagina de adjuta] sur como resolver iste problema.", "smw-property-label-similarity-title": "Reporto de similaritates de etiquettas de proprietate", "smw-property-label-similarity-intro": "<u>$1</u> calcula similaritates pro etiquettas existente de proprietates", "smw-property-label-similarity-threshold": "Limine:", "smw-property-label-similarity-type": "Monstrar identificator de typo", "smw-property-label-similarity-noresult": "Nulle resultato ha essite trovate pro le optiones seligite.", "smw-property-label-similarity-docu": "Iste pagina comparara le [https://www.semantic-mediawiki.org/wiki/Property_similarity distantia de similaritate] (non confunder con un similaritate semantic o lexical) inter etiquettas de proprietate e los reporta si illos excede le limine. Le reporto pote adjutar a filtrar proprietates mal orthographiate o equivalente que representa le mesme concepto (vide le pagina special [[Special:Properties|proprietates]] pro adjutar a clarificar le concepto e uso del proprietates reportate). Le limine pote esser adjustate pro augmentar o reducer le distantia usate pro le correspondentia per approximation. <code>[[Property:$1|$1]]</code> es usate pro exemptar proprietates del analyse.", "smw-admin-operational-statistics": "Iste pagina contine statisticas operational colligite in o ab functiones de Semantic MediaWiki. Un lista extendite de statisticas specific de wikis pote esser trovate [[Special:Statistics|<b>hic</b>]].", "smw_adminlinks_datastructure": "Structura de datos", "smw_adminlinks_displayingdata": "<PERSON><PERSON><PERSON>", "smw_adminlinks_inlinequerieshelp": "Adjuta sur consultas integrate", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Numero de usos] estimate: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Proprietate definite per le {{PLURAL:$1|usator|systema}}", "smw-property-indicator-last-count-update": "Numero estimate de usos\nUltime actualisation: $1", "smw-concept-indicator-cache-update": "Contator del cache\nUltime actualisation: $1", "smw-createproperty-isproperty": "Es un proprietate del typo $1.", "smw-createproperty-allowedvals": "Le {{PLURAL:$1|valor|valores}} permittite pro iste proprietate es:", "smw-paramdesc-category-delim": "Le delimitator", "smw-paramdesc-category-template": "Un patrono con le qual formatar le elementos", "smw-paramdesc-category-userparam": "Un parametro a passar al patrono", "smw-info-par-message": "Le message a monstrar.", "smw-info-par-icon": "Le icone a monstrar, o \"info\" o \"attention!\".", "prefs-smw": "MediaWiki semantic", "prefs-general-options": "Optiones general", "prefs-extended-search-options": "Recerca extendite", "prefs-ask-options": "Recerca semantic", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] e extensiones associate forni preferentias individual pro un gruppo selecte de functiones e characteristicas. Un lista de parametros individual con lor description e characteristicas es disponibile sur le sequente [https://www.semantic-mediawiki.org/wiki/Help:User_preferences pagina de adjuta].", "smw-prefs-ask-options-tooltip-display": "Mon<PERSON>r le texto del parametro como un legenda emergente de information sur le pagina special del [[Special:Ask|constructor de consultas]] #ask.", "smw-prefs-ask-options-compact-view-basic": "Activar le vista compacte basic", "smw-prefs-help-ask-options-compact-view-basic": "Si activate, monstra un insimul reducite de ligamines sur le vista compacte de Special:Ask", "smw-prefs-general-options-time-correction": "Activar le correction horari pro paginas special usante le preferentia local [[Special:Preferences#mw-prefsection-rendering|Differentia de tempore]].", "smw-prefs-general-options-jobqueue-watchlist": "Monstrar le observatorio del cauda de actiones in mi barra personal", "smw-prefs-help-general-options-jobqueue-watchlist": "Si activate, monstra un [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist lista] de travalios pendente seligite con lor dimension de cauda estimate.", "smw-prefs-general-options-disable-editpage-info": "Disactivar le texto introductori sur le pagina de modification", "smw-prefs-general-options-disable-search-info": "Disactivar le information de adjuta al syntaxe sur le pagina de recerca standard", "smw-prefs-general-options-suggester-textinput": "Activar le assistentia de entrata pro entitates semantic", "smw-prefs-help-general-options-suggester-textinput": "Si activate, permitte usar un [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance assistentia de entrata] pro cercar proprietates, conceptos, e categorias ab un contexto de entrata.", "smw-prefs-general-options-show-entity-issue-panel": "Monstra le pannello de problemas de entitate", "smw-prefs-help-general-options-show-entity-issue-panel": "<PERSON> <PERSON>, facera verificationes de integritate sur cata pagina e monstrara le [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel pannello de problemas de entitate].", "smw-prefs-factedsearch-profile": "Selige un profilo predefinite pro le [[Special:FacetedSearch|recerca in faciettas]]:", "smw-ui-tooltip-title-property": "Proprietate", "smw-ui-tooltip-title-quantity": "Conversion de unitates", "smw-ui-tooltip-title-info": "Information", "smw-ui-tooltip-title-service": "Ligamines de servicio", "smw-ui-tooltip-title-warning": "Advertimento", "smw-ui-tooltip-title-error": "Error", "smw-ui-tooltip-title-parameter": "Parametro", "smw-ui-tooltip-title-event": "Evento", "smw-ui-tooltip-title-note": "<PERSON>a", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "Referentia", "smw_unknowntype": "Le typo \"$1\" de iste proprietate non es valide.", "smw-concept-cache-text": "Le concepto ha un total de $1 pagina{{PLURAL:$1||s}} e ha essite actualisate pro le ultime vice le $2 a $3.", "smw_concept_header": "Paginas del concepto \"$1\"", "smw_conceptarticlecount": "Presentation de $1 {{PLURAL:$1|pagina|paginas}}.", "smw-qp-empty-data": "Le datos requestate non poteva esser monstrate a causa de alcun criterios de selection insufficiente.", "right-smw-admin": "Accesso al cargas administrative (Semantic MediaWiki)", "right-smw-patternedit": "Accesso de modification pro mantener le expressiones regular e patronos permittite (Semantic MediaWiki)", "right-smw-pageedit": "Accesso de modification pro paginas annotate <code>Is edit protected</code> (Semantic MediaWiki)", "right-smw-schemaedit": "Modificar [https://www.semantic-mediawiki.org/wiki/Help:Schema paginas de schema] (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "Accesso al [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist observatorio] del cauda de actiones (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Acceder a information sur un disaccordo de versiones associate a un entitate (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "Vider le [https://www.semantic-mediawiki.org/wiki/Help:Edit_help adjuta al modification] (Semantic MediaWiki)", "restriction-level-smw-pageedit": "protegite (solmente usatores eligibile)", "action-smw-patternedit": "modificar expressiones regular usate per Semantic MediaWiki", "action-smw-pageedit": "modificar paginas annotate con <code>Is edit protected</code> (Semantic MediaWiki)", "group-smwadministrator": "Administratores (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrator (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:<PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator": "Curatores (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|curator (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:Curatores (Semantic MediaWiki)", "group-smweditor": "Redactores (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|redactor|redactrice}} (Semantic MediaWiki)", "grouppage-smweditor": "{{ns:project}}:Redactores (Semantic MediaWiki)", "action-smw-admin": "acceder al cargas administrative de Semantic MediaWiki", "action-smw-ruleedit": "modificar paginas de regulas (Semantic MediaWiki)", "smw-property-namespace-disabled": "Le proprietate [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks spatio de nomines] es disactivate. Non es possibile declarar un typo o altere characteristicas specific de proprietates pro iste proprietate.", "smw-property-predefined-default": "\"$1\" es un proprietate predefinite del typo $2.", "smw-property-predefined-common": "Iste proprietate es pre-installate (etiam cognoscite como [https://www.semantic-mediawiki.org/wiki/Help:Special_properties proprietate special]) e veni con privilegios administrative additional, ma pote esser usate como qualcunque altere [https://www.semantic-mediawiki.org/wiki/Property proprietate definite per usatores].", "smw-property-predefined-ask": "\"$1\" es un proprietate predefinite que representa meta-information (in forma de un [https://www.semantic-mediawiki.org/wiki/Subobject subobjecto]) sur consultas individual e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "\"$1\" es un proprietate predefinite que collige le numero de conditiones usate in un consulta e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "\"$1\" es un proprietate predefinite que informa sur le profunditate de un consulta e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "Es un valor numeric computate a base del annidation de subconsultas, del catenas de proprietates, e del elementos de description disponibile, le execution de un consulta essente restringite per le parametro de configuration <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "\"$1\" es un proprietate predefinite que describe le parametros que influentia le resultato de un consulta e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "Es parte de un collection de proprietates que specifica un [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler profilo de consultas].", "smw-sp-properties-docu": "Iste pagina lista le [https://www.semantic-mediawiki.org/wiki/Property proprietates] disponibile sur iste wiki e lor numeros de usos. Pro mantener le statisticas sempre actual, es recommendate executar regularmente le script de [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics mantenentia del statisticas de proprietates]. Pro un vista differentiate, vide le paginas special [[Special:UnusedProperties|proprietates non usate]] o [[Special:WantedProperties|proprietates desirate]].", "smw-sp-properties-cache-info": "Le datos listate ha essite recuperate del [https://www.semantic-mediawiki.org/wiki/Caching cache]. Ultime actualisation: $1.", "smw-sp-properties-header-label": "Lista de proprietates", "smw-admin-settings-docu": "Monstra un lista de tote le parametros predefinite e localisate que es relevante al ambiente de Semantic MediaWiki. Pro detalios sur parametros individual, per favor consulta le pagina de adjuta sur le [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration].", "smw-sp-admin-settings-button": "Generar lista de parametros", "smw-admin-idlookup-title": "Cercar", "smw-admin-idlookup-docu": "Iste section monstra detalios technic sur un entitate individual (pagina wiki, subobjecto, proprietate, etc.) in Semantic MediaWiki. Le entrata pote esser un identificator numeric o un valor textual pro coincider con le campo de recerca pertinente, ma tote referentia de identificator es relative a Semantic MediaWiki e non al identificator de pagina o version de MediaWiki.", "smw-admin-iddispose-title": "Elimination", "smw-admin-iddispose-docu": "Debe esser notate que le operation de elimination es sin restrictiones e removera le entitate del motor de immagazinage insimul con tote su referentias in tabellas pendente, si confirmate. Per favor, exeque iste operation con '''caution''' e solmente post que le [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentation] ha essite consultate.", "smw-admin-iddispose-done": "Le identificator \"$1\" ha essite removite del servitor de immagazinage.", "smw-admin-iddispose-references": "Le identificator \"$1\" {{PLURAL:$2|non ha alcun|ha al minus un}} referentia active:", "smw-admin-iddispose-references-multiple": "Lista de correspondentias con al minus un registro active de referentias.", "smw-admin-iddispose-no-references": "Le recerca non ha trovate un entrata de tabella que corresponde a \"$1\".", "smw-admin-idlookup-input": "Cercar:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Vista general", "smw-admin-tab-notices": "Avisos de obsolescentia", "smw-admin-tab-maintenance": "Mantenentia", "smw-admin-tab-supplement": "Functiones supplementari", "smw-admin-tab-registry": "Registro", "smw-admin-tab-alerts": "<PERSON><PERSON><PERSON>", "smw-admin-alerts-tab-deprecationnotices": "Avisos de obsolescentia", "smw-admin-alerts-tab-maintenancealerts": "Alertas de mantenentia", "smw-admin-alerts-section-intro": "Iste section monstra alertas e notificationes relative a configurationes, operationes, e altere activitates que ha essite classificate como requirente attention de un administrator o usator con derectos appropriate.", "smw-admin-maintenancealerts-section-intro": "Le sequente alertas e notificationes debe esser resolvite, e ben que non es essential, es previste que isto adjuta a meliorar le mantenibilitate del systema e de operationes.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Optimisation de tabellas", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Le systema ha trovate que le ultime [https://www.semantic-mediawiki.org/wiki/Table_optimization optimisation de tabellas] ha essite exequite $2 dies retro (registro del $1), lo que excede le limite de mantenentia de $3 dies. Como mentionate in le documentation, le execution de optimisationes permittera al planificator de consultas de prender melior decisiones sur consultas, pro isto es recommendate exequer regularmente le optimisation de tabellas.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Entitates obsolete", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Le systema ha contate $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities entitates obsolete] e ha attingite un nivello critic de mantenentia arretrate, excedente le limite de $2. Es recommendate executar le script de mantenentia [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>].", "smw-admin-maintenancealerts-invalidentities-alert-title": "Entitates non valide", "smw-admin-maintenancealerts-invalidentities-alert": "Le systema ha detegite que $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities entitate{{PLURAL:$1||s}}] es in un spatio de nomines non mantenite. Es recommendate executar le script de mantenentia [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] o [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>].", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Parametros", "smw-admin-configutation-tab-namespaces": "Spatios de nomines", "smw-admin-configutation-tab-schematypes": "Typos de schema", "smw-admin-maintenance-tab-tasks": "Cargas", "smw-admin-maintenance-tab-scripts": "Scripts de mantenentia", "smw-admin-maintenance-no-description": "Sin description.", "smw-admin-maintenance-script-section-title": "Lista de scripts de mantenentia disponibile", "smw-admin-maintenance-script-section-intro": "Le sequente scripts de mantenentia require un administrator e accesso al linea de commando pro poter executar le scripts listate.", "smw-admin-maintenance-script-description-dumprdf": "Exportation in RDF de triplettos existente.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Iste script es usate pro gerer le caches de conceptos pro Semantic MediaWiki, ubi illo pote crear, remover, e actualisar le caches seligite.", "smw-admin-maintenance-script-description-rebuilddata": "Recrea tote le datos semantic in le base de datos, iterante per tote le paginas que poterea haber datos semantic.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Reconstrue le indice de Elasticsearch (solmente pro installationes que usa le <code>ElasticStore</code>), iterante per tote le entitates que ha datos semantic.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Cercar entitates mancante in Elasticsearch (solmente pro installationes que usa le <code>ElasticStore</code>) e programmar travalios de actualisation appropriate.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Reconstrue le indice de recerca de texto integral de <code>SQLStore</code> (pro installationes ubi le configuration ha essite activate).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Reconstrue le statisticas de uso pro tote le entitates de proprietate.", "smw-admin-maintenance-script-description-removeduplicateentities": "Remove entitates duplicate trovate in tabellas seligite que non ha referentias active.", "smw-admin-maintenance-script-description-setupstore": "Configura le servitor de immagazinage e de consultas como es definite in <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Actualisa le campo <code>smw_sort</code> in <code>SQLStore</code> (secundo le parametro [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "<PERSON><PERSON> le campo <code>smw_hash</code> pro lineas in le quales iste non ha valor.", "smw-admin-maintenance-script-description-purgeentitycache": "Purga entratas de cache pro entitates cognoscite e lor datos associate.", "smw-admin-maintenance-script-description-updatequerydependencies": "Actualisa consultas e dependentias de consulta (vide le parametro [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Eliminar entitates e ligamines de consulta obsolete.", "smw-admin-maintenance-script-description-runimport": "Reimpler e importar contento auto-discoperite ab [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Scripts de actualisation", "smw-admin-maintenance-script-section-rebuild": "Scripts de reconstruction", "smw-livepreview-loading": "Cargamento in curso…", "smw-sp-searchbyproperty-description": "Iste pagina forni un [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces interfacie de navigation] simple pro trovar entitates describite per un proprietate e un valor nominate. Altere interfacies de recerca disponibile include le [[Special:PageProperty|recerca de proprietate de pagina]] e le [[Special:Ask|constructor de consultas <code>ask</code>]].", "smw-sp-searchbyproperty-resultlist-header": "Lista de resultatos", "smw-sp-searchbyproperty-nonvaluequery": "Un lista de valores que ha le proprietate \"$1\" assignate.", "smw-sp-searchbyproperty-valuequery": "Un lista de paginas que ha le proprietate \"$1\" con valor \"$2\" annotate.", "smw-datavalue-number-textnotallowed": "\"$1\" non pote esser assignate a un typo de numero declarate con valor $2.", "smw-datavalue-number-nullnotallowed": "\"$1\" ha retornate con \"NULL\" le qual non es permittite como numero.", "smw-editpage-annotation-enabled": "Iste pagina supporta annotationes semantic in le texto (p.ex. <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) pro construer contento structurate e consultabile fornite per Semantic MediaWiki. Pro un description comprehensive de como usar annotationes o del function de analysator #ask, per favor visita iste paginas de adjuta: [https://www.semantic-mediawiki.org/wiki/Help:Getting_started como initiar], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation annotation in texto], o [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries consultas in linea].", "smw-editpage-annotation-disabled": "Iste pagina non es activate pro annotationes semantic in le texto a causa de restrictiones de spatio de nomines. Detalios sur como activar le spatio de nomines pote esser trovate in le pagina de adjuta sur le [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration].", "smw-editpage-property-annotation-enabled": "Iste proprietate pote esser extendite usante annotationes semantic pro specificar un typo de datos (p.ex. <nowiki>\"[[Has type::Page]]\"</nowiki>) o altere declarationes secundari (p.ex. <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). Pro un description de como augmentar iste pagina, vide le pagina de adjuta [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaration de un proprietate] o le pagina de adjuta [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes lista de typos de datos disponibile].", "smw-editpage-property-annotation-disabled": "Iste proprietate non pote esser extendite con un annotation de typo de datos (p.ex. <nowiki>\"[[Has type::Page]]\"</nowiki>) perque illo es ja predefinite (vide le pagina de adjuta sur le [https://www.semantic-mediawiki.org/wiki/Help:Special_properties proprietates special] pro plus informationes).", "smw-editpage-concept-annotation-enabled": "Iste concepto pote esser extendite usante le function de analysator #concept. Pro un description de como usar #concept, vide le pagina de adjuta sur le [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceptos].", "smw-search-syntax-support": "Le entrata de recerca supporta le uso del [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search syntaxe de consulta semantic] pro adjutar a trovar resultatos usante Semantic MediaWiki.", "smw-search-input-assistance": "Le [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance assistente de entrata] es equalmente activate pro facilitar le pre-selection de proprietates e categorias disponibile.", "smw-search-help-intro": "Un entrata <code><nowiki>[[ ... ]]</nowiki></code> signalara al processor de entrata de usar le servitor de recerca de Semantic MediaWiki. Debe esser notate que combinar <code><nowiki>[[ ... ]]</nowiki></code> con un recerca de texto non structurate como <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> non es supportate.", "smw-search-help-structured": "Recercas structurate:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (como [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context contexto filtrate])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (con un [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context contexto de consulta])", "smw-search-help-proximity": "Recercas de proximitate (un proprietate essente incognite, '''solmente''' disponibile pro le servitores que forni un integration de recerca de texto integral):\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code> (cercar \"lorem\" e \"ipsum\" in tote le documentos que ha essite indexate)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (trovar \"lorem ipsum\" como phrase)", "smw-search-help-ask": "Le sequente ligamines explica como usar le syntaxe <code>#ask</code>:\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selection de paginas] describe como seliger paginas e construer conditiones;\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operatores de recerca] lista le operatores de recerca disponibile, includente illos pro consultas de intervallo e con metacharacteres.", "smw-search-input": "Entrata e recerca", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Assistentia de entrata] es fornite pro le campo de entrata e necessita usar un del sequente prefixos:\n\n*<code>p:</code> pro activar le suggestiones de proprietates (p.ex. <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> pro activar le suggestiones de categorias\n\n*<code>con:</code> pro activar le suggestiones de conceptos", "smw-search-syntax": "Syntaxe", "smw-search-profile": "Extendite", "smw-search-profile-tooltip": "Functiones de recerca in connexion con Semantic MediaWiki", "smw-search-profile-sort-best": "Melior correspondentia", "smw-search-profile-sort-recent": "Le plus recente", "smw-search-profile-sort-title": "Titulo", "smw-search-profile-extended-help-intro": "Le [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile profilo extendite] de Special:Recerca forni accesso a functiones de recerca specific a Semantic MediaWiki e su servitor de consultas supportate.", "smw-search-profile-extended-help-sort": "Specifica un preferentia de ordinamento pro le presentation de resultatos con:", "smw-search-profile-extended-help-sort-title": "* \"Titulo\" usante le titulo del pagina (o le titulo a monstrar) como criterio de ordinamento", "smw-search-profile-extended-help-sort-recent": "* \"Le plus recente\" monstrara primo le entitates le plus recentemente modificate (entitates de subobjecto essera supprimite perque ille entitates non es annotate con un [[Property:Modification date|data de modification]])", "smw-search-profile-extended-help-sort-best": "* \"Melior correspondentia\" ordinara le entitates per [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy relevantia] a base del scores/punctos fornite per le servitor", "smw-search-profile-extended-help-form": "Formularios es providite (si mantenite) pro trovar casos de uso specific, exponente differente campos de proprietate e valor pro restringer le processo de entrata e facilitar al usatores de proceder con un requesta de recerca. (vide $1)", "smw-search-profile-extended-help-namespace": "Le quadro de selection de spatio de nomines essera celate quando un formulario es seligite, ma pote esser rendite visibile per medio del button \"monstrar/celar\".", "smw-search-profile-extended-help-search-syntax": "Le campo de entrata de recerca supporta le uso del syntaxe <code>#ask</code> pro definir un contexto de recerca specific de Semantic MediaWiki. Expressiones utile include:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> pro trovar tote lo que contine \"...\" e es particularmente utile quando le contexto de recerca o proprietates implicate es incognite (p.ex. <code>in:(lorem && ipsum)</code> es equivalente a <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> pro trovar tote lo que contine \"...\" in exactemente le mesme ordine", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> pro trovar qualcunque entitate con un proprietate \"...\" (p.ex. <code>has:(Foo && Bar)</code> es equivalente a <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> pro excluder omne entitate que include \"...\"", "smw-search-profile-extended-help-search-syntax-prefix": "* Prefixos additional personalisate es disponibile e definite, como: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Alcun expressiones es reservate, como: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Alcunes del operationes listate es solmente utile in connexion con un indice de texto integral activate o con le ElasticStore.''", "smw-search-profile-extended-help-query": "Ha usate <code><nowiki>$1</nowiki></code> como consulta.", "smw-search-profile-extended-help-query-link": "Pro plus detalios, per favor usa le $1.", "smw-search-profile-extended-help-find-forms": "le formularios disponibile", "smw-search-profile-extended-section-sort": "Ordinar per", "smw-search-profile-extended-section-form": "Formularios", "smw-search-profile-extended-section-search-syntax": "Entrata de recerca", "smw-search-profile-extended-section-namespace": "<PERSON><PERSON><PERSON> de nomines", "smw-search-profile-extended-section-query": "Consulta", "smw-search-profile-link-caption-query": "constructor de consultas", "smw-search-show": "<PERSON><PERSON><PERSON>", "smw-search-hide": "<PERSON><PERSON>", "log-name-smw": "Registro de Semantic MediaWiki", "log-show-hide-smw": "$1 le registro de Semantic MediaWiki", "logeventslist-smw-log": "Registro de Semantic MediaWiki", "log-description-smw": "Activitates pro le [https://www.semantic-mediawiki.org/wiki/Help:Logging typos de evento activate] que ha essite reportate per Semantic MediaWiki e su componentes.", "logentry-smw-maintenance": "Eventos concernente le mantenentia emittite per Semantic MediaWiki", "smw-datavalue-import-unknown-namespace": "Le spatio de nomines de importation \"$1\" es incognite. Per favor assecura te que le detalios de importation OWL es disponibile via [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Impossibile trovar un URI de spatio de nomines \"$1\" in le [[MediaWiki:Smw import $1|importation de $1]].", "smw-datavalue-import-missing-type": "Nulle definition de typo esseva trovate pro \"$1\" in le [[MediaWiki:Smw import $2|importation de $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Importation de $1]]", "smw-datavalue-import-invalid-value": "\"$1\" non es un formato valide e es expectate que illo consiste de \"spatio de nomines\":\"identificator\" (p.ex. \"foaf:nomine\").", "smw-datavalue-import-invalid-format": "Se expectava que le catena \"$1\" es dividite in quatro partes, ma le formato non esseva comprendite.", "smw-property-predefined-impo": "\"$1\" es un proprietate predefinite que describe un relation a un [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabulario importate] e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "\"$1\" es un proprietate predefinite que describe le [[Special:Types|typo de datos]] de un proprietate e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "\"$1\" es un proprietate predefinite que representa un constructo de [https://www.semantic-mediawiki.org/wiki/Help:Container contentor] e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "Le contentor permitte accumular assignationes de proprietate-valor similar a un pagina wiki normal, ma intra un spatio de entitates differente, ben que illo es ligate al subjecto que lo incorpora.", "smw-property-predefined-errp": "\"$1\" es un proprietate predefinite que tracia errores de entrata pro annotationes de valores irregular e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "In le major parte de casos, illo es causate per un disaccordo de typo o un restriction de [[Property:Allows value|valor]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] es un proprietate predefinite que pote definir un lista de valores permittite pro restringer assignationes de valor pro un proprietate e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list \"$1\"] es un proprietate predefinite que pote specificar un referentia a un lista que contine valores permittite pro restringer assignationes de valor pro un proprietate e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "Le proprietate \"$1\" ha un area de application restricte e non pote esser usate como proprietate de annotation per un usator.", "smw-datavalue-property-restricted-declarative-use": "Le proprietate \"$1\" es un proprietate declarative e pote esser usate solmente in un pagina de proprietate o categoria.", "smw-datavalue-property-create-restriction": "Le proprietate \"$1\" non existe e le usator non dispone del permission \"$2\" (vide [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoritate]) pro crear o annotar valores con un proprietate non approbate.", "smw-datavalue-property-invalid-character": "\"$1\" contine un character \"$2\" listate como parte del etiquetta de proprietate e ha essite classificate como non valide.", "smw-datavalue-property-invalid-chain": "Usar \"$1\" como catena de proprietate non es permittite durante le processo de annotation.", "smw-datavalue-restricted-use": "Le valor de datos \"$1\" ha essite marcate pro uso restricte.", "smw-datavalue-invalid-number": "\"$1\" non pote esser interpretate como numero.", "smw-query-condition-circular": "Un possibile condition circular ha essite detegite in \"$1\".", "smw-query-condition-empty": "Le description del consulta ha un condition vacue.", "smw-types-list": "Lista de typos de datos", "smw-types-default": "\"$1\" es un typo de datos integrate.", "smw-types-help": "Ulterior information e exemplos pote esser trovate sur iste [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 pagina de adjuta].", "smw-type-anu": "\"$1\" es un variante del typo de datos [[Special:Types/URL|URL]] e es usate principalmente pro un declaration de exportation ''owl:AnnotationProperty''.", "smw-type-boo": "\"$1\" es un typo de datos basic pro describer un valor ver/false.", "smw-type-cod": "\"$1\" es un variante del typo de datos [[Special:Types/Text|Texto]] que es destinate pro esser usate con textos technic de qualcunque longitude, como codice fonte.", "smw-type-geo": "\"$1\" es un typo de datos que describe locos geographic e require le extension [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] pro fornir un functionalitate extendite.", "smw-type-tel": "\"$1\" es un typo de datos special pro describer numeros de telephono international secundo RFC 3966.", "smw-type-txt": "\"$1\" es un typo de datos basic pro describer catenas de longitude arbitrari.", "smw-type-dat": "\"$1\" es un typo de datos basic pro representar punctos in tempore in un formato unificate.", "smw-type-ema": "\"$1\" es un typo de datos special pro representar un e-mail.", "smw-type-tem": "\"$1\" es un typo de datos numeric special pro representar un temperatura.", "smw-type-qty": "\"$1\" es un typo de datos pro describer quantitates con un representation numeric e un unitate de mesura.", "smw-type-rec": "\"$1\" es un typo de datos de contentor que specifica un lista de proprietates con typos in un ordine fixe.", "smw-type-extra-tem": "Le schema de conversion include unitates supportate com<PERSON>, <PERSON><PERSON><PERSON>, Fahrenheit, e Rankine.", "smw-type-tab-properties": "Proprietates", "smw-type-tab-types": "Typos", "smw-type-tab-type-ids": "Identificatores de typo", "smw-type-tab-errors": "Errores", "smw-type-primitive": "Basic", "smw-type-contextual": "Contextual", "smw-type-compound": "Composite", "smw-type-container": "Contentor", "smw-type-no-group": "Non classificate", "smw-special-pageproperty-description": "Iste pagina forni un interfacie de navigation pro trovar tote le valores de un proprietate e un pagina date. Altere interfacies de recerca disponibile include le [[Special:SearchByProperty|recerca de proprietates]] e le [[Special:Ask|constructor de consultas \"ask\"]].", "smw-property-predefined-errc": "\"$1\" es un proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] que representa errores connexe a mal annotationes de valores o mal tractamento de entrata.", "smw-property-predefined-long-errc": "Le errores se collige in un [https://www.semantic-mediawiki.org/wiki/Help:Container receptaculo] que pote includer un referentia al proprietate que ha causate le discrepantia.", "smw-property-predefined-errt": "\"$1\" es un proprietate predefinite que contine un description textual de un error e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Un subobjecto definite per usator contineva un schema de nomination invalide. Le notation de puncto ($1) usate in le prime cinque characteres es reservate pro extensiones. Tu pote definir un [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier identificator nominate].", "smw-datavalue-record-invalid-property-declaration": "Le definition del registro contine le proprietate \"$1\" que es declarate illo mesme como typo de registro e isto non es permittite.", "smw-property-predefined-mdat": "\"$1\" es un proprietate predefinite que corresponde al data del ultime modification de un subjecto e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "\"$1\" es un proprietate predefinite que corresponde al data del prime version de un subjecto e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "\"$1\" es un proprietate predefinite que indica si un subjecto es nove o non e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "\"$1\" es un proprietate predefinite que contine le nomine de pagina del usator qui creava le ultime version e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "\"$1\" es un proprietate predefinite que describe le typo MIME de un file incargate e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "\"$1\" es un proprietate predefinite que describe le typo multimedial de un file incargate e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "\"$1\" es un proprietate predefinite que contine le nomine del formato de resultato usate in un consulta e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "\"$1\" es un proprietate predefinite que describe le conditiones del consulta como un catena e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "\"$1\" es un proprietate predefinite que contine le tempore (in secundas) que esseva necessari pro completar le execution del consulta e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "\"$1\" es un proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] que identifica fontes alternative de consultas (per exemplo, remote, federate).", "smw-property-predefined-askco": "\"$1\" es un proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro describer le stato de un consulta o su componentes.", "smw-property-predefined-long-askco": "Le numero o numeros assignate representa un stato codificate interne que es explicate sur le [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler pagina de adjuta].", "smw-property-predefined-prec": "\"$1\" es un proprietate predefinite que describe un [https://www.semantic-mediawiki.org/wiki/Help:Display_precision precision de presentation] (in digitos decimal) pro typos de datos numeric.", "smw-property-predefined-attch-link": "\"$1\" es un proprietate predefinite que collige ligamines a files e imagines incastrate sur un pagina e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-inst": "\"$1\" es un proprietate predefinite interne que immagazina information de categorias independentemente de MediaWiki e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-unit": "\"$1\" es un proprietate predefinite declarative pro definir unitates de presentation pro proprietates numeric con typos e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-unit": "Un lista separate per commas permitte describer le unitates o formatos a usar pro le visualisation.", "smw-property-predefined-conv": "\"$1\" es un proprietate predefinite declarative pro definir le factor de conversion pro un certe unitate de un quantitate physic e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-serv": "\"$1\" es un proprietate predefinite declarative pro adder ligamines de servicio a un proprietate e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-redi": "\"$1\" es un proprietate predefinite interne pro registrar redirectiones e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subp": "\"$1\" es un proprietate predefinite declarative pro definir que un proprietate es un [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of subproprietate de] un altere e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subc": "\"$1\" es un proprietate predefinite pro definir que un categoria es un [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of subcategoria de] un altere e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-conc": "\"$1\" es un proprietate predefinite interne pro definir un concepto associate e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-err-type": "\"$1\" es un proprietate predefinite pro identificar un gruppo o classe de [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors errores de processamento] e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-skey": "\"$1\" es un proprietate predefinite interne pro continer un referentia de ordinamento e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pplb": "\"$1\" es un proprietate predefinite declarative pro specificar un [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label etiquetta de proprietate preferite] e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-chgpro": "\"$1\" es un proprietate predefinite pro continer informationes sur le [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation propagation de modificationes] e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-link": " e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-format-schema": " e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-profile-schema": " e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans": " e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-source": " e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-group": " e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cont-len": "\"$1\" es un proprietate predefinite pro immagazinar informationes de longitude e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-len": "Illo es usate in connexion con le [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e le [https://www.semantic-mediawiki.org/Attachment_processor processor de annexos]) pro colliger e immagazinar informationes de longitude obtenite ab un file ingerite (si fornite).", "smw-property-predefined-cont-lang": "\"$1\" es un proprietate predefinite pro immagazinar informationes de lingua e es fornite per Semantic MediaWiki (https://www.semantic-mediawiki.org/wiki/Help:Special_properties).", "smw-property-predefined-long-cont-lang": "Illo es usate in connexion con le [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e le [https://www.semantic-mediawiki.org/Attachment_processor processor de annexos]) pro colliger e immagazinar information de lingua recuperate ab un file ingerite (si fornite).", "smw-property-predefined-cont-title": "\"$1\" es un proprietate predefinite pro immagazinar informationes de titulo e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-title": "Illo es usate in connexion con le [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e le [https://www.semantic-mediawiki.org/Attachment_processor processor de annexos]) pro colliger e immagazinar informationes de titulo extrahite ab un file ingerite (si fornite).", "smw-property-predefined-cont-author": "\"$1\" es un proprietate predefinite pro immagazinar informationes de autor e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-author": "Illo es usate in connexion con le [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e le [https://www.semantic-mediawiki.org/Attachment_processor processor de annexos]) pro colliger e immagazinar informationes de autor obtenite ab un file ingerite (si fornite).", "smw-property-predefined-cont-date": "\"$1\" es un proprietate predefinite pro immagazinar informationes de data e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-date": "Illo es usate in connexion con le [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e le [https://www.semantic-mediawiki.org/Attachment_processor processor de annexos]) pro colliger e immagazinar informationes de data extrahite ab un file ingerite (si fornite).", "smw-property-predefined-cont-type": "\"$1\" es un proprietate predefinite pro immagazinar informationes de typo de file e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-type": "Illo es usate in connexion con le [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e le [https://www.semantic-mediawiki.org/Attachment_processor processor de annexos]) pro colliger e immagazinar informationes de typo de file extrahite ab un file ingerite (si fornite).", "smw-property-predefined-cont-keyw": "\"$1\" es un proprietate predefinite pro representar parolas clave e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-keyw": "Illo es usate in connexion con le [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e le [https://www.semantic-mediawiki.org/Attachment_processor processor de annexos]) pro colliger e immagazinar parolas clave extrahite ab un file ingerite (si fornite).", "smw-property-predefined-file-attch": "\"$1\" es un proprietate predefinite pro representar un contentor que immagazina information de annexos e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-file-attch": "Illo es usate in connexion con le [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (e le [https://www.semantic-mediawiki.org/Attachment_processor processor de annexos]) pro colliger tote le informationes specific al contento recuperabile ab un file ingerite (si fornite).", "smw-types-extra-geo-not-available": "Le [https://www.semantic-mediawiki.org/wiki/Extension:Maps extension \"Maps\"] non esseva detegite, pro isto \"$1\" es restringite in su capacitate de operation.", "smw-datavalue-monolingual-dataitem-missing": "Manca un elemento expectate pro construer un valor composite monolingue.", "smw-datavalue-languagecode-missing": "Pro le annotation \"$1\", le analysator syntactic non poteva determinar un codice de lingua (i.e. \"foo@en\").", "smw-datavalue-languagecode-invalid": "\"$1\" non es recognoscite como codice de lingua supportate.", "smw-property-predefined-lcode": "\"$1\" es un proprietate predefinite que representa un codice de lingua formate secundo BCP47 e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "\"$1\" es un typo de datos [https://www.semantic-mediawiki.org/wiki/Help:Container contentor] que associa un valor de texto con un [[Property:Language code|codice de lingua]] specific.", "smw-types-extra-mlt-lcode": "Le typo de datos {{PLURAL:$2||non}} require un codice de lingua (i.e., un annotation de valor sin codice de lingua {{PLURAL:$2|non|}} es acceptate).", "smw-property-predefined-text": "\"$1\" es un proprietate predefinite que representa texto de longitude arbitrari e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "\"$1\" es un proprietate predefinite que permitte describer un proprietate in le contexto de un lingua e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "\"$1\" es un proprietate predefinite pro definir un lista de proprietates usate con un proprietate de typo [[Special:Types/Record|registro]] e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Tempore de analyse syntactic de annotationes in texto", "smw-limitreport-intext-postproctime": "[SMW] tempore post processamento", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|secunda|secundas}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|secunda|secundas}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Tempore de actualisation de immagazinage (sur un purga de pagina)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|secunda|secundas}}", "smw_allows_pattern": "Iste pagina se expecta de continer un lista de referentias (sequite per [https://en.wikipedia.org/wiki/Regular_expression expressiones regular]) a render disponibile per le proprietate [[Property:Allows pattern|Permitte patrono]]. Pro modificar iste pagina, le derecto <code>smw-patternedit</code> es necessari.", "smw-datavalue-allows-pattern-mismatch": "\"$1\" ha essite classificate como invalide per le expression regular \"$2\".", "smw-datavalue-allows-pattern-reference-unknown": "Le referentia de patrono \"$1\" non corresponde a un entrata in [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "Le referentia de lista \"$1\" non corresponde a un pagina [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "Le contento de lista \"$1\" non contine elementos con un marcator de lista *.", "smw-datavalue-feature-not-supported": "Le functionalitate \"$1\" non es supportate o ha essite disactivate sur iste wiki.", "smw-property-predefined-pvap": "\"$1\" es un proprietate predefinite que pote specificar un [[MediaWiki:Smw allows pattern|referentia de patrono]] pro applicar correspondentia de [https://en.wikipedia.org/wiki/Regular_expression expressiones regular] e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "\"$1\" es un proprietate predefinite que pote assignar un titulo de presentation distincte a un entitate e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "\"$1\" es un proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro restringer assignationes de valores a fin que cata instantia sia unic (o un, al maximo).", "smw-property-predefined-long-pvuc": "Unicitate es establite quando duo valores non es equal in lor representation literal e omne violation de iste restriction essera categorisate como error.", "smw-datavalue-constraint-uniqueness-violation": "Le proprietate \"$1\" permitte solmente assignationes de valores unic e ''$2'' ja habeva essite annotate in le subjecto \"$3\".", "smw-datavalue-constraint-uniqueness-violation-isknown": "Le proprietate \"$1\" permitte solmente annotationes de valores unic, ''$2'' ja contine un valor assignate. \"$3\" viola le restriction de unicitate.", "smw-datavalue-constraint-violation-non-negative-integer": "Le proprietate \"$1\" ha un restriction de \"numero integre non negative\" e le valor ''$2'' viola iste requisito.", "smw-datavalue-constraint-violation-must-exists": "Le proprietate \"$1\" ha un restriction de <code>must_exists</code> e le valor ''$2'' viola iste requisito.", "smw-datavalue-constraint-violation-single-value": "Le proprietate \"[[Property:$1|$1]]\" ha un restriction de <code>single_value</code> e le valor \"$2\" viola iste requisito.", "smw-constraint-violation-uniqueness": "Un restriction de <code>unique_value_constraint</code> es assignate al proprietate \"[[Property:$1|$1]]\" le qual permitte solmente assignationes de valores unic e le annotation de valor ''$2'' ja habeva essite trovate como annotate in le subjecto \"$3\".", "smw-constraint-violation-uniqueness-isknown": "Un restriction de <code>unique_value_constraint</code> es assignate al proprietate \"[[Property:$1|$1]]\", dunque, solmente annotationes de valores unic es permittite. ''$2'' ja contine un valor annotate con \"$3\", violante le restriction de unicitate pro le subjecto actual.", "smw-constraint-violation-non-negative-integer": "Un restriction de <code>integer_non_negative</code> es assignate al proprietate \"[[Property:$1|$1]]\" e le annotation de valor ''$2'' viola le requisito de restriction.", "smw-constraint-violation-must-exists": "Un restriction de <code>must_exists</code> es assignate al proprietate \"[[Property:$1|$1]]\" e le annotation de valor ''$2'' viola le requisito de restriction.", "smw-constraint-violation-single-value": "Un restriction de <code>single_value</code> es assignate al proprietate \"[[Property:$1|$1]]\" e le annotation de valor \"$2\" viola le requisito de restriction.", "smw-constraint-violation-class-shape-constraint-missing-property": "Un restriction de <code>shape_constraint</code> es assignate al categoria \"[[:$1]]\" con un clave de <code>property</code>, le proprietate obligatori \"$2\" manca.", "smw-constraint-violation-class-shape-constraint-wrong-type": "Un restriction de <code>shape_constraint</code> es assignate al categoria \"[[:$1]]\" con un clave de <code>property_type</code>, le proprietate \"$2\" non corresponde al typo de \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "Un restriction de <code>shape_constraint</code> es assignate al categoria \"[[:$1]]\" con un clave de <code>max_cardinality</code>, le proprietate \"$2\" non corresponde al cardinalitate de \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "Un restriction de <code>shape_constraint</code> es assignate al categoria \"[[:$1]]\" con un clave de <code>min_textlength</code>, le proprietate \"$2\" non corresponde al requisito de longitude de \"$3\".", "smw-constraint-violation-class-mandatory-properties-constraint": "Un restriction <code>mandatory_properties</code> ha essite assignate al categoria \"[[:$1]]\" de sorta que illo require le proprietates obligatori sequente: $2", "smw-constraint-violation-allowed-namespace-no-match": "Un restriction de <code>allowed_namespaces</code> es assignate al proprietate \"[[Property:$1|$1]]\" e \"$2\" viola le requisito de spatio de nomines, solmente le sequente spatios de nomines \"$3\" es permittite.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "Le restriction de <code>allowed_namespaces</code> require un typo de pagina.", "smw-constraint-schema-category-invalid-type": "Le schema \"$1\" annotate es invalide pro un categoria, illo require un typo \"$2\".", "smw-constraint-schema-property-invalid-type": "Le schema \"$1\" annotate es invalide pro un proprietate, illo require un typo \"$2\".", "smw-constraint-error-allows-value-list": "\"$1\" non es in le lista ($2) de [[Property:Allows value|valores permittite]] pro le proprietate \"$3\".", "smw-constraint-error-allows-value-range": "\"$1\" non es in le intervallo \"$2\" specificate per le restriction de [[Property:Allows value|valores permittite]] del proprietate \"$3\".", "smw-property-predefined-boo": "\"$1\" es un [[Special:Types/Boolean|typo]] e proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro representar valores boolean.", "smw-property-predefined-num": "\"$1\" es un [[Special:Types/Number|typo]] e proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro representar valores numeric.", "smw-property-predefined-dat": "\"$1\" es un [[Special:Types/Date|typo]] e proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro representar valores de data.", "smw-property-predefined-uri": "\"$1\" es un [[Special:Types/URL|typo]] e proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro representar valores URI/URL.", "smw-property-predefined-qty": "\"$1\" es un [[Special:Types/Quantity|typo]] e proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro representar valores de quantitate.", "smw-datavalue-time-invalid-offset-zone-usage": "\"$1\" contine un offset/differentia e un identificator de zona; isto non es supportate.", "smw-datavalue-time-invalid-values": "Le valor \"$1\" contine information non interpretabile in forma de \"$2\".", "smw-datavalue-time-invalid-date-components-common": "\"$1\" contine alcun information non interpretabile.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\" contine un lineetta extrinsec o altere characteres que es invalide pro un interpretation de data.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" contine alcun componentes vacue.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" contine plus de tres componentes requirite pro un interpretation de data.", "smw-datavalue-time-invalid-date-components-sequence": "\"$1\" contine un sequentia que non poteva esser interpretate contra un matrice de correspondentia disponibile pro componentes de datas.", "smw-datavalue-time-invalid-ampm": "\"$1\" contine \"$2\" como elemento de hora que es invalide pro un convention de 12 horas.", "smw-datavalue-time-invalid-jd": "Impossibile interpretar le valor de entrata \"$1\" como un numero DJ (die julian) valide con \"$2\" reportate.", "smw-datavalue-time-invalid-prehistoric": "Impossibile interpretar un valor de entrata prehistoric \"$1\". Per exemplo, haber specificate plus que annos o un modello de calendario pote retornar resultatos non expectate in un contexto prehistoric.", "smw-datavalue-time-invalid": "Impossibile interpretar le valor de entrata \"$1\" como componente de data o hora valide con \"$2\" reportate.", "smw-datavalue-external-formatter-uri-missing-placeholder": "Le URI a formatar non contine le marcator de spatio reservate ''$1''.", "smw-datavalue-external-formatter-invalid-uri": " \"$1\" es un URL non valide.", "smw-datavalue-external-identifier-formatter-missing": "Le proprietate non contine un assignamento [[Property:External formatter uri|\"URI externe a formatar\"]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "Le identificator externe \"$1\" expecta un substitution de campos multiple, ma al valor currente \"$2\" manca al minus un parametro de valor pro satisfacer le requisito.", "smw-datavalue-keyword-maximum-length": "Le parola clave excedeva le longitude maxime de $1 character{{PLURAL:$1||es}}.", "smw-property-predefined-eid": "\"$1\" es un [[Special:Types/External identifier|typo]] e proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro representar identificatores externe.", "smw-property-predefined-peid": "\"$1\" es un proprietate predefinite que specifica un identificator externe e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pefu": "\"$1\" es un proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro specificar un ressource externe con un marcator de spatio reservate.", "smw-property-predefined-long-pefu": "Es expectate que le URI contine un marcator de spatio reservate le qual essera adjustate con un valor de [[Special:Types/External identifier|identificator externe]] pro formar un referentia de ressource valide.", "smw-type-eid": "\"$1\" es un variante del typo de datos [[Special:Types/Text|Texto]] pro describer ressources externe (basate sur URI) e require que le proprietates assignate declara un [[Property:External formatter uri|URI de formatator externe]].", "smw-property-predefined-keyw": "\"$1\" es un proprietate predefinite e [[Special:Types/Keyword|typo]] fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] que normalisa un texto e ha un longitude de characteres restringite.", "smw-type-keyw": "\"$1\" es un variante del typo de datos [[Special:Types/Text|Texto]] que ha un longitude de characteres restringite con un representation de contento normalisate.", "smw-datavalue-stripmarker-parse-error": "Le valor fornite \"$1\" contine [https://en.wikipedia.org/wiki/Help:Strip_marker marcatores de \"strip\"] e, pro isto, non pote esser analysate sufficientemente.", "smw-datavalue-parse-error": "Le valor date \"$1\" non esseva comprendite.", "smw-datavalue-propertylist-invalid-property-key": "Le lista de proprietates \"$1\" contineva un clave de proprietate invalide \"$2\".", "smw-datavalue-type-invalid-typeuri": "Le typo \"$1\" non poteva esser transformate in un representation valide de URI.", "smw-datavalue-wikipage-missing-fragment-context": "Le valor de entrata \"$1\" del pagina wiki non pote esser usate sin pagina de contexto.", "smw-datavalue-wikipage-invalid-title": "Le valor de typo de pagina \"$1\" contine characteres invalide o es incomplete e, pro isto, pote causar resultatos non expectate durante un processo de consulta o annotation.", "smw-datavalue-wikipage-property-invalid-title": "Le proprietate \"$1\" (como typo de pagina) con le valor de entrata \"$2\" contine characteres invalide o es incomplete e, pro isto, pote causar resultatos non expectate durante un processo de consulta o annotation.", "smw-datavalue-wikipage-empty": "Le valor de entrata de pagina wiki es vacue (p.ex. <code>[[SomeProperty::]], [[]]</code>) e, pro isto, non pote esser usate como nomine o como parte de un condition de consulta.", "smw-type-ref-rec": "\"$1\" es un typo de [https://www.semantic-mediawiki.org/wiki/Container contentor] que permitte registrar information additional (per exemplo, datos de provenientia) sur un assignation de valor.", "smw-datavalue-reference-invalid-fields-definition": "Le typo de datos [[Special:Types/Reference|Referentia]] expecta un lista de proprietates que deberea esser declarate usante le proprietate [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Ha campos].", "smw-parser-invalid-json-format": "Le analysator de syntaxe JSON ha retornate con un \"$1\".", "smw-property-preferred-label-language-combination-exists": "\"$1\" non pote esser usate como etiquetta preferite perque le lingua \"$2\" es ja assignate al etiquetta \"$3\".", "smw-clipboard-copy-link": "Copiar ligamine al area de transferentia", "smw-property-userdefined-fixedtable": "\"$1\" ha essite configurate como [https://www.semantic-mediawiki.org/wiki/Fixed_properties proprietate fixe] e qualcunque modification de su [https://www.semantic-mediawiki.org/wiki/Type_declaration declaration de typo] necessita executar le script <code>setupStore.php</code> o realisar le carga special [[Special:SemanticMediaWiki|\"Installation e actualisation del base de datos\"]].", "smw-data-lookup": "Obtention de datos…", "smw-data-lookup-with-wait": "Le requesta es in curso de processamento e pote prender un momento.", "smw-no-data-available": "<PERSON>ulle datos disponibile.", "smw-property-req-violation-missing-fields": "Al proprietate \"$1\" manca un declaration [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Ha campos</code>] que es obligatori pro iste typo \"$2\".", "smw-property-req-violation-multiple-fields": "Proprietate \"$1\" contine multiple declarationes [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Ha campos</code>] confligente; solmente un declaration es expectate pro iste typo \"$2\".", "smw-property-req-violation-missing-formatter-uri": "Al proprietate \"$1\" manca detalios de declaration pro le typo annotate per le fallimento de definir le proprietate <code>URI del formatator externe</code>.", "smw-property-req-violation-predefined-type": "\"$1\" es un proprietate predefinite que contine un declaration de typo \"$2\" que es incompatibile con le typo predefinite de iste proprietate.", "smw-property-req-violation-import-type": "Un declaration de typo esseva detegite que es incompatibile con le typo predefinite del vocabulario importate \"$1\". In general, non es necessari declarar un typo perque le informationes es recuperate del definition de importation.", "smw-property-req-violation-change-propagation-locked-error": "Proprietate \"$1\" ha essite alterate e require que le entitates assignate sia evalutate de novo usante un processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation de cambiamentos]. Le pagina del proprietate ha essite blocate usque le actualisation del specification principal ha terminate pro evitar interruptiones intermediari o specificationes contradictori. Le processo pote prender un momento ante que le pagina pote esser disblocate, perque illo depende del grandor e frequentia del [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cauda de actiones].", "smw-property-req-violation-change-propagation-locked-warning": "Le proprietate \"$1\" ha essite alterate e require que le entitates assignate sia reevalutate per medio de un processo de  [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation de cambiamentos]. Le actualisation pote prender un momento, post que illo depende del grandor e frequentia del [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cauda de actiones]. Es suggerite postponer le cambiamentos al proprietate pro evitar interruptiones intermediari o specificationes contradictori.", "smw-property-req-violation-change-propagation-pending": "Il ha actualisationes de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation de cambiamentos] in progresso ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|action|actiones}}] estimate). Es melior non modificar proprietates usque le fin de iste processo, alteremente il pote occurrer interruptiones intermediari o specificationes contradictori.", "smw-property-req-violation-missing-maps-extension": "Semantic MediaWiki non poteva deteger le extension [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] que es un prerequisito, e in consequentia limita le functionalitate de iste proprietate (i.e., incapace de immagazinar o processar datos geographic).", "smw-property-req-violation-type": "Le proprietate contine specificationes de typo concurrente, que pote resultar in annotationes de valor non valide. Pro iste motivo, se attende que un usator assigna solmente un typo appropriate.", "smw-property-req-error-list": "Le proprietate contine le sequente errores o avisos:", "smw-property-req-violation-parent-type": "Le proprietate \"$1\" e le proprietate parente assignate \"$2\" ha differente annotationes de typo.", "smw-property-req-violation-forced-removal-annotated-type": "Le application del [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance hereditage obligatori del typo del genitor] ha essite activate; le typo de annotation pro le proprietate \"$1\" non correspondente al typo de su proprietate genitor \"$2\" e ha essite alterate pro reflecter iste requisito. Es recommendate adjustar le definition del typo in le pagina de maniera que le message de error e le application obligatori sia removite pro iste proprietate.", "smw-change-propagation-protection": "Iste pagina es blocate pro evita rle modification accidental de datos durante un processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation de cambiamentos]. Le actualisation pote prender un momento ante que le pagina pote esser disblocate, perque illo depende del grandor e frequentia del [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cauda de actiones].", "smw-category-change-propagation-locked-error": "Le categoria \"$1\" ha essite alterate. Isto necessita que le entitates includite sia re-evaluate con un processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation de cambiamentos]. Intertanto, le pagina del categoria ha essite blocate usque le fin del actualisation del specification primari, a fin de evitar interruptiones intermediari o specificationes contradictori. Le actualisation pote prender un momento ante que le pagina pote esser disblocate, perque illo depende del grandor e frequentia del [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cauda de actiones].", "smw-category-change-propagation-locked-warning": "Le categoria \"$1\" ha essite alterate. Isto necessita que le entitates includite sia re-evaluate con un processo de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation de cambiamentos]. Le actualisation pote prender un momento perque illo depende del grandor e frequentia del [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cauda de actiones]. Es suggerite postponer cambiamentos al categoria pro evitar interruptiones intermediari o specificationes contradictori.", "smw-category-change-propagation-pending": "Il ha actualisationes de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagation de cambiamentos] in progresso ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|action|actiones}}] estimate). Es melior non modificar categorias usque le fin de iste processo, alteremente il pote occurrer interruptiones intermediari o specificationes contradictori.", "smw-category-invalid-value-assignment": "\"$1\" non es recognoscite como categoria o annotation de valor valide.", "protect-level-smw-pageedit": "Permitter solmente usatores con le permission de modificar paginas (Semantic MediaWiki)", "smw-create-protection": "Le creation del proprietate \"$1\" es restringite al usatores con le derecto (o [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups gruppo de usatores]) \"$2\" appropriate durante que le [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoritate] es activate.", "smw-create-protection-exists": "Le modification del proprietate \"$1\" es restringite al usatores con le derecto (o [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups gruppo de usatores]) \"$2\" appropriate durante que le [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoritate] es activate.", "smw-edit-protection": "Iste pagina es [[Property:Is edit protected|protegite]] pro impedir le modification accidental de datos e pote solmente esser modificate per usatores con le derecto de modification o [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups gruppo de usatores] appropriate (\"$1\").", "smw-edit-protection-disabled": "Le protection contra le modification ha essite disactivate, dunque \"$1\" non pote esser usate pro proteger paginas de entitate contra le modification non autorisate.", "smw-edit-protection-auto-update": "Semantic MediaWiki ha actualisate le stato de protection de accordo con le proprietate \"Es protegite contra modification\".", "smw-edit-protection-enabled": "Protegite contra modification (Semantic MediaWiki)", "smw-patternedit-protection": "Iste pagina es protegite e pote solmente esser modificate per usatores con le [https://www.semantic-mediawiki.org/wiki/Help:Permissions permission] <code>smw-patternedit</code> appropriate.", "smw-property-predefined-edip": "\"$1\" es un proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro indicar si le modification es protegite o non.", "smw-property-predefined-long-edip": "Ben que qualcunque usator es qualificate pro adder iste proprietate a un subjecto, solmente un usator con un permission dedicate pote modificar o revocar le protection a un entitate post que illo ha essite addite.", "smw-query-reference-link-label": "Referentia de consulta", "smw-format-datatable-emptytable": "<PERSON>ulle datos disponibile in tabella", "smw-format-datatable-info": "Se monstra le entratas _START_ a _END_ de _TOTAL_", "smw-format-datatable-infoempty": "Se monstra le entratas 0 a 0 de 0", "smw-format-datatable-infofiltered": "(filtrate de un total de _MAX_ entratas)", "smw-format-datatable-lengthmenu": "Monstrar _MENU_ entratas", "smw-format-datatable-loadingrecords": "Cargamento…", "smw-format-datatable-processing": "Processamento…", "smw-format-datatable-search": "Recerca:", "smw-format-datatable-zerorecords": "Nulle registro correspondente trovate", "smw-format-datatable-first": "Prime", "smw-format-datatable-last": "Ultime", "smw-format-datatable-next": "Se<PERSON>e", "smw-format-datatable-previous": "Precedente", "smw-format-datatable-sortascending": ": activar pro mitter le columna in ordine ascendente", "smw-format-datatable-sortdescending": ": activar pro mitter le columna in ordine descendente", "smw-format-datatable-toolbar-export": "Exportar", "smw-category-invalid-redirect-target": "Le categoria \"$1\" contine un destination de redirection invalide a un spatio de nomines non-categoria.", "smw-parser-function-expensive-execution-limit": "Le function del analysator syntactic ha attingite le limite de executiones costose (vide le parametro de configuration [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "Semantic MediaWiki refresca iste pagina in function de certe operationes necessari post le execution de consultas.", "apihelp-smwinfo-summary": "Modulo API pro obtener information sur statisticas de Semantic MediaWiki e altere meta-information.", "apihelp-ask-summary": "Modulo API pro consultar Semantic MediaWiki usante le linguage \"ask\".", "apihelp-askargs-summary": "Modulo API pro consultar Semantic MediaWiki usante le linguage \"ask\" como lista de conditiones, visualisationes e parametros.", "apihelp-browsebyproperty-summary": "Modulo API pro obtener information sur un proprietate o lista de proprietates.", "apihelp-browsebysubject-summary": "Modulo API pro obtener information sur un subjecto.", "apihelp-smwtask-summary": "Modulo API pro executar cargas concernente Semantic MediaWiki (solmente pro uso interne, non pro uso public).", "apihelp-smwbrowse-summary": "Modulo API pro supportar activitates de navigation pro differente typos de entitate in Semantic MediaWiki.", "apihelp-ask-parameter-api-version": "Formato del sortita:\n;2:Formato compatibile con versiones ancian que usa {} pro le lista de resultatos.\n;3:Formato experimental que usa [] como lista de resultatos.", "apihelp-smwtask-param-task": "<PERSON><PERSON>i le typo de carga", "apihelp-smwtask-param-params": "Parametros codificate in JSON que corresponde al requisitos del typo de carga seligite", "smw-apihelp-smwtask-example-update": "Exemplo de execution de un carga de actualisation pro un subjecto particular:", "smw-api-invalid-parameters": "Parametros invalide, \"$1\"", "smw-parser-recursion-level-exceeded": "Le nivello de recursiones de $1 ha essite excedite durante un processo de analyse syntactic. Es suggerite validar le structura del patrono, o si necessari adjustar le parametro de configuration <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "Presentation de $1 {{PLURAL:$1|pagina|paginas}} que usa iste proprietate.", "smw-property-page-list-search-count": "Presentation de $1 {{PLURAL:$1|pagina|paginas}} que usa iste proprietate con un valor correspondente a \"$2\".", "smw-property-page-filter-note": "Le [https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter filtro de recerca] permitte le inclusion de [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions expressiones de consulta] como <code>~</code> o <code>!</code>. Le [https://www.semantic-mediawiki.org/wiki/Query_engine motor de consultas] seligite poterea tamben esser capace de ignorar le differentia inter majusculas e minusculas o supportar altere expressiones curte como:\n\n* <code>in:</code> le resultato debe continer le termino, p.ex. '<code>in:Foo</code>'\n\n* <code>not:</code> le resultato non debe continer le termino, p.ex. '<code>not:Bar</code>'", "smw-property-reserved-category": "Categoria", "smw-category": "Categoria", "smw-datavalue-uri-invalid-scheme": " \"$1\" non ha essite listate como schema de URI valide.", "smw-datavalue-uri-invalid-authority-path-component": "Ha essite constatate que “$1” contine un componente de autoritate o de percurso “$2” non valide.", "smw-browse-property-group-title": "Gruppo de proprietates", "smw-browse-property-group-label": "Etiquetta de gruppo de proprietates", "smw-browse-property-group-description": "Description de gruppo de proprietates", "smw-property-predefined-ppgr": "\"$1\" es un proprietate predefinite que identifica entitates (principalmente categorias) que es usate como instantia de gruppamento pro proprietates. Es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-filter": "Filtro", "smw-section-expand": "Expander le section", "smw-section-collapse": "Contraher le section", "smw-ask-format-help-link": "Formato [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Folio de trucos", "smw-personal-jobqueue-watchlist": "Observatorio del cauda de actiones", "smw-personal-jobqueue-watchlist-explain": "Le numeros indica un estimation de entratas del cauda de actiones que attende execution.", "smw-property-predefined-label-skey": "Clave de ordinamento", "smw-processing": "Processamento…", "smw-loading": "Cargamento…", "smw-fetching": "Obtention…", "smw-preparing": "Preparation…", "smw-expand": "Expander", "smw-collapse": "<PERSON><PERSON><PERSON>", "smw-copy": "Copiar", "smw-copy-clipboard-title": "Copia le contento al area de transferentia", "smw-jsonview-expand-title": "Expande le vista JSON", "smw-jsonview-collapse-title": "Contrahe le vista JSON", "smw-jsonview-search-label": "Cercar:", "smw-redirect-target-unresolvable": "Le destination es irresolubile pro le ration \"$1\"", "smw-types-title": "Typo: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Non es permittite cambiar le modello de contento de un pagina de [https://www.semantic-mediawiki.org/wiki/Help:Schema schema].", "smw-schema-namespace-edit-protection": "Iste pagina es protegite e pote solmente esser modificate per usatores con le [https://www.semantic-mediawiki.org/wiki/Help:Permissions permission] <code>smw-schemaedit</code> appropriate.", "smw-schema-namespace-edit-protection-by-import-performer": "Iste pagina ha essite importate per un [https://www.semantic-mediawiki.org/wiki/Import_performer executor de importationes] listate, e isto significa que iste pagina pote solmente esser cambiate per iste usatores listate.", "smw-schema-error-title": "Error{{PLURAL:$1||es}} de validation", "smw-schema-error-schema": "Le schema de validation '''$1''' ha trovate le sequente inconsistentias:", "smw-schema-error-miscellaneous": "Error miscellanee ($1)", "smw-schema-error-validation-json-validator-inaccessible": "Le validator de JSON \"<b>$1</b>\" non es accessibile (o installate); pro isto, le file \"$2\" non pote esser examinate, impediente le salveguarda o alteration del pagina actual.", "smw-schema-error-validation-file-inaccessible": "Le file de validation \"$1\" es inaccessibile.", "smw-schema-error-type-missing": "Al contento manca un typo necessari pro render lo recognoscibile e usabile in le [https://www.semantic-mediawiki.org/wiki/Help:Schema spatio de nomines de schema].", "smw-schema-error-type-unknown": "Le typo \"$1\" non es registrate e non pote esser usate pro contento in le spatio de nomines [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-error-json": "Error JSON: \"$1\"", "smw-schema-error-input": "Le validation de entrata ha trovate le sequente problemas, le quales debe esser remediate ante que le contento pote esser salveguardate. Le pagina de [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling adjuta sur le schemas] poterea fornir alcun consilios sur como remover inconsistentias o resolver problemas con le entrata de schemas.", "smw-schema-error-input-schema": "Le schema de validation '''$1''' ha trovate le sequente inconsistentias e illos debe esser remediate ante que le contento pote esser salveguardate. Le pagina de [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling adjuta sur le schemas] pote fornir alcun consilios sur como resolver iste problemas.", "smw-schema-error-title-prefix": "Iste typo de schema require que le titulo del schema comencia con un prefixo \"$1\".", "smw-schema-validation-error": "Le typo \"$1\" non es registrate e non pote esser usate pro contento in le spatio de nomines [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-validation-schema-title": "Schema JSON", "smw-schema-summary-title": "<PERSON><PERSON><PERSON>", "smw-schema-title": "<PERSON><PERSON><PERSON>", "smw-schema-usage": "<PERSON><PERSON>", "smw-schema-type": "<PERSON><PERSON> de schema", "smw-schema-type-description": "Description del typo", "smw-schema-description": "Description del schema", "smw-schema-description-link-format-schema": "Iste typo de schema supporta le definition de characteristicas pro crear ligamines sensibile al contexto in connexion con un proprietate assignate a un [[Property:Formatter schema|schema formatator]].", "smw-schema-description-search-form-schema": "Iste typo de schema supporta le definition de formularios de entrata e characteristicas pro le profilo de [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch recerca extendite] ubi illo contine instructiones sur como generar campos de entrata, definir spatios de nomines predefinite, o declarar expressiones de prefixo pro un requesta de recerca.", "smw-schema-description-property-profile-schema": "Iste typo de schema supporta le definition de un profilo pro declarar characteristicas al proprietate assignate e su valores de annotation.", "smw-schema-description-facetedsearch-profile-schema": "Iste typo de schema supporta le definition de profilos usate como parte del ambiente de [[Special:FacetedSearch|recerca in faciettas]]", "smw-schema-description-property-group-schema": "Iste typo de schema supporta le definition de [https://www.semantic-mediawiki.org/wiki/Help:Property_group gruppos de proprietates] pro adjutar a structurar le interfacie de [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse navigation].", "smw-schema-description-property-constraint-schema": "Isto supporta le definition de regulas de restriction pro un instantia de proprietate assi como le valores assignate a illo.", "smw-schema-description-class-constraint-schema": "Iste typo de schema supporta le definition de regulas de restriction pro un instantia de classe (i.e. un categoria).", "smw-schema-tag": "Etiquetta{{PLURAL:$1||s}}", "smw-property-predefined-constraint-schema": "\"$1\" es un proprietate predefinite que defini un schema de restriction e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-desc": "\"$1\" es un proprietate predefinite que immagazina un description de schema e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-def": "\"$1\" es un proprietate predefinite que immagazina le contento del schema e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-tag": "\"$1\" es un proprietate predefinite fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] pro identificar un collection de schemas.", "smw-property-predefined-long-schema-tag": "Un etiquetta que identifica le schemas de contento o characteristicas similar.", "smw-property-predefined-schema-type": "\"$1\" es un proprietate predefinite que describe un typo pro distinguer un gruppo de schemas e es fornite per [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-schema-type": "Cata [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type typo] forni su proprie interpretation de elementos de syntaxe e regulas de application e pote esser exprimite con le adjuta de un [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation schema de validation].", "smw-ask-title-keyword-type": "Recerca de parolas clave", "smw-ask-message-keyword-type": "Iste recerca corresponde al condition <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Non pote connecter al destination remote \"$1\".", "smw-remote-source-disabled": "Le fonte '''$1''' ha disactivate le supporto de requestas remote!", "smw-remote-source-unmatched-id": "Le fonte '''$1''' non concorda con un version de Semantic MediaWiki que pote supportar un requesta remote.", "smw-remote-request-note": "Le resultato es recuperate del fonte remote '''$1''' e il es probabile que le contento generate continera information que non es disponibile desde le wiki actual.", "smw-remote-request-note-cached": "Le resultato ha essite '''immagazinate in cache''' desde le fonte remote '''$1''' e il es probabile que le contento generate continera information que non es disponibile desde le wiki actual.", "smw-parameter-missing": "Le parametro \"$1\" manca.", "smw-property-tab-usage": "<PERSON><PERSON>", "smw-property-tab-profile-schema": "Schema de profilo", "smw-property-tab-redirects": "Synonymos", "smw-property-tab-subproperties": "Subproprietates", "smw-property-tab-errors": "Assignationes incorrecte", "smw-property-tab-constraint-schema": "Schema de restriction", "smw-property-tab-constraint-schema-title": "Schema de restriction compilate", "smw-property-tab-specification": "… plus", "smw-concept-tab-list": "Lista", "smw-concept-tab-errors": "Errores", "smw-ask-tab-result": "Resultato", "smw-ask-tab-extra": "Extra", "smw-ask-tab-debug": "Debug", "smw-ask-tab-code": "Codice", "smw-install-incomplete-tasks-title": "Cargas de administration incomplete", "smw-install-incomplete-intro": "Il ha $2 carga{{PLURAL:$2||s}} incomplete o [[Special:PendingTaskList|pendente]] a facer pro finir le {{PLURAL:$1|installation|actualisation}} de [https://www.semantic-mediawiki.org Semantic MediaWiki]. Un administrator o usator con sufficiente derectos pote completar lo{{PLURAL:$2||s}}. Isto debe esser facite ante de adder nove datos pro evitar inconsistentias.", "smw-install-incomplete-intro-note": "Iste message disparera post que tote le cargas pertinente ha essite concludite.", "smw-pendingtasks-intro-empty": "Nulle cargas ha essite classificate como pendente, incomplete, o non concludite in connexion con Semantic MediaWiki.", "smw-pendingtasks-intro": "Iste pagina forni information sur le cargas que ha essite classificate como pendente, incomplete, o non concludite in connexion con Semantic MediaWiki.", "smw-pendingtasks-setup-no-tasks-intro": "Le installation (o actualisation) ha terminate. Actualmente il non ha cargas pendente o non concludite.", "smw-pendingtasks-tab-setup": "Installation", "smw-updateentitycollation-incomplete": "Le parametro <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> recentemente alterate require que le script <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> es executate de sorta que le entitates es actualisate e continera le valor correcte del campo de ordination.", "smw-updateentitycountmap-incomplete": "Le campo <code>smw_countmap</code> addite in un edition recente require que le script <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> es executate de sorta que functiones pote acceder al contento de iste campo.", "smw-populatehashfield-incomplete": "Le plenamento del campo <code>smw_hash</code> ha essite omittite durante le installation. Le script <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> debe esser executate.", "smw-install-incomplete-populate-hash-field": "Le plenamento del campo <code>smw_hash</code> ha essite omittite durante le installation. Le script <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> debe esser executate.", "smw-install-incomplete-elasticstore-indexrebuild": "Le <code>ElasticStore</code> ha essite seligite como le mechanismo de [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore immagazinage predefinite], totevia le extension non poteva trovar alcun indication que le script <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> ha essite executate. Per favor, executa le script como instruite.", "smw-elastic-rebuildelasticindex-run-incomplete": "Le <code>ElasticStore</code> ha essite seligite como le mechanismo de [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore immagazinage predefinite], totevia le extension non poteva trovar alcun indication que le script <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> ha essite executate. Per favor, executa le script como instruite.", "smw-pendingtasks-setup-intro": "Le {{PLURAL:$1|installation|actualisation}} de <b>Semantic MediaWiki</b> ha classificate le sequente cargas como [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade incomplete] e un administrator (o usator con derectos sufficiente) debe concluder ille cargas ante que le usatores continua a crear o alterar contento.", "smw-pendingtasks-setup-tasks": "Cargas", "smw-filter-count": "Numero de filtros", "smw-es-replication-check": "Verification del replication (Elasticsearch)", "smw-es-replication-error": "Problema de replication de Elasticsearch", "smw-es-replication-file-ingest-error": "Problema de ingestion de file", "smw-es-replication-maintenance-mode": "Mantenentia de Elasticsearch", "smw-es-replication-error-missing-id": "Le surveliantia de replication ha trovate que le articulo \"$1\" (ID: $2) manca sur le servitor Elasticsearch.", "smw-es-replication-error-divergent-date": "Le surveliantia de replication ha trovate que le <b>data de modification</b> monstra un discrepantia pro le articulo \"$1\" (ID: $2).", "smw-es-replication-error-divergent-date-short": "Le sequente information de data ha essite usate pro comparation:", "smw-es-replication-error-divergent-date-detail": "Data de modification referentiate:\n*Elasticsearch: $1 \n*Base de datos: $2", "smw-es-replication-error-divergent-revision": "Le surveliantia de replication ha trovate que le <b>version associate</b> monstra un discrepantia pro le articulo \"$1\" (ID: $2).", "smw-es-replication-error-divergent-revision-short": "Le sequente datos de version associate ha essite usate pro comparation:", "smw-es-replication-error-divergent-revision-detail": "Version associate referentiate:\n*Elasticsearch: $1 \n*Base de datos: $2", "smw-es-replication-error-maintenance-mode": "Le replication de Elasticsearch es actualmente restringite proque illo opera in un [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>modo de mantenentia</b>], le cambios a entitates e paginas <b>non</b> es immediatemente visibile e le resultatos de consultas pote continer information obsolete.", "smw-es-replication-error-no-connection": "Le surveliantia de replication non pote exequer verificationes perque illo non pote establir un connexion al racemo Elasticsearch.", "smw-es-replication-error-bad-request-exception": "Le gestor de connexiones a Elasticsearch ha levate un exception de requesta incorrecte (\"400 conflict http error\") que indica un problema persistente durante le replication e requestas de recerca.", "smw-es-replication-error-other-exception": "Le gestor de connexiones a Elasticsearch ha levate un exception: \"$1\".", "smw-es-replication-error-suggestions": "Es suggerite modificar o purgar le pagina pro eliminar le discrepantia. Si le problema persiste, alora verifica le racemo de Elasticsearch mesme (allocator, exceptiones, spatio de disco, etc.).", "smw-es-replication-error-suggestions-maintenance-mode": "Es suggerite contactar le administrator del wiki pro verificar si un [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild reconstruction del indice] es actualmente in curso o le parametro <code>refresh_interval</code> non ha essite configurate al valor predefinite expectate.", "smw-es-replication-error-suggestions-no-connection": "<PERSON>s suggerite contactar le administrator del wiki pro signalar le problema \"nulle connexion\".", "smw-es-replication-error-suggestions-exception": "Per favor consulta le registros pro information sur le stato de Elasticsearch, su indices, e possibile problemas de mal configuration.", "smw-es-replication-error-file-ingest-missing-file-attachment": "Le surveliantia de replication ha trovate que a \"$1\" manca un annotation de [[Property:File attachment|annexo de file]], indicante que le processator de ingestion de file non ha comenciate o non ha finite.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Per favor assecura te que le carga de [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion ingestion de files] es programmate e executate ante que le indice de annotationes e files es rendite disponibile.", "smw-report": "Reporto", "smw-legend": "<PERSON>a", "smw-datavalue-constraint-schema-category-invalid-type": "Le schema annotate \"$1\" es invalide pro un categoria; illo require un typo \"$2\".", "smw-datavalue-constraint-schema-property-invalid-type": "Le schema annotate \"$1\" es invalide pro un proprietate; illo require un typo \"$2\".", "smw-entity-examiner-check": "Execution de {{PLURAL:$1|un examinator|examinatores}} in secunde plano", "smw-entity-examiner-indicator": "Pannello de problemas de entitates", "smw-entity-examiner-deferred-check-awaiting-response": "Le examinator \"$1\" attende actualmente un responsa del servitor.", "smw-entity-examiner-deferred-elastic-replication": "Elasticsearch", "smw-entity-examiner-deferred-constraint-error": "Restriction", "smw-entity-examiner-associated-revision-mismatch": "Version", "smw-entity-examiner-deferred-fake": "False", "smw-entity-examiner-indicator-suggestions": "Como parte del examination de entitates, le sequente problema{{PLURAL:$1||s}} ha essite trovate e es suggerite revider {{PLURAL:$1|le problema|los}} con attention e prender le action{{PLURAL:$1||es}} appropriate.", "smw-indicator-constraint-violation": "Restriction{{PLURAL:$1||es}}", "smw-indicator-revision-mismatch": "Version", "smw-indicator-revision-mismatch-error": "Le verification de [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner versiones associate] ha trovate un disaccordo inter le version referentiate in MediaWiki e le version associate in Semantic MediaWiki pro iste entitate.", "smw-indicator-revision-mismatch-comment": "Un discrepantia normalmente indica que alcun processo ha interrumpite le operation de immagazinage in Semantic MediaWiki. Es recommendate revider le registros del servitor e cercar exceptiones o altere fallimentos.", "smw-facetedsearch-intro-text": "Le [https://www.semantic-mediawiki.org/wiki/Faceted_search <b>recerca in faciettas</b>] de Semantic MediaWiki forni al usatores un interfacie simple pro restringer rapidemente le resultatos del consulta a partir de un condition per medio de vistas in faciettas create desde proprietates e categorias dependente.", "smw-facetedsearch-intro-tips": "*Usa <code>category:?</code>, <code>property:?</code> o <code>concept:?</code> pro trovar le categorias, proprietates o conceptos disponibile pro construer un insimul de conditiones\n*Usa le syntaxe #ask pro describer un condition (p.ex. <code><nowiki>[[Category:Foo]]</nowiki></code>)\n* Usa “OR”, “AND”, o altere expressiones de consulta pro crear conditiones complexe\n*Expressiones como <code>in:</code> o <code>phrase:</code> pote esser usate pro trovar phrases complete o pro recercas non structurate, si le [https://www.semantic-mediawiki.org/wiki/Query_engine motor de consultas] seligite supporta ille expressiones", "smw-facetedsearch-profile-label-default": "Profilo predefinite", "smw-facetedsearch-intro-tab-explore": "Explorar", "smw-facetedsearch-intro-tab-search": "Cercar", "smw-facetedsearch-explore-intro": "Selige un collection e comenciar a percurrer.", "smw-facetedsearch-profile-options": "Optiones de profilo", "smw-facetedsearch-size-options": "Optiones de pagination", "smw-facetedsearch-order-options": "Optiones de ordine", "smw-facetedsearch-format-options": "Optiones de presentation", "smw-facetedsearch-format-table": "<PERSON><PERSON>", "smw-facetedsearch-input-filter-placeholder": "Filtrar…", "smw-facetedsearch-no-filters": "<PERSON><PERSON>un filtro.", "smw-facetedsearch-no-filter-range": "Necun gamma de filtros.", "smw-facetedsearch-no-output": "Pro le formato “$1” seligite, necun sortita es disponibile.", "smw-facetedsearch-clear-filters": "Rader filtro{{PLURAL:$1||s}}", "smw-search-placeholder": "<PERSON><PERSON><PERSON>…", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "Infra appare non plus de {{PLURAL:$1|<strong>1</strong> resultato|<strong>$1</strong> resultatos}} a partir del numero <strong>$2</strong>."}