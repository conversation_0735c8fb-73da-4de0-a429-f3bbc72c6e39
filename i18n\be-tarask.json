{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "Nerogaf", "Red Winged Duck", "Rene<PERSON><PERSON><PERSON>", "Wizardist", "Zedlik", "아라"]}, "smw-desc": "Робіць {{GRAMMAR:вінавальны|{{SITENAME}}}} больш зручнай для кампутараў ''і'' людзей ([https://www.semantic-mediawiki.org/wiki/Help:User_manual дакумэнтацыя])", "smw-error": "Памылка", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Сэмантычная МэдыяВікі] была ўсталяваная і задзейнічаная, аднак адсутнічае неабходны [https://www.semantic-mediawiki.org/wiki/Help:Upgrade ключ абнаўленьня].", "smw-upgrade-release": "Выданьне", "smw-upgrade-progress": "Прагрэс", "smw-upgrade-error-why-title": "Чаму я бачу гэтую старонку?", "smw-upgrade-error-how-title": "Як мне выправіць гэтую памылку?", "smw-upgrade-maintenance-title": "Абслугоўваньне » Сэмантычная МэдыяВікі", "smw_viewasrdf": "RDF-крыніца", "smw_finallistconjunct": " і", "smw-factbox-head": "… болей пра «$1»", "smw-factbox-facts": "Факты", "smw-factbox-facts-derived": "Атрыманыя факты", "smw_isspecprop": "Гэтая ўласьцівасьць зьяўляецца спэцыяльнай у {{GRAMMAR:месны|{{SITENAME}}}}.", "smw-concept-cache-header": "Выкарыстаньне кэшу", "smw-concept-no-cache": "Кэш адсутнічае.", "smw_concept_description": "Апісаньне канцэпцыі «$1»", "smw_no_concept_namespace": "Канцэпцыі могуць быць вызначаныя толькі на старонках прасторы назваў «Канцэпцыя:».", "smw_multiple_concepts": "Кожная старонка канцэпцыі можа мець толькі адно вызначэньне канцэпцыі.", "smw_concept_cache_miss": "Канцэпцыя «$1» ня можа быць выкарыстаная ў гэты момант, таму што канфігурацыя {{GRAMMAR:родны|{{SITENAME}}}} патрабуе, каб яна вылічалася аф-лайн. Калі гэта праблема ня зьнікне празь некаторы час, запытайце Вашага адміністратара сайта ўключыць гэтую канцэпцыю.", "smw_noinvannot": "Значэньні ня можа быць прызначаныя ў адваротныя уласьцівасьці.", "version-semantic": "Сэмантычныя пашырэньні", "smw_baduri": "Спасылкі на форму «$1» не дазволеныя.", "smw_printername_count": "Падл<PERSON>к вынікаў", "smw_printername_csv": "экспарт у фармаце CSV", "smw_printername_dsv": "Экспарт у фармат DSV", "smw_printername_debug": "Запыт для наладкі (для экспэртаў)", "smw_printername_embedded": "Устаўляць зьмест старонак", "smw_printername_json": "экспарт у фармаце JSON", "smw_printername_list": "Сьпіс", "smw_printername_plainlist": "Звычайны сьпіс", "smw_printername_ol": "Нумараваны сьпіс", "smw_printername_ul": "Маркіраваны сьпіс", "smw_printername_table": "Табліца", "smw_printername_broadtable": "Шырокая табліца", "smw_printername_template": "Шаблён", "smw_printername_templatefile": "Файл шаблёну", "smw_printername_rdf": "Экспарт у фармат RDF", "smw_printername_category": "Катэгорыя", "validator-type-class-SMWParamSource": "тэкст", "smw-paramdesc-limit": "Максымальная колькасьць вынікаў для вяртаньня", "smw-paramdesc-offset": "Адхіленьне першага выніку", "smw-paramdesc-headers": "Паказваць назвы загалоўкаў/уласьцівасьцяў", "smw-paramdesc-mainlabel": "Метка для назвы галоўнай старонкі", "smw-paramdesc-link": "Паказваць значэньні як спасылкі", "smw-paramdesc-intro": "Тэкст для паказу перад вынікамі запыту, калі яны ёсьць", "smw-paramdesc-outro": "Тэкст для паказу пасьля вынікаў запыту, калі яны ёсьць", "smw-paramdesc-default": "Тэкст для паказу ў выпадку адсутнасьці вынікаў", "smw-paramdesc-sep": "Разьдзяляльнік вынікаў", "smw-paramdesc-propsep": "Межнік паміж уласьцівасьцямі ў выніковым запісе", "smw-paramdesc-distribution": "Замест адлюстраваньня ўсіх значэньняў падлічыць ужываньні і паказаць іх.", "smw-paramdesc-distributionsort": "Адсартаваць разьмеркаваньне значэньняў паводле колькасьці ўжываньня.", "smw-paramdesc-template": "Назва шаблёну, які будзе выкарыстоўвацца для вываду вынікаў", "smw-paramdesc-columns": "Колькасьць слупкоў, у якіх будуць паказвацца вынікі", "smw-paramdesc-userparam": "Значэньне, якое перадаецца ў кожны выклік шаблёну, калі ён выкарыстоўваецца", "smw-paramdesc-class": "Дадатковая кляса CSS, каб задаць сьпіс табліцы", "smw-paramdesc-introtemplate": "Назва шаблёну для паказу перад вынікамі запыту, калі яны ёсьць", "smw-paramdesc-outrotemplate": "Назва шаблёну для паказу пасьля вынікаў запыту, калі яны ёсьць", "smw-paramdesc-embedformat": "Тэг HTML, які выкарыстоўваецца для вызначэньня загалоўкаў", "smw-paramdesc-embedonly": "Не паказваць загалоўкі", "smw-paramdesc-table-class": "Дадатковая кляса CSS для табліцы", "smw-paramdesc-rdfsyntax": "Сынтаксіс RDF для выкарыстаньня", "smw-paramdesc-csv-sep": "Вызначае разьдзяляльнік слупкоў", "smw-paramdesc-csv-valuesep": "Вызначае межнік значэньняў", "smw-paramdesc-dsv-separator": "Разьдзяляльнік", "smw-paramdesc-dsv-filename": "Назва DSV-файла", "smw-paramdesc-filename": "Назва зыходнага файлу", "smw-smwdoc-description": "Паказвае табліцу з усімі парамэтрамі, якія могуць ужывацца для выбранага фармату вынікаў разам з значэньнямі па змоўчваньні і апісаньнямі.", "smw-smwdoc-default-no-parameter-list": "Гэты фармат выніку не забясьпечвае спэцыфічныя парамэтры.", "smw-smwdoc-par-format": "Фармат вынікаў для паказу дакумэнтацыі пра парамэтры.", "smw-paramdesc-sort": "Уласьцівасьць, паводле якой сартаваць запыт", "smw-paramdesc-order": "Парадак сартаваньня запыту", "smw-paramdesc-searchlabel": "Тэкст для працягу пошуку", "smw-paramdesc-named_args": "Назвы аргумэнтаў, перададзеных у шаблён", "smw-paramdesc-export": "Налады імпарту", "smw-paramdesc-json-type": "Тып сэрыялізацыі", "smw-paramdesc-source": "Альтэрнатыўная крыніца запыту", "smw-paramdesc-jsonsyntax": "Сынтаксыс JSON для выкарыстаньня", "smw-printername-feed": "Канал RSS і Atom", "smw-paramdesc-feedtype": "Тып каналу", "smw-paramdesc-feedtitle": "Тэкст, які будзе скарыстаны ў якасьці назвы каналу", "smw-paramdesc-feeddescription": "Тэкст, які будзе скарыстаны ў якасьці апісаньня каналу", "smw-paramdesc-feedpagecontent": "Зьмест старонкі, які будзе адлюстроўвацца на канале", "smw-label-feed-description": "Канал $1 $2", "smw-paramdesc-mimetype": "Тып мэдыя (MIME-тып) для зыходнага файлу", "smw_iq_disabled": "Сэмантычныя запыты былі выключаны ў {{GRAMMAR:месны|{{SITENAME}}}}.", "smw_iq_moreresults": "… наступныя вынікі", "smw_parseerror": "Пададзенае значэньне не было распазнанае.", "smw_notitle": "«$1» ня можа выкарыстоўвацца як назва старонкі ў {{GRAMMAR:месны|{{SITENAME}}}}.", "smw_noproperty": "«$1» ня можа выкарыстоўвацца як уласьцівасьць старонкі ў {{GRAMMAR:месны|{{SITENAME}}}}.", "smw_wrong_namespace": "Тут дазволены толькі старонкі з прасторы назваў «$1».", "smw_manytypes": "Для ўласьцівасьці вызначана болей аднаго тыпу.", "smw_emptystring": "Пустыя радкі не дазволены.", "smw_notinenum": "«$1» не ўваходзіць у сьпіс ($2) [[Property:Allows value|дазволеных значэньняў]] для ўласьцівасьці «$3».", "smw-datavalue-constraint-error-allows-value-list": "«$1» не ўваходзіць у сьпіс ($2) [[Property:Allows value|дазволеных значэньняў]] для ўласьцівасьці «$3».", "smw-datavalue-constraint-error-allows-value-range": "«$1» не ўваходзіць у дыяпазон ($2), вызначаны [[Property:Allows value|дазволенымі значэньнямі]] для ўласьцівасьці «$3».", "smw_noboolean": "«$1» не зьяўляецца лягічным значэньнем (праўда/няпраўда).", "smw_true_words": "праўда,п,так,т", "smw_false_words": "няпраўда,н,не", "smw_nofloat": "«$1» не зьяўляецца лікам.", "smw_infinite": "<PERSON><PERSON><PERSON><PERSON>, большыя за «$1», не падтрымліваюцца.", "smw_unitnotallowed": "«$1» не аб’яўленая як дапушчальная адзінка вымярэньняў для гэтай уласьцівасьці.", "smw_nounitsdeclared": "Няма вызначаных адзінак вымярэньня для гэтай уласьцівасьці.", "smw_novalues": "Значэньні не пазначаныя", "smw_nodatetime": "Дата «$1» не была распазнаная.", "smw_toomanyclosing": "Зашмат уваходжаньняў «$1» у запыце.", "smw_noclosingbrackets": "Выкарыстаныя дужкі «<nowiki>[[</nowiki>» у Вашым запыце не былі зачынены адпаведнымі дужкамі «]]».", "smw_misplacedsymbol": "Сымбаль «$1» быў выкарыстаны ў месцы, дзе ён ня мае сэнсу.", "smw_unexpectedpart": "Частка запыту «$1» не была распазнаная.\nВынікі могуць быць нечаканымі.", "smw_emptysubquery": "Нейкі падзапыт ня мае слушных умоваў.", "smw_misplacedsubquery": "Нейкі падзапыт быў выкарыстаны ў месцы, дзе выкарыстаньне падзапытаў не дазволена.", "smw_valuesubquery": "Падзапыты не падтрымліваюцца для значэньняў уласьцівасьці «$1».", "smw_badqueryatom": "Частка запыту «<nowiki>[[…]]</nowiki>» не была распазнаная.", "smw_propvalueproblem": "Значэньне ўласьцівасьці «$1» не было распазнанае.", "smw_noqueryfeature": "Нейкая ўласьцівасьць не падтрымліваецца ў {{GRAMMAR:месны|{{SITENAME}}}}, і частка запыту была ігнараваная ($1).", "smw_noconjunctions": "Злучэньні ў запытах не падтрымліваюцца ў {{GRAMMAR:месны|{{SITENAME}}}}, і частка запыту была ігнараваная ($1).", "smw_nodisjunctions": "Падзяленьні ў запытах не падтрымліваюцца ў {{GRAMMAR:месны|{{SITENAME}}}}, і частка запыту была ігнараваная ($1).", "smw_querytoolarge": "$2 {{PLURAL:$2|наступная ўмова запыту ня можа ўлічвацца|наступныя ўмовы запыту ня могуць улічвацца|наступных умоваў запыту ня могуць улічвацца}} праз абмежаваньні гэтай вікі на глыбіню і памер запыту: <code>$1</code>.", "smw_notemplategiven": "Для выкананьня гэтага запыту, неабходна ўвесьці значэньне парамэтру «template».", "smw_db_sparqlqueryproblem": "Немагчыма атрымаць вынік запыту да базы зьвестак SPARQL. Магчыма гэтая часовая памылка ці праблема праграмнага забесьпячэньня базы зьвестак.", "smw_db_sparqlqueryincomplete": "Выкананьне запыту было перарванае, з-за таго, што ён зьяўляецца занадта складаным. Некаторыя вынікі могуць адсутнічаць. Калі магчыма, паспрабуйце спрасьціць запыт.", "smw_type_header": "Уласьцівасьці тыпу «$1»", "smw_typearticlecount": "{{PLURAL:$1|Паказаная $1 уласьцівасьць, якая выкарыстоўвае|Паказаныя $1 уласьцівасьці, якія выкарыстоўваюць|Паказаныя $1 уласьцівасьцяў, якія выкарыстоўваюць}} гэты тып.", "smw_attribute_header": "Старонкі, якія выкарыстоўваюць уласьцівасьць «$1»", "smw_attributearticlecount": "{{PLURAL:$1|Паказаная $1 старонка, якая выкарыстоўвае|Паказаныя $1 старонкі, якія выкарыстоўваюць|Паказаныя $1 старонак, якія выкарыстоўваюць}} гэтую ўласьцівасьць.", "smw-propertylist-subproperty-header": "Падуласьцівасьці", "smw-propertylist-redirect-header": "Сынонімы", "smw-propertylist-error-header": "Старонкі зь няправільнымі прызначэньнямі", "smw-propertylist-count": "{{PLURAL:$1|Паказаная|Паказаныя}} $1 {{PLURAL:$1|зьвязаная існасьць|зьвязаныя існасьці|зьвязаных існасьцяў}}.", "smw-propertylist-count-with-restricted-note": "{{PLURAL:$1|Паказаная|Паказаныя}} $1 {{PLURAL:$1|зьвязаная існасьць|зьвязаныя існасьці|зьвязаных існасьцяў}} (іх болей, аднак адлюстраваньне абмежаванае «$2»).", "smw-propertylist-count-more-available": "{{PLURAL:$1|Паказаная|Паказаныя}} $1 {{PLURAL:$1|зьвязаная існасьць|зьвязаныя існасьці|зьвязаных існасьцяў}} (іх болей).", "specialpages-group-smw_group": "Сэмантычная MediaWiki", "exportrdf": "Экспарт старонак у фармаце RDF", "smw_exportrdf_docu": "Гэта старонка дазваляе Вам атрымліваць зьвесткі са старонкі ў фармаце RDF.\nКаб экспартаваць старонкі, увядзіце іх назвы ніжэй у тэкставым полі, па адной назьве на радок.", "smw_exportrdf_recursive": "Рэкурсіўны экспарт усіх зьвязаных старонак.\nЗьвярніце ўвагу на тое, што вынік можа быць вялікім!", "smw_exportrdf_backlinks": "Таксама экспартаваць усе старонкі, якія спасылаюцца на экспартаваныя старонкі.\nСтварае RDF з поўнай навігацыяй.", "smw_exportrdf_lastdate": "Не экспартаваць старонкі, якія не зьмяняліся з пададзенага часу.", "smw_exportrdf_submit": "Экспартаваць", "uriresolver": "Пераўтваральнік URI", "properties": "Уласьцівасьці", "smw-categories": "Катэгорыі", "smw_properties_docu": "Наступныя ўласьцівасьці выкарыстоўваюцца ў {{GRAMMAR:месны|{{SITENAME}}}}.", "smw_property_template": "$1 тыпу $2 ($3 {{PLURAL:$3|выкарыстаньне|выкарыстаньні|выкарыстаньняў}})", "smw_propertylackspage": "Усе ўласьцівасьці павінны мець старонку апісаньня!", "smw_propertylackstype": "Для гэтай уласьцівасьці не пазначаны ніякі тып (пакуль будзе выкарыстоўвацца тып $1).", "smw_propertyhardlyused": "Гэтая ўласьцівасьць наўрад ці выкарыстоўваецца ў {{GRAMMAR:месны|{{SITENAME}}}}!", "smw-sp-property-searchform": "Адлюстраваць уласьцівасьці, якія ўтрымліваюць:", "smw-special-property-searchform": "Выявіць уласьцівасьці, якія ўтрымліваюць:", "smw-special-property-searchform-options": "Налады", "smw-special-wantedproperties-filter-label": "Фільтар:", "smw-special-wantedproperties-filter-none": "Няма", "smw-special-wantedproperties-filter-unapproved": "Незацьверджана", "unusedproperties": "Уласьцівасьці, якія не выкарыстоўваюцца", "smw-unusedproperties-docu": "Гэтая старонка ўтрымлівае [https://www.semantic-mediawiki.org/wiki/Unused_properties неўжываныя ўласьцівасьці], якія былі аб’яўленыя, хаця ніводная старонка іх не выкарыстоўвае. Для дыфэрэнцаванага выгляду, глядзіце [[Special:Properties|агульную спэцыяльную старонку]] або [[Special:WantedProperties|старонку запатрабаваных уласьцівасьцяў]].", "smw-unusedproperty-template": "$1 тыпу $2", "wantedproperties": "Запатрабаваныя ўласьцівасьці", "smw-wantedproperties-docu": "Гэтая старонка ўтрымлівае [https://www.semantic-mediawiki.org/wiki/Wanted_properties запатрабаваныя ўласьцівасьці], якія ўжываюцца ў вікі, але ня маюць старонкі з апісаньнем. Для дыфэрэнцаванага выгляду, глядзіце [[Special:Properties|агульную спэцыяльную старонку]] або [[Special:UnusedProperties|старонку неўжываных уласьцівасьцяў]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|выкарыстаньне|выкарыстаньні|выкарыстаньняў}})", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|выкарыстаньне|выкарыстаньні|выкарыстаньняў}})", "smw_purge": "Абнавіць", "types": "Тыпы", "smw_types_docu": "Сьпіс [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes даступных тыпаў зьвестак], у якім кожны [https://www.semantic-mediawiki.org/wiki/Help:Datatype тып] зьяўляецца наборам атрыбутаў для апісаньня значэньняў з пункту гледжаньня захоўваньня і адлюстраваньня характарыстык, якія паходзяць ад прызначанай уласьцівасьці.", "smw-statistics-property-instance": "{{PLURAL:$1|1=Значэньне|Значэньні|Значэньняў}} уласьцівасьці (агулам)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|1=Значэньне|Значэньні|Значэньняў}}]] (агулам)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|1=Уласьцівасьць|Уласьцівасьці|Уласьцівасьцяў}} (агулам)", "smw-statistics-property-used": "{{PLURAL:$1|1=Уласьцівасьць|Уласьцівасьці}} ({{PLURAL:$1|1=ужытая|ужытыя}} з прынамсі адным значэньнем)", "smw-statistics-property-page": "{{PLURAL:$1|1=Уласьцівасьць|Уласьцівасьці|Уласьцівасьцяў}} ({{PLURAL:$1|1=зарэгістраваная|зарэгістраваныя|зарэгістраваных}} са старонкай)", "smw-statistics-property-type": "{{PLURAL:$1|1=Уласьцівасьць|Уласьцівасьці|Уласьцівасьцяў}} ({{PLURAL:$1|1=прысвоеная|прысвоеныя|прысвоеных}} тыпу зьвестак)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Запыт|Запыты|Запытаў}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|1=Запыт|Запыты}}]] (укладзеныя, усяго)", "smw-statistics-query-size": "Памер запыту", "smw-statistics-error-count": "{{PLURAL:$1|Значэньне ўласьцівасьці|Значэньні ўласьцівасьці|Значэньняў уласьцівасьці}} ([[Special:ProcessingErrorList|{{PLURAL:$1|няправільная анатацыя|няправільныя анатацыі}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Значэньне ўласьцівасьці|Значэньні ўласьцівасьці|Значэньняў уласьцівасьці}} ({{PLURAL:$1|няправільная анатацыя|няправільныя анатацыі}})", "smw_uri_doc": "Пераўтваральнік URI ажыцьцяўляе [$1 пошук W3C TAG у httpRange-14].\nГэта забясьпечвае, што ў залежнасьці ад запыту выдаецца або прадстаўленьне RDF (для машынаў), або вікі-старонка (для людзей).", "ask": "Сэмантычны пошук", "smw_ask_sortby": "Сартаваць па слупку (неабавязкова)", "smw_ask_ascorder": "Па павелічэньні", "smw_ask_descorder": "Па зьмяншэньні", "smw-ask-order-rand": "Выпадковы", "smw_ask_submit": "Шукаць", "smw_ask_editquery": "Рэдагаваць запыт", "smw_add_sortcondition": "[Дадаць умовы сартаваньня]", "smw-ask-sort-add-action": "Дадаць умовы сартаваньня", "smw_ask_hidequery": "Схаваць запыт (кампактны выгляд)", "smw_ask_help": "Дапамога па стварэньні запытаў", "smw_ask_queryhead": "Умова", "smw_ask_printhead": "Друкаваць абранае", "smw_ask_printdesc": "(дадавайце адну назву ўласьцівасьці на радок)", "smw_ask_format_as": "Фарматаваць як:", "smw_ask_defaultformat": "дапомна", "smw_ask_otheroptions": "Іншыя налады", "smw_ask_show_embed": "Паказаць убудаваны код", "smw_ask_hide_embed": "Схаваць убудаваны код", "smw_ask_embed_instr": "Каб убудаваць гэты запыт у старонку, выкарыстоўвайце код пададзены ніжэй.", "smw-ask-delete": "Выдаліць", "smw-ask-sorting": "Сартаваньне", "smw-ask-options": "Налады", "smw-ask-options-sort": "Налады сартаваньня", "smw-ask-format-options": "Фармат і налады", "smw-ask-parameters": "Парамэтры", "smw-ask-search": "По<PERSON><PERSON>к", "smw-ask-debug-desc": "Генэруе інфармацыю пра адладку запыту", "smw-ask-no-cache": "Адлучыць кэш запыту", "smw-ask-result": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-empty": "Прыбраць усе запісы", "smw-ask-download-link-desc": "Запампаваць запыты вынікаў у фармаце $1", "smw-ask-format": "Фармат", "smw-ask-input-assistance": "Дапамога пры ўводзе", "searchbyproperty": "Шукаць па ўласьцівасьці", "processingerrorlist": "Сьпіс памылак апрацоўкі", "propertylabelsimilarity": "Паведамленьне аб падобнасьці назваў уласьцівасьцяў", "smw-processingerrorlist-intro": "Наступны сьпіс забясьпечвае агульны выгляд [https://www.semantic-mediawiki.org/wiki/Processing_errors памылак апрацоўваньня], якія ўзьніклі ў сувязі з [https://www.semantic-mediawiki.org/ Сэмантычнай MediaWiki]. Раіцца рэгулярна назіраць за гэтым сьпісам на звыклай аснове і выпраўляць нядзейсныя анатацыі значэньняў.", "smw_sbv_docu": "Шукаць усе старонкі, якія ўтрымліваюць пададзеную ўласьцівасьць і значэньне.", "smw_sbv_novalue": "Увядзіце значэньне для ўласьцівасьці, ці паглядзіце ўсе значэньні ўласьцівасьцяў для «$1».", "smw_sbv_displayresultfuzzy": "Сьпіс усіх старонак, якія ўтрымліваюць уласьцівасьць «$1» са значэньнем «$2».\nЗ-за таго, што супала толькі некалькі вынікаў, паказаны старонкі, якія маюць блізкія значэньні.", "smw_sbv_property": "Уласьцівасьць:", "smw_sbv_value": "Значэньне:", "smw_sbv_submit": "Знайсьці", "browse": "Прагляд {{GRAMMAR:родны|{{SITENAME}}}}", "smw_browselink": "Праглядзець уласьцівасьці", "smw_browse_article": "Увядзіце назву старонкі для пачатку прагляду.", "smw_browse_go": "Перайсьці", "smw_browse_show_incoming": "Паказаць уласьцівасьці, якія спасылаюцца сюды", "smw_browse_hide_incoming": "Схаваць уласьцівасьці, якія спасылаюцца сюды", "smw_browse_no_outgoing": "Гэта старонка ня мае ўласьцівасьцяў.", "smw_browse_no_incoming": "На гэту старонку не спасылаюцца ніякія ўласьцівасьці.", "smw-browse-from-backend": "У сапраўдны час ідзе атрыманьне інфармацыі з сэрвэрнай прылады.", "smw-browse-intro": "Дадзеная старонка прадастаўляе падрабязнасьці аб тэме або экзэмпляры сутнасьці, калі ласка, увядзіце назву аб'екту, які патрабуе праверкі.", "smw-browse-invalid-subject": "Спраўджаньне суб’екта вярнула памылку «$1».", "smw-browse-api-subject-serialization-invalid": "Суб’ект мае некарэктны фармат сэрыялізацыі.", "smw-browse-show-group": "Адлюстраваць групы", "smw-browse-hide-group": "Схаваць групы", "smw_inverse_label_default": "$1 з", "smw_inverse_label_property": "Паметка адваротнай уласьцівасьці", "pageproperty": "Старонка пошуку ўласьцівасьцяў", "smw_pp_docu": "Увядзіце старонку і ўласьцівасьць, ці толькі ўласьцівасьць, каб атрымаць усе пададзеныя значэньні.", "smw_pp_from": "З старонкі:", "smw_pp_type": "Уласьцівасьць:", "smw_pp_submit": "Шукаць", "smw_result_prev": "Папярэднія", "smw_result_next": "Наступныя", "smw_result_results": "Вынікі", "smw_result_noresults": "Вынікаў няма.", "smwadmin": "Панэль інструмэнтаў сэмантычнай MediaWiki", "smw-admin-statistics-job-title": "Статыстыка заданьняў", "smw-admin-statistics-querycache-title": "Кэш запытаў", "smw-admin-setupsuccess": "Рухавік сховішча наладжаны.", "smw_smwadmin_return": "Вярнуцца да $1", "smw_smwadmin_updatestarted": "Быў распачаты новы працэс абнаўленьня сэмантычных зьвестак.\nУсе зьвесткі будуць пераўтвораны ці зьменены, калі гэта будзе неабходна.\nВы можаце назіраць за працэсам абнаўленьня на гэтай спэцыяльнай старонцы.", "smw_smwadmin_updatenotstarted": "Працэс абнаўленьня ўжо распачаты.\nНовага не ствараем.", "smw_smwadmin_updatestopped": "Усе працэсы абнаўленьня былі спыненыя.", "smw_smwadmin_updatenotstopped": "Каб спыніць працэс абнаўленьня, пастаўце ў полі адзнаку дзеля пацьверджаньня вашых намераў.", "smw-admin-docu": "Гэтая спэцыяльная старонка дапамагае вам падчас усталяваньня, абнаўленьня, абслугоўваньня і выкарыстаньня <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a>, а таксама забясьпечвае далейшыя адміністрацыйныя функцыі і задачы разам з статыстыкай.\nНе забывайце захоўваць каштоўныя зьвесткі перад выкананьнем адміністратарскіх функцыяў.", "smw-admin-environment": "Абалонка праграмнага забесьпячэньня", "smw-admin-db": "Наладка базы зьвестак", "smw-admin-dbdocu": "Semantic MediaWiki патрабуе ўласную структуру базы зьвестак (яна залежыць ад MediaWiki і не ўплывае на астатнюю частку ўсталяваньня installation) для захоўваньня сэмантычных зьвестак.\nГэта ўсталёвачная функцыя можа быць выкананая некалькі разоў без аніякай шкоды, але для ўсталяваньня ці абнаўленьня дастаткова будзе аднаго разу.", "smw-admin-permissionswarn": "Калі адбываюцца SQL-памылкі падчас выкананьня апэрацыі, гэта значыць, што рахунак базы зьвестак, які выкарыстоўвае вікі (праверце ваш файл «LocalSettings.php»), магчыма, ня мае адпаведных правоў.\nНадайце рахунку дадатковыя правы на стварэньне і выдаленьне табліцаў, часова ўвядзіце рахунак адміністратара базы зьвестак у файл «LocalSettings.php», ці карыстайцеся скрыптом падтрымкі <code>setupStore.php</code>, які можа выкарыстоўваць рахунак адміністратара.", "smw-admin-dbbutton": "Усталяваць ці абнавіць табліцы", "smw-admin-announce": "Анансуйце вашую вікі", "smw-admin-deprecation-notice-title": "Паведамленьні аб старэньні", "smw-admin-deprecation-notice-title-notice": "Састарэлыя налады", "smw-admin-deprecation-notice-title-replacement": "Замененыя ці перайменаваныя налады", "smw-admin-deprecation-notice-title-removal": "Прыбраныя налады", "smw-smwadmin-refresh-title": "Выпраўленьне й абнаўленьне зьвестак", "smw_smwadmin_datarefresh": "Перабудова зьвестак", "smw_smwadmin_datarefreshdocu": "Існуе магчымасьць аднаўленьня ўсіх зьвестак Semantic MediaWiki на аснове цяперашняга зьместу {{GRAMMAR:родны|{{SITENAME}}}}.\nГэта можа быць карысным для аднаўленьня парушаных зьвестак ці абнаўленьня зьвестак, калі ўнутраны фармат зьмяняецца пры абнаўленьні праграмнага забесьпячэньня.\nАбнаўленьне будзе выконвацца старонка за старонкай і можа заняць пэўны час.\nНіжэй паказваецца інфармацыя, ці адбываецца абнаўленьне, якая дазваляе Вам пачынаць ці спыняць абнаўленьне (за выключэньнем выпадкаў, калі гэта магчымасьць забароненая адміністратарам сайта).", "smw_smwadmin_datarefreshprogress": "<strong>Ідзе працэс абнаўленьня.</strong>\nГэта нармальна, што працэс абнаўленьня запавольваецца, і зьвесткі абнаўляюцца невялікімі кавалкамі.\nКаб скончыць абнаўленьне хутчэй, Вы можаце скарыстацца скрыптом падтрымкі МэдыяВікі <code>runJobs.php</code> (выкарыстоўвайце парамэтар <code>--maxjobs 1000</code>, каб абмежаваць колькасьць выкананых абнаўленьняў у адным пакеце).\nПрыкладны час сканчэньня цяперашняга абнаўленьня:", "smw_smwadmin_datarefreshbutton": "Заплянаваць перабудову зьвестак", "smw_smwadmin_datarefreshstop": "Спыніць гэта абнаўленьне", "smw_smwadmin_datarefreshstopconfirm": "Так, я {{GENDER:$1|ўпэўнены|ўпэўненая}}.", "smw-admin-outdateddisposal-title": "Ліквідацыя састарэлых сутнасьцяў", "smw-admin-outdateddisposal-active": "Прызначана праца па ліквідацыі састарэлых аб'ектаў.", "smw-admin-outdateddisposal-button": "Заплянаваць ліквідацыю", "smw-admin-propertystatistics-title": "Перабудова статыстыкі ўласьцівасьці", "smw-admin-propertystatistics-button": "Заплянаваць перабудову статыстыкі", "smw-admin-fulltext-title": "Перабудова паўнатэкставага пошуку", "smw-admin-fulltext-button": "Запланаваць паўнатэкставую перабудову", "smw-admin-support": "Падтрымка", "smw-admin-supportdocu": "Ёсьць розныя крыніцы, каб дапамагчы вам у выпадку праблемаў:", "smw-admin-installfile": "Калі ўзьнікнуць праблемы з вашым усталяваньнем, пачніце з рэкамэндацыяў у <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">файле INSTALL</a> і <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">на старонцы ўсталёўкі</a>.", "smw-admin-smwhomepage": "Поўная дакумэнтацыя Semantic MediaWiki знаходзіцца на <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Можна паведамляць пра памылкі ў <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">сыстэму сачэньня за праблемамі</a>, старонка <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">паведамленьняў пра памылкі</a> мае даведку пра тое, як напісаць эфэктыўную справаздачу пра праблему.", "smw-admin-questions": "Калі вы маеце пытаньні ці прапановы, далучайцеся да дыскусіі ў <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">паштовай рассылцы</a> Semantic MediaWiki.", "smw-admin-other-functions": "Іншыя функцыі", "smw-admin-supplementary-section-title": "Дапаможныя функцыі", "smw-admin-supplementary-section-subtitle": "Падтрыманыя функцыі ядра", "smw-admin-supplementary-settings-title": "Канфігурацыя і налады", "smw-admin-supplementary-settings-intro": "<u>$1</u> паказвае парамэтры, якія вызначаюць паводзіны Сэмантычнай МэдыяВікі", "smw-admin-supplementary-operational-statistics-title": "Апэрацыйная статыстыка", "smw-admin-supplementary-operational-statistics-intro": "Паказвае пашыраны набор <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Пошук і вылучэньне сутнасьці", "smw-admin-supplementary-idlookup-intro": "Падтрымлівае простую функцыю <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Дубляваны прагляд элемэнтаў", "smw-admin-supplementary-duplookup-intro": "<u>$1</span>, каб знайсьці элемэнты, якія класыфікуюцца як дублікаты ў абранай матрыцы табліцаў", "smw-admin-supplementary-operational-statistics-cache-title": "Статыстыка кэшу", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> паказвае абраны набор статыстыкі кэшу", "smw-admin-supplementary-elastic-intro": "<u>$1</u> паказвае падрабязнасьці пра налады і індэксную статыстыку", "smw-admin-supplementary-elastic-functions": "Падтрымваныя функцыі", "smw-admin-supplementary-elastic-settings-title": "Налады (індэксы)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> выкарыстоўваецца ''Elasticsearch'' для кіраваньня індэксамі ''Сэмантычнай МэдыяВікі''", "smw-admin-supplementary-elastic-mappings-title": "Мапаваньні", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u>, каб адлюстраваць індэксы і месцазнаходжаньні палёў", "smw-admin-supplementary-elastic-mappings-summary": "Зьмест", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> паказвае статыстыку вузла", "smw-admin-supplementary-elastic-indices-title": "Індэксы", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> забясьпечвае агляд даступных індэксаў і іх статыстыку", "smw-admin-supplementary-elastic-statistics-title": "Статыстыка", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> паказвае статыстыку ўзроўню індэксу", "smw-admin-supplementary-elastic-status-replication": "Стан рэплікацыі", "smw-admin-supplementary-elastic-status-last-active-replication": "Апошняя актыўная рэплікацыя: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Прамежак абнаўленьня: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Затрымка ўзнаўленьня працы: $1 (ацэнка)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Назапашаны пратакол (файл) нявыкананай працы: $1 (ацэнка)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Рэплікацыя заблякавана: $1 (перабудова ў працэсе)", "smw-list-count": "Сьпіс утрымлівае $1 {{PLURAL:$1|уваход|уваходы}}.", "smw-property-label-similarity-title": "Справаздача аб падобнасьці назваў уласьцівасьцяў", "smw-property-label-similarity-intro": "<u>$1</u> вылічвае падобнасьці для існых назваў уласьцівасьцяў", "smw-property-label-similarity-threshold": "Парог:", "smw-property-label-similarity-type": "Адлюстраваць тып ідэнтыфікатару", "smw-property-label-similarity-noresult": "Для абраных парамэтраў ня знойдзена вынікаў.", "smw_adminlinks_datastructure": "Структура зьвестак", "smw_adminlinks_displayingdata": "Паказ зьвестак", "smw_adminlinks_inlinequerieshelp": "Дапамога па ўбудаваных запытах", "smw-property-indicator-type-info": "Уласьцівасьць, вызначаная {{PLURAL:$1|ўдзельнікам|сыстэмай}}", "smw-property-indicator-last-count-update": "Прыкладная колькасьць выкарыстоўваньняў\nАпошні раз абноўлена: $1", "smw-concept-indicator-cache-update": "Лічыльнік кішэню\nАпошні раз абноўлены: $1", "smw-createproperty-isproperty": "Гэта ўласьцівасьць тыпу $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|1=Дазволенае значэньне|Дазволеныя значэньні}} для гэтай уласьцівасьці:", "smw-paramdesc-category-delim": "Межнік", "smw-paramdesc-category-template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, з дапамогай якога фарматуюцца элэмэнты", "smw-paramdesc-category-userparam": "Парамэтар для перадачы шаблёну", "smw-info-par-message": "Паведамленьне для адлюстраваньня.", "smw-info-par-icon": "Значак для адлюстраваньня «info» або «warning».", "prefs-smw": "Сэмантычная MediaWiki", "prefs-general-options": "Агульныя налады", "prefs-ask-options": "Сэмантычны пошук", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] і зьвязаныя пашырэньні дазваляюць пэрсанальную наладку для групы абраных функцыяў. Сьпіс індывідуальных наладаў з апісаньнямі і характарыстыкамі даступны на гэтай [https://www.semantic-mediawiki.org/wiki/Help:User_preferences старонцы дапамогі].", "smw-prefs-ask-options-tooltip-display": "Адлюстроўваць тэкст парамэтру у выглядзе інфармацыйнай падказкі на адмысловай старонцы [[Special:Ask|канструктара запытаў]].", "smw-prefs-ask-options-compact-view-basic": "Дазволіць асноўны кампактны выгляд", "smw-prefs-help-ask-options-compact-view-basic": "Калі ўключана, адлюстроўвае скарочаны набор спасылак на Special:Ask у сьціснутым фармаце", "smw-prefs-general-options-jobqueue-watchlist": "Адлюстраваць сьпіс назіраньняў за чэргай задачаў на асабістым панэлю", "smw-prefs-general-options-disable-editpage-info": "Адлучыць уступны тэкст на старонцы рэдагаваньня", "smw-prefs-general-options-disable-search-info": "Адлучыць інфармацыю аб падтрымцы сынтаксысу на змоўчнай старонцы пошуку", "smw-prefs-general-options-suggester-textinput": "Улучыць дапамогу для ўводу сэмантычных аб’ектаў", "smw-ui-tooltip-title-property": "Уласьцівасьць", "smw-ui-tooltip-title-quantity": "Канвэрсія адзінкі", "smw-ui-tooltip-title-info": "Інфармацыя", "smw-ui-tooltip-title-service": "Службовыя спасылкі", "smw-ui-tooltip-title-warning": "Папярэджаньне", "smw-ui-tooltip-title-error": "Памылка", "smw-ui-tooltip-title-parameter": "Парам<PERSON>тар", "smw-ui-tooltip-title-event": "Падзея", "smw-ui-tooltip-title-note": "Камэнтар", "smw-ui-tooltip-title-legend": "Легенда", "smw-ui-tooltip-title-reference": "Зноска", "smw_unknowntype": "Тып «$1» гэтай уласьцівасьці зьяўляецца няслушным", "smw-concept-cache-text": "Задумка мае $1 {{PLURAL:$1|старонку|старонкі|старонак}}, і была апошні раз абноўлена $3, $2.", "smw_concept_header": "Старонкі канцэпцыі «$1»", "smw_conceptarticlecount": "Ніжэй {{PLURAL:$1|паказаная $1 старонка|паказаныя $1 старонкі|паказаныя $1 старонак}}.", "smw-qp-empty-data": "Запытаныя зьвесткі ня могуць быць адлюстраваныя ў сувязі зь недастатковасьцю крытэраў адбору.", "right-smw-admin": "Доступ да адміністрацыйных задачаў (Сэмантычная MediaWiki)", "restriction-level-smw-pageedit": "абарон<PERSON>на (толькі адпаведныя ўдзельнікі)", "action-smw-patternedit": "рэдагаваць звычайныя выразы, якія выкарыстоўваюцца Сэмантычнай МэдыяВікі", "smw-property-namespace-disabled": "Уласьцівасьць «[https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks прастора назваў]» адключаная, таму спроба прызначыць тып ці іншыя асаблівыя характарыстыкі для гэтай уласьцівасьці немагчымая.", "smw-sp-properties-header-label": "Сьпіс уласьцівасьцяў", "smw-admin-idlookup-title": "Шукаць паводле", "smw-admin-iddispose-title": "Пазбаўленьне", "smw-admin-idlookup-input": "Пошук:", "smw-admin-objectid": "Ідэнтыфікатар:", "smw-admin-tab-general": "Агляд", "smw-admin-tab-notices": "Заўвагі аб састарэньні", "smw-admin-tab-supplement": "Дадатковыя функцыі", "smw-admin-tab-registry": "Рэестар", "smw-admin-maintenancealerts-invalidentities-alert-title": "Хібныя існасьці", "smw-livepreview-loading": "Загрузка…", "smw-sp-searchbyproperty-resultlist-header": "Сьпіс вынікаў", "smw-search-syntax": "Сынтаксыс", "smw-search-profile": "Пашырана", "smw-search-profile-sort-recent": "Самыя нядаўнія", "smw-search-profile-sort-title": "Назва", "smw-search-profile-extended-help-sort-title": "* «Назва» з выкарыстаньнем назвы старонкі (ці назвы, абранай для паказу) як крытэр сартаваньня", "smw-search-profile-extended-help-find-forms": "даступныя формы", "smw-search-profile-extended-section-sort": "Сартаваць паводле", "smw-search-profile-extended-section-form": "Формы", "smw-search-profile-extended-section-search-syntax": "Пошук увахода", "smw-search-profile-extended-section-namespace": "Прастора назваў", "smw-search-profile-extended-section-query": "Запыт", "smw-search-profile-link-caption-query": "будаўнік запытаў", "smw-search-show": "Паказаць", "smw-search-hide": "Схаваць", "log-name-smw": "<PERSON><PERSON><PERSON><PERSON><PERSON> Сэмантычнай MediaWiki", "log-show-hide-smw": "$1 летапіс Сэмантычнай МэдыяВікі", "logeventslist-smw-log": "Летапіс Сэмантычнай Мэдыя Вікі", "logentry-smw-maintenance": "Падзеі, зьвязаныя з падтрымкай, якія адбыліся ў Сэмантычнай МэдыяВікі", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|імпарт $1]]", "smw-datavalue-restricted-use": "Значэньне зьвестак «$1» было пазначана для абмежаванага карыстаньня.", "smw-datavalue-invalid-number": "«$1» ня можа быць інтэрпрэтавана як лічба.", "smw-query-condition-circular": "Магчымая цыклічная ўмова была знойдзена ў «$1».", "smw-query-condition-empty": "У апісаньні запыту існуе пустая ўмова.", "smw-types-list": "Сьпіс тыпаў зьвестак", "smw-types-default": "«$1» зьяўляецца ўбудаваным тыпам зьвестак.", "smw-type-boo": "«$1» зьяўляецца звычайным тыпам зьвестак для апісаньня значэньня праўда/хлусьня.", "smw-type-ema": "«$1» зьяўляецца тыпам зьвестак для прадастаўленьня адрасу элэктроннай пошты.", "smw-type-tab-properties": "Уласьцівасьці", "smw-type-tab-types": "Тыпы", "smw-type-tab-errors": "Памылкі", "smw-type-primitive": "Асноўная", "smw-type-contextual": "Залежныя", "smw-limitreport-intext-parsertime": "[SMW] Час разбору ўнутрытэкставай анатацыі", "smw-limitreport-intext-postproctime": "[SMW] час пост-апрацоўкі", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|сэкунда|сэкундаў}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|сэкунда|сэкундаў}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|сэкунда|сэкунды|сэкундаў}}", "smw-constraint-violation-class-mandatory-properties-constraint": "Абмежаваньне <code>mandatory_properties</code> зьвязанае з катэгорыяй «[[:$1]]» і патрабуе такіх абавязковых уласьцівасьцяў: $2", "smw-datavalue-time-invalid-date-components-empty": "«$1» зьмяшчае некаторыя пустыя кампанэнты.", "smw-datavalue-external-formatter-invalid-uri": "«$1» зьяўляецца няправільным URL-адрасам.", "smw-datavalue-parse-error": "Дадзенае значэньне «$1» не было распазнанае.", "smw-no-data-available": "Няма даступных зьвестак.", "smw-es-replication-error-no-connection": "Сачэньне за паўтарэньнем ня можа выканаць праверкі, бо ня можа ўсталяваць сувязь з клястэрам Elasticsearch.", "smw-listingcontinuesabbrev": " (працяг)", "smw-showingresults": "Ніжэй {{PLURAL:$1|пададзены|пададзеныя}} да <strong>$1</strong> {{PLURAL:$1|выніку|вынікаў}}, пачынаючы з #<strong>$2</strong>."}