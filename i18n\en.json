{"@metadata": {"authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, "smw-desc": "Making your wiki more accessible - for machines ''and'' humans ([https://www.semantic-mediawiki.org/wiki/Help:User_manual online documentation])", "smw-title": "Semantic MediaWiki", "smw-error": "Error", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] was installed and enabled but is missing an appropriate [https://www.semantic-mediawiki.org/wiki/Help:Upgrade upgrade key].", "smw-upgrade-release": "Release", "smw-upgrade-progress": "Progress", "smw-upgrade-progress-explain": "An estimation as to when the upgrade is going to be finished is difficult to predict as it depends on the size of the data repository and the available hardware and can take a moment for larger wikis to complete.\n\nPlease contact your local administrator to get more information about the progress.", "smw-upgrade-progress-create-tables": "Creating (or updating) tables and indices...", "smw-upgrade-progress-post-creation": "Running post creation tasks...", "smw-upgrade-progress-table-optimization": "Running table optimizations...", "smw-upgrade-progress-supplement-jobs": "Adding supplement jobs...", "smw-upgrade-error-title": "Error » Semantic MediaWiki", "smw-upgrade-error-why-title": "Why do I see this page?", "smw-upgrade-error-why-explain": "Semantic MediaWiki's internal database structure has changed and requires some adjustments to be fully functional. There can be several reasons including:\n* Additional fixed properties (requires additional table setup) were added\n* An upgrade contains some changes to tables or indices making an interception obligatory before accessing the data\n* Changes to the storage or query engine", "smw-upgrade-error-how-title": "How do I fix this error?", "smw-upgrade-error-how-explain-admin": "An administrator (or any person with administrator rights) has to run either MediaWiki's [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] or Semantic MediaWiki's [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] maintenance script.", "smw-upgrade-error-how-explain-links": "You may also consult the following pages for further assistance:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation Installation] instructions\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting Troubleshooting] help page", "smw-extensionload-error-why-title": "Why do I see this page?", "smw-extensionload-error-why-explain": "The extension was <b>not</b> loaded using <code>enableSemantics</code> and instead enabled by other means such as using <code>wfLoadExtension( 'SemanticMediaWiki' )</code> directly.", "smw-extensionload-error-how-title": "How do I fix this error?", "smw-extensionload-error-how-explain": "To enable the extension and avoid issues with namespace declarations and pending configurations it is necessary to use <code>enableSemantics</code> which will ensure required variables are set before loading the extension via the <code>ExtensionRegistry</code>.\n\nPlease have a look at the [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] help page for further assistance.", "smw-upgrade-maintenance-title": "Maintenance » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Why do I see this page?", "smw-upgrade-maintenance-note": "The system is currently undergoing an [https://www.semantic-mediawiki.org/wiki/Help:Upgrade upgrade] of the [https://www.semantic-mediawiki.org/ Semantic MediaWiki] extension together with its data repository and we would like to ask you for your patience and allow the maintenance to continue before the wiki can be made accessible again.", "smw-upgrade-maintenance-explain": "The extension tries to minimize the impact and downtime by deferring most of its maintenance tasks to after the <code>update.php</code> but some database related changes are required to finish first to avoid data inconsistencies. It can include:\n* Changing table structures such as adding new or modify existing fields\n* Changing or adding table indices\n* Running table optimizations (when enabled)", "smw-semantics-not-enabled": "Semantic MediaWiki functionality was not enabled for this wiki.", "smw_viewasrdf": "RDF feed", "smw_finallistconjunct": ", and", "smw-factbox-head": "... more about \"$1\"", "smw-factbox-facts": "Facts", "smw-factbox-facts-help": "Shows statements and facts that have been created by a user", "smw-factbox-attachments": "Attachments", "smw-factbox-attachments-value-unknown": "N/A", "smw-factbox-attachments-is-local": "Is local", "smw-factbox-attachments-help": "Shows available attachments", "smw-factbox-facts-derived": "Derived facts", "smw-factbox-facts-derived-help": "Shows facts that have been derived from rules or with the help of other reasoning techniques", "smw_isspecprop": "This property is a special property in this wiki.", "smw-concept-cache-header": "Cache usage", "smw-concept-cache-count": "The [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count concept cache] contains {{PLURAL:$1|'''one''' entity|'''$1''' entities}} ($2).", "smw-concept-no-cache": "No cache available.", "smw_concept_description": "Description of concept \"$1\"", "smw_no_concept_namespace": "Concepts can only be defined on pages in the Concept: namespace.", "smw_multiple_concepts": "Each concept page can have only one concept definition.", "smw_concept_cache_miss": "The concept \"$1\" can not be used at the moment, since the wiki configuration requires it to be computed off-line.\nIf the problem does not go away after some time, ask your site administrator to make this concept available.", "smw_noinvannot": "Values cannot be assigned to inverse properties.", "version-semantic": "Semantic extensions", "smw_uri_blacklist": "https://www.w3.org/1999/02/22-rdf-syntax-ns#\n https://www.w3.org/2000/01/rdf-schema#\n https://www.w3.org/2002/07/owl#", "smw_baduri": "URIs of the form \"$1\" are not allowed.", "smw_csv_link": "CSV", "smw_dsv_link": "DSV", "smw_json_link": "JSON", "smw_rdf_link": "RDF", "smw_printername_count": "Count results", "smw_printername_csv": "CSV export", "smw_printername_dsv": "DSV export", "smw_printername_debug": "Debug query (for experts)", "smw_printername_embedded": "Embed page contents", "smw_printername_json": "JSON export", "smw_printername_list": "List", "smw_printername_plainlist": "Plain list", "smw_printername_ol": "Numbered list", "smw_printername_ul": "Bulleted list", "smw_printername_table": "Table", "smw_printername_broadtable": "Broad table", "smw_printername_template": "Template", "smw_printername_templatefile": "Template file", "smw_printername_rdf": "RDF export", "smw_printername_category": "Category", "validator-type-class-SMWParamSource": "text", "smw-paramdesc-limit": "The maximum number of results to return", "smw-paramdesc-offset": "The offset of the first result", "smw-paramdesc-headers": "Display the headers/property names", "smw-paramdesc-mainlabel": "The label to give to the main page name", "smw-paramdesc-link": "Show values as links", "smw-paramdesc-intro": "The text to display before the query results, if there are any", "smw-paramdesc-outro": "The text to display after the query results, if there are any", "smw-paramdesc-default": "The text to display if there are no query results", "smw-paramdesc-sep": "The separator between results", "smw-paramdesc-propsep": "The separator between the properties of a result entry", "smw-paramdesc-valuesep": "The separator between the values for a property of a result", "smw-paramdesc-showsep": "Show separator in top of CSV file (\"sep=<value>\")", "smw-paramdesc-distribution": "Instead of displaying all values, count their occurrences, and show these.", "smw-paramdesc-distributionsort": "Sort the value distribution by occurrence count.", "smw-paramdesc-distributionlimit": "Limit the value distribution to the count of only some values.", "smw-paramdesc-aggregation": "Specify to what the aggregation should relate to", "smw-paramdesc-template": "The name of a template with which to display the printouts", "smw-paramdesc-columns": "The number of columns in which to display results", "smw-paramdesc-userparam": "A value passed into each template call, if a template is used", "smw-paramdesc-class": "An additional CSS class to set for the list", "smw-paramdesc-introtemplate": "The name of a template to display before the query results, if there are any", "smw-paramdesc-outrotemplate": "The name of a template to display after the query results, if there are any", "smw-paramdesc-embedformat": "The HTML tag used to define headings", "smw-paramdesc-embedonly": "Display no headings", "smw-paramdesc-table-class": "An additional CSS class to set for the table", "smw-paramdesc-table-transpose": "Display table headers vertically and results horizontally", "smw-paramdesc-prefix": "Control display of namespace in printouts", "smw-paramdesc-rdfsyntax": "The RDF syntax to be used", "smw-paramdesc-csv-sep": "Specifies a column separator", "smw-paramdesc-csv-valuesep": "Specifies a value separator", "smw-paramdesc-csv-merge": "Merge rows and column values with an identical subject identifier (aka first column)", "smw-paramdesc-csv-bom": "Add a BOM (character to signal endianness) at the top of the output file", "smw-paramdesc-dsv-separator": "The separator to use", "smw-paramdesc-dsv-filename": "The name for the DSV file", "smw-paramdesc-filename": "The name for the output file", "smw-smwdoc-description": "Shows a table of all parameters that can be used for the specified result format together with default values and descriptions.", "smw-smwdoc-default-no-parameter-list": "This result format does not provide format specific parameters.", "smw-smwdoc-par-format": "The result format to display parameter documentation for.", "smw-smwdoc-par-parameters": "Which parameters to show. \"specific\" for those added by the format, \"base\" for those available in all formats, and \"all\" for both.", "smw-paramdesc-sort": "Property to sort the query by", "smw-paramdesc-order": "Order of the query sort", "smw-paramdesc-searchlabel": "Text for continuing the search", "smw-paramdesc-named_args": "Name the arguments passed to the template", "smw-paramdesc-template-arguments": "Sets how the named arguments are passed to the template", "smw-paramdesc-import-annotation": "Additional annotated data are to be copied during the parsing of a subject", "smw-paramdesc-export": "Export option", "smw-paramdesc-prettyprint": "A pretty-print output that displays additional indents and newlines", "smw-paramdesc-json-unescape": "Output to contain unescaped slashes and multibyte Unicode characters", "smw-paramdesc-json-type": "Serialization type", "smw-paramdesc-source": "Alternative query source", "smw-paramdesc-jsonsyntax": "JSON syntax to be used", "smw-printername-feed": "RSS and Atom feed", "smw-paramdesc-feedtype": "Feed type", "smw-paramdesc-feedtitle": "The text to be used as the title of the feed", "smw-paramdesc-feeddescription": "The text to be used as the description of the feed", "smw-paramdesc-feedpagecontent": "Page content to be displayed with the feed", "smw-label-feed-link": "RSS", "smw-label-feed-description": "$1 $2 feed", "smw-paramdesc-mimetype": "The media type (MIME type) for the output file", "smw_iq_disabled": "Semantic queries have been disabled for this wiki.", "smw_iq_moreresults": "... further results", "smw_parseerror": "The given value was not understood.", "smw_decseparator": ".", "smw_kiloseparator": ",", "smw_notitle": "\"$1\" cannot be used as a page name in this wiki.", "smw_noproperty": "\"$1\" cannot be used as a property name in this wiki.", "smw_wrong_namespace": "Only pages in namespace \"$1\" are allowed here.", "smw_manytypes": "More than one type defined for property.", "smw_emptystring": "Empty strings are not accepted.", "smw_notinenum": "\"$1\" is not in the list ($2) of [[Property:Allows value|allowed values]] for the \"$3\" property.", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" is not in the list ($2) of [[Property:Allows value|allowed values]] for the \"$3\" property.", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" is not within that range of \"$2\" specified by the [[Property:Allows value|allows value]] constraint for the \"$3\" property.", "smw-constraint-error": "Constraint issue", "smw-constraint-error-suggestions": "Please check listed violations and properties together with their annotated values to ensure that all constraint requirements are met.", "smw-constraint-error-limit": "The list will contain a maximum of $1 violations.", "smw_noboolean": "\"$1\" is not recognized as a Boolean (true/false) value.", "smw_true_words": "true,t,yes,y", "smw_false_words": "false,f,no,n", "smw_nofloat": "\"$1\" is not a number.", "smw_infinite": "Numbers as large as \"$1\" are not supported.", "smw_unitnotallowed": "\"$1\" is not declared as a valid unit of measurement for this property.", "smw_nounitsdeclared": "No units of measurement were declared for this property.", "smw_novalues": "No values specified.", "smw_nodatetime": "The date \"$1\" was not understood.", "smw_toomanyclosing": "There appear to be too many occurrences of \"$1\" in the query.", "smw_noclosingbrackets": "Some use of \"<nowiki>[[</nowiki>\" in your query was not closed by a matching \"]]\".", "smw_misplacedsymbol": "The symbol \"$1\" was used in a place where it is not useful.", "smw_unexpectedpart": "The part \"$1\" of the query was not understood.\nResults might not be as expected.", "smw_emptysubquery": "Some subquery has no valid condition.", "smw_misplacedsubquery": "Some subquery was used in a place where no subqueries are allowed.", "smw_valuesubquery": "Subqueries not supported for values of property \"$1\".", "smw_badqueryatom": "Some part \"<nowiki>[[...]]</nowiki>\" of the query was not understood.", "smw_propvalueproblem": "The value of property \"$1\" was not understood.", "smw_noqueryfeature": "Some query feature was not supported in this wiki and part of the query was dropped ($1).", "smw_noconjunctions": "Conjunctions in queries are not supported in this wiki and part of the query was dropped ($1).", "smw_nodisjunctions": "Disjunctions in queries are not supported in this wiki and part of the query was dropped ($1).", "smw_querytoolarge": "The following {{PLURAL:$2|query condition|$2 query conditions}} could not be considered due to this wiki's restrictions on query size or depth: <code>$1</code>.", "smw_notemplategiven": "Provide a value for the parameter \"template\" for this query format to work.", "smw_db_sparqlqueryproblem": "The query result could not be obtained from the SPARQL database. This error might be temporary or indicate a bug in the database software.", "smw_db_sparqlqueryincomplete": "Answering the query turned out to be too difficult and was aborted. Some results could be missing. If possible, try using a simpler query instead.", "smw_type_header": "Properties of type \"$1\"", "smw_typearticlecount": "Showing $1 {{PLURAL:$1|property|properties}} using this type.", "smw_attribute_header": "Pages using the property \"$1\"", "smw_attributearticlecount": "Showing $1 {{PLURAL:$1|page|pages}} using this property.", "smw-propertylist-subproperty-header": "Subproperties", "smw-propertylist-redirect-header": "Synonyms", "smw-propertylist-error-header": "Pages with improper assignments", "smw-propertylist-count": "Showing $1 related {{PLURAL:$1|entity|entities}}.", "smw-propertylist-count-with-restricted-note": "Showing $1 related {{PLURAL:$1|entity|entities }} (more are available but the display is restricted to \"$2\").", "smw-propertylist-count-more-available": "Showing $1 related {{PLURAL:$1|entity|entities}} (more are available).", "specialpages-group-smw_group": "Semantic MediaWiki", "specialpages-group-smw_group-maintenance": "Maintenance", "specialpages-group-smw_group-properties-concepts-types": "Properties, concepts, and types", "specialpages-group-smw_group-search": "Browse and search", "exportrdf": "Export pages to RDF", "smw_exportrdf_docu": "This page allows you to obtain data from a page in RDF format.\nTo export pages, enter the titles in the text box below, one title per line.", "smw_exportrdf_recursive": "Recursively export all related pages.\nNote that the result could be large!", "smw_exportrdf_backlinks": "Also export all pages that refer to the exported pages.\nGenerates browsable RDF.", "smw_exportrdf_lastdate": "Do not export pages that were not changed since the given point in time.", "smw_exportrdf_submit": "Export", "uriresolver": "URIResolver", "properties": "Properties", "smw-categories": "Categories", "smw_properties_docu": "The following properties are used in the wiki.", "smw_property_template": "$1 of type $2 ($3 {{PLURAL:$3|use|uses}})", "smw_property_template_notype": "$1 ($2)", "smw_propertylackspage": "All properties should be described by a page!", "smw_propertylackstype": "No type was specified for this property (assuming type $1 for now).", "smw_propertyhardlyused": "This property is hardly used within the wiki!", "smw-property-name-invalid": "Property $1 can not be used (invalid property name).", "smw-property-name-reserved": "\"$1\" was listed as reserved name and should not be used as a property. The following [https://www.semantic-mediawiki.org/wiki/Help:Property_naming help page] may contain information as to why this name was reserved.", "smw-sp-property-searchform": "Display properties that contain:", "smw-sp-property-searchform-inputinfo": "The input is case sensitive and when used for filtering, only properties that match the condition are displayed.", "smw-special-property-searchform": "Display properties that contain:", "smw-special-property-searchform-inputinfo": "The input is case sensitive and when used for filtering, only properties that match the condition are displayed.", "smw-special-property-searchform-options": "Options", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "None", "smw-special-wantedproperties-filter-unapproved": "Unapproved", "smw-special-wantedproperties-filter-unapproved-desc": "Filter option used in connection with the authority mode.", "concepts": "Concepts", "smw-special-concept-docu": "A [https://www.semantic-mediawiki.org/wiki/Help:Concepts concept] can be viewed as \"dynamic category\", i.e. as a collection of pages that are not created manually, but that are computed by Semantic MediaWiki from a description of a given query.", "smw-special-concept-header": "List of concepts", "smw-special-concept-count": "The following {{PLURAL:$1|concept is|$1 concepts are}} being listed.", "smw-special-concept-empty": "No concept was found.", "unusedproperties": "Unused properties", "smw-unusedproperties-docu": "This page lists [https://www.semantic-mediawiki.org/wiki/Unused_properties unused properties] that are declared although no other page makes use of them. For a differentiated view, see the [[Special:Properties|entire]] or [[Special:WantedProperties|wanted properties]] special pages.", "smw-unusedproperty-template": "$1 of type $2", "wantedproperties": "Wanted properties", "smw-wantedproperties-docu": "This page lists [https://www.semantic-mediawiki.org/wiki/Wanted_properties wanted properties] that are used in the wiki but do not have a page describing them. For a differentiated view, see the [[Special:Properties|entire]] or [[Special:UnusedProperties|unused properties]] special pages.", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|use|uses}})", "smw-special-wantedproperties-docu": "This page lists [https://www.semantic-mediawiki.org/wiki/Wanted_properties wanted properties] that are used in the wiki but do not have a page describing them. For a differentiated view, see the [[Special:Properties|entire]] or [[Special:UnusedProperties|unused properties]] special pages.", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|use|uses}})", "smw_purge": "Refresh", "smw-purge-update-dependencies": "Semantic MediaWiki is purging the current page due to some outdated dependencies it has detected which require an update.", "smw-purge-failed": "Semantic MediaWiki tried to purge the page but failed", "types": "Types", "smw_types_docu": "List of [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes available datatypes] with each [https://www.semantic-mediawiki.org/wiki/Help:Datatype type] representing a unique set of attributes to describe a value in terms of storage and display characteristics that are hereditary to an assigned property.", "smw-special-types-no-such-type": "\"$1\" is unknown or has not been specified as valid datatype.", "smw-statistics": "Semantic statistics", "smw-statistics-cached": "Semantic statistics (cached)", "smw-statistics-entities-total": "Entities (total)", "smw-statistics-entities-total-info": "An estimated row count of entities. It includes properties, concepts, or any other registered object representation that requires an ID assignment.", "smw-statistics-property-instance": "Property {{PLURAL:$1|value|values}} (total)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Property|Properties}}]] (total)", "smw-statistics-property-total-info": "The total of registered properties.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Property|Properties}} (total)", "smw-statistics-property-used": "{{PLURAL:$1|Property|Properties}} (used with at least one value)", "smw-statistics-property-page": "{{PLURAL:$1|Property|Properties}} (registered with a page)", "smw-statistics-property-page-info": "Count for properties that have a dedicated page and description.", "smw-statistics-property-type": "{{PLURAL:$1|Property|Properties}} (assigned to a datatype)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Query|Queries}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Query|Queries}}]] (embedded, total)", "smw-statistics-query-format": "<code>$1</code> format", "smw-statistics-query-size": "Query size", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Concept|Concepts}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Concept|Concepts}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Subobject|Subobjects}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Subobject|Subobjects}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Datatype|Datatypes}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Property value|Property values}} ([[Special:ProcessingErrorList|{{PLURAL:$1|improper annotation|improper annotations}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Property value|Property values}} ({{PLURAL:$1|improper annotation|improper annotations}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities Outdated {{PLURAL:$1|entity|entities}}]", "smw-statistics-delete-count-info": "Entities that have been marked for removal should be disposed of regularly using the provided maintenance scripts.", "smw_uri_doc": "The URI resolver implements the [$1 W3C TAG finding on httpRange-14].\nIt ensures that an RDF representation (for machines) or a wiki page (for humans) is delivered depending on the request.", "ask": "Semantic search", "smw_ask_doculink": "https://www.semantic-mediawiki.org/wiki/Help:Semantic_search", "smw-ask-help": "This section contains some links to help explain how to use the <code>#ask</code> syntax.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecting pages] describes how to select pages and build conditions\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Search operators] lists available search operators including those for range and wildcard queries\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Displaying information] outlines the use of printout statements and formatting options", "smw_ask_sortby": "Sort by column (optional)", "smw_ask_ascorder": "Ascending", "smw_ask_descorder": "Descending", "smw-ask-order-rand": "Random", "smw_ask_submit": "Find results", "smw_ask_editquery": "Edit query", "smw_add_sortcondition": "[Add sorting condition]", "smw-ask-sort-add-action": "Add sorting condition", "smw_ask_hidequery": "Hide query (compact view)", "smw_ask_help": "Querying help", "smw_ask_queryhead": "Condition", "smw_ask_printhead": "Printout selection", "smw_ask_printdesc": "(add one property name per line)", "smw_ask_format_as": "Format as:", "smw_ask_defaultformat": "default", "smw_ask_otheroptions": "Other options", "smw-ask-otheroptions-info": "This section contains options that alter printout statements. Parameter descriptions can be viewed by hovering over them.", "smw-ask-otheroptions-collapsed-info": "Please use the plus icon to view all available options", "smw_ask_show_embed": "Show embed code", "smw_ask_hide_embed": "Hide embed code", "smw_ask_embed_instr": "To embed this query inline into a wiki page use the code below.", "smw-ask-delete": "Remove", "smw-ask-sorting": "Sorting", "smw-ask-options": "Options", "smw-ask-options-sort": "Sort options", "smw-ask-format-options": "Format and options", "smw-ask-parameters": "Parameters", "smw-ask-search": "Search", "smw-ask-debug": "Debug", "smw-ask-debug-desc": "Generates query debug information", "smw-ask-no-cache": "Disable query cache", "smw-ask-no-cache-desc": "Results without query cache", "smw-ask-result": "Result", "smw-ask-empty": "Clear all entries", "smw-ask-download-link-desc": "Download queried results in $1 format", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Help with the selected format: $1", "smw-ask-condition-change-info": "The condition was altered and the search engine needs to rerun the query to produce results that match the new requirements.", "smw-ask-input-assistance": "Input assistance", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Input assistance] is provided for the printout, sort, and condition field. The condition field requires to use one of following prefixes:", "smw-ask-condition-input-assistance-property": "<code>p:</code> to fetch property suggestions (e.g. <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> to fetch category suggestions", "smw-ask-condition-input-assistance-concept": "<code>con:</code> to fetch concept suggestions", "smw-ask-format-change-info": "The format was modified and it is required to execute the query again to match new parameters and visualization options.", "smw-ask-format-export-info": "The selected format is an export format which has no visual representation therefore results are only provided as download.", "smw-ask-query-search-info": "The query <code><nowiki>$1</nowiki></code> was answered by the {{PLURAL:$3|1=<code>$2</code> (from cache)|<code>$2</code> (from cache)|<code>$2</code>}} in $4 {{PLURAL:$4|second|seconds}}.", "smw-ask-extra-query-log": "Query log", "smw-ask-extra-other": "Other", "searchbyproperty": "Search by property", "processingerrorlist": "Processing error list", "constrainterrorlist": "Constraint error list", "propertylabelsimilarity": "Property label similarity report", "missingredirectannotations": "Missing redirect annotations", "smw-processingerrorlist-intro": "The following list provides an overview about [https://www.semantic-mediawiki.org/wiki/Processing_errors processing errors] that appeared in connection with [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. It is recommended to monitor this list on a regular basis and correct invalid value annotations.", "smw-processingerrorlist-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Processing_errors", "smw-constrainterrorlist-intro": "The following list provides an overview about [https://www.semantic-mediawiki.org/wiki/Constraint_errors constraint errors] that appeared in connection with [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. It is recommended to monitor this list on a regular basis and correct invalid value annotations.", "smw-constrainterrorlist-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Constraint_errors", "smw-missingredirects-intro": "The following section will list pages that are missing [https://www.semantic-mediawiki.org/wiki/Redirects redirect] annotations in Semantic MediaWiki (by comparing with information stored in MediaWiki) and to restore those annotations either manually [https://www.semantic-mediawiki.org/wiki/Help:Purge purge] the page or run the <code>rebuildData.php</code> maintenance script (with option <code>--redirects</code>).", "smw-missingredirects-list": "Pages with missing annotations", "smw-missingredirects-list-intro": "Showing $1 {{PLURAL:$1|page|pages}} with missing redirect annotations.", "smw-missingredirects-noresult": "No missing redirect annotations found.", "smw_sbv_docu": "Search for all pages that have a given property and value.", "smw_sbv_novalue": "Enter a valid value for the property, or view all property values for \"$1\".", "smw_sbv_displayresultfuzzy": "A list of all pages that have property \"$1\" with value \"$2\".\nSince there have been only a few results, also nearby values are displayed.", "smw_sbv_property": "Property:", "smw_sbv_value": "Value:", "smw_sbv_submit": "Find results", "browse": "Browse wiki", "smw_browselink": "Browse properties", "smw_browse_article": "Enter the name of the page to start browsing from.", "smw_browse_go": "Go", "smw_browse_more": "...", "smw_browse_show_incoming": "Show incoming properties", "smw_browse_hide_incoming": "Hide incoming properties", "smw_browse_no_outgoing": "This page has no properties.", "smw_browse_no_incoming": "No properties link to this page.", "smw-browse-from-backend": "Information is currently being retrieved from the backend.", "smw-browse-intro": "This page provides details about a subject or entity instance, please enter the name of an object to be inspected.", "smw-browse-invalid-subject": "The subject validation returned with a \"$1\" error.", "smw-browse-api-subject-serialization-invalid": "The subject has an invalid serialization format.", "smw-browse-js-disabled": "It is suspected that JavaScript is disabled or not available. We recommend using a browser where it is supported. Other options are discussed on the [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>] configuration parameter page.", "smw-browse-show-group": "Show groups", "smw-browse-hide-group": "Hide groups", "smw-noscript": "This page or action requires JavaScript to work. Please enable JavaScript in your browser or use a browser where it is supported, so that functionality can be provided as requested. For further assistance, please have a look at the [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript] help page.", "smw_inverse_label_default": "$1 of", "smw_inverse_label_property": "Inverse property label", "pageproperty": "Page property search", "pendingtasklist": "List of pending tasks", "facetedsearch": "Faceted search", "smw_pp_docu": "Either enter a page and property, or just a property to retrieve all assigned values.", "smw_pp_from": "From page:", "smw_pp_type": "Property:", "smw_pp_submit": "Find results", "smw-prev": "previous {{PLURAL:$1|$1}}", "smw-next": "next {{PLURAL:$1|$1}}", "smw_result_prev": "Previous", "smw_result_next": "Next", "smw_result_results": "Results", "smw_result_noresults": "No results.", "smwadmin": "Semantic MediaWiki Dashboard", "smw-admin-statistics-job-title": "Job statistics", "smw-admin-statistics-job-docu": "The job statistics displays information about scheduled Semantic MediaWiki jobs that have not yet been executed. The number of jobs may be slightly inaccurate or contain failed attempts. Please consult the [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manual] for further information.", "smw-admin-statistics-querycache-title": "Query cache", "smw-admin-statistics-querycache-disabled": "The [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] has not been enabled on this wiki and therefore no statistics are available.", "smw-admin-statistics-querycache-legend": "The cache statistics is to contain provisional cumulative as well as derived data including:\n* \"misses\" as the total attempts to retrieve data from the cache with unattainable responses, forcing a direct repository (DB, triple-store etc.) retrieval\n* \"deletes\" as the total amount of cache eviction operations (either through a purge or query dependency)\n* \"hits\" contains the amount of cache retrievals from either embedded (queries called from within a wiki page) or non-embedded (if enabled, requested by pages like Special:Ask or the API) sources\n* \"medianRetrievalResponseTime\" is an orientation value of the median response time (in sec.) for cached and non-cached retrieval requests over the time span of the collection process\n* \"noCache\" indicates the amount of no attempt requests (limit=0 queries, 'no-cache' option etc.) to retrieve results from cache", "smw-admin-statistics-section-explain": "The section provides additional statistics for administrators.", "smw-admin-statistics-semanticdata-overview": "Overview", "smw-admin-permission-missing": "The access to this page has been blocked due to missing permissions, please consult the [https://www.semantic-mediawiki.org/wiki/Help:Permissions permissions] help page for details about the necessary settings.", "smw-admin-setupsuccess": "The storage engine was set up.", "smw_smwadmin_return": "Return to $1", "smw_smwadmin_updatestarted": "A new update process for refreshing the semantic data was started.\nAll stored data will be rebuilt or repaired where needed.\nYou can follow the progress of the update on this special page.", "smw_smwadmin_updatenotstarted": "There is already an update process running.\nNot creating another one.", "smw_smwadmin_updatestopped": "All existing update processes have been stopped.", "smw_smwadmin_updatenotstopped": "To stop the running update process, you must activate the checkbox to indicate that you are really sure.", "smw-admin-docu": "This special page helps you during installation, upgrade, maintenance and usage of <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> and also provides further administrative functions and tasks as well as statistics.\nRemember to back up valuable data before executing administrative functions.", "smw-admin-environment": "Software environment", "smw-admin-db": "Database setup", "smw-admin-db-preparation": "The table initialization is ongoing and may take a moment before results are displayed pending the size and possible table optimizations.", "smw-admin-dbdocu": "Semantic MediaWiki requires its own database structure (and is independent from MediaWiki hence do not affect the rest of the MediaWiki installation) in order to store the semantic data.\nThis setup function can be executed multiple times without doing any harm, but it is needed only once on installation or upgrade.", "smw-admin-permissionswarn": "If the operation fails with SQL errors, the database user employed by your wiki (check your \"LocalSettings.php\" file) probably does not have sufficient permissions.\nEither grant this user additional permissions to create and delete tables, temporarily enter the login of your database root in the \"LocalSettings.php\" file, or use the maintenance script <code>setupStore.php</code>, which can use the credentials of an administrator.", "smw-admin-dbbutton": "Initialize or upgrade tables", "smw-admin-announce": "Announce your wiki", "smw-admin-announce-text": "If your wiki is public, you can register it on <a href=\"https://wikiapiary.com\">WikiApiary</a>, the wiki tracking wiki.", "smw-admin-deprecation-notice-title": "Deprecation notices", "smw-admin-deprecation-notice-docu": "The following section contains settings that have been deprecated or removed but were detected to be active on this wiki. It is expected that any future release will remove support for these configurations.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> is deprecated and will be removed in $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> will remove (or replace) the following {{PLURAL:$2|option|options}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> is deprecated and will be removed in $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> was replaced by <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> was replaced by <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|option|options}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> is being replaced by <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> was removed in $2", "smw-admin-deprecation-notice-title-notice": "Deprecated settings", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Deprecated settings</b> shows settings that have been detected to be used on this wiki and are planned to be removed or changed in a future release.", "smw-admin-deprecation-notice-title-replacement": "Replaced or renamed settings", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>Replaced or renamed settings</b> contains settings that were renamed or otherwise modified and it is recommended to forthwith update their name or format.", "smw-admin-deprecation-notice-title-removal": "Removed settings", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Removed settings</b> identifies settings that were removed in a previous release but have been detected to be used on this wiki.", "smw-admin-deprecation-notice-section-legend": "Legend", "smw-smwadmin-refresh-title": "Data repair and update", "smw_smwadmin_datarefresh": "Data rebuild", "smw_smwadmin_datarefreshdocu": "It is possible to restore all Semantic MediaWiki data based on the current contents of the wiki.\nThis can be useful to repair broken data or to refresh the data if the internal format has changed due to some software upgrade.\nThe update is executed page by page and will not be completed immediately.\nThe following shows if an update is in progress and allows you to start or stop updates (unless this feature was disabled by the site administrator).", "smw_smwadmin_datarefreshprogress": "<strong>An update is already in progress.</strong>\nIt is normal that the update progresses only slowly since it only refreshes data in small chunks each time a user accesses the wiki.\nTo finish this update more quickly, you can invoke the MediaWiki maintenance script <code>runJobs.php</code> (use the option <code>--maxjobs 1000</code> to restrict the number of updates done in one batch).\nEstimated progress of current update:", "smw_smwadmin_datarefreshbutton": "Schedule data rebuild", "smw_smwadmin_datarefreshstop": "Stop this update", "smw_smwadmin_datarefreshstopconfirm": "Yes, I am {{GENDER:$1|sure}}.", "smw-admin-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki", "smw-admin-job-scheduler-note": "Tasks (those enabled) in this section are performed via the job queue to avoid deadlock situations during their execution. The [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue job queue] is responsible for the processing and it is critical that the <code>runJobs.php</code> maintenance script has an appropriate capacity (see also configuration parameter <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Outdated entities disposal", "smw-admin-outdateddisposal-intro": "Some activities (a change to a property type, the removal of wikipages, or the correction of error values) will result in [https://www.semantic-mediawiki.org/wiki/Outdated_entities outdated entities] and it is suggested to remove them periodically to free associated table space.", "smw-admin-outdateddisposal-active": "An outdated entities disposal job has been scheduled.", "smw-admin-outdateddisposal-button": "Schedule disposal", "smw-admin-feature-disabled": "This feature has been disabled on this wiki, please consult the <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">settings</a> help page or contact the system administrator.", "smw-admin-propertystatistics-title": "Property statistics rebuild", "smw-admin-propertystatistics-intro": "Rebuilds the entire property usage statistics and therein updates and corrects the [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count usage count] of properties.", "smw-admin-propertystatistics-active": "A property statistics rebuild job has been scheduled.", "smw-admin-propertystatistics-button": "Schedule statistics rebuild", "smw-admin-fulltext-title": "Full-text search rebuild", "smw-admin-fulltext-intro": "Rebuilds the search index from property tables with an enabled [https://www.semantic-mediawiki.org/wiki/Full-text full-text search] datatype. Changes to the index rules (altered stopwords, new stemmer etc.) and/or a newly added or altered table does require to run this job again.", "smw-admin-fulltext-active": "A full-text search rebuild job has been scheduled.", "smw-admin-fulltext-button": "Schedule full-text rebuild", "smw-admin-support": "Getting support", "smw-admin-supportdocu": "Various resources are provided to help you in case of problems:", "smw-admin-installfile": "If you experience problems with your installation, start by checking the guidelines in the <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">INSTALL file</a> and the <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">installation page</a>.", "smw-admin-smwhomepage": "The complete user documentation to Semantic MediaWiki is at <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Bugs can be reported to the <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">issue tracker</a>, the <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">reporting bugs</a> page provides some guidance on how to write an effective issue report.", "smw-admin-questions": "If you have further questions or suggestions, join the discussion on the Semantic MediaWiki <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">user mailing list</a>.", "smw-admin-other-functions": "Other functions", "smw-admin-statistics-extra": "Statistic functions", "smw-admin-statistics": "Statistics", "smw-admin-supplementary-section-title": "Supplementary functions", "smw-admin-supplementary-section-subtitle": "Supported core functions", "smw-admin-supplementary-section-intro": "This section provides additional functions beyond the scope of maintenance activities and it is possible that some functions which are listed (see the [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentation]) are restricted or unavailable and therefore inaccessible on this wiki.", "smw-admin-supplementary-settings-title": "Configuration and settings", "smw-admin-supplementary-settings-intro": "<u>$1</u> shows parameters that define the behaviour of Semantic MediaWiki", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Operational statistics", "smw-admin-supplementary-operational-statistics-short-title": "operational statistics", "smw-admin-supplementary-operational-statistics-intro": "Displays an extended set of <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Entity lookup and disposal", "smw-admin-supplementary-idlookup-short-title": "entity lookup and disposal", "smw-admin-supplementary-idlookup-intro": "Supports a simple <u>$1</u> function", "smw-admin-supplementary-duplookup-title": "Duplicate entities lookup", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> to find entities that are categorized as duplicates for the selected table matrix", "smw-admin-supplementary-duplookup-docu": "This page lists entries from selected tables that have been categorized as [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities duplicates]. Duplicate entries should (if at all) only occur on rare occasions potentially caused by a terminated update or unsuccessful rollback transaction.", "smw-admin-supplementary-duplookup-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities", "smw-admin-supplementary-operational-statistics-cache-title": "Cache statistics", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> shows a selected set of cache related statistics", "smw-admin-supplementary-operational-table-statistics-title": "Table statistics", "smw-admin-supplementary-operational-table-statistics-short-title": "table statistics", "smw-admin-supplementary-operational-table-statistics-intro": "Generates <u>$1</u> for a selected set of tables", "smw-admin-supplementary-operational-table-statistics-explain": "This section contains selected table statistics to help administrators and data curators to make informed decisions about that state of the back-end and the storage engine.", "smw-admin-supplementary-operational-table-statistics-legend": "The legend describes some of the keys used for the table statistics and includes:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> total number of rows in a table", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> last ID currently in use\n* <code>duplicate_count</code> number of duplicates found in the id_table (see also [[Special:SemanticMediaWiki/duplicate-lookup|Duplicate entities lookup]])\n* <code>rows.rev_count</code> number of rows that have a revision_id assigned indicating a direct wikipage link\n* <code>rows.smw_namespace_group_by_count</code> numbers of aggregated rows for namespaces used in the table\n* <code>rows.smw_proptable_hash.query_match_count</code> number of query subobjects with a corresponding table reference\n* <code>rows.smw_proptable_hash.query_null_count</code> number of query subobjects without a table reference (unlinked, floating reference)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> percentage of terms that are unique (a low percentage rate indicates that repetitive terms occupy the table content and index)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> number of terms that only appear once\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> number of terms that appear more than once", "smw-admin-supplementary-elastic-title": "Elasticsearch", "smw-admin-supplementary-elastic-version-info": "Version", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> shows details about settings and index statistics", "smw-admin-supplementary-elastic-docu": "This page contains information about settings, mappings, health, and index statistics related to an Elasticsearch cluster that is connected to Semantic MediaWiki and its [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Supported functions", "smw-admin-supplementary-elastic-settings-title": "Settings (indices)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> used by Elasticsearch to manage Semantic MediaWiki indices", "smw-admin-supplementary-elastic-mappings-title": "Mappings", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> to list indices and field mappings", "smw-admin-supplementary-elastic-mappings-docu": "This page contains field mapping details used by the current index. It is recommended to monitor the mappings in connection with the <code>index.mapping.total_fields.limit</code> (specifies the maximum number of fields in an index allowed).", "smw-admin-supplementary-elastic-mappings-docu-extra": "The <code>property_fields</code> refers to the count of indexed core fields while the <code>nested_fields</code> refers to an accumulated count of additional fields assigned to a core field to support specific structured search patterns.", "smw-admin-supplementary-elastic-mappings-summary": "Summary", "smw-admin-supplementary-elastic-mappings-fields": "Field mappings", "smw-admin-supplementary-elastic-nodes-title": "Nodes", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> shows node statistics", "smw-admin-supplementary-elastic-indices-title": "Indices", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> provides an overview of available indices and their statistics", "smw-admin-supplementary-elastic-statistics-title": "Statistics", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> shows index level statistics", "smw-admin-supplementary-elastic-statistics-docu": "This page provides an insight on indices statistics for different operations that are happening on an index level, the returned stats are aggregated with primaries and total aggregations. The [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html help page] contains a detailed description of available indices stats.", "smw-admin-supplementary-elastic-status-replication": "Replication status", "smw-admin-supplementary-elastic-status-last-active-replication": "Last active replication: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Refresh interval: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Recovery job backlog: $1 (estimation)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Ingest (file) job backlog: $1 (estimation)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replication locked: $1 (rebuild in-progress)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Replication monitoring (active): $1", "smw-admin-supplementary-elastic-replication-header-title": "Replication status", "smw-admin-supplementary-elastic-replication-function-title": "Replication", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> shows information about failed replications", "smw-admin-supplementary-elastic-replication-docu": "This page provides information about the [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring replication status] of entities that were reported to have issues with the Elasticsearch cluster. It is recommended to review listed entities and purge the content in order to confirm that it was a temporary issue.", "smw-admin-supplementary-elastic-replication-files-docu": "It should be noted that for the list of files, the [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion file ingest] job is required to be executed first and has to finish its processing.", "smw-admin-supplementary-elastic-replication-files": "Files", "smw-admin-supplementary-elastic-replication-pages": "Pages", "smw-admin-supplementary-elastic-endpoints": "Endpoints", "smw-admin-supplementary-elastic-config": "Configurations", "smw-admin-supplementary-elastic-no-connection": "The wiki is currently '''unable''' to establish a connection to the Elasticsearch cluster, please contact the wiki administrator to investigate the issue as it incapacitates the index and query ability of the system.", "smw-list-count": "The list contains $1 {{PLURAL:$1|entry|entries}}.", "smw-property-label-uniqueness": "The \"$1\" label was matched to at least one other property representation. Please consult the [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness help page] on how to resolve this issue.", "smw-property-label-similarity-title": "Property label similarity report", "smw-property-label-similarity-intro": "<u>$1</u> calculates similarities for existing property labels", "smw-property-label-similarity-threshold": "Threshold:", "smw-property-label-similarity-type": "Display Type ID", "smw-property-label-similarity-noresult": "No results were found for the selected options.", "smw-property-label-similarity-docu": "This pages compares the [https://www.semantic-mediawiki.org/wiki/Property_similarity similarity distance] (not to be confused with a semantic or lexical similarity) between property labels and reports them if they exceed the threshold. The report may help filter misspelled or equivalent properties that represent the same concept (see the [[Special:Properties|properties]] special page to help clarify concept and usage of reported properties). The threshold can be adjusted to widen or narrow the distance used for the approximate matching. <code>[[Property:$1|$1]]</code> is used to exempt properties from the analysis.", "smw-admin-operational-statistics": "This page contains operational statistics collected in or from Semantic MediaWiki related functions. An extended list of wiki specific statistics can be found [[Special:Statistics|<b>here</b>]].", "smw_adminlinks_datastructure": "Data structure", "smw_adminlinks_displayingdata": "Data display", "smw_adminlinks_inlinequerieshelp": "Inline queries help", "smw-page-indicator-usage-count": "Estimated [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count usage count]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|User|System}} defined property", "smw-property-indicator-last-count-update": "Estimated usage count\nLast updated: $1", "smw-concept-indicator-cache-update": "Cache count\nLast updated: $1", "smw-createproperty-isproperty": "It is a property of type $1.", "smw-createproperty-allowedvals": "The allowed {{PLURAL:$1|value for this property is|values for this property are}}:", "smw-paramdesc-category-delim": "The delimiter", "smw-paramdesc-category-template": "A template to format the items with", "smw-paramdesc-category-userparam": "A parameter to pass to the template", "smw-info-par-message": "Message to display.", "smw-info-par-icon": "Icon to show, either \"info\" or \"warning\".", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "General options", "prefs-extended-search-options": "Extended search", "prefs-ask-options": "Semantic search", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] and associated extensions provide individual preferences for a group of selected features and functions. A list of individual settings with their description and characteristics is available on the following [https://www.semantic-mediawiki.org/wiki/Help:User_preferences help page].", "smw-prefs-ask-options-tooltip-display": "Display parameter text as an info tooltip on the #ask [[Special:Ask|query builder]] special page.", "smw-prefs-ask-options-compact-view-basic": "Enable basic compact view", "smw-prefs-help-ask-options-compact-view-basic": "If enabled, displays a reduced set of links on the Special:Ask compact view.", "smw-prefs-general-options-time-correction": "Enable time correction for special pages using the local [[Special:Preferences#mw-prefsection-rendering|time offset]] preference", "smw-prefs-general-options-jobqueue-watchlist": "Show the job queue watchlist in my personal bar", "smw-prefs-help-general-options-jobqueue-watchlist": "If enabled, shows a [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist list of] pending selected jobs together with their estimated queue sizes.", "smw-prefs-general-options-disable-editpage-info": "Disable the introductory text on the edit page", "smw-prefs-general-options-disable-search-info": "Disable the syntax support information on the standard search page", "smw-prefs-general-options-suggester-textinput": "Enable input assistance for semantic entities", "smw-prefs-help-general-options-suggester-textinput": "If enabled, allows to use an [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance input assistance] to find properties, concepts, and categorties from an input context.", "smw-prefs-general-options-show-entity-issue-panel": "Show the entity issue panel", "smw-prefs-help-general-options-show-entity-issue-panel": "If enabled, runs integrity checks on each page and shows the [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel Entity issue panel].", "smw-prefs-factedsearch-profile": "Select a [[Special:FacetedSearch|faceted search]] default profile:", "smw-ui-tooltip-title-property": "Property", "smw-ui-tooltip-title-quantity": "Unit conversion", "smw-ui-tooltip-title-info": "Information", "smw-ui-tooltip-title-service": "Service links", "smw-ui-tooltip-title-warning": "Warning", "smw-ui-tooltip-title-error": "Error", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "Event", "smw-ui-tooltip-title-note": "Note", "smw-ui-tooltip-title-legend": "Legend", "smw-ui-tooltip-title-reference": "Reference", "smw_unknowntype": "The \"$1\" type of this property is invalid", "smw-concept-cache-text": "The concept has a total of $1 {{PLURAL:$1|page|pages}}, and was last updated $3, $2.", "smw_concept_header": "Pages of concept \"$1\"", "smw_conceptarticlecount": "Showing below $1 {{PLURAL:$1|page|pages}}.", "smw-qp-empty-data": "Requested data could not be displayed due to some insufficient selection criteria.", "right-smw-admin": "Access to administration tasks (Semantic MediaWiki)", "right-smw-patternedit": "Edit access to maintain allowed regular expressions and patterns (Semantic MediaWiki)", "right-smw-pageedit": "Edit access for <code>Is edit protected</code> annotated pages (Semantic MediaWiki)", "right-smw-schemaedit": "Edit [https://www.semantic-mediawiki.org/wiki/Help:Schema schema pages] (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "Access to the job queue [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist watchlist] feature (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Access information about an entity associated revision mismatch (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "View [https://www.semantic-mediawiki.org/wiki/Help:Edit_help edit help] (Semantic MediaWiki)", "restriction-level-smw-pageedit": "protected (only eligible users)", "action-smw-patternedit": "edit regular expressions used by Semantic MediaWiki", "action-smw-pageedit": "edit pages annotated with <code>Is edit protected</code> (Semantic MediaWiki)", "group-smwadministrator": "Administrators (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrator (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:Administrators (Semantic MediaWiki)", "group-smwcurator": "Curators (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|curator (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:Curators (Semantic MediaWiki)", "group-smweditor": "Editors (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|editor (Semantic MediaWiki)}}", "grouppage-smweditor": "{{ns:project}}:Editors (Semantic MediaWiki)", "action-smw-admin": "access Semantic MediaWiki administration tasks", "action-smw-ruleedit": "edit rule pages (Semantic MediaWiki)", "smw-property-namespace-disabled": "The property [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks namespace] is disabled, attempting to declare a type or other property specific characteristics for this property is not possible.", "smw-property-predefined-default": "\"$1\" is a predefined property of type $2.", "smw-property-predefined-common": "This property is pre-deployed (also known as [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property]) and comes with additional administrative privileges but can be used just like any other [https://www.semantic-mediawiki.org/wiki/Property user-defined property].", "smw-property-predefined-ask": "\"$1\" is a predefined property that represents meta information (in form of a [https://www.semantic-mediawiki.org/wiki/Subobject subobject]) about individual queries and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "\"$1\" is a predefined property that collects the number of conditions used in a query and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "\"$1\" is a predefined property that informs about the depth of a query and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "It is a numerical value computed on the basis of subquery nesting, property chains, and available description elements with the execution of a query being restricted by the <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code> configuration parameter.", "smw-property-predefined-askpa": "\"$1\" is a predefined property describing parameters that influence a query result and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "It is part of a collection of properties that specify a [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler query profile].", "smw-sp-properties-docu": "This page lists [https://www.semantic-mediawiki.org/wiki/Property properties] and their usage counts available for this wiki. For up-to-date count statistics it is recommended that the [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics property statistics] maintenance script is run on a regular basis. For a differentiated view, see the [[Special:UnusedProperties|unused]] or [[Special:WantedProperties|wanted properties]] special pages.", "smw-sp-properties-cache-info": "The listed data have been retrieved from [https://www.semantic-mediawiki.org/wiki/Caching cache], and were last updated $1.", "smw-sp-properties-header-label": "List of properties", "smw-admin-settings-docu": "Displays a list of all default and localized settings that are relevant to the Semantic MediaWiki environment. For details on individual settings, please consult the [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration] help page.", "smw-sp-admin-settings-button": "Generate settings list", "smw-admin-idlookup-title": "Lookup", "smw-admin-idlookup-docu": "This section shows technical details about an individual entity (wikipage, subobject, property, etc.) in Semantic MediaWiki. The input can be a numeric ID or a string value to match the relevant search field, yet any ID reference relates to Semantic MediaWiki and not to MediaWiki's page or revision ID.", "smw-admin-iddispose-title": "Disposal", "smw-admin-iddispose-docu": "It should be noted that the disposal operation is unrestricted and will remove the entity from the storage engine together with all its references in pending tables, if confirmed. Please perform this task with '''caution''' and only after the [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentation] has been consulted.", "smw-admin-iddispose-done": "ID \"$1\" was removed from the storage backend.", "smw-admin-iddispose-references": "ID \"$1\" has {{PLURAL:$2|no|at least one}} active reference:", "smw-admin-iddispose-references-multiple": "List of matches with at least one active reference record.", "smw-admin-iddispose-no-references": "The search was unable to match \"$1\" to a table entry.", "smw-admin-idlookup-input": "Search:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Overview", "smw-admin-tab-notices": "Deprecation notices", "smw-admin-tab-maintenance": "Maintenance", "smw-admin-tab-supplement": "Supplementary functions", "smw-admin-tab-registry": "Registry", "smw-admin-tab-alerts": "<PERSON><PERSON><PERSON>", "smw-admin-alerts-tab-deprecationnotices": "Deprecation notices", "smw-admin-alerts-tab-maintenancealerts": "Maintenance alerts", "smw-admin-alerts-section-intro": "This section shows alerts and notices related to settings, operations, and other activities that have been classified to require attention from an administrator or user with appropriated rights.", "smw-admin-maintenancealerts-section-intro": "The following alerts and notices should be resolved and while not essential it is expected to help improve system and operational maintainability.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Table optimization", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "The system has found that the last [https://www.semantic-mediawiki.org/wiki/Table_optimization table optimization] was run $2 days ago (record from $1) which exceeds the $3 days maintenance threshold. As mentioned in the documentation, running optimizations will allow the query planner to make better decisions about queries therefore it is suggested to run the table optimization on a regular basis.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Outdated entities", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "The system has counted $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities outdated entities] and reached a critical level of unattended maintenance by exceeding the threshold of $2. It is recommended to run the [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] maintenance script.", "smw-admin-maintenancealerts-invalidentities-alert-title": "Invalid entities", "smw-admin-maintenancealerts-invalidentities-alert": "The system matched $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|entity|entities}}] to an [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace unmaintained namespace] and it is recommended to run the [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] or [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>] maintenance script.", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Settings", "smw-admin-configutation-tab-namespaces": "Namespaces", "smw-admin-configutation-tab-schematypes": "Schema types", "smw-admin-maintenance-tab-tasks": "Tasks", "smw-admin-maintenance-tab-scripts": "Maintenance scripts", "smw-admin-maintenance-no-description": "No description.", "smw-admin-maintenance-script-section-title": "List of available maintenance scripts", "smw-admin-maintenance-script-section-intro": "The following maintenance scripts require an administrator and access to the command line to be able to execute listed scripts.", "smw-admin-maintenance-script-description-dumprdf": "RDF export of existing triples.", "smw-admin-maintenance-script-description-rebuildconceptcache": "This script is used to manage concept caches for Semantic MediaWiki where it can create, remove, and update selected caches.", "smw-admin-maintenance-script-description-rebuilddata": "Recreates all the semantic data in the database, by cycling through all the pages that might have semantic data.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Rebuilds the Elasticsearch index (only for installations that use the <code>ElasticStore</code>), by cycling through all entities that have semantic data.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Find missing entities in Elasticsearch (only for installations that use the <code>ElasticStore</code>) and schedule appropriate update jobs.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Rebuilds the <code>SQLStore</code> fulltext search index (for installations where the setting has been enabled).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Rebuilds the usage statistics for all property entities.", "smw-admin-maintenance-script-description-removeduplicateentities": "Removes duplicate entities found in selected tables that have no active references.", "smw-admin-maintenance-script-description-setupstore": "Sets up the storage and query backend as defined in <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Updates the <code>smw_sort</code> field in the <code>SQLStore</code> (in accordance with the [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation] setting).", "smw-admin-maintenance-script-description-populatehashfield": "Populates the <code>smw_hash</code> field for rows missing the value.", "smw-admin-maintenance-script-description-purgeentitycache": "Purge cache entries for known entities and their associated data.", "smw-admin-maintenance-script-description-updatequerydependencies": "Update queries and query dependencies (see the [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore] setting).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Dispose of outdated entities and query links.", "smw-admin-maintenance-script-description-runimport": "Populate and import auto-discovered content from [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Update scripts", "smw-admin-maintenance-script-section-rebuild": "Rebuild scripts", "smw-livepreview-loading": "Loading...", "smw-sp-searchbyproperty-description": "This page provides a simple [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces browsing interface] for finding entities described by a property and a named value. Other available search interfaces include the [[Special:PageProperty|page property search]], and the [[Special:Ask|ask query builder]].", "smw-sp-searchbyproperty-resultlist-header": "List of results", "smw-sp-searchbyproperty-nonvaluequery": "A list of values that have the property \"$1\" assigned.", "smw-sp-searchbyproperty-valuequery": "A list of pages that have property \"$1\" with value \"$2\" annotated.", "smw-datavalue-number-textnotallowed": "\"$1\" can not be assigned to a declared number type with value $2.", "smw-datavalue-number-nullnotallowed": "\"$1\" returned with a \"NULL\" which is not allowed as number.", "smw-editpage-annotation-enabled": "This page supports semantic in-text annotations (e.g. <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) to build structured and queryable content provided by Semantic MediaWiki. For a comprehensive description on how to use annotations or the #ask parser function, please have a look at the [https://www.semantic-mediawiki.org/wiki/Help:Getting_started getting started], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation in-text annotation], or [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries inline queries] help pages.", "smw-editpage-annotation-disabled": "This page is not enabled for semantic in-text annotations due to namespace restrictions. Details about how to enable the namespace can be found on the [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration] help page.", "smw-editpage-property-annotation-enabled": "This property can be extended using semantic annotations to specify a datatype (e.g. <nowiki>\"[[Has type::Page]]\"</nowiki>) or other supporting declarations (e.g. <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). For a description on how to augment this page, see the [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaration of a property] or the [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes list of available data types] help page.", "smw-editpage-property-annotation-disabled": "This property cannot be extended with a datatype annotation (e.g. <nowiki>\"[[Has type::Page]]\"</nowiki>) as it is already predefined (see the [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special properties] help page for more information).", "smw-editpage-concept-annotation-enabled": "This concept can be extended using the #concept parser function. For a description on how to use #concept, see the [https://www.semantic-mediawiki.org/wiki/Help:Concepts concept] help page.", "smw-search-syntax-support": "The search input supports the use of the semantic [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search query syntax] to help match results using Semantic MediaWiki.", "smw-search-input-assistance": "The [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance input assistant] is also enabled to ease the pre-selection of available properties and categories.", "smw-search-help-intro": "A <code><nowiki>[[ ... ]]</nowiki></code> input will signal to the input processor to use the Semantic MediaWiki search back-end. It should be noted that combining <code><nowiki>[[ ... ]]</nowiki></code> with an unstructured text search such as <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> is not supported.", "smw-search-help-structured": "Structured searches:\n\n* <code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (as [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context filtered context])\n\n* <code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (with a [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context query context])", "smw-search-help-proximity": "Proximity searches (a property being unknown, '''only''' available for those back-ends that provide a full-text search integration):\n\n* <code><nowiki>[[in:lorem ipsum]]</nowiki></code> (search in all documents for \"lorem\" and \"ipsum\" that have been indexed)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (match \"lorem ipsum\" as phrase)", "smw-search-help-ask": "The following links will explain how to use the <code>#ask</code> syntax.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecting pages] describes how to select pages and build conditions\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Search operators] lists available search operators including those for range and wildcard queries", "smw-search-input": "Input and search", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Input assistance] is provided for the input field and requires to use one of following prefixes:\n\n* <code>p:</code> to enable property suggestions (e.g. <code><nowiki>[[p:Has ...</nowiki></code>)\n\n* <code>c:</code> to enable category suggestions\n\n* <code>con:</code> to enable concept suggestions", "smw-search-syntax": "Syntax", "smw-search-profile": "Extended", "smw-search-profile-tooltip": "Search functions in connection with Semantic MediaWiki", "smw-search-profile-sort-best": "Best match", "smw-search-profile-sort-recent": "Most recent", "smw-search-profile-sort-title": "Title", "smw-search-profile-extended-help-intro": "The Special:Search [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile extended profile] provides access to search functions specific to Semantic MediaWiki and its supported query backend.", "smw-search-profile-extended-help-sort": "Specifies a sorting preference for the result display with:", "smw-search-profile-extended-help-sort-title": "* \"Title\" using the page title (or display title) as sort criteria", "smw-search-profile-extended-help-sort-recent": "* \"Most recent\" will show the most recent modified entities first (subobject entities will be suppressed as those entities are not annotated with a [[Property:Modification date|Modification date]])", "smw-search-profile-extended-help-sort-best": "* \"Best match\" will sort entities by [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy relevancy] based on scores provided by the backend", "smw-search-profile-extended-help-form": "Forms are provided (if maintained) to match specific use cases by exposing different property and value fields to narrow down the input process and make it easy for users to proceed with a search request. (see $1)", "smw-search-profile-extended-help-namespace": "The namespace selection box will be hidden as soon as a form is selected but can be made visible with the help of the \"show/hide\" button.", "smw-search-profile-extended-help-search-syntax": "The search input field supports the use of the <code>#ask</code> syntax to define a Semantic MediaWiki specific search context. Useful expressions include:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> to find anything that contains \"...\" and is especially useful when the search context or properties involved are unknown (e.g. <code>in:(lorem && ipsum)</code> is equivalent to <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> to find anything that contains \"...\" in the exact same order", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> to match any entity with a property \"...\" (e.g. <code>has:(Foo && Bar)</code> is equivalent to <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> to not match any entity that includes \"...\"", "smw-search-profile-extended-help-search-syntax-prefix": "* Additional custom prefixes are available and defined such as: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Some expressions are reserved such as: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Some of the listed operations are only useful in connection with an enabled full-text index or the ElasticStore.''", "smw-search-profile-extended-help-query": "Used <code><nowiki>$1</nowiki></code> as query.", "smw-search-profile-extended-help-query-link": "For more details, please use the $1.", "smw-search-profile-extended-help-find-forms": "available forms", "smw-search-profile-extended-section-sort": "Sort by", "smw-search-profile-extended-section-form": "Forms", "smw-search-profile-extended-section-search-syntax": "Search input", "smw-search-profile-extended-section-namespace": "Namespace", "smw-search-profile-extended-section-query": "Query", "smw-search-profile-link-caption-query": "query builder", "smw-search-show": "Show", "smw-search-hide": "<PERSON>de", "log-name-smw": "Semantic MediaWiki log", "log-show-hide-smw": "$1 Semantic MediaWiki log", "logeventslist-smw-log": "Semantic MediaWiki log", "log-description-smw": "Activities for [https://www.semantic-mediawiki.org/wiki/Help:Logging enabled event types] that have been reported by Semantic MediaWiki and its components.", "logentry-smw-maintenance": "Maintenance related events emitted by Semantic MediaWiki", "smw-datavalue-import-unknown-namespace": "The import namespace \"$1\" is unknown. Please ensure that OWL import details are available via [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Unable to find a \"$1\" namespace URI in the [[MediaWiki:Smw import $1|$1 import]].", "smw-datavalue-import-missing-type": "No type definition was found for \"$1\" in the [[MediaWiki:Smw import $2|$2 import]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1 import]]", "smw-datavalue-import-invalid-value": "\"$1\" is not a valid format and is expected to consist of \"namespace\":\"identifier\" (e.g. \"foaf:name\").", "smw-datavalue-import-invalid-format": "Expected the string \"$1\" to be divided into four parts but the format was not understood.", "smw-property-predefined-impo": "\"$1\" is a predefined property that describes a relation to an [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary imported vocabulary] and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "\"$1\" is a predefined property that describes the [[Special:Types|datatype]] of a property and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "\"$1\" is a predefined property representing a [https://www.semantic-mediawiki.org/wiki/Help:Container container] construct and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "The container allows to accumulate property-value assignments similar to that of a normal wiki page but within a different entity space while being linked to the embedding subject.", "smw-property-predefined-errp": "\"$1\" is a predefined property that tracks input errors for irregular value annotations and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "In most cases it is caused by a type mismatch or a [[Property:Allows value|value]] restriction.", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] is a predefined property that can define a list of permissible values to restrict value assignments for a property and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list \"$1\"] is a predefined property that can specify a reference to a list that holds permissible values to restrict value assignments for a property and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "Property \"$1\" has a restricted application area and cannot be used as annotation property by a user.", "smw-datavalue-property-restricted-declarative-use": "Property \"$1\" is a declarative property and can only be used on a property or category page.", "smw-datavalue-property-create-restriction": "Property \"$1\" doesn't exist and the user is missing the \"$2\" permission (see [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode authority mode]) to create or annotate values with an unapproved property.", "smw-datavalue-property-invalid-character": "\"$1\" contains a listed \"$2\" character as part of the property label and has therefore been classified as invalid.", "smw-datavalue-property-invalid-chain": "Using \"$1\" as property chain is not permitted during the annotation process.", "smw-datavalue-restricted-use": "Datavalue \"$1\" has been marked for restricted use.", "smw-datavalue-invalid-number": "\"$1\" can not be interpreted as a number.", "smw-query-condition-circular": "A possible circular condition has been detected in \"$1\".", "smw-query-condition-empty": "The query description has an empty condition.", "smw-types-list": "List of datatypes", "smw-types-default": "\"$1\" is a built-in datatype.", "smw-types-help": "Further information and examples can be found on this [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 help page].", "smw-type-anu": "\"$1\" is a variant of the [[Special:Types/URL|URL]] datatype and is mostly used for a ''owl:AnnotationProperty'' export declaration.", "smw-type-boo": "\"$1\" is a basic datatype to describe a true/false value.", "smw-type-cod": "\"$1\" is a variant of the [[Special:Types/Text|Text]] datatype to be used for technical texts of arbitrary length, such as source code listings.", "smw-type-geo": "\"$1\" is a datatype that describes geographic locations and requires the [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] extension to provide an extended functionality.", "smw-type-tel": "\"$1\" is a special datatype to describe international telephone numbers according to RFC 3966.", "smw-type-txt": "\"$1\" is a basic datatype to describe strings of arbitrary length.", "smw-type-dat": "\"$1\" is a basic datatype to represent points in time in a unified format.", "smw-type-ema": "\"$1\" is a special datatype to represent an email.", "smw-type-tem": "\"$1\" is a special numeric datatype to represent a temperature.", "smw-type-qty": "\"$1\" is a datatype to describe quantities with a numeric representation and a unit of measurement.", "smw-type-rec": "\"$1\" is a container datatype that specifies a list of typed properties in a fixed order.", "smw-type-extra-tem": "The conversion schema includes supported units such as Kelvin, Celsius, Fahrenheit, and Rankine.", "smw-type-tab-properties": "Properties", "smw-type-tab-types": "Types", "smw-type-tab-type-ids": "Type IDs", "smw-type-tab-errors": "Errors", "smw-type-primitive": "Basic", "smw-type-contextual": "Contextual", "smw-type-compound": "Compound", "smw-type-container": "Container", "smw-type-no-group": "Unclassified", "smw-specials-browse-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:<PERSON><PERSON><PERSON>", "smw-specials-bytype-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Type_$1", "smw-specials-types-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Types", "smw-special-pageproperty-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:PageProperty", "smw-special-pageproperty-description": "This page provides a browsing interface for finding all values of a property and a given page. Other available search interfaces include the [[Special:SearchByProperty|property search]], and the [[Special:Ask|ask query builder]].", "smw-property-predefined-errc": "\"$1\" is a predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] and represents errors that appeared in connection with improper value annotations or input processing.", "smw-property-predefined-long-errc": "Errors are collected in a [https://www.semantic-mediawiki.org/wiki/Help:Container container] that may include a reference to the property that caused the discrepancy.", "smw-property-predefined-errt": "\"$1\" is a predefined property containing a textual description of an error and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "A user-defined subobject contained an invalid naming scheme. The dot notation ($1) used within the first five characters is reserved for extensions. You may set a [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier named identifier].", "smw-datavalue-record-invalid-property-declaration": "The record definition contains the \"$1\" property which itself is declared as record type and that is not permitted.", "smw-property-predefined-mdat": "\"$1\" is a predefined property that corresponds to the date of the last modification of a subject and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "\"$1\" is a predefined property that corresponds to the date of the first revision of a subject and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "\"$1\" is a predefined property that indicates whether a subject is new or not and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "\"$1\" is a predefined property that contains the page name of the user who created the last revision and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "\"$1\" is a predefined property that describes the MIME type of an uploaded file and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "\"$1\" is a predefined property that describes the media type of an uploaded file and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "\"$1\" is a predefined property that holds the name of the result format used in a query and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "\"$1\" is a predefined property that describes the conditions of the query as a string and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "\"$1\" is a predefined property containing a time value (in seconds) that was required to complete the query execution and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "\"$1\" is a predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] that identifies alternative (e.g. remote, federated) query sources.", "smw-property-predefined-askco": "\"$1\" is a predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to describe the state of a query or its components.", "smw-property-predefined-long-askco": "The number or numbers assigned represent an internal codified state that is explained on the [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler help page].", "smw-property-predefined-prec": "\"$1\" is a predefined property that describes a [https://www.semantic-mediawiki.org/wiki/Help:Display_precision display precision] (in decimal digits) for numeric datatypes.", "smw-property-predefined-attch-link": "\"$1\" is a predefined property that collects embedded file and image links found in a page and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-inst": "\"$1\" is an internal predefined property that stores category information independent of MediaWiki and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-unit": "\"$1\" is a declarative predefined property to define units of display for numeric typed properties and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-unit": "A comma-separated list allows to describe units or formats to be used for the display.", "smw-property-predefined-conv": "\"$1\" is a declarative predefined property to define conversion factor for some unit of a physical quantity and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-serv": "\"$1\" is a declarative predefined property to add service links to a property and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-redi": "\"$1\" is an internal predefined property to record redirects and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subp": "\"$1\" is a declarative predefined property to define that a property is a [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of subproperty of] another and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subc": "\"$1\" is a predefined property to define that a category is a [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of subcategory of] another and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-conc": "\"$1\" is an internal predefined property to define an associated concept and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-err-type": "\"$1\" is a predefined property to indentify a group or class of [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors processing errors] and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-skey": "\"$1\" is an internal predefined property to hold a sort reference and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pplb": "\"$1\" is a declarative predefined property to specify a [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label preferred property label] and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-chgpro": "\"$1\" is a predefined property to hold [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation change propagation] information and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-link": " and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-format-schema": " and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-profile-schema": " and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans": " and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-source": " and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-group": " and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cont-len": "\"$1\" is a predefined property to store length information and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-len": "It is used in connection with the [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (and the [https://www.semantic-mediawiki.org/Attachment_processor attachment processor]) to collect and store length information retrieved from an ingested file (if provided).", "smw-property-predefined-cont-lang": "\"$1\" is a predefined property to store language information and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-lang": "It is used in connection with the [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (and the [https://www.semantic-mediawiki.org/Attachment_processor attachment processor]) to collect and store language information retrieved from an ingested file (if provided).", "smw-property-predefined-cont-title": "\"$1\" is a predefined property to store title information and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-title": "It is used in connection with the [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (and the [https://www.semantic-mediawiki.org/Attachment_processor attachment processor]) to collect and store title information retrieved from an ingested file (if provided).", "smw-property-predefined-cont-author": "\"$1\" is a predefined property to store author information and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-author": "It is used in connection with the [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (and the [https://www.semantic-mediawiki.org/Attachment_processor attachment processor]) to collect and store author information retrieved from an ingested file (if provided).", "smw-property-predefined-cont-date": "\"$1\" is a predefined property to store date information and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-date": "It is used in connection with the [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (and the [https://www.semantic-mediawiki.org/Attachment_processor attachment processor]) to collect and store date information retrieved from an ingested file (if provided).", "smw-property-predefined-cont-type": "\"$1\" is a predefined property to store file type information and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-type": "It is used in connection with the [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (and the [https://www.semantic-mediawiki.org/Attachment_processor attachment processor]) to collect and store type information retrieved from an ingested file (if provided).", "smw-property-predefined-cont-keyw": "\"$1\" is a predefined property to represent keywords and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-keyw": "It is used in connection with the [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (and the [https://www.semantic-mediawiki.org/Attachment_processor attachment processor]) to collect and store keywords retrieved from an ingested file (if provided).", "smw-property-predefined-file-attch": "\"$1\" is a predefined property to represent a container that stores attachment information and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-file-attch": "It is used in connection with the [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (and the [https://www.semantic-mediawiki.org/Attachment_processor attachment processor]) to collect all content specific information retrievable from an ingested file (if provided).", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps Extension \"Maps\"] was not detected therefore \"$1\" is restricted in its capacity to operate.", "smw-datavalue-monolingual-dataitem-missing": "An expected item for building a monolingual compound value is missing.", "smw-datavalue-monolingual-lcode-parenthesis": "($1)", "smw-datavalue-languagecode-missing": "For the \"$1\" annotation, the parser was unable to determine a language code (i.e. \"foo@en\").", "smw-datavalue-languagecode-invalid": "\"$1\" was not recognized as a supported language code.", "smw-property-predefined-lcode": "\"$1\" is a predefined property that represents a BCP47 formatted language code and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "\"$1\" is a [https://www.semantic-mediawiki.org/wiki/Help:Container container] datatype that associates a text value with a specific [[Property:Language code|language code]].", "smw-types-extra-mlt-lcode": "The datatype does {{PLURAL:$2|require|not require}} a language code (i.e. {{PLURAL:$2|a value annotation without a language code is not accepted|a value annotation without a language code is accepted}}).", "smw-property-predefined-text": "\"$1\" is a predefined property that represents text of arbitrary length and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "\"$1\" is a predefined property that allows to describe a property in context of a language and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "\"$1\" is a predefined property to define a list of properties used with a [[Special:Types/Record|record]] typed property and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] In-text annotation parser time", "smw-limitreport-intext-postproctime": "[SMW] post processing time", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|second|seconds}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|second|seconds}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Store update time (on page purge)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|second|seconds}}", "smw_allows_pattern": "This page is expected to contain a list of references (followed by [https://en.wikipedia.org/wiki/Regular_expression regular expressions]) to be made available by the [[Property:Allows pattern|Allows pattern]] property. To edit this page, the <code>smw-patternedit</code> right is required.", "smw-datavalue-allows-pattern-mismatch": "\"$1\" was classified as invalid by the \"$2\" regular expression.", "smw-datavalue-allows-pattern-reference-unknown": "The \"$1\" pattern reference could not be matched to an entry in [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "The \"$1\" list reference was not matchable to a [[MediaWiki:Smw allows list $1]] page.", "smw-datavalue-allows-value-list-missing-marker": "The \"$1\" list content is missing items with a * list marker.", "smw-datavalue-feature-not-supported": "The \"$1\" feature is not supported or was disabled on this wiki.", "smw-property-predefined-pvap": "\"$1\" is a predefined property that can specify a [[MediaWiki:Smw allows pattern|pattern reference]] to apply [https://en.wikipedia.org/wiki/Regular_expression regular expression] matching and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "\"$1\" is a predefined property that can assign a distinct display title to an entity and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "\"$1\" is a predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to restrict value assignments for each instance to be unique (or one at most).", "smw-property-predefined-long-pvuc": "Uniqueness is established when two values are not equal in their literal representation and any violation of that constraint will be categorized as error.", "smw-datavalue-constraint-uniqueness-violation": "Property \"$1\" only permits unique value assignments and ''$2'' was already annotated in subject \"$3\".", "smw-datavalue-constraint-uniqueness-violation-isknown": "Property \"$1\" only permits unique value annotations, ''$2'' already contains an assigned value. \"$3\" violates the uniqueness constraint.", "smw-datavalue-constraint-violation-non-negative-integer": "Property \"$1\" has a \"non negative integer\" constraint and value ''$2'' is violating that requirement.", "smw-datavalue-constraint-violation-must-exists": "Property \"$1\" has a <code>must_exists</code> constraint and value ''$2'' is violating that requirement.", "smw-datavalue-constraint-violation-single-value": "The \"[[Property:$1|$1]]\" property has a <code>single_value</code> constraint and value \"$2\" is violating that requirement.", "smw-constraint-violation-uniqueness": "A <code>unique_value_constraint</code> constraint is assigned to the \"[[Property:$1|$1]]\" property which only permits unique value assignments and the ''$2'' value annotation was already found to be annotated in the \"$3\" subject.", "smw-constraint-violation-uniqueness-isknown": "A <code>unique_value_constraint</code> constraint is assigned to the \"[[Property:$1|$1]]\" property, therefore only unique value annotations are permitted. ''$2'' already contains an annotated value with \"$3\", violating the uniqueness constraint for the current subject.", "smw-constraint-violation-non-negative-integer": "A <code>non_negative_integer</code> constraint is assigned to the \"[[Property:$1|$1]]\" property and the ''$2'' value annotation is violating the constraint requirement.", "smw-constraint-violation-must-exists": "A <code>must_exists</code> constraint is assigned to the \"[[Property:$1|$1]]\" property and the ''$2'' value annotation is violating the constraint requirement.", "smw-constraint-violation-single-value": "A <code>single_value</code> constraint is assigned to the \"[[Property:$1|$1]]\" property and the \"$2\" value annotation is violating the constraint requirement.", "smw-constraint-violation-class-shape-constraint-missing-property": "A <code>shape_constraint</code> is assigned to the \"[[:$1]]\" category with a <code>property</code> key, the required \"$2\" property is missing.", "smw-constraint-violation-class-shape-constraint-wrong-type": "A <code>shape_constraint</code> is assigned to the \"[[:$1]]\" category with a <code>property_type</code> key, the \"$2\" property doesn't match the type of \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "A <code>shape_constraint</code> is assigned to the \"[[:$1]]\" category with a <code>max_cardinality</code> key, the \"$2\" property doesn't match the cardinality of \"$3\".", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "A <code>shape_constraint</code> is assigned to the \"[[:$1]]\" category with a <code>min_textlength</code> key, the \"$2\" property doesn't match the length requirement of \"$3\".", "smw-constraint-violation-class-mandatory-properties-constraint": "A <code>mandatory_properties</code> constraint is assigned to the \"[[:$1]]\" category and requires the following mandatory properties: $2", "smw-constraint-violation-allowed-namespace-no-match": "A <code>allowed_namespaces</code> constraint is assigned to the \"[[Property:$1|$1]]\" property and \"$2\" violates the namespace requirement, only the following \"$3\" namespaces are allowed.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "The <code>allowed_namespaces</code> constraint requires a page type.", "smw-constraint-schema-category-invalid-type": "The annotated \"$1\" schema is invalid for a category, it requires a \"$2\" type.", "smw-constraint-schema-property-invalid-type": "The annotated \"$1\" schema is invalid for a property, it requires a \"$2\" type.", "smw-constraint-error-allows-value-list": "\"$1\" is not in the list ($2) of [[Property:Allows value|allowed values]] for the \"$3\" property.", "smw-constraint-error-allows-value-range": "\"$1\" is not within that range of \"$2\" specified by the [[Property:Allows value|allows value]] constraint for the \"$3\" property.", "smw-property-predefined-boo": "\"$1\" is a [[Special:Types/Boolean|type]] and predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to represent boolean values.", "smw-property-predefined-num": "\"$1\" is a [[Special:Types/Number|type]] and predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to represent numeric values.", "smw-property-predefined-dat": "\"$1\" is a [[Special:Types/Date|type]] and predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to represent date values.", "smw-property-predefined-uri": "\"$1\" is a [[Special:Types/URL|type]] and predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to represent URI/URL values.", "smw-property-predefined-qty": "\"$1\" is a [[Special:Types/Quantity|type]] and predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to represent quantity values.", "smw-datavalue-time-invalid-offset-zone-usage": "\"$1\" contains an offset and zone identifier which is not supported.", "smw-datavalue-time-invalid-values": "The \"$1\" value contains uninterpretable information in form of \"$2\".", "smw-datavalue-time-invalid-date-components-common": "\"$1\" contains some uninterpretable information.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\" contains an extrinsic dash or other characters that are invalid for a date interpretation.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" contains some empty components.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" contains more than three components required for a date interpretation.", "smw-datavalue-time-invalid-date-components-sequence": "\"$1\" contains a sequence that could not be interpreted against an available match matrix for date components.", "smw-datavalue-time-invalid-ampm": "\"$1\" contains \"$2\" as hour element that is invalid for a 12-hour convention.", "smw-datavalue-time-invalid-jd": "Unable to interpret the \"$1\" input value as valid JD (Julian day) number with \"$2\" being reported.", "smw-datavalue-time-invalid-prehistoric": "Unable to interpret a prehistoric \"$1\" input value. For example, having specified more than years or a calendar model may return unexpected results in a prehistoric context.", "smw-datavalue-time-invalid": "Unable to interpret the \"$1\" input value as valid date or time component with \"$2\" being reported.", "smw-datavalue-external-formatter-uri-missing-placeholder": "Formatter URI is missing the ''$1'' placeholder.", "smw-datavalue-external-formatter-invalid-uri": " \"$1\" is an invalid URL.", "smw-datavalue-external-identifier-formatter-missing": "The property is missing an [[Property:External formatter uri|\"External formatter URI\"]] assignment.", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "The \"$1\" external identifier expects a multi field substitution but the current \"$2\" value is missing at least one value parameter to match the requirement.", "smw-datavalue-keyword-maximum-length": "The keyword exceeded the maximum length of $1 {{PLURAL:$1|character|characters}}.", "smw-property-predefined-eid": "\"$1\" is a [[Special:Types/External identifier|type]] and predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to represent external identifiers.", "smw-property-predefined-peid": "\"$1\" is a predefined property that specifies an external identifier and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pefu": "\"$1\" is a predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to specify an external resource with a placeholder.", "smw-property-predefined-long-pefu": "The URI is expected to contain a placeholder that will be adjusted with an [[Special:Types/External identifier|external identifier]] value to form a valid resource reference.", "smw-type-eid": "\"$1\" is a variant of the [[Special:Types/Text|Text]] datatype to describe external resources (URI based) and requires assigned properties to declare an [[Property:External formatter uri|External formatter URI]].", "smw-property-predefined-keyw": "\"$1\" is a predefined property and [[Special:Types/Keyword|type]] provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] that normalizes a text and has a restricted character length.", "smw-type-keyw": "\"$1\" is a variant of the [[Special:Types/Text|Text]] datatype that has a restricted character length with a normalized content representation.", "smw-datavalue-stripmarker-parse-error": "The given value \"$1\" contains [https://en.wikipedia.org/wiki/Help:Strip_markers strip markers] and therefore it cannot be parsed sufficiently.", "smw-datavalue-parse-error": "The given value \"$1\" was not understood.", "smw-datavalue-propertylist-invalid-property-key": "The property list \"$1\" contained an invalid property key \"$2\".", "smw-datavalue-type-invalid-typeuri": "The \"$1\" type could not be transformed into a valid URI representation.", "smw-datavalue-wikipage-missing-fragment-context": "The wikipage input value \"$1\" cannot be used without a context page.", "smw-datavalue-wikipage-invalid-title": "The page type input value \"$1\" contains invalid characters or is incomplete and therefore can cause unexpected results during a query or annotation process.", "smw-datavalue-wikipage-property-invalid-title": "Property \"$1\" (as page type) with input value \"$2\" contains invalid characters or is incomplete and therefore can cause unexpected results during a query or annotation process.", "smw-datavalue-wikipage-empty": "The wikipage input value is empty (e.g. <code>[[SomeProperty::]], [[]]</code>) and therefore it cannot be used as a name or as part of a query condition.", "smw-type-ref-rec": "\"$1\" is a [https://www.semantic-mediawiki.org/wiki/Container container] type that allows to record additional information (e.g. provenance data) about a value assignment.", "smw-datavalue-reference-outputformat": "$1: $2", "smw-datavalue-reference-invalid-fields-definition": "The [[Special:Types/Reference|Reference]] type expects a list of properties to be declared using the [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Has fields] property.", "smw-parser-invalid-json-format": "The JSON parser returned with a \"$1\".", "smw-property-preferred-title-format": "$1 ($2)", "smw-property-preferred-label-language-combination-exists": "\"$1\" cannot be used as preferred label because the language \"$2\" is already assigned to the \"$3\" label.", "smw-parse": "$1", "smw-clipboard-copy-link": "Copy link to clipboard", "smw-property-userdefined-fixedtable": "\"$1\" was configured as [https://www.semantic-mediawiki.org/wiki/Fixed_properties fixed property] and any modification to its [https://www.semantic-mediawiki.org/wiki/Type_declaration type declaration] requires to either run <code>setupStore.php</code> or to complete the special [[Special:SemanticMediaWiki|\"Database installation and upgrade\"]] task.", "smw-data-lookup": "Fetching data...", "smw-data-lookup-with-wait": "The request is being processed and may take a moment.", "smw-no-data-available": "No data available.", "smw-property-req-violation-missing-fields": "Property \"$1\" is missing a required [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] declaration for this \"$2\" type.", "smw-property-req-violation-multiple-fields": "Property \"$1\" contains multiple (therefore competing) [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] declarations, only one is expected for this \"$2\" type.", "smw-property-req-violation-missing-formatter-uri": "Property \"$1\" is missing declaration details for the annotated type by failing to define the <code>External formatter URI</code> property.", "smw-property-req-violation-predefined-type": "Property \"$1\" as predefined property contains a \"$2\" type declaration that is incompatible with the default type of this property.", "smw-property-req-violation-import-type": "A type declaration was detected that is incompatible with the predefined type of the imported \"$1\" vocabulary. In general, it is not necessary to declare a type because information are retrieved from the import definition.", "smw-property-req-violation-change-propagation-locked-error": "Property \"$1\" was altered and requires assigned entities to be reevaluated using a [https://www.semantic-mediawiki.org/wiki/Change_propagation change propagation] process. The property page has been locked until the primary specification update is completed to prevent intermediary interruptions or contradictory specifications. The process may take a moment before the page can be unlocked as it depends on the size and frequency of the [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue job queue] scheduler.", "smw-property-req-violation-change-propagation-locked-warning": "Property \"$1\" was altered and requires assigned entities to be reevaluated using a [https://www.semantic-mediawiki.org/wiki/Change_propagation change propagation] process. The update may take a moment as it depends on the size and frequency of the [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue job queue] scheduler and it is suggested to postpone changes to the property to prevent intermediary interruptions or contradictory specifications.", "smw-property-req-violation-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation Change propagation] updates are pending ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|job|jobs}}] estimated) and it is recommended to wait with modifications to a property until the process has been finalized to prevent intermediary interruptions or contradictory specifications.", "smw-property-req-violation-missing-maps-extension": "Semantic MediaWiki was unable to detect the [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"] extension which is a prerequisite and as a consequence limits the functionality (i.e. unable to store or process geographic data) of this property.", "smw-property-req-violation-type": "The property contains competing type specifications which may result in invalid value annotations therefore it is expected that a user assigns one appropriate type.", "smw-property-req-error-list": "The property contains the following errors or warnings:", "smw-property-req-violation-parent-type": "Property \"$1\" and the assigned parent property \"$2\" have different type annotations.", "smw-property-req-violation-forced-removal-annotated-type": "The [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance mandatory parent type inheritance] enforcement has been enabled, the annotate type for the \"$1\" property doesn't match its parent property \"$2\" type and has been altered to reflect that requirement. It is recommended to adjust the in-page type definition so that the error message and mandatory enforcement is removed for this property.", "smw-change-propagation-protection": "This page is locked to prevent accidental data modification while a [https://www.semantic-mediawiki.org/wiki/Change_propagation change propagation] update is run. The process may take a moment before the page is unlocked as it depends on the size and frequency of the [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue job queue] scheduler.", "smw-category-change-propagation-locked-error": "Category \"$1\" was altered and requires assigned entities to be reevaluated using a [https://www.semantic-mediawiki.org/wiki/Change_propagation change propagation] process. In the meantime, the category page has been locked until the primary specification update is completed to prevent intermediary interruptions or contradictory specifications. The process may take a moment before the page can be unlocked as it depends on the size and frequency of the [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue job queue] scheduler.", "smw-category-change-propagation-locked-warning": "Category \"$1\" was altered and requires assigned entities to be reevaluated using a [https://www.semantic-mediawiki.org/wiki/Change_propagation change propagation] process. The update may take a moment as it depends on the size and frequency of the [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue job queue] scheduler and it is suggested to postpone changes to the category to prevent intermediary interruptions or contradictory specifications.", "smw-category-change-propagation-pending": "[https://www.semantic-mediawiki.org/wiki/Change_propagation Change propagation] updates are pending ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|job|jobs}}] estimated) and it is recommended to wait with modifications to a category until the process has been finalized to prevent intermediary interruptions or contradictory specifications.", "smw-category-invalid-value-assignment": "\"$1\" is not recognized as valid category or value annotation.", "protect-level-smw-pageedit": "Allow only users with page edit permission (Semantic MediaWiki)", "smw-create-protection": "Creation of the \"$1\" property is restricted to users with the appropriate \"$2\" right (or [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups user group]) while the [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode authority mode] is enabled.", "smw-create-protection-exists": "Changes to the \"$1\" property is restricted to users with the appropriate \"$2\" right (or [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups user group]) while the [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode authority mode] is enabled.", "smw-edit-protection": "This page is [[Property:Is edit protected|protected]] to prevent accidental data modification and can only be edited by users with the appropriate edit right (\"$1\") or [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups user group].", "smw-edit-protection-disabled": "The edit protection has been disabled therefore \"$1\" cannot be used to protect entity pages from unauthorized editing.", "smw-edit-protection-auto-update": "Semantic MediaWiki has updated the protection status according to the \"Is edit protected\" property.", "smw-edit-protection-enabled": "Edit protected (Semantic MediaWiki)", "smw-patternedit-protection": "This page is protected and can only be edited by users with the appropriate <code>smw-patternedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions permission].", "smw-property-predefined-edip": "\"$1\" is a predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to indicate whether editing is protected or not.", "smw-property-predefined-long-edip": "While any user is qualified to add this property to a subject, only a user with a dedicated permission can edit or revoke the protection to an entity after it has been added.", "smw-query-reference-link-label": "Query reference", "smw-format-datatable-emptytable": "No data available in table", "smw-format-datatable-info": "Showing _START_ to _END_ of _TOTAL_ entries", "smw-format-datatable-infoempty": "Showing 0 to 0 of 0 entries", "smw-format-datatable-infofiltered": "(filtered from _MAX_ total entries)", "smw-format-datatable-infothousands": ",", "smw-format-datatable-lengthmenu": "Show _MENU_ entries", "smw-format-datatable-loadingrecords": "Loading...", "smw-format-datatable-processing": "Processing...", "smw-format-datatable-search": "Search:", "smw-format-datatable-zerorecords": "No matching records found", "smw-format-datatable-first": "First", "smw-format-datatable-last": "Last", "smw-format-datatable-next": "Next", "smw-format-datatable-previous": "Previous", "smw-format-datatable-sortascending": ": activate to sort column ascending", "smw-format-datatable-sortdescending": ": activate to sort column descending", "smw-format-datatable-toolbar-export": "Export", "smw-format-list-separator": ", ", "smw-format-list-property-separator": ", ", "smw-format-list-value-separator": ", ", "smw-format-list-field-label-separator": ": ", "smw-format-list-other-fields-open": " (", "smw-format-list-other-fields-close": ")", "smw-category-invalid-redirect-target": "Category \"$1\" contains an invalid redirect target to a non-category namespace.", "smw-parser-function-expensive-execution-limit": "The parser function has reached the limit for expensive executions (see configuration parameter [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "Semantic MediaWiki is refreshing the current page on the condition of some required query post processing.", "apihelp-smwinfo-summary": "API module to retrieve information about Semantic MediaWiki statistics and other meta information.", "apihelp-ask-summary": "API module to query Semantic MediaWiki using the ask language.", "apihelp-askargs-summary": "API module to query Semantic MediaWiki using the ask language as list of conditions, printouts and parameters.", "apihelp-browsebyproperty-summary": "API module to retrieve information about a property or list of properties.", "apihelp-browsebysubject-summary": "API module to retrieve information about a subject.", "apihelp-smwtask-summary": "API module to execute Semantic MediaWiki related tasks (for internal use only, not for public use).", "apihelp-smwbrowse-summary": "API module to support browse activities for different entity types in Semantic MediaWiki.", "apihelp-ask-parameter-api-version": "Output formatting:\n;2:Backwards-compatible format using {} for the result list.\n;3:Experimental format using [] as result list.", "apihelp-smwtask-param-task": "Defines the task type", "apihelp-smwtask-param-params": "JSON encoded parameters that match the selected task type requirement", "smw-apihelp-smwtask-example-update": "Example of running a update task for a particular subject:", "smw-api-invalid-parameters": "Invalid parameters, \"$1\"", "smw-parser-recursion-level-exceeded": "The level of $1 recursions was exceeded during a parse process. It is suggested to validate the template structure, or if necessary to adjust the configuration parameter <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "Showing $1 {{PLURAL:$1|page|pages}} using this property.", "smw-property-page-list-search-count": "Showing $1 {{PLURAL:$1|page|pages}} using this property with a \"$2\" value match.", "smw-property-page-filter-note": "The [https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter search filter] allows the inclusion of [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions query expressions] such as <code>~</code> or <code>!</code>. The selected [https://www.semantic-mediawiki.org/wiki/Query_engine query engine] might also support case insensitive matching or other short expressions like:\n\n* <code>in:</code> result should include the term, e.g. '<code>in:Foo</code>'\n\n* <code>not:</code> result should to not include the term, e.g. '<code>not:Bar</code>'", "smw-property-reserved-category": "Category", "smw-category": "Category", "smw-datavalue-uri-invalid-scheme": " \"$1\" has not been listed as valid URI scheme.", "smw-datavalue-uri-invalid-authority-path-component": "\"$1\" has been identified to contain an invalid \"$2\" authority or path component.", "smw-browse-property-group-title": "Property group", "smw-browse-property-group-label": "Property group label", "smw-browse-property-group-description": "Property group description", "smw-property-predefined-ppgr": "\"$1\" is a predefined property that identifies entities (mainly categories) that are used as grouping instance for properties and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-filter": "Filter", "smw-section-expand": "Expand the section", "smw-section-collapse": "Collapse the section", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1] format", "smw-help": "Help", "smw-cheat-sheet": "Cheat sheet", "smw-personal-jobqueue-watchlist": "Job queue watchlist", "smw-personal-jobqueue-watchlist-explain": "The numbers indicate an estimation of job queue entries awaiting execution.", "smw-property-predefined-label-skey": "<PERSON><PERSON><PERSON>", "smw-processing": "Processing...", "smw-loading": "Loading...", "smw-fetching": "Fetching...", "smw-preparing": "Preparing...", "smw-expand": "Expand", "smw-collapse": "Collapse", "smw-copy": "Copy", "smw-copy-clipboard-title": "Copies content to the clipboard", "smw-jsonview-expand-title": "Expands the JSON view", "smw-jsonview-collapse-title": "Collapses the JSON view", "smw-jsonview-search-label": "Search:", "smw-redirect-target-unresolvable": "The target is unresolvable on the reason of \"$1\"", "smw-types-title": "Type: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Changing the content model of a [https://www.semantic-mediawiki.org/wiki/Help:Schema schema page] is not permitted.", "smw-schema-namespace-edit-protection": "This page is protected and can only be edited by users with the appropriate <code>smw-schemaedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions permission].", "smw-schema-namespace-edit-protection-by-import-performer": "This page was imported by a listed [https://www.semantic-mediawiki.org/wiki/Import_performer import performer]. This means that changing the content of this page is restricted to only those listed users.", "smw-schema-error-title": "Validation {{PLURAL:$1|error|errors}}", "smw-schema-error-schema": "The validation schema '''$1''' found the following inconsistencies:", "smw-schema-error-miscellaneous": "Miscellaneous error ($1)", "smw-schema-error-validation-json-validator-inaccessible": "The JSON validator \"<b>$1</b>\" is not accessible (or installed) and is the reason why the \"$2\" file cannot be examined which prevents the current page from being saved or altered.", "smw-schema-error-validation-file-inaccessible": "The validation file \"$1\" is inaccessible.", "smw-schema-error-violation": "[\"$1\", \"$2\"]", "smw-schema-error-type-missing": "The content is missing a type in order for it to be recognized and usable in the [https://www.semantic-mediawiki.org/wiki/Help:Schema schema namespace].", "smw-schema-error-type-unknown": "The \"$1\" type is not registered and cannot be used for content in the [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema] namespace.", "smw-schema-error-json": "JSON error: \"$1\"", "smw-schema-error-input": "The input validation has found the following issues, they need to be addressed before the content can be saved. The [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling schema help] page may provide some advice on how to remove inconsistencies or resolve issues with the schema input.", "smw-schema-error-input-schema": "The validation schema '''$1''' found the following inconsistencies and they need to be addressed before the content can be saved. The [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling schema help] page may provide some advice on how to resolve these issues.", "smw-schema-error-title-prefix": "This schema type requires that the title of the schema starts with a \"$1\" prefix.", "smw-schema-validation-error": "The \"$1\" type is not registered and cannot be used for content in the [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema] namespace.", "smw-schema-validation-schema-title": "JSON schema", "smw-schema-summary-title": "Summary", "smw-schema-title": "<PERSON><PERSON><PERSON>", "smw-schema-type-help-link": "https://www.semantic-mediawiki.org/wiki/Help:Schema/Type/$1", "smw-schema-usage": "Usage", "smw-schema-type": "Schema type", "smw-schema-type-description": "Type description", "smw-schema-description": "Schema description", "smw-schema-description-link-format-schema": "This schema type supports the definition of characteristics for creating context sensitive links in connection with a [[Property:Formatter schema|formatter schema]] assigned property.", "smw-schema-description-search-form-schema": "This schema type supports the definition of input forms and characteristics for the [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch extended search] profile where it contains instructions on how to generate input fields, define default namespaces, or declare prefix expressions for a search request.", "smw-schema-description-property-profile-schema": "This schema type supports the definition of a profile to declare characteristics to the assigned property and its annotation values.", "smw-schema-description-facetedsearch-profile-schema": "This schema type supports the definition of profiles used as part of the [[Special:FacetedSearch|Faceted search]] environment.", "smw-schema-description-property-group-schema": "This schema type supports the definition of [https://www.semantic-mediawiki.org/wiki/Help:Property_group property groups] to help structure the [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse browsing] interface.", "smw-schema-description-property-constraint-schema": "This supports the definition of constraint rules for a property instance as well as those values assigned to it.", "smw-schema-description-class-constraint-schema": "This schema type supports the definition of constraint rules for a class instance (a.k.a. category).", "smw-schema-tag": "{{PLURAL:$1|Tag|Tags}}", "smw-property-predefined-constraint-schema": "\"$1\" is a predefined property that defines a constraint schema and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-desc": "\"$1\" is a predefined property that stores a schema description and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-def": "\"$1\" is a predefined property that stores the schema content and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-tag": "\"$1\" is a predefined property provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] to identify a collection of schemata.", "smw-property-predefined-long-schema-tag": "A label that identifies schemata of similar content or characteristics.", "smw-property-predefined-schema-type": "\"$1\" is a predefined property that describes a type to distinguish a group of schemata and is provided by [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-schema-type": "Each [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type type] provides its own interpretation of syntax elements and application rules and can be expressed with the help of a [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation validation schema].", "smw-ask-title-keyword-type": "Keyword search", "smw-ask-message-keyword-type": "This search matches the <code><nowiki>$1</nowiki></code> condition.", "smw-remote-source-unavailable": "Unable to connect to the remote \"$1\" target.", "smw-remote-source-disabled": "The '''$1''' source has disabled the remote request support!", "smw-remote-source-unmatched-id": "The '''$1''' source does not match a version of Semantic MediaWiki that can support a remote request.", "smw-remote-request-note": "The result is fetched from the '''$1''' remote source and it is likely for generated content to contain information that is not available from within the current wiki.", "smw-remote-request-note-cached": "The result is '''cached''' from the '''$1''' remote source and it is likely for generated content to contain information that is not available from within the current wiki.", "smw-parameter-missing": "Parameter \"$1\" is missing.", "smw-property-tab-usage": "Usage", "smw-property-tab-profile-schema": "Profile schema", "smw-property-tab-redirects": "Synonyms", "smw-property-tab-subproperties": "Subproperties", "smw-property-tab-errors": "Improper assignments", "smw-property-tab-constraint-schema": "Constraint schema", "smw-property-tab-constraint-schema-title": "Compiled constraint schema", "smw-property-tab-specification": "... more", "smw-concept-tab-list": "List", "smw-concept-tab-errors": "Errors", "smw-ask-tab-result": "Result", "smw-ask-tab-extra": "Extra", "smw-ask-tab-debug": "Debug", "smw-ask-tab-code": "Code", "smw-helplink-concepts": "https://www.semantic-mediawiki.org/wiki/Help:Concepts", "smw-install-incomplete-tasks-title": "Incomplete administration tasks", "smw-install-incomplete-intro": "There are $2 incomplete or [[Special:PendingTaskList|pending]] {{PLURAL:$2|task|tasks}} to finish {{PLURAL:$1|installation|upgrading}} of [https://www.semantic-mediawiki.org Semantic MediaWiki]. An administrator or user with sufficient rights can complete {{PLURAL:$2|it|these}}. This should be done before adding new data to avoid inconsistencies.", "smw-install-incomplete-intro-note": "This message will disappear after all relevant tasks have been resolved.", "smw-pendingtasks-intro-empty": "No tasks have been classified as pending, incomplete, or outstanding in connection with Semantic MediaWiki.", "smw-pendingtasks-intro": "This page provides information about tasks that have been classified as pending, incomplete, or outstanding in connection with Semantic MediaWiki.", "smw-pendingtasks-setup-no-tasks-intro": "The installation (or upgrade) has been completed, currently there are no pending or outstanding tasks.", "smw-pendingtasks-tab-setup": "Setup", "smw-updateentitycollation-incomplete": "The <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> setting was recently altered and requires that the <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> script is executed so that entities are updated and contain the correct sort field value.", "smw-updateentitycountmap-incomplete": "The <code>smw_countmap</code> field was added in a recent release and requires that the <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> script is executed so that functions can access the content of this field.", "smw-populatehashfield-incomplete": "The <code>smw_hash</code> field population was skipped during the setup. The <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> script must be executed.", "smw-install-incomplete-populate-hash-field": "The <code>smw_hash</code> field population was skipped during the setup. The <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> script must be executed.", "smw-install-incomplete-elasticstore-indexrebuild": "The <code>ElasticStore</code> has been selected as [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore default store], yet the extension was unable to find any record that the <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> script was executed, please run the script as instructed.", "smw-elastic-rebuildelasticindex-run-incomplete": "The <code>ElasticStore</code> has been selected as [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore default store], yet the extension was unable to find any record that the <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> script was executed, please run the script as instructed.", "smw-pendingtasks-setup-intro": "The {{PLURAL:$1|installation|upgrade}} of <b>Semantic MediaWiki</b> has classified the following tasks as [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade incomplete] and an administrator (or user with sufficient rights) is expected to resolve those tasks before users continue to create or alter content.", "smw-pendingtasks-setup-tasks": "Tasks", "smw-helplink": "https://www.semantic-mediawiki.org/wiki/Help:$1", "smw-filter-count": "Filter count", "smw-es-replication-check": "Replication check (Elasticsearch)", "smw-es-replication-error": "Elasticsearch replication issue", "smw-es-replication-file-ingest-error": "File ingest issue", "smw-es-replication-maintenance-mode": "Elasticsearch maintenance", "smw-es-replication-error-missing-id": "The replication monitoring has found that article \"$1\" (ID: $2) is missing from the Elasticsearch backend.", "smw-es-replication-error-divergent-date": "The replication monitoring has found that for the \"$1\" article (ID: $2) the <b>modification date</b> shows a discrepancy.", "smw-es-replication-error-divergent-date-short": "The following date information were used for comparison:", "smw-es-replication-error-divergent-date-detail": "Referenced modification date:\n* Elasticsearch: $1\n* Database: $2", "smw-es-replication-error-divergent-revision": "The replication monitoring has found that for the \"$1\" article (ID: $2) the <b>associated revision</b> shows a discrepancy.", "smw-es-replication-error-divergent-revision-short": "The following associated revision data were used for comparison:", "smw-es-replication-error-divergent-revision-detail": "Referenced associated revision:\n* Elasticsearch: $1\n* Database: $2", "smw-es-replication-error-maintenance-mode": "The Elasticsearch replication is currently restricted because it operates in a [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>maintenance mode</b>], changes to entities and pages are <b>not</b> immediately visible and query results may contain outdated information.", "smw-es-replication-error-no-connection": "The replication monitoring is unable to perform any checks since it cannot establish a connection to the Elasticsearch cluster.", "smw-es-replication-error-bad-request-exception": "The Elasticsearch connection handler has thrown a bad request exception (\"400 conflict http error\") indicating a continuing issue during replication and search requests.", "smw-es-replication-error-other-exception": "The Elasticsearch connection handler has thrown an exception: \"$1\".", "smw-es-replication-error-suggestions": "It is suggested to edit or purge the page to remove the discrepancy. If the issue remains then check the Elasticsearch cluster itself (allocator, exceptions, disk space etc.).", "smw-es-replication-error-suggestions-maintenance-mode": "It is suggested to contact the wiki administrator to check whether an [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild index rebuild] is currently in progress or the <code>refresh_interval</code> hasn't been set to the expected default value.", "smw-es-replication-error-suggestions-no-connection": "It is suggested to contact the wiki administrator and report the \"no connection\" issue.", "smw-es-replication-error-suggestions-exception": "Please check the logs for information about the status of Elasticsearch, their indices, and possible misconfiguration issues.", "smw-es-replication-error-file-ingest-missing-file-attachment": "The replication monitoring has found that \"$1\" is missing a [[Property:File attachment|File attachment]] annotation indicating that the file ingest processor hasn't started or isn't finished.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Please ensure that the [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion file ingest] job is scheduled and executed before the annotation and file index is made available.", "smw-report": "Report", "smw-legend": "Legend", "smw-datavalue-constraint-schema-category-invalid-type": "The annotated \"$1\" schema is invalid for a category, it requires a \"$2\" type.", "smw-datavalue-constraint-schema-property-invalid-type": "The annotated \"$1\" schema is invalid for a property, it requires a \"$2\" type.", "smw-entity-examiner-check": "Running {{PLURAL:$1|an examiner|examiners}} in the background", "smw-entity-examiner-indicator": "Entity issue panel", "smw-entity-examiner-deferred-check-awaiting-response": "The \"$1\" examiner is currently awaiting a response from the backend.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "Constraint", "smw-entity-examiner-associated-revision-mismatch": "Revision", "smw-entity-examiner-deferred-fake": "Fake", "smw-entity-examiner-indicator-suggestions": "As part of the entity examination, the following {{PLURAL:$1|issue was|issues were}} found and it is suggested to carefully review {{PLURAL:$1|the issue|them}} and take appropriate {{PLURAL:$1|action|actions}}.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Constraint|Constraints}}", "smw-indicator-revision-mismatch": "Revision", "smw-indicator-revision-mismatch-error": "The [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner associated revision] check found a mismatch between the revision referenced in MediaWiki and the one being associated in Semantic MediaWiki for this entity.", "smw-indicator-revision-mismatch-comment": "A mismatch normally indicates that some process interrupted the storage operation in Semantic MediaWiki. It is recommended to review the server logs and look for exceptions or other failures.", "smw-facetedsearch-intro-text": "The [https://www.semantic-mediawiki.org/wiki/Faceted_search <b>Faceted Search</b>] of Semantic MediaWiki provides users with a simple interface to quickly narrow down query results from a condition with the help of faceted views created from dependent properties and categories.", "smw-facetedsearch-intro-tips": "* Use <code>category:?</code>, <code>property:?</code>, or <code>concept:?</code> to find available categories, properties, or concepts to build a condition set\n* Use the #ask syntax to describe a condition (e.g. <code><nowiki>[[Category:Foo]]</nowiki></code>)\n* Use \"OR\", \"AND\", or other query expressions to create complex conditions\n* Expressions like <code>in:</code> or <code>phrase:</code> can be used for full-text matches or unstructured searches, if the selected [https://www.semantic-mediawiki.org/wiki/Query_engine query engine] supports those expressions", "smw-facetedsearch-profile-label-default": "Default profile", "smw-facetedsearch-intro-tab-explore": "Explore", "smw-facetedsearch-intro-tab-search": "Search", "smw-facetedsearch-explore-intro": "Select a collection and start browsing.", "smw-facetedsearch-profile-options": "Profile options", "smw-facetedsearch-size-options": "Paging options", "smw-facetedsearch-order-options": "Order options", "smw-facetedsearch-format-options": "Display options", "smw-facetedsearch-format-table": "Table", "smw-facetedsearch-input-filter-placeholder": "Filter...", "smw-facetedsearch-no-filters": "No filters.", "smw-facetedsearch-no-filter-range": "No filter range.", "smw-facetedsearch-no-output": "For the selected \"$1\" format, no output was available.", "smw-facetedsearch-clear-filters": "Clear {{PLURAL:$1|filter|filters}}", "smw-specials-facetedsearch-helplink": "https://www.semantic-mediawiki.org/wiki/Help:Special:FacetedSearch", "smw-search-placeholder": "Search...", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "Showing below up to {{PLURAL:$1|<strong>1</strong> result|<strong>$1</strong> results}} starting with #<strong>$2</strong>."}