{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nemo bis", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Surfo", "Yekrats"]}, "smw-error": "<PERSON><PERSON>", "smw-upgrade-progress": "Progreso", "smw-upgrade-error-title": "<PERSON>ro (Semantika MediaVikio)", "smw_viewasrdf": "RDF-fonto", "smw_finallistconjunct": ", kaj", "smw-factbox-head": "… pli pri „$1”", "smw-factbox-facts": "<PERSON><PERSON><PERSON><PERSON>", "smw-factbox-attachments-is-local": "Ĉu estas loka", "smw-factbox-facts-derived": "Deriv<PERSON>j fak<PERSON>j", "smw_isspecprop": "Ĉi tiu atributo estas speciala atributo en ĉi tiu vikio.", "smw_concept_description": "Priskribo de koncepto \"$1\"", "smw_no_concept_namespace": "Konceptoj povas nur esti difinita en paĝoj en la nomspaco Concept:.", "smw_baduri": "<PERSON><PERSON><PERSON><PERSON><PERSON>, URI-oj de la kamparo \"$1\" ne estas permesita.", "smw_printername_csv": "CSV-elporto", "smw_printername_dsv": "DSV-elporto", "smw_printername_json": "JSON-elporto", "smw_printername_list": "Listo", "smw_printername_plainlist": "Simpla listo", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "Larĝa tabelo", "smw_printername_template": "Ŝablono", "smw_printername_templatefile": "Ŝablona dosiero", "smw_printername_rdf": "RDF-elporto", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "teksto", "smw-paramdesc-jsonsyntax": "Uzota JSON-sintakso", "smw-paramdesc-feedtype": "Speco de abonfluo", "smw_iq_disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>, semantikaj informmendoj estis malebligitaj por ĉi tiu vikio.", "smw_iq_moreresults": "… pluaj rezultoj", "smw_parseerror": "La donata valoro ne estas komprenita.", "smw_notitle": "\"$1\" ne eblas esti uzata kiel paĝnomo en ĉi tiu vikio.", "smw_manytypes": "Pli ol unu tipo estas difinita por atributo.", "smw_emptystring": "Malplenaj ĉenoj ne estas akceptitaj.", "smw_notinenum": "\"$1\" ne estas en la listo ($2) de [[Property:Allows value|eblaj valoroj]] por la atributo \"$3\".", "smw_noboolean": "\"$1\" ne estas agnoskita kiel Bulea (vera/falsa) valoro.", "smw_true_words": "vera,v,jes,j", "smw_false_words": "falsa,f,ne,n", "smw_nofloat": "\"$1\" ne estas nombro.", "smw_nodatetime": "La dato \"$1\" ne estis komprenita.", "smw_toomanyclosing": "Verŝajne estas tro da okazoj de \"$1\" en la mendo.", "smw_misplacedsymbol": "La signo \"$1\" estis uzita en loko kie ĝi ne estas utila.", "smw_emptysubquery": "<PERSON>u submendo havas neniun validan kondiĉon.", "smw_propvalueproblem": "La valoro de atributo \"$1\" ne estas komprenita.", "smw_type_header": "Atributoj de tipo \"$1\"", "smw_typearticlecount": "Montrante $1 {{PLURAL:$1|atributon|atributojn}} uzante ĉi tiun tipon.", "smw_attribute_header": "Paĝoj uzante la econ \"$1\"", "smw_attributearticlecount": "Jen $1 {{PLURAL:$1|paĝo havanta|paĝoj havantaj}} ĉi tiun atributon.", "smw-propertylist-subproperty-header": "Sub-ecoj", "smw-propertylist-redirect-header": "Sinonimoj", "exportrdf": "Elporti paĝojn al RDF", "smw_exportrdf_submit": "Elporti", "properties": "Atribut<PERSON>j", "smw-categories": "<PERSON><PERSON><PERSON><PERSON>", "smw_properties_docu": "La jenaj atributoj estas uzataj en la vikio.", "smw_property_template": "$1 de datumtipo $2 ($3 {{PLURAL:$3|apero|aperoj}})", "smw-special-property-searchform-options": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Filtrilo:", "smw-special-wantedproperties-filter-none": "Nenio", "smw-special-wantedproperties-filter-unapproved": "Neaprobita", "concepts": "Konceptoj", "smw-special-concept-header": "Listo de konceptoj", "unusedproperties": "Neuzitaj atributoj", "smw-unusedproperty-template": "$1 de datumtipo $2", "wantedproperties": "Volitaj atributoj", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|uzo|uzoj}})", "smw_purge": "Refreŝigi", "types": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_types_docu": "Jen listo de ĉiuj datentipoj kiu povas esti asignitaj al atributoj.\nĈiu datentipo havas paĝon kie plua informo povas esti aldonita.", "smw-statistics": "<PERSON><PERSON><PERSON><PERSON> statistiko", "smw-statistics-entities-total": "<PERSON><PERSON><PERSON> (totalo)", "ask": "Semantika serĉo", "smw_ask_sortby": "Ordigi <PERSON> kolumnoj (nedevige)", "smw_ask_ascorder": "Kreskante", "smw_ask_descorder": "Malkreskante", "smw-ask-order-rand": "<PERSON><PERSON><PERSON>", "smw_ask_submit": "<PERSON><PERSON><PERSON>", "smw_ask_editquery": "<PERSON><PERSON><PERSON> serĉomendon", "smw_add_sortcondition": "[<PERSON><PERSON>i ordigan kondiĉon]", "smw_ask_hidequery": "Kaŝi serĉmendon", "smw_ask_help": "Helpo pri serĉomendoj", "smw_ask_queryhead": "Serĉomendo", "smw_ask_format_as": "Formati kiel:", "smw_ask_defaultformat": "defa<PERSON><PERSON>", "smw_ask_otheroptions": "<PERSON><PERSON>", "smw_ask_show_embed": "<PERSON><PERSON> kodon", "smw_ask_hide_embed": "Kaŝi enkorpigitan kodon", "smw-ask-delete": "[<PERSON><PERSON>]", "smw-ask-sorting": "<PERSON><PERSON><PERSON>", "smw-ask-options": "<PERSON><PERSON><PERSON>", "smw-ask-options-sort": "Opcioj pri ordigado", "smw-ask-format-options": "Aranĝo kaj opcioj", "smw-ask-parameters": "Parametroj", "smw-ask-search": "Serĉi", "smw-ask-debug": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-result": "Re<PERSON>lto", "smw-ask-format": "Aranĝo", "smw-ask-input-assistance": "Helpo pri enigado", "smw-ask-extra-query-log": "Protokolo de informpetoj", "smw-ask-extra-other": "<PERSON><PERSON>", "searchbyproperty": "Serĉi laŭ atributo", "smw_sbv_property": "Atributo:", "smw_sbv_value": "Valoro:", "smw_sbv_submit": "<PERSON><PERSON><PERSON>", "browse": "<PERSON><PERSON><PERSON> vikion", "smw_browselink": "<PERSON><PERSON><PERSON><PERSON>", "smw_browse_article": "<PERSON><PERSON> la nomon de la paĝo por komenci retumadon.", "smw_browse_go": "Ek", "smw_browse_show_incoming": "malkaŝi atributojn kiu enligiĝas ĉi tien", "smw_browse_hide_incoming": "kaŝi atributojn kiu enligiĝas ĉi tien", "smw_browse_no_outgoing": "Ĉi tiu paĝo havas neniujn atributojn.", "smw_browse_no_incoming": "Neniuj atributojn ligas al ĉi tiun paĝon.", "smw-browse-show-group": "<PERSON><PERSON>", "smw-browse-hide-group": "Kaŝi grupojn", "smw_inverse_label_default": "$1 el", "smw_inverse_label_property": "Inversa atributa etikedo", "pageproperty": "Serĉo de paĝaj atributoj", "smw_pp_from": "<PERSON> paĝo", "smw_pp_type": "Atributo", "smw_pp_submit": "<PERSON><PERSON><PERSON>", "smw_result_prev": "Antaŭe", "smw_result_next": "Sekva", "smw_result_results": "Re<PERSON>lt<PERSON>j", "smw_result_noresults": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON> rezult<PERSON>j", "smwadmin": "Administradaj funkcioj por Semantic MediaWiki", "smw-admin-statistics-job-title": "Statistiko pri taskoj", "smw-admin-statistics-semanticdata-overview": "Superrigardo", "smw_smwadmin_return": "Reiri al $1", "smw-admin-db": "Datenbaza instalado kaj promociado", "smw-admin-announce": "<PERSON><PERSON><PERSON> vian vikion", "smw_smwadmin_datarefreshstop": "<PERSON><PERSON> ĉi tiun ĝisdatigon", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, {{GENDER:$1|mi certas}}.", "smw-admin-support": "Teni subtenon", "smw-admin-bugsreport": "<PERSON><PERSON>j povas esti raportitaj al <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>.", "smw-admin-statistics": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-mappings-summary": "Resumo", "smw-admin-supplementary-elastic-nodes-title": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-indices-title": "Indicoj", "smw-admin-supplementary-elastic-statistics-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-files": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-pages": "Paĝoj", "smw-admin-supplementary-elastic-endpoints": "Finpunktoj", "smw-property-label-similarity-threshold": "Sojlo:", "smw_adminlinks_datastructure": "Datumstrukturo", "smw-property-indicator-type-info": "{{PLURAL:$1|Uzanto|Sistemo}}", "smw-createproperty-isproperty": "Ĉi tiu estas atributo de speco $1.", "smw-createproperty-allowedvals": "La {{PLURAL:$1|permesita valoro por ĉi tiu atributo|permesitaj valoroj por ĉi tiuj atributoj}} estas:", "prefs-general-options": "Ĝeneralaj opcioj", "prefs-ask-options": "Opcioj pri semantika serĉado", "smw-ui-tooltip-title-property": "Eco", "smw-ui-tooltip-title-info": "Informo", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-error": "<PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Parametro", "smw-ui-tooltip-title-event": "Evento", "smw-ui-tooltip-title-note": "Noto", "smw-ui-tooltip-title-legend": "K<PERSON><PERSON>", "smw-ui-tooltip-title-reference": "Referenco", "smw_unknowntype": "Nesubtenita datumtipo \"$1\" difinita por atributo.", "smw_concept_header": "Paĝoj de koncepto \"$1\"", "group-smwcurator": "<PERSON><PERSON><PERSON><PERSON> (Semantika MediaVikio)", "smw-livepreview-loading": "Ŝarĝante...", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekundo|sekundoj}}", "smw-property-predefined-pvuc": "\"$1\" estas predifinita eco, provizita de [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantika Mediavikio] por restrikti valorajn atribuojn por ke ĉiu ekzemplero estas unika (aŭ maksimume ekzemplerigita unufoje).", "smw-datavalue-parse-error": "La donita valoro \"$1\" ne estis komprenita.", "smw-format-datatable-next": "Sekva", "smw-listingcontinuesabbrev": "<PERSON><PERSON><PERSON><PERSON>", "smw-showingresults": "Montras {{PLURAL:$1|'''1''' trovitan|'''$1''' trovitajn}} ekde la #'''$2'''-a."}