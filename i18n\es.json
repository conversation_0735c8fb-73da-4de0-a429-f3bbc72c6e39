{"@metadata": {"authors": ["AVIADOR71", "Alvaro<PERSON><PERSON><PERSON>", "Amaia", "Amitie 10g", "<PERSON><PERSON>", "ArenaL5", "<PERSON><PERSON><PERSON><PERSON>", "BaRaN6161 TURK", "Bfvale", "<PERSON><PERSON>", "Caleidoscopic", "Carlosmg.dg", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ciencia Al Poder", "Codename Noreste", "Crazymadlover", "DDPAT", "DannyS712", "Dgstranz", "Dvdgmz", "Felipe95a", "<PERSON><PERSON><PERSON><PERSON>", "Harvest", "<PERSON><PERSON>", "Ice bulldog", "Ihojose", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ivanhercaz", "JDíaz32", "Jack<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Joanmp17", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "KATRINE1992", "Lemondoge", "Locos epraix", "Luzcaru", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "MartaEgea", "MaxSem", "McDut<PERSON><PERSON>", "Nemo bis", "No se", "<PERSON><PERSON><PERSON>", "<PERSON>", "RicardoSGZ", "Ryo567", "Sanbec", "Sophivorus", "Spacebirdy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Translationista", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "아라"]}, "smw-desc": "Hace tu wiki más accesible para las máquinas y las personas ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentación en línea])", "smw-error": "Error", "smw-upgrade-progress": "Progreso", "smw-upgrade-error-title": "Error • Semantic MediaWiki", "smw-upgrade-error-why-title": "¿Por qué veo esta página?", "smw-upgrade-error-how-title": "¿Cómo corrijo este error?", "smw-extensionload-error-why-title": "¿Por qué veo esta página?", "smw-extensionload-error-how-title": "¿Cómo corrijo este error?", "smw-upgrade-maintenance-title": "Mantenimiento • Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "¿Por qué veo esta página?", "smw-semantics-not-enabled": "No se ha habilitado la funcionalidad de Semantic MediaWiki para esta wiki.", "smw_viewasrdf": "Feed RDF", "smw_finallistconjunct": "y", "smw-factbox-head": "… más sobre «$1»", "smw-factbox-facts": "Datos", "smw-factbox-facts-help": "Mostrar declaraciones y hechos que han sido creados por un usuario", "smw-factbox-attachments": "<PERSON><PERSON><PERSON>", "smw-factbox-attachments-value-unknown": "N/D", "smw-factbox-attachments-is-local": "Es local", "smw-factbox-facts-derived": "<PERSON><PERSON> derivados", "smw-factbox-facts-derived-help": "Mostrar datos que han sido derivados de reglas o con la ayuda de otras técnicas de razonamiento", "smw_isspecprop": "Esta propiedad es una propiedad especial en esta wiki.", "smw-concept-cache-header": "Uso de antememoria", "smw-concept-cache-count": "La [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count antememoria de conceptos] contiene {{PLURAL:$1|'''una''' entidad|'''$1''' entidades}} ($2).", "smw-concept-no-cache": "No hay antememoria disponible.", "smw_concept_description": "Descripción del concepto «$1»", "smw_no_concept_namespace": "Los conceptos solo pueden definirse en páginas del espacio de nombres Concepto:.", "smw_multiple_concepts": "Cada página de concepto solamente puede tener una definición de concepto.", "smw_concept_cache_miss": "El concepto «$1» no puede ser utilizado en este momento, ya que la configuración de la wiki exige que sea computado sin conexión.\nSi el problema persiste después de pasado algún tiempo, solicita al administrador de tu sitio que haga que este concepto esté disponible.", "smw_noinvannot": "Los valores no pueden asignarse a propiedades inversas.", "version-semantic": "Extensiones semánticas", "smw_baduri": "Las URIs con la forma «$1» no están permitidas.", "smw_printername_count": "Contar resultados", "smw_printername_csv": "Exportar a CSV", "smw_printername_dsv": "Exportar a DSV", "smw_printername_debug": "<PERSON><PERSON><PERSON> consulta (para expertos)", "smw_printername_embedded": "Incrustar el contenido de la página", "smw_printername_json": "Exportar a JSON", "smw_printername_list": "Lista", "smw_printername_plainlist": "Lista plana", "smw_printername_ol": "Lista numerada", "smw_printername_ul": "Lista viñetada", "smw_printername_table": "Tabla", "smw_printername_broadtable": "Tabla ancha", "smw_printername_template": "Plantilla", "smw_printername_templatefile": "Archivo de plantilla", "smw_printername_rdf": "Exportar a RDF", "smw_printername_category": "Categoría", "validator-type-class-SMWParamSource": "texto", "smw-paramdesc-limit": "La cantidad máxima de resultados a devolver", "smw-paramdesc-offset": "El desplazamiento del primer resultado", "smw-paramdesc-headers": "Muestra los nombres de encabezados/propiedades", "smw-paramdesc-mainlabel": "La etiqueta a asignar al nombre de la página principal", "smw-paramdesc-link": "Muestra los valores como enlaces", "smw-paramdesc-intro": "Si lo hubiese, sería el texto que se muestra antes de los resultados de la consulta", "smw-paramdesc-outro": "Si lo hubiese, sería el texto que se muestra después de los resultados de la consulta", "smw-paramdesc-default": "El texto que mostrar si no hay resultados de consulta", "smw-paramdesc-sep": "El separador entre resultados", "smw-paramdesc-propsep": "El separator entre las propiedades de una entrada de resultado", "smw-paramdesc-valuesep": "El separator entre los valores para una propiedad de un resultado", "smw-paramdesc-showsep": "Muestra el separador al comienzo del archivo CSV («sep=<valor>»)", "smw-paramdesc-distribution": "Cuenta sus ocurrencias y las muestra, en lugar de mostrar todos los valores.", "smw-paramdesc-distributionsort": "Ordena la distribución de valores por número de ocurrencias.", "smw-paramdesc-distributionlimit": "Limita la distribución de valores solamente al recuento de algunos de valores.", "smw-paramdesc-aggregation": "Especifica con qué debe relacionarse el conjunto", "smw-paramdesc-template": "El nombre de la plantilla con la que se mostrará el listado", "smw-paramdesc-columns": "El número de columnas en las que se muestran los resultados", "smw-paramdesc-userparam": "Si se utiliza una plantilla, es un valor pasado en cada llamada de la plantilla", "smw-paramdesc-class": "Una clase adicional de CSS a establecer para la lista", "smw-paramdesc-introtemplate": "Si hay alguna plantilla, es el nombre de la plantilla a mostrar antes de los resultados de la consulta", "smw-paramdesc-outrotemplate": "Si hay alguna plantilla, es el nombre de una plantilla a mostrar después de los resultados de la consulta", "smw-paramdesc-embedformat": "La etiqueta HTML utilizada para definir títulos", "smw-paramdesc-embedonly": "No mostrar ningún título", "smw-paramdesc-table-class": "Una clase CSS adicional a establecer para la tabla", "smw-paramdesc-table-transpose": "Mostrar los encabezados de tabla verticalmente y los resultados horizontalmente", "smw-paramdesc-prefix": "Controlar la visualización del espacio de nombres en las impresiones", "smw-paramdesc-rdfsyntax": "La sintaxis de RDF a usar", "smw-paramdesc-csv-sep": "Especifica un separador de columnas", "smw-paramdesc-csv-valuesep": "Especifica un separador de valores", "smw-paramdesc-csv-merge": "Fusionar filas y valores de columnas con el mismo identificador de sujeto (aka primera columna)", "smw-paramdesc-csv-bom": "Añadir un BOM (carácter para indicar la orientación «endian») en la parte superior del archivo de salida", "smw-paramdesc-dsv-separator": "El separador a usar", "smw-paramdesc-dsv-filename": "El nombre para el archivo DSV", "smw-paramdesc-filename": "Nombre del archivo de salida", "smw-smwdoc-description": "Muestra una tabla de todos los parámetros que se pueden usar para un formato de resultados especificado junto a los valores por defecto y las descripciones.", "smw-smwdoc-default-no-parameter-list": "Este formato de resultado no brinda parámetros específicos de formato.", "smw-smwdoc-par-format": "El formato de resultados en el que mostrar la documentación de un parámetro.", "smw-smwdoc-par-parameters": "Los parámetros que mostrar. \"specific\" para los añadidos por el formato, \"base\" para los disponibles en todos los formatos, y \"all\" para ambos.", "smw-paramdesc-sort": "Propiedad a partir de la que ordenar la petición", "smw-paramdesc-order": "Orden de ordenación de la consulta", "smw-paramdesc-searchlabel": "Texto para continuar la búsqueda", "smw-paramdesc-named_args": "Nombre los argumentos que se pasan a la plantilla", "smw-paramdesc-template-arguments": "Establece cómo se pasan los argumentos con nombre a la plantilla", "smw-paramdesc-import-annotation": "Los datos anotados adicionales se deben copiar durante el análisis de un tema", "smw-paramdesc-export": "Opción de exportación", "smw-paramdesc-prettyprint": "Una salida con formato agradable que muestra sangrías y saltos de línea adicionales", "smw-paramdesc-json-unescape": "La salida contendrá barras sin secuencias de escape y caracteres Unicode de varios bytes", "smw-paramdesc-json-type": "Tipo de serialización", "smw-paramdesc-source": "Fuente de consulta alternativa", "smw-paramdesc-jsonsyntax": "Sintaxis JSON que utilizar", "smw-printername-feed": "Suministro RSS o Atom", "smw-paramdesc-feedtype": "Tipo de fuente de noticias", "smw-paramdesc-feedtitle": "El texto que se empleará como título del suministro", "smw-paramdesc-feeddescription": "El texto que se utilizará como descripción del suministro", "smw-paramdesc-feedpagecontent": "Contenido de página que se mostrará con el suministro", "smw-label-feed-description": "Fuente de noticias $1 $2", "smw-paramdesc-mimetype": "El tipo de medio (MIME) del archivo de salida", "smw_iq_disabled": "Se han desactivado las consultas semánticas en esta wiki.", "smw_iq_moreresults": "... más resultados", "smw_parseerror": "No se comprendió el valor provisto.", "smw_decseparator": ",", "smw_kiloseparator": "&nbsp;", "smw_notitle": "\"$1\" no puede usarse como un nombre de página en esta wiki.", "smw_noproperty": "\"$1\" no puede usarse como un nombre de propiedad en esta wiki.", "smw_wrong_namespace": "Aquí se permiten solo las páginas en el espacio de nombres \"$1\".", "smw_manytypes": "Más de un tipo definido para la propiedad.", "smw_emptystring": "No se aceptan cadenas vacías.", "smw_notinenum": "«$1» no se encuentra en la lista ($2) de [[Property:Allows value|valores permitidos]] de la propiedad «$3».", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" no está dentro del rango de \"$2\" especificado por la restricción [[Property:Allows value|permite el valor]] para la propiedad \"$3\".", "smw-constraint-error-suggestions": "Por favor, compruebas las violaciones y propiedades junto con sus valores anotados para asegurar que se cumplen todos los requisitos de la restricción.", "smw_noboolean": "\"$1\" no es reconocido como un valor booleano (verdadero/falso).", "smw_true_words": "verdadero,t,si,s,true", "smw_false_words": "falso,f,no,n,false", "smw_nofloat": "«$1» no es un número.", "smw_infinite": "No se admiten números del tamaño de «$1».", "smw_unitnotallowed": "\"$1\" no está declarada como una unidad de medida válida para esta propiedad.", "smw_nounitsdeclared": "No hay unidades de medida declaradas para esta propiedad.", "smw_novalues": "No se han especificado valores.", "smw_nodatetime": "La fecha «$1» no ha sido comprendida.", "smw_toomanyclosing": "Parece haber demasiadas coincidencias de \"$1\" en la consulta.", "smw_noclosingbrackets": "Algún uso de \"<nowiki>[[</nowiki>\" en tu consulta no se cerró con los correspondientes \"]]\".", "smw_misplacedsymbol": "El símbolo \"$1\" se ha usado en un lugar donde no es de utilidad.", "smw_unexpectedpart": "La parte \"$1\" de la consulta no fue entendida.\nLos resultados podrían no ser los esperados.", "smw_emptysubquery": "Alguna subconsulta no tiene condición válida.", "smw_misplacedsubquery": "Alguna subconsulta fue usada en un lugar donde las subconsultas no están permitidas.", "smw_valuesubquery": "Los valores de la propiedad «$1» no admiten las subconsultas.", "smw_badqueryatom": "Alguna parte \"<nowiki>[[...]]</nowiki>\" de la consulta no fue entendida.", "smw_propvalueproblem": "El valor de propiedad \"$1\" no fue entendida.", "smw_noqueryfeature": "Alguna característica de la consulta no se admite en esta wiki, y parte de ella se ha eliminado ($1).", "smw_noconjunctions": "No se admiten las conjunciones en las consultas en esta wiki y por ello se ha eliminado parte de la consulta ($1).", "smw_nodisjunctions": "Las disjunciones en las consultas no se admiten en esta wiki y parte de la consulta se ha eliminado ($1).", "smw_querytoolarge": "No se {{PLURAL:$2|pudo|pudieron}} considerar {{PLURAL:$2|la siguiente condición|las siguientes $2 condiciones}} de consulta debido a restricciones en esta wiki de tamaño o profundidad de consulta: <code>$1</code>.", "smw_notemplategiven": "Proveer un valor para el parámetro \"plantilla\" para este formato de consulta para funcionar.", "smw_db_sparqlqueryproblem": "No se pudieron obtener los resultados de la consulta de la base de datos SPARQL. Este error puede ser temporal o indicar un error en el programa de la base de datos.", "smw_db_sparqlqueryincomplete": "Resultó demasiado difícil responder la consulta y fue abandonada. Es probable que falten algunos resultados. Si fuese posible, intente hacer una consulta más sencilla.", "smw_type_header": "Propiedades de tipo \"$1\"", "smw_typearticlecount": "Se {{PLURAL:$1|muestra $1 propiedad que usa|muestran $1 propiedades que usan}} este tipo.", "smw_attribute_header": "Páginas que usan la propiedad \"$1\"", "smw_attributearticlecount": "Se {{PLURAL:$1|muestra $1 página que usa|muestran $1 páginas que usan}} esta propiedad.", "smw-propertylist-subproperty-header": "Subpropiedades", "smw-propertylist-redirect-header": "Sinónimos", "smw-propertylist-error-header": "Páginas con asignaciones no apropiadas", "smw-propertylist-count": "Se {{PLURAL:$1|muestra $1 propiedad relacionada|muestran $1 propiedades relacionadas}}.", "smw-propertylist-count-with-restricted-note": "Se {{PLURAL:$1|muestra $1 entidad relacionada|muestran $1 entidades relacionadas}}. Hay más disponibles pero la salida está restringida a \"$2\".", "smw-propertylist-count-more-available": "Se {{PLURAL:$1|muestra $1 entidad relacionada|muestran $1 entidades relacionadas}} (hay más disponibles).", "specialpages-group-smw_group": "Semantic MediaWiki", "specialpages-group-smw_group-maintenance": "Mantenimiento", "specialpages-group-smw_group-search": "Explorar y buscar", "exportrdf": "Exportar páginas a RDF", "smw_exportrdf_docu": "Esta página permite obtener datos de una página en formato RDF.\nPara exportar páginas, escribe los títulos en el cuadro de abajo, uno por línea.", "smw_exportrdf_recursive": "Exportar igualmente todas las páginas pertinentes de forma recurrente. Esta posibilidad puede conseguir un gran número de resultados !", "smw_exportrdf_backlinks": "Exportar igualmente todas las páginas que reenvían a páginas exportadas. Resulta un RDF en el que se facilita la navegación.", "smw_exportrdf_lastdate": "No exportar páginas que no fueron cambiadas desde el punto dado en el tiempo.", "smw_exportrdf_submit": "Exportar", "uriresolver": "Traductor de URI", "properties": "Propiedades", "smw-categories": "Categorías", "smw_properties_docu": "Las siguientes propiedades se usan en la wiki.", "smw_property_template": "$1 de tipo $2 ($3 {{PLURAL:$3|uso|usos}})", "smw_propertylackspage": "Todas las propiedades deberían describirse por una página.", "smw_propertylackstype": "Ningún tipo fue especificada para esta propiedad (asumiendo tipo $1 por ahora).", "smw_propertyhardlyused": "¡Esta propiedad se usa escasamente dentro de la wiki!", "smw-property-name-invalid": "La propiedad $1 no puede usarse (nombre de propiedad no válido).", "smw-property-name-reserved": "«$1» se marcó como nombre reservado y, por lo tanto, no debería utilizarse como una propiedad. Es posible que esta [https://www.semantic-mediawiki.org/wiki/Help:Property_naming página de ayuda] incluya información sobre el motivo por el que este nombre se reservó.", "smw-sp-property-searchform": "<PERSON><PERSON> propiedades que contengan:", "smw-sp-property-searchform-inputinfo": "La entrada distingue mayúsculas y minúsculas y, cuando se utiliza para el filtrado, solo se muestran las propiedades que coinciden con la condición.", "smw-special-property-searchform": "<PERSON><PERSON> propiedades que contengan:", "smw-special-property-searchform-inputinfo": "Se hace distinción de mayúsculas y minúsculas en la entrada. Cuando esta se emplee para filtrar, únicamente aparecerán las propiedades que coincidan con la condición.", "smw-special-property-searchform-options": "Opciones", "smw-special-wantedproperties-filter-label": "Filtro:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "Sin aprobar", "smw-special-wantedproperties-filter-unapproved-desc": "La opción de filtrado se utiliza en conjunto con el modo de autoridad.", "concepts": "Conceptos", "smw-special-concept-docu": "Un [https://www.semantic-mediawiki.org/wiki/Help:Concepts concepto] puede verse como una \"categoría dinámica\". Es decir, como una colección de páginas que no se crean manualmente, sino que se calculan a través de Semantic MediaWiki a partir de una descripción de una consulta dada.", "smw-special-concept-header": "Lista de conceptos", "smw-special-concept-count": "Se {{PLURAL:$1|lista el siguiente concepto|listan los siguientes $1 conceptos}}.", "smw-special-concept-empty": "No se encontró ningún concepto.", "unusedproperties": "Propiedades no utilizadas", "smw-unusedproperties-docu": "Esta página lista las [https://www.semantic-mediawiki.org/wiki/Unused_properties propiedades no utilizadas] que se declaran aunque ninguna otra página hace uso de ellos. Para una visión diferenciada, vea [[Special:Properties|todo]] o la página especial de [[Special:WantedProperties|propiedades buscadas]].", "smw-unusedproperty-template": "$1 de tipo $2", "wantedproperties": "Propiedades requeridas", "smw-wantedproperties-docu": "Esta página lista las [https://www.semantic-mediawiki.org/wiki/Wanted_properties propiedades buscadas] que se utilizan en la wiki pero no tienen una página que las describa. Para una visión diferenciada, vea [[Special:Properties|todo]] o la página especial de [[Special:UnusedProperties|propiedades no utilizadas]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw-special-wantedproperties-docu": "Esta página enumera las [https://www.semantic-mediawiki.org/wiki/Wanted_properties propiedades solicitadas] que se emplean en la wiki pero que no cuentan con una página descriptora. Para obtener una visualización diferenciada, consulta las listas de [[Special:Properties|todas las propiedades]] y de las [[Special:UnusedProperties|propiedades no utilizadas]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|uso|usos}})", "smw_purge": "Actualizar", "smw-purge-failed": "Semantic MediaWiki intentó purgar la página pero falló", "types": "Tipos", "smw_types_docu": "Lista de [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tipos de datos disponibles] donde cada [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipo] representa un conjunto único de atributos para describir un valor en términos de almacenamiento y características de muestra que son hereditarios a una propiedad asignada.", "smw-special-types-no-such-type": "El tipo de datos especificado no existe", "smw-statistics": "Estadísticas de semántica", "smw-statistics-entities-total": "Entidades (totales)", "smw-statistics-property-instance": "{{PLURAL:$1|Valor|Valores}} de propiedad (total)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Propiedad|Propiedades}}]] (total)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Propiedad|Propiedades}} (total)", "smw-statistics-property-used": "{{PLURAL:$1|Propiedad|Propiedades}} (utilizado con al menos un valor)", "smw-statistics-property-page": "{{PLURAL:$1|Propiedad (registrada con una página)|Propiedades (registradas con una página)}}", "smw-statistics-property-type": "{{PLURAL:$1|Propiedad (asignada a un tipo de datos)|Propiedades (asignadas a un tipo de datos)}}", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Consulta|Consultas}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Consulta|Consultas}}]]", "smw-statistics-query-size": "Tamaño de la consulta", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Concepto|Conceptos}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Concepto|Conceptos}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Subobjeto|Subobjetos}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Subobjeto|Subobjetos}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Tipo de datos|Tipos de datos}}]]", "smw-statistics-error-count": "{{PLURAL:$1|<PERSON>or|<PERSON>ores}} de propiedad ([[Special:ProcessingErrorList|{{PLURAL:$1|anotación impropia|anotaciones impropias}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|<PERSON>or|Valores}} de propiedad ({{PLURAL:$1|anotación impropia|anotaciones impropias}})", "smw-statistics-delete-count": "{{PLURAL:$1|Entidad desactualizada (marcada para su eliminación)|Entidades desactualizadas (marcadas para su eliminación)}}", "smw_uri_doc": "El traductor de URI implementa [$1 W3C TAG finding on httpRange-14].\nEsto se preocupa de cosas que los humanos no lo hacen en los sitios web..", "ask": "Búsqueda semántica", "smw-ask-help": "Esta sección contiene algunos enlaces para ayudar a explicar cómo utilizar  la sintaxis <code>#ask</code>.\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selección de páginas] describe cómo seleccionar páginas y construir condiciones.\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadores de búsqueda] enumera los operadores de búsqueda disponibles, incluidos los de consulta de intervalo y de caracteres comodines.\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Muestra de información] expone el uso de las declaraciones de salida y las opciones de formato.", "smw_ask_sortby": "Ordenar por columna (opcional)", "smw_ask_ascorder": "Ascendente", "smw_ask_descorder": "Descendente", "smw-ask-order-rand": "Al azar", "smw_ask_submit": "Buscar resultados", "smw_ask_editquery": "Editar consulta", "smw_add_sortcondition": "[Agregar condición de orden]", "smw-ask-sort-add-action": "Añadir condición de ordenamiento", "smw_ask_hidequery": "Ocultar consulta (vista compacta)", "smw_ask_help": "Ayuda sobre consultas", "smw_ask_queryhead": "Condición", "smw_ask_printhead": "Selección de datos para mostrar", "smw_ask_printdesc": "(añade un nombre de propiedad por línea)", "smw_ask_format_as": "Formatear como:", "smw_ask_defaultformat": "predeterminado", "smw_ask_otheroptions": "Otras opciones", "smw-ask-otheroptions-info": "Esta sección contiene opciones que alteran los formatos de impresión. Las descripciones de dichas opciones pueden verse pasando el ratón sobre ellas.", "smw-ask-otheroptions-collapsed-info": "Usa el icono de suma para ver todas las opciones disponibles", "smw_ask_show_embed": "Mostrar código embebido", "smw_ask_hide_embed": "Ocultar c<PERSON><PERSON> embebido", "smw_ask_embed_instr": "Para incluír esta consulta en línea dentro de una wiki usa el código siguiente:", "smw-ask-delete": "<PERSON><PERSON><PERSON>", "smw-ask-sorting": "Ordenación", "smw-ask-options": "Opciones", "smw-ask-options-sort": "Opciones de ordenamiento", "smw-ask-format-options": "Formato y opciones", "smw-ask-parameters": "Parámetros", "smw-ask-search": "Buscar", "smw-ask-debug": "Depuración", "smw-ask-debug-desc": "Genera información para depurar consultas", "smw-ask-no-cache": "Deshabilitar la caché de consultas", "smw-ask-no-cache-desc": "Resultados sin antememoria de consulta", "smw-ask-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-empty": "Borrar todas las entradas", "smw-ask-download-link-desc": "Descargar los resultados de la consulta en el formato $1", "smw-ask-format": "Formato", "smw-ask-format-selection-help": "Ayuda con el formato seleccionado: $1", "smw-ask-condition-change-info": "La condición se alteró y el motor de búsqueda debe volver a ejecutar la consulta para producir resultados que coincidan con los nuevos requisitos.", "smw-ask-input-assistance": "Asistencia de entrada", "smw-ask-condition-input-assistance": "Se brinda [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance asistencia de entrada] para los campos de salida, de ordenación y de condición. Para utilizar este último, es necesario emplear alguno de estos prefijos:", "smw-ask-condition-input-assistance-property": "<code>p:</code> para buscar sugerencias de propiedades (p. ej., <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> para buscar sugerencias de categorías", "smw-ask-condition-input-assistance-concept": "<code>con:</code> para buscar sugerencias de conceptos", "smw-ask-format-change-info": "Se modificó el formato y, por tanto, es necesario ejecutar de nuevo la consulta para que corresponda con los parámetros y las opciones de visualización nuevos.", "smw-ask-format-export-info": "El formato de exportación seleccionado no tiene representación visual, por lo que los resultados se proporcionan solamente para descargar.", "smw-ask-query-search-info": "{{PLURAL:$3|1=<code>$2</code> (de la caché)|<code>$2</code> (de la caché)|<code>$2</code>}} respondió la consulta <code><nowiki>$1</nowiki></code> en $4 {{PLURAL:$4|segundo|segundos}}.", "smw-ask-extra-query-log": "Registro de consultas", "searchbyproperty": "Buscar por propiedad", "processingerrorlist": "Lista de errores de procesamiento", "constrainterrorlist": "Lista de errores de restricción", "propertylabelsimilarity": "Informe de similitud de etiquetas de propiedades", "smw-processingerrorlist-intro": "La siguiente lista proporciona una descripción general de los errores de procesamiento que aparecieron con [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Se recomienda monitorear esta lista de forma regular y corregir las anotaciones de valores no válidos.", "smw-constrainterrorlist-intro": "La siguiente lista proporciona una visión general sobre los [https://www.semantic-mediawiki.org/wiki/Constraint_errors errores de restricción] que aparecen conectados con [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Se recomienda supervisar esta lista regularmente y corregir los valores inválidos de las anotaciones.", "smw_sbv_docu": "Buscar todas las páginas que tienen una propiedad y un valor determinados.", "smw_sbv_novalue": "Escribe un valor válido para la propiedad, o ve todos los valores de propiedad para \"$1\".", "smw_sbv_displayresultfuzzy": "Lista de todas las páginas que tienen la propiedad \"$1\" con valor \"$2\".\nComo hay pocos resultados, también se muestran los valores aproximados.", "smw_sbv_property": "Propiedad:", "smw_sbv_value": "Valor:", "smw_sbv_submit": "Buscar resultados", "browse": "Explorar la wiki", "smw_browselink": "Explorar propiedades", "smw_browse_article": "Escribe el nombre de la página desde donde comenzar a navegar.", "smw_browse_go": "<PERSON>r", "smw_browse_show_incoming": "<PERSON><PERSON> propiedades entrantes", "smw_browse_hide_incoming": "<PERSON><PERSON><PERSON><PERSON> propiedades entrantes", "smw_browse_no_outgoing": "Esta página no tiene propiedades.", "smw_browse_no_incoming": "<PERSON>ng<PERSON> propiedad vincula aquí.", "smw-browse-from-backend": "Se está recuperando la información del motor.", "smw-browse-intro": "Esta página proporciona detalles sobre un sujeto o una instancia de entidad, ingrese el nombre de un objeto a inspeccionar.", "smw-browse-invalid-subject": "La validación del tema devolvió el error \"$1\".", "smw-browse-api-subject-serialization-invalid": "El tema tiene un formato de serialización no válido.", "smw-browse-js-disabled": "Es probable que JavaScript esté desactivado o no esté disponible y recomendamos utilizar un navegador que sea compatible. En la página de configuración [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi $smwgBrowseByApi] se discuten otras opciones.", "smw-browse-show-group": "Mostrar grupos", "smw-browse-hide-group": "Ocultar grupos", "smw-noscript": "Esta pagina o accion necesita Javascript para funcionar, por favor activar el Javascript en su navigador o usar un navigador donde esto es suportado, asi puede funcionar bien y asi es suministrado como se ha solicitado. Para mas ayuda, por favor ver el enlace siguiente en la pagina de ayuda.  [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript] .", "smw_inverse_label_default": "$1 de", "smw_inverse_label_property": "Etiqueta de propiedad inversa", "pageproperty": "Búsqueda de propiedades de página", "pendingtasklist": "Lista de tareas pendientes", "smw_pp_docu": "Proporciona una página y propiedad o solo una propiedad para recuperar todos los valores asignados.", "smw_pp_from": "Desde la página:", "smw_pp_type": "Propiedad:", "smw_pp_submit": "Encontrar resultados", "smw-prev": "$1 anterior{{PLURAL:$1||es}}", "smw-next": "$1 siguiente{{PLURAL:$1||s}}", "smw_result_prev": "Anterior", "smw_result_next": "Siguient<PERSON>", "smw_result_results": "Resul<PERSON><PERSON>", "smw_result_noresults": "No hay resultados.", "smwadmin": "Tablero (Semantic MediaWiki)", "smw-admin-statistics-job-title": "Estadísticas de trabajo", "smw-admin-statistics-job-docu": "Las estadísticas de trabajo muestran información acerca de los trabajos para Semantic MediaWiki programados que aún no se han ejecutado. El número de trabajos puede ser ligeramente inexacto o contener intentos fallidos, consulte este [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manual] para información detallada.", "smw-admin-statistics-querycache-title": "Antememoria de consultas", "smw-admin-statistics-querycache-disabled": "El [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] no ha sido habilitado en esta wiki y por lo tanto no hay estadísticas disponibles.", "smw-admin-permission-missing": "Debido a la falta de los permisos necesarios, se ha bloqueado el acceso a esta página. Consulta la página de ayuda sobre los [https://www.semantic-mediawiki.org/wiki/Help:Permissions permisos] para obtener más información sobre las configuraciones necesarias.", "smw-admin-setupsuccess": "El motor de almacenamiento se ha configurado.", "smw_smwadmin_return": "Regresar a $1", "smw_smwadmin_updatestarted": "Se ha iniciado un proceso de actualización nuevo para actualizar los datos semánticos.\nTodos los datos almacenados se reconstruirán o repararán donde haga falta.\nPuedes hacer un seguimiento del progreso de la actualización en esta página especial.", "smw_smwadmin_updatenotstarted": "Ya hay un proceso de actualización ejecutándose.\nNo se creará otro.", "smw_smwadmin_updatestopped": "Se han detenido todos los procesos de actualización existentes.", "smw_smwadmin_updatenotstopped": "Para detener el proceso de actualización en ejecución, debes marcar la casilla para indicar que estás realmente seguro.", "smw-admin-docu": "Esta página especial te ayudará durante la instalación, actualización, mantenimiento y uso de <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a>, además de proporcionar funciones administrativas y tareas, así como estadísticas.\n\nRecuerda respaldar cualquier información importante antes de ejecutar funciones administrativas.", "smw-admin-environment": "Entorno de software", "smw-admin-db": "Configuración de la base de datos", "smw-admin-db-preparation": "La inicialización de la tabla está en curso y se puede producir una demora antes de que se muestren los resultados, en función del tamaño y las optimizaciones que deban realizarse.", "smw-admin-dbdocu": "Semantic MediaWiki necesita algunas extensiones en la base de datos de MediaWiki para poder almacenar la información semántica.\nLa función indicada abajo asegura que tu base de datos está configurada correctamente.\nLos cambios realizados en este paso no afectan el resto de la base de datos de MediaWiki y pueden ser revertidos fácilmente si así se desea.\nEsta función de configuración puede ejecutarse varias veces sin hacer ningún daño, pero es necesaria sólo una vez para la instalación o actualización.", "smw-admin-permissionswarn": "Si la operación falla con errores SQL, la base de datos de usuarios empleada por su wiki (revise su archivo \"LocalSettings.php\") probablemente no tiene permisos suficientes.\nUd puede conceder a este usuario permisos adicionales para crear y borrar tablas, ingresando temporalmente la clave de acceso de su base de datos raíz en el archivo \"LocalSettings.php\", o bien usar el script de mantenimiento <code>setupStore.php</code>, que puede usar las credenciales de un administrador.", "smw-admin-dbbutton": "Inicializar o modernizar tablas", "smw-admin-announce": "Anuncia tu wiki", "smw-admin-announce-text": "Si tu wiki es pública, lo puedes registrar en <a href=\"https://wikiapiary.com\">WikiApiary</a>, la wiki de seguimiento de wikis.", "smw-admin-deprecation-notice-title": "Alertas de obsolescencia", "smw-admin-deprecation-notice-docu": "La siguiente sección contiene ajustes que se han quedado obsoletos o eliminados, pero que fueron detectados para estar activos en esta wiki. Se espera que en el futuro cualquier versión elimine el soporte para estas configuraciones.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> está obsoleto y se eliminará en $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> eliminar<PERSON> (o reemplazará) {{PLURAL:$2|la opción siguiente|las opciones siguientes}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> está desusado y se eliminará en la versión $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> fue reemplazado por <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> tiene {{PLURAL:$2|esta opción|estas opciones}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> se reemplazará por <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> fue eliminado en $2", "smw-admin-deprecation-notice-title-notice": "Configuraciones obsoletadas", "smw-admin-deprecation-notice-title-notice-explanation": "Los siguientes ajustes se detectaron para ser usados en esta wiki y está planeado que sean eliminados o cambiados en versiones futuras.", "smw-admin-deprecation-notice-title-replacement": "Opciones sustituidas o retituladas", "smw-admin-deprecation-notice-title-replacement-explanation": "La sección siguiente contiene opciones cuyo nombre se ha modificado o han recibido cambios de otro tipo. Se recomienda que se actualice inmediatamente su nombre o su formato.", "smw-admin-deprecation-notice-title-removal": "Opciones eliminadas", "smw-admin-deprecation-notice-title-removal-explanation": "Las opciones siguientes se eliminaron en una versión anterior, pero se detectó que aún se utilizan en esta wiki.", "smw-smwadmin-refresh-title": "Reparación y actualización de datos", "smw_smwadmin_datarefresh": "Reconstrucción de los datos", "smw_smwadmin_datarefreshdocu": "Es posible restaurar toda la información de MediaWiki Semántica basándose en los contenidos actuales de la wiki.\nEsto puede ser útil para reparar la información destruida o para volver a cargar la información si el formato interno ha cambiado a causa de alguna actualización de software.\nLa actualización se ejecuta página a página y no se completará de inmediato.\nLo siguiente muestra es hay una actualización en progreso y le permite iniciar o detener actualizaciones (a menos que esta característica haya sido inhabilitada por el administrador del sitio).", "smw_smwadmin_datarefreshprogress": "<strong>Actualización en progreso.</strong>\nEs normal que el proceso de actualización se retrase ya que sólo actualiza datos en porciones pequeñas cada vez que un usuario ingresa a la wiki.\nPara finalizar esta actualización en forma más rápida, puedes invocar el script de matenimiento MediaWiki <code>runJobs.php</code> (usa la opción <code>--maxjobs 1000</code>  para restringir el número de actualizaciones hechas en cada paquete).\nProgreso estimado de la actualización:", "smw_smwadmin_datarefreshbutton": "Programar actualización de datos", "smw_smwadmin_datarefreshstop": "Detener esta actualización", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, estoy {{GENDER:$1|seguro|segura}}.", "smw-admin-job-scheduler-note": "La mayoría de las actividades de esta sección se realizan como trabajos para evitar situaciones de bloqueo durante su ejecución. El [https://www.mediawiki.org/wiki/Manual:Job_queue planificador de trabajos] es responsable del procesamiento y es fundamental que el script de mantenimiento <code>runJobs.php</code> (véase también el parámetro de configuración <code>$wgRunJobsAsync</code>) tenga la capacidad adecuada.", "smw-admin-outdateddisposal-title": "Eliminación de entidades obsoletas", "smw-admin-outdateddisposal-intro": "Algunas actividades (un cambio a un tipo de propiedad, la eliminación de wikipáginas o la corrección de valores de error) resultarán en [https://www.semantic-mediawiki.org/wiki/Outdated_entities entidades anticuadas] y se sugiere eliminarlas periódicamente para liberar el espacio de tablas asociado.", "smw-admin-outdateddisposal-active": "Se ha puesto en cola un trabajo de eliminación de entidades obsoletas.", "smw-admin-outdateddisposal-button": "Programar una eliminación", "smw-admin-feature-disabled": "Se ha deshabilitado esta función en esta wiki. Para más información, consulta la página de <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">configuración</a> o contacta con el administrador del sistema.", "smw-admin-propertystatistics-title": "Reconstruir las estadísticas de la propiedad", "smw-admin-propertystatistics-intro": "Reconstruye todas las estadísticas de uso de la propiedad y actualiza y corrige el [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count  recuento de uso] de propiedades.", "smw-admin-propertystatistics-active": "Se ha programado una reconstrucción de estadísticas.", "smw-admin-propertystatistics-button": "Programar reconstrucción de estadísticas", "smw-admin-fulltext-title": "Reconstrucción de búsqueda de texto completo", "smw-admin-fulltext-intro": "Recompila el índice de búsquedas a partir de tablas de propiedades mediante un tipo de datos para [https://www.semantic-mediawiki.org/wiki/Full-text búsquedas de texto completo] que se haya activado. Es necesario ejecutar esta tarea de nuevo si se producen modificaciones en las reglas de indización (cambios de palabras no significativas, un lematizador nuevo, etc.) o se añade o modifica una tabla.", "smw-admin-fulltext-active": "Se ha programado una tarea de reconstrucción para búsquedas de texto completo.", "smw-admin-fulltext-button": "Programar reconstrucción de texto completo", "smw-admin-support": "Obtener asistencia", "smw-admin-supportdocu": "Se ofrecen algunos recursos para ayudarte si se producen problemas:", "smw-admin-installfile": "Si experimentas problemas con tu instalación, comienza revisando las directivas en el <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">archivo INSTALL</a>.", "smw-admin-smwhomepage": "Puede encontrarse la documentación de usuario completa de Semantic MediaWiki en <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Se pueden enviar informes de error a <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>.", "smw-admin-questions": "Si tienes preguntas o sugerencias adicionales, únete a la discusión en la <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">lista de correo de usuarios de Semantic MediaWiki\n</a>.", "smw-admin-other-functions": "Otras funciones", "smw-admin-statistics-extra": "Funciones estadísticas", "smw-admin-statistics": "Estadísticas", "smw-admin-supplementary-section-title": "Funciones suplementarias", "smw-admin-supplementary-section-subtitle": "Funciones de núcleo", "smw-admin-supplementary-section-intro": "Esta sección proporciona funciones adicionales más allá del alcance del mantenimiento y es posible que algunas funciones que están listadas en la [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentación] estén restringidas o no disponibles y por lo tanto inaccesibles en esta wiki.", "smw-admin-supplementary-settings-title": "Configuración y parámetros", "smw-admin-supplementary-settings-intro": "<u>$1</u> muestra parámetros que definen el comportamiento de Semantic MediaWiki", "smw-admin-supplementary-operational-statistics-title": "Estadísticas operacionales", "smw-admin-supplementary-operational-statistics-intro": "<u>$1</u> muestra un conjunto extendido de estadísticas", "smw-admin-supplementary-idlookup-title": "Búsqueda y eliminación de la entidad", "smw-admin-supplementary-idlookup-intro": "<u>$1</u> contiene funciones para buscar y desechar entidades individuales", "smw-admin-supplementary-duplookup-title": "Búsqueda de entidades duplicadas", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> para enumerar entradas que estén categorizadas por contener duplicados en la tabla de entidades", "smw-admin-supplementary-duplookup-docu": "Esta página lista entradas que han sido categorizadas como duplicadas en la [https://www.semantic-mediawiki.org/wiki/Help:Entity_table tabla de entidades]. Las entradas duplicadas solo deberían ocurrir (como mucho) en raras ocasiones potencialmente causadas por un proceso terminado durante una actualización de la base de datos o una transacción de reversión fallida.", "smw-admin-supplementary-operational-statistics-cache-title": "Estadísticas de antememoria", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> muestra un conjunto seleccionado de estadísticas relativas a la antememoria", "smw-admin-supplementary-elastic-version-info": "Versión", "smw-admin-supplementary-elastic-intro": "<u>$1</u> informa sobre estadísticas de ajustes e indices", "smw-admin-supplementary-elastic-docu": "Esta página contiene información sobre ajustes, mapeos, salud y estadísticas de índices relacionados con un clúster de Elasticsearch que está conectado a Semantic MediaWiki y su [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Funciones compatibles", "smw-admin-supplementary-elastic-settings-title": "Configuración (índices)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> utilizado por Elasticsearch para administrar indices de MediaWiki Semántica", "smw-admin-supplementary-elastic-mappings-title": "Asignaciones", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> para listar indices y mapeos de campo", "smw-admin-supplementary-elastic-mappings-docu": "Esta página contiene detalles de la asignación de campos, ya que se utilizan con los índices actuales. El resumen de mapeo debe ser controlado en conexión con el <code>index.mapping.total_fields.limit</code> que especifica el número máximo de campos en un índice.", "smw-admin-supplementary-elastic-mappings-summary": "Resumen", "smw-admin-supplementary-elastic-mappings-fields": "Mapeos de campo", "smw-admin-supplementary-elastic-nodes-title": "Nodos", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> muestra estadísticas de nodo", "smw-admin-supplementary-elastic-indices-title": "Índices", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> proporciona una vista general de índices disponibles y sus estadísticas", "smw-admin-supplementary-elastic-statistics-title": "Estadísticas", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> muestra estadísticas de nivel de índice", "smw-admin-supplementary-elastic-statistics-docu": "Esta página proporciona una visión de las estadísticas de los índices de las diferentes operaciones que están ocurriendo a nivel de índice, las estadísticas devueltas se combinan con las primarias y las totales. La [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html página de ayuda] contiene una descripción detallada de las estadísticas de los índices disponibles.", "smw-admin-supplementary-elastic-status-replication": "Estado de replicación", "smw-admin-supplementary-elastic-status-last-active-replication": "Última replicación activa: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Intervalo de refrescado: $1", "smw-admin-supplementary-elastic-replication-files": "Archivos", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-config": "Configuraciones", "smw-list-count": "La lista contiene $1 {{PLURAL:$1|entrada|entradas}}.", "smw-property-label-uniqueness": "La etiqueta «$1» corresponde con por lo menos una representación de propiedad diferente. Consulta la [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness página de ayuda] para saber cómo solucionar este problema.", "smw-property-label-similarity-title": "Informe de similitud de etiqueta de propiedad", "smw-property-label-similarity-intro": "<u>$1</u> calcula las semejanzas para etiquetas de propiedades existentes", "smw-property-label-similarity-threshold": "Umbral:", "smw-property-label-similarity-type": "Mostrar identificador de tipo", "smw-property-label-similarity-noresult": "No se encontró ningún resultado para las opciones seleccionadas.", "smw-property-label-similarity-docu": "Compara y notifica la [https://www.semantic-mediawiki.org/wiki/Property_similarity semejanza sintáctica] (no la semántica) entre dos etiquetas de propiedades, lo que puede ayudar a filtrar propiedades mal escritas o equivalentes que representen el mismo concepto (consulta la página especial [[Special:Properties|Propiedades]] para clarificar los conceptos y los usos de las propiedades notificadas). Puede ajustarse el umbral para aumentar o disminuir la distancia de semejanza. <code>[[Property:$1|$1]]</code> se utiliza para eximir propiedades del análisis.", "smw-admin-operational-statistics": "Esta página contiene estadísticas operacionales recogidas en o desde funciones relacionadas de la MediaWiki Semántica. Puede encontrar [[Special:Statistics|<b>aquí</b>]] una lista ampliada de estadísticas específicas de la wiki.", "smw_adminlinks_datastructure": "Estructura de datos", "smw_adminlinks_displayingdata": "Most<PERSON><PERSON> da<PERSON>", "smw_adminlinks_inlinequerieshelp": "Ayuda de consultas en línea", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count Número de usos] estimado: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Propiedad definida por el {{PLURAL:$1|usuario|sistema}}", "smw-property-indicator-last-count-update": "Cuenta estimada de uso\nActualizada por última vez el: $1", "smw-concept-indicator-cache-update": "Recuento de antememoria\nÚltima actualización: $1", "smw-createproperty-isproperty": "Es una propiedad de tipo $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|El valor permitido para esta propiedad es|Los valores permitidos para esta propiedad son}}:", "smw-paramdesc-category-delim": "El delimitador", "smw-paramdesc-category-template": "Una plantilla con la que dar formato a los elementos", "smw-paramdesc-category-userparam": "Un parámetro que pasar a la plantilla", "smw-info-par-message": "El mensaje que mostrar.", "smw-info-par-icon": "Icono que mostrar: ya sea \"info\" (información) o bien \"warning\" (advertencia).", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Opciones generales", "prefs-extended-search-options": "Búsqueda ampliada", "prefs-ask-options": "Búsqueda semántica", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] (o extensiones relacionadas) suministran las opciones mostradas a continuación para permitir la personalización individual de las funciones seleccionadas. Para más información, consulta [https://www.semantic-mediawiki.org/wiki/Help:User_preferences esta página de ayuda].", "smw-prefs-ask-options-tooltip-display": "Mostrar el texto del parámetro como ayuda informativa", "smw-prefs-ask-options-compact-view-basic": "Habilitar vista compacta básica", "smw-prefs-help-ask-options-compact-view-basic": "Si se habilita, muestra un conjunto reducido de enlaces en la vista compacta Special:Ask.", "smw-prefs-general-options-time-correction": "Activar la corrección horaria en las páginas especiales mediante la preferencia local de [[Special:Preferences#mw-prefsection-rendering|desfase horario]]", "smw-prefs-general-options-jobqueue-watchlist": "Mostrar la lista de seguimiento de la cola de tareas en mi barra personal", "smw-prefs-help-general-options-jobqueue-watchlist": "Si está habilitado, muestra una [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist lista de] tareas seleccionadas pendientes junto con sus tamaños de cola estimados.", "smw-prefs-general-options-disable-editpage-info": "Desactivar el texto introductorio de la página de edición", "smw-prefs-general-options-disable-search-info": "Desactivar la información de sintaxis admitida en la página estándar de búsquedas", "smw-prefs-general-options-suggester-textinput": "Habilitar la asistencia de entrada para entidades semánticas", "smw-prefs-help-general-options-suggester-textinput": "Si está habilitado, permite utilizar una [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance asistencia de entrada] para encontrar propiedades, conceptos y categorías desde un contexto de entrada.", "smw-prefs-general-options-show-entity-issue-panel": "Mostrar el panel de problemas de la entidad", "smw-prefs-help-general-options-show-entity-issue-panel": "Si está habilitado, ejecuta verificaciones de integridad en cada página y muestra el [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel Panel de problemas de la entidad].", "smw-ui-tooltip-title-property": "Propiedad", "smw-ui-tooltip-title-quantity": "Conversión de unidad", "smw-ui-tooltip-title-info": "Información", "smw-ui-tooltip-title-service": "Enlaces de servivio", "smw-ui-tooltip-title-warning": "Advertencia", "smw-ui-tooltip-title-error": "Error", "smw-ui-tooltip-title-parameter": "Parámetro", "smw-ui-tooltip-title-event": "Evento", "smw-ui-tooltip-title-note": "<PERSON>a", "smw-ui-tooltip-title-legend": "Leyenda", "smw-ui-tooltip-title-reference": "Referencia", "smw_unknowntype": "El tipo «$1» de esta propiedad no es válido", "smw-concept-cache-text": "El concepto tiene un total de $1 {{PLURAL:$1|página|páginas}}, y se actualizó por última vez el $3 a las $2.", "smw_concept_header": "Páginas de concepto \"$1\"", "smw_conceptarticlecount": "Mostrando abajo $1 {{PLURAL:$1|página|páginas}}.", "smw-qp-empty-data": "Los datos solicitados podrían no mostrarse, debido a algunos criterios de selección insuficientes.", "right-smw-admin": "Acceder a tareas de administración (Semántica MediaWiki)", "right-smw-patternedit": "Acceso de edición para mantener expresiones regulares y patrones permitidos (Semantic MediaWiki)", "right-smw-pageedit": "Acceso de edición para páginas con anotación <code>is edit prrotected</code> (MediaWiki Semántica)", "right-smw-schemaedit": "Editar [https://www.semantic-mediawiki.org/wiki/Help:Schema páginas de esquema] (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "Acceso a la función de lista de [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist seguimiento] de la cola de trabajos (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Acceder a información sobre una discrepancia de revisión asociada a una entidad", "right-smw-vieweditpageinfo": "Ver [https://www.semantic-mediawiki.org/wiki/Help:Edit_help ayuda de edición] (Semantic MediaWiki)", "restriction-level-smw-pageedit": "protegido (solamente usuarios elegibles)", "action-smw-patternedit": "editar expresiones regulares utilizadas por Semantic MediaWiki", "action-smw-pageedit": "editar páginas anotadas con <code>is edit protected</code> (MediaWiki Semántica)", "group-smwadministrator": "Administradores (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrador|administradora}} (Semantic MediaWiki)", "grouppage-smwadministrator": "{{ns:project}}:Administradores (Semantic MediaWiki)", "group-smwcurator": "Curadores (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|curador|curadora}} (Semantic MediaWiki)", "grouppage-smwcurator": "{{ns:project}}:Curadores (Semantic MediaWiki)", "group-smweditor": "Editores (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|editor|editora}} (Semantic MediaWiki)", "grouppage-smweditor": "{{ns:<PERSON>yecto}}:Editores (Semánticos MediaWiki)", "action-smw-admin": "acceder a tareas de administración de Semantic MediaWiki", "action-smw-ruleedit": "editar páginas de regla (Semantic MediaWiki)", "smw-property-predefined-default": "«$1» es una propiedad predefinida de tipo $2.", "smw-property-predefined-common": "Esta propiedad está implementada previamente (también conocida como [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propiedad especial]) y viene con privilegios administrativos adicionales, pero puede usarse como cualquier otra [https://www.semantic-mediawiki.org/wiki/Property propiedad definida por usuario].", "smw-property-predefined-ask": "\"$1\" es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que representa metainformación (en forma de un [https://www.semantic-mediawiki.org/wiki/Subobject subobjeto]) sobre consultas individuales.", "smw-property-predefined-asksi": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que recopila el número de condiciones utilizadas en una consulta.", "smw-property-predefined-askde": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que informa sobre la profundidad de una consulta.", "smw-property-predefined-long-askde": "Es un valor numérico calculado con base en el anidamiento de subconsultas, en las cadenas de propiedades y en los elementos descriptores disponibles, con la ejecución de una consulta restringida por el parámetro de configuración <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que describe parámetros que influyen el resultado de una consulta.", "smw-property-predefined-long-askpa": "Es parte de una colección de propiedades que especifican un [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler perfil de consulta].", "smw-sp-properties-docu": "Esta página muestra las [https://www.semantic-mediawiki.org/wiki/Property propiedades] y el número de usos disponible de cada una de ellas para esta wiki. Para obtener estadísticas actualizadas, se recomienda ejecutar regularmente el <i>script</i> de mantenimiento de [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics estadísticas de propiedades]. Para una vista diferenciada, véanse las páginas especiales «[[Special:UnusedProperties|Propiedades sin uso]]» o «[[Special:WantedProperties|Propiedades requeridas]]».", "smw-sp-properties-cache-info": "Los datos listados se han obtenido de la [https://www.semantic-mediawiki.org/wiki/Caching caché], y fueron actualizados por última vez el $1.", "smw-sp-properties-header-label": "Lista de propiedades", "smw-admin-settings-docu": "Muestra una lista de todas las preferencias predeterminadas y localizadas que son relevantes para el entorno de Semantic MediaWiki. Para más detalles sobre preferencias individuales, consulta la página de ayuda de [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuración].", "smw-sp-admin-settings-button": "Generar lista de preferencias", "smw-admin-idlookup-title": "Buscar", "smw-admin-idlookup-docu": "Esta sección muestra detalles técnicos sobre una entidad individual (página de wiki, subobjeto, propiedad, etc.) de Semantic MediaWiki. La entrada puede ser un identificador numérico o una cadena que corresponda con el campo de búsqueda relevante. Observa que las referencias de los identificadores se relacionan con Semantic MediaWiki y no con los identificadores de página o de revisión de MediaWiki.", "smw-admin-iddispose-title": "Eliminación", "smw-admin-iddispose-docu": "Cabe resaltar que la operación de eliminación no está restringida y, si se confirma, quitará, del motor de almacenamiento, la entidad junto con todas sus referencias en las tablas pendientes. Por favor, realiza esta tarea con '''precaución''' y sólo después de haber consultado la [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentación].", "smw-admin-iddispose-done": "El identificador «$1» se eliminó del motor de almacenamiento.", "smw-admin-iddispose-references": "El identificador «$1» {{PLURAL:$2|no tiene ninguna|tiene al menos una}} referencia activa:", "smw-admin-iddispose-references-multiple": "Lista de correspondencias con al menos un registro de referencias activo.", "smw-admin-iddispose-no-references": "No se pudo relacionar «$1» con ninguna entrada de la tabla.", "smw-admin-idlookup-input": "Buscar:", "smw-admin-objectid": "Identificador:", "smw-admin-tab-general": "Visión general", "smw-admin-tab-notices": "Avisos de obsolescencia", "smw-admin-tab-supplement": "Funciones suplementarias", "smw-admin-tab-registry": "Registro", "smw-admin-tab-alerts": "<PERSON><PERSON><PERSON>", "smw-admin-alerts-tab-deprecationnotices": "Avisos de obsolescencia", "smw-admin-alerts-tab-maintenancealerts": "Alertas de mantenimiento", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Optimización de tablas", "smw-admin-maintenancealerts-invalidentities-alert-title": "Entidades no válidas", "smw-livepreview-loading": "Cargando…", "smw-sp-searchbyproperty-description": "Esta página proporciona una [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces interfaz de exploración] simple para encontrar entidades descritas por una propiedad y un valor con nombre. Otras interfaces de búsqueda disponibles incluyen la  [[Special:PageProperty|búsqueda de propiedades de página]] y el [[Special:Ask|constructor de consultas]].", "smw-sp-searchbyproperty-resultlist-header": "Lista de resultados", "smw-sp-searchbyproperty-nonvaluequery": "Una lista de valores que tienen asignada la propiedad \"$1\".", "smw-sp-searchbyproperty-valuequery": "Una lista de las páginas que tienen la propiedad \"$1\" con el valor \"$2\" anotado.", "smw-datavalue-number-textnotallowed": "\"$1\" no puede asignarse a un tipo de número declarado con valor $2.", "smw-datavalue-number-nullnotallowed": "«$1» devolvió «NULL», el cual no se permite como número.", "smw-editpage-annotation-enabled": "Esta página admite anotaciones semánticas en el texto (p. ej. <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) para construir contenido estructurado y consultable provisto por Semantic MediaWiki. Para una descripción completa sobre cómo usar anotaciones o la función analizadora #ask, consulta a las páginas de ayuda [https://www.semantic-mediawiki.org/wiki/Help:Getting_started primeros pasos], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation anotaciones en el texto] o [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries consultas en línea].", "smw-editpage-annotation-disabled": "Esta página no tiene habilitadas las anotaciones semánticas dentro del texto debido a restricciones en el espacio de nombres. Puedes encontrar instrucciones sobre cómo habilitar el espacio de nombres en la página de ayuda de la [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuración].", "smw-editpage-property-annotation-enabled": "Esta propiedad puede extenderse usando anotaciones semánticas para especificar un tipo de dato (p. ej. <nowiki>\"[[Has type::Page]]\"</nowiki>) u otras declaraciones de asistencia (e.g. <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). Para saber cómo aumentar esta página, consulta las páginas de ayuda [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaración de una propiedad] o [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes lista de los tipos de datos disponibles].", "smw-editpage-property-annotation-disabled": "Esta propiedad no puede extenderse con una anotación de tipo de dato (p. ej. <nowiki>\"[[Has type::Page]]\"</nowiki>) porque ya está predefinida (para más información, consulta la página de ayuda [https://www.semantic-mediawiki.org/wiki/Help:Special_properties propiedades especiales]).", "smw-editpage-concept-annotation-enabled": "Es posible extender este concepto por medio de la función #concept del analizador. Para saber cómo utilizar #concept, consulta la página de ayuda de los [https://www.semantic-mediawiki.org/wiki/Help:Concepts conceptos].", "smw-search-syntax-support": "En las búsquedas se puede utilizar la [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search sintaxis de consultas semánticas] para encontrar correspondencias mediante Semantic MediaWiki.", "smw-search-input-assistance": "El [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance asistente de entrada] se activa también para facilitar la preselección de las entidades disponibles.", "smw-search-help-ask": "Los siguientes enlaces explicarán cómo usar la sintaxis <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Seleccionar páginas] describe cómo seleccionar páginas y crear condiciones\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Operadores de búsqueda] enumera los operadores de búsqueda disponibles, incluidos los de consultas de rango y comodín", "smw-search-input": "Entrada y busqueda", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Asistencia de entrada] se proporciona para el campo de entrada y requiere usar uno de los siguientes prefijos:\n\n*<code>p:</code> para habilitar sugerencias de propiedad (por ejemplo, <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> para habilitar sugerencias de categoría\n\n*<code>con:</code> para habilitar sugerencias de concepto", "smw-search-syntax": "Sintaxis", "smw-search-profile": "Extendido", "smw-search-profile-tooltip": "Busca funciones en conexión con MediaWiki Semántica", "smw-search-profile-sort-best": "Mejor coincidencia", "smw-search-profile-sort-recent": "Más reciente", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-intro": "El [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile perfil extendido] Special:Search proporciona acceso para buscar funciones concretas a Semantic MediaWiki y su backend de consulta admitido.", "smw-search-profile-extended-help-sort": "Especifica una preferencia de ordenamiento para mostrar los resultados con:", "smw-search-profile-extended-help-sort-title": "* \"Título\" usando el título de la página (o el título en pantalla) como criterio de clasificación", "smw-search-profile-extended-help-sort-recent": "* \"Más reciente\" mostrará primero las entidades modificadas más recientemente ( las entidades de subobjeto serán suprimidas así como las entidades que no están anotadas con una [[Propiedad:fecha de Modificación|de fecha de Modificación]])", "smw-search-profile-extended-help-sort-best": "* \"Mejor coincidencia\" ordenará las entidades por [https://www.semantic-mediawiki.org/wiki/help:ElasticStore/Relevancy relevancia] basándose en las puntuaciones provistas por el backend", "smw-search-profile-extended-help-form": "Se proporcionan formularios (si se actualizan) para que coincidan con casos de uso específicos, exponiendo diferentes campos de propiedades y valores para reducir el proceso de entrada y facilitar a los usuarios la tarea de realizar una solicitud de búsqueda. (ver $1)", "smw-search-profile-extended-help-namespace": "La caja de selección del espacio de nombres estará oculta en cuanto se seleccione una forma, pero puede ser hecha visible con la ayuda del botón \"mostrar/ocultar\".", "smw-search-profile-extended-help-search-syntax": "El campo de entrada de la búsqueda perimite el uso de la sintaxis <code>#preguntar</code>  para definir una búsqueda concreta contextual con Semántica MediaWiki . Las expresiones útiles incluyen:", "smw-search-profile-extended-help-search-syntax-prefix": "* Prefijos adicionales personalizados están disponibles y definidos cada uno como: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* Algunas expresiones están reservadas como: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "' Algunas de las operaciones listadas son s<PERSON>lo <PERSON> en conexión con un  índice habilitado de texto lleno o el ElasticStore.''", "smw-search-profile-extended-help-query": "Se utilizó <code><nowiki>$1</nowiki></code> como consulta.", "smw-search-profile-extended-help-query-link": "(Para más detalles $1).", "smw-search-profile-extended-help-find-forms": "formularios disponibles", "smw-search-profile-extended-section-sort": "Ordenar por", "smw-search-profile-extended-section-form": "Formularios", "smw-search-profile-extended-section-search-syntax": "Entrada de búsqueda", "smw-search-profile-extended-section-namespace": "Espacio de nombre<PERSON>", "smw-search-profile-extended-section-query": "Consulta", "smw-search-profile-link-caption-query": "ver", "smw-search-show": "Mostrar", "smw-search-hide": "Ocultar", "log-name-smw": "Registro de Semantic MediaWiki", "log-show-hide-smw": "$1 el registro de Semantic MediaWiki", "logeventslist-smw-log": "Registro de Semantic MediaWiki", "log-description-smw": "Actividades para [https://www.semantic-mediawiki.org/wiki/Help:Logging tipos de evento habilitados] que han sido notificados por la MediaWiki Semántica y sus componentes.", "logentry-smw-maintenance": "Sucesos relacionados con el mantenimiento emitidos por Semantic MediaWiki", "smw-datavalue-import-unknown-namespace": "El espacio de nombres de importación «$1» es desconocido. Comprueba que los detalles de importación de OWL estén disponibles a través de [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "No se pudo encontrar el URI del espacio de nombres «$1» en la [[MediaWiki:Smw import $1|importación de $1]].", "smw-datavalue-import-missing-type": "No se encontró ninguna definición de tipos para «$1» en la [[MediaWiki:Smw import $2|importación de $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Importación de $1]]", "smw-datavalue-import-invalid-value": "\"$1\" no tiene un formato válido. Se espera que conste de \"espacio de nombres\":\"identificador\" (por ejemplo, \"foaf:name\").", "smw-datavalue-import-invalid-format": "Se esperaba que la cadena «$1» estuviese dividida en cuatro partes, pero no se comprendió su formato.", "smw-property-predefined-impo": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que describe relaciones con [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary vocabulario importado].", "smw-property-predefined-type": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que describe el [[Special:Types|tipo de datos]] de una propiedad.", "smw-property-predefined-sobj": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que representa una construcción de [https://www.semantic-mediawiki.org/wiki/Help:Container contenedor].", "smw-property-predefined-long-sobj": "El contenedor permite acumular pares de asignaciones propiedad-valor semejantes a los de páginas wiki normales pero ubicadas en un espacio de entidades diferente y con enlaces al sujeto que lo incluye.", "smw-property-predefined-errp": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que permite rastrear anotaciones de valores irregulares en los errores de entrada.", "smw-property-predefined-long-errp": "En la mayoría de los casos lo provoca un error de correspondencia de tipos o una restricción de [[Property:Allows value|valores]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value «$1»] es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que puede definir una lista de valores permitidos para restringir las asignaciones de valores de alguna propiedad.", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list «$1»] es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que permite especificar una referencia a una lista que contenga valores permitidos para restringir las asignaciones de valores en propiedades.", "smw-datavalue-property-restricted-annotation-use": "La propiedad «$1» tiene aplicaciones limitadas y los usuarios no pueden utilizarla como propiedad de anotación.", "smw-datavalue-property-restricted-declarative-use": "La propiedad «$1» es declarativa y solo puede utilizarse en páginas de propiedad o de categoría.", "smw-datavalue-property-create-restriction": "No existe la propiedad «$1» y el usuario carece del permiso «$2» (consulta [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Modo de autoridad]) para crear o anotar valores con una propiedad no aprobada.", "smw-datavalue-property-invalid-character": "«$1» contiene el carácter registrado «$2» como parte de la etiqueta de una propiedad. Por ello, se ha clasificado como no válido.", "smw-datavalue-property-invalid-chain": "No se permite el uso de «$1» como cadena de propiedades durante el proceso de anotación.", "smw-datavalue-restricted-use": "El valor de datos \"$1\" ha sido marcado para uso restringido.", "smw-datavalue-invalid-number": "No se puede interpretar a \"$1\" como un número.", "smw-query-condition-circular": "Se detectó una posible condición circular en \"$1\".", "smw-types-list": "Lista de tipos de datos", "smw-types-default": "\"$1\" es un tipo de datos.", "smw-types-help": "Más información y ejemplos en esta [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 página de ayuda].", "smw-type-anu": "«$1» es una variante del tipo de datos [[Special:Types/URL|URL]] y se utiliza en gran parte para declaraciones de exportación del tipo ''owl:AnnotationProperty''.", "smw-type-boo": "«$1» es un tipo de datos básico para describir un valor de verdadero/falso.", "smw-type-cod": "«$1» es una variante del tipo de datos [[Special:Types/Text|Text]] que está destinado para utilizarse con textos técnicos de cualquier longitud, tales como listados de código fuente.", "smw-type-geo": "«$1» es un tipo de datos que describe ubicaciones geográficas y necesita la extensión [https://www.semantic-mediawiki.org/wiki/Extension:Maps Maps] para proveer más funciones.", "smw-type-tel": "«$1» es un tipo de datos especial para describir números telefónicos internacionales de conformidad con RFC 3966.", "smw-type-txt": "«$1» es un tipo de datos básico que describe cadenas de longitud arbitraria.", "smw-type-dat": "«$1» es un tipo de datos básico para representar puntos en el tiempo en un formato unificado.", "smw-type-tab-type-ids": "Identificadores de tipo", "smw-type-container": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-no-group": "Sin clasificar", "smw-special-pageproperty-description": "Esta página provee una interfaz de búsqueda para encontrar todos los valores de una propiedad o página dada. Otras interfazes de búsqueda incluyen [[Special:SearchByProperty|búsqueda de propiedad]] y el [[Special:Ask|constructor de preguntas]].", "smw-property-predefined-errc": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que representa errores que aparecen en la conexión con anotaciones de valores o procesamiento de entradas inapropiados.", "smw-property-predefined-long-errc": "Los errores se recopilan en un [https://www.semantic-mediawiki.org/wiki/Help:Container contenedor] que puede que incluya una referencia a la propiedad que provocó la discrepancia.", "smw-property-predefined-errt": "«$1» es una propiedad predefinida, proporcionada [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que contiene una descripción textual de un error.", "smw-subobject-parser-invalid-naming-scheme": "Un subobjeto definido por el usuario tenía un esquema de nombres invalido. La notación de punto ($1) utilizada en los primeros cinco caracteres está reservada para las extensiones. Puedes establecer un [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier identificador].", "smw-datavalue-record-invalid-property-declaration": "La definición del registro contiene la propiedad «$1», la cual está declarada ella misma como tipo de registro. Esto no se permite.", "smw-property-predefined-mdat": "\"$1\" es una propiedad predefinida proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] que corresponde con la fecha de la última modificación de un sujeto.", "smw-property-predefined-cdat": "\"$1\" es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que corresponde con la fecha de la primera revisión de un asunto.", "smw-property-predefined-newp": "\"$1\" es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que indica si un sujeto es nuevo o no.", "smw-property-predefined-ledt": "\"$1\" es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que contiene el nombre de la página del usuario que creó la última revisión.", "smw-property-predefined-mime": "«$1» es una propiedad predefinida, proporcioada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que describe el tipo MIME de un archivo cargado.", "smw-property-predefined-media": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que describe el tipo de multimedia de un archivo cargado.", "smw-property-predefined-askfo": "\"$1\" es una propiedad predefinida, proporcionada [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que guarda el nombre del formato de resultado usado en una consulta.", "smw-property-predefined-askst": "\"$1\" es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que describe las condiciones de la consulta como una cadena.", "smw-property-predefined-askdu": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que contiene un valor de tiempo (en segundos) necesario para completar la ejecución de la consulta.", "smw-property-predefined-asksc": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que identifica fuentes alternativas de consultas (por ejemplo, fuentes remotas o federadas).", "smw-property-predefined-askco": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para describir el estado de una consulta o de sus componentes.", "smw-property-predefined-long-askco": "El número o los números asignados representan un estado codificado interno que está explicado en la [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler págin<PERSON> de ayuda].", "smw-property-predefined-prec": "«$1» es una propiedad predefinida que describe una [https://www.semantic-mediawiki.org/wiki/Help:Display_precision precisión de muestra] (en dígitos decimales) para tipos de datos numéricos.", "smw-types-extra-geo-not-available": "No se ha detectado la extensión [https://www.semantic-mediawiki.org/wiki/Extension:Maps Maps], por lo tanto, la capacidad para operar de «$1» está restringida.", "smw-datavalue-monolingual-dataitem-missing": "Falta un elemento esperado para construir un valor compuesto monolingüe.", "smw-datavalue-languagecode-missing": "El analizador sintáctico no pudo determinar el código de idioma de la anotación «$1» (p. ej., «algo@es»).", "smw-datavalue-languagecode-invalid": "No se reconoce «$1» como un código de idioma admitido.", "smw-property-predefined-lcode": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que representa un código de idioma con formato BCP47.", "smw-type-mlt-rec": "\"$1\" es un tipo de datos [https://www.semantic-mediawiki.org/wiki/Help:Container contenedor] que asocia un valor de texto con un [[Property:Language code|código de idioma]] específico.", "smw-types-extra-mlt-lcode": "El tipo de datos{{PLURAL:$2|requiere|no requiere}} un código de idioma (o sea, {{PLURAL:$2|no se acepta|se acepta}} una anotación de valor sin un código de idioma).", "smw-property-predefined-text": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que representa un texto de longitud arbitraria.", "smw-property-predefined-pdesc": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que permite describir una propiedad en el contexto de un idioma.", "smw-property-predefined-list": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que define una lista de propiedades usadas con una propiedad de tipo [[Special:Types/Record|registro]].", "smw-limitreport-intext-parsertime": "[SMW] Analizador de tiempo de la anotación en el texto", "smw-limitreport-intext-postproctime": "[SMW] duración de posprocesamiento", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Tiempo de actualización del almacenaje (en la purga de la página)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|segundo|segundos}}", "smw_allows_pattern": "Esta página debería contener una lista de referencias (seguido de [https://es.wikipedia.org/wiki/Expresi%C3%B3n_regular expresiones regulares]) para estar disponible por la propiedad [[Property:Allows pattern|Permite patrón]]. Para editar esta página, se requiere el permiso <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "La expresión regular \"$2\" clasificó a \"$1\" como no válida.", "smw-datavalue-allows-pattern-reference-unknown": "La referencia del patrón \"$1\" no corresponde a ninguna entrada de [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "La referencia de lista «$1» no se corresponde con ninguna página de [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "Al contenido de la lista «$1» le faltan elementos con un marcador de lista *.", "smw-datavalue-feature-not-supported": "La funcionalidad \"$1\" no es compatible o se desactivó en esta wiki.", "smw-property-predefined-pvap": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que puede especificar una [[MediaWiki:Smw allows pattern|patrón de referencia]] para que coincida con una [https://es.wikipedia.org/wiki/Expresi%C3%B3n_regular expresión regular].", "smw-property-predefined-dtitle": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que puede asignar un título de visualización diferente a una entidad.", "smw-property-predefined-pvuc": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que restringe las asignaciones de valores para cada instancia a valores únicos.", "smw-property-predefined-long-pvuc": "La singularidad se establece cuando dos valores no son iguales en su representación literal, cualquier violación de esta restricción se clasifica como errónea.", "smw-datavalue-constraint-uniqueness-violation": "La propiedad \"$1\" sólo permite que el único valor de las cesiones y \"$2\" ya fue anotado en el \" asunto \"$3\".", "smw-datavalue-constraint-uniqueness-violation-isknown": "La propiedad \"$1\" sólo permite anotaciones de valores exclusivos. ''$2'' ya contiene un valor asignado. \"$3\" viola la restricción de exclusividad.", "smw-datavalue-constraint-violation-non-negative-integer": "La propiedad «$1» tiene una restricción de «entero no negativo» y el valor ''$2'' incumple ese requisito.", "smw-datavalue-constraint-violation-single-value": "La \"[[Property:$1|$1]]\" tiene una restricción <code>valor_singular</code> y el valor \"$2\" está violando este requisito.", "smw-property-predefined-boo": "«$1» es un [[Special:Types/Boolean|tipo]] y una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores boleanos.", "smw-property-predefined-num": "«$1» es un [[Especial:Tipos/Número|tipo]] y propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores numéricos.", "smw-property-predefined-dat": "«$1» es un [[Especial:Tipos/Fecha|tipo]] y propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores de fecha.", "smw-property-predefined-uri": "«$1» es un [[Special:Types/URL|tipo]] y propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores URI/URL.", "smw-property-predefined-qty": "«$1» es una [[Special:Types/Quantity|tipo]] y propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar valores de cantidad.", "smw-datavalue-time-invalid-offset-zone-usage": "«$1» contiene una diferencia y un identificador de huso no admitidos.", "smw-datavalue-time-invalid-values": "El valor «$1» contiene información que no se puede interpretar en forma de «$2».", "smw-datavalue-time-invalid-date-components-common": "«$1» contiene alguna información que no se puede interpretar.", "smw-datavalue-time-invalid-date-components-dash": "«$1» contiene un guión extrínseco u otros caracteres que son inválidos para la interpretación de una fecha.", "smw-datavalue-time-invalid-date-components-empty": "«$1» contiene algunos componentes vacíos.", "smw-datavalue-time-invalid-date-components-three": "«$1» contiene más de tres componentes necesarios para la interpretación de una fecha.", "smw-datavalue-time-invalid-date-components-sequence": "\"$1\" contiene una secuencia que no se ha podido interpretar contra una matriz disponible de correspondencias para componentes de una fecha.", "smw-datavalue-time-invalid-ampm": "«$1» contiene «$2» como elemento de hora que no es válido en el formato convencional de 12 horas.", "smw-datavalue-time-invalid-jd": "No se puede interpretar el valor de entrada «$1» como un DJ (día juliano) valido al ser reportado «$2».", "smw-datavalue-time-invalid-prehistoric": "No se puede interpretar un valor de entrada prehistórico «$1». <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, al haber especificado más años o un modelo de calendario que podría devolver resultados inesperados en un contexto prehistórico (anterior al año 10000).", "smw-datavalue-time-invalid": "No se puede interpretar el valor de entrada «$1» como una fecha o componente temporal valido al ser reportado «$2».", "smw-datavalue-external-formatter-uri-missing-placeholder": "Falta el marcador de posición «$1» en el URL del formateador.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" es una dirección URL no válida.", "smw-datavalue-external-identifier-formatter-missing": "A la propiedad le falta una asignación de [[Propiedad:URI del formateador externo|«URI del formateador externo»]].", "smw-datavalue-keyword-maximum-length": "La palabra clave superó la longitud máxima de $1 {{PLURAL:$1|carácter|caracteres}}", "smw-property-predefined-eid": "«$1» es una [[Special:Types/External identifier|tipo]] y propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para representar identificadores externos.", "smw-property-predefined-peid": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que especifica un identificador externo.", "smw-property-predefined-pefu": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para especificar un recurso externo con un marcador de posición.", "smw-property-predefined-long-pefu": "Se espera que el URI contenga un marcador de posición que se ajustará con un valor de [[Special:Types/External identifier|identificador externo]] para formar una referencia de recurso válida.", "smw-type-eid": "«$1» es una variante del tipo de dato [[Special:Types/Text/Texto]] que requiere propiedades asignadas para declarar una [[Propiedad:URI del formateador externo|URI del formateador externo]].", "smw-property-predefined-keyw": "«$1» es una propiedad predefina y un [[Special:Types/Keyword|tipo]], proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que normaliza un texto y tiene una longitud de caracteres restringida.", "smw-type-keyw": "«$1» es una variante del tipo de dato [[Special:Types/Text|texto]] que tiene una longitud de caracteres restringida y normaliza su representación del contenido.", "smw-datavalue-stripmarker-parse-error": "El valor aportado «$1» contiene [https://en.wikipedia.org/wiki/Help:Strip_markers ''strip markers''] (marcadores de tira), por lo que no puede ser analizado suficientemente.", "smw-datavalue-parse-error": "No se comprendió el valor «$1» proporcionado.", "smw-datavalue-propertylist-invalid-property-key": "La lista de propiedades «$1» contenía una clave de propiedad no válida, «$2».", "smw-datavalue-type-invalid-typeuri": "No se pudo transformar el tipo «$1» en una representación de URI válida.", "smw-datavalue-wikipage-missing-fragment-context": "El valor de entrada wikipágina «$1» no puede ser utilizado sin una página de contexto.", "smw-datavalue-wikipage-invalid-title": "El valor de entrada de tipo página «$1» contiene caracteres inválidos o está incompleto, por lo que puede causar resultados inesperados durante una consulta o proceso de anotación.", "smw-datavalue-wikipage-property-invalid-title": "La propiedad «$1» (como tipo de página) con el valor de entrada «$2» contiene caracteres inválidos o está incompleto, por lo que puede causar resultados inesperados durante una consulta o proceso de anotación.", "smw-datavalue-wikipage-empty": "El valor de entrada wikipágina está vacío (p. ej., <code>[[SomeProperty::]], [[]]</code>), por lo que no puede ser utilizado como un nombre o como parte de una condición de consulta.", "smw-type-ref-rec": "«$1» es un tipo [https://www.semantic-mediawiki.org/wiki/Container contenedor] que permite registrar información adicional (p. ej., el origen de los datos) sobre la asignación de un valor.", "smw-datavalue-reference-invalid-fields-definition": "El tipo [[Special:Types/Reference|referencia]] espera una lista de propiedades para ser declaradas usando la propiedad [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Tiene campos].", "smw-parser-invalid-json-format": "El analizador de JSON devolvió «$1».", "smw-property-preferred-label-language-combination-exists": "No se puede utilizar «$1» como etiqueta preferida porque el idioma «$2» ya se ha asignado a la etiqueta «$3».", "smw-clipboard-copy-link": "<PERSON><PERSON><PERSON> enlace en el portapapeles", "smw-property-userdefined-fixedtable": "«$1» fue configurada como una [https://www.semantic-mediawiki.org/wiki/Fixed_properties propiedad fijada] y cualquier modificación de su [https://www.semantic-mediawiki.org/wiki/Type_declaration tipo de declaración] requiere que se ejecute <code>setupStore.php</code> o completar la tarea especial [[Special:SemanticMediaWiki|«Instalación y actualización de la base de datos»]].", "smw-data-lookup": "Obteniendo datos...", "smw-data-lookup-with-wait": "La solicitud está procesándose y puede tomar algo de tiempo.", "smw-no-data-available": "No hay datos disponibles.", "smw-property-req-violation-missing-fields": "A la propiedad «$1» le faltan detalles de declaración para el tipo «$2» anotado al no definir la propiedad <code>Tiene campos</code>.", "smw-property-req-violation-missing-formatter-uri": "A la propiedad «$1» le faltan detalles de declaración para el tipo anotado al no definir la propiedad <code>URI del formateador externo</code>.", "smw-property-req-violation-predefined-type": "La propiedad «$1», como propiedad predefinida, contiene un tipo de declaración «$2» que es incompatible con el tipo predeterminado de esta propiedad.", "smw-property-req-violation-import-type": "Fue decteado un tipo de declaración que es incompatible con el tipo predefinido del vocabulario importado «$1». En general, no es necesario declarar un tipo porque la información se recupera de la definición de importación.", "smw-property-req-violation-change-propagation-locked-error": "La propiedad «$1» fue alterada y necesita que las entidades asignadas sean reevaluadas usando un proceso de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagación de cambios]. La página de la propiedad ha sido protegida hasta que la actualización de especificación primaria se haya completado para prevenir interrupciones intermedias o especificaciones contradictorias. El proceso puede tomar un momento antes de que la página pueda ser desprotegida, este dependerá del tamaño y la frecuencia del planificador de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cola de trabajo] del planificador.", "smw-property-req-violation-change-propagation-locked-warning": "La propiedad «$1» fue alterada y necesita que las entidades asignadas sean reevaluadas usando un proceso de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagación de cambios]. La actualización puede tomar un momento que dependerá del tamaño y la frecuencia del planificador de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cola de trabajo] del planificador. Se sugiere que se posponga cambios en la propiedad para prevenir interrupciones intermedias o especificaciones contradictorias.", "smw-property-req-violation-change-propagation-pending": "La actualización de la [https://www.semantic-mediawiki.org/wiki/Change_propagation propagación de cambios] está pendiente ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|trabajo|trabajos}}] {{PLURAL:$1|estimado|estimados}}) y se recomienda esperar a realizar modificaciones a una propiedad hasta que el proceso haya finalizado para prevenir interrupciones intermedias y especificaciones de contradictorias.", "smw-property-req-violation-missing-maps-extension": "MediaWiki Semántica no fue capaz de detectar la extensión [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"Maps\"], la cual es un prerequisito para el tipo seleccionado y como consecuencia limita la funcionalidad de esta propiedad.", "smw-property-req-violation-type": "", "smw-change-propagation-protection": "Esta página está bloqueada para evitar modificaciones accidentales de los datos durante la ejecución de una actualización de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagación de cambios]. El proceso puede demorar unos momentos antes de que se desbloquee la página, ya que depende del tamaño y de la frecuencia del programador de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cola de tareas].", "smw-category-change-propagation-locked-error": "La categoría «$1» fue alterada y necesita que las entidades asignadas sean reevaluadas usando un proceso de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagación de cambios]. Mientras tanto, la página de la categoría ha sido protegida hasta que se haya completado la actualización de especificación primaria para prevenir interrupciones intermedias o especificaciones contradictorias. El proceso puede tomar un momento antes de que la página pueda ser desprotegida, este dependerá del tamaño y la frecuencia del planificador de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cola de trabajo] del planificador.", "smw-category-change-propagation-locked-warning": "La categoría «$1» fue alterada y necesita que las entidades asignadas sean reevaluadas usando un proceso de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagación de cambios]. La actualización puede tomar un momento que dependerá del tamaño y la frecuencia del planificador de la [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue cola de trabajo] del planificador. Se sugiere que se posponga cambios en la propiedad para prevenir interrupciones intermedias o especificaciones contradictorias.", "smw-category-change-propagation-pending": "Las actualizaciones de la [https://www.semantic-mediawiki.org/wiki/Change_propagation propagación de cambios] están pendientes ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|trabajo|trabajos}}] {{PLURAL:$1|estimado|estimados}}) y se recomienda esperar a realizar modificaciones en la categoría hasta que el proceso haya finalizado para prevenir interrupciones intermedias o especificaciones contradictorias.", "smw-category-invalid-value-assignment": "\"$1\" no es reconocido como una categoría válida o una anotación válida.", "protect-level-smw-pageedit": "Permitir únicamente usuarios con el permiso de edición de páginas (Semantic MediaWiki)", "smw-create-protection": "La creación de la propiedad «$1» está restringida a los usuarios que cuenten con el privilegio (o pertenezcan al [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuarios]) «$2» adecuado, siempre que esté activado el [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridad].", "smw-create-protection-exists": "Las modificaciones a la propiedad «$1» están restringidas a los usuarios que cuenten con el privilegio (o pertenezcan al [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuarios]) «$2» adecuado, siempre que esté activado el [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode modo de autoridad].", "smw-edit-protection": "Esta página está [[Propiedad:Está protegida de edición|protegida]] para prevenir la modificación accidental de datos y solo puede ser editada por usuarios con los derechos de edición apropiados («$1») o por un [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups grupo de usuarios].", "smw-edit-protection-disabled": "Se desactivó la protección contra ediciones; por ello, no se puede utilizar «$1» para proteger las páginas de entidades de ediciones no autorizadas.", "smw-edit-protection-auto-update": "Semantic MediaWiki ha actualizado el estado de protección conforme a la propiedad «Está protegida de edición».", "smw-edit-protection-enabled": "Edición protegida (Semantic MediaWiki)", "smw-patternedit-protection": "Esta página está protegida y solo pueden modificarla aquellos usuarios que cuenten con el [https://www.semantic-mediawiki.org/wiki/Help:Permissions permiso] <code>smw-patternedit</code> adecuado.", "smw-property-predefined-edip": "«$1» es una propiedad predefinida, proporcionada por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], para señalar si la edición está protegida o no.", "smw-property-predefined-long-edip": "Aunque cualquier usuario puede añadir esta propiedad a un tema, solo aquellos usuarios que posean un privilegio especializado pueden editar o revocar la protección de una entidad después de haberse añadido esta.", "smw-query-reference-link-label": "Referencia de consulta", "smw-format-datatable-emptytable": "No hay datos disponibles en la tabla", "smw-format-datatable-info": "Se muestran las entradas _START_ a _END_, de _TOTAL_", "smw-format-datatable-infoempty": "Se muestran de 0 a 0 de 0 entradas", "smw-format-datatable-infofiltered": "(filtradas de un total de _MAX_ entradas)", "smw-format-datatable-infothousands": "&nbsp;", "smw-format-datatable-lengthmenu": "Mostrar _MENU_ entradas", "smw-format-datatable-loadingrecords": "Cargando…", "smw-format-datatable-processing": "Procesando…", "smw-format-datatable-search": "Buscar:", "smw-format-datatable-zerorecords": "No se encontró ningún registro que coincidiera", "smw-format-datatable-first": "Primer", "smw-format-datatable-last": "Último", "smw-format-datatable-next": "Siguient<PERSON>", "smw-format-datatable-previous": "Anterior", "smw-format-datatable-sortascending": ": actívalo para ordenar la columna ascendentemente", "smw-format-datatable-sortdescending": ": actívalo para ordenar la columna descendentemente", "smw-format-datatable-toolbar-export": "Exportar", "smw-format-list-other-fields-open": "(", "smw-category-invalid-redirect-target": "La categoría «$1» contiene un objetivo de redirección no válido a un espacio de nombres que no es de categoría.", "smw-parser-function-expensive-execution-limit": "La función de análisis ha alcanzado el limite para ejecuciones costosas (véase el parámetro de configuración  [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "Semantic MediaWiki está actualizando la página actual siempre y cuando haya consultas que necesiten posprocesamiento.", "apihelp-smwinfo-summary": "Módulo de la API para recuperar información sobre las estadísticas de Semantic MediaWiki y otros metadatos.", "apihelp-ask-summary": "Módulo de la API para realizar consultas en Semantic MediaWiki mediante el lenguaje Ask.", "apihelp-askargs-summary": "Módulo de la API para realizar consultas en Semantic MediaWiki mediante el lenguaje Ask en forma de lista de condiciones, visualizaciones y parámetros.", "apihelp-browsebyproperty-summary": "Módulo de la API para recuperar información sobre alguna propiedad o lista de propiedades.", "apihelp-browsebysubject-summary": "Módulo de la API para recuperar información sobre algún tema.", "apihelp-smwtask-summary": "Módulo de la API para ejecutar tareas relacionadas con Semantic MediaWiki (para uso interno solamente, no apto para uso público).", "apihelp-smwbrowse-summary": "Módulo API para añadir compatibilidad con las actividades de exploración en diferentes tipos de entidades de Semantic MediaWiki.", "apihelp-ask-parameter-api-version": "Formato de salida:\n;2:Formato compatible con el formato antiguo utilizando {} en la lista de resultados.\n;3:Formato experimental utilizando [] en la lista de resultados.", "smw-api-invalid-parameters": "Parámetros no válidos: \"$1\"", "smw-parser-recursion-level-exceeded": "Se ha excedido el límite de $1 recursiones durante el análisis. Se sugiere validar la estructura de la plantilla o, de ser preciso, ajustar el parámetro <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "Se {{PLURAL:$1|muestra $1 página que utiliza|muestran $1 páginas que utilizan}} esta propiedad.", "smw-property-page-list-search-count": "Se {{PLURAL:$1|muestra $1 página que utiliza|muestran $1 páginas que utilizan}} esta propiedad con una correspondencia de valor «$2».", "smw-property-reserved-category": "Categoría", "smw-category": "Categoría", "smw-datavalue-uri-invalid-scheme": "No se incluyó «$1» en los esquemas de URI válidos.", "smw-browse-property-group-title": "Grupo de propiedades", "smw-browse-property-group-label": "Etiqueta de grupo de propiedades", "smw-browse-property-group-description": "Descripción de grupo de propiedades", "smw-property-predefined-ppgr": "«$1» es una propiedad predefinida, provista por [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], que identifica entidades (principalmente categorías) empleadas como instancias de agrupamiento para las propiedades.", "smw-filter": "Filtro", "smw-section-expand": "Expandir la sección", "smw-section-collapse": "Contraer la sección", "smw-ask-format-help-link": "Formato [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Hoja de referencia", "smw-personal-jobqueue-watchlist": "Lista de seguimiento (cola de tareas)", "smw-property-predefined-label-skey": "Clave de ordenación", "smw-processing": "Procesando…", "smw-loading": "Cargando...", "smw-preparing": "Preparando...", "smw-expand": "Expandir", "smw-collapse": "<PERSON><PERSON><PERSON>", "smw-copy": "Copiar", "smw-jsonview-search-label": "Buscar:", "smw-redirect-target-unresolvable": "El destino es irresoluble por el motivo «$1»", "smw-types-title": "Tipo: $1", "smw-schema-error-title": "{{PLURAL:$1|Error|Errores}} de validación", "smw-schema-error-validation-json-validator-inaccessible": "No se puede acceder a (o no se ha instalado) el validador de JSON «<b>$1</b>»; este es el motivo por el cual no puede examinarse el archivo «$2», lo cual no permite guardar ni modificar la página actual.", "smw-schema-summary-title": "Resumen", "smw-schema-type": "T<PERSON><PERSON> de <PERSON>", "smw-schema-type-description": "Descripción del tipo", "smw-schema-description": "Descripción del esquema", "smw-schema-description-property-profile-schema": "Este tipo de esquema soporta la definición de un perfil para declarar características a la propiedad asignada y sus valores de anotación.", "smw-ask-title-keyword-type": "Búsqueda de palabras clave", "smw-ask-message-keyword-type": "Esta búsqueda coincide con la condición <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "No se puede conectar con el destino remoto «$1».", "smw-remote-source-disabled": "El origen '''$1''' ha desactivado las solicitudes remotas.", "smw-remote-source-unmatched-id": "La fuente '''$1''' no concuerda con la versión de MediaWiki Semántica que soporta solicitudes remotas.", "smw-remote-request-note": "El resultado se recupera a partir del origen remoto '''$1'''. Es probable que el contenido generado contenga información no disponible en la wiki actual.", "smw-remote-request-note-cached": "El resultado se '''almacena temporalmente''' a partir del origen remoto '''$1'''. Es probable que el contenido generado contenga información no disponible en la wiki actual.", "smw-parameter-missing": "Falta el parámetro \"$1\".", "smw-property-tab-usage": "<PERSON><PERSON>", "smw-property-tab-redirects": "Sinónimos", "smw-property-tab-subproperties": "Subpropiedades", "smw-property-tab-errors": "Asignaciones incorrectas", "smw-property-tab-constraint-schema": "Esquema de restricción", "smw-property-tab-specification": "... más", "smw-concept-tab-list": "Lista", "smw-concept-tab-errors": "Errores", "smw-ask-tab-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-extra": "Extras", "smw-ask-tab-debug": "Depuración", "smw-ask-tab-code": "Código", "smw-pendingtasks-setup-tasks": "<PERSON><PERSON><PERSON>", "smw-legend": "Leyenda", "smw-entity-examiner-deferred-constraint-error": "Restricción", "smw-entity-examiner-associated-revision-mismatch": "Revisión", "smw-indicator-constraint-violation": "{{PLURAL:$1|Restricción|Restricciones}}", "smw-search-placeholder": "Buscar...", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "Abajo se muestran {{PLURAL:$1|<strong>1</strong> resultado|hasta <strong>$1</strong> resultados}} comenzando con el n.º <strong>$2</strong>."}