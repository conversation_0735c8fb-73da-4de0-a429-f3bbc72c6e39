{"@metadata": {"authors": ["<PERSON><PERSON>", "Danieldegroot2", "DengroWikibase", "Ecthelion3", "<PERSON><PERSON>", "Festina90", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hex", "Kghbln", "<PERSON><PERSON><PERSON>", "KlaasZ4usV", "<PERSON><PERSON><PERSON>", "Mainframe98", "Mar(c)", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Nemo bis", "Optilete", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rangekill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "SPQRobin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tjcool007", "Wiki13", "Xbaked potatox"]}, "smw-desc": "Uw wiki toegankelijker maken – voor machines ''en'' mensen ([https://www.semantic-mediawiki.org/wiki/Help:User_manual online documentatie])", "smw-error": "Fout", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] is geïnstalleerd en ingeschakeld, maar er ontbreekt een geschikte [https://www.semantic-mediawiki.org/wiki/Help:Upgrade upgrade-sleutel].", "smw-upgrade-release": "Uitgave", "smw-upgrade-progress": "Voortgang", "smw-upgrade-progress-explain": "Het is moeilijk te voorspellen wanneer het opwaarderen voltooid zal zijn. Het hangt af van de grootte van de gegevensopslag en de beschikbare apparatuur. Voor grotere wiki’s kan het even duren.\n\nVoor meer informatie over de voortgang neemt u contact op met uw lokale beheerder.", "smw-upgrade-progress-create-tables": "Tabellen en indexen aanmaken (of bijwerken)…", "smw-upgrade-progress-post-creation": "Taken voor na de a<PERSON>ak uit<PERSON>eren…", "smw-upgrade-progress-table-optimization": "Tabellen optimaliseren…", "smw-upgrade-progress-supplement-jobs": "<PERSON><PERSON><PERSON><PERSON><PERSON> taken toevoegen…", "smw-upgrade-error-title": "Fout » Semantic MediaWiki", "smw-upgrade-error-why-title": "Waarom zie ik deze pagina?", "smw-upgrade-error-why-explain": "De interne databasestructuur van Semantic MediaWiki is gewijzigd en vereist enkele aanpassingen om volledig te kunnen werken. Er kunnen verschillende redenen zijn, waaronder:\n* Er zijn vaste eigenschappen toegevoegd (vereist extra tabelinstellingen)\n* Een upgrade bevat wijzigingen aan tabellen of indices waardoor een onderschepping verplicht is voordat toegang tot de gegevens wordt verkregen\n* Wijzigingen in het opslag- of query-systeem", "smw-upgrade-error-how-title": "Hoe los ik deze fout op?", "smw-upgrade-error-how-explain-admin": "<PERSON><PERSON> beheerder (of een persoon met beheerde<PERSON><PERSON><PERSON>) moet het onderhoudsscript [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] van MediaWiki of [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] van Semantic MediaWiki uitvoeren.", "smw-upgrade-error-how-explain-links": "U kunt ook de volgende pagina’s raadplegen voor verdere hulp:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation Installatie-instructies]\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting Hulppagina probleemoplossing]", "smw-extensionload-error-why-title": "Waarom zie ik deze pagina?", "smw-extensionload-error-why-explain": "De uitbreiding is <b>niet</b> gel<PERSON><PERSON> met behul<PERSON> van <code>enableSemantics</code> en is in plaats daarvan op een andere manier ingeschakeld, zoals door rechtstreeks gebruik van <code>wfLoadExtension( 'SemanticMediaWiki' )</code>.", "smw-extensionload-error-how-title": "Hoe los ik deze fout op?", "smw-extensionload-error-how-explain": "Om de uitbreiding in te schakelen en problemen met naamru<PERSON>te-declaraties en in behandeling zijnde configuraties te voorkomen, is het noodzakelijk om <code>enableSemantics</code> te gebruiken. Dit zorgt ervoor dat de vereiste variabelen worden klaargezet voordat de uitbreiding via de <code>ExtensionRegistry</code> wordt geladen.\n\nKijk eens naar de hulppagina [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] voor verdere inlichtingen.", "smw-upgrade-maintenance-title": "Onderhoud » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Waarom zie ik deze pagina?", "smw-upgrade-maintenance-note": "Het systeem ondergaat momenteel een [https://www.semantic-mediawiki.org/wiki/Help:Upgrade opwaardering] van de uitbreiding [https://www.semantic-mediawiki.org/ Semantic MediaWiki] alsmede de bijbehorende gegevensopslag. Even geduld alstublieft; na het onderhoud kan de wiki weer toegankelijk worden gemaakt.", "smw-upgrade-maintenance-explain": "De uitbreiding probeert hinder en uitval te beperken door de meeste van haar onderhoudstaken uit te stellen tot na het uitvoeren van <code>update.php</code>, maar sommige wijzigingen aan de database moeten eerst worden afgewerkt om tegenstrijdigheden in de gegevens te voorkomen. Dit kan het volgende omvatten:\n* Het wijzigen van tabelstructuren zoals het toevoegen van nieuwe of het wijzigen van bestaande velden\n* Het wijzigen of toevoegen van tabelindices\n* Het optimaliseren van de tabellen (indien ingeschakeld)", "smw-semantics-not-enabled": "Semantic MediaWiki-functionaliteit is niet ingeschakeld voor deze wiki.", "smw_viewasrdf": "RDF-feed", "smw_finallistconjunct": " en", "smw-factbox-head": "… meer over “$1”", "smw-factbox-facts": "<PERSON><PERSON><PERSON>", "smw-factbox-facts-help": "Toon verklaringen en feiten die door een gebruiker zijn a<PERSON>t", "smw-factbox-attachments": "Bijlagen", "smw-factbox-attachments-value-unknown": "N.v.t.", "smw-factbox-attachments-is-local": "Is lokaal", "smw-factbox-attachments-help": "Toont beschikbare bi<PERSON>lagen", "smw-factbox-facts-derived": "Afgeleide feiten", "smw-factbox-facts-derived-help": "Toont feiten die zijn afgeleid uit regels of met behulp van andere redeneertechnieken", "smw_isspecprop": "<PERSON><PERSON> is een speciale eigenschap in deze wiki.", "smw-concept-cache-header": "Cachegebruik", "smw-concept-cache-count": "De [https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count conceptcache] bevat {{PLURAL:$1|'''één''' entiteit|'''$1''' entiteiten}} ($2).", "smw-concept-no-cache": "Geen cache besch<PERSON>.", "smw_concept_description": "Beschrijving van concept \"$1\"", "smw_no_concept_namespace": "Concepten kunnen alleen op pagina’s in de naamru<PERSON> ‘Concept:’ gedefinieerd worden.", "smw_multiple_concepts": "Iedere conceptpagina kan maar één conceptdefinitie bevatten.", "smw_concept_cache_miss": "Het concept \"$1\" kan op het moment niet gebruikt worden, omdat de wiki-configuratie vereist dat deze offline wordt berekend.\nAls het probleem over enige tijd nog niet verholpen is, vraag uw sitebeheerder dan om dit concept beschikbaar te maken.", "smw_noinvannot": "Waarden kunnen niet toegekend worden aan omgekeerde eigenschappen.", "version-semantic": "Semantische uitbreidingen", "smw_baduri": "URI's van het formaat \"$1\" zijn niet toe<PERSON>.", "smw_printername_count": "Resultaten tellen", "smw_printername_csv": "Naar CSV exporteren", "smw_printername_dsv": "Naar DSV exporteren", "smw_printername_debug": "Query debuggen (voor experts)", "smw_printername_embedded": "Paginainhoud insluiten", "smw_printername_json": "Naar JSON exporteren", "smw_printername_list": "Lijst", "smw_printername_plainlist": "Gewone lijst", "smw_printername_ol": "Genummerde lijst", "smw_printername_ul": "<PERSON><PERSON><PERSON> met opsommingstekens", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON>e tabel", "smw_printername_template": "Sjabloon", "smw_printername_templatefile": "Sjabloonbestand", "smw_printername_rdf": "Naar RDF exporteren", "smw_printername_category": "Categorie", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-limit": "Het maximaal aantal weer te geven resultaten", "smw-paramdesc-offset": "De positie van het eerste resultaat", "smw-paramdesc-headers": "De <PERSON>n van koppen en eigenschappen weergeven", "smw-paramdesc-mainlabel": "Het label voor de hoofdpaginanaam", "smw-paramdesc-link": "Waarden als koppelingen weergeven", "smw-paramdesc-intro": "De tekst die wordt weergegeven boven de zoekresultaten, als die er zijn", "smw-paramdesc-outro": "De tekst die wordt weergegeven onder de zoekresultaten, als die er zijn", "smw-paramdesc-default": "De tekst die wordt weergegeven als er geen zoekresultaten zijn", "smw-paramdesc-sep": "Het scheidingsteken voor resultaten", "smw-paramdesc-propsep": "Het scheidingsteken tussen de eigenschappen van een resultaatvermelding", "smw-paramdesc-valuesep": "Het scheidingsteken tussen de waarden voor een eigenschap van een resultaat", "smw-paramdesc-showsep": "Het scheidingsteken bovenaan het CSV-bestand weergeven (\"sep=<waarde>\")", "smw-paramdesc-distribution": "<PERSON><PERSON> de <PERSON> waarden weer in plaats van alle waarden.", "smw-paramdesc-distributionsort": "De waardeverdeling sorteren op aantal keer dat die voorkomt.", "smw-paramdesc-distributionlimit": "De waardeverdeling beperken tot alleen het aantal van bepaalde waarden.", "smw-paramdesc-aggregation": "Geeft aan waar de aggregatie betrekking op moet hebben", "smw-paramdesc-template": "De naam van een sjab<PERSON><PERSON> waarmee de afdrukken moeten worden weergegeven", "smw-paramdesc-columns": "Het aantal kolommen waarin de resultaten weergegeven moeten worden", "smw-paramdesc-userparam": "Een aan elke sjabloon-a<PERSON><PERSON><PERSON> doorgegeven waarde, als er een sjabloon wordt gebruikt", "smw-paramdesc-class": "Een extra CSS-klasse die voor de lijst kan worden ingesteld", "smw-paramdesc-introtemplate": "De naam van een boven de zoekresultaten weer te geven sjabloon als er zoekresultaten zijn", "smw-paramdesc-outrotemplate": "De naam van een onder de zoekresultaten weer te geven sjabloon als er zoekresultaten zijn", "smw-paramdesc-embedformat": "Het HTML-element voor het definiëren van sectiekoppen", "smw-paramdesc-embedonly": "<PERSON><PERSON> koppen weergeven", "smw-paramdesc-table-class": "Een extra CSS-klasse om voor de tabel in te stellen", "smw-paramdesc-table-transpose": "Tabelkoppen verticaal en resultaten horizontaal weergeven", "smw-paramdesc-prefix": "De weergave van de naamruimte bij afdrukken instellen", "smw-paramdesc-rdfsyntax": "De te gebruiken RDF-syntaxis", "smw-paramdesc-csv-sep": "Geeft een kolomscheidingsteken aan", "smw-paramdesc-csv-valuesep": "Geeft een waardescheidingsteken aan", "smw-paramdesc-csv-merge": "Voeg rijen en kolomwaardes met een identiek onderwerp-ID (oftewel eerste kolom) samen", "smw-paramdesc-csv-bom": "Voeg een B<PERSON> (teken om endianness aan te geven) toe aan het begin van het uitvoerbestand", "smw-paramdesc-dsv-separator": "Het te gebruiken scheidingsteken", "smw-paramdesc-dsv-filename": "De naam voor het DSV-bestand", "smw-paramdesc-filename": "De naam voor het uitvoerbestand", "smw-smwdoc-description": "Geeft een tabel weer met alle parameters die kunnen worden gebruikt voor de opgegeven resultaatopmaak samen met standaardwaarden en beschrijvingen.", "smw-smwdoc-default-no-parameter-list": "Deze resultaatindeling biedt geen indelingsspecifieke parameters.", "smw-smwdoc-par-format": "De resultaatopmaak waarvoor parameterdocumentatie weergegeven moet worden.", "smw-smwdoc-par-parameters": "Welke parameters weer te geven; \"specific\" voor degene toegevoegd door de opmaak, \"base\" voor degene die beschikbaar zijn in alle opmaken, en \"all\" voor beide.", "smw-paramdesc-sort": "Eigenschap om de zoekopdracht op te sorteren", "smw-paramdesc-order": "Sorteervolgorde", "smw-paramdesc-searchlabel": "Tekst voor het voortzetten van de zoekopdracht", "smw-paramdesc-named_args": "No<PERSON> de a<PERSON> de sja<PERSON>on door te geven parameters", "smw-paramdesc-template-arguments": "St<PERSON>t in hoe de benoemde argumenten aan de sjabloon worden doorgegeven", "smw-paramdesc-import-annotation": "Aanvullende geannote<PERSON>e gegevens zullen gekopieerd worden tijdens het parseren van een onderwerp", "smw-paramdesc-export": "Export<PERSON><PERSON>", "smw-paramdesc-prettyprint": "<PERSON><PERSON> netjes opgemaakte uitvoer inschakelen die tabs en nieuwe regels toevoegt", "smw-paramdesc-json-unescape": "De uitvoer bevat slashes en multibyte Unicode-tekens die niet voorafgegaan worden door een escape", "smw-paramdesc-json-type": "Serialisatietype", "smw-paramdesc-source": "Alternatieve zoekopdrachtbron", "smw-paramdesc-jsonsyntax": "Te gebruiken JSON-syntaxis", "smw-printername-feed": "RSS- en Atom-feed", "smw-paramdesc-feedtype": "Feedtype", "smw-paramdesc-feedtitle": "De tekst die als titel van de feed moet worden gebruikt", "smw-paramdesc-feeddescription": "De tekst die als beschrijving van de feed moet worden gebruikt", "smw-paramdesc-feedpagecontent": "In de feed weer te geven paginainhoud", "smw-label-feed-description": "$2-feed $1", "smw-paramdesc-mimetype": "Het mediatype (MIME-type) voor het uitvoerbestand", "smw_iq_disabled": "Zoekopdrachten binnen tekst zijn uitgeschakeld in deze wiki.", "smw_iq_moreresults": "... meer resultaten", "smw_parseerror": "De opgegeven waarde is niet begrepen.", "smw_decseparator": ",", "smw_kiloseparator": ".", "smw_notitle": "\"$1\" kan in deze wiki niet als paginanaam gebruikt worden.", "smw_noproperty": "\"$1\" kan in deze wiki niet als eigenschapsnaam gebruikt worden.", "smw_wrong_namespace": "<PERSON>er zijn alleen pagina's in naamruimte \"$1\" toegestaan.", "smw_manytypes": "<PERSON><PERSON> dan <PERSON> type gedefinieerd voor eigenschap.", "smw_emptystring": "Lege strings zijn niet toe<PERSON>.", "smw_notinenum": "“$1” staat niet in de lijst met [[Property:Allows value|mogelijke waarden]] voor de eigenschap “$3” ($2).", "smw-datavalue-constraint-error-allows-value-list": "“$1” staat niet in de lijst ($2) van [[Property:Allows value|toegestane waarden]] voor de eigenschap “$3”.", "smw-datavalue-constraint-error-allows-value-range": "“$1” valt niet binnen het bereik van “$2” dat is opgegeven door de beperking [[Property:Allows value|toegestane waarden]] voor de eigenschap “$3”.", "smw-constraint-error": "Beperkingsprobleem", "smw-constraint-error-suggestions": "Controleer de vermelde overtredingen en eigenschappen samen met hun gean<PERSON><PERSON>e waarden om er zeker van te zijn dat aan alle beperkingsvereisten wordt voldaan.", "smw-constraint-error-limit": "De lijst bevat maximaal $1 overtredingen.", "smw_noboolean": "\"$1\" is niet herkend als een booleaanse waarde (waar/onwaar).", "smw_true_words": "waar,w,ja,j,true", "smw_false_words": "onwaar,o,nee,n,false", "smw_nofloat": "\"$1\" is geen getal.", "smw_infinite": "Getallen zo groot als \"$1\" worden niet ondersteund.", "smw_unitnotallowed": "\"$1\" is niet ingesteld als een geldige meeteenheid voor deze eigenschap.", "smw_nounitsdeclared": "Er zijn geen meeteenheden opgegeven voor deze eigenschap.", "smw_novalues": "<PERSON>n waarden opgegeven.", "smw_nodatetime": "De datum \"$1\" kon niet verwerkt worden.", "smw_toomanyclosing": "\"$1\" lijkt te vaak voor te komen in de zoekopdracht.", "smw_noclosingbrackets": "<PERSON><PERSON><PERSON> in uw zoekopdracht was \"<nowiki>[[</nowiki>\" niet afgesloten door een bijbehorende \"]]\".", "smw_misplacedsymbol": "Het symbool \"$1\" is gebruikt op een plaats waar het niet gebruikt hoort te worden.", "smw_unexpectedpart": "Het de<PERSON> \"$1\" van de z<PERSON>dracht is niet begrepen.\nDe resultaten kunnen afwijken van de verwachting.", "smw_emptysubquery": "Er is een sub<PERSON><PERSON> met een onjuiste conditie.", "smw_misplacedsubquery": "Er is een subzoekopdracht gebruikt op een plaats waar subzoekopdrachten niet gebruikt mogen worden.", "smw_valuesubquery": "Subzoekopdrachten worden niet ondersteund voor waarden van de eigenschap \"$1\".", "smw_badqueryatom": "<PERSON><PERSON> on<PERSON> \"<nowiki>[[...]]</nowiki>\" van de z<PERSON> is niet begrepen.", "smw_propvalueproblem": "De waarde van eigenschap \"$1\" is niet begrepen.", "smw_noqueryfeature": "<PERSON>en bepaalde vraagoptie wordt niet ondersteund in deze wiki en een deel van de z<PERSON>kopdracht is genegeerd ($1).", "smw_noconjunctions": "Verbindingen in zoekopdrachten worden in deze wiki niet ondersteund en een deel van de zoekopdracht is genegeerd ($1).", "smw_nodisjunctions": "Scheidingen in zoekopdrachten worden niet ondersteund in deze wiki, dus een deel van de zoekopdracht is genegeerd ($1).", "smw_querytoolarge": "De volgende {{PLURAL:$2|zoekopdrachtconditie is|$2 zoekopdrachtcondities zijn}} niet in acht genomen vanwege beperkingen in de grootte of diepte van zoekopdrachten in deze wiki: <code>$1</code>.", "smw_notemplategiven": "<PERSON>f een waarde voor de parameter \"sjabloon\" op om deze zoekopdracht te laten werken.", "smw_db_sparqlqueryproblem": "Er is geen antwoord op de zoekopdracht gekomen uit de SPARQL-database. Deze fout kan tij<PERSON>ijk zijn of wijzen op een bug in de databasesoftware.", "smw_db_sparqlqueryincomplete": "De zoekopdracht beantwoorden bleek te moeilijk en deze is afgebroken. Sommige resultaten kunnen ontbreken. Probeer een eenvoudiger zoekopdracht te gebruiken.", "smw_type_header": "Eigenschappen van het type \"$1\"", "smw_typearticlecount": "{{PLURAL:$1|Eén eigenschap gebruikt|$1 eigenschappen gebruiken}} dit type.", "smw_attribute_header": "<PERSON><PERSON><PERSON>'s die de eigenschap \"$1\" gebruiken", "smw_attributearticlecount": "{{PLURAL:$1|Eén pagina gebruikt|$1 pagina's gebruiken}} deze eigenschap.", "smw-propertylist-subproperty-header": "Subeigenschappen", "smw-propertylist-redirect-header": "Synoniemen", "smw-propertylist-error-header": "<PERSON><PERSON><PERSON>'s met <PERSON><PERSON><PERSON><PERSON>", "smw-propertylist-count": "$1 gerelateerde {{PLURAL:$1|entiteit|entiteiten}} weergegeven.", "smw-propertylist-count-with-restricted-note": "$1 gerelateerde {{PLURAL:$1|entiteit|entiteiten}} weergegeven (er zijn er meer beschik<PERSON>, maar de weergave is beperkt tot \"$2\").", "smw-propertylist-count-more-available": "$1 gerelateerde {{PLURAL:$1|entiteit|entiteiten}} weergegeven (er zijn er meer besch<PERSON>).", "specialpages-group-smw_group-maintenance": "Onderhoud", "specialpages-group-smw_group-properties-concepts-types": "Eigenschappen, concepten en typen", "specialpages-group-smw_group-search": "<PERSON>ren en zoeken", "exportrdf": "Pagina's exporteren naar RDF", "smw_exportrdf_docu": "Deze pagina maakt het mogelijk gegevens te verkrijgen van een pagina in RDF-formaat.\nOm pagina's te exporteren, voert u in het onderstaande invoerveld paginanamen in, <PERSON><PERSON> paginanaam per regel.", "smw_exportrdf_recursive": "Alle gerelateerde pagina's recursief exporteren.\nHet resultaat kan groot zijn!", "smw_exportrdf_backlinks": "Ook alle pagina's exporteren die verwijzen naar de te exporteren pagina's.\nGenereert doorbladerbare RDF.", "smw_exportrdf_lastdate": "<PERSON>gin<PERSON>'s die sinds het opgegeven tijdstip niet gewij<PERSON>d zijn, niet exporteren.", "smw_exportrdf_submit": "Exporteren", "uriresolver": "URI-resolver", "properties": "Eigenschappen", "smw-categories": "Categorieën", "smw_properties_docu": "De volgende eigenschappen worden in de wiki gebruikt.", "smw_property_template": "$1 van type $2 ($3 keer {{PLURAL:$3|gebruikt}})", "smw_propertylackspage": "Alle eigenschappen moeten op een pagina beschreven worden!", "smw_propertylackstype": "Er is geen type opgegeven voor deze eigenschap (type $1 wordt verondersteld).", "smw_propertyhardlyused": "Deze eigenschap wordt in de wiki vrijwel niet gebruikt!", "smw-property-name-invalid": "Eigenschap $1 kan niet gebruikt worden (ongeldige eigenschapsnaam).", "smw-property-name-reserved": "\"$1\" werd vermeld als gereserveerde naam en mag niet worden gebruikt als een eigenschap. De volgende [https://www.semantic-mediawiki.org/wiki/Help:Property_naming helppagina] kan informatie bevatten over waarom deze naam was gereserveerd.", "smw-sp-property-searchform": "Eigenschappen weergeven die de volgende tekst bevatten:", "smw-sp-property-searchform-inputinfo": "De invoer is hoofdlettergevoelig, en als deze wordt gebruikt voor filteren dan worden alleen eigenschappen weergegeven die overeenkomen met de voorwaarde.", "smw-special-property-searchform": "Eigenschappen weergeven die de volgende waarde bevatten:", "smw-special-property-searchform-inputinfo": "De invoer is hoofdlettergevoelig, en als deze wordt gebruikt voor filteren dan worden alleen eigenschappen weergegeven die overeenkomen met de voorwaarde.", "smw-special-property-searchform-options": "Opties", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "<PERSON><PERSON> go<PERSON>", "smw-special-wantedproperties-filter-unapproved-desc": "Filteroptie die wordt gebruikt in verband met de autorisatiemodus.", "concepts": "<PERSON><PERSON>", "smw-special-concept-docu": "<PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Concepts concept] kan gezien worden als \"dynamische categorie\", dat wil zeggen als een verzameling pagina's die niet handmatig zijn aangema<PERSON>t, maar zijn berekend door Semantic MediaWiki uit een beschrijving van een gegeven zoekopdracht.", "smw-special-concept-header": "<PERSON><PERSON><PERSON> van concepten", "smw-special-concept-count": "{{PLURAL:$1|Het volgende concept wordt|De volgende $1 concepten worden}} weergegeven.", "smw-special-concept-empty": "Er is geen concept gevonden.", "unusedproperties": "Ongebruikte eigenschappen", "smw-unusedproperties-docu": "Op deze pagina vindt u [https://www.semantic-mediawiki.org/wiki/Unused_properties ongebruikte eigenschappen] die bestaan hoewel ze op geen andere pagina gebruikt worden. Voor een andere weergave, zie de speciale pagina's met [[Special:Properties|alle]] of [[Special:WantedProperties|gewenste eigenschappen]].", "smw-unusedproperty-template": "$1 van type $2", "wantedproperties": "Gewenste eigenschappen", "smw-wantedproperties-docu": "Op deze pagina vindt u [https://www.semantic-mediawiki.org/wiki/Wanted_properties gewenste eigenschappen] die in de wiki worden gebruikt, maar geen pagina hebben waarop ze worden beschreven. Voor een andere weergave, zie de speciale pagina's met [[Special:Properties|alle]] of [[Special:UnusedProperties|ongebruikte eigenschappen]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|keer|keren}} gebruikt)", "smw-special-wantedproperties-docu": "Op deze pagina vindt u [https://www.semantic-mediawiki.org/wiki/Wanted_properties gewenste eigenschappen] die in de wiki worden gebruikt, maar geen pagina hebben waarop ze worden beschreven. Voor een gedifferentieerde weergave, zie de speciale pagina's met [[Special:Properties|alle]] of [[Special:UnusedProperties|ongebruikte eigenschappen]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|keer|keren}} gebruikt)", "smw_purge": "<PERSON><PERSON><PERSON><PERSON>", "smw-purge-update-dependencies": "Semantic MediaWiki is deze pagina opnieuw aan het opbouwen, want er zijn een aantal verouderde afhankelijkheden ontdekt waardoor de pagina bijgewerkt moet worden.", "smw-purge-failed": "Semantic MediaWiki probeerde de pagina op te schonen, maar dat lukte niet", "types": "Types", "smw_types_docu": "<PERSON><PERSON><PERSON> <PERSON> [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes beschikbare datatypes] wa<PERSON><PERSON> elk [https://www.semantic-mediawiki.org/wiki/Help:Datatype type] een unieke set attributen vertegenwoordigt om een waarde te beschrijven in termen van opslag en weergavekenmerken die erfelijk zijn voor een toegewezen eigenschap.", "smw-special-types-no-such-type": "\"$1\" is onbekend of is niet opgegeven als geldig datatype.", "smw-statistics": "Semantische statistieken", "smw-statistics-cached": "Semantische statistieken (uit de cache)", "smw-statistics-entities-total": "Entiteiten (totaal)", "smw-statistics-entities-total-info": "<PERSON>en geschat aantal rijen met en<PERSON><PERSON><PERSON>. Het omvat eigenschappen, concepten of andere geregistreerde objectrepresentaties waarvoor een ID-toewijzing vereist is.", "smw-statistics-property-instance": "Eigenschapswaarde{{PLURAL:$1||n}} (totaal)", "smw-statistics-property-total": "[[Special:Properties|Eigenschap{{PLURAL:$1||pen}}]] (totaal)", "smw-statistics-property-total-info": "Het totaal aan geregistreerde eigenschappen.", "smw-statistics-property-total-legacy": "Eigenschap{{PLURAL:$1||pen}} (totaal)", "smw-statistics-property-used": "{{PLURAL:$1|Eigenschap|Eigenschappen}} (<PERSON><PERSON><PERSON><PERSON><PERSON> met ten minste <PERSON><PERSON> waarde)", "smw-statistics-property-page": "Eigenschap{{PLURAL:$1||pen}} (geregistreerd bij een pagina)", "smw-statistics-property-page-info": "Het aantal eigenschappen met een eigen pagina en beschrijving.", "smw-statistics-property-type": "Eigenschap{{PLURAL:$1||pen}} (toegewezen aan een datatype)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Zoekopdracht|Zoekopdrachten}}", "smw-statistics-query-inline": "[[Property:Has query|Zoekopdracht{{PLURAL:$1||en}}]] (ingebed, totaal)", "smw-statistics-query-format": "formaat <code>$1</code>", "smw-statistics-query-size": "<PERSON><PERSON><PERSON> van z<PERSON>dracht", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Concept|Concepten}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Concept|Concepten}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Subobject|Subobjecten}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Subobject|Subobjecten}}", "smw-statistics-datatype-count": "[[Special:Types|Datatype{{PLURAL:$1||s}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Eigenschapswaarde|Eigenschapswaarden}} ([[Special:ProcessingErrorList|{{PLURAL:$1|ongeldige annotatie|ongeldige annotaties}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Eigenschapswaarde|Eigenschapswaarden}} ({{PLURAL:$1|verkeerde annotatie|verkeerde annotaties}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities Verouderde {{PLURAL:$1|entiteit|entiteiten}}]", "smw-statistics-delete-count-info": "De voor verwijdering gemarkeerde entiteiten moeten regelmatig verwijderd worden met be<PERSON><PERSON> van de meegeleverde onderhoudsscripts.", "smw_uri_doc": "In de URI-resolver is de [$1 bevinding van de Groep Technische Architectuur van de W3C inzake httpRange-14] doorgevoerd.\nHierd<PERSON> wordt, a<PERSON><PERSON><PERSON><PERSON><PERSON> van het ve<PERSON>, een RDF-weergave (voor machines) of een wikipagina (voor mensen) geleverd.", "ask": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>ken", "smw-ask-help": "Dit gedeelte bevat een aantal koppelingen om uit te leggen hoe de syntaxis <code>#ask</code> moet worden gebruikt:\n\n* in [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Pagina's selecteren] wordt beschreven hoe u pagina's selecteert en selectiecriteria formuleert;\n\n* in [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Zoekoperators] worden de beschikbare zoekoperators opgesomd, waaronder die voor zoekopdrachten voor een bepaald bereik en met jokertekens;\n\n* in [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Informatie weergeven] wordt het gebruik van afdrukinstructies en opmaakopties beschreven.", "smw_ask_sortby": "Sorteren op kolom (optioneel)", "smw_ask_ascorder": "Oplopend", "smw_ask_descorder": "Aflopend", "smw-ask-order-rand": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Resultaten zoeken", "smw_ask_editquery": "Zoekopdracht bewerken", "smw_add_sortcondition": "[Sorteervoorwaarde toevoegen]", "smw-ask-sort-add-action": "Sorteervoorwaarde toevoegen", "smw_ask_hidequery": "Zoekopdracht verbergen (compacte weergave)", "smw_ask_help": "Hulp bij zoekopdrachten", "smw_ask_queryhead": "Voorwaarde", "smw_ask_printhead": "Print selectie", "smw_ask_printdesc": "(voeg <PERSON><PERSON> eigenscha<PERSON> per regel toe)", "smw_ask_format_as": "Opmaken als:", "smw_ask_defaultformat": "standaard", "smw_ask_otheroptions": "Andere opties", "smw-ask-otheroptions-info": "Dit onderdeel bevat opties die afdrukinstructies wijzigen. Parameterbeschrijvingen worden zichtbaar door de muisaanwi<PERSON> erover te bewegen.", "smw-ask-otheroptions-collapsed-info": "Gebruik het plusicoon om alle beschikbare opties te bekijken", "smw_ask_show_embed": "Insluitcode weergeven", "smw_ask_hide_embed": "Insluitcode verbergen", "smw_ask_embed_instr": "Gebruik de onderstaande code om deze zoekopdracht op te nemen in een wikipagina.", "smw-ask-delete": "Verwijderen", "smw-ask-sorting": "Sortering", "smw-ask-options": "Opties", "smw-ask-options-sort": "Sorteeropties", "smw-ask-format-options": "Formaat en opties", "smw-ask-parameters": "Parameters", "smw-ask-search": "<PERSON><PERSON>", "smw-ask-debug": "Debugg<PERSON>", "smw-ask-debug-desc": "Genereert debuginformatie voor zoekopdrachten", "smw-ask-no-cache": "Querycache uitschakelen", "smw-ask-no-cache-desc": "Resultaten zonder querycache", "smw-ask-result": "Resultaat", "smw-ask-empty": "Alle vermeldingen wissen", "smw-ask-download-link-desc": "Download opgevraagde resultaten in $1-formaat", "smw-ask-format": "Indeling", "smw-ask-format-selection-help": "Hulp voor het geselecteerde formaat: $1.", "smw-ask-condition-change-info": "De voorwa<PERSON>e is gewijzigd en de zoekmachine moet de zoekopdracht opnieuw uitvoeren om resultaten te verkrijgen die aan de nieuwe vereisten voldoen.", "smw-ask-input-assistance": "<PERSON>lp bij invoer", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Hulp bij invoer] wordt geleverd voor het afdruk-, sorteer- en conditieveld. Voor het conditieveld moet een van de volgende voorvoegsels worden gebruikt:", "smw-ask-condition-input-assistance-property": "<code>p:</code> om suggesties voor eigenschappen op te halen (bijv. <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> om categoriesuggesties op te halen", "smw-ask-condition-input-assistance-concept": "<code>con:</code> om conceptsuggesties op te halen", "smw-ask-format-change-info": "Het uitvoerformaat is gewijzigd en de zoekopdracht moet opnieuw worden uitgevoerd zodat de resultaten aansluiten bij de nieuwe parameters en weergaveopties.", "smw-ask-format-export-info": "Het geselecteerde formaat is voor gegevensexport en heeft geen visuele weergave. De resultaten kunnen alleen worden gedownload.", "smw-ask-query-search-info": "<PERSON>cht <code><nowiki>$1</nowiki></code> is beantwoord door de {{PLURAL:$3|1=<code>$2</code> (uit de cache)|<code>$2</code> (uit de cache)|<code>$2</code>}} in $4 {{PLURAL:$4|seconde|seconden}}.", "smw-ask-extra-query-log": "Logboek zoekopdrachten", "smw-ask-extra-other": "Overige", "searchbyproperty": "Op eigenschap zoeken", "processingerrorlist": "<PERSON><PERSON><PERSON> met verwerkings<PERSON>uten", "constrainterrorlist": "<PERSON><PERSON><PERSON> met be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "propertylabelsimilarity": "Gelijkenisrapportage eigenschapslabels", "missingredirectannotations": "Ontbrekende omleidaantekeningen", "smw-processingerrorlist-intro": "De volgende lijst biedt een overzicht van [https://www.semantic-mediawiki.org/wiki/Processing_errors verwerkingsfouten] die in verband met [https://www.semantic-mediawiki.org/ Semantic MediaWiki] zijn opgetreden. Het wordt aanbevolen om deze lijst met enige regelmaat te controleren en ongeldige waardeannotaties te corrigeren.", "smw-constrainterrorlist-intro": "In de volgende lijst staat een overzicht van [https://www.semantic-mediawiki.org/wiki/Constraint_errors beperkingsfouten] die zijn verschenen in verband met [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Het wordt aanbevolen deze lijst regelmatig te controleren en annotaties met ongeldige waarden te verbeteren.", "smw-missingredirects-intro": "In de volgende sectie worden pagina’s vermeld waaraan [https://www.semantic-mediawiki.org/wiki/Redirects doorverwijzings-annotaties] ontbreken in Semantic MediaWiki (door deze te vergelijken met de in MediaWiki opgeslagen gegevens). Om deze annotaties handmatig te herstellen, [https://www.semantic-mediawiki.org/wiki/Help:Purge leegt u de cache] van de pagina of voert u het onderhoudsscript <code>rebuildData.php</code> uit (met optie <code>--redirects</code>).", "smw-missingredirects-list": "<PERSON><PERSON><PERSON> met ontbrekende aantekeningen", "smw-missingredirects-list-intro": "Er {{PLURAL:$1|wordt|worden}} $1 {{PLURAL:$1|pagina|pagina's}} met ontbrekende omleidaantekeningen weergegeven.", "smw-missingredirects-noresult": "Kan geen ontbrekende omleidaantekeningen vinden.", "smw_sbv_docu": "Alle pagina's zoeken die een bepaalde eigenschap en waarde hebben.", "smw_sbv_novalue": "Voer een geldige waarde in voor de eigenschap, of bekijk alle waarden voor eigenschap \"$1.\"", "smw_sbv_displayresultfuzzy": "<PERSON><PERSON> lij<PERSON> van alle pagina's met de eigenschap \"$1\" met waarde \"$2\".\nOmdat er een beperkt aantal resultaten is, worden ook nabije waarden weergegeven.", "smw_sbv_property": "Eigenschap:", "smw_sbv_value": "Waarde:", "smw_sbv_submit": "Resultaten zoeken", "browse": "Wiki bekijken", "smw_browselink": "Eigenschappen bekijken", "smw_browse_article": "<PERSON><PERSON><PERSON> de <PERSON> in van de pagina waar u met browsen wilt beginnen.", "smw_browse_go": "OK", "smw_browse_show_incoming": "Inkomende eigenschappen weergeven", "smw_browse_hide_incoming": "Inkomende eigenschappen verbergen", "smw_browse_no_outgoing": "Deze pagina heeft geen eigenschappen.", "smw_browse_no_incoming": "Er verwijzen geen eigenschappen naar deze pagina.", "smw-browse-from-backend": "Informatie wordt momenteel uit de backend gehaald.", "smw-browse-intro": "Deze pagina bevat informatie over een subject of instantie van een entiteit. <PERSON><PERSON><PERSON> de na<PERSON> in van een te inspecteren object.", "smw-browse-invalid-subject": "De onderwerpvalidatie gaf een \"$1\"-fout terug.", "smw-browse-api-subject-serialization-invalid": "Het onderwerp heeft een ongeldige serialisatie-formaat.", "smw-browse-js-disabled": "Het vermoeden bestaat dat JavaScript is uitgeschakeld of niet be<PERSON><PERSON> is. We raden aan gebruik te maken van een browser die JavaScript ondersteunt. Andere mogelijkheden worden besproken op de hulppagina over de configuratieparameter [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "<PERSON>n gro<PERSON>en", "smw-browse-hide-group": "Verberg groepen", "smw-noscript": "Deze pagina of handeling vereist JavaScript om te werken. Schakel JavaScript in uw browser in, of gebruik een browser waarin dit wordt ondersteund, zodat de functionaliteit naar behoren kan werken. Kijk voor meer informatie op de hulppagina [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 van", "smw_inverse_label_property": "Tegenovergesteld eigenschapslabel", "pageproperty": "Paginaeigenschap z<PERSON>ken", "pendingtasklist": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON> taken", "facetedsearch": "Gefacetteerd zoeken", "smw_pp_docu": "Voer een pagina en eigenschap in, of alleen een eigenschap om alle toegewezen waarden op te halen.", "smw_pp_from": "<PERSON> pagina:", "smw_pp_type": "Eigenschap:", "smw_pp_submit": "Resultaten zoeken", "smw-prev": "vorige {{PLURAL:$1|$1}}", "smw-next": "volgende {{PLURAL:$1|$1}}", "smw_result_prev": "Vorige", "smw_result_next": "Volgende", "smw_result_results": "Resultaten", "smw_result_noresults": "<PERSON><PERSON> resultaten.", "smwadmin": "Semantic MediaWiki-dashboard", "smw-admin-statistics-job-title": "Taakstatistieken", "smw-admin-statistics-job-docu": "De taakstatistieken geven informatie over geplande Semantic MediaWiki-taken die nog niet zijn uitgevoerd. Het aantal taken kan enigszins afwijken of mislukte pogingen bevatten. Raadpleeg de [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue handleiding] voor meer informatie.", "smw-admin-statistics-querycache-title": "Querycache", "smw-admin-statistics-querycache-disabled": "De [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] is niet ingeschakeld op deze wiki en daarom zijn er geen statistieken beschik<PERSON>ar.", "smw-admin-statistics-querycache-legend": "De cachestatistieken moeten zowel voorlopige cumulatieve als afgeleide gegevens bevatten, waaronder:\n* \"misses\" (missers), oftewel het totale aantal pogingen om gegevens uit de cache op te halen met onbereikbare antwoorden, waardoor ze direct uit een repository (DB, triple-store enz.) moeten worden opgehaald \n* \"deletes\" (verwijderingen), oftewel het totale aantal bewerkingen voor het legen van de cache (door opschonen dan wel query-afhankelijkheid)\n* \"hits\" (treffers) bevat het aantal cache-opvragingen van hetzij ingebedde bronnen (query's opgeroepen vanuit een wikipagina) of niet-ingebedde bronnen (indien ingeschakeld, opgevraagd door pagina's zoals Special:Ask of de API).\n* \"medianRetrievalResponseTime\" (mediaan van de reactietijd voor het ophalen) is een oriëntatiewaarde van de mediane responstijd (in sec.) voor gecachete en niet-gecachete ophaalverzoeken over de tijdspanne van het verzamelproces\n* \"noCache\" (geen cache) geeft het aantal verzoeken zonder poging aan (query's met limit=0, de optie 'no-cache', enz.) om resultaten uit de cache op te halen", "smw-admin-statistics-section-explain": "De sectie biedt aanvullende statistieken voor moderatoren.", "smw-admin-statistics-semanticdata-overview": "Overzicht", "smw-admin-permission-missing": "De toegang tot deze pagina is geblokkeerd wegens het ontbreken van de juiste rechten. Raadpleeg de [https://www.semantic-mediawiki.org/wiki/Help:Permissions rechten] help-pagina voor meer informatie over de vereiste instellingen.", "smw-admin-setupsuccess": "De opslagmodule is ingesteld.", "smw_smwadmin_return": "Terug naar $1", "smw_smwadmin_updatestarted": "Er is een proces gestart voor het bijwerken van de semantische gegevens.\nAlle opgeslagen gegevens worden opnieuw opgebouwd of gerepareerd als dat nodig is.\nU kunt de voortgang volgen via deze speciale pagina.", "smw_smwadmin_updatenotstarted": "Er loopt al een bijwerkproces.\nEr wordt geen nieuw proces gestart.", "smw_smwadmin_updatestopped": "Alle lopende bijwerkprocessen zijn afgebroken.", "smw_smwadmin_updatenotstopped": "Om de lopende processen te stoppen, moet u het vinkje inschakelen om aan te geven dat u het zeker weet.", "smw-admin-docu": "Deze speciale pagina helpt u tijdens de installatie en het bijwerken van <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a> en voorziet in verdere administratieve functies en taken, alsook statistieken.\nDenk eraan een reservekopie te maken van uw waardevolle gegevens voordat u beheerhandelingen verricht.", "smw-admin-environment": "Software omgeving", "smw-admin-db": "Database instellen", "smw-admin-db-preparation": "De initialisatie van de tabel is aan de gang en het kan even duren voordat de resultaten worden weergegeven, a<PERSON><PERSON><PERSON><PERSON><PERSON> van de grootte en mogelijke tabeloptimalisaties.", "smw-admin-dbdocu": "Semantic MediaWiki vereist zijn eigen databasestructuur (en is onafhankelijk van MediaWiki en heeft dus geen invloed op de rest van de MediaWiki-installatie) om de semantische gegevens op te slaan.\nDeze instellingsfunctie kan meerdere keren worden uitgevoerd zonder schade aan te richten, maar is slechts één keer nodig bij installatie of upgrade.", "smw-admin-permissionswarn": "Als de handeling mislukt en er SQL-foutmeldingen worden weergegeven, heeft de databasegebruiker van uw wiki waarschijnlijk onvoldoende rechten (zie het \"LocalSettings.php\"-bestand).\nGeef deze gebruiker de benodigde extra rechten om tabellen aan te maken en te verwijderen, zet tijdelijk de aanmeldgegevens van de hoof<PERSON>iker (root) van uw databasesysteem in het \"LocalSettings.php\"-bestand, of gebruik het beheerscript <code>setupStore.php</code> dat de gebruikersnaam en wachtwoord van een beheerder kan gebruiken.", "smw-admin-dbbutton": "Tabellen initialiseren of bijwerken", "smw-admin-announce": "Uw wiki aankondigen", "smw-admin-announce-text": "Als uw wiki openbaar is, kunt u deze registreren op <a href=\"https://wikiapiary.com\">WikiApiary</a>, de wiki tracking wiki.", "smw-admin-deprecation-notice-title": "Verouderingsmeldingen", "smw-admin-deprecation-notice-docu": "In de volgende sectie staan verouderde of verwijderde instellingen waarvan is vastgesteld dat ze op deze wiki nog in gebruik zijn. Naar verwachting zal de ondersteuning voor deze instellingen in een toekomstige uitgave verdwijnen.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> is verouderd en wordt in $2 verwijderd", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> zal de volgende optie{{PLURAL:$2||s}} verwijderen (of vervangen):", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> is verouderd en wordt in $2 verwijderd", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> is vervangen door <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> is vervangen door <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "Optie{{PLURAL:$2||s}} voor <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> wordt vervangen door <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> is verwijderd in $2", "smw-admin-deprecation-notice-title-notice": "Verouderde instellingen", "smw-admin-deprecation-notice-title-notice-explanation": "Onder <b>Verouderde instellingen</b> sta<PERSON> de instellingen vermeld waarvan is vastgesteld dat ze op deze wiki in gebruik zijn en die in een toekomstige uitgave worden verwijderd of gewijzigd.", "smw-admin-deprecation-notice-title-replacement": "Vervangen of hernoemde instellingen", "smw-admin-deprecation-notice-title-replacement-explanation": "<PERSON>der <b><PERSON><PERSON><PERSON><PERSON> of hernoemde instellingen</b> sta<PERSON> de instellingen vermeld die hernoemd of anderszins gewijzigd zijn. Het wordt aanbevolen de naam of indeling ervan onmidd<PERSON>jk bij te werken.", "smw-admin-deprecation-notice-title-removal": "Gewijzigde instellingen", "smw-admin-deprecation-notice-title-removal-explanation": "Onder <b>Verwijderde instellingen</b> ziet u de instellingen die in een vorige uitgave zijn verwijderd, maar waar<PERSON> is vastgesteld dat ze op deze wiki nog in gebruik zijn.", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw-smwadmin-refresh-title": "Gegevens repareren en bijwerken", "smw_smwadmin_datarefresh": "Gegevens opnieuw opbouwen", "smw_smwadmin_datarefreshdocu": "Het is mogelijk om alle gegevens van Semantic MediaWiki opnieuw te genereren op basis van de huidige inhoud van de wiki.\nDit kan handig zijn om gegevens te repareren of de gegevens te vernieuwen als de interne opmaak gewijzigd is bij een softwareupdate.\nDe gegevens worden pagina voor pagina bijgewerkt en het kan enige tijd duren voor de taak is afgerond.\nHieronder wordt weergegeven of er op dit moment een taak loopt en stelt u in staat een taak te stoppen of te starten (tenzij deze mogelijkheid door de sitebeheerder is uitgeschaked).", "smw_smwadmin_datarefreshprogress": "<strong>Er loopt een bijwerktaak</strong>\nHet is normaal dat de voortgang langzaam is omdat de gegevens ververst worden in kleine porties iedere keer als een gebruiker de wiki raadpleegt.\nOm het bijwerken sneller te laten verlopen, kunt u het beheerscript <code>runJobs.php</code> draaien. Gebruik de optie <code>--maxjobs 1000</code> om aan het aantal bij te werken pagina's per handeling te beperken.\nGeschatte voortgang van de huidige taak:", "smw_smwadmin_datarefreshbutton": "Gegevensherstel plannen", "smw_smwadmin_datarefreshstop": "Bijwerken afbreken", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, ik weet het {{GENDER:$1|zeker}}.", "smw-admin-job-scheduler-note": "De taken (die zijn ingeschakeld) in deze paragraaf worden uitgevoerd via de taakwachtrij om impasses tijdens hun uitvoering te voorkomen. De [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue werkvoorbereider] is verantwoordelijk voor de verwerking en het luistert nauw dat het onderhoudsscript <code>runJobs.php</code> een juiste capaciteit heeft (zie ook de configuratieparameter <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Opruiming verouderde entiteiten", "smw-admin-outdateddisposal-intro": "Sommige activiteiten (een wijziging van een eigenschapstype, het verwijderen van wikipagina’s of het corrigeren van foute waarden) resulteren in [https://www.semantic-mediawiki.org/wiki/Outdated_entities verouderde entiteiten]. Het wordt aangeraden deze af en toe te verwijderen om de tabelruimte die ze innemen vrij te maken.", "smw-admin-outdateddisposal-active": "Er is een taak ingepland om verouderde entiteiten op te ruimen.", "smw-admin-outdateddisposal-button": "Opruiming inplannen", "smw-admin-feature-disabled": "Deze functie is uitgeschakeld op deze wiki. Raadpleeg de hulppagina voor <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">instellingen</a> of neem contact op met de systeembeheerder.", "smw-admin-propertystatistics-title": "Eigenschapstatistieken opnieuw opbouwen", "smw-admin-propertystatistics-intro": "Bouwt de volledige gebruiksstatistieken van eigenschappen opnieuw op, waarbij de [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count gebruikstellers] van eigenschappen bijgewerkt en gecorrigeerd worden.", "smw-admin-propertystatistics-active": "<PERSON>en taak om statistieken opnieuw op te bouwen is aan de wachtrij toegevoegd.", "smw-admin-propertystatistics-button": "Inplannen statistieken opnieuw opbouwen", "smw-admin-fulltext-title": "Opnieuw opbouwen van z<PERSON>ken in volledige tekst", "smw-admin-fulltext-intro": "Bouwt opnieuw de zoekindex op van eigenschaptabellen waarop een ''[https://www.semantic-mediawiki.org/wiki/Full-text zoeken in volledige tekst]''-datatype is ingeschakeld. Wijzigingen in de indexregels (gewijzigde stopwoorden, nieuwe ''stemmer'', enz.) en/of een nieuw toegevoegde of gewijzigde tabel vereisen het opnieuw uitvoeren van deze taak.", "smw-admin-fulltext-active": "<PERSON>en taak om het zoeken in volledige tekst opnieuw op te bouwen is aan de wachtrij toegevoegd.", "smw-admin-fulltext-button": "Opnieuw opbouwen van z<PERSON>ken in volledige tekst aan wachtrij toe<PERSON>n", "smw-admin-support": "Ondersteuning krijgen", "smw-admin-supportdocu": "Er zijn verscheidene bronnen beschikbaar om u te ondersteunen als u problemen ondervindt:", "smw-admin-installfile": "Als u problemen on<PERSON>vindt bij uw installatie, controleer dan e<PERSON><PERSON> de richtl<PERSON>en in het bestand <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">INSTALL</a> en de <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">installatiepagina</a>.", "smw-admin-smwhomepage": "De volledige gebruikersdocumentatie voor Semantic MediaWiki is te vinden op <b><a href=\"http://semantic-mediawiki.org/wiki/Semantic_MediaWiki_-_startpagina\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Problemen kunt u melden op de <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">issuetracker</a>. De pagina over het <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">rapporteren van bugs</a> biedt een aantal richtlijnen voor het schrijven van een effectief probleemrapport.", "smw-admin-questions": "Als u verder nog vragen of suggesties hebt, neem dan deel aan het overleg op de <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">gebruikersmailinglijst</a> van Semantic MediaWiki.", "smw-admin-other-functions": "Andere functies", "smw-admin-statistics-extra": "Statistische functies", "smw-admin-statistics": "Statistieken", "smw-admin-supplementary-section-title": "Aanvullende functies", "smw-admin-supplementary-section-subtitle": "Ondersteunde kernfuncties", "smw-admin-supplementary-section-intro": "In deze sectie staan aanvullende functies die buiten het bereik van onderhoudsactiviteiten vallen. Het is mogelijk dat sommige hier vermelde functies (zie de [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions documentatie]) beperkt of helemaal niet beschikbaar zijn en daarom niet toegankelijk zijn op deze wiki.", "smw-admin-supplementary-settings-title": "Configuratie en instellingen", "smw-admin-supplementary-settings-intro": "Onder <u>$1</u> staan de parameters die het gedrag van Semantic MediaWiki bepalen", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "<PERSON>ele statistieken", "smw-admin-supplementary-operational-statistics-short-title": "operationele statistieken", "smw-admin-supplementary-operational-statistics-intro": "Geeft een keur aan <u>$1</u> weer", "smw-admin-supplementary-idlookup-title": "Opzoeken en opruimen van entiteiten", "smw-admin-supplementary-idlookup-short-title": "opzoeken en opruimen van entiteiten", "smw-admin-supplementary-idlookup-intro": "Ondersteunt een eenvoudige functie voor het <u>$1</u>", "smw-admin-supplementary-duplookup-title": "Dubbele entiteiten opzoeken", "smw-admin-supplementary-duplookup-intro": "<u>$1</u>: dit is om entiteiten te vinden die zijn gecategoriseerd als duplicaten voor de geselecteerde tabelmatrix", "smw-admin-supplementary-duplookup-docu": "Deze pagina bevat vermeldingen uit geselecteerde tabellen die zijn gecategoriseerd als [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities duplicaten]. Dubbele vermeldingen zouden zelden (of nooit) moeten voorkomen, maar kunnen zich sporadisch voordoen als gevolg van een afgebroken update of een mislukte terugdraai-transactie.", "smw-admin-supplementary-operational-statistics-cache-title": "Cachestatistieken", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> toont een geselecteerde verzameling statistieken over de cache", "smw-admin-supplementary-operational-table-statistics-title": "Tabelstatistieken", "smw-admin-supplementary-operational-table-statistics-short-title": "tabelstatistieken", "smw-admin-supplementary-operational-table-statistics-intro": "Genereert <u>$1</u> voor een geselecteerde verzameling tabellen", "smw-admin-supplementary-operational-table-statistics-explain": "In deze sectie staan geselecteerde tabelstatistieken waarmee systeem- en gegevensbeheerders weloverwogen beslissingen kunnen nemen over de status van de back-end en de opslagengine.", "smw-admin-supplementary-operational-table-statistics-legend": "De legenda beschrijft enkele van de sleutels die worden gebruikt voor de tabelstatistieken en omvat:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> totaal aantal rijen in een tabel", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> laatste ID die momenteel in gebruik is\n* <code>duplicate_count</code> aantal in de id_table gevonden duplicaten (zie ook [[Special:SemanticMediaWiki/duplicate-lookup|Dubbele entiteiten opzoeken]])\n* <code>rows.rev_count</code> aantal rijen met een toegekende revision_id die een directe wikipagina-koppeling aangeeft\n* <code>rows.smw_namespace_group_by_count</code> aantal geaggregeerde rijen voor naamruimten die in de tabel worden gebruikt\n* <code>rows.smw_proptable_hash.query_match_count</code> aantal query-subobjecten met een overeenkomstige tabelverwijzing\n* <code>rows.smw_proptable_hash.query_null_count</code> aantal query-subobjecten zonder tabelverwijzing (niet-gekoppeld, zwevende verwijzing)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> percentage termen die uniek zijn (een laag percentage geeft aan dat herhaalde termen de tabelinhoud en index in beslag nemen)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> aantal termen die maar <PERSON>én keer voorkomen\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> aantal termen die meer dan eens voorkomen", "smw-admin-supplementary-elastic-version-info": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> toont details over instellingen en indexstatistieken", "smw-admin-supplementary-elastic-docu": "Deze pagina bevat informatie over instel<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, bed<PERSON><PERSON>fstoestand en indexstatistieken met betrekking tot een Elasticsearch-cluster die is verbonden met Semantic MediaWiki en zijn [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "Ondersteunde onderdelen", "smw-admin-supplementary-elastic-settings-title": "Instellingen (indexen)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> waarmee Elasticsearch de indexen van Semantic MediaWiki beheert", "smw-admin-supplementary-elastic-mappings-title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-mappings-intro": "Onder <u>$1</u> worden indices en veldtoewijzingen weergegeven", "smw-admin-supplementary-elastic-mappings-docu": "Hier sta<PERSON> gegevens over de door de huidige index gebruikte veldtoewijzingen. Het wordt aanbevolen om de toewijzingen geregeld te controleren in verband met de grenswaarde <code>index.mapping.total_fields.limit</code> (het maximaal toegestane aantal velden in een index).", "smw-admin-supplementary-elastic-mappings-docu-extra": "De waarde <code>property_fields</code> verwijst naar het aantal geïndexeerde kernvelden, terwijl <code>nested_fields</code> verwijst naar een gezamenlijke optelsom van extra velden die aan een kernveld zijn toegewezen om specifieke gestructureerde zoekpatronen mogelijk te maken.", "smw-admin-supplementary-elastic-mappings-summary": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-mappings-fields": "Veldtoewijzingen", "smw-admin-supplementary-elastic-nodes-title": "Knooppunten", "smw-admin-supplementary-elastic-nodes-intro": "Onder <u>$1</u> staan de knooppuntstatistieken", "smw-admin-supplementary-elastic-indices-title": "Indices", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> geeft een overzicht van de beschikbare indices en hun statistieken", "smw-admin-supplementary-elastic-statistics-title": "Statistieken", "smw-admin-supplementary-elastic-statistics-intro": "Op de pagina <u>$1</u> staan statistieken op indexniveau", "smw-admin-supplementary-elastic-statistics-docu": "Hier staan statistieken die inzicht bieden in de verschillende bewerkingen die op indexniveau plaatsvinden. Deze statistieken zijn samengevo<PERSON>d met primaire en totale aggregaties. Op de [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html hulppagina] worden de beschikbare indexstatistieken nader beschreven.", "smw-admin-supplementary-elastic-status-replication": "Replicatiestatus", "smw-admin-supplementary-elastic-status-last-active-replication": "Laatste actieve replicatie: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Verversinterval: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Achterstand hersteltaken: $1 (schatting)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Taakachterstand (bestands)invoer: $1 (schatting)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replicatie vergrendeld: $1 (heropbouw in uitvoering)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Replicatiebewaking (actief): $1", "smw-admin-supplementary-elastic-replication-header-title": "Replicatiestatus", "smw-admin-supplementary-elastic-replication-function-title": "Replicatie", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> toont informatie over mislukte replicaties", "smw-admin-supplementary-elastic-replication-docu": "Op deze pagina staat informatie over de [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring replicatiestatus] van entiteiten waarvan is gemeld dat ze problemen hebben met het Elasticsearch-cluster. Het wordt aanbevolen de vermelde entiteiten te controleren en de cache ervan te legen om te bevestigen dat het een tijdelijk probleem was.", "smw-admin-supplementary-elastic-replication-files-docu": "Opgemerkt moet worden dat voor de lijst met bestanden e<PERSON>t de taak [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion bestandsinname (''file ingest'')] moet worden uitgevoerd en de verwerking ervan voltooid moet zijn.", "smw-admin-supplementary-elastic-replication-files": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON><PERSON>'s", "smw-admin-supplementary-elastic-endpoints": "Eindpunten", "smw-admin-supplementary-elastic-config": "Configuraties", "smw-admin-supplementary-elastic-no-connection": "De wiki kan momenteel '''geen'' verbinding tot stand brengen met de Elasticsearch-cluster. Neem contact op met de wikibeheerder om het probleem te onderzoeken, aangezien dit de index- en zoekmogelijkheden van het systeem uitschakelt.", "smw-list-count": "Er staan $1 vermelding{{PLURAL:$1||en}} in de lijst.", "smw-property-label-uniqueness": "Het label “$1” is aan ten minste nog één andere eigenschap gekoppeld. Raadpleeg de [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness hulppagina] om dit probleem op te lossen.", "smw-property-label-similarity-title": "Gelijkenisrapportage eigenschapslabels", "smw-property-label-similarity-intro": "<u>$1</u> berekent overeenkomsten voor bestaande eigenschapslabels", "smw-property-label-similarity-threshold": "Drempelwaarde:", "smw-property-label-similarity-type": "Typenummer weergeven", "smw-property-label-similarity-noresult": "Er zijn geen resultaten gevonden voor de geselecteerde opties.", "smw-property-label-similarity-docu": "Deze pagina vergelijkt de [https://www.semantic-mediawiki.org/wiki/Property_similarity gelijkenisafstand] (niet te verwar<PERSON> met een semantische of lexicale gelijkenis) tussen eigenschapslabels en meldt ze als ze de drempel overschrijden. De melding kan helpen bij het filteren van verkeerd gespelde of gelijkwaardige eigenschappen die hetzelfde concept vertegenwoordigen (zie de speciale pagina [[Special:Properties|Eigenschappen]] om het concept en het gebruik van gemelde eigenschappen te verduidelijken). De drempel kan worden aangepast om de afstand die wordt gebruikt voor het bij benadering vergelijken te vergroten of te verkleinen. <code>[[Property:$1|$1]]</code> wordt gebruikt om eigenschappen van de analyse uit te zonderen.", "smw-admin-operational-statistics": "Op deze pagina staan operationele statistieken die zijn verzameld in of vanuit functies die verband houden met Semantic MediaWiki. Een uitgebreide lijst met wiki-specifieke statistieken vindt u [[Special:Statistics|<b>hier</b>]].", "smw_adminlinks_datastructure": "Gegevensstructuur", "smw_adminlinks_displayingdata": "<PERSON><PERSON><PERSON>s weer<PERSON>", "smw_adminlinks_inlinequerieshelp": "Hulp bij ing<PERSON><PERSON><PERSON> z<PERSON>drachten", "smw-page-indicator-usage-count": "Geschatte [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count aantal keren gebruikt]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "Door {{PLURAL:$1|de gebruiker|het systeem}} gedefinieerde eigenschap", "smw-property-indicator-last-count-update": "Geschat gebruiksaantal\nLaatst bijgewerkt: $1", "smw-concept-indicator-cache-update": "Cache-teller\nLaatst bijgewerkt: $1", "smw-createproperty-isproperty": "Het is een eigenscha<PERSON> van het type $1.", "smw-createproperty-allowedvals": "De toegelaten {{PLURAL:$1|waarde voor deze eigenschap is|waarden voor deze eigenschap zijn}}:", "smw-paramdesc-category-delim": "Het scheidingsteken", "smw-paramdesc-category-template": "<PERSON>en sja<PERSON>on om de items mee op te maken", "smw-paramdesc-category-userparam": "Een aan de sja<PERSON> door te geven parameter", "smw-info-par-message": "Weer te geven bericht.", "smw-info-par-icon": "Pictogram dat \"informatie\" of \"waarschuwing\" aangeeft.", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Algemene opties", "prefs-extended-search-options": "Uitgebreid zoeken", "prefs-ask-options": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>ken", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] en bijbehorende extensies bieden individuele voorkeuren voor een groep geselecteerde eigenschappen en functies. E<PERSON> lij<PERSON> met individuele instellingen met hun beschrijving en kenmerken is beschikbaar op [https://www.semantic-mediawiki.org/wiki/Help:User_preferences deze hulppagina].", "smw-prefs-ask-options-tooltip-display": "Parametertekst weergeven als info-tooltip op de speciale pagina #ask [[Special:Ask|zoekopdrachtbouwer]].", "smw-prefs-ask-options-compact-view-basic": "De eenvoudige compacte weergave inschakelen", "smw-prefs-help-ask-options-compact-view-basic": "Bij inschakeling wordt een beperkt aantal koppelingen weergegeven in de compacte weergave van Special:Ask.", "smw-prefs-general-options-time-correction": "Tijdcorrectie inschakelen voor speciale pagina’s met be<PERSON><PERSON> van de lokale voorkeur [[Special:Preferences#mw-prefsection-rendering|Tijdverschil]]", "smw-prefs-general-options-jobqueue-watchlist": "De taakwachtrij-volglijst weergeven in mijn persoonlijke balk", "smw-prefs-help-general-options-jobqueue-watchlist": "Als dit is ingeschakeld wordt een [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist lijst met] geselecteerde uitstaande taken weergegeven, samen met hun geschatte wacht<PERSON>.", "smw-prefs-general-options-disable-editpage-info": "De inleidende tekst op de bewerkingspagina uitschakelen", "smw-prefs-general-options-disable-search-info": "De hulpinformatie voor syntaxis uitschakelen op de standaardzoekpagina", "smw-prefs-general-options-suggester-textinput": "Invoerhulp voor semantische entiteiten inschakelen", "smw-prefs-help-general-options-suggester-textinput": "Na inschakeling kunt u een [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance invoerhulp] gebruiken om eigenschappen, concepten en categorieën uit een invoercontext te vinden.", "smw-prefs-general-options-show-entity-issue-panel": "Het paneel entiteitsproblemen weergeven", "smw-prefs-help-general-options-show-entity-issue-panel": "Bij inschakeling worden er integriteitscontroles op elke pagina uitgevoerd en wordt het [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel paneel entiteitsproblemen] weergegeven.", "smw-prefs-factedsearch-profile": "Selecteer een standaardprofiel voor [[Special:FacetedSearch|gefacetteerd zoeken]]:", "smw-ui-tooltip-title-property": "Eigenschap", "smw-ui-tooltip-title-quantity": "Eenheidomzetting", "smw-ui-tooltip-title-info": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-service": "Dienstenkoppelingen", "smw-ui-tooltip-title-warning": "Waarschuwing", "smw-ui-tooltip-title-error": "Fout", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "Evenement", "smw-ui-tooltip-title-note": "Opmerking", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "Referentie", "smw_unknowntype": "Het type \"$1\" van deze eigenschap is ongeldig", "smw-concept-cache-text": "Het concept heeft in totaal $1 {{PLURAL:$1|pagina|pagina's}} en is voor het laatst bijgewerkt op $3, $2.", "smw_concept_header": "<PERSON><PERSON><PERSON>'s met het concept \"$1\"", "smw_conceptarticlecount": "Hieronder {{PLURAL:$1|wordt $1 pagina|worden $1 pagina's}} weergegeven.", "smw-qp-empty-data": "De gevraagde gegevens kunnen niet worden weergegeven vanwege enkele onvoldoende selectiecriteria.", "right-smw-admin": "Toegang tot administratieve taken (Semantic MediaWiki)", "right-smw-patternedit": "Bewerkingsrechten om toegestane reguliere expressies en patronen te onderhouden (Semantic MediaWiki)", "right-smw-pageedit": "Bewerkingsrechten voor pagina’s die zijn gean<PERSON><PERSON> met <code>Is edit protected</code> (Semantic MediaWiki)", "right-smw-schemaedit": "Bewerk [https://www.semantic-mediawiki.org/wiki/Help:Schema schema pages] (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "De [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist volglijst voor de taakwachtrij] gebruiken (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Toegangsinformatie over een revisie wanverhouding geasso<PERSON><PERSON> met een entiteit (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "Bekijk [https://www.semantic-mediawiki.org/wiki/Help:Edit_help edit help] (Semantic MediaWiki)", "restriction-level-smw-pageedit": "beveiligd (alleen in aanmerking komende gebruikers)", "action-smw-patternedit": "reguliere expressies gebruikt door Semantic MediaWiki te bewerken", "action-smw-pageedit": "pagina’s te bewerken die geannoteerd zijn met <code>Is edit protected</code> (Semantic MediaWiki)", "group-smwadministrator": "Beheerders (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|beheerder (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:Beheerders_(Semantic_MediaWiki)", "group-smwcurator": "Curatoren (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|curator (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON>n (Semantic MediaWiki)", "group-smweditor": "Redacteuren (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|redacteur (Semantic MediaWiki)}}", "grouppage-smweditor": "{{ns:project}}:Redacteuren (Semantic MediaWiki)", "action-smw-admin": "Semantic MediaWiki te beheren", "action-smw-ruleedit": "<PERSON><PERSON><PERSON><PERSON><PERSON>’s te bewerken (Semantic MediaWiki)", "smw-property-namespace-disabled": "De eigenschap [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks naamruimte] is uitgeschakeld. Het is niet mogelijk een type of andere eigenschapspecifieke kenmerken voor deze eigenschap te declareren.", "smw-property-predefined-default": "“$1” is een voorgedefinieerde eigenschap van het type $2.", "smw-property-predefined-common": "Deze eigenschap is voorgedefinieerd (en is dus een [https://www.semantic-mediawiki.org/wiki/Help:Special_properties speciale eigenschap]). Er zijn extra beheersprivileges aan verbonden maar verder kan ze net als elke andere [https://www.semantic-mediawiki.org/wiki/Property door de gebruiker gedefinieerde eigenschap] worden gebruikt.", "smw-property-predefined-ask": "“$1” is een voorgedefinieerde eigenschap die staat voor meta-informatie (in de vorm van een [https://www.semantic-mediawiki.org/wiki/Subobject subobject]) over individuele zoekopdrachten. Deze eigenschap wordt geleverd bij [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "\"$1\" is een voorgedefinieerde eigenschap die het aantal voorwaarden dat wordt gebruikt in een zoekopdracht verzamelt en bij [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] wordt geleverd.", "smw-property-predefined-askde": "\"$1\" is een voorgedefinieerde eigenschap die de diepte van een zoekopdracht aangeeft en bij [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] wordt geleverd.", "smw-property-predefined-long-askde": "Het is een numerieke waarde die is berekend op basis van geneste zoe<PERSON>drachten, eigens<PERSON><PERSON><PERSON><PERSON>, en beschikbare beschrijvingselementen waarbij de uitvoer van de zoekopdracht beperkt wordt door de configuratieparameter <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "“$1” is een voorgedefinieerde eigenschap die parameters beschrijft die een zoekresultaat beïnvloeden. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "Deze maakt deel uit van een verzameling eigenschappen die een [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler zoekopdrachtprofiel] bepalen.", "smw-sp-properties-docu": "Op deze pagina wordt een lijst van [https://www.semantic-mediawiki.org/wiki/Property eigenschappen] getoond die beschikbaar zijn op deze wiki, met tellingen van het aantal keren dat ze gebruikt worden. Voor actuele tellingen wordt aangeraden het onderhoudsscript voor [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics eigenschapstatistieken] regelmatig uit te voeren. Voor een andere weergave, zie de speciale pagina's [[Special:UnusedProperties|ongebruikte eigenschappen]] of [[Special:WantedProperties|gewenste eigenschappen]].", "smw-sp-properties-cache-info": "Deze gegevens zijn overgenomen uit een [https://www.semantic-mediawiki.org/wiki/Caching cache] en zijn voor het laatst bijgewerkt op $1.", "smw-sp-properties-header-label": "<PERSON><PERSON><PERSON> met e<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-settings-docu": "Geeft een lijst van alle standaard en lokale instellingen die relevant zijn voor de Semantic MediaWiki-omgeving. Raadpleeg de help-pagina [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuratie] voor meer informatie over afzonderlijke instellingen.", "smw-sp-admin-settings-button": "<PERSON><PERSON><PERSON> met instel<PERSON><PERSON>", "smw-admin-idlookup-title": "Opzoeken", "smw-admin-idlookup-docu": "Deze sectie toont technische details over een individuele entiteit (wikipagina, subobject, eigenschap, enz.) in Semantic MediaWiki. De invoer kan een numerieke ID zijn of een tekenreekswaarde die overeenkomt met het <PERSON><PERSON>, maar elke ID-verwijzing heeft betrekking op Semantic MediaWiki en niet op een pagina of versie-ID van MediaWiki.", "smw-admin-iddispose-title": "Opruiming", "smw-admin-iddispose-docu": "Houd er rekening mee dat de opruimingsoperatie onbeperkt is. De entiteit zal uit de opslag verwijderd worden, samen met alle referenties ervan in uitstaande tabellen, indien bevestigd. Voer deze taak '''voorzichtig''' uit en pas na het raadplegen van de [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal documentatie].", "smw-admin-iddispose-done": "ID “$1” is verwijderd uit de opslag-backend.", "smw-admin-iddispose-references": "ID “$1” heeft {{PLURAL:$2|geen|ten minste één}} actieve referentie:", "smw-admin-iddispose-references-multiple": "<PERSON><PERSON><PERSON> met t<PERSON><PERSON><PERSON> met ten minste één actief referentierecord.", "smw-admin-iddispose-no-references": "Bij het zoeken kon “$1” niet worden gekoppeld aan een tabelvermelding.", "smw-admin-idlookup-input": "<PERSON><PERSON>:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Overzicht", "smw-admin-tab-notices": "Verouderingsmeldingen", "smw-admin-tab-maintenance": "Onderhoud", "smw-admin-tab-supplement": "Aanvullende functies", "smw-admin-tab-registry": "Register", "smw-admin-tab-alerts": "Waarschuwingen", "smw-admin-alerts-tab-deprecationnotices": "Verouderingsmeldingen", "smw-admin-alerts-tab-maintenancealerts": "Onderhoudswaarschuwingen", "smw-admin-alerts-section-intro": "In deze sectie worden waarschuwingen en mededelingen weergegeven met betrekking tot instellingen, bewerkingen en andere activiteiten waarvan is aangegeven dat deze de aandacht vereisen van een moderator of geb<PERSON>iker met de juiste rechten.", "smw-admin-maintenancealerts-section-intro": "De volgende waarschuwingen en mededelingen zouden moeten worden opgelost. Verplicht is dat niet, maar naar verwachting helpt het wel de onderhoudbaarheid en de werking van het systeem te verbeteren.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Tabeloptimal<PERSON><PERSON>", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Het systeem heeft vastgesteld dat de laatste [https://www.semantic-mediawiki.org/wiki/Table_optimization tabeloptimalisatie] $2 dagen geleden is uitgevoerd (logboekvermelding van $1), wat de onderhoudsdrempel van $3 dagen overschrijdt. Zoals vermeld in de documentatie, zorgt het uitvoeren van optimalisaties ervoor dat de zoekopdrachtplanner betere beslissingen kan nemen over zoekopdrachten. Daarom wordt het aanbevolen de tabeloptimalisatie regelmatig uit te voeren.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Verouderde entiteiten", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Het systeem heeft $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities verouderde entiteiten] geteld en heeft door het overschrijden van de drempel van $2 een kritiek niveau van achterstallig onderhoud bereikt. Het wordt aanbevolen om het onderhoudsscript [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] uit te voeren.", "smw-admin-maintenancealerts-invalidentities-alert-title": "Ongeldige entiteiten", "smw-admin-maintenancealerts-invalidentities-alert": "Het systeem heeft vastgesteld dat $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities entiteit{{PLURAL:$1||en}}] zich in een [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace niet onderhouden naamruimte] bevinden. Het wordt aanbevolen het onderhoudsscript [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] of [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>] uit te voeren.", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Instellingen", "smw-admin-configutation-tab-namespaces": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-configutation-tab-schematypes": "Schematypes", "smw-admin-maintenance-tab-tasks": "Taken", "smw-admin-maintenance-tab-scripts": "Onderhoudsscripts", "smw-admin-maintenance-no-description": "<PERSON><PERSON>.", "smw-admin-maintenance-script-section-title": "<PERSON><PERSON><PERSON> met be<PERSON><PERSON><PERSON><PERSON> onderhoudsscripts", "smw-admin-maintenance-script-section-intro": "Voor de volgende onderhoudsscripts zijn een beheerder en toegang tot de opdrachtregel vereist om de genoemde scripts uit te kunnen voeren.", "smw-admin-maintenance-script-description-dumprdf": "RDF-export van bestaande ''triples''.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Dit script wordt gebruikt om conceptcaches voor Semantic MediaWiki te beheren. Het kan de geselecteerde caches aanmaken, verwijderen en bijwerken.", "smw-admin-maintenance-script-description-rebuilddata": "Herschept alle semantische gegevens in de database door alle pagina’s te doorlopen die mogelijk semantische gegevens bevatten.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Bouwt de Elasticsearch-index opnieuw op (alleen voor installaties die <code>ElasticStore</code> gebruiken), door alle entiteiten te doorlopen die semantische gegevens bevatten.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Vindt ontbrekende entiteiten in Elasticsearch (alleen voor installaties die <code>ElasticStore</code> gebruiken) en plant de juiste bijwerkingstaken.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Bouwt de zoekindex voor volledige tekst <code>SQLStore</code> opnieuw op (voor installaties waarbij dit is ingeschakeld).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Bouwt de gebruiksstatistieken voor alle eigenschapsentiteiten opnieuw op.", "smw-admin-maintenance-script-description-removeduplicateentities": "Verwijdert dubbele entiteiten die gevonden zijn in geselecteerde tabellen zonder actieve referenties.", "smw-admin-maintenance-script-description-setupstore": "Stelt de opslag- en query-backend in zoals gedefinieerd in <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Werkt het veld <code>smw_sort</code> in de <code>SQLStore</code> bij (in overeenstemming met de instelling [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "Vult het veld <code>smw_hash</code> in voor rijen waarin de waarde ontbreekt.", "smw-admin-maintenance-script-description-purgeentitycache": "Cache-vermeldingen wissen voor bekende entiteiten en de bijbehorende gegevens.", "smw-admin-maintenance-script-description-updatequerydependencies": "Query’s en query-afhankelijkheden bijwerken (zie de instelling [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Verouderde entiteiten en querykoppelingen opruimen.", "smw-admin-maintenance-script-description-runimport": "Automatisch ontdekte inhoud invullen en importeren van [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Bijwerkingsscripts", "smw-admin-maintenance-script-section-rebuild": "Scripts voor opnieuw opbouwen", "smw-livepreview-loading": "Bezig met laden…", "smw-sp-searchbyproperty-description": "Deze pagina biedt een eenvoudige [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces bladerinteractie] voor het vinden van entiteiten met een eigenschap met een bepaalde waarde. Andere beschikbare zoekinteracties zijn de [[Special:PageProperty|zoekpagina voor pagina-eigenschappen]] en de [[Special:Ask|zoekopdrachtbouwer]].", "smw-sp-searchbyproperty-resultlist-header": "<PERSON><PERSON><PERSON> van resultaten", "smw-sp-searchbyproperty-nonvaluequery": "<PERSON><PERSON> met waarden wa<PERSON>an de eigenschap \"$1\" is toegekend.", "smw-sp-searchbyproperty-valuequery": "<PERSON><PERSON> met p<PERSON><PERSON>'s die de eigenschap \"$1\" he<PERSON><PERSON> met de g<PERSON><PERSON><PERSON><PERSON> waarde \"$2\".", "smw-datavalue-number-textnotallowed": "\"$1\" kan niet worden toegewezen aan een gede<PERSON><PERSON><PERSON>d getalty<PERSON> met waarde $2.", "smw-datavalue-number-nullnotallowed": "“$1” retourneerde een “NULL”, wat niet als getal is toegestaan.", "smw-editpage-annotation-enabled": "Deze pagina ondersteunt semantische annotaties in de tekst (bv. <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) om gestructureerde en opvraagbare inhoud te bouwen die wordt geleverd door Semantic MediaWiki. Voor een uitgebreide beschrijving van het gebruik van annotaties of de parserfunctie #ask, kijkt u op de hulppagina’s [https://www.semantic-mediawiki.org/wiki/Help:Getting_started Aan de slag], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation Annotaties in tekst] of [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries Ingevoegde zoekopdrachten].", "smw-editpage-annotation-disabled": "Deze pagina is vanwege naamruimtebeperkingen niet geschikt voor semantische annotaties in de tekst. Details over het inschakelen van de naamruimte kunt u vinden op de hulppagina [https://www.semantic-mediawiki.org/wiki/Help:Configuration Configuratie].", "smw-editpage-property-annotation-enabled": "Deze eigenschap kan worden uitgebreid met behul<PERSON> van semantische annotaties om een gegevenstype op te geven (bv.. <nowiki>\"[[Has type::Page]]\"</nowiki>) of andere ondersteunende verklaringen (bv. <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>). Voor een beschrijving van hoe u deze pagina kunt uitbreiden kijkt u op de hulppagina’s [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration Eigenschappen declareren] of [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes Lij<PERSON> met beschikbare gegevenstypen].", "smw-editpage-property-annotation-disabled": "Deze eigenschap kan niet worden uitgebreid met een gegevenstype-annotatie (bv. <nowiki>\"[[Has type::Page]]\"</nowiki>) omdat deze al vooraf is gedefinieerd (zie de hulppagina over [https://www.semantic-mediawiki.org/wiki/Help:Special_properties speciale eigenschappen] voor meer informatie).", "smw-editpage-concept-annotation-enabled": "Dit concept kan worden uitgebreid met de parserfunctie #concept. Voor een beschrijving van het gebruik van #concept kijkt u op de hulppagina [https://www.semantic-mediawiki.org/wiki/Help:Concepts Concepten].", "smw-search-syntax-support": "De zoe<PERSON>ang ondersteunt het gebruik van de [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search syntaxis voor semantische zoekopdrachten] om resultaten te helpen vinden met be<PERSON><PERSON> van Semantic MediaWiki.", "smw-search-input-assistance": "De [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance invoerassistent] is ook ingeschakeld om de voorselectie van beschikbare eigenschappen en categorieën te vergemakkelijken.", "smw-search-help-intro": "<PERSON>en invoer van <code><nowiki>[[ ... ]]</nowiki></code> geeft de invoerprocessor het signaal om de Semantic MediaWiki zoekback-end te gebruiken. Opgemerkt moet worden dat het combineren van <code><nowiki>[[ ... ]]</nowiki></code> met een ongestructureerde tekstzoekopdracht zoals <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> niet wordt ondersteund.", "smw-search-help-structured": "Gestructureerde zoekopdrachten:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (als [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context gefilterde context])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (met een [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context zoekopdrachtcontext])", "smw-search-help-proximity": "Zoeken op nabijheid (bij een onbekende eigenschap '''alleen'' beschikbaar voor back-ends met een geïntegreerde functie voor zoeken op volledige tekst):\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code> (in alle geïndexeerde documenten naar “lorem” en “ipsum” zoeken)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (“lorem ipsum” als zin zoeken)", "smw-search-help-ask": "Op de hieronder gekoppelde pagina's wordt uitgelegd hoe u de syntaxis <code>#ask</code> gebruikt:\n\n* in [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Pagin<PERSON>'s selecteren] wordt beschreven hoe u pagina's selecteert en selectiecriteria formuleert;\n\n* in [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Zoekoperators] worden de beschikbare zoekoperators opgesomd, waaronder die voor zoekopdrachten voor een bepaald bereik en met jokertekens.", "smw-search-input": "Invoeren en zoeken", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Hulp bij invoer] is be<PERSON><PERSON><PERSON><PERSON> voor het invoerveld en vereist het gebruik van een van de volgende voorvoegsels:\n\n* <code>p:</code> om suggesties voor eigenschappen in te schakelen (bv. <code><nowiki>[[p:Has ...</nowiki></code>)\n\n* <code>c:</code> om categoriesuggesties in te schakelen\n\n* <code>con:</code> om conceptsuggesties in te schakelen", "smw-search-syntax": "Syntaxis", "smw-search-profile": "Uitgebreid", "smw-search-profile-tooltip": "Zoekfunc<PERSON> in verband met Semantic MediaWiki", "smw-search-profile-sort-best": "<PERSON><PERSON> treffer", "smw-search-profile-sort-recent": "Meest recent", "smw-search-profile-sort-title": "Titel", "smw-search-profile-extended-help-intro": "Het [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile uitgebreide profiel] van Special:Search biedt toegang tot zoekfuncties die specifiek zijn voor Semantic MediaWiki en de bijbehorende query-backend.", "smw-search-profile-extended-help-sort": "Geeft een sorteervoorkeur op voor de resultaatweergave met:", "smw-search-profile-extended-help-sort-title": "* “Titel” waarbij de paginatitel (of weergavetitel) als sorteercriterium wordt gebruikt", "smw-search-profile-extended-help-sort-recent": "* “Meest recente” toont eerst de meest recent gewijzigde entiteiten (subobject-entiteiten worden echter niet weergegeven, want die zijn niet gean<PERSON>erd met een [[Property:Modification date|wijzigingsdatum]])", "smw-search-profile-extended-help-sort-best": "* “Beste treffer” sorteert entiteiten op [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy relevantie] op basis van door de back-end geleverde scores", "smw-search-profile-extended-help-form": "Er worden formulieren verstrekt (indien onderhouden) om aan bepaalde gebruiksscenario’s te voldoen. Daarin worden verschillende eigenschaps- en waardevelden weergegeven om zo het invoerproces binnen de perken te houden en het voor gebruikers gemakkelijk te maken om door te gaan met een zoekopdracht (zie $1).", "smw-search-profile-extended-help-namespace": "Het naamruimte-selectievakje wordt verborgen zodra een formulier wordt geselecteerd, maar kan weer zichtbaar worden gemaakt met de knop “weergeven/verbergen”.", "smw-search-profile-extended-help-search-syntax": "In het zoekinvoerveld kan de syntaxis van <code>#ask</code> worden gebruikt om een voor Semantic MediaWiki specifieke zoekcontext te definiëren. Nuttige uitdrukkingen zijn onder meer:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> om alles te vinden dat “...” bevat. Dit is vooral handig als de betrokken zoekcontext of eigenschappen onbekend zijn (bv. <code>in:(lorem && ipsum)</code> is gelijkwa<PERSON>ig aan <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> om alles te vinden dat “...” in exact dezelfde volgorde bevat", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> om elke entiteit te met de eigenschap “...” te vinden (bv. <code>has:(Foo && Bar)</code> is gel<PERSON>jkwaardig aan <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> om geen enkele entiteit te vinden die “...” bevat", "smw-search-profile-extended-help-search-syntax-prefix": "* Er zijn nog meer aangepaste voorvoegsels beschik<PERSON>ar en gedefinieerd, zoals: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* So<PERSON>ige uitdrukkingen zijn g<PERSON>, zoals: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''So<PERSON><PERSON> van de genoemde verrichtingen zijn alleen nuttig bij gebruik van een volledige-tekstindex of de ElasticStore.''", "smw-search-profile-extended-help-query": "<code><nowiki>$1</nowiki></code> is als zoekopdracht gebruikt.", "smw-search-profile-extended-help-query-link": "Zie voor meer informatie de $1.", "smw-search-profile-extended-help-find-forms": "beschikbare formulieren", "smw-search-profile-extended-section-sort": "Sorteren op", "smw-search-profile-extended-section-form": "Formulieren", "smw-search-profile-extended-section-search-syntax": "Zoekin<PERSON><PERSON>", "smw-search-profile-extended-section-namespace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-query": "Zoe<PERSON><PERSON>dracht", "smw-search-profile-link-caption-query": "zoekopdrachtbouwer", "smw-search-show": "Weergeven", "smw-search-hide": "Verbergen", "log-name-smw": "Logboek Semantic MediaWiki", "log-show-hide-smw": "$1 Semantic MediaWiki-logboek", "logeventslist-smw-log": "Logboek Semantic MediaWiki", "log-description-smw": "Activiteiten voor [https://www.semantic-mediawiki.org/wiki/Help:Logging ingeschakelde gebeurtenistypen] die zijn gemeld door Semantic MediaWiki en de componenten ervan.", "logentry-smw-maintenance": "Onderhoudsgerelateerde gebeurtenissen die zijn doorgevoerd door Semantic MediaWiki", "smw-datavalue-import-unknown-namespace": "De importnaamruimte “$1” is onbekend. Zorg ervoor dat de OWL-importgegevens beschikbaar zijn via [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Kan geen URI in de naamruimte “$1” vinden in de [[MediaWiki:Smw import $1|import van $1]].", "smw-datavalue-import-missing-type": "Er is geen typedefinitie voor “$1” gevonden in de [[MediaWiki:Smw import $2|import van $2]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Import van $1]]", "smw-datavalue-import-invalid-value": "“$1” is niet juist opgema<PERSON>t. Dit moet bestaan uit “naamruimte”:“ID” (bv. “foaf:naam”).", "smw-datavalue-import-invalid-format": "De tekenreeks “$1” moet uit vier delen bestaan, maar de indeling werd niet begrepen.", "smw-property-predefined-impo": "“$1” is een voorgedefinieerde eigenschap die een verhouding tot een [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary geïmporteerde woordenschat] beschrijft. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org /wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "“$1” is een voorgedefinieerde eigenschap die het [[Special:Types|gegevenstype]] van een eigenschap beschrijft. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "“$1” is een voorgedefinieerde eigenschap die een [https://www.semantic-mediawiki.org/wiki/Help:Container container]-constructie vertegenwoordigt. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "De container maakt het mogelijk om eigenschapswaardetoewijzingen te vergaren, <PERSON><PERSON><PERSON><PERSON><PERSON> met die van een normale wikipagina, maar binnen een andere entiteitsruimte, terwijl ze gekoppeld zijn aan het inbeddingsonderwerp.", "smw-property-predefined-errp": "“$1” is een voorgedefinieerde eigenschap die invoerfouten bij annotaties met onregelmatige waarden bijhoudt. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "In de meeste gevallen wordt dit veroorzaakt door een afwijkend type of door een [[Property:Allows value|waarde]]-beperking.", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value “$1”] is een voorgedefinieerde eigenschap die een lijst met toegestane waarden kan definiëren om waardetoewijzingen voor een eigenschap te beperken. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list “$1”] is een voorgedefinieerde eigenschap die een verwijzing kan specificeren naar een lijst die toegestane waarden bevat om waardetoewijzingen voor een eigenschap te beperken. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "De eigenschap “$1” heeft een beperkt toepassingsgebied en kan niet door een gebruiker als annotatie-eigenschap worden gebruikt.", "smw-datavalue-property-restricted-declarative-use": "De eigenschap “$1” is een declaratieve eigenschap en kan alleen worden gebruikt op een eigenschaps- of categoriepagina.", "smw-datavalue-property-create-restriction": "De eigenschap “$1” bestaat niet en de gebruiker beschikt niet over het recht “$2” om waarden te maken of te annoteren met een niet-goedgekeurde eigenschap (zie [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode Autoriteitsmodus]).", "smw-datavalue-property-invalid-character": "“$1” bevat het op de lijst vermelde teken “$2” als onderdeel van het eigenschapslabel en is daarom als ongeldig geclassificeerd.", "smw-datavalue-property-invalid-chain": "Het gebruik van “$1” als eigenschappenketen is niet toegestaan tijdens het annotatieproces.", "smw-datavalue-restricted-use": "De gegevenswaarde “$1” is gemarkeerd voor beperkt gebruik.", "smw-datavalue-invalid-number": "\"$1\" kan niet worden geïnterpreteerd als een getal.", "smw-query-condition-circular": "Er is een mogelijke circulaire aanroep aangetro<PERSON>n in \"$1\".", "smw-query-condition-empty": "De beschrij<PERSON> van de zoekopdracht bevat een lege voorwaarde.", "smw-types-list": "<PERSON><PERSON><PERSON> van gegevenstypen", "smw-types-default": "\"$1\" is een ingebouwd gegevenstype.", "smw-types-help": "Meer informatie en voorbeelden vindt u op [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 deze hulppagina].", "smw-type-anu": "\"$1\" is een variant van het [[Special:Types/URL|URL]] gegevenstype, dat meestal wordt gebruikt voor een \"owl:AnnotationProperty\" exportdeclaratie.", "smw-type-boo": "\"$1\" is een basisdatatype voor het beschrij<PERSON> van een waarheidswaarde.", "smw-type-cod": "\"$1\" is een variant van het [[Special:Types/Text|Tekst]] gegevenstype, dat kan worden gebruikt voor technische teksten van willekeurige lengte, zoals broncode.", "smw-type-geo": "\"$1\" is een datatype dat geografische locaties beschrijft. Voor volledige functionaliteit is de uitbreiding [https://www.semantic-mediawiki.org/wiki/Extension:Maps Semantic Maps] vereist.", "smw-type-tel": "\"$1\" is een speciaal gegevenstype om internationale telefoonnummers te beschrijven conform RFC 3966.", "smw-type-txt": "\"$1\" is een basisdatatype om tekenreeksen van willekeurige lengte te beschrijven.", "smw-type-dat": "\"$1\" is een basisdatatype om tijdstippen in een uniform formaat weer te geven.", "smw-type-ema": "“$1” is een speciaal gegevenstype dat een e-mail vertegenwoordigt.", "smw-type-tem": "“$1” is een speciaal numeriek gegevenstype dat een temperatuur vertegenwoordigt.", "smw-type-qty": "“$1” is een gegevenstype om hoeveelheden te beschrijven met een numerieke weergave en een meeteenheid.", "smw-type-rec": "“$1” is een containergegevenstype dat een lijst met eigenschappen met typen in een vaste volgorde opgeeft.", "smw-type-extra-tem": "Het conversieschema bevat ondersteunde eenheden zoa<PERSON>, <PERSON><PERSON><PERSON>, Fahrenheit en Rankine.", "smw-type-tab-properties": "Eigenschappen", "smw-type-tab-types": "Typen", "smw-type-tab-type-ids": "Type-ID’s", "smw-type-tab-errors": "<PERSON><PERSON><PERSON>", "smw-type-primitive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-type-contextual": "Contextue<PERSON>", "smw-type-compound": "Samengesteld", "smw-type-container": "Container", "smw-type-no-group": "<PERSON><PERSON>", "smw-special-pageproperty-description": "Deze pagina biedt een browserinterface waarmee u alle waarden van een eigenschap en een bepaalde pagina kunt vinden. Andere beschikbare zoekinterfaces zijn onder meer [[Special:SearchByProperty|Op eigenschap zoeken]] en de [[Special:Ask|zoekopdrachtbouwer voor ''Ask'']].", "smw-property-predefined-errc": "“$1” is een voorgedefinieerde eigenschap die wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]. Deze eigenschap vertegenwoordigt fouten die zijn verschenen in verband met onjuiste waardeannotaties of invoerverwerking.", "smw-property-predefined-long-errc": "Fouten worden verzameld in een [https://www.semantic-mediawiki.org/wiki/Help:Container container] die mogelijk een verwijzing bevat naar de eigenschap die de discrepantie heeft veroorzaakt.", "smw-property-predefined-errt": "“$1” is een voorgedefinieerde eigenschap die een tekstuele beschrijving van een fout bevat. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Een door de gebruiker gedefinieerd subobject bevatte een ongeldig naamgevingsschema. De puntnotatie ($1) die binnen de eerste vijf tekens wordt gebruikt, is gereserveerd voor uitbreidingen. U kunt een [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier ID op naam] instellen.", "smw-datavalue-record-invalid-property-declaration": "De recorddefinitie bevat de eigenschap “$1”, die zelf als recordtype is g<PERSON><PERSON><PERSON><PERSON>d, en dat is niet toe<PERSON><PERSON>.", "smw-property-predefined-mdat": "“$1” is een voorgedefinieerde eigenschap die overeenkomt met de datum van de laatste wijziging van een onderwerp. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "“$1” is een voorgedefinieerde eigenschap die overeenkomt met de datum van de eerste herziening van een onderwerp. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "“$1” is een voorgedefinieerde eigenschap die aangeeft of een onderwerp nieuw is of niet. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "“$1” is een voorgedefinieerde eigenschap die de paginanaam bevat van de gebruiker die de laatste versie heeft aangemaakt. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "“$1” is een voorgedefinieerde eigenschap die het MIME-type van een geüpload bestand beschrijft. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "“$1” is een voorgedefinieerde eigenschap die het mediatype van een geüpload bestand beschrijft. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "“$1” is een voorgedefinieerde eigenschap die de naam bevat van het resultaatformaat dat in een zoekopdracht wordt gebruikt. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "“$1” is een voorgedefinieerde eigenschap die de voorwaarden van de zoekopdracht beschrijft als een tekenreeks. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "“$1” is een voorgedefinieerde eigenschap die een tijdswaarde (in seconden) bevat die nodig was om de uitvoering van de zoekopdracht te voltooien. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "“$1” is een voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], die alternatieve (bv. externe, federatieve) zoekbronnen identificeert.", "smw-property-predefined-askco": "“$1” is een voorgedefinieerde eigenschap die wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]. Deze eigenschap beschrijft de status van een zoekopdracht of de componenten ervan.", "smw-property-predefined-long-askco": "Het toegewezen nummer of de toegewezen nummers vertegenwoordigen een interne gecodificeerde status die op de [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler hulppagina] wordt uitgelegd.", "smw-property-predefined-prec": "“$1” is een voorgedefinieerde eigenschap die een [https://www.semantic-mediawiki.org/wiki/Help:Display_precision weergaveprecisie] (in decimale cijfers) voor numerieke gegevenstypen beschrijft.", "smw-property-predefined-attch-link": "“$1” is een voorgedefinieerde eigenschap die ingebedde bestands- en afbeeldingskoppelingen verzamelt die op een pagina worden gevonden. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-inst": "“$1” is een interne voorgedefinieerde eigenschap die van MediaWiki onafhankelijke categorie-informatie opslaat. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-unit": "“$1” is een voorgedefinieerde declaratieve eigenschap om weergave-eenheden te definiëren voor eigenschappen van numerieke typen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-unit": "Een door komma’s gescheiden lijst maakt het mogelijk eenheden of formaten te beschrijven die voor de weergave moeten worden gebruikt.", "smw-property-predefined-conv": "“$1” is een declaratieve, voorgedefinieerde eigenschap om de conversiefactor te definiëren voor een bepaalde eenheid van een fysieke grootheid. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-serv": "“$1” is een voorgedefinieerde declaratieve eigenschap om dienstkoppelingen aan een eigenschap toe te voegen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-redi": "“$1” is een voorgedefinieerde interne eigenschap om doorverwijzingen vast te leggen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subp": "“$1” is een voorgedefinieerde declaratieve eigenschap om te definiëren dat een eigenschap een [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of deeleigenschap van] een andere eigenschap is. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subc": "“$1” is een voorgedefinieerde eigenschap om te definiëren dat een categorie een [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of subcategorie van] een andere is. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-conc": "“$1” is een voorgedefinieerde interne eigenschap om een bijbehorend concept te definiëren. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-err-type": "“$1” is een voorgedefinieerde eigenschap om een groep of klasse [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors verwerkingsfouten] te identificeren. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-skey": "“$1” is een voorgedefinieerde interne eigenschap die een sorteerreferentie bevat. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pplb": "“$1” is een voorgedefinieerde declaratieve eigenschap om een [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label voorkeurseigenschapslabel] op te geven. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-chgpro": "“$1” is een voorgedefinieerde eigenschap die informatie over de [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation propagatie van wijzigingen] bevat. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]", "smw-property-predefined-schema-link": " Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-format-schema": " Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-profile-schema": " Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans": " Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-source": " Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-group": " Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cont-len": "“$1” is een voorgedefinieerde eigenschap om lengte-informatie op te slaan. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-len": "Deze wordt gebruikt in combinatie met de [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (en de [https://www.semantic-mediawiki.org/Attachment_processor bijlageprocessor]) om de uit een ingenomen bestand gehaalde lengte-informatie te verzamelen en op te slaan (indien aanwezig).", "smw-property-predefined-cont-lang": "“$1” is een voorgedefinieerde eigenschap om taalinformatie op te slaan. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-lang": "Deze wordt gebruikt in combinatie met de [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (en de [https://www.semantic-mediawiki.org/Attachment_processor bijlageprocessor]) om de uit een ingenomen bestand gehaalde taalinformatie te verzamelen en op te slaan (indien aanwezig).", "smw-property-predefined-cont-title": "“$1” is een voorgedefinieerde eigenschap om titelinformatie op te slaan. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-title": "Deze wordt gebruikt in combinatie met de [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (en de [https://www.semantic-mediawiki.org/Attachment_processor bijlageprocessor]) om de uit een ingenomen bestand gehaalde titelinformatie te verzamelen en op te slaan (indien aanwezig).", "smw-property-predefined-cont-author": "“$1” is een voorgedefinieerde eigenschap om auteursinformatie op te slaan. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-author": "Deze wordt gebruikt in combinatie met de [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (en de [https://www.semantic-mediawiki.org/Attachment_processor bijlageprocessor]) om de uit een ingenomen bestand gehaalde auteursinformatie te verzamelen en op te slaan (indien aanwezig).", "smw-property-predefined-cont-date": "“$1” is een voorgedefinieerde eigenschap om datuminformatie op te slaan. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-date": "Deze wordt gebruikt in combinatie met de [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (en de [https://www.semantic-mediawiki.org/Attachment_processor bijlageprocessor]) om de uit een ingenomen bestand gehaalde datuminformatie te verzamelen en op te slaan (indien aanwezig).", "smw-property-predefined-cont-type": "“$1” is een voorgedefinieerde eigenschap om bestandstype-informatie op te slaan. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-type": "Deze wordt gebruikt in combinatie met de [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (en de [https://www.semantic-mediawiki.org/Attachment_processor bijlageprocessor]) om de uit een ingenomen bestand gehaalde type-informatie te verzamelen en op te slaan (indien aanwezig).", "smw-property-predefined-cont-keyw": "“$1” is een voorgedefinieerde eigenschap om trefwoorden te vertegenwoordigen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-keyw": "Deze wordt gebruikt in combinatie met de [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (en de [https://www.semantic-mediawiki.org/Attachment_processor bijlageprocessor]) om de uit een ingenomen bestand gehaalde sleutelwoorden te verzamelen en op te slaan (indien aanwezig).", "smw-property-predefined-file-attch": "“$1” is een voorgedefinieerde eigenschap om een container te vertegenwoordigen waarin bijlage-informatie wordt opgeslagen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-file-attch": "Deze wordt gebruikt in combinatie met de [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (en de [https://www.semantic-mediawiki.org/Attachment_processor bijlageprocessor]) om alle uit een ingenomen bestand opvraagbare inhoudsspecifieke informatie te verzamelen (indien aanwezig).", "smw-types-extra-geo-not-available": "De [https://www.semantic-mediawiki.org/wiki/Extension:Maps uitbreiding “Maps”] is niet gedetecteerd en daarom is “$1” beperkt in zijn vermogen om te werken.", "smw-datavalue-monolingual-dataitem-missing": "Er ontbreekt een element dat is vereist voor het opbouwen van een eentalige samengestelde waarde.", "smw-datavalue-languagecode-missing": "Voor de annotatie “$1” kon de parser geen taalcode bepalen (bv “iets@nl”).", "smw-datavalue-languagecode-invalid": "\"$1\" wordt niet herkend als een ondersteunde taalcode.", "smw-property-predefined-lcode": "“$1” is een voorgedefinieerde eigenschap die een volgens BCP47 opgemaakte taalcode vertegenwoordigt. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "“$1” is een [https://www.semantic-mediawiki.org/wiki/Help:Container container]-gegevenstype dat een tekstwaarde associeert met een bepaalde [[Eigenschap:Taalcode|taalcode]].", "smw-types-extra-mlt-lcode": "Voor dit gegevenstype is een taalcode {{PLURAL:$2||niet}} vereist (d.w.z. een waarde-annotatie zonder taalcode wordt {{PLURAL:$2|niet|}} geaccepteerd).", "smw-property-predefined-text": "“$1” is een voorgedefinieerde eigenschap die tekst van willekeurige lengte vertegenwoordigt. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "“$1” is een voorgedefinieerde eigenschap die het mogelijk maakt een eigenschap in de context van een taal te beschrijven. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "“$1” is een voorgedefinieerde eigenschap om een lijst met eigenschappen te definiëren die worden gebruikt met een eigenschap van het type [[Special:Types/Record|record]]. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Parse<PERSON><PERSON><PERSON><PERSON> van annotaties in de tekst", "smw-limitreport-intext-postproctime": "[SMW] naverwerkingstijd", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|seconde|seconden}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|seconde|seconden}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Duur bijwerken opslag (bij pagina opschonen)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|seconde|seconden}}", "smw_allows_pattern": "Deze pagina wordt geacht een lijst met referenties te bevatten (gevolgd door [https://en.wikipedia.org/wiki/Regular_expression reguliere expressies]) die beschikbaar wordt gemaakt door de eigenschap [[Property:Allows pattern|Allows pattern]]. Om deze pagina te bewerken is het recht <code>smw-patternedit</code> vereist.", "smw-datavalue-allows-pattern-mismatch": "“$1” is als ongeldig geclassificeerd door de reguliere expressie “$2”.", "smw-datavalue-allows-pattern-reference-unknown": "De patroonreferentie “$1” kon niet worden gekoppeld aan een item in [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "Voor de lijstreferentie “$1” is geen pagina [[MediaWiki:Smw allows list $1]] gevonden.", "smw-datavalue-allows-value-list-missing-marker": "In de inhoud van de lijst “$1” ontbreken vermeldingen met een lijstmarkering “*”.", "smw-datavalue-feature-not-supported": "De functie “$1” wordt niet ondersteund of is uitgeschakeld op deze wiki.", "smw-property-predefined-pvap": "“$1” is een voorgedefinieerde eigenschap die een [[MediaWiki:Smw allows pattern|patroonreferentie]] kan specificeren om met een [https://en.wikipedia.org/wiki/Regular_expression reguliere expressie] te vergelijken. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "“$1” is een vooraf gedefinieerde eigenschap die een aparte weergavetitel aan een entiteit kan toewijzen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "“$1” is een voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], om de waarde van elke instantie te beperken zodat deze uniek is (of hoogstens één).", "smw-property-predefined-long-pvuc": "Uniciteit wordt vastgesteld wanneer twee waarden niet gelijk zijn in hun letterlijke weergave. <PERSON><PERSON> overtreding van die beperking wordt als fout gecategoriseerd.", "smw-datavalue-constraint-uniqueness-violation": "De eigenschap “$1” staat alleen unieke waardetoewijzingen toe en ''$2'' was al gean<PERSON><PERSON> in onderwerp “$3”.", "smw-datavalue-constraint-uniqueness-violation-isknown": "De eigenschap “$1” staat alleen unieke waarde-annotaties toe, ''$2'' bevat al een toegewezen waarde. “$3” schendt de uniciteitsvereiste.", "smw-datavalue-constraint-violation-non-negative-integer": "De eigenschap “$1” heeft een beperking “niet-negatieve gehele getallen” en de waarde “$2” schendt deze vereiste.", "smw-datavalue-constraint-violation-must-exists": "De eigenschap “$1” heeft een beperking <code>must_exists</code> en de waarde ''$2'' schendt deze vereiste.", "smw-datavalue-constraint-violation-single-value": "De eigenschap “[[Property:$1|$1]]” heeft een beperking <code>single_value</code> en de waarde “$2” schendt deze vereiste.", "smw-constraint-violation-uniqueness": "De beperking <code>unique_value_constraint</code> is toegewezen aan de eigenschap \"[[Property:$1|$1]]\" zodat deze alleen unieke waardetoewijzingen toestaat, maar de waardeannotatie ''$2'' bleek al te zijn geannoteerd in het onderwerp \"$3\".", "smw-constraint-violation-uniqueness-isknown": "Er is een beperking <code>unique_value_constraint</code> toegewezen aan de eigenschap \"[[Property:$1|$1]]\", zodat alleen annotaties met unieke waarden zijn toegestaan. ''$2'' bevat echter al een geannoteerde waarde met \"$3\", wat in strijd is met de uniciteitsbeperking voor het huidige onderwerp.", "smw-constraint-violation-non-negative-integer": "Er is een beperking <code>non_negative_integer</code> toegewezen aan de eigenschap “[[Property:$1|$1]]” en de waardeannotatie ''$2'' schendt de beperkingsvereiste.", "smw-constraint-violation-must-exists": "Er is een beperking <code>must_exists</code> toegewezen aan de eigenschap “[[Property:$1|$1]]” en de waardeannotatie ''$2'' schendt de beperkingsvereiste.", "smw-constraint-violation-single-value": "Er is een beperking <code>single_value</code> toegewezen aan de eigenschap “[[Property:$1|$1]]” en de waardeannotatie “$2” schendt de beperkingsvereiste.", "smw-constraint-violation-class-shape-constraint-missing-property": "Er is een beperking <code>shape_constraint</code> toegewezen aan de categorie “[[:$1]]” met een sleutel <code>property</code>. De vereiste eigenschap “$2” ontbreekt.", "smw-constraint-violation-class-shape-constraint-wrong-type": "Er is een beperking <code>shape_constraint</code> toegewezen aan de categorie “[[:$1]]” met een sleutel <code>property_type</code>. De eigenschap “$2” is niet van het type “$3”.", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "Er is een beperking <code>shape_constraint</code> toegewezen aan de categorie “[[:$1]]” met een sleutel <code>max_cardinality</code>. De eigenschap “$2” heeft niet de kardinaliteit van “$3”.", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "Er is een beperking <code>shape_constraint</code> toegewezen aan de categorie “[[:$1]]” met een sleutel <code>min_textlength</code>. De eigenschap “$2” voldoet niet aan de minimumlengte van “$3”.", "smw-constraint-violation-class-mandatory-properties-constraint": "Er is een beperking <code>mandatory_properties</code> toegewezen aan de categorie “[[:$1]]”. De volgende verplichte eigenschappen zijn vereist: $2", "smw-constraint-violation-allowed-namespace-no-match": "Er is een beperking <code>allowed_namespaces</code> toegewezen aan de eigenschap “[[Property:$1|$1]]” en “$2” schendt de naamruimtevereiste. Alleen de volgende naamruimten zijn toegestaan: $3", "smw-constraint-violation-allowed-namespaces-requires-page-type": "De beperking <code>allowed_namespaces</code> vereist een paginatype.", "smw-constraint-schema-category-invalid-type": "Het geannoteerde schema “$1” is ongeldig voor een categorie. Een type “$2” is vereist.", "smw-constraint-schema-property-invalid-type": "Het geannoteerde schema “$1” is ongeldig voor een eigenschap. Een type “$2” is vereist.", "smw-constraint-error-allows-value-list": "“$1” staat niet in de lijst met [[Property:Allows value|mogelijke waarden]] voor de eigenschap “$3” ($2).", "smw-constraint-error-allows-value-range": "“$1” valt niet binnen het bereik van “$2” dat is opgegeven door de beperking [[Property:Allows value|toegestane waarden]] voor de eigenschap “$3”.", "smw-property-predefined-boo": "“$1” is een [[Special:Types/Boolean|type]] en voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], om booleaanse waarden weer te geven.", "smw-property-predefined-num": "“$1” is een [[Special:Types/Number|type]] en voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], om numerieke waarden weer te geven.", "smw-property-predefined-dat": "“$1” is een [[Special:Types/Date|type]] en voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], om datumwaarden weer te geven.", "smw-property-predefined-uri": "“$1” is een [[Special:Types/URL|type]] en voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], om URI/URL-waarden weer te geven.", "smw-property-predefined-qty": "“$1” is een [[Special:Types/Quantity|type]] en voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], om hoeveelheidswaarden weer te geven.", "smw-datavalue-time-invalid-offset-zone-usage": "“$1” bevat een offset en zone-ID die niet wordt ondersteund.", "smw-datavalue-time-invalid-values": "De waarde “$1” bevat niet-interpreteerbare informatie in de vorm van “$2”.", "smw-datavalue-time-invalid-date-components-common": "“$1” bevat niet-interpreteerbare informatie.", "smw-datavalue-time-invalid-date-components-dash": "“$1” bevat een extrinsiek streepje of andere tekens die ongeldig zijn voor een datuminterpretatie.", "smw-datavalue-time-invalid-date-components-empty": "“$1” bevat enkele lege componenten.", "smw-datavalue-time-invalid-date-components-three": "“$1” bevat meer dan drie componenten die nodig zijn voor een datuminterpretatie.", "smw-datavalue-time-invalid-date-components-sequence": "“$1” bevat een reeks die niet kon worden geïnterpreteerd op basis van een beschikbare overeenkomstmatrix voor datumcomponenten.", "smw-datavalue-time-invalid-ampm": "“$1” bevat “$2” als uurelement dat ongeldig is voor een conventie van 12 uur.", "smw-datavalue-time-invalid-jd": "Kan de invoerwaarde “$1” niet interpreteren als een geldig JD-nummer (Juliaanse Dag). De teruggegeven waarde is “$2”.", "smw-datavalue-time-invalid-prehistoric": "Het is niet mogelijk een prehistorische invoerwaarde van “$1” te interpreteren. Als u bijvoorbeeld meer dan jaren of een kalendermodel heeft gespecificeerd, kan dit onverwachte resultaten opleveren in een prehistorische context.", "smw-datavalue-time-invalid": "Kan de invo<PERSON>waarde “$1” niet interpreteren als een geldig onderde<PERSON> van een datum of tijd. De teruggegeven waarde is “$2”.", "smw-datavalue-external-formatter-uri-missing-placeholder": "In de op te maken URI ontbreekt de plaatsaanduiding ''$1''.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" is een ongeldige URL.", "smw-datavalue-external-identifier-formatter-missing": "In de eigenschap ontbreekt de toewijzing van een [[Property:External formatter uri|externe URI met plaatsaanduiding]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "Voor de externe identificatie “$1” is een vervanging van meerdere velden vereist, maar aan de huidige waarde “$2” ontbreekt ten minste één waardeparameter die nodig is om aan de vereiste te voldoen.", "smw-datavalue-keyword-maximum-length": "Het sleutelwoord overschrijdt de maximale lengte van $1 teken{{PLURAL:$1||s}}.", "smw-property-predefined-eid": "“$1” is een [[Special:Types/External identifier|type]] en voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], om externe identificaties weer te geven.", "smw-property-predefined-peid": "“$1” is een voorgedefinieerde eigenschap die een externe identificatiecode specificeert. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pefu": "“$1” is een voorgedefinieerde eigenschap, geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], om een externe bron met een plaatsaanduiding op te geven.", "smw-property-predefined-long-pefu": "De URI moet een tijdelijke aanduiding bevatten die wordt aangepast met de waarde van een [[Special:Types/External identifier|externe identificatiecode]] om een geldige bronreferentie te vormen.", "smw-type-eid": "“$1” is een variant van het datatype [[Special:Types/Text|Text]] om externe bronnen te beschrijven (op basis van URI) en vereist toegewezen eigenschappen om een [[Property:External formatter uri|externe URI met plaatsaanduiding]] te declareren.", "smw-property-predefined-keyw": "“$1” is een voorgedefinieerde eigenschap en voorgedefinieerd [[Special:Types/Keyword|type]], geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki], waarmee een tekst kan worden genormaliseerd en de lengte in tekens kan worden beperkt.", "smw-type-keyw": "“$1” is een variant van het gegevenstype [[Special:Types/Text|Text]] met een beperkte tekenlengte en een genormaliseerde inhoudsweergave.", "smw-datavalue-stripmarker-parse-error": "De opgegeven waarde “$1” bevat [https://en.wikipedia.org/wiki/Help:Strip_markers ''strip markers''] en kan daarom niet in voldoende mate worden verwerkt.", "smw-datavalue-parse-error": "De opgegeven waarde “$1” is niet begrepen.", "smw-datavalue-propertylist-invalid-property-key": "De eigenschappenlijst “$1” bevatte een ongeldige eigenschapssleutel “$2”.", "smw-datavalue-type-invalid-typeuri": "Het type “$1” kon niet worden omgezet in een geldige URI-weergave.", "smw-datavalue-wikipage-missing-fragment-context": "De wikipagina-invoerwaarde “$1” kan niet worden gebruikt zonder contextpagina.", "smw-datavalue-wikipage-invalid-title": "De invoerwaarde van het paginatype, “$1”, bevat ongeldige tekens of is onvolledig en kan daarom onverwachte resultaten veroorzaken tijdens een opvraag- of annotatieproces.", "smw-datavalue-wikipage-property-invalid-title": "De eigenschap “$1” (als paginatype) met de invoerwaarde “$2” bevat ongeldige tekens of is onvolledig en kan daarom onverwachte resultaten veroorzaken tijdens een opvraag- of annotatieproces.", "smw-datavalue-wikipage-empty": "De invoerwaarde van de wikipagina is leeg (bv. <code>[[SomeProperty::]], [[]]</code>) en kan daarom niet worden gebruikt als naam of als onderdeel van een zoekopdracht-voorwaarde.", "smw-type-ref-rec": "“$1” is een [https://www.semantic-mediawiki.org/wiki/Container container]-type waarmee aanvullende informatie (bv. herkomstgegevens) over een waardetoekenning kan worden vastgelegd.", "smw-datavalue-reference-invalid-fields-definition": "Voor het type [[Special:Types/Reference|Reference]] wordt verwacht dat een lijst met eigenschappen wordt g<PERSON><PERSON><PERSON><PERSON><PERSON> met behul<PERSON> van de eigenschap [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields Has fields].", "smw-parser-invalid-json-format": "In de JSON-parser is een fout opgetreden: “$1”.", "smw-property-preferred-label-language-combination-exists": "“$1” kan niet als voorkeurslabel worden gebruikt omdat de taal “$2” al is toegewezen aan het label “$3”.", "smw-clipboard-copy-link": "Verwijzing naar het klembord kopiëren", "smw-property-userdefined-fixedtable": "“$1” is ingesteld als [https://www.semantic-mediawiki.org/wiki/Fixed_properties vaste eigenschap] en elke wijziging aan de [https://www.semantic-mediawiki.org/wiki/Type_declaration typedeclaratie] ervan vereist het uitvoeren van <code>setupStore.php</code> of het voltooien van de speciale taak [[Special:SemanticMediaWiki|“Database installeren en bijwerken”]].", "smw-data-lookup": "<PERSON><PERSON><PERSON><PERSON>…", "smw-data-lookup-with-wait": "Het verzoek wordt verwerkt en kan even duren.", "smw-no-data-available": "<PERSON><PERSON> g<PERSON><PERSON>", "smw-property-req-violation-missing-fields": "In de eigenschap “$1” ontbreekt een door het type “$2” vereiste verklaring [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>].", "smw-property-req-violation-multiple-fields": "Eigenschap \"$1\" bevat meerdere (en dus concurrerende) declaraties [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>]. Er wordt slechts één verwacht voor dit gegevenstype \"$2\".", "smw-property-req-violation-missing-formatter-uri": "In de eigenschap “$1” ontbreken de verklaringsgegevens voor het geannoteerde type omdat de eigenschap <code>External formatter URI</code> niet is gedefinieerd.", "smw-property-req-violation-predefined-type": "De eigenschap “$1” als voorgedefinieerde eigenschap bevat een verklaring van het type “$2” die niet compatibel is met het standaardtype van deze eigenschap.", "smw-property-req-violation-import-type": "Er is een typedeclaratie gevonden die niet verenigba<PERSON> is met het voorgedefinieerde type van de geïmporteerde woordenschat “$1”. Over het algemeen is het niet nodig om een type te declareren, want die informatie wordt uit de importdefinitie verkregen.", "smw-property-req-violation-change-propagation-locked-error": "De eigenschap “$1” is gew<PERSON><PERSON><PERSON><PERSON>, waardoor de toegewezen entiteiten opnieuw moeten worden geëvalueerd met behulp van een [https://www.semantic-mediawiki.org/wiki/Change_propagation wijzigingspropagatie]-proces. Om tussentijdse onderbrekingen of tegenstrijdige specificaties te voorkomen is de eigenschapspagina vergrendeld totdat het bijwerken van de primaire specificatie is voltooid. Het kan even duren voordat de pagina weer kan worden ontgrendeld; dit hangt af van de grootte en planningsfrequentie van de [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue taakwachtrij].", "smw-property-req-violation-change-propagation-locked-warning": "De eigenschap “$1” is g<PERSON><PERSON><PERSON><PERSON><PERSON>, waardoor de toegewezen entiteiten opnieuw moeten worden geëvalueerd met behulp van een [https://www.semantic-mediawiki.org/wiki/Change_propagation wijzigingspropagatie]-proces. Het bijwerken kan even duren; het hangt af van de grootte en planningsfrequentie van de [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue taakwachtrij]. Om tussentijdse onderbrekingen of tegenstrijdige specificaties te voorkomen wordt het aanbevolen geen wijzigingen aan de eigenschap aan te brengen voordat het bijwerken voltooid is.", "smw-property-req-violation-change-propagation-pending": "<PERSON>r zijn nog [https://www.semantic-mediawiki.org/wiki/Change_propagation wijzigingen in behandeling] (ongeveer $1 [https://www.mediawiki.org/wiki/Manual:Job_queue {{PLURAL:$1|taak|taken}}]). Het wordt aangeraden om te wachten met aanpassingen aan een eigenschap tot het proces is afgerond, dit om tussentijdse onderbrekingen of tegenstrijdige specificaties te voorkomen.", "smw-property-req-violation-missing-maps-extension": "Semantic MediaWiki heeft de vereiste uitbreiding [https://www.semantic-mediawiki.org/wiki/Extension:Maps “Maps”] niet kunnen vinden. Als gevolg daarvan kan deze eigenschap geen geografische gegevens opslaan of verwerken.", "smw-property-req-violation-type": "De eigenschap bevat concurrerende typespecificaties die kunnen leiden tot ongeldige waarde-annotaties. Daarom wordt verwacht dat een gebruiker één geschikt type toewijst.", "smw-property-req-error-list": "De eigenschap bevat de volgende fouten of waarschuwingen:", "smw-property-req-violation-parent-type": "De eigenschap “$1” en de toegewezen bovenliggende eigenschap “$2” hebben verschillende type-annotaties.", "smw-property-req-violation-forced-removal-annotated-type": "De [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance verplichte overerving van het bovenliggende type] is ingeschakeld. Het annotatietype “$1” van de eigenschap komt niet overeen met het type “$2” van de bovenliggende eigenschap en is gewijzigd om aan deze eis te voldoen. Het wordt aanbevolen de typedefinitie op de pagina zo aan te passen dat de foutmelding en typehandhaving voor deze eigenschap worden verwijderd.", "smw-change-propagation-protection": "Deze pagina is vergrendeld om onbedoelde gegevenswijziging te voorkomen terwijl de [https://www.semantic-mediawiki.org/wiki/Change_propagation propagatie van wijzigingen] in uitvoering is. Het kan even duren voordat de pagina wordt ontgrendeld; het hangt af van de grootte en planningsfrequentie van de [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue taakwachtrij].", "smw-category-change-propagation-locked-error": "De categorie “$1” is g<PERSON><PERSON><PERSON><PERSON><PERSON>, waardoor de toegewezen entiteiten opnieuw moeten worden geëvalue<PERSON> met behulp van een [https://www.semantic-mediawiki.org/wiki/Change_propagation wijzigingspropagatie]-proces. Om tussentijdse onderbrekingen of tegenstrijdige specificaties te voorkomen is de categoriepagina vergrendeld totdat het bijwerken van de primaire specificatie is voltooid. Het kan even duren voordat de pagina weer kan worden ontgrendeld; dit hangt af van de grootte en planningsfrequentie van de [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue taakwachtrij].", "smw-category-change-propagation-locked-warning": "De categorie “$1” is g<PERSON><PERSON><PERSON><PERSON><PERSON>, waardoor de toegewezen entiteiten opnieuw moeten worden geëvalue<PERSON> met behulp van een [https://www.semantic-mediawiki.org/wiki/Change_propagation wijzigingspropagatie]-proces. Het bijwerken kan even duren; het hangt af van de grootte en planningsfrequentie van de [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue taakwachtrij]. Om tussentijdse onderbrekingen of tegenstrijdige specificaties te voorkomen wordt het aanbevolen geen wijzigingen aan de categorie aan te brengen voordat het bijwerken voltooid is.", "smw-category-change-propagation-pending": "<PERSON>r zijn nog [https://www.semantic-mediawiki.org/wiki/Change_propagation wijzigingen in behandeling] (ongeveer $1 [https://www.mediawiki.org/wiki/Manual:Job_queue {{PLURAL:$1|taak|taken}}]). Het wordt aangeraden om te wachten met aanpassingen aan een categorie tot het proces is afgerond, dit om tussentijdse onderbrekingen of tegenstrijdige specificaties te voorkomen.", "smw-category-invalid-value-assignment": "“$1” wordt niet herkend als geldige categorie of waardeannotatie.", "protect-level-smw-pageedit": "Alleen gebruike<PERSON> <PERSON><PERSON><PERSON> met toestem<PERSON> voor het bewerken van pagina’s (Semantic MediaWiki)", "smw-create-protection": "Het aanmaken van de eigenschap “$1” is beperkt tot gebruikers met het recht (of de [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups gebruikersgroep]) “$2” zolang de [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode autoriteitsmodus] is ingeschakeld.", "smw-create-protection-exists": "Het wijzigen van de eigenschap “$1” is beperkt tot gebruikers met het recht (of de [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups gebruikersgroep]) “$2” zolang de [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode autoriteitsmodus] is ingeschakeld.", "smw-edit-protection": "Deze pagina is [[Property:Is edit protected|beveiligd]] ter voor<PERSON><PERSON> van onbedoelde gegevenswijziging en kan alleen worden bewerkt door gebruikers met het juiste recht (“$1”) of die lid zijn van de juiste [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups gebruikersgroep].", "smw-edit-protection-disabled": "Het beveiligen tegen bewerken is uitgeschakeld en daarom kan “$1” niet worden gebruikt om entiteitspagina’s te beveiligen tegen ongeoorloofd bewerken.", "smw-edit-protection-auto-update": "Semantic MediaWiki heeft de beveiligingsstatus bijgewerkt volgens de eigenschap “Is beveiligd tegen bewerken”.", "smw-edit-protection-enabled": "Beveiligd tegen bewerken (Semantic MediaWiki)", "smw-patternedit-protection": "Deze pagina is beveiligd en kan alleen worden bewerkt door gebruikers met de [https://www.semantic-mediawiki.org/wiki/Help:Permissions toestemming] <code>smw-patternedit</code>.", "smw-property-predefined-edip": "“$1” is een voorgedefinieerde eigenschap die wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]. Deze eigenschap geeft aan of de pagina al dan niet tegen bewerken beveiligd is.", "smw-property-predefined-long-edip": "<PERSON><PERSON>el elke gebruiker deze eigenschap aan een onderwerp mag toevoegen, kan alleen een gebruiker met een speciale machtiging de bescherming van een entiteit bewerken of intrekken nadat die is toegevoegd.", "smw-query-reference-link-label": "Referentie naar de zoekopdracht", "smw-format-datatable-emptytable": "Geen data beschik<PERSON> in de tabel", "smw-format-datatable-info": "Weergave _START_ tot _END_ van _TOTAL_ resultaten", "smw-format-datatable-infoempty": "Weergave 0 to 0 van 0 resultaten", "smw-format-datatable-infofiltered": "(gefilterd uit _MAX_ resultaten)", "smw-format-datatable-infothousands": ".", "smw-format-datatable-lengthmenu": "Toon _MENU_ resultaten", "smw-format-datatable-loadingrecords": "Bezig met laden…", "smw-format-datatable-processing": "Verwerken...", "smw-format-datatable-search": "Zoekopdracht:", "smw-format-datatable-zerorecords": "<PERSON><PERSON> overeenkomende resultaten gevonden", "smw-format-datatable-first": "Eerste", "smw-format-datatable-last": "Laatste", "smw-format-datatable-next": "Volgende", "smw-format-datatable-previous": "Vorige", "smw-format-datatable-sortascending": ": <PERSON>ren om de kolom oplopend te sorteren", "smw-format-datatable-sortdescending": ": <PERSON>ren om de kolom aflopend te sorteren", "smw-format-datatable-toolbar-export": "Exporteren", "smw-category-invalid-redirect-target": "De categorie “$1” bevat een onjuiste doorverwijzing naar een pagina die zich niet in de categorie-naam<PERSON><PERSON><PERSON> bevindt.", "smw-parser-function-expensive-execution-limit": "De parserfunctie heeft de grens voor veeleisende verrichtingen bereikt (zie de configuratieparameter [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "Semantic MediaWiki ververst deze pagina al naargelang deze na het uitvoeren van que<PERSON>’s moet worden bijgewerkt.", "apihelp-smwinfo-summary": "API-module om informatie op te halen over Semantic MediaWiki-statistieken en andere metagegevens.", "apihelp-ask-summary": "API-module om Semantic MediaWiki te bevragen met be<PERSON><PERSON> “ask”.", "apihelp-askargs-summary": "API-module om Semantic MediaWiki te bevragen met be<PERSON><PERSON> van <PERSON> “ask” aan de hand van een lijst met voorwaarden, uitvoeraanwijzingen en parameters.", "apihelp-browsebyproperty-summary": "API-module om informatie over een eigenschap of een lijst met eigenschappen op te vragen.", "apihelp-browsebysubject-summary": "API-module om informatie over een onderwerp op te vragen.", "apihelp-smwtask-summary": "API-module om Semantic MediaWiki-g<PERSON><PERSON><PERSON><PERSON> taken uit te voeren (alleen voor intern gebruik, niet voor openbaar gebruik).", "apihelp-smwbrowse-summary": "API-module ter ondersteuning van het doorbladeren van verschillende entiteitstypen in Semantic MediaWiki.", "apihelp-ask-parameter-api-version": "Uitvoeropmaak:\n;2:<PERSON><PERSON><PERSON><PERSON><PERSON>s compatibele indeling met {} voor de resultatenlijst.\n;3:Experimentele indeling met [] als resultatenlijst.", "apihelp-smwtask-param-task": "Definieert het taaktype", "apihelp-smwtask-param-params": "In JSON gecodeerde parameters die overeenkomen met de geselecteerde taaktypevereiste", "smw-apihelp-smwtask-example-update": "<PERSON><PERSON><PERSON><PERSON> van het uitvoeren van een bijwerktaak voor een bepaald onderwerp:", "smw-api-invalid-parameters": "Onjuiste parameters, “$1”", "smw-parser-recursion-level-exceeded": "Het recursieniveau is tijdens een ontleedproces de $1 gepasseerd. Het wordt aangeraden om de sjabloonstructuur op juistheid te controleren, of zo nodig de configuratieparameter <code>$maxRecursionDepth</code> aan te passen.", "smw-property-page-list-count": "{{PLURAL:$1|Eén pagina gebruikt|$1 pagina’s gebruiken}} deze eigenschap.", "smw-property-page-list-search-count": "{{PLURAL:$1|Eén pagina gebruikt|$1 pagina’s gebruiken}} deze eigenschap met een bij “$2” passende waarde.", "smw-property-page-filter-note": "Het [https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter zoekfilter] maakt het mogelijk [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions query-expressies] zoals <code>~</code> of <code>!</code> in te voegen. De geselecteerde [https://www.semantic-mediawiki.org/wiki/Query_engine query-engine] ondersteunt mogelijk ook hoofdletter-ongevoelig zoeken of andere korte uitdrukkingen zoals:\n\n* <code>in:</code> resultaat moet de term bevatten, bv. ‘<code>in:Foo</code>’\n\n* <code>not:</code> het resultaat mag de term niet bevatten, bv. ‘<code>not:Bar</code>’", "smw-property-reserved-category": "Categorie", "smw-category": "Categorie", "smw-datavalue-uri-invalid-scheme": " “$1” is niet vermeld als geldig URI-schema.", "smw-datavalue-uri-invalid-authority-path-component": "Er is vastgesteld dat “$1” een ongeldige autoriteits- of padcomponent “$2” bevat.", "smw-browse-property-group-title": "Eigenschappengroep", "smw-browse-property-group-label": "Label eigenschappengroep", "smw-browse-property-group-description": "Beschrijving eigenschappengroep", "smw-property-predefined-ppgr": "“$1” is een vooraf gedefinieerde eigenschap die entiteiten (voornamelijk categorieën) identificeert die worden gebruikt als groeperingsinstantie voor eigenschappen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-filter": "Filter", "smw-section-expand": "De selectie uitvouwen", "smw-section-collapse": "De selectie invouwen", "smw-ask-format-help-link": "Formaat [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON>", "smw-cheat-sheet": "Spiekbriefje", "smw-personal-jobqueue-watchlist": "Volglijst taakwachtrij", "smw-personal-jobqueue-watchlist-explain": "De getallen geven een schatting aan van het aantal nog uit te voeren taken in de wachtrij.", "smw-property-predefined-label-skey": "Sorteersleutel", "smw-processing": "Verwerken…", "smw-loading": "<PERSON>den…", "smw-fetching": "<PERSON><PERSON><PERSON>…", "smw-preparing": "Voorbereiden…", "smw-expand": "Uitvouwen", "smw-collapse": "Samenvouwen", "smw-copy": "<PERSON><PERSON><PERSON><PERSON>", "smw-copy-clipboard-title": "<PERSON><PERSON>d naar het klembord kopiëren", "smw-jsonview-expand-title": "Vouwt de JSON-weergave uit", "smw-jsonview-collapse-title": "Vouwt de JSON-weergave samen", "smw-jsonview-search-label": "<PERSON><PERSON>:", "smw-redirect-target-unresolvable": "Het doel kan niet worden bepaald vanwege “$1”", "smw-types-title": "Type: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Het wijzigen van het inhoudsmodel van een [https://www.semantic-mediawiki.org/wiki/Help:Schema schemapagina] is niet toe<PERSON>.", "smw-schema-namespace-edit-protection": "Deze pagina is beveiligd en kan alleen worden bewerkt door gebruikers met de [https://www.semantic-mediawiki.org/wiki/Help:Permissions toestemming] <code>smw-schemaedit</code>.", "smw-schema-namespace-edit-protection-by-import-performer": "Deze pagina is geïmporteerd door een vermelde [https://www.semantic-mediawiki.org/wiki/Import_performer ''import performer'']. Dit betekent dat het wijzigen van de inhoud van deze pagina voorbehouden is aan de vermelde gebruikers.", "smw-schema-error-title": "Valideerfout{{PLURAL:$1||en}}", "smw-schema-error-schema": "A<PERSON> de hand van het validatieschema '''$1''' zijn de volgende tegenstrijdigheden aangetroffen:", "smw-schema-error-miscellaneous": "Overige fout ($1)", "smw-schema-error-validation-json-validator-inaccessible": "<PERSON> JSON-validator <b>“$1”</b> is niet <PERSON> (of niet geïnstalleerd). <PERSON>ard<PERSON> kan het bestand “$2” niet worden beoor<PERSON>eld, wat het onmogelijk maakt de huidige pagina op te slaan of te wijzigen.", "smw-schema-error-validation-file-inaccessible": "Het validatiebestand “$1” is niet <PERSON>.", "smw-schema-error-type-missing": "Er ontbreekt een typeaanduiding voor de inhoud, zodat deze kan niet worden herkend en gebruikt in de [https://www.semantic-mediawiki.org/wiki/Help:Schema schemanaamruimte].", "smw-schema-error-type-unknown": "Het type “$1” is niet geregistreerd en kan niet worden gebruikt voor inhoud in de naamruimte [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-error-json": "JSON-fout: “$1”", "smw-schema-error-input": "Bij de invoervalidatie zijn de volgende problemen aangetroffen. Deze moeten worden opgelost voordat de inhoud kan worden opgeslagen. De [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling hulppagina over schema’s] kan advies geven over het wegnemen van tegens<PERSON> of het oplossen van problemen met de schema-invoer.", "smw-schema-error-input-schema": "A<PERSON> de hand van het validatieschema '''$1''' zijn de volgende tegenstrijdigheden aangetroffen. Deze moeten worden verholpen voordat de inhoud kan worden opgeslagen. De [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling hulppagina over schema’s] kan advies geven over het oplossen van deze problemen.", "smw-schema-error-title-prefix": "Voor dit schematype is vereist dat de titel van het schema begint met het voorvoegsel “$1”.", "smw-schema-validation-error": "Het type “$1” is niet geregistreerd en kan niet worden gebruikt voor inhoud in de naamruimte [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema].", "smw-schema-validation-schema-title": "JSON-schema", "smw-schema-summary-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-schema-title": "<PERSON><PERSON><PERSON>", "smw-schema-usage": "Gebruik", "smw-schema-type": "Type schema", "smw-schema-type-description": "Beschrijving type", "smw-schema-description": "Beschrijving schema", "smw-schema-description-link-format-schema": "Dit schematype ondersteunt de definitie van kenmerken voor het aanmaken van contextgevoelige koppelingen in verband met een aan een [[Property:Formatter schema|formatteerschema]] toegewezen eigenschap.", "smw-schema-description-search-form-schema": "Dit schematype ondersteunt de definitie van invoerformulieren en kenmerken voor het profiel [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch uitgebreid zoeken], waar het instructies bevat over het genereren van invoervelden, het defini<PERSON><PERSON> van standaardn<PERSON>mru<PERSON>, of het declareren van voorvoegsel-expressies voor een zoekopdracht.", "smw-schema-description-property-profile-schema": "Dit schematype ondersteunt de definitie van een profiel om kenmerken te declareren voor de toegewezen eigenschap en de bijbehorende annotatiewaarden.", "smw-schema-description-facetedsearch-profile-schema": "Dit schematype ondersteunt het definiëren van profielen die worden gebruikt als onderdeel van de omgeving voor [[Special:FacetedSearch|gefacetteerd zoeken]].", "smw-schema-description-property-group-schema": "Dit schematype ondersteunt de definitie van [https://www.semantic-mediawiki.org/wiki/Help:Property_group eigenschapsgroepen] om de [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse browsing-interface] te helpen structureren", "smw-schema-description-property-constraint-schema": "Dit ondersteunt de definitie van beperkingsregels voor een eigenschapsinstantie en voor de waarden die eraan zijn toegewezen.", "smw-schema-description-class-constraint-schema": "Dit schematype ondersteunt de definitie van beperkingsregels voor een klasse-instantie (ook wel categorie genoemd).", "smw-schema-tag": "{{PLURAL:$1|Tag|Tags}}", "smw-property-predefined-constraint-schema": "“$1” is een voorgedefinieerde eigenschap die een beperkingsschema definieert. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-desc": "“$1” is een voorgedefinieerde eigenschap waarin een schemabeschrijving wordt opgeslagen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-def": "“$1” is een voorgedefinieerde eigenschap waarin de schema-inhoud wordt opgeslagen. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-tag": "“$1” is een voorgedefinieerde eigenschap waarmee een verzameling schema’s wordt geïdentificeerd. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-schema-tag": "Een label dat schema’s van vergelijkbare inhoud of kenmerken identificeert.", "smw-property-predefined-schema-type": "“$1” is een voorgedefinieerde eigenschap die een type beschrijft om een groep schema’s te onderscheiden. Deze eigenschap wordt geleverd door [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-schema-type": "Elk [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type type] biedt zijn eigen interpretatie van syntaxiselementen en toepassingsregels en kan worden uitgedrukt met behulp van een [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation validatieschema].", "smw-ask-title-keyword-type": "Zoeken op sleutelwoorden", "smw-ask-message-keyword-type": "<PERSON><PERSON> past bij de voorwa<PERSON> <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Kan geen verbinding maken met het externe doel “$1”.", "smw-remote-source-disabled": "De bron '''$1''' heeft de ondersteuning voor verzoeken op afstand uitgeschakeld!", "smw-remote-source-unmatched-id": "De bron '''$1''' gebruikt een versie van Semantic MediaWiki die geen externe verzoeken kan ondersteunen.", "smw-remote-request-note": "Het resultaat wordt opgevraagd bij de externe bron '''$1'''  en het is waarschijnlijk dat de gegenereerde inhoud informatie bevat die niet besch<PERSON> is vanuit de huidige wiki.", "smw-remote-request-note-cached": "Het resultaat is '''in de cache''' van de externe bron '''$1''' opgeslagen en het is waarschijnlijk dat de gegenereerde inhoud informatie bevat die niet besch<PERSON> is vanuit de huidige wiki.", "smw-parameter-missing": "Parameter \"$1\" ontbreekt.", "smw-property-tab-usage": "Gebruik", "smw-property-tab-profile-schema": "Profielschema", "smw-property-tab-redirects": "Synoniemen", "smw-property-tab-subproperties": "Subeigenschappen", "smw-property-tab-errors": "<PERSON><PERSON><PERSON><PERSON>", "smw-property-tab-constraint-schema": "Beperkingsschema", "smw-property-tab-constraint-schema-title": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>sschema", "smw-property-tab-specification": "... meer", "smw-concept-tab-list": "Lijst", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-ask-tab-result": "Resultaat", "smw-ask-tab-extra": "Extra", "smw-ask-tab-debug": "Foutopsporing", "smw-ask-tab-code": "Code", "smw-install-incomplete-tasks-title": "Onvolledige administratieve taken", "smw-install-incomplete-intro": "Er {{PLURAL:$2|is <PERSON><PERSON>|zijn $2}} onvoltooide of [[Special:PendingTaskList|openstaande]] {{PLURAL:$2|taak, die nodig is|taken, die nodig zijn}} om het {{PLURAL:$1|installeren|bijwerken}} van [https://www.semantic-mediawiki.org Semantic MediaWiki] te voltooien. Een beheerder of gebruiker met voldoende rechten kan deze uitvoeren. Dit moet worden gedaan voordat nieuwe gegevens worden toegevoegd om tegenstrijdigheden te voorkomen.", "smw-install-incomplete-intro-note": "Dit bericht verdwijnt nadat alle relevante taken zijn afgerond.", "smw-pendingtasks-intro-empty": "<PERSON><PERSON> <PERSON><PERSON><PERSON> geen taken geclassificeerd als in behandeling, onvoltooid of openstaand in verband met Semantic MediaWiki.", "smw-pendingtasks-intro": "Op deze pagina staat informatie over taken die zijn geclassificeerd als in behandeling, onvo<PERSON>ig of openstaand in verband met Semantic MediaWiki.", "smw-pendingtasks-setup-no-tasks-intro": "De installatie (of het bijwerken) is voltooid. <PERSON><PERSON> zijn er geen lopende of openstaande taken.", "smw-pendingtasks-tab-setup": "Installatie", "smw-updateentitycollation-incomplete": "De <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]</code> zetting werd recent gewijzigd en vereist dat het <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> script uitgevoerd wordt zodat entiteiten geüpdated worden en de juiste sorteerveldwaarde bevatten.", "smw-updateentitycountmap-incomplete": "Het veld <code>smw_countmap</code> is toegevoegd in een onlangs uitgebrachte versie en vereist dat het script <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> wordt uitgevoerd zodat functies toegang kunnen krijgen tot de inhoud van dit veld.", "smw-populatehashfield-incomplete": "Het invullen van het veld <code>smw_hash</code> is overgeslagen tijdens de installatie. Het script <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> moet worden uitgevoerd.", "smw-install-incomplete-populate-hash-field": "Het invullen van het veld <code>smw_hash</code> is overgeslagen tijdens de installatie. Het script <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> moet worden uitgevoerd.", "smw-install-incomplete-elasticstore-indexrebuild": "Er is voor <code>ElasticStore</code> gekozen als [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore standaardwinkel], maar de uitbreiding kon geen aanwijzing vinden dat het script <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> is uitgevoerd. Voer dat script uit zoals is aangegeven.", "smw-elastic-rebuildelasticindex-run-incomplete": "Er is voor <code>ElasticStore</code> gekozen als [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore standaardwinkel], maar de uitbreiding kon geen aanwijzing vinden dat het script <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> is uitgevoerd. Voer dat script uit zoals is aangegeven.", "smw-pendingtasks-setup-intro": "Na het {{PLURAL:$1|installeren|bijwerken}} van <b>Semantic MediaWiki</b> z<PERSON><PERSON> de <PERSON> taken als [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade onvoltooid] aangemerkt. <PERSON><PERSON> beheerde<PERSON> (of andere gebruiker met voldo<PERSON>e rechten) moet deze taken voltooien voordat gebruikers weer inhoud kunnen aanmaken of bewerken.", "smw-pendingtasks-setup-tasks": "Taken", "smw-filter-count": "Aantal filters", "smw-es-replication-check": "Replicatiecontrole (Elasticsearch)", "smw-es-replication-error": "Replicatieprobleem Elasticsearch", "smw-es-replication-file-ingest-error": "<PERSON><PERSON>em met bestand<PERSON><PERSON>", "smw-es-replication-maintenance-mode": "Onderhoud Elasticsearch", "smw-es-replication-error-missing-id": "Uit de replicatiecontrole is gebleken dat het artikel “$1” (ID: $2) ontbreekt in de Elasticsearch-backend.", "smw-es-replication-error-divergent-date": "Uit de replicatiecontrole is gebleken dat voor het artikel “$1” (ID: $2) de <b>wijzigingsdatum</b> een discrepantie vertoont.", "smw-es-replication-error-divergent-date-short": "De volgende datuminformatie is gebruikt voor de vergelijking:", "smw-es-replication-error-divergent-date-detail": "Wijzigingsdatum waarnaar wordt verwezen:\n*Elasticsearch: $1 \n*Database: $2", "smw-es-replication-error-divergent-revision": "Uit de replicatiecontrole is gebleken dat voor het artikel “$1” (ID: $2) de <b>bijbehorende versie</b> een discrepantie vertoont.", "smw-es-replication-error-divergent-revision-short": "De volgende gegevens m.b.t. bijbehorende versies zijn gebruikt voor de vergelijking:", "smw-es-replication-error-divergent-revision-detail": "Bijbehorende versie waarnaar wordt verwezen:\n*Elasticsearch: $1 \n*Database: $2", "smw-es-replication-error-maintenance-mode": "De Elasticsearch-replicatie is momenteel beperkt omdat Elasticsearch in een [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>onderhoudsmodus</b>] werkt. Wijzigingen in entiteiten en pagina’s zijn <b>niet</b> meteen zichtbaar en de zoekresultaten kunnen verouderde informatie bevatten.", "smw-es-replication-error-no-connection": "Er kunnen geen replicatiecontroles worden verricht omdat er geen verbinding met de Elasticsearch-cluster tot stand kan worden gebracht.", "smw-es-replication-error-bad-request-exception": "Bij het tot stand brengen van een verbinding is van Elasticsearch een fout m.b.t. een ongeldig verzoek uitgegaan (“400 conflict http error”), wat aangeeft dat er een aanhoudend probleem is tijdens replicatie- en zoekaanvragen.", "smw-es-replication-error-other-exception": "Bij het tot stand brengen van een verbinding is van Elasticsearch een fout uitgegaan: “$1”.", "smw-es-replication-error-suggestions": "Er wordt voorgesteld om de pagina te bewerken of op te schonen om de discrepantie weg te nemen. Als het probleem zich blijft voordoen, controleer dan de Elasticsearch-cluster zelf (allocator, fouten, schijfruimte, enz.).", "smw-es-replication-error-suggestions-maintenance-mode": "Er wordt voorgesteld om contact op te nemen met de wikibeheerder om te kijken of het systeem momenteel bezig is met het [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild opnieuw opbouwen van de index] of de <code>refresh_interval</code> niet is ingesteld op de verwachte standaardwaarde.", "smw-es-replication-error-suggestions-no-connection": "Er wordt voorgesteld om contact op te nemen met de wikibeheerder en het probleem “geen verbinding” te melden.", "smw-es-replication-error-suggestions-exception": "<PERSON><PERSON> in de logboeken voor informatie over de status van Elasticsearch, de indices ervan, en mogelijke problemen met verkeerde instellingen.", "smw-es-replication-error-file-ingest-missing-file-attachment": "Uit de replicatiecontrole is gebleken dat in “$1” de annotatie [[Property:File attachment|Bestandsbijlage]] ontbreekt, wat aangeeft dat het proces voor bestandsinname niet is gestart of nog niet is voltooid.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Zorg ervoor dat de taak [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion bestandsinname] wordt gepland en uitgevoerd voordat de annotatie en bestandsindex beschikbaar worden gemaakt.", "smw-report": "Verslag", "smw-legend": "<PERSON>a", "smw-datavalue-constraint-schema-category-invalid-type": "Het geannoteerde schema “$1” is ongeldig voor een categorie. Een type “$2” is vereist.", "smw-datavalue-constraint-schema-property-invalid-type": "Het geannoteerde schema “$1” is ongeldig voor een eigenschap. Een type “$2” is vereist.", "smw-entity-examiner-check": "In de achtergrond {{PLURAL:$1|draait momenteel een proces|draaien momenteel $1 processen}} voor entiteitscontrole", "smw-entity-examiner-indicator": "Paneel voor entiteitsproblemen", "smw-entity-examiner-deferred-check-awaiting-response": "De controle “$1” wacht momenteel een antwoord af van de backend.", "smw-entity-examiner-deferred-elastic-replication": "Elasticsearch", "smw-entity-examiner-deferred-constraint-error": "Be<PERSON>king", "smw-entity-examiner-associated-revision-mismatch": "<PERSON><PERSON><PERSON>", "smw-entity-examiner-deferred-fake": "Nep", "smw-entity-examiner-indicator-suggestions": "<PERSON><PERSON> on<PERSON><PERSON><PERSON> van de entiteitscontrole {{PLURAL:$1|is het volgende probleem|zijn de volgende problemen}} gevonden. Het wordt aanbevolen {{PLURAL:$1|het probleem|de problemen}} nauwkeurig te bekijken en de nodige maatregelen te treffen.", "smw-indicator-constraint-violation": "Beperking{{PLURAL:$1||en}}", "smw-indicator-revision-mismatch": "<PERSON><PERSON><PERSON>", "smw-indicator-revision-mismatch-error": "Bij de controle op de [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner bijbehorende versie] is een discrepantie aangetroffen tussen de versie waarnaar in MediaWiki wordt verwezen en de versie die in Semantic MediaWiki aan deze entiteit is gekoppeld.", "smw-indicator-revision-mismatch-comment": "Een afwijking duidt er normaal gesproken op dat een of ander proces de opslagbewerking in Semantic MediaWiki heeft onderbroken. Het wordt aanbevolen de serverlogboeken te bekijken en te zoeken naar foutmeldingen of storingen.", "smw-facetedsearch-intro-text": "Met het [https://www.semantic-mediawiki.org/wiki/Faceted_search <b>gefacetteerd zoeken</b>] in Semantic MediaWiki kunnen gebruikers snel en eenvoudig op basis van een gestelde voorwaarde de zoekresultaten beperken. De weergave wordt daarbij aan de hand van de afhankelijke eigenschappen en categorieën in facetten verdeeld.", "smw-facetedsearch-intro-tips": "*Met <code>category:?</code>, <code>property:?</code> of <code>concept:?</code> vindt u de beschikbare categorieën, eigenschappen of concepten om een voorwaarde mee samen te stellen\n*Gebruik de syntaxis van #ask om een voorwaarde te beschrijven (bv. <code><nowiki>[[Category:Foo]]</nowiki></code>)\n*Met “OR” (of), “AND” (en) of andere zoekexpressies maakt u complexe voorwaarden\n*Met expressies zoals <code>in:</code> of <code>phrase:</code> kan in de volledige tekst of ongestructureerd worden gezocht, indien het uitgekozen [https://www.semantic-mediawiki.org/wiki/Query_engine zoekmechanisme] die expressies kan hanteren", "smw-facetedsearch-profile-label-default": "Standaardprofiel", "smw-facetedsearch-intro-tab-explore": "Verkennen", "smw-facetedsearch-intro-tab-search": "<PERSON><PERSON>", "smw-facetedsearch-explore-intro": "Kies een verzameling en kijk erin rond.", "smw-facetedsearch-profile-options": "Profielopties", "smw-facetedsearch-size-options": "Pagineringsopties", "smw-facetedsearch-order-options": "Volgorde-opties", "smw-facetedsearch-format-options": "Weergaveopties", "smw-facetedsearch-format-table": "<PERSON><PERSON>", "smw-facetedsearch-input-filter-placeholder": "Filteren…", "smw-facetedsearch-no-filters": "<PERSON>n filters.", "smw-facetedsearch-no-filter-range": "<PERSON><PERSON>.", "smw-facetedsearch-no-output": "Voor het gekozen formaat “$1” is geen uitvoer beschik<PERSON>ar.", "smw-facetedsearch-clear-filters": "Filter{{PLURAL:$1||s}} wissen", "smw-search-placeholder": "<PERSON><PERSON>…", "smw-listingcontinuesabbrev": "meer", "smw-showingresults": "Hieronder {{PLURAL:$1|staat '''1''' resultaat|staan '''$1''' resultaten}} vanaf #'''$2'''."}