{"error_types": {"ERROR_SCHEMA_INVALID_KEY": {"indicator_title": "smw-setupcheck-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "smw-setupcheck-invalid-upgrade-key"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-why-this-page"}, {"type": "paragraph", "text": "smw-setupcheck-reason-for-invalid-upgrade-key"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-administrator-assistance"}, {"type": "paragraph", "text": "smw-setupcheck-install-assistance"}]}, "ERROR_DB_REQUIREMENT_INCOMPATIBLE": {"indicator_title": "smw-setupcheck-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "smw-setupcheck-requires-db-minimum-version"}, {"type": "db-requirement", "text": "smw-setupcheck-db-requirement"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-requires-software-update"}, {"type": "paragraph", "text": "smw-setupcheck-temporarily-disable"}]}, "ERROR_EXTENSION_INCOMPATIBLE": {"indicator_title": "smw-setupcheck-dependency-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "ERROR_TEXT"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-requires-software-update"}]}, "ERROR_EXTENSION_DEPENDENCY": {"indicator_title": "smw-setupcheck-dependency-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "ERROR_TEXT"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-dependency-requires-semanticmediawiki"}, {"type": "paragraph", "text": "smw-setupcheck-enablesemantics-assistance"}]}, "ERROR_EXTENSION_DEPENDENCY_MULTIPLE": {"indicator_title": "smw-setupcheck-dependency-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "smw-setupcheck-dependency-error-multiple"}, {"type": "paragraph", "text": "ERROR_TEXT_MULTIPLE"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-dependency-multiple-requires-semanticmediawiki"}, {"type": "paragraph", "text": "smw-setupcheck-enablesemantics-assistance"}]}, "ERROR_EXTENSION_REGISTRY": {"indicator_title": "smw-setupcheck-registry-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "smw-setupcheck-registry-enablesemantics-conflict"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-remove-wfloadextension"}, {"type": "paragraph", "text": "smw-setupcheck-enablesemantics-assistance"}], "stack_trace": [{"type": "section", "text": "smw-setupcheck-stack-trace"}, {"type": "paragraph", "text": "smw-setupcheck-stack-trace-abort-condition"}, {"type": "errorbox", "text": "ERROR_TEXT"}, {"type": "errorbox", "text": "TRACE_STRING"}]}, "ERROR_EXTENSION_LOAD": {"indicator_title": "smw-setupcheck-registry-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "smw-setupcheck-registry-invalid-access"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-registry-requires-enablesemantics"}, {"type": "paragraph", "text": "smw-setupcheck-enablesemantics-assistance"}], "stack_trace": [{"type": "section", "text": "smw-setupcheck-stack-trace"}, {"type": "paragraph", "text": "smw-setupcheck-stack-trace-abort-condition"}, {"type": "errorbox", "text": "TRACE_STRING"}]}, "ERROR_EXTENSION_INVALID_ACCESS": {"indicator_title": "smw-setupcheck-registry-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "smw-setupcheck-registry-invalid-wfloadextension-use"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-registry-requires-enablesemantics"}, {"type": "paragraph", "text": "smw-setupcheck-enablesemantics-assistance"}]}, "ERROR_CONFIG_PROFILE_UNKNOWN": {"indicator_title": "smw-setupcheck-error", "indicator_color": "#dd3d31", "output_form": [{"type": "paragraph", "text": "ERROR_TEXT"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-how-to-fix-error"}, {"type": "paragraph", "text": "smw-setupcheck-config-profile"}]}, "MAINTENANCE_MODE": {"indicator_title": "smw-setupcheck-maintenance", "indicator_color": "#ffc107", "output_form": [{"type": "paragraph", "text": "smw-setupcheck-in-maintenance-mode"}, {"type": "version", "text": "smw-setupcheck-release"}, {"type": "section", "text": "smw-setupcheck-why-this-page"}, {"type": "paragraph", "text": "smw-setupcheck-reason-for-in-maintenance"}], "progress": [{"type": "section", "text": "smw-setupcheck-progress"}, {"type": "paragraph", "progress_keys": {"create-tables": "smw-setupcheck-progress-key-create-tables", "post-creation": "smw-setupcheck-progress-key-post-creation", "table-optimization": "smw-setupcheck-progress-key-table-optimization", "supplement-jobs": "smw-setupcheck-progress-key-supplement-jobs"}, "text": "smw-setupcheck-estimation-of-completion"}, {"type": "paragraph", "text": "smw-setupcheck-administrator-contact-for-information"}]}}}