{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "HitomiAkane", "Jaideraf", "Kghbln", "Meno25", "<PERSON><PERSON>", "Ramsis II", "아라"]}, "smw-desc": "جعل الويكى الخاص بك أكثر قابليه للوصول - للآلات ''و'' البشر ([https://www.semantic-mediawiki.org/wiki/Help:User_manual توثيق على الإنترنت])", "smw_viewasrdf": "تلقيم RDF", "smw_finallistconjunct": "، و", "smw_isspecprop": "هذه الخاصيه هى خاصيه خاصه فى هذا الويكى.", "smw_concept_description": "وصف المبدأ \"$1\"", "smw_no_concept_namespace": "المبادئ يمكن تعريفها فقط فى الصفحات فى نطاق Concept:", "smw_multiple_concepts": "كل صفحه مبدأ يمكن أن تحتوى على تعريف مبدأ واحد.", "smw_concept_cache_miss": "المبدأ \"$1\" لا يمكن استخدامه حاليا، بما أن ضبط الويكى يحتاجه إلى أن يتم حسابه خارجيا. لو أن المشكله لم تنته بعد بعض الوقت، سل إدارى موقعك ليجعل هذا المبدأ متوفرا.", "smw_noinvannot": "القيم لا يمكن تعيينها لخصائص معكوسه.", "smw_baduri": "URIs من النوع \"$1\" غير مسموح بها.", "smw_csv_link": "سى فى إس", "smw_printername_count": "ع<PERSON> النتا<PERSON>ج", "smw_printername_csv": "تصدير CSV", "smw_printername_debug": "استعلام التصليح (للخبراء)", "smw_printername_embedded": "تضمين محتويات الصفحة", "smw_printername_json": "تصدير JSON", "smw_printername_list": "قائمة", "smw_printername_ol": "ترقيم", "smw_printername_ul": "ترتيب فى عناصر", "smw_printername_table": "جدول", "smw_printername_broadtable": "جدول عريض", "smw_printername_template": "قالب", "smw-paramdesc-limit": "الرقم الأقصى للعناصر المُرجعة", "smw-paramdesc-headers": "اعرض العناوين/أسماء الخصائص", "smw-paramdesc-mainlabel": "العلامه للإعطاء لاسم الصفحه الرئيسية", "smw-paramdesc-link": "أظهر القيم كوصلات", "smw-paramdesc-intro": "النص للعرض قبل نتائج الاستعلام، لو كانت هناك أي", "smw-paramdesc-outro": "النص المطلوب عرضه بعد نتائج الاستعلام، إن وُجدت", "smw-paramdesc-default": "النص المطلوب عرضه إذا لم تتوفر نتائج للاستعلام", "smw-paramdesc-sep": "فاصل القيم", "smw-paramdesc-template": "اسم القالب لعرض الخرج به", "smw-paramdesc-columns": "رقم الأعمده لعرض النتائج بها", "smw-paramdesc-embedformat": "وسم HTML المستخدم لتعريف الترويسة", "smw-paramdesc-embedonly": "لا تظهر الترويسات", "smw-paramdesc-searchlabel": "نص وصله النتائج", "smw_iq_disabled": "استعلامات السيمانتيك تم تعطيلها فى هذا الويكى.", "smw_iq_moreresults": "… مزيد من النتائج", "smw_parseerror": "القيمه المعطاه لم يتم فهمها.", "smw_decseparator": ".", "smw_kiloseparator": "،", "smw_notitle": "\"$1\" لا يمكن أن تستخدم مثل هذا الاسم فى صفحه ويكى.", "smw_wrong_namespace": "فقط الصفحات فى النطاق \"$1\" مسموح بها هنا.", "smw_manytypes": "أكثر من نوع واحد لتعريف الخاصيه.", "smw_emptystring": "السلاسل الفارغه غير مقبوله.", "smw_notinenum": "\"$1\" ليس ضمن قائمه [[Property:Allows value|القيم الممكنه]] ($2) لهذه الخاصيه \"$3\".", "smw_noboolean": "\"$1\" غير متعرف عليها كقيمه بووليان (صواب/خطأ).", "smw_true_words": "صحيح،ص،نعم،ن", "smw_false_words": "خطأ،خ،لا،ل", "smw_nofloat": "ليس عددا \"$1\".", "smw_infinite": "الأرقام الكبيره مثل \"$1\" غير مدعومه.", "smw_nodatetime": "التاريخ \"$1\" لم يتم فهمه.", "smw_toomanyclosing": "يبدو أنه هناك الكثير من \"$1\" فى الاستعلام.", "smw_noclosingbrackets": "\"]]\" فى استعلامك لم تكن مغلقه باستخدام \"<nowiki>[[</nowiki>\" بعض استخدام", "smw_misplacedsymbol": "الرمز \"$1\" تم استخدامه فى مكان حيث هو ليس مفيدا.", "smw_unexpectedpart": "الجزء \"$1\" من الاستعلام لم يفهم.\nالنتائج قد لا تكون كما هو متوقع.", "smw_emptysubquery": "بعض الاستعلامات الفرعيه ليس لها شرط صحيح.", "smw_misplacedsubquery": "بعض الاستعلامات الفرعيه تم استخدمها فى مكان غير مسموح فيه بالاستعلامات الفرعيه.", "smw_valuesubquery": "الاستعلامات الفرعيه غير مدعومه لقيم الخاصيه \"$1\".", "smw_badqueryatom": "جزء ما \"<nowiki>[[…]]</nowiki>\" من الاستعلام لم يتم فهمه.", "smw_propvalueproblem": "قيمه الخاصيه \"$1\" لم يتم فهمها.", "smw_noqueryfeature": "ميزه استعلام ما لم يتم دعمها فى هذا الويكى وجزء من الاستعلام تم إسقاطه ($1).", "smw_noconjunctions": "الوقوفات فى الاستعلامات غير مدعومه فى هذا الويكى وجزء من الاستعلام تم إسقاطه ($1).", "smw_nodisjunctions": "المفارق فى استعلامات ليست مدعومه فى هذا الويكى وجزء من الاستعلام رفض $1.", "smw_querytoolarge": "{{PLURAL:$2|شروط الاستفسار}} التاليه لم يمكن اعتبارها نتيجه لقيود الويكى فى حجم أو عمق الاستعلام: $1.", "smw_notemplategiven": "وفر قيمه للمحدد \"template\" لتعمل صيغه الاستعلام هذه.", "smw_type_header": "خصائص النوع \"$1\"", "smw_typearticlecount": "عرض {{PLURAL:$1||خاصيه واحده تستخدم|خاصيتين تستخدمان|$1 خصائص تستخدم|$1 خاصيه تستخدم}} هذا النوع.", "smw_attribute_header": "الصفحات التى تستخدم الخاصيه \"$1\"", "smw_attributearticlecount": "عرض {{PLURAL:$1||الصفحه التى تستخدم|الصفحتين اللتين تستخدمان|الصفحات التى تستخدم}} هذه الخاصيه.", "specialpages-group-smw_group": "سيمانتيك ميدياويكى", "exportrdf": "آر دی‌ إف إلى صفحات تصدير", "smw_exportrdf_docu": " هذه الصفحه تتيح لك الحصول على بيانات من صفحه فى شكل آر دی‌ إف.\nالتصدير إلى صفحات، أدخل العناوين فى مربع النص أدناه، عنوان واحد لكل سطر.", "smw_exportrdf_recursive": "تصدير جميع الصفحات ذات الصله بشكل تكرارى.\nعلما أنه يمكن أن تكون النتيجه كبيرة!", "smw_exportrdf_backlinks": "أيضا تصدير كل الصفحات التى تشير إلى الصفحات تم تصديرها.\nيولد آر دى إف قابل للتصفح.", "smw_exportrdf_lastdate": "لا تصدر الصفحات التى لم تتغير منذ نقطه زمنيه محدده.", "smw_exportrdf_submit": "تصدير", "uriresolver": "<PERSON><PERSON><PERSON><PERSON> URI", "properties": "الخصائص", "smw-categories": "تصانيف", "smw_properties_docu": "الخصائص التاليه تستخدم فى الويكى.", "smw_property_template": "$1 من نوع $2 ({{PLURAL:$3|مرة|مرتين|$3 مرات}})", "smw_propertylackspage": "جميع الخصائص ينبغى أن توصف بصفحة!", "smw_propertylackstype": "لا نوع تم تحديده لهذه الخاصيه (افتراض النوع $1 حاليا).", "smw_propertyhardlyused": "هذه الخاصيه لا تكاد تستخدم داخل الويكي!", "unusedproperties": "خصائص غير مستخدمة", "smw-unusedproperties-docu": "تسرد هذه الصفحة [https://www.semantic-mediawiki.org/wiki/Unused_properties الخصائص غير المستخدمة] التي تم تعريفها على الرغم من عدم استفادة صفحات أخرى منها، للحصول على عرض متباين; انظر الصفحات الخاصة [[Special:Properties|كل الخصائص]] أو [[Special:WantedProperties|خصائص مطلوبة]].", "smw-unusedproperty-template": "$1 من نوع $2", "wantedproperties": "خصائص مطلوبة", "smw-wantedproperties-docu": "تسرد هذه الصفحة [https://www.semantic-mediawiki.org/wiki/Wanted_properties خصائص مطلوبة] مستخدمة في الويكي ولكن لا توجد صفحات تصفها، للحصول على عرض متباين; انظر الصفحات الخاصة [[Special:Properties|كل الخصائص]] أو [[Special:UnusedProperties|خصائص غير مستخدمة]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|استخدام|استخدام}})", "smw_purge": "تحديث", "types": "أنواع", "smw_types_docu": "التالى قائمه بجميع أنواع البيانات التى يمكن أن تسند إلى الخصائص.\nكل نوع بيانات له صفحه حيث يمكن توفير معلومات إضافيه.", "smw_uri_doc": "القرار URI بتطبق [$1 W3C TAG العثور على httpRange-14]. بيقفل إنه بيتم تسليم تمثيل RDF (للوحات) أو صفحة ويكي (للانسان) حسب الطلب.", "ask": "بحث سيمانتيك", "smw_ask_sortby": "الترتيب حس<PERSON> العمود (اختياري)", "smw_ask_ascorder": "تصاعدي", "smw_ask_descorder": "تنازلي", "smw_ask_submit": "جِد نتائجًا", "smw_ask_editquery": "عدل الاستعلام", "smw_add_sortcondition": "[أضف شرط ترتيب]", "smw_ask_hidequery": "إخفاء الاستعلام", "smw_ask_help": "مساعده الاستعلام", "smw_ask_queryhead": "استعلام", "smw_ask_printhead": "بيانات إضافيه للعرض", "smw_ask_printdesc": "(أضف اسمًا مناسبًا واحدًا فى كل سطر)", "smw_ask_format_as": "صياغه ك:", "smw_ask_defaultformat": "افتراضي", "smw_ask_otheroptions": "خيا<PERSON><PERSON><PERSON> أخرى", "smw_ask_show_embed": "عرض الكود المضمن", "smw_ask_hide_embed": "إخفاء الكود المضمن", "smw_ask_embed_instr": "لتضمين هذا الاستعلام فى صفحه ويكى، استخدم الكود أدناه", "searchbyproperty": "البحث حسب الخصائص", "smw_sbv_docu": "البحث عن كل الصفحات التى لها خصائص معينه وقيمة", "smw_sbv_novalue": "أدخل قيمه صحيحه للخاصيه، أو انظر كل قيم الخصائص ل\"$1\"", "smw_sbv_displayresultfuzzy": "قائمه بكل الصفحات التى لديها الخاصيه \"$1\" بالقيمه \"$2\".\nبما أنه كانت هناك فقط عده نتائج، فالقيم المقاربه معروضه أيضا.", "smw_sbv_property": "خاصية:", "smw_sbv_value": "قيمة:", "smw_sbv_submit": "جِد نتائجًا", "browse": "استعرض ويكى", "smw_browselink": "خصائص التصفح", "smw_browse_article": "أدخل اسم الصفحه لبدء التصفح منها.", "smw_browse_go": "اذهب", "smw_browse_show_incoming": "أظهر الخواص التى تصل هنا", "smw_browse_hide_incoming": "أخفِ الخواص التى تصل هنا", "smw_browse_no_outgoing": "هذه الصفحه ليس لديها خصائص.", "smw_browse_no_incoming": "لا خصائص تصل إلى هذه الصفحه.", "smw_inverse_label_default": "$1 من", "smw_inverse_label_property": "اعكس علامه الخاصية", "pageproperty": "بحث خصائص الصفحة", "smw_pp_docu": "البحث عن جميع قيم خاصيه على صفحه معينه.\nأدخل صفحه وخاصيه.", "smw_pp_from": "من صفحة", "smw_pp_type": "الخاصية", "smw_pp_submit": "جِد نتائجًا", "smw_result_prev": "السابق", "smw_result_next": "التالي", "smw_result_results": "النتائج", "smw_result_noresults": "لا توجد نتائج.", "smwadmin": "وظائف إداريه لسيمانتيك ميدياويكي", "smw-admin-setupsuccess": "تم تنصيب محرك التخزين بنجاح", "smw_smwadmin_return": "أرجع إلى $1", "smw_smwadmin_updatestarted": "عمليه تحديث جديده لتحديث بيانات سيمانتيك بدأت.\nكل البيانات المخزنه ستتم إعاده بنائها أو إصلاحها عند الحاجه.\nأنت يمكنك متابعه تطور التحديث على هذه الصفحه الخاصه.", "smw_smwadmin_updatenotstarted": "يوجد بالفعل عمليه تحديث جاريه.\nلا تنشئ واحده أخرى.", "smw_smwadmin_updatestopped": "كل عمليات التحديث الموجوده تم إيقافها", "smw_smwadmin_updatenotstopped": "لوقف عمليه التحديث الجاريه، يجب عليك تفعيل الصندوق لتعبر عن أنك متأكد حقا.", "smw-admin-docu": "هذه الصفحه الخاصه تساعدك خلال تنصيب وترقيه <a href=\"https://www.semantic-mediawiki.org\">سيمانتيك ميدياويكي</a>.\nتذكر أن تخزن احتياطيا البيانات القيمه قبل تنفيذ وظائف إداريه.", "smw-admin-db": "تثبيت و تحديث قاعده البيانات", "smw-admin-dbdocu": "سيمانتيك ميدياويكى يتطلب بعض الامتدادات لقاعده بيانات ميدياويكى حتى يخزن البيانات السيمانتيك.\nالوظيفه التاليه تؤكد أن قاعده بياناتك منصبه بشكل صحيح.\nالتغييرات المعموله فى هذه الخطوه لا تؤثر على بقيه قاعده بيانات ميدياويكى، ويمكن استرجاعها بسهوله فى حاله الرغبه فى ذلك.\nوظيفه التنصيب هذه يمكن تنفيذها عده مرات بدون عمل أى ضرر، لكنها مطلوبه مره واحده عند التنصيب أو الترقيه.", "smw-admin-permissionswarn": "لو أن العمليه فشلت مع أخطاء SQL، فقاعده البيانات التى طبقها المستخدم بواسطه الويكى الخاص بك (تحقق من LocalSettings.php الخاص بك) على الأرجح لا تمتلك سماحات كافيه.\nإما أن تمنح هذا المستخدم سماحات إضافيه لإنشاء وحذف الجداول، مؤقتا أدخل تسجيل الدخول للroot الخاص بقاعده بياناتك فى LocalSettings.php، أو استخدم سكريبت الصيانه <code>setupStore.php</code> الذى يمكنه استخدام تأكيدات AdminSettings.php.", "smw-admin-dbbutton": "جداول البدء أو الترقية", "smw-admin-announce": "أعلن عن الويكى الخاص بك", "smw_smwadmin_datarefresh": "إصلاح البيانات وتحديثها", "smw_smwadmin_datarefreshdocu": "من الممكن استرجاع كل بيانات سيمانتيك ميدياويكى بناء على المحتويات الحاليه للويكى.\nهذا يمكن أن يكون مفيدا لإصلاح البيانات المكسوره أو لتحديث البيانات لو أن الصيغه الداخليه تغيرت بسبب ترقيه برنامج.\nالتحديث يتم تنفيذه صفحه بصفحه ولن يتم إكماله حالا.\nالتالى يعرض ما إذا كان التحديث يجرى ويسمح لك ببدء أو إنهاء التحديثات (إلا لو كانت هذه الخاصيه تم تعطيلها بواسطه إدارى الموقع).", "smw_smwadmin_datarefreshprogress": "<strong>تحديث يجرى بالفعل.</strong>\nمن الطبيعى أن تتقدم عمليات التحديث ببطء فقط بما أنها تحدث البيانات فقط فى كميات صغيره كل مره مستخدم ما يصل إلى الويكى.\nللانتهاء من هذا التحديث سريعا، يمكنك تشغيل سكريبت صيانه ميدياويكى <code>runJobs.php</code> (استخدم الخيار <code>--maxjobs 1000</code> لتحديد عدد التحديثات المعموله كل مرة).\nالتقدم المقدر للتحديث الحالي:", "smw_smwadmin_datarefreshbutton": "أبدا تحديث البيانات", "smw_smwadmin_datarefreshstop": "أوقف هذا التحديث", "smw_smwadmin_datarefreshstopconfirm": "نعم، أنا {{GENDER:$1|متأكد}}", "smw-admin-support": "أحصل على دعم", "smw-admin-supportdocu": ":مصادر متعدده يمكن أن تساعده فى حاله مشاكل", "smw-admin-installfile": "لو أنك تواجه مشاكل مع تنصيبك، ابدأ بالتحقق من الإرشادات فى <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">ملف INSTALL</a>.", "smw-admin-smwhomepage": "وثائق المستخدم الكامله لسيمانتيك ميدياويكى موجوده فى <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "العلل يمكن إبلاغها إلى <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues/\">ميديازيللا</a>.", "smw-admin-questions": "لو لديك أسئله أو اقتراحات أخرى، انضم إلى النقاش فى <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">منتدى مستخدمى سيمانتيك ميدياويكي</a>.", "smw_adminlinks_datastructure": "هيكل البيانات", "smw_adminlinks_displayingdata": "عرض البيانات", "smw_adminlinks_inlinequerieshelp": "مساعده الاستعلامات الداخلية", "smw-createproperty-isproperty": "هذه خاصية من نوع $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1||القيمة المسموح بها لهذه الخاصية هي|القيمتان المسموح بهما لهذه الخاصية هما|القيم المسموح بها لهذه الخاصية هي}}:", "smw_unknowntype": "نوع غير مدعوم \"$1\" لتعريف الممتلكات.", "smw_concept_header": "صفحات المبدأ \"$1\"", "smw_conceptarticlecount": "عرض {{PLURAL:$1||صفحه واحده تنتمي|صفحتين تنتميان|$1 صفحات تنتمي|$1 صفحه تنتمي}} إلى هذا المبدأ.", "smw-livepreview-loading": "تحميل…", "smw-schema-tag": "{{PLURAL:$1|وسم|وسوم}}", "smw-listingcontinuesabbrev": "متابعه", "smw-showingresults": "الصفحه دى بتعرض {{PLURAL:$1|<strong>1</strong> نتيجه}} من اول رقم <strong>$2</strong>."}