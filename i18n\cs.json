{"@metadata": {"authors": ["Aktron", "<PERSON>", "DemonioCZ", "Dvorapa", "Ilimanaq29", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Kghbln", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marek <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Mormegil", "Patriccck", "<PERSON><PERSON>", "Vks", "XenoPheX", "아라"]}, "smw-desc": "Činíme vaší wiki přístupnější – pro stroje ''i'' lidi ([https://www.semantic-mediawiki.org/wiki/Help:User_manual online documentation])", "smw-error": "Chyba", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Sémantická MediaWiki] byla instalována a aktivována, ale schází v ní potřebný [https://www.semantic-mediawiki.org/wiki/Help:Upgrade licenční klíč pro upgrade].", "smw-upgrade-release": "Vydání", "smw-upgrade-progress": "Postup", "smw-upgrade-progress-explain": "Obraťte se na místního správce, který vám sdělí více informací o postupu.", "smw-upgrade-progress-create-tables": "Vytv<PERSON><PERSON><PERSON><PERSON> (nebo aktualizace) tabulek a indexů...", "smw-upgrade-progress-post-creation": "Prováděj<PERSON> se <PERSON>hy po vytvoření...", "smw-upgrade-progress-table-optimization": "Provádí se optimal<PERSON>ce tabulek...", "smw-upgrade-error-title": "Chyba » Sémantická MediaWiki", "smw-upgrade-error-why-title": "Proč vidím tuto stránku?", "smw-upgrade-error-why-explain": "Interní struktura databáze Sémantické MediaWiki se změnila a vyžaduje určité úpravy, aby byla plně funkční. K tomu mohlo dojít z různých př<PERSON>, včetně:\n* Byly přid<PERSON> da<PERSON> (vyžadující dodatečné nastavení tabulek) \n* Upgrade obsahuje některé změny tabulek nebo indexů, které vyžadují zásah před přístupem k datům\n* Změny úložiště nebo dotazovacího stroje", "smw-upgrade-error-how-title": "Jak tuto chybu opravím?", "smw-upgrade-error-how-explain-admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (nebo kdokoli s právy správce) musí spustit skript pro údržbu jádra MediaWiki [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php], nebo skript pro údržbu Sémantické MediaWiki [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php].", "smw-upgrade-error-how-explain-links": "<PERSON><PERSON> a pomoc zís<PERSON> také na následují<PERSON> (anglických) stránkách:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation Pokyny pro instalaci]\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting Řešení problémů]", "smw-extensionload-error-why-title": "Proč vidím tuto stránku?", "smw-extensionload-error-why-explain": "Rozšíření <b>nebylo</b> načteno pomoc<PERSON> <code>enableSemantics</code> a místo toho bylo zapnuto jin<PERSON><PERSON>, jako je p<PERSON><PERSON><PERSON> vol<PERSON> <code>wfLoadExtension( 'SemanticMediaWiki' )</code>.", "smw-extensionload-error-how-title": "Jak tuto chybu opravím?", "smw-extensionload-error-how-explain": "Pro aktivaci rozšíření a vyloučení problémů při deklaracích jmenných prostorů a nevyřízených konfigurací je nutné použít funkci <code>enableSemantics</code>, k<PERSON><PERSON>, aby potřebné proměnné byly nastaveny před načtením rozšíření prostřednictvím modulu <code>ExtensionRegistry</code>. \n\nDalší informace najdete na stránce nápovědy [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] .", "smw-upgrade-maintenance-title": "Údržba » Sémantická MediaWiki", "smw-upgrade-maintenance-why-title": "Proč vidím tuto stránku?", "smw-upgrade-maintenance-note": "V systému právě probíhá [https://www.semantic-mediawiki.org/wiki/Help:Upgrade upgrade] rozšíření [https://www.semantic-mediawiki.org/ Sémantická MediaWiki] společně s jejím datovým úložištěm. Vyčkejte, než bude údržba dokončena, poté se wiki znovu zpřístupní.", "smw-upgrade-maintenance-explain": "Rozšíření se pokouší minimalizovat dopad na dostupnost a zkrátit dobu výpadku odložením většiny úloh údržby na dobu po spuštění <code>update.php</code>, ale některé změny související s databází musí být dokončeny jako první, aby se předešlo nekonzistenci dat. Mezi tyto změny patří: \n* <PERSON>m<PERSON>na struktury tabulek, např. přidání nových nebo změna stávajících sloupců  \n* Změna nebo přidání indexů tabulek \n* Spuštění optimalizace tabulek (je-li povolena)", "smw-semantics-not-enabled": "Funkce Sémantické MediaWiki nejsou na této wiki aktivovány.", "smw_viewasrdf": "RDF kanál", "smw_finallistconjunct": " a", "smw-factbox-head": "...více o \"$1\"", "smw-factbox-facts": "<PERSON><PERSON><PERSON>", "smw-factbox-facts-help": "Zobrazuje výroky a fakta vytvořené uživatelem", "smw-factbox-attachments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-factbox-attachments-value-unknown": "N/A", "smw-factbox-attachments-is-local": "Je místní", "smw-factbox-attachments-help": "Zobrazit dostupné přílohy", "smw-factbox-facts-derived": "<PERSON>d<PERSON><PERSON><PERSON> fakty", "smw-factbox-facts-derived-help": "Zobrazuje fakta odvozená z pravidel nebo získaná pomocí jiných způsobů uvažování", "smw_isspecprop": "Tato vlastnost je speciální vlastnost na této wiki.", "smw-concept-cache-header": "Využití cache", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count <PERSON><PERSON> konce<PERSON>] obsahuje {{PLURAL:$1|'''jednu''' polož<PERSON>|'''$1''' polo<PERSON><PERSON>}} ($2).", "smw-concept-no-cache": "<PERSON><PERSON>í <PERSON> žádná cache.", "smw_concept_description": "Popis pojmu „$1“", "smw_no_concept_namespace": "Pojmy je možné definovat pouze na stránkách ve jmenném prostoru Concept:", "smw_multiple_concepts": "<PERSON><PERSON><PERSON>á stránka konceptu může mít jen jednu definici.", "smw_concept_cache_miss": "Koncept „$1“ není mož<PERSON> p<PERSON>, protože konfigurace wiki vyžaduje, aby se vypočítal až dodatečně. Pokud problém přetrvává delší dobu, pož<PERSON><PERSON><PERSON><PERSON> sprá<PERSON>, aby tento koncept zpřístupnil.", "smw_noinvannot": "Inverzním vlastnostem nelze přiřazovat hodnoty.", "version-semantic": "Sémantická rozšíření", "smw_baduri": "<PERSON><PERSON><PERSON><PERSON>, URI z rozsahu „$1“ na tomto místě nejsou dostupné.", "smw_printername_count": "Spočítat výsledky", "smw_printername_csv": "Export do CSV", "smw_printername_dsv": "Export do DSV", "smw_printername_debug": "<PERSON><PERSON> (pro experty)", "smw_printername_embedded": "Vložit obsah s<PERSON>", "smw_printername_json": "Export do JSON", "smw_printername_list": "Seznam", "smw_printername_plainlist": "Prostý <PERSON>nam", "smw_printername_ol": "Číslovaný seznam", "smw_printername_ul": "Odrážkový seznam", "smw_printername_table": "Tabulka", "smw_printername_broadtable": "Široká tabulka", "smw_printername_template": "Šablona", "smw_printername_templatefile": "<PERSON><PERSON><PERSON>", "smw_printername_rdf": "Export do RDF", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "text", "smw-paramdesc-limit": "Maximální počet vrácených výsledků", "smw-paramdesc-offset": "Posunutí prvního výsledku", "smw-paramdesc-headers": "Zobrazení záhlaví/názvů vlastností", "smw-paramdesc-mainlabel": "Popisek přidělený názvu hlavní stránky", "smw-paramdesc-link": "Zobrazit hodnoty jako od<PERSON>zy", "smw-paramdesc-intro": "Text, který se má zobrazit před výsledky dotazu, pokud n<PERSON><PERSON><PERSON> jsou", "smw-paramdesc-outro": "Text, který se má zobrazit po vý<PERSON><PERSON><PERSON><PERSON><PERSON>, pokud n<PERSON><PERSON><PERSON> jsou", "smw-paramdesc-default": "Text, který se má zobrazit, pokud dotaz nevrá<PERSON>í ž<PERSON><PERSON> v<PERSON>ky", "smw-paramdesc-sep": "Oddělovač mezi výsledky", "smw-paramdesc-propsep": "Oddělovač mezi vlastnostmi položky výsledku", "smw-paramdesc-valuesep": "Oddělovač mezi hodnotami vlastnosti výsledku", "smw-paramdesc-showsep": "Zobrazit na začátku CSV souboru oddělovač (\"sep=<hodnota>\")", "smw-paramdesc-distribution": "Místo zobrazení všech hodnot spočítat kolikrát se která vyskytuje a zobrazit tyto počty.", "smw-paramdesc-distributionsort": "Řadí distribuci hodnot podle počtu výskytů.", "smw-paramdesc-distributionlimit": "Omezí distribuci hodnot na počet pouze některých vlastností.", "smw-paramdesc-aggregation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k čemu  se agregace vztahuje", "smw-paramdesc-template": "<PERSON><PERSON><PERSON><PERSON>, pomocí které zobrazit výpisy", "smw-paramdesc-columns": "<PERSON><PERSON><PERSON> s<PERSON>, ve kterých se zobrazí výsledky", "smw-paramdesc-userparam": "Hodnota předávaná kaž<PERSON><PERSON><PERSON>, je-li u<PERSON><PERSON>", "smw-paramdesc-class": "Přídavná třída CSS pro seznam", "smw-paramdesc-introtemplate": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> se má zobrazit před výsledkem dotazu, je-li n<PERSON>", "smw-paramdesc-outrotemplate": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> se má zobrazit po výsledku dotazu, je-li n<PERSON>", "smw-paramdesc-embedformat": "HTML tag, k<PERSON><PERSON><PERSON> jsou definovány nadpisy", "smw-paramdesc-embedonly": "Nezobrazovat nadpisy", "smw-paramdesc-table-class": "Další CSS třída, kter<PERSON> bude nastavena tabulce", "smw-paramdesc-csv-sep": "<PERSON><PERSON><PERSON><PERSON><PERSON> slou<PERSON>", "smw-paramdesc-csv-valuesep": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnot", "smw-paramdesc-dsv-separator": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> má být použit", "smw-paramdesc-dsv-filename": "Název DSV souboru", "smw-paramdesc-filename": "Název výstupního souboru", "smw-smwdoc-description": "Zobrazí tabulku v<PERSON><PERSON>, kter<PERSON> lze pro daný form<PERSON> výsledk<PERSON> p<PERSON>, spolu s výchozími hodnotami a popisy.", "smw-smwdoc-par-format": "<PERSON><PERSON><PERSON>, pro který se zobrazí dokumentace.", "smw-paramdesc-sort": "V<PERSON><PERSON>t, podle které výsledky řadit", "smw-paramdesc-order": "Způsob řazení výsledků dotazu", "smw-paramdesc-named_args": "Pojmenujte argumenty předané <PERSON>ě", "smw-paramdesc-template-arguments": "<PERSON><PERSON><PERSON><PERSON>, jak jsou pojmenované argumenty předány <PERSON>", "smw-paramdesc-import-annotation": "Další an<PERSON> data je třeba zkopírovat během syntaktické analýzy předmětu", "smw-paramdesc-export": "Možnosti exportu", "smw-paramdesc-json-type": "Typ serializace", "smw-paramdesc-source": "Alternativní zdroj dotazu", "smw-paramdesc-jsonsyntax": "Syntaxe JSON, kter<PERSON> bude p<PERSON>žita", "smw-printername-feed": "Kanál RSS a Atom", "smw-paramdesc-feedtype": "<PERSON><PERSON> ka<PERSON>", "smw-paramdesc-feedtitle": "Text, k<PERSON><PERSON> bude užit jako titulek kanálu", "smw-paramdesc-feeddescription": "Text, k<PERSON><PERSON> bude užit jako popis kanálu", "smw-paramdesc-feedpagecontent": "<PERSON><PERSON><PERSON>, který bude zobrazen v kanálu", "smw-label-feed-description": "$2 kanál $1", "smw_iq_disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> byly pro tuto wiki zakázány.", "smw_iq_moreresults": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_parseerror": "Zadaná hodnota nebyla pochopená.", "smw_notitle": "„$1“ není mož<PERSON>é p<PERSON>žít na této wiki jako název stránky.", "smw_noproperty": "„$1“ nelze na této wiki použít jako název vlastnosti.", "smw_wrong_namespace": "Zde jsou povoleny jen stránky ze jmenného prostoru \"$1\"", "smw_manytypes": "Pro vlastnost byl definován více než jeden typ.", "smw_emptystring": "Prázdné řetězce nejsou povolené.", "smw_notinenum": "\"$1\" není v seznamu ($2) [[Property:Allows value|povolených hodnot]] pro vlastnost \"$3\".", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" není v seznamu ($2) speciální vlastnosti [[Property:Allows value|povolených hodnot]] pro vlastnost \"$3\".", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" není v rozsahu s názvem \"$2\" určeném pomocí omezení speciální vlastnosti [[Property:Allows value|povoluje hodnotu]] pro vlastnost \"$3\".", "smw-constraint-error-limit": "Seznam bude obsahovat maximálně $1 porušení.", "smw_noboolean": "„$1“ nebylo rozpoznáno jako platná hodnota typu boolean (ano/ne).", "smw_true_words": "ano,a,yes,y", "smw_false_words": "ne,no,n", "smw_nofloat": "„$1“ není <PERSON>.", "smw_infinite": "Tak dlouhá čísla jako $1 nejsou podporována.", "smw_unitnotallowed": "\"$1\" není platnou mě<PERSON>u jed<PERSON>u této vlast<PERSON>.", "smw_nounitsdeclared": "Pro tuto vlastnost nebyly defino<PERSON>y ž<PERSON>dn<PERSON> m<PERSON>.", "smw_novalues": "<PERSON><PERSON><PERSON><PERSON> hodnoty ne<PERSON>ly z<PERSON>.", "smw_nodatetime": "Datum \"$1\" nedává smysl.", "smw_toomanyclosing": "Dotazovaný řetězec „$1“ má příliš mnoho výskytů.", "smw_noclosingbrackets": "Některý výskyt „<nowiki>[[</nowiki>“ ve vašem dotazu nebyl ukončen odpovídajícím „]]“.", "smw_misplacedsymbol": "Symbol „$1“ byl užitý na místě, kde nemá význam.", "smw_unexpectedpart": "Část dotazu „$1“ nedává smysl.\nVýsledky pravděpodobně nesplní očekávání.", "smw_emptysubquery": "Některý poddotaz nemá platné <PERSON>.", "smw_misplacedsubquery": "Některý poddotaz byl použitý na místě, kde nejsou poddotazy povoleny.", "smw_valuesubquery": "Poddotazy nejsou podporovány pro hodnoty vlastnosti „$1“.", "smw_badqueryatom": "Někter<PERSON> „<nowiki>[[…]]</nowiki>“ nebyla s<PERSON>zumitelná.", "smw_propvalueproblem": "Hodnota vlastnosti „$1“ nedávala smysl.", "smw_noqueryfeature": "Určitá část dotazu není na této wiki podporovaná a bude proto ignorována ($1).", "smw_noconjunctions": "Konjunkce v dotazu není na této wiki podporována a bude proto ignorována ($1).", "smw_nodisjunctions": "Disjunkce nejsou v dotazech na této wiki podporované a část dotazu byla ignorována ($1).", "smw_querytoolarge": "{{PLURAL:$2|Následující podmínka|$2 následující podmínky}} dotazu nebudou zohledněné z důvodu omezení této wiki na délku nebo hĺoubku dotazu: <code>$1</code>.", "smw_notemplategiven": "Aby tento form<PERSON>t dotazu fungo<PERSON>, uveďte hodnotu parametru „template“.", "smw_db_sparqlqueryproblem": "Výsledek dotazu se nepodařilo získat z databáze SPARQL. Tato chyba může být dočasná nebo může indikovat chybu v softwaru databáze.", "smw_db_sparqlqueryincomplete": "Zodpovězení dotazu se ukázalo být př<PERSON><PERSON>š obtížným a bylo stornováno. Výsledky nemusí být úplné. Je-li to mo<PERSON><PERSON><PERSON>, zkuste použít jed<PERSON>du<PERSON><PERSON> dotaz.", "smw_type_header": "Vlastností typu „$1“", "smw_typearticlecount": "Zobrazeno je $1 {{PLURAL:$1|vlastnost|vlastnosti|vlastností}} tohoto typu.", "smw_attribute_header": "Stránek používajících vlastnost „$1“", "smw_attributearticlecount": "Zobrazeno je $1 {{PLURAL:$1|stránka používající|stránky používající|stránek používajících}} tuto vlastnost.", "smw-propertylist-redirect-header": "Synonyma", "smw-propertylist-error-header": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>ahující nesprávná přiřazení", "smw-propertylist-count": "Zobrazuji $1 související {{PLURAL:$1|entitu|entity}}.", "specialpages-group-smw_group": "Sémantická MediaWiki", "specialpages-group-smw_group-maintenance": "Údržba", "specialpages-group-smw_group-properties-concepts-types": "<PERSON><PERSON><PERSON><PERSON>, koncepty a typy", "specialpages-group-smw_group-search": "Procházet a hledat", "exportrdf": "Export stránek do RDF", "smw_exportrdf_docu": "Tato stránka vám umožňuje exportovat části stránek do formátu RDF. Po zadaní názvů stránek do spodního textového pole (jeden název na řádek) můžete exportovat stránky.", "smw_exportrdf_recursive": "Rekurzívně exportovat všechny související stránky. <PERSON><PERSON>, výsledek může být velmi roz<PERSON>áhlý!", "smw_exportrdf_backlinks": "Exportovat tak<PERSON> s<PERSON>, k<PERSON><PERSON> od<PERSON>zuj<PERSON> na exportované stránky. Vytvoří přehledné RDF.", "smw_exportrdf_lastdate": "Neexport<PERSON><PERSON>, <PERSON><PERSON><PERSON>něné od zadaného č<PERSON>u.", "smw_exportrdf_submit": "Exportovat", "uriresolver": "Překladač URI", "properties": "V<PERSON><PERSON>ti", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "Na této wiki se používají následující vlastnosti.", "smw_property_template": "$1 typu $2 ($3 {{PLURAL:$3|použit<PERSON>}})", "smw_propertylackspage": "Všechny vlastnosti by m<PERSON><PERSON> m<PERSON><PERSON> str<PERSON> s popisem!", "smw_propertylackstype": "Této vlastnosti nebyl definován <PERSON> typ (předpokládá se prozatím typ $1)", "smw_propertyhardlyused": "Tato vlastnost se na wiki téměř nepoužívá!", "smw-property-name-invalid": "Vlastnost $1 nelze použít (neplatný název vlastnosti).", "smw-sp-property-searchform": "Zobrazit vlast<PERSON>, <PERSON><PERSON><PERSON> o<PERSON>:", "smw-sp-property-searchform-inputinfo": "Vstup je citlivý na velikost písmen a je-li použit pro filtrování, zobrazí se jen vlast<PERSON>, k<PERSON><PERSON> spl<PERSON><PERSON><PERSON><PERSON> danou podmínku.", "smw-special-property-searchform": "Zobrazit vlast<PERSON>, <PERSON><PERSON><PERSON> o<PERSON>:", "smw-special-property-searchform-inputinfo": "Vstup rozlišuje malá a velká písmena. Při filtrov<PERSON> jsou zobrazeny jen vlastnosti splňující podmínku.", "smw-special-property-searchform-options": "Nastavení", "smw-special-wantedproperties-filter-label": "Filtr:", "smw-special-wantedproperties-filter-none": "Žádná", "smw-special-wantedproperties-filter-unapproved": "Neschváleno", "smw-special-wantedproperties-filter-unapproved-desc": "Volby filtru použité ve spojení s režimem autority.", "concepts": "Koncepty", "smw-special-concept-header": "Seznam konceptů", "smw-special-concept-empty": "Nebyl nalezen žádný koncept.", "unusedproperties": "Nepoužité vlastnosti", "smw-unusedproperties-docu": "<PERSON><PERSON> stránka obsahuje [https://www.semantic-mediawiki.org/wiki/Unused_properties nepoužívané vlastnosti], kter<PERSON> jsou dekla<PERSON> i přesto, že je žádná jiná stránka nepoužívá. K porovnání se můžete podívat také na speciální stránky [[Special:Properties|všech]] nebo [[Special:WantedProperties|chybějících vlastností]].", "smw-unusedproperty-template": "$1 typu $2", "wantedproperties": "Požadované vlastnosti", "smw-wantedproperties-docu": "<PERSON><PERSON> stránka obsahuje [https://www.semantic-mediawiki.org/wiki/Wanted_properties chyběj<PERSON><PERSON><PERSON> vlastnosti], kter<PERSON> jsou použ<PERSON> na wiki i přesto, že k nim neexistuje stránka s jejich popisem. K porovnání se můžete podívat také na speciální stránky [[Special:Properties|všech]] nebo [[Special:UnusedProperties|nepoužívaných vlastností]].", "smw-wantedproperty-template": "$1 ($2 užití)", "smw_purge": "Obnovit", "types": "<PERSON><PERSON>", "smw_types_docu": "Následuje seznam všech údajových typů, kter<PERSON> je možné přiřadit vlastnostem. Každý údajový typ má stránku, kde je možné uvést dodatečné informace.", "smw-statistics-entities-total": "Entity (celkem)", "smw-statistics-property-total-info": "Celkový počet registrovaných vlastností.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Vlastnost|Vlastnosti}} (celkem)", "smw-statistics-property-used": "{{PLURAL:$1| Vlastnost|Vlastnosti}} (p<PERSON><PERSON><PERSON> s alespoň jednou hodnotou)", "smw-statistics-property-page": "{{PLURAL:$1| Vlastnost|Vlastnosti}} (registrované ve stránce)", "smw-statistics-property-page-info": "<PERSON><PERSON><PERSON> vlastností, k<PERSON><PERSON> ma<PERSON> v<PERSON> strán<PERSON> a popis.", "smw-statistics-property-type": "{{PLURAL:$1| Vlastnost|Vlastnosti}} (přiřazeno k datovému typu)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Dotaz|Dotazy}}", "smw-statistics-query-size": "Velikost dotazu", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Koncept|Koncepty}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Koncept|Koncepty}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Podobjekt|Podobjekty}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Subobjekt|Subobjekty}}", "smw_uri_doc": "URI resolver se stará o implementaci [$1 W3C TAG hledání na httpRange-14].\nStará se o to, aby se lidé nestali webstránkami.", "ask": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_sortby": "Řadit podle sloupce (volitelné)", "smw_ask_ascorder": "Vzestupně", "smw_ask_descorder": "Sestupně", "smw-ask-order-rand": "Náhodně", "smw_ask_submit": "<PERSON><PERSON><PERSON>", "smw_ask_editquery": "<PERSON><PERSON><PERSON><PERSON> dotaz", "smw_add_sortcondition": "[Přidat podmínku na řazení]", "smw-ask-sort-add-action": "Přidat podmínku řazení", "smw_ask_hidequery": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kompaktní zobrazení)", "smw_ask_help": "Pomocník pro dotazy", "smw_ask_queryhead": "Podmínka", "smw_ask_printhead": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "smw_ask_printdesc": "(každý název vlastnosti na samostatný řádek)", "smw_ask_format_as": "Formát výstupu:", "smw_ask_defaultformat": "výchozí", "smw_ask_otheroptions": "<PERSON><PERSON><PERSON>", "smw-ask-otheroptions-info": "<PERSON><PERSON> se<PERSON> o<PERSON><PERSON>, k<PERSON><PERSON> změní příkazy výpisu. Popisy parametrů zobrazíte umístěním ukazatele myši nad parametr.", "smw-ask-otheroptions-collapsed-info": "Pro zobrazení všech dostupných možností prosím použijte ikonu plus", "smw_ask_show_embed": "Zobrazit kód pro vložení do stránky", "smw_ask_hide_embed": "Skrýt kód pro vložení do stránky", "smw_ask_embed_instr": "Tento kód použijte ke vložení tohoto dotazu do textu stránky na wiki.", "smw-ask-delete": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-sorting": "Řazení", "smw-ask-options": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-options-sort": "Možnosti řazení", "smw-ask-format-options": "Formát a možnosti", "smw-ask-parameters": "Parametry", "smw-ask-search": "Hledat", "smw-ask-debug": "Ladit", "smw-ask-debug-desc": "Generuje ladicí informace k dotazu", "smw-ask-no-cache": "Deaktivovat cache dotazu", "smw-ask-no-cache-desc": "Výsledky bez cache dotazu", "smw-ask-result": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-empty": "Odstranit všechny záznamy", "smw-ask-download-link-desc": "Stáhnout výsledky dotazu ve formátu $1", "smw-ask-format": "<PERSON><PERSON><PERSON>", "smw-ask-format-selection-help": "Nápověda pro vybraný formát: $1.", "smw-ask-condition-change-info": "Podmínka byla změněna a vyhledávací modul požaduje spustit dotaz znovu, aby výsledky odpovídaly novým požadavkům.", "smw-ask-input-assistance": "Pomoc při zadávání dat", "smw-ask-extra-query-log": "Proto<PERSON>l <PERSON>ů", "searchbyproperty": "Hledat podle hodnoty vlastnosti", "processingerrorlist": "Seznam chyb zpracování", "smw_sbv_docu": "Hledat na wiki článek, který má vlastnost s jistou hodnotou.", "smw_sbv_novalue": "Nebyla uvedena hodnota. Prosím, vložte ji ve formuláři nebo zobrazte všechny atributy typu $1", "smw_sbv_displayresultfuzzy": "Seznam v<PERSON><PERSON> stránek, j<PERSON><PERSON><PERSON> vlastnost „$1“ má hodnotu „$2“. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> v<PERSON>d<PERSON> m<PERSON>, jsou zobrazeny i blízké hodnoty.", "smw_sbv_property": "Vlastnost:", "smw_sbv_value": "Hodnota:", "smw_sbv_submit": "Hledat výsledky", "browse": "Prohledat wiki", "smw_browselink": "<PERSON><PERSON><PERSON><PERSON>", "smw_browse_article": "<PERSON>ade<PERSON><PERSON><PERSON>, od k<PERSON><PERSON> ch<PERSON>te za<PERSON><PERSON>.", "smw_browse_go": "<PERSON><PERSON><PERSON><PERSON>", "smw_browse_show_incoming": "Zobrazit příchozí vlastnosti", "smw_browse_hide_incoming": "Skrýt příchozí vlastnosti", "smw_browse_no_outgoing": "<PERSON><PERSON> s<PERSON> nem<PERSON> žádné vlastnosti.", "smw_browse_no_incoming": "Na tuto stránku neodkazují žádné vlastnosti.", "smw-browse-from-backend": "Informace jsou nyní načítány z databáze.", "smw-browse-show-group": "Zobrazit skupiny", "smw-browse-hide-group": "<PERSON>kr<PERSON><PERSON> s<PERSON>", "smw_inverse_label_default": "$1 z/ze", "pageproperty": "Hledání vlastností s<PERSON>", "pendingtasklist": "Seznam čekají<PERSON><PERSON>ch <PERSON>", "smw_pp_docu": "Buď zadejte stránku a vlastnost nebo pouze vlastnost pro načtení všech přiřazených hodnot.", "smw_pp_from": "<PERSON><PERSON>", "smw_pp_type": "Vlastnictví:", "smw_pp_submit": "Výsledky hledání", "smw-prev": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{PLURAL:$1|$1}}", "smw-next": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{PLURAL:$1|$1}}", "smw_result_prev": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_next": "<PERSON><PERSON><PERSON>", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "Bo<PERSON><PERSON><PERSON> nej<PERSON>u ž<PERSON>dn<PERSON> v<PERSON>ky.", "smwadmin": "Panel (Sémantická MediaWiki)", "smw-admin-statistics-job-title": "Statistiky <PERSON>", "smw-admin-statistics-querycache-title": "Mezipaměť dotazů", "smw-admin-statistics-semanticdata-overview": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-permission-missing": "Přístup na tuto stránku byl zablokován kvůli chybějícím oprávněním, podrobnosti o nezbytných nastaveních naleznete na stránce nápovědy pro [https://www.semantic-mediawiki.org/wiki/Help:Permissions oprávnění].", "smw_smwadmin_return": "Zpět na $1", "smw_smwadmin_updatestarted": "Byl spuštěn nový proces aktualizace sémantických dat.\nVšechna uložená data budou zkontrolována a, bude-li to nutné, znovu vytvořena nebo opravena.\nPrůběh aktualizace můžete sledovat na této speciální stránce.", "smw_smwadmin_updatenotstarted": "Proces updatu již probí<PERSON>.\nNelze vytvořit další.", "smw_smwadmin_updatestopped": "Všechny procesy aktualizace byly zastaveny.", "smw_smwadmin_updatenotstopped": "<PERSON><PERSON>ste zastavili b<PERSON><PERSON><PERSON><PERSON><PERSON> proces aktualizace, musíte za<PERSON><PERSON><PERSON>ol<PERSON>, <PERSON><PERSON><PERSON><PERSON>, že si jste opravdu jist{{GENDER:||a|(a)}}.", "smw-admin-docu": "Tato speciální stránka vám pomůže s instalací, <PERSON><PERSON>, údrž<PERSON>u a používáním  <a href=\"https://www.semantic-mediawiki.org\">Sémantické MediaWiki</a>. Najdete v ní také další administrativní funkce, úkoly a statistiky.\nPřed spuštěním administrativních funkcí nezapomeňte zálohovat cenná data.", "smw-admin-environment": "Softwarové prostředí", "smw-admin-db": "Instalace databáze", "smw-admin-dbdocu": "<PERSON><PERSON> mohla <PERSON>mantická MediaWiki ukládat sémantická data, potřebuje svou vlastní strukturu databáze (a je přitom nezávislá na jádru MediaWiki, proto neovlivňuje ostatní část instalace MediaWiki).\nTuto přípravnou funkci můžete spustit vícekrát, an<PERSON><PERSON> by <PERSON><PERSON><PERSON> k jakémuko<PERSON> p<PERSON>í, ale je to nutné pouze jednou při instalaci nebo upgradu.", "smw-admin-permissionswarn": "Selže-li operace s SQL chybami, pak uživatel databáze, kterého vaše wiki používá (zkontrolujte soubor „LocalSettings.php“) pravděpodobně nemá dostatečná oprávnění.\nPřidělte tomuto uživateli dostatečná oprávnění k tvorbě a mazání tabulek, dočasně do souboru „LocalSettings.php“ zadejte údaje rootu databáze, nebo použijte údržbový skript <code>setupStore.php</code>, který používá údaje správce.", "smw-admin-dbbutton": "Inicializovat či upgradovat tabulky", "smw-admin-announce": "Nahlásit tuto wiki", "smw-admin-deprecation-notice-title-notice": "Připravované změny", "smw-admin-deprecation-notice-title-notice-explanation": "V této wiki byla rozpoznána následující nastavení, která budou v příští verzi odebrána nebo změněna.", "smw-admin-deprecation-notice-title-replacement": "Nahrazená nebo přejmenovaná nastavení", "smw-admin-deprecation-notice-title-replacement-explanation": "Následující sekce obsahuje nastavení, k<PERSON><PERSON> byla přej<PERSON>ována nebo jinak změněna. Doporučujeme aktualizovat jejich název nebo formát.", "smw-admin-deprecation-notice-title-removal": "Odebraná nastavení", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Odebraná nastavení</b> j<PERSON><PERSON> nastavení, kter<PERSON> byla odebrána v předchozí verzi, ale která se na této wiki nadále používají.", "smw_smwadmin_datarefresh": "Nové vytvoření dat", "smw_smwadmin_datarefreshdocu": "Na základě obsahu stránek wiki je možné obnovit všechna data Semantic MediaWiki.\nTo může být užitečné při opravě poškozených dat nebo při jejich <PERSON>, pokud se v důsledku upgradu softwaru změní vnitřní formát.\nUpdate se spouští stránku po stránce a nebude dokončen ihned.\nNásledující se zobrazuje pokud update právě probíhá a umožňuje vám updaty spouštět či zastavovat (pokud tuto možnost nezakázal administrátor).", "smw_smwadmin_datarefreshprogress": "<strong>Probíhá update.</strong>\n<PERSON>, že update postupu<PERSON> pomalu, protože data zpracovává v malých dávkách vždy, když wiki navštíví nějaký uživatel.\nChcete-li update dokončit rychleji, můžete spustit údržbový skript MediaWiki <code>runJobs.php</code> (k omezení počtu updatů provedených v jedné dávce použijte nastavení <code>--maxjobs 1000</code>).\nOdhadovaný průběh updatu:", "smw_smwadmin_datarefreshbutton": "Naplánovat znovusestavení dat", "smw_smwadmin_datarefreshstop": "Zastavit update", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, jsem si {{GENDER:$1|jistý|jist<PERSON>|jistý/á}}.", "smw-admin-support": "Získání podpory", "smw-admin-supportdocu": "V případě problémů je k pomoci připraveno několik zdrojů:", "smw-admin-installfile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> problémy s instalací, pře<PERSON>t<PERSON>te si návod k instalaci v <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">souboru INSTALL</a> a na <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">instala<PERSON><PERSON><PERSON> strán<PERSON></a>.", "smw-admin-smwhomepage": "Kompletní uživatelská dokumentace Semantic MediaWiki je dostupná na <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Chyby lze hlásit do <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">zápisníku chyb</a>, strán<PERSON> <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">Hlášení chyb</a> pak poskytuje návod jak efektivně napsat hlášení chyby.", "smw-admin-questions": "Další otázky či návrhy můžete pokládat do diskuse na Semantic MediaWiki v <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">uživatelském komunikačním vlákně</a> nebo <a href=\"https://www.semantic-mediawiki.org/wiki/Semantic_MediaWiki_chatroom\">chatovací\n místnosti</a>.", "smw_adminlinks_datastructure": "<PERSON>tov<PERSON> struktura", "smw_adminlinks_inlinequerieshelp": "Nápověda k inline dotazům", "smw-createproperty-isproperty": "Toto je vlastnost typu $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Povolená hodnota této vlastnosti je|Povolené hodnoty této vlastnosti jsou}}:", "smw-paramdesc-category-delim": "Odd<PERSON><PERSON><PERSON>č", "smw-paramdesc-category-userparam": "Parametr předávaný do šablony", "smw-info-par-message": "Jaká zpráva se má zobrazit.", "smw-info-par-icon": "<PERSON><PERSON><PERSON> ikona se má zobrazit, buď „info“ nebo „warning“ (varování).", "prefs-smw": "Sémantická MediaWiki", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Sémantická MediaWiki] (a podobná rozšíření) poskytuje individuální úpravy pro nějaké zvolené funkce. Podívejte se prosím na [https://www.semantic-mediawiki.org/wiki/Help:User_preferences nápovědu] pro dalš<PERSON> informace.", "smw-prefs-ask-options-tooltip-display": "Zobrazit parametr textu jako informační nástroj na speciální stránce [[Special:Ask|přípravy dotazu]] pro #ask.", "smw-ui-tooltip-title-property": "Vlastnost", "smw-ui-tooltip-title-quantity": "Množství", "smw-ui-tooltip-title-warning": "Varování", "smw-ui-tooltip-title-parameter": "Parametr", "smw-ui-tooltip-title-event": "Ud<PERSON>lost", "smw-ui-tooltip-title-note": "Poznámka", "smw_unknowntype": "Pro vlastnost je definován nepodporovaný typ „$1“.", "smw_concept_header": "Stran konceptu \"$1\"", "smw_conceptarticlecount": "{{PLURAL:$1|zob<PERSON>na je|zobrazeny jsou|zobrazeno je}} $1 {{PLURAL:$1|stránka náležející|strán<PERSON> náležející|strán<PERSON> náležejících}} tomuto konceptu.", "group-smwadministrator": "S<PERSON>r<PERSON><PERSON><PERSON> (Sémantická MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|správce|správkyně|správce}} (Semantic MediaWiki)", "action-smw-ruleedit": "edit<PERSON><PERSON> (Sémantická MediaWiki)", "smw-sp-properties-cache-info": "Uvedená data pocházejí z [https://www.semantic-mediawiki.org/wiki/Caching cache] a byla naposledy aktualizována $1.", "smw-sp-properties-header-label": "Seznam vlastností", "smw-livepreview-loading": "Nahrávám…", "smw-sp-searchbyproperty-resultlist-header": "Seznam výsledků", "smw-editpage-annotation-enabled": "Tato stránka podporuje sémantické anotace uvnitř textu (např. <nowiki>„[[Is specified as::World Heritage Site]]“</nowiki>), k<PERSON><PERSON> slou<PERSON> k budování strukturovan<PERSON>ho a prohledávatelného obsahu prostřednictvím Semantic MediaWiki. Podrobný popis, jak používat anotace a funkci parseru #ask, naleznete na stránkách nápovědy [https://www.semantic-mediawiki.org/wiki/Help:Getting_started ''Začínáme''], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation ''Anotace uvnitř textu''] či [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries ''inline dotazy''].", "smw-editpage-annotation-disabled": "Na této stránce nejsou povoleny sémantické anotace uvnitř textu kvůli omezením jmenného prostoru. Podrobný popis, jak tento jmenný prostor povolit, najdete v [https://www.semantic-mediawiki.org/wiki/Help:Configuration nápovědě ke konfiguraci].", "smw-search-syntax-support": "Vyhledávací vstup podporuje použití sémantické [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search syntaxové ot<PERSON>], kter<PERSON> pomůže získávání výsledků s použitím Semantic MediaWiki.", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Vstupní asistent] je také aktivní pro zjednodušení zvolení možných vlastností a kategorií.", "smw-search-profile-extended-section-sort": "Řadit podle", "smw-search-profile-extended-section-form": "<PERSON><PERSON><PERSON><PERSON>", "smw-type-geo": "\"$1\" je typ dat, který popisuje geografické lokace a požaduje [https://www.semantic-mediawiki.org/wiki/Extension:Maps mapové rozšíření] k poskytnutí rozšířených funkcí.", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps Rozšíření map] nebylo z<PERSON>, protože \"$1\" je omezeno v kapacitě, takže nemůže fungovat.", "smw-datavalue-keyword-maximum-length": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> slovo je delší než maximální délku {{PLURAL:$1|$1 znaku|$1 znaků}}.", "smw-type-eid": "\"$1\" je varianta datatypu [[Special:Types/Text|textu]] k popisu externí<PERSON> (založených na URI) a požaduje přidělené vlastnosti, aby dekla<PERSON> [[Property:External formatter uri|Externí formátovač URI]].", "smw-property-predefined-keyw": "\"$1\" je předdefinovaná vlastnost a [[Special:Types/Keyword|druh]] poskytnutý [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Sémantickou MediaWiki], který normalizuje text a má omezenou délku.", "smw-type-keyw": "\"$1\" je druh [[Special:Types/Text|textu]], k<PERSON><PERSON> má o<PERSON>zenou délku a normalizuje reprezentaci obsahu.", "smw-change-propagation-protection": "<PERSON><PERSON> strán<PERSON> je zamčena kvůli prevenci náhodných editací dat během [https://www.semantic-mediawiki.org/wiki/Change_propagation změny anotací hodnot]. Proces může trvat chv<PERSON><PERSON>, než bude stránka odemčena, což záleží na velikosti a frekvenci [https://www.mediawiki.org/wiki/Manual:Job_queue plánovače].", "smw-format-datatable-next": "Dalš<PERSON>", "smw-format-datatable-previous": "Předchozí", "smw-types-title": "Datentyp: $1", "smw-schema-usage": "Použití", "smw-ask-title-keyword-type": "Hledání klíčových slov", "smw-ask-message-keyword-type": "Toto vyhledává shody se stavem <code><nowiki>$1</nowiki></code>.", "smw-property-tab-usage": "Použití", "smw-property-tab-redirects": "Synonyma", "smw-concept-tab-list": "Seznam", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-listingcontinuesabbrev": "<PERSON><PERSON><PERSON><PERSON>.", "smw-showingresults": "Níže zobrazuji nejvýše <strong>$1</strong> {{PLURAL:$1|výsledek|vý<PERSON>dky|výsledků}} počínaje od <strong>$2</strong>."}