{"@metadata": {"authors": ["Eitvys200", "Hugo.arg", "Manvydasz", "McDut<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nemo bis", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "smw-desc": "Padaryti Jūsų wiki labiau prieinamą - ma<PERSON><PERSON><PERSON> ''ir'' žmon<PERSON>ms ([https://www.semantic-mediawiki.org/wiki/Help:User_manual online documentation])", "smw-error": "<PERSON><PERSON><PERSON>", "smw-upgrade-release": "Išleistas", "smw-upgrade-progress": "Progresas", "smw-upgrade-progress-table-optimization": "<PERSON>yk<PERSON><PERSON> lentelės optimizacijos ...", "smw-upgrade-error-why-title": "<PERSON><PERSON><PERSON><PERSON> matau šį puslapį?", "smw-upgrade-error-how-title": "Kaip ištaisyti šią k<PERSON>ą?", "smw-extensionload-error-why-title": "<PERSON><PERSON><PERSON><PERSON> matau šį puslapį?", "smw-extensionload-error-how-title": "Kaip ištaisyti šią k<PERSON>ą?", "smw-upgrade-maintenance-why-title": "<PERSON><PERSON><PERSON><PERSON> matau šį puslapį?", "smw_viewasrdf": "RDF srautas", "smw_finallistconjunct": ", ir", "smw-factbox-head": "... daugiau apie \"$1\"", "smw-factbox-facts": "Faktai", "smw_isspecprop": "Ši savybė yra speciali šioje wiki.", "smw_concept_description": "Sąvokos \"$1\" apraš<PERSON><PERSON>", "smw_no_concept_namespace": "Sąvokos gali būti apibrėžtos tik Concept: vardų srities puslapiuose.", "smw_multiple_concepts": "Kiekviename sąvokos aprašymo puslapyje gali turėti tik vieną sąvokos apibrėžimą.", "smw_concept_cache_miss": "Sąvoka \"$1\" negali būti naudo<PERSON>, nes wiki konfigūracija reikalauja ją apskaič<PERSON>oti off-line.\nJei problema neišnyksta po tam tikro laiko, kre<PERSON><PERSON>tės į svetainės administratorių, kad šią sąvoką padarytų prieinamą.", "smw_noinvannot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> negali būti p<PERSON> atvir<PERSON>š<PERSON>i savybei.", "version-semantic": "Semantiniai praplėtimai", "smw_baduri": "URI formoje \"$1\" yra dra<PERSON><PERSON><PERSON>.", "smw_printername_count": "Paskaičiuoti rezultatus", "smw_printername_csv": "CSV eksportavimas", "smw_printername_dsv": "DSV eksportavimas", "smw_printername_debug": "Derinti už<PERSON>ą (ekspertams)", "smw_printername_embedded": "Įstatyti puslapio turinį", "smw_printername_json": "JSON eksportas", "smw_printername_list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_printername_ol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_printername_ul": "Sudarymas i<PERSON>", "smw_printername_table": "Lentelė", "smw_printername_broadtable": "<PERSON><PERSON><PERSON>", "smw_printername_template": "Šablonas", "smw_printername_templatefile": "Šablono failas", "smw_printername_rdf": "RDF eksportavimas", "smw_printername_category": "Kategorija", "validator-type-class-SMWParamSource": "tekstas", "smw-paramdesc-limit": "<PERSON><PERSON><PERSON><PERSON> gr<PERSON>žinamas rezultatų skaičius", "smw-paramdesc-headers": "Rod<PERSON><PERSON>š<PERSON>/savy<PERSON><PERSON> pavadinimus", "smw-paramdesc-mainlabel": "Etiketėje pateikti pagrindinio puslapio pavadin<PERSON>", "smw-paramdesc-link": "<PERSON><PERSON><PERSON> re<PERSON>š<PERSON> ka<PERSON> nuo<PERSON>as", "smw-paramdesc-intro": "Tekstas būtų rodomas prieš užklausos rezultatus, jei yra kokių nors rezultatų", "smw-paramdesc-outro": "Tekstas būtų rodomas po užklausos rezultatų, jei yra kokių nors rezultatų", "smw-paramdesc-default": "Tekstas būtų rod<PERSON>, jei nėra užklausos rezultatų", "smw-paramdesc-sep": "Skirtukas tarp rezultatų", "smw-paramdesc-template": "<PERSON><PERSON><PERSON><PERSON>, su kuriuo būtų galima parodyti išvedimą, pavadinimas", "smw-paramdesc-columns": "Stulpeli<PERSON>, kuriuose bus rodomas rezu<PERSON>", "smw-paramdesc-userparam": "Reikšmė patenka į kiekvieną šabloną iškvietimą, jei <PERSON> yra naudojamas", "smw-paramdesc-introtemplate": "Šablon<PERSON>, k<PERSON><PERSON> b<PERSON> rodomas prieš už<PERSON> rezultatus, jei yra kokių nors rezultatų", "smw-paramdesc-outrotemplate": "Šablono p<PERSON>din<PERSON>s, k<PERSON><PERSON> b<PERSON> rodomas po užklausos rezultatų, jei yra kokių nors rezultatų", "smw-paramdesc-embedformat": "HTML žymė naudojama apibrėžti ant<PERSON>š<PERSON>", "smw-paramdesc-embedonly": "Nerodyti antraščių", "smw-paramdesc-dsv-filename": "DSV failo pavadinimas", "smw-paramdesc-filename": "Išvesties failo pavadinimas", "smw-paramdesc-searchlabel": "Tekstas tęsti paieškai", "smw-paramdesc-export": "Eksportavimo nustatymas", "smw-printername-feed": "RSS ir Atom srautas", "smw-paramdesc-feedtype": "Srauto <PERSON>", "smw-label-feed-description": "$1 $2 srautas", "smw_iq_disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> užklausos yra išjungtos šioje wiki.", "smw_iq_moreresults": "... to<PERSON>ni rezultatai", "smw_parseerror": "Nuro<PERSON><PERSON> re<PERSON> yra nesuprantama.", "smw_notitle": "\"$1\" negali b<PERSON>ti naudoja<PERSON> kaip pu<PERSON>pio pavadinimas šiame wiki.", "smw_wrong_namespace": "Čia leidžiami tik puslapiai, priklausantys vardų sričiai \"$1\".", "smw_manytypes": "Savybei apibrėžtas daugiau negu vienas tipas.", "smw_emptystring": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>ut<PERSON> yra nep<PERSON>.", "smw_notinenum": "\"$1\" nėra šios savybės galimų reikšmių sąraše ($2).", "smw_noboolean": "\"$1\" nėra pripažįstama kaip loginė (true / false) vertė.", "smw_true_words": "tiesa,t,taip,yes,y", "smw_false_words": "klaida,k,ne,n,false,f,no", "smw_nofloat": "\"$1\" n<PERSON><PERSON> s<PERSON>.", "smw_infinite": "<PERSON><PERSON> did<PERSON>, ka<PERSON> \"$1\" yra ne<PERSON>.", "smw_novalues": "Nenurodyta <PERSON>.", "smw_nodatetime": "Data \"$1\" buvo neatpažinta.", "smw_toomanyclosing": "<PERSON><PERSON><PERSON>, kad yra per daug elementų \"$1\" užklausoje.", "smw_noclosingbrackets": "<PERSON><PERSON><PERSON><PERSON> \"<nowiki>[[</nowiki>\" jūs<PERSON> užklausoje nebuvo uždaryta atitikimo \"]]\".", "smw_misplacedsymbol": "Simbolis \"$1\" buvo panaudotas toje vietoje, kurioje jis n<PERSON>ra naudojamas.", "smw_unexpectedpart": "<PERSON>ž<PERSON><PERSON><PERSON> da<PERSON> \"$1\" nebuvo suprasta.\nRezultatas gali būti ne <PERSON>, kurio t<PERSON>.", "smw_emptysubquery": "Kai kurios užkla<PERSON>os dalys turi blogas sąlygas.", "smw_propvalueproblem": "Savy<PERSON><PERSON><PERSON> „$1“ buvo nesuprasta.", "smw_attribute_header": "Puslapiai naudojantys savybę „$1“", "smw-propertylist-redirect-header": "Sinonimai", "exportrdf": "Eksportuoti puslapius į RDF", "smw_exportrdf_submit": "Eksportuoti", "properties": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-categories": "<PERSON><PERSON><PERSON><PERSON>", "concepts": "<PERSON>nce<PERSON><PERSON><PERSON>", "smw-special-concept-header": "Koncepcijų sąrašas", "unusedproperties": "Nenaudo<PERSON><PERSON>", "smw-unusedproperty-template": "$1, $2 tipo", "smw_purge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "types": "Tipai", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Savybė|Savybės}} (viso)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Užklausa|Užklausos}}", "smw-statistics-query-size": "<PERSON>ž<PERSON><PERSON><PERSON> d<PERSON>", "smw-statistics-delete-count": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON> objektas|<PERSON><PERSON><PERSON> objektai}} (paž<PERSON>ėti trynimui)", "smw_ask_sortby": "Rūšiuoti pagal stulpelį (neprivaloma)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "smw_ask_descorder": "Mažėjimo tvarka", "smw_ask_submit": "<PERSON><PERSON><PERSON> rezultatus", "smw_ask_editquery": "Redaguoti užklausą", "smw_add_sortcondition": "[<PERSON><PERSON><PERSON><PERSON> rū<PERSON><PERSON><PERSON><PERSON> s<PERSON>]", "smw_ask_hidequery": "Slėpti užklausą", "smw_ask_help": "Užklausų pagalba", "smw_ask_queryhead": "<PERSON>ąly<PERSON>", "smw_ask_printhead": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_printdesc": "(pridėkite vieną savybės pavadinimą eilutėje)", "smw_ask_format_as": "Formatuoti kaip:", "smw_ask_defaultformat": "numatomas", "smw_ask_otheroptions": "Kit<PERSON> nustatym<PERSON>", "smw_ask_show_embed": "Rodyti įterpimo kodą", "smw_ask_hide_embed": "Slėpti įterpimo kodą", "smw-ask-delete": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-sorting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-ask-search": "Pa<PERSON>š<PERSON>", "searchbyproperty": "Ieškoti pagal savybę", "processingerrorlist": "<PERSON>laid<PERSON> są<PERSON>", "smw_sbv_property": "Savybė:", "smw_sbv_value": "Reikšmė:", "smw_sbv_submit": "<PERSON><PERSON><PERSON> rezultatus", "browse": "Naršyti viki", "smw_browselink": "Narš<PERSON>i savybes", "smw_browse_go": "Eiti", "smw_browse_show_incoming": "<PERSON><PERSON><PERSON>, kurios čia nukreipia", "smw_browse_hide_incoming": "s<PERSON><PERSON><PERSON><PERSON> sa<PERSON>, kurios čia nukreipia", "smw_browse_no_outgoing": "Šis puslapis neturi savybių.", "smw_browse_no_incoming": "<PERSON><PERSON><PERSON>, kurios nukreipia į šį puslapį.", "smw_inverse_label_default": "$1 iš", "smw_pp_from": "<PERSON><PERSON>:", "smw_pp_type": "Savybė:", "smw_pp_submit": "<PERSON><PERSON><PERSON> rezultatus", "smw-prev": "ankstesnis {{PLURAL:$1|$1}}", "smw-next": "kitas {{PLURAL:$1|$1}}", "smw_result_prev": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_next": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_results": "Rezultatai", "smw_result_noresults": "Nėra rezultatų.", "smw-admin-permission-missing": "Prieiga prie šio puslapio buvo užblokuota dėl teisių nebuvimo, pra<PERSON><PERSON> perž<PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Permissions teisių] pagalbinį puslapį, kur rasite informaciją apie reikiamus nustatymus.", "smw_smwadmin_return": "Grįžti į $1", "smw_smwadmin_updatestopped": "Visi egzistuojantys atnaujinimo procesai buvo sustabdyti.", "smw-admin-announce": "Paskelbti savo viki", "smw-admin-deprecation-notice-title-notice": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-deprecation-notice-title-replacement": "Pakeisti ar pervadinti nustatymai", "smw-admin-deprecation-notice-title-replacement-explanation": "Nustatymai buvo pervadinta ar kitaip pakeisti, praš<PERSON> atnaujinti jų pavadinimą ar formatą.", "smw-admin-deprecation-notice-title-removal": "Pa<PERSON>lint<PERSON> nustatymai", "smw_smwadmin_datarefreshbutton": "<PERSON><PERSON><PERSON><PERSON> atnau<PERSON> duomenis", "smw_smwadmin_datarefreshstop": "Sustabdyti šį atnaujinimą", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, esu {{GENDER:$1|tikras|tikra}}.", "smw-admin-support": "<PERSON><PERSON><PERSON><PERSON> parama", "smw-admin-supplementary-duplookup-title": "Pasikartojantys įrašai", "smw-list-count": "Šiame sąraše yra $1 {{PLURAL:$1|įrašas|įrašai}}.", "smw_adminlinks_datastructure": "Duomenų struktūra", "smw_adminlinks_displayingdata": "<PERSON><PERSON><PERSON>", "smw-property-indicator-type-info": "{{PLURAL: $1|Naudotojas|Sistema}}", "smw-createproperty-isproperty": "Tai yra $1 tipo savybė.", "smw-createproperty-allowedvals": "<PERSON><PERSON> le<PERSON> re<PERSON> yra:", "smw-paramdesc-category-template": "<PERSON><PERSON><PERSON><PERSON>, kuris bus naudojamas formatuoti objektus", "smw-info-par-message": "<PERSON><PERSON><PERSON><PERSON>.", "smw-info-par-icon": "Piktograma rodymui, arba „info“ arba „warning“.", "smw-ui-tooltip-title-property": "Savybė", "smw-ui-tooltip-title-quantity": "Vienetų konvertavimas", "smw-ui-tooltip-title-info": "Informacija", "smw-ui-tooltip-title-service": "Paslaugų nuorodos", "smw-ui-tooltip-title-warning": "Įspėjimas", "smw-ui-tooltip-title-parameter": "Parametras", "smw-ui-tooltip-title-event": "Įvykis", "smw-ui-tooltip-title-note": "Pastaba", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw-ui-tooltip-title-reference": "<PERSON><PERSON><PERSON><PERSON>", "smw_unknowntype": "<PERSON><PERSON><PERSON> nustatytas ne<PERSON>as", "smw-sp-properties-header-label": "Savybių sąrašas", "smw-sp-admin-settings-button": "Generuoti nustatymų sąrašą", "smw-admin-objectid": "ID:", "smw-livepreview-loading": "Įkeliama…", "smw-sp-searchbyproperty-resultlist-header": "Rezultatų sąrašas", "smw-types-list": "Duomenų tipų sąrašas", "smw-datavalue-languagecode-invalid": "„$1“ nebuvo atpažintas kaip palaikomas kalbos kodas.", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|sekundė|sekundės}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekundė|sekund<PERSON>s|sekund<PERSON><PERSON><PERSON>}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|sekundė|sekundės}}", "smw-datavalue-constraint-violation-single-value": "„[[Property:$1|$1]]“ ypatybė turi <code> single_value </code> apribojimą ir vertė „$2“ pažeidžia šį reikalavimą.", "smw-datavalue-external-formatter-invalid-uri": "„$1“ yra negalimas URL.", "smw-datavalue-parse-error": "Nurodyta reikšmė „$1“ nebuvo suprasta.", "smw-clipboard-copy-link": "Kopijuoti nuorodą į iškarpinę", "smw-data-lookup-with-wait": "Praš<PERSON><PERSON> a<PERSON>dor<PERSON> ir tai gali užtrukti.", "smw-section-expand": "Išskleisti dalį", "smw-section-collapse": "Suskleisti dalį", "smw-help": "Pagalba", "smw-es-replication-error-no-connection": "Replikacijos stebėjimas negali atlikti jokių patikrinimų, nes negali sukurti ryšio su Elasticsearch klusteriu.", "smw-es-replication-error-suggestions-no-connection": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ip<PERSON> į „wiki“ administratorių ir pranešti apie „be ryšio“ problemą.", "smw-listingcontinuesabbrev": "tęsti", "smw-showingresults": "Žemiau rodoma iki '''$1''' {{PLURAL:$1|rezultato|rezultatų|rezultatų}} pradedant #'''$2'''."}