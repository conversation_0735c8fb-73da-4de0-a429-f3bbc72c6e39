{"@metadata": {"authors": ["20ع<PERSON><PERSON> العزيز", "AA800", "Alaa", "Cigaryno", "Kassem7899", "Kghbln", "<PERSON><PERSON><PERSON>@yahoo.fr http://www.cri.ensmp.fr", "McDut<PERSON><PERSON>", "Meno25", "Mido", "<PERSON>", "<PERSON><PERSON> ho<PERSON>ny", "NEHAOUA", "OsamaK", "<PERSON><PERSON>", "<PERSON><PERSON>", "Uwe", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "أحمد <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "الهميان", "دي<PERSON><PERSON><PERSON>", "روخو", "محم<PERSON> أحمد عبد الفتاح"]}, "smw-desc": "تقرّب الويكي إلى كلٍّ من النّظم الحاسوبية  ''و'' البشر ([https://www.semantic-mediawiki.org/wiki/Help:User_manual دليل المستخدم])", "smw-error": "خطأ", "smw-upgrade-error": "تم تثبيت وتمكين ميدياويكي الدلالي لكنه يفتقد إلى  [https://www.semantic-mediawiki.org/wiki/Help:Upgrade مفتاح ترقية] مناسب.", "smw-upgrade-release": "أصدر", "smw-upgrade-progress": "التقدم", "smw-upgrade-progress-explain": "يصعب التنبؤ بتقدير وقت الانتهاء من الترقية حيث أنه يعتمد على حجم مستودع البيانات والأجهزة المتاحة ويمكن أن يستغرق لحظة حتى يكتمل الويكي الأكبر. \n\nيُرجَى الاتصال بإداريك المحلي للحصول على مزيد من المعلومات حول التقدم.", "smw-upgrade-progress-create-tables": "إنشاء (أو تحديث) الجداول والمؤشرات...", "smw-upgrade-progress-post-creation": "تشغيل مهام إنشاء المنشور...", "smw-upgrade-progress-table-optimization": "تحسينات جدول التشغيل...", "smw-upgrade-progress-supplement-jobs": "إضافة وظائف تكميلية...", "smw-upgrade-error-title": "خطأ في ميدياويكي الدلالي", "smw-upgrade-error-why-title": "لماذا أرى هذه الصفحة؟", "smw-upgrade-error-why-explain": "لقد تغير هيكل قاعدة البيانات الداخلية في ميدياويكي ويتطلب بعض التعديلات لكي تعمل بكامل طاقتها، يمكن أن يكون هناك العديد من الأسباب بما فيها: \n* تمت إضافة خصائص مرممة إضافية (يتطلب إعداد جدول إضافي)  \n* تحتوي الترقية على بعض التغييرات على الجداول أو الفهارس التي تقوم بإجراء اعتراض إلزامي قبل الوصول إلى البيانات\n* التغييرات في محرك التخزين أو الاستعلام", "smw-upgrade-error-how-title": "كيف أقوم بإصلاح هذا الخطأ؟", "smw-upgrade-error-how-explain-admin": "يجب على الإداري (أو أي شخص لديه صلاحية الإداري) أن يدير إما سكريبت صيانة [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] ميدياويكي أو [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] ميدياويكي الدلالي.", "smw-upgrade-error-how-explain-links": "يمكنك أيضا الرجوع إلى الصفحات التالية للحصول على مزيد من المساعدة:\n* تعليمات [https://www.semantic-mediawiki.org/wiki/Help:Installation التثبيت]\n* صفحة المساعدة [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting استكشاف الأخطاء وإصلاحها]", "smw-extensionload-error-why-title": "لماذا أرى هذه الصفحة؟", "smw-extensionload-error-why-explain": "<b>لم</b> يتم تحميل الامتداد  باستخدام <code>enableSemantics</code> وبدلا من ذلك تم تمكينه بوسائل أخرى مثل استخدام  <code>wfLoadExtension( 'SemanticMediaWiki' )</code> مباشرةً.", "smw-extensionload-error-how-title": "كيف يمكنني إصلاح هذا الخطأ؟", "smw-extensionload-error-how-explain": "لتمكين الامتداد وتجنب المشكلات المتعلقة بإعلانات النطاقات والتكوينات المعلقة؛ من الضروري استخدام <code>enableSemantics</code> والتي ستضمن تعيين المتغيرات المطلوبة قبل تحميل الامتداد عبر <code>ExtensionRegistry</code>. \n\nيُرجًى إلقاء نظرة على صفحة المساعدة [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics تمكين الدلالات] للمزيد من المساعدة.", "smw-upgrade-maintenance-title": "صيانة ميدياويكي الدلالي", "smw-upgrade-maintenance-why-title": "لماذا أرى هذه الصفحة؟", "smw-upgrade-maintenance-note": "يخضع النظام حاليا [https://www.semantic-mediawiki.org/wiki/Help:Upgrade لترقية] امتداد [https://www.semantic-mediawiki.org/ ميدياويكي الدلالي] مع مستودع بياناته ونود أن نطلب منك صبرك وأن تسمح باستمرار الصيانة قبل أن يصبح من الممكن الوصول إلى الويكي مرة أخرى.", "smw-upgrade-maintenance-explain": "تحاول الإضافة تقليل التأثير ووقت التوقف عن العمل من خلال إرجاء معظم مهام الصيانة الخاصة بها إلى ما بعد <code>update.php</code> ولكن يلزم إجراء بعض التغييرات المتعلقة بقاعدة البيانات أولا لتفادي عدم تناسق البيانات، يمكن أن تشمل:  \n* تغيير هياكل الجدول مثل إضافة حقول جديدة أو تعديلها   \n* تغيير أو إضافة مؤشرات الجدول  \n* تشغيل تحسينات الجدول (عند التمكين)", "smw-semantics-not-enabled": "لم يتم تمكين وظائف ميدياويكي الدلالي لهذا الويكي.", "smw_viewasrdf": "تلقيمة RDF", "smw_finallistconjunct": " و", "smw-factbox-head": "...المزيد عن \"$1\"", "smw-factbox-facts": "حقا<PERSON>ق", "smw-factbox-facts-help": "يعرض البيانات والحقائق التي تم إنشاؤها بواسطة مستخدم", "smw-factbox-attachments": "المرفقات", "smw-factbox-attachments-value-unknown": "غ/م", "smw-factbox-attachments-is-local": "م<PERSON><PERSON>ي", "smw-factbox-attachments-help": "يعرض المرفقات المتاحة", "smw-factbox-facts-derived": "حقائق مشتقة", "smw-factbox-facts-derived-help": "يعرض الحقائق التي تم استخلاصها من القواعد أو بمساعدة تقنيات التفكير الأخرى", "smw_isspecprop": "هذه خصيصة مميَّزة في هذه الويكي.", "smw-concept-cache-header": "استخدام ذاكرة التخزين المؤقت", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count مخبأ مفهوم] يحتوي على {{PLURAL:$1|كيان '''واحد'''|'''$1''' كيانات}} ($2).", "smw-concept-no-cache": "لا يوجد مخبأ متاح.", "smw_concept_description": "وصف المفهوم \"$1\"", "smw_no_concept_namespace": "المفاهيم لا يمكن تعريفها إلا في الصفحات ضمن مفهوم : نطاق التسمية", "smw_multiple_concepts": "كل صفحة مفهوم لا يمكن أن تضم أكثر من تعريف مفهوم واحد.", "smw_concept_cache_miss": "المبدأ \"$1\" لا يمكن استخدامه حاليا إذ أن كيفية ضبط الويكي تقضي بتعطيلها. إذا لم تنته المشكلة بعد برهة، فسَل إداري الموقع ليتيح هذا المبدأ.", "smw_noinvannot": "لا يمكن تعيين قيم لخصائص معكوسة.", "version-semantic": "ملحقات دلالية", "smw_baduri": "المسارات في الصيغة \"$1\" غير مسموح بها هنا.", "smw_csv_link": "سي إس في", "smw_dsv_link": "دي إس في", "smw_json_link": "جسون", "smw_rdf_link": "آر دي إف", "smw_printername_count": "عدّ النتائج", "smw_printername_csv": "تصدير في صيغة CSV", "smw_printername_dsv": "تصدير في صيغة DSV", "smw_printername_debug": "استعلام صيانة (للخبراء)", "smw_printername_embedded": "تضمين محتويات الصفحة", "smw_printername_json": "تصدير في صيغة JSON", "smw_printername_list": "قائمة", "smw_printername_plainlist": "قائمة عادية", "smw_printername_ol": "عناصر مُرقَّمة", "smw_printername_ul": "عناصر مسرودة", "smw_printername_table": "جدول", "smw_printername_broadtable": "جدول عريض", "smw_printername_template": "قالب", "smw_printername_templatefile": "<PERSON><PERSON><PERSON> قالب", "smw_printername_rdf": "تصدير في صيغة RDF", "smw_printername_category": "تصنيف", "validator-type-class-SMWParamSource": "نصّ", "smw-paramdesc-limit": "العدد الأقصى للنتائج المُرجعة", "smw-paramdesc-offset": "إزاحة النتيجة الأولى", "smw-paramdesc-headers": "اعرض العناوين/أسماء الخصائص", "smw-paramdesc-mainlabel": "العنوان الذي سيُعيّن للصفحة الرئيسية", "smw-paramdesc-link": "أظهر القيم كروابط", "smw-paramdesc-intro": "النّصّ المعروض قبل نتائج الاستعلام (إنْ وُجدت)", "smw-paramdesc-outro": "النّصّ المعروض بعد نتائج الاستعلام (إنْ وُجدت)", "smw-paramdesc-default": "النّصّ المعروض إذا لم توجد نتائج للاستعلام", "smw-paramdesc-sep": "الفاصل بين النتائج", "smw-paramdesc-propsep": "الفاصل بين خصائص إدخال النتيجة", "smw-paramdesc-valuesep": "الفاصل بين قيم خاصية نتيجة", "smw-paramdesc-showsep": "أظهر الفاصلة في رأس ملف CSV على النحو (\"sep=<الرمز>\")", "smw-paramdesc-distribution": "بدلاً من عرض كل القيم، اسرد عدد مرات ورودها.", "smw-paramdesc-distributionsort": "رتّب توزيعات القيم حسب عدد مرات ورودها.", "smw-paramdesc-distributionlimit": "حدّ توزيع القيم بتواتر بعض القيم لا كلّها.", "smw-paramdesc-aggregation": "حدد ما يجب أن يرتبط به التجميع", "smw-paramdesc-template": "اسم القالب المستخدم لعرض المخرجات به", "smw-paramdesc-columns": "عد<PERSON> الأعمدة لعرض النتائج بها", "smw-paramdesc-userparam": "قيمة تُمرَّر في كل استدعاء قالب إذا كان القالب سيُستخدَم.", "smw-paramdesc-class": "فئة CSS إضافية لتعيين للقائمة", "smw-paramdesc-introtemplate": "اسم قالب يُعرض قبل نتائج الاستعلام، إنْ وُجِدَت.", "smw-paramdesc-outrotemplate": "اسم قالب يُعرض بعد نتائج الاستعلام، إنْ وُجِدَت.", "smw-paramdesc-embedformat": "وسم HTML المستخدم لتعريف الترويسة", "smw-paramdesc-embedonly": "لا تظهر الترويسات", "smw-paramdesc-table-class": "فئة CSS إضافية تُعيَّن للجدول", "smw-paramdesc-table-transpose": "عرض رؤوس الجدول عموديا والنتائج أفقيا", "smw-paramdesc-rdfsyntax": "نَحوُ RDF المُستَخدم", "smw-paramdesc-csv-sep": "تحديد فاصل الأعمدة", "smw-paramdesc-csv-valuesep": "تحديد فاصل قيمة", "smw-paramdesc-csv-merge": "دمج الصفوف وقيم العمود مع معرف موضوع متطابق (يُعرَف أيضا باسم العمود الأول)", "smw-paramdesc-csv-bom": "إضافة بوم (حرف لإشارة إنديانيس) في الجزء العلوي من ملف الإخراج", "smw-paramdesc-dsv-separator": "الفاصلة المُستَخدَمة", "smw-paramdesc-dsv-filename": "اسم ملف DSV", "smw-paramdesc-filename": "الاسم للملف المستخرج", "smw-smwdoc-description": "يعرض جدولا يضمّ كل المعامِلات التي يمكن استخدامها مع صيغة النتائج المختارة مع قيمها المبدئية و وصفها.", "smw-smwdoc-default-no-parameter-list": "لا يوفر تنسيق النتيجة هذا وسائط تنسيق محددة.", "smw-smwdoc-par-format": "صيغة النتائج التي تُعرض تفاصيل معامِلاتها.", "smw-smwdoc-par-parameters": "أي المعاملات تُعرَض. \"specific\" تعني الخاصة بالصيغة المختارة و \"base\" تعني المشتركة بين كل الصيغ و \"all\" تعني كلّها.", "smw-paramdesc-sort": "الخصيصة التي تُرتّب عليها نتائج الاستعلام", "smw-paramdesc-order": "رتبة ترتيب الاستعلام", "smw-paramdesc-searchlabel": "نص مواصلة عرض النتائج", "smw-paramdesc-named_args": "المعاملات الممررة بأسمائها إلى القالب", "smw-paramdesc-template-arguments": "لتعيين كيفية تمرير الوسائط المسماة إلى القالب", "smw-paramdesc-import-annotation": "يجب نسخ بيانات مشروحة إضافية أثناء تحليل موضوع ما", "smw-paramdesc-export": "<PERSON>يار التصدير", "smw-paramdesc-prettyprint": "مخرجات تجميلية لإدخال مسافات و سطور فارغة", "smw-paramdesc-json-unescape": "الإخراج لاحتواء شرطات مائلة غير منفذة وأحرف يونيكود متعددة البايت", "smw-paramdesc-json-type": "نوع التسلسل", "smw-paramdesc-source": "مصدر بديل للاستعلام", "smw-paramdesc-jsonsyntax": "نحوُ JSON المُستَخدَم", "smw-printername-feed": "تلقيمات Atom و RSS", "smw-paramdesc-feedtype": "نوع التلقيمة", "smw-paramdesc-feedtitle": "نصُّ عنوان التلقيمة", "smw-paramdesc-feeddescription": "نصُّ وصف التلقيمة", "smw-paramdesc-feedpagecontent": "محتوى الصفحة المعروض مع التلقيمة", "smw-label-feed-link": "<PERSON>ر <PERSON><PERSON> إس", "smw-label-feed-description": "تلقيمة $2 $1", "smw-paramdesc-mimetype": "نوع الوسائط (نوع MIME) لملف الإخراج", "smw_iq_disabled": "الاستعلامات الدلالية مُعطّلة في هذه الويكي.", "smw_iq_moreresults": "…مزيد من النتائج", "smw_parseerror": "القيمة المعطاة لم يتم فهمها.", "smw_decseparator": "٫", "smw_kiloseparator": "،", "smw_notitle": "\"$1\" لا يمكن أن يكون اسم صفحة في هذه الويكي.", "smw_noproperty": "\"$1\" لا يمكن أن يكون اسم خصيصة في هذه الويكي.", "smw_wrong_namespace": "وحدها الصفحات في النطاق \"$1\" مسموح بها هنا.", "smw_manytypes": "عُرّف أكثر من نوع بيانات للخصيصة ذاتها.", "smw_emptystring": "النصوص الفارغة غير مقبولة.", "smw_notinenum": "\"$1\" ليست في قائمة ($2) [[Property:Allows value|للقيم المسموح بها]] للخاصية \"$3\".", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" ليست ضمن القائمة ($2) [[Property:Allows value|القيم المسموح بها]] للخاصية \"$3\".", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" ليست ضمن نطاق \"$2\" المحدد بواسطة قيد [[Property:Allows value|القيم المسموح بها]] للخاصية \"$3\".", "smw-constraint-error": "مشكلة قيد", "smw-constraint-error-suggestions": "يُرجَى التحقق من الانتهاكات والخواص المدرجة مع قيمها المشروحة لضمان تلبية جميع متطلبات القيد.", "smw-constraint-error-limit": "ستتضمن القائمة $1 انتهاكات بحد أقصى.", "smw_noboolean": "\"$1\" لم يُتعرّف عليها كقيمة منطقية (صواب/بطلان).", "smw_true_words": "صواب،ص،نعم،ن", "smw_false_words": "بطلان،ب،لا،ل", "smw_nofloat": "ليس عددا \"$1\".", "smw_infinite": "الأرقام الكبيرة مثل \"$1\" غير مدعومة.", "smw_unitnotallowed": "\"$1\" ليس مُعرَّفا كوحدة قياس صحيحة لهذه الخصيصة.", "smw_nounitsdeclared": "لم تُعرّف لهذه الخصيصة وحدات قياس.", "smw_novalues": "لم تُعيّن أي قِيَم.", "smw_nodatetime": "التاريخ \"$1\" لم يتم فهمه.", "smw_toomanyclosing": "يبدو أن \"$1\" ترد كثيرا للغاية في الاستعلام.", "smw_noclosingbrackets": "بعض استخدامات \"<nowiki>[[</nowiki>\" في الاستعلام لم تُغلَق بوضع \"]]\" مقابلة.", "smw_misplacedsymbol": "الرمز \"$1\" استُخدِم في موضع لا يفيد فيه.", "smw_unexpectedpart": "الجزء \"$1\" من الاستعلام لم يفهم.\nالنتائج قد لا تكون كالمتوقعة.", "smw_emptysubquery": "يوجد استعلام فرعي بلا شرط صحيح.", "smw_misplacedsubquery": "استُخدِم استعلام فرعي في موضع غير مسموح فيه بالاستعلامات الفرعية.", "smw_valuesubquery": "الاستعلامات الفرعية غير مدعومة لقيم الخصيصة \"$1\".", "smw_badqueryatom": "جزء ما \"<nowiki>[[…]]</nowiki>\" من الاستعلام لم يتم فهمه.", "smw_propvalueproblem": "قيمة الخصيصة \"$1\" لم يتم فهمها.", "smw_noqueryfeature": "تفصيلة ما في الاستعلام ليست مدعومة في هذه الويكي فأغفِل جزء من الاستعلام ($1).", "smw_noconjunctions": "الوصل في الاستعلامات غير مدعوم في هذه الويكي فأُغفِل جزء من الاستعلام ($1).", "smw_nodisjunctions": "المفارق في الاستعلامات ليست مدعومة في هذه الويكي فأُغفِل جزء من الاستعلام $1.", "smw_querytoolarge": "{{PLURAL:$2|شروط الاستعلام}} التالية لم يمكن أخذها في الاعتبار بسبب قيود من الويكي على حجم أو عمق الاستعلام: <code>$1</code>.", "smw_notemplategiven": "أدخل قيمة للمعامل \"template\" لكي تعمل صيغة الاستعلام هذه.", "smw_db_sparqlqueryproblem": "تعذّر إيجاد نتيجة للاستعلام من قاعدة بيانات SPARQL. هذه العطل قد يكون مؤقتا أو قد يدّل على علّة في برمجية قاعدة البيانات.", "smw_db_sparqlqueryincomplete": "الإجابة على هذا الاستعلام اتّضح أنها صعبة للغاية و قد تم إغفالها. بعض النتائج قد تكون ناقصة. استخدم استعلاما أبسط لو أمكن.", "smw_type_header": "الخصائص من النوع \"$1\"", "smw_typearticlecount": "التالية {{PLURAL:$1||خصيصة واحدة|خصيصتان|$1 خصائص|$1 خصيصة}} من هذا النوع.", "smw_attribute_header": "الصفحات التي تستخدم الخصيصة \"$1\"", "smw_attributearticlecount": "التالية {{PLURAL:$1||الصفحة التي لها|الصفحتان اللتان لهما|الصفحات التي لها}} هذه الخصيصة.", "smw-propertylist-subproperty-header": "خصائص فرعية", "smw-propertylist-redirect-header": "مرادفات", "smw-propertylist-error-header": "صفحات بمهام غير لائقة", "smw-propertylist-count": "عرض $1 {{PLURAL:$1|كيان|كيانات}} مرتبط.", "smw-propertylist-count-with-restricted-note": "عرض $1 {{PLURAL:$1|كيان|كيانات}} مرتب<PERSON> (يتوفر المزيد ولكن يقتصر العرض على \"$2\").", "smw-propertylist-count-more-available": "عرض $1 {{PLURAL:$1|كيان|كيانات}} مرتب<PERSON> (يتوفر المزيد)", "specialpages-group-smw_group": "سيمانتيك ميدياويكي", "specialpages-group-smw_group-maintenance": "صيانة", "specialpages-group-smw_group-properties-concepts-types": "الخصائص والمفاهيم والأنواع", "specialpages-group-smw_group-search": "التصفح والبحث", "exportrdf": "تصدير الصفحات إلى RDF", "smw_exportrdf_docu": " هذه الصفحة تتيح لك الحصول على بيانات من صفحة في صيغة RDF.\nلتصدير صفحات، أدخل عناوينها في حقل النص أدناه، عنوانا واحدا في السطر.", "smw_exportrdf_recursive": "صدّر كل الصفحات ذات الصلة على نحو عودي.\nلاحظ أن النتيجة قد تكون كبيرة.", "smw_exportrdf_backlinks": "كذلك صدّر كل الصفحات التي تشير إلى صفحات تم تصديرها.\nهذا يولد RDF قابلا للتصفح.", "smw_exportrdf_lastdate": "لا تصدّر الصفحات التي لم تتغير منذ لحظة معينة في الزمن.", "smw_exportrdf_submit": "صدّر", "uriresolver": "حال_المسارات", "properties": "الخصائص", "smw-categories": "تصنيفات", "smw_properties_docu": "الخصائص التالية مستخدمة في هذه الويكي.", "smw_property_template": "$1 من نوع $2 ({{PLURAL:$3|مرة|مرّتان|$3 مرات}})", "smw_propertylackspage": "كل خصيصة يجب أن توصف بصفحة!", "smw_propertylackstype": "لم يحدد نوع لهذه الخصيصة (افتُرِضَ النوع $1 مؤقتا).", "smw_propertyhardlyused": "هذه الخصيصة تكاد لا تُستخدم في الويكي!", "smw-property-name-invalid": "الخصيصة $1 لا يمكن استخدامها (اسم الخصيصة غير صحيح).", "smw-property-name-reserved": "تم إدراج \"$1\" كاسم محجوز ولا يجب استخدامه كخاصية، قد تحتوي صفحة  [https://www.semantic-mediawiki.org/wiki/Help:Property_naming صفحة المساعدة] التالية على معلومات عن سبب حجز هذا الاسم.", "smw-sp-property-searchform": "عرض الخصائص التي تحتوي على:", "smw-sp-property-searchform-inputinfo": "الإدخال حساس لحالة الأحرف، و عندما يستخدم كمرشّح فإنّ الخصائص التي تتطابق مع الشرط وحدها تُعرض.", "smw-special-property-searchform": "عرض الخصائص التي تحتوي على:", "smw-special-property-searchform-inputinfo": "الإدخال حساس لحالة الأحرف وعند استخدامه لتصفية، يتم عرض الخصائص التي تتناسب مع الحالة فقط.", "smw-special-property-searchform-options": "خيارات", "smw-special-wantedproperties-filter-label": "مرشح:", "smw-special-wantedproperties-filter-none": "لا شيء", "smw-special-wantedproperties-filter-unapproved": "غير موافق عليها", "smw-special-wantedproperties-filter-unapproved-desc": "خيار التصفية المستخدم في اتصال مع وضع السلطة.", "concepts": "المفاهيم", "smw-special-concept-docu": "يمكن عدّ [https://www.semantic-mediawiki.org/wiki/Help:Concepts المفهوم] تصنيفا ديناميًّا، أي تعدادًا لمجموعة من الصفحات لم يُنشّأ يدويا، بل تُولّده ميدياويكي الدلالية من وصف استعلام مُعطى.", "smw-special-concept-header": "قائمة مفاهيم", "smw-special-concept-count": "{{PLURAL:$1|المفهوم|المفهومان|$1 المفاهيم}} التالية {{PLURAL:$1|مسرود|مسرودان|مسرودة}}", "smw-special-concept-empty": "لا توجد مفاهيم.", "unusedproperties": "خصائص غير مستخدمة", "smw-unusedproperties-docu": "تسرد هذه الصفحة [https://www.semantic-mediawiki.org/wiki/Unused_properties الخصائص غير المستخدمة] التي تم تعريفها على الرغم من عدم استفادة صفحات أخرى منها، للحصول على عرض متباين; انظر الصفحات الخاصة [[Special:Properties|كل الخصائص]] أو [[Special:WantedProperties|خصائص مطلوبة]].", "smw-unusedproperty-template": "$1 من نوع $2", "wantedproperties": "خصائص مطلوبة", "smw-wantedproperties-docu": "تسرد هذه الصفحة [https://www.semantic-mediawiki.org/wiki/Wanted_properties خصائص مطلوبة] مستخدمة في الويكي ولكن لا توجد صفحات تصفها، للحصول على عرض متباين; انظر الصفحات الخاصة [[Special:Properties|كل الخصائص]] أو [[Special:UnusedProperties|خصائص غير مستخدمة]].", "smw-wantedproperty-template": "$1 ({{PLURAL:$2|استخدام|استخدامان|$2 استخدامات}})", "smw-special-wantedproperties-docu": "تسرد هذه الصفحة [https://www.semantic-mediawiki.org/wiki/Wanted_properties الخصائص المطلوبة] المستخدمة في الويكي ولكن ليست لديها صفحة تصفها، للحصول على عرض متباين; انظر الصفحات الخاصة [[Special:Properties|كل الخصائص]] أو[[Special:UnusedProperties|خصائص غير مستخدمة]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|استخدام|استخدامات}})", "smw_purge": "أن<PERSON>ش", "smw-purge-update-dependencies": "يقوم ميدياويكي الدلالي بتطهير الصفحة الحالية بسبب بعض التبعيات القديمة التي اكتشفها والتي تتطلب تحديثا.", "smw-purge-failed": "حاول ميدياويكي الدلالي تطهير الصفحة لكنه فشل", "types": "الأنواع", "smw_types_docu": "قائمة [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes أنواع البيانات المتاحة] بكل [https://www.semantic-mediawiki.org/wiki/Help: نوع] يمثل مجموعة فريدة من السمات لوصف قيمة من حيث التخزين وعرض الخصائص التي هي وراثية إلى الخاصية المعينة.", "smw-special-types-no-such-type": "\"$1\" غير معروف أو لم يتم تحديده كنوع بيانات صحيح.", "smw-statistics": "إحصائيات دلالية", "smw-statistics-cached": "إحصاءات دلالية (مخزنة)", "smw-statistics-entities-total": "الكيانات (إجمالي)", "smw-statistics-entities-total-info": "عدد صفوف تقديري للكيانات. يشمل الخصائص، المبادىء، أو أي تمثيل كائن مسجل آخر يتطلب تعيين معرف.", "smw-statistics-property-instance": "{{PLURAL:$1|قيمة|قيمتان|قيمة}} لخصائص (الإجمالي)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|خصيصة|خصيصتان|$1 خصائص}}]] (الإجمالي)", "smw-statistics-property-total-info": "إجمالي الخصائص المسجلة.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|خصيصة|خصيصتان|$1 خصائص}} (الإجمالي)", "smw-statistics-property-used": "{{PLURAL:$1|خاصية|خصائص}} (مستخدمة مع قيمة واحدة على الأقل)", "smw-statistics-property-page": "{{PLURAL:$1|خصيصة|خصيصتان|$1 خصائص}} (موصوفة بصفحات)", "smw-statistics-property-page-info": "عدد الخصائص التي لها صفحة ووصف مخصص.", "smw-statistics-property-type": "{{PLURAL:$1|خصيصة|خصيصتان|$1 خصائص}} (معيّن لها نوع بيانات)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|استعلام|استعلامات}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|استعلام|استعلامان|$1 استعلامات}}]] (مدمج، إجمالي)", "smw-statistics-query-format": "صيغة <code>$1</code>", "smw-statistics-query-size": "حجم الاستعلام", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|مفهوما|مفهومان|$1 مفهوما}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|مفهوما|مفهومان|$1 مفهوما}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|كيانا فرعيا|كيانان فرعيان|$1 كيانا فرعيا}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|كائن فرعي|كائنات فرعية}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|نوع بيانات|نوعي بيانات|$1 نوع بيانات}}]]", "smw-statistics-error-count": "{{PLURAL:$1|قيمة خاصية|قيم خواص}} ([[Special:ProcessingErrorList|{{PLURAL:$1|شرح غير مناسب|شروح غير مناسبة}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|قيمة خاصية|قيم خواص}} ({{PLURAL:$1|شرح مناسب|شروح مناسبة}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|كيان قديم|كيانات قديمة}}]", "smw-statistics-delete-count-info": "ينبغي التخلص من الكيانات التي تم وضع علامة عليها للإزالة بشكل منتظم باستخدام نصوص الصيانة المقدمة.", "smw_uri_doc": "ينفذ محلل URI العثور على [ $1 W3C TAG على httpRange-14].\nويضمن تسليم تمثيل RDF (للأجهزة) أو صفحة wiki (للبشر) اعتمادًا على الطلب.", "ask": "ب<PERSON><PERSON> دلالي", "smw-ask-help": "يحتوي هذا القسم على بعض الروابط للمساعدة في شرح كيفية استخدام صيغة <code>#ask</code> .\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages تحديد الصفحات] يصف كيفية تحديد الصفحات وبناء الشروط\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators عوامل البحث] تسرد عوامل البحث المتاحة بما في ذلك تلك الخاصة باستعلامات النطاق والبدلات\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information عرض المعلومات] يوضح استخدام عبارات الطباعة وخيارات التنسيق", "smw_ask_sortby": "الترتيب حس<PERSON> العمود (اختياري)", "smw_ask_ascorder": "تصاعدي", "smw_ask_descorder": "تنازلي", "smw-ask-order-rand": "عشوائي", "smw_ask_submit": "جِد نتائجًا", "smw_ask_editquery": "[تعديل الاستعلام]", "smw_add_sortcondition": "[أضف شرط ترتيب]", "smw-ask-sort-add-action": "أض<PERSON> شرط الفرز", "smw_ask_hidequery": "إخفاء استعلام (عرض مدمج)", "smw_ask_help": "مساعدة في الاستعلامات", "smw_ask_queryhead": "شرط", "smw_ask_printhead": "اختيار النسخة المطبوعة", "smw_ask_printdesc": "(أدخل اسم خصيصة واحدة في كل سطر)", "smw_ask_format_as": "أخرجه في صيغة:", "smw_ask_defaultformat": "المبدئية", "smw_ask_otheroptions": "خيا<PERSON><PERSON><PERSON> أخرى", "smw-ask-otheroptions-info": "يضمّ هذا القسم خيارات لتعديل شكل مخرجات البيانات. وصف المعاملات تمكن مطالعته بالتحويم فوق كل منها.", "smw-ask-otheroptions-collapsed-info": "انقر علامة + لمطالعة كل الخيارات المتاحة", "smw_ask_show_embed": "اعرض رموز التضمين", "smw_ask_hide_embed": "أخفِ رموز التضمين", "smw_ask_embed_instr": "لتضمين هذا الاستعلام في صفحة ويكي استخدم الرموز التالية.", "smw-ask-delete": "إزالة", "smw-ask-sorting": "الترتيب", "smw-ask-options": "خيارات", "smw-ask-options-sort": "خيارا<PERSON> الفرز", "smw-ask-format-options": "التنسيق والخيارات", "smw-ask-parameters": "وسائط", "smw-ask-search": "ب<PERSON><PERSON>", "smw-ask-debug": "تصحيح", "smw-ask-debug-desc": "يولد معلومات تصحيح الاستعلام", "smw-ask-no-cache": "تعطيل ذاكرة التخزين المؤقت للاستعلام", "smw-ask-no-cache-desc": "النتائج بدون ذاكرة التخزين المؤقت للاستعلام", "smw-ask-result": "النتيجة", "smw-ask-empty": "محو جميع الإدخالات", "smw-ask-download-link-desc": "تحميل النتائج الاستعلام بصيغة $1", "smw-ask-format": "الصيغة", "smw-ask-format-selection-help": "المساعدة في التنسيق المحدد: $1", "smw-ask-condition-change-info": "تم تغيير الحالة ويحتاج محرك البحث إلى إعادة تشغيل الاستعلام لإنتاج نتائج تتوافق مع المتطلبات الجديدة.", "smw-ask-input-assistance": "مساعدة الإدخال", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance مساعدة الإدخال] يتم توفيرها للطباعة، والفرز، وحقل الشرط، يتطلب حقل الشرط استخدام إحدى البادئات التالية:", "smw-ask-condition-input-assistance-property": "<code>p:</code> لجلب اقتراحات خاصية (على سبيل المثال <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> لجلب اقتراحات تصنيف", "smw-ask-condition-input-assistance-concept": "<code>con:</code> لجلب اقتراحات مفهوم", "smw-ask-format-change-info": "تم تعديل الصيغة وهي مطلوبة لتنفيذ الاستعلام مرة أخرى لمطابقة الوسائط وخيارات التصور الجديدة.", "smw-ask-format-export-info": "الصيغة المحددة هي صيغة التصدير التي ليس لها تمثيل مرئي وبالتالي يتم توفير النتائج فقط كتنزيل.", "smw-ask-query-search-info": "الاستعلام <code><nowiki>$1</nowiki></code> was تم الرد عليه من قبل {{PLURAL:$3|1=<code>$2</code> (من ذاكرة التخزين المؤقت)|<code>$2</code> (من ذاكرة التخزين المؤقت)|<code>$2</code>}} في $4 {{PLURAL:$4|ثانية|ثواني}}.", "smw-ask-extra-query-log": "سجل الاستعلام", "smw-ask-extra-other": "أ<PERSON><PERSON><PERSON>", "searchbyproperty": "البحث بالخصائص", "processingerrorlist": "قائمة معالجة الخطأ", "constrainterrorlist": "قائمة خطأ القيد", "propertylabelsimilarity": "تقرير تشابه تسمية الخواص", "missingredirectannotations": "تفتقد التعليقات التوضيحية للتحويلات", "smw-processingerrorlist-intro": "توفر القائمة التالية نظرة عامة حول [https://www.semantic-mediawiki.org/wiki/Processing_errors معالجة الأخطاء] التي ظهرت في اتصال ب[https://www.semantic-mediawiki.org/ ميدياويكي الدلالي]، من المستحسن مراقبة هذه القائمة على أساس منتظم، وتصحيح شروح القيمة غير الصالحة.", "smw-constrainterrorlist-intro": "توفر القائمة التالية نظرة عامة حول [https://www.semantic-mediawiki.org/wiki/Constraint_errors أخطاء القيد] التي ظهرت فيما يتعلق [https://www.semantic-mediawiki.org/ ميدياويكي الدلالي]، يُوصَى بمراقبة هذه القائمة بشكل منتظم وتصحيح التعليقات التوضيحية للقيم غير الصالحة.", "smw-missingredirects-intro": "سيعرض القسم التالي الصفحات التي تفتقد تعليقات [https://www.semantic-mediawiki.org/wiki/Redirects تحويلات] في ميدياويكي الدلالي (من خلال المقارنة مع المعلومات المخزنة في ميدياويكي) واستعادة تلك التعليقات إما يدويا  [https://www.semantic-mediawiki.org/wiki/Help:Purge تطهير] الصفحة أو تشغيل سكريبت الصيانة <code>rebuildData.php</code> (مع خيار <code>--redirects</code>).", "smw-missingredirects-list": "الصفحات التي تحتوي على تعليقات توضيحية مفقودة", "smw-missingredirects-list-intro": "عرض $1 {{PLURAL:$1|صفحة|صفحات}} مع عدم وجود توضيحات  للتحويلات.", "smw-missingredirects-noresult": "لم يتم العثور على تعليقات توضيحية مفقودة للتحويلات.", "smw_sbv_docu": "البحث عن كل الصفحات ذات خصيصة معينة بقيمتها.", "smw_sbv_novalue": "أدخل قيمة صحيحة للخصيصة، أو اعرض كل قيم الخصيصة \"$1\"", "smw_sbv_displayresultfuzzy": "كل الصفحات ذات الخصيصة \"$1\" بالقيمة \"$2\".\nبما أنه وجد عدد قليل من النتائج فإن القيم المقاربة معروضة كذلك.", "smw_sbv_property": "الخصيصة:", "smw_sbv_value": "القيمة:", "smw_sbv_submit": "جِد نتائجًا", "browse": "تصفّح الويكي", "smw_browselink": "تصفّح الخصائص", "smw_browse_article": "أد<PERSON>ل اسم صفحة لبدء التصفح منها.", "smw_browse_go": "اذهب", "smw_browse_show_incoming": "عرض الخصائص الواردة", "smw_browse_hide_incoming": "إخفاء الخصائص الواردة", "smw_browse_no_outgoing": "هذه الصفحة ليست بها خصائص.", "smw_browse_no_incoming": "لا خصائص تربط إلى هذه الصفحة.", "smw-browse-from-backend": "يجري حاليًا استرجاع المعلومات من الواجهة الخلفية.", "smw-browse-intro": "توفر هذه الصفحة تفاصيل حول نموذج موضوع أو كيان، يُرجَى إدخال اسم كائن ليتم تفتيشه.", "smw-browse-invalid-subject": "التحقق من هذا الموضوع عاد مع الخطأ \"$1\" .", "smw-browse-api-subject-serialization-invalid": "هذا الموضوع به صيغة تسلسل غير صالحة.", "smw-browse-js-disabled": "يُشتبه في أن JavaScript معطل أو غير متاح. نوصي باستخدام متصفح يدعمه. تتم مناقشة الخيارات الأخرى في صفحة معلمات تكوين [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code> ].", "smw-browse-show-group": "عرض المجموعات", "smw-browse-hide-group": "إخفاء المجموعات", "smw-noscript": "تتطلب هذه الصفحة أو الإجراء تشغيل JavaScript. يُرجى تمكين JavaScript في متصفحك أو استخدام متصفح يدعمه، حتى يمكن توفير الوظيفة حسب الطلب. لمزيد من المساعدة، يُرجى إلقاء نظرة على صفحة المساعدة [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript].", "smw_inverse_label_default": "$1 من", "smw_inverse_label_property": "عنوان الخصيصة العكسية", "pageproperty": "البحث في خصائص صفحة", "pendingtasklist": "قائمة المهام المعلقة", "smw_pp_docu": "إما أن تدخل صفحة أو خاصية، أو خاصية فقط لاسترداد جميع القيم المحددة.", "smw_pp_from": "من صفحة:", "smw_pp_type": "الخصيصة:", "smw_pp_submit": "جِد نتائجًا", "smw-prev": "{{PLURAL:$1|$1}} السابقة", "smw-next": "{{PLURAL:$1|$1}} التالية", "smw_result_prev": "السابق", "smw_result_next": "اللاحق", "smw_result_results": "النتائج", "smw_result_noresults": "لا توجد نتائج.", "smwadmin": "لوحة المعلومات (ميدياويكي الدلالي)", "smw-admin-statistics-job-title": "إحصاءات العمل", "smw-admin-statistics-job-docu": "تعرض إحصائيات الوظائف معلومات حول وظائف Semantic MediaWiki المجدولة التي لم يتم تنفيذها بعد. قد يكون عدد الوظائف غير دقيق إلى حد ما أو يحتوي على محاولات فاشلة. يرجى الرجوع إلى [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manual] لمزيد من المعلومات.", "smw-admin-statistics-querycache-title": "استعلام الذاكرة المخبأة", "smw-admin-statistics-querycache-disabled": "لم يتم تفعيل [https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] على هذه الويكي، وبالتالي لا تتوفر أي إحصائيات.", "smw-admin-statistics-querycache-legend": "إحصائيات ذاكرة التخزين المؤقت هي احتواء البيانات التراكمية المؤقتة وكذلك البيانات المشتقة بما في ذلك:  \n* \"يخطئ\" كمجموع محاولات لاسترداد البيانات من ذاكرة التخزين المؤقت مع ردود غير قابلة للتحقيق، مما اضطر مستودع مباشر (دب، أو مخزن ثلاثي إلخ) للاسترجاع  \n* \"حذف\" الإجمالي كمية عمليات إخلاء ذاكرة التخزين المؤقت (إما من خلال التطهير أو الاستعلام التبعية)  \n*\" الزيارات \"يحتوي على كمية استرجاع ذاكرة التخزين المؤقت من إما جزءا لا يتجزأ من (الاستعلامات المسماه من داخل صفحة ويكي) أو غير المضمنة (إذا تم تمكينها، يتم طلبها بواسطة صفحات مثل المصادر Special:Ask أو API)  \n* \"ميديانرتريفالريسبونزيتيمي\" هي قيمة توجيهية لوقت الاستجابة الوسيط (في ثانية) لطلبات الاسترجاع المخزنة مؤقتا وغير المخزنة مؤقتا خلال الفترة الزمنية لعملية التجميع  \n* \"نوكاش\" يشير إلى كمية أي طلبات محاولة (الحد = 0 استعلام، خيار 'لا توجد ذاكرة تخزين مؤقت' إلخ) لاسترداد النتائج من ذاكرة التخزين المؤقت", "smw-admin-statistics-section-explain": "يوفر القسم إحصائيات إضافية للإداريين.", "smw-admin-statistics-semanticdata-overview": "نظرة عامة", "smw-admin-permission-missing": "الوصول إلى هذه الصفحة قد تم حظره بسبب صلاحيات مفقودة; يُرجَى الرجوع إلى صفحة المساعدة [https://www.semantic-mediawiki.org/wiki/Help:Permissions صلاحيات] لمزيد من التفاصيل حول الإعدادات اللازمة.", "smw-admin-setupsuccess": "تم تنصيب محرك تخزين", "smw_smwadmin_return": "ارجع إلى $1", "smw_smwadmin_updatestarted": "إجراء تحديث البيانات الدلالية قد بدأ.\nكل البيانات المخزنة ستجري إعادة بنائها أو إصلاحها حسب الحاجة.\nيمكنك متابعة تطور التحديث على هذه الصفحة الخاصة.", "smw_smwadmin_updatenotstarted": "توجد بالفعل عملية تحديث جارية.\nلا تبدأ غيرها.", "smw_smwadmin_updatestopped": "كل عمليات التحديث الجارية تم إيقافها", "smw_smwadmin_updatenotstopped": "لوقف عملية التحديث الجارية يجب عليك التأشير في الصندوق للتوكيد.", "smw-admin-docu": "هذه الصفحة الخاصة تساعدك خلال تنصيب وترقية وصيانة واستخدام < href=\"https://www.semantic-mediawiki.org\">ميدياويكي الدلالية</a>\nوتوفر أيضًا المزيد من الوظائف والمهام الإدارية وكذلك إحصاءات.\nتذكر أن تحفظ احتياطيا البيانات القيمة قبل إجراء وظائف إدارية", "smw-admin-environment": "بيئة البرمجيات", "smw-admin-db": "إعداد قاعدة البيانات", "smw-admin-db-preparation": "تهيئة الجدول مستمرة وربما تستغرق ثانية قبل عرض نتائج بانتظار الحجم والتحسينات الممكنة للجدول.", "smw-admin-dbdocu": "يتطلب ميدياويكي الدلالي بنية قاعدة البيانات الخاصة به (وهي مستقلة عن ميدياويكي وبالتالي لا تؤثر على بقية تثبيت ميدياويكي) من أجل تخزين البيانات الدلالية. \nيمكن تنفيذ وظيفة الإعداد هذه عدة مرات دون إلحاق أي ضرر، ولكنها مطلوبة مرة واحدة فقط عند التثبيت أو الترقية.", "smw-admin-permissionswarn": "إذا فشلت العملية بأخطاء SQL، مستخدم قاعدة البيانات المستخدمة من قبل الويكي الخاص بك (تحقق من ملف \"LocalSettings.php\" الخاص بك) ربما ليست لديه صلاحيات كافية;\nامنح هذا المستخدم صلاحيات إضافية لإنشاء الجداول وحذفها أو إدخال تسجيل الدخول لجذر قاعدة البيانات مؤقتا في ملف \"LocalSettings.php\"، أو استخدام كود الصيانة <code>setupStore.php</code>، والذي يمكنه استخدام بيانات الاعتماد من إداري.", "smw-admin-dbbutton": "تهيئة أو ترقية الجداول", "smw-admin-announce": "أعلن عن هذه الويكي", "smw-admin-announce-text": "إذا الويكي الخاص بك عامًا، يمكنك تسجيله على <a href=\"https://wikiapiary.com\">WikiApiary</a>،\nويكي تتبع الويكي.", "smw-admin-deprecation-notice-title": "إشعارات الانتقاص", "smw-admin-deprecation-notice-docu": "القسم التالي يحتوي على الإعدادات التي تم إيقافها أو إزالتها ولكن تم الكشف عنها لتكون نشطة في هذا الويكي، من المتوقع أن أي إصدار مستقبلي سيزيل الدعم لهذه التكوينات.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> تم إهماله وستتم إزالته في $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> سيزيل (أو يستبدل) {{PLURAL:$2|الخيار التالي|الخيارات التالية}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> يتم إهماله وستتم إزالته في $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> تم استبداله ب<code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> أُستبدل بـ $2", "smw-admin-deprecation-notice-config-replacement-option": "{{PLURAL:$2|خيار|خيارات}} <code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> :", "smw-admin-deprecation-notice-config-replacement-option-list": "يتم استبدال <code>$1</code> بـ<code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> أُزيل في $2", "smw-admin-deprecation-notice-title-notice": "التغييرات القادمة", "smw-admin-deprecation-notice-title-notice-explanation": "تم اكتشاف الإعدادات التالية ليتم استخدامها على هذا الويكي ومن المقرر إزالتها أو تغييرها في إصدار مستقبلي.", "smw-admin-deprecation-notice-title-replacement": "الإعدادات التي تم استبدالها أو إعادة تسميتها", "smw-admin-deprecation-notice-title-replacement-explanation": "يحتوي القسم التالي على الإعدادات التي تمت إعادة تسميتها أو تعديلها بطريقة أخرى، ويُوصَى بتحديث اسمها أو تنسيقها فورا.", "smw-admin-deprecation-notice-title-removal": "تمت إزالة الإعدادات", "smw-admin-deprecation-notice-title-removal-explanation": "تمت إزالة الإعدادات المدرجة في إصدار سابق ولكن تم اكتشافها حتى الآن لاستخدامها على هذا الويكي.", "smw-admin-deprecation-notice-section-legend": "عنوان تفسيري", "smw-smwadmin-refresh-title": "إصلاح وتحديث البيانات", "smw_smwadmin_datarefresh": "إعادة بناء البيانات", "smw_smwadmin_datarefreshdocu": "من الممكن استرجاع كل بيانات ميدياويكي الدلالية بناء على المحتويات الحالية للويكي.\nقد يكون هذا مفيدا لإصلاح البيانات المعطوبة أو لتحديث البيانات في حال تغيّرت الصيغة الداخلية بسبب ترقية برمجية.\nالتحديث يتم إجراؤه صفحة بصفحة ولن يتم لحظيا.\nالتالي يعرض ما إذا كان التحديث يجري و يسمح لك ببدء أو وقف التحديثات (إلا لو كانت هذه الوظيفة قد عطّلها مدير الموقع).", "smw_smwadmin_datarefreshprogress": "<strong>يوجد تحديث جارٍ بالفعل.</strong>\nمن الطبيعي أن تتقدم عمليات التحديث ببطء بما أنها تجري على البيانات بكميات صغيرة كل مرة يستخدم فيها شخص ما الويكي.\nلإتمام التحديث سريعا يمكنك تشغيل سكربت صيانة ميدياويكي <code>runJobs.php</code> (استخدم الخيار <code>--maxjobs 1000</code> لتحديد عدد التحديثات المجراة في كل دفعة).\nالتقدم التقديري للتحديث الحالي:", "smw_smwadmin_datarefreshbutton": "إعادة بناء بيانات الجدول الزمني", "smw_smwadmin_datarefreshstop": "أوقف هذا التحديث", "smw_smwadmin_datarefreshstopconfirm": "نعم، أنا {{GENDER:$1|متأكد|متأكدة}}.", "smw-admin-job-scheduler-note": "يتم تنفيذ المهام (التي تم تمكينها) في هذا القسم من خلال قائمة انتظار المهام لتجنب حالات توقف تام أثناء تنفيذها، [https://www.mediawiki.org/wiki/Manual:Job_queue قائمة انتظار المهام] هي المسئولة عن المعالجة ومن الأهمية بمكان أن سكريبت الصيانة <code>runJobs.php</code> لديه سعة مناسبة (راجع أيضا وسيط التكوين <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "التخلص من الكيانات التي عفا عليها الزمن", "smw-admin-outdateddisposal-intro": "بعض الأنشطة (تغيير نوع الخاصية، أو إزالة صفحات الويكي، أو تصحيح قيم خاطئة) ينتج في [https://www.semantic-mediawiki.org/wiki/Outdated_entities الكيانات التي عفا عليها الزمن] وتُقترَح إزالتها بشكل دوري لتحرير مساحة الجدول المرتبط.", "smw-admin-outdateddisposal-active": "لقد تمت جدولة مهمة التخلص من الكيانات القديمة.", "smw-admin-outdateddisposal-button": "التخلص من الجدول الزمني", "smw-admin-feature-disabled": "تم تعطيل هذه الميزة في هذا الويكي; يرجى الرجوع إلى صفحة المساعدة الإعدادات <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">settings</a> أو اتصل بإداري النظام.", "smw-admin-propertystatistics-title": "إعادة بناء إحصاءات الخاصية", "smw-admin-propertystatistics-intro": "يعيد بناء إحصائيات استخدام الخاصية بأكملها بما فيها التحديثات ويصحح [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count عدد استخدام] الخصائص.", "smw-admin-propertystatistics-active": "تمت جدولة مهمة إعادة بناء إحصائيات الخاصية.", "smw-admin-propertystatistics-button": "إعادة بناء إحصاءات الجدول الزمني", "smw-admin-fulltext-title": "بحث إعادة بناء النص الكامل", "smw-admin-fulltext-intro": "إعادة إنشاء فهرس البحث من جداول الخصائص مع تمكين [https://www.semantic-mediawiki.org/wiki/Full-text البحث عن النص الكامل]، التغييرات التي تطرأ على قواعد الفهرس (ستوبوردز المتغيرة، ستيمر الجديدة إلخ) و/أو الجدول المضاف أو المتبدل حديثا يتطلب تشغيل هذه المهمة مرة أخرى.", "smw-admin-fulltext-active": "تمت جدولة مهمة إعادة بناء البحث عن نص كامل.", "smw-admin-fulltext-button": "إعادة بناء جدولة النص الكامل", "smw-admin-support": "الحصول على دعم", "smw-admin-supportdocu": "يتم توفير موارد مختلفة لمساعدتك في حالة حدوث مشاكل:", "smw-admin-installfile": "لو واجهتك مشاكل مع تنصيبتك فابدأ بمراجعة الإرشادات في https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">INSTALL file</a> and the <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">صفحة التثبيت</a>.", "smw-admin-smwhomepage": "وثائق الاستخدام الكاملة لميدياويكي الدلالية موجودة في <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "الأخطاء يمكن الإبلاغ عنها في <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues/\"متعقب المشاكل</a>، ، توفر صفحة <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">الإبلاغ عن الأخطاء</a> بعض الإرشادات حول كيفية كتابة تقرير مشكلة فعال.", "smw-admin-questions": "إذا كانت لديك أسئلة أو اقتراحات أخرى، فقم بالانضمام إلى المناقشة في <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">قائمة بريد مستخدم</a> ميدياويكي الدلالي.", "smw-admin-other-functions": "وظائف أ<PERSON>ى", "smw-admin-statistics-extra": "الدوال الإحصائية", "smw-admin-statistics": "إحصاءات", "smw-admin-supplementary-section-title": "وظائف إضافية", "smw-admin-supplementary-section-subtitle": "الوظائف الأساسية المدعومة", "smw-admin-supplementary-section-intro": "يوفر هذا القسم وظائف إضافية خارج نطاقأنشطة الصيانة ومن الممكن أن تكون بعض الوظائف المسردة (انظر [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions التوثيق]) مقيدة أو غير متاحة وبالتالي لا يمكن الوصول إليها في هذا الويكي.", "smw-admin-supplementary-settings-title": "إعدادات التكوين", "smw-admin-supplementary-settings-intro": "<u>$1</u> يسرد الإعدادات المستخدمة في ميدياويكي الدلالي", "smw-admin-main-title": "ميدياويكي الدلالي » $1", "smw-admin-supplementary-operational-statistics-title": "الإحصاءات التشغيلية", "smw-admin-supplementary-operational-statistics-short-title": "الإحصاءات التشغيلية", "smw-admin-supplementary-operational-statistics-intro": "يعرض مجموعة ممتدة من <u>$1</u>", "smw-admin-supplementary-idlookup-title": "البحث عن الكيانات والتخلص منها", "smw-admin-supplementary-idlookup-short-title": "البحث عن الكيانات والتخلص منها", "smw-admin-supplementary-idlookup-intro": "يدعم وظيفة <u>$1</u> بسيطة", "smw-admin-supplementary-duplookup-title": "بحث كيانات مكررة", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> للعثور على الكيانات المصنفة على أنها مكررة في مصفوفة الجداول المختارة", "smw-admin-supplementary-duplookup-docu": "تعرض هذه الصفحة مدخلات من الجداول المحددة التي تم تصنيفها على أنها [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities مكررة]، يجب أن تحدث إدخالات مكررة (على سبيل المثال) فقط في حالات نادرة من المحتمل حدوثها بسبب تحديث تم إنهاؤه أو تضمين استرجاع غير ناجح.", "smw-admin-supplementary-operational-statistics-cache-title": "إحصائيات ذاكرة التخزين المؤقت", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> يعرض مجموعة محددة من الإحصائيات متعلقة بذاكرة التخزين المؤقت", "smw-admin-supplementary-operational-table-statistics-title": "إحصائيات الجدول", "smw-admin-supplementary-operational-table-statistics-short-title": "إحصائيات الجدول", "smw-admin-supplementary-operational-table-statistics-intro": "ينشئ <u>$1</u> لمجموعة محددة من الجداول", "smw-admin-supplementary-operational-table-statistics-explain": "يحتوي هذا القسم على إحصائيات محددة للجدول لمساعدة الإداريين وقيم البيانات على اتخاذ قرارات مستنيرة بشأن هذه الحالة لنهاية العملية ومحرك التخزين.", "smw-admin-supplementary-operational-table-statistics-legend": "توضح وسيلة الإيضاح بعض المفاتيح المستخدمة لإحصائيات الجدول وتتضمن:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> إجمالي عدد الصفوف في الجدول", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> آخر معرف مستخدم حاليًا\n* <code>duplicate_count</code> عدد التكرارات الموجودة في جدول المعرفات (انظر أيضًا [[Special:SemanticMediaWiki/duplicate-lookup|بحث عن الكيانات المكررة]])\n* <code>rows.rev_count</code> عدد الصفوف التي تم تعيين revision_id لها مما يشير إلى ارتباط مباشر بصفحة ويكي\n* <code>rows.smw_namespace_group_by_count</code> عدد الصفوف المجمعة لمساحات الأسماء المستخدمة في الجدول\n* <code>rows.smw_proptable_hash.query_match_count</code> عدد الكائنات الفرعية للاستعلام مع مرجع جدول مطابق\n* <code>rows.smw_proptable_hash.query_null_count</code> عدد الكائنات الفرعية للاستعلام بدون مرجع جدول (مرجع عائم غير مرتبط)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> نسبة المصطلحات الفريدة (يشير معدل النسبة المئوية المنخفض إلى أن المصطلحات المتكررة تشغل محتوى الجدول والفهرس)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> عدد المصطلحات التي تظهر مرة واحدة فقط\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> عدد المصطلحات التي تظهر أكثر من مرة", "smw-admin-supplementary-elastic-title": "إلاستيك سيرش", "smw-admin-supplementary-elastic-version-info": "معلومات الإصدار", "smw-admin-supplementary-elastic-section-subtitle": "إلاستيك سيرش", "smw-admin-supplementary-elastic-intro": "<u>$1</u> يعرض تفاصيل عن إعدادات وإحصائيات الفهرس", "smw-admin-supplementary-elastic-docu": "تحتوي هذه الصفحة على معلومات حول الإعدادات والتعيينات والصحة وإحصائيات الفهرس المتعلقة بمجموعة البحث المرن المرتبطة بميدياويكي الدلالي و[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "الوظائف المدعومة", "smw-admin-supplementary-elastic-settings-title": "إعدادات", "smw-admin-supplementary-elastic-settings-intro": "تُستخدَم <u>$1</u> بواسطة البحث المرن لإدارة فهارس ميدياكيكي الدلالي", "smw-admin-supplementary-elastic-mappings-title": "التعيينات", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> لسرد الفهارس وتعيينات الحقل", "smw-admin-supplementary-elastic-mappings-docu": "تحتوي هذه الصفحة على تفاصيل تعيين الحقول المستخدمة مع الفهرس الحالي، من المستحسن مراقبة الخرائط المرتبطة بـ<code>index.mapping.total_fields.limit</code> (يحد<PERSON> الحد الأقصى المسموح به لعدد الحقول في الفهرس).", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code> يشير إلى عدد حقول القلب المفهرسة بينما <code>nested_fields</code> يشير إلى عدد تراكمي للحقول الإضافية المضافة إلى حقل قلب من أجل دعم أنماط بحث هيكلي محددة.", "smw-admin-supplementary-elastic-mappings-summary": "الملخص", "smw-admin-supplementary-elastic-mappings-fields": "خريطة الحقول", "smw-admin-supplementary-elastic-nodes-title": "العقد", "smw-admin-supplementary-elastic-nodes-intro": "يعرض <u>$1</u> إحصائيات العقدة", "smw-admin-supplementary-elastic-indices-title": "الفهارس", "smw-admin-supplementary-elastic-indices-intro": "يقدم <u>$1</u> نظرة عامة على الفهارس المتاحة وإحصاءاتها", "smw-admin-supplementary-elastic-statistics-title": "الإحصاءات", "smw-admin-supplementary-elastic-statistics-intro": "يعرض <u>$1</u> مستوى الفهرس", "smw-admin-supplementary-elastic-statistics-docu": "تقدم هذه الصفحة إحصاءات حول إحصائيات الفهارس الخاصة بالعمليات المختلفة التي تحدث على مستوى الفهرس، حيث يتم تجميع الإحصائيات المرتجعة مع المجموعات التمهيدية والإجمالية، تحتوي صفحة [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html صفحة المساعدة] على وصف مفصل لإحصاءات الفهارس المتاحة.", "smw-admin-supplementary-elastic-status-replication": "حالة النسخ المتماثل", "smw-admin-supplementary-elastic-status-last-active-replication": "آخر عملية نسخ متماثل نشطة: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "فاصل التحديث: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "تراكم وظيفة الاسترداد: $1 (تقدير)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "تراكم مهام الاستيعاب (الملف): $1 (تقدير)", "smw-admin-supplementary-elastic-status-rebuild-lock": "تم قفل النسخ المتماثل: $1 (إعادة البناء قيد التقدم)", "smw-admin-supplementary-elastic-status-replication-monitoring": "مراقبة التكرار (نشط): $1", "smw-admin-supplementary-elastic-replication-header-title": "حالة النسخ المتماثل", "smw-admin-supplementary-elastic-replication-function-title": "استنساخ", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> عرض معلومات حول النسخ المتماثل الفاشلة", "smw-admin-supplementary-elastic-replication-docu": "توفر هذه الصفحة معلومات حول [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring حالة النسخ المتماثل] للكيانات التي تم الإبلاغ عن احتوائها على مشكلات في مجموعة إلاستيك سيرش، يُوصَى بمراجعة الكيانات المدرجة في القائمة وتطهير المحتوى من أجل تأكيد أنها كانت مشكلة مؤقتة.", "smw-admin-supplementary-elastic-replication-files-docu": "تجدر الإشارة إلى أنه بالنسبة لقائمة الملفات، يلزم تنفيذ المهمة [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion استيعاب الملف] أولا ويجب أن تنتهي من معالجتها.", "smw-admin-supplementary-elastic-replication-files": "ملفات", "smw-admin-supplementary-elastic-replication-pages": "صفحات", "smw-admin-supplementary-elastic-endpoints": "نقاط النهاية", "smw-admin-supplementary-elastic-config": "الضبط", "smw-admin-supplementary-elastic-no-connection": "'''يتعذر''' على الويكي حاليا إنشاء اتصال بمجموعة إلاستيك سيرش؛ يُرجَى الاتصال بإداري الويكي للتحقيق في المشكلة لأنه يعطل قدرة النظام على البحث والاستعلام.", "smw-list-count": "تحتوي القائمة على $1 {{PLURAL:$1|إدخال واحد|إدخالات}}.", "smw-property-label-uniqueness": "تمت مطابقة التصنيف \"$1\" مع تمثيل خاصية أخرى واحدة على الأقل، يُرجَى الرجوع إلى [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness صفحة المساعدة] حول كيفية حل هذه المشكلة.", "smw-property-label-similarity-title": "تقرير تشابه تسمية الخواص", "smw-property-label-similarity-intro": "<u>$1</u> يحسب تشابهات تسميات الخواص الموجودة", "smw-property-label-similarity-threshold": "عتبة:", "smw-property-label-similarity-type": "عرض معرف النوع", "smw-property-label-similarity-noresult": "لم يتم العثور على نتائج الخيارات المحددة.", "smw-property-label-similarity-docu": "تقارن هذه الصفحات [https://www.semantic-mediawiki.org/wiki/Property_similarity مسافة التشابه] (يجب عدم الخلط بينها وبين التشابه الدلالي أو المعجمي) بين تسميات الخواص والإبلاغ عنها إذا تجاوزت الحد، قد يساعد التقرير في تصفية الخصائص التي تحتوي على أخطاء إملائية أو مكافئة والتي تمثل نفس المفهوم (راجع الصفحة الخاصة [[Special:Properties|خواص]] للمساعدة في توضيح مفهوم واستخدام الخصائص المبلغ عنها)، يمكن ضبط الحد لتوسيع أو تضييق المسافة المستخدمة في المطابقة التقريبية، يتم استخدام <code>[[Property:$1|$1]]</code> لإعفاء الخصائص من التحليل.", "smw-admin-operational-statistics": "تحتوي هذه الصفحة على إحصاءات تشغيلية تم جمعها في أو من المهام ذات الصلة بميدياويكي الدلالي، يمكن الاطلاع على قائمة طويلة بالإحصاءات المحددة [[Special:Statistics|<b>هنا</b>]].", "smw_adminlinks_datastructure": "هيكل البيانات", "smw_adminlinks_displayingdata": "عرض البيانات", "smw_adminlinks_inlinequerieshelp": "المساعدة المضمنة للاستعلامات", "smw-page-indicator-usage-count": "[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count عدد الاستخدام] المقدر: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|مستخدم|نظام}} حدد خاصية", "smw-property-indicator-last-count-update": "عدد الاستخدام المقدر\nآخر تحديث: $1", "smw-concept-indicator-cache-update": "عدد ذاكرة التخزين المؤقت\nآخر تحديث: $1", "smw-createproperty-isproperty": "هذه خاصية من نوع $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1||القيمة المسموح بها لهذه الخصيصة هي|القيمتان المسموح بهما لهذه الخصيصة هما|القيم المسموح بها لهذه الخصيصة هي}}:", "smw-paramdesc-category-delim": "الفاصلة", "smw-paramdesc-category-template": "قالب يستخدم لتنسيق العناصر به", "smw-paramdesc-category-userparam": "معامل يمرر إلى القالب", "smw-info-par-message": "رسالة لعرضها", "smw-info-par-icon": "أيقونة تظهر، إما \"info\" أو \"warning\".", "prefs-smw": "ميدياويكي الدلالية", "prefs-general-options": "خيارات عامة", "prefs-extended-search-options": "ب<PERSON><PERSON> موسع", "prefs-ask-options": "البح<PERSON> الدلالي", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ ميدياويكي الدلالي] والإضافات المرتبطة توفر تفضيلات فردية لمجموعة من الميزات والوظائف المحددة، تتوفر قائمة بالإعدادات الفردية مع وصفها وخصائصها على [https://www.semantic-mediawiki.org/wiki/Help:User_preferences صفحة المساعدة].", "smw-prefs-ask-options-tooltip-display": "اعرض نص المعامل كبالون تلميحة #ask [[Special:Ask|query builder]]", "smw-prefs-ask-options-compact-view-basic": "تمكين العرض المضغوط الأساسي", "smw-prefs-help-ask-options-compact-view-basic": "في حالة التمكين ، تعرض مجموعة من الروابط في عرض خاص:سؤال المضغوط.", "smw-prefs-general-options-time-correction": "تمكين تصحيح الوقت لصفحات خاصة باستخدام تفضيل [[Special:Preferences#mw-prefsection-rendering|تعويض الوقت]] المحلي.", "smw-prefs-general-options-jobqueue-watchlist": "عرض قائمة انتظار قائمة مراقبة الوظائف في شريطي الشخصي", "smw-prefs-help-general-options-jobqueue-watchlist": "في حالة التمكين، يعرض [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist قائمة] انتظار المهام المحددة مع أحجام قوائم انتظارهم المقدرة.", "smw-prefs-general-options-disable-editpage-info": "تعطيل النص التمهيدي في صفحة التعديل", "smw-prefs-general-options-disable-search-info": "تعطيل معلومات دعم بناء الجملة في صفحة البحث القياسية", "smw-prefs-general-options-suggester-textinput": "تمكين مساعدة المدخلات لاقتراحات الكيانات الدلالية", "smw-prefs-help-general-options-suggester-textinput": "في حالة التمكين، يسمح باستخدام [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance مساعدة المدخلات] للبحث عن الخصائص والمفاهيم والتصنيفات من سياق المدخلات.", "smw-ui-tooltip-title-property": "الخصيصة", "smw-ui-tooltip-title-quantity": "تحويل الوحدة", "smw-ui-tooltip-title-info": "معلومات", "smw-ui-tooltip-title-service": "وصلات خدمية", "smw-ui-tooltip-title-warning": "تحذير", "smw-ui-tooltip-title-error": "خطأ", "smw-ui-tooltip-title-parameter": "معامل", "smw-ui-tooltip-title-event": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-note": "ملاحظة", "smw-ui-tooltip-title-legend": "م<PERSON><PERSON><PERSON><PERSON> الشكل", "smw-ui-tooltip-title-reference": "مرجع", "smw_unknowntype": "نوع هذه الخاصية \"$1\" غير صالح", "smw-concept-cache-text": "المفهوم لديه ما مجموعه $1 {{PLURAL:$1|صفحة|صفحات}}، وكان آخر تحديث $3، $2.", "smw_concept_header": "صفحات المفهوم \"$1\"", "smw_conceptarticlecount": "عرض {{PLURAL:$1||صفحة واحدة تنتمي|صفحتين تنتميان|$1 صفحات تنتمي|$1 صفحة تنتمي}} أسفله.", "smw-qp-empty-data": "لا يمكن عرض البيانات المطلوبة لأن بعض معايير الاختيار غير كافية.", "right-smw-admin": "الوصول للمهام الإدارية (ميدياويكي الدلالي)", "right-smw-patternedit": "تحرير الوصول إلى الحفاظ على التعبيرات النمطية والأنماط المسموح بها (ميدياويكي الدلالي)", "right-smw-pageedit": "تحرير الوصول للصفحات المشروحة <code>هل التحرير محمي</code> (ميدياويكي الدلالي)", "restriction-level-smw-pageedit": "محمية (المستخدمون المؤهلون فقط)", "action-smw-patternedit": "تعديل التعابير النمطية المستخدمة من قبل ميدياويكي الدلالي", "action-smw-pageedit": "تحرير الصفحات المشروحة ب<code>هل التحرير محمي</code> (ميدياويكي الدلالي)", "group-smwadministrator": "الإداريون (ميدياويكي الدلالي)", "group-smwadministrator-member": "{{GENDER:$1|إداري (ميدياويكي الدلالي)}}", "grouppage-smwadministrator": "{{ns:project}}:الإداريون (ميدياويكي الدلالي)", "group-smwcurator": "القيمون (ميدياويكي الدلالي)", "group-smwcurator-member": "{{GENDER:$1|قيمون (ميدياويكي الدلالي)}}", "grouppage-smwcurator": "{{ns:project}}:قيمو<PERSON> (ميدياويكي الدلالي)", "group-smweditor": "المحررون (ميدياويكي الدلالية)", "action-smw-admin": "الوصول للمهام الإدارية في ميدياويكي الدلالي", "action-smw-ruleedit": "تحرير صفحات القاعدة (ميدياويكي الدلالي)", "smw-property-predefined-default": "\"$1\" هي خاصية محددة مسبقا $2.", "smw-property-predefined-common": "هذه الخاصية سبق نشرها (معروفة أيضًا باسم [https://www.semantic-mediawiki.org/wiki/Help:Special_properties خاصية خاصة])، وتأتي مع امتيازات إدارية إضافية ولكن يمكن استخدامها تمامًا مثل أية [https://www.semantic-mediawiki.org/wiki/Property خاصية معرفة من قبل المستخدم].", "smw-property-predefined-ask": "\"$1\" هي خاصية معرفة مسبقًا تمثل المعلومات الوصفية (في شكل [https://www.semantic-mediawiki.org/wiki/Subobject كائن فرعي]) حول استعلامات فردية ويتم توفيرها من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-asksi": "\"$1\" هي خاصية معرفة مسبقًا تجمع عددًا من الشروط المستخدمة في استعلام ويتم توفيرها من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-askde": "\"$1\" هي خاصية معرفة مسبقًا تخبر بشأن عمق الاستعلام ويتم توفيرها من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-long-askde": "عبارة عن قيمة رقمية يتم حسابها على أساس تعشيش الاستعلام الفرعي وسلاسل الخاصية وعناصر الوصف المتاحة مع تنفيذ استعلام مقيد بواسطة تكوين وسيط <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>.", "smw-property-predefined-askpa": "\"$1\" هي خاصية محددة مسبقًا تصف الوسائط التي تؤثر على نتيجة الاستعلام ويتم توفيرها من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-long-askpa": "هو جزء من مجموعة الخصائص التي تحدد [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler الملف الشخصي للاستعلام].", "smw-sp-properties-docu": "تسرد هذه الصفحة [https://www.semantic-mediawiki.org/wiki/Property الخصائص] وعدد استخداماتها المتاحة لهذا الويكي، للحصول على إحصاءات حديثة فمن المستحسن أن يتم تشغيل سكريبت الصيانة [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics إحصاءات الخاصية] على أساس منتظم، للاطلاع على عرض مختلف; راجع الصفحات الخاصة [[Special:UnusedProperties|خصائص غير مستخدمة]] أو [[Special:WantedProperties|مطلوبة]].", "smw-sp-properties-cache-info": "تم استرداد البيانات المدرجة من [https://www.semantic-mediawiki.org/wiki/Caching النسخة المخبأة]، وكان آخر تحديث $1.", "smw-sp-properties-header-label": "قائمة خصائص", "smw-admin-settings-docu": "يعرض قائمة بكافة الإعدادات الافتراضية والمحلية ذات الصلة بيئة ميدياويكي الدلالية، للحصول على تفاصيل حول الإعدادات الفردية; يُرجىَ الرجوع إلى صفحة المساعدة [https://www.semantic-mediawiki.org/wiki/Help:Configuration تكوين].", "smw-sp-admin-settings-button": "توليد قائمة إعدادات", "smw-admin-idlookup-title": "اب<PERSON><PERSON> عن", "smw-admin-idlookup-docu": "يعرض هذا القسم التفاصيل الفنية عن كيان فردي (صفحة ويكي، كائن فرعي، خاصية، إلخ) في ميدياويكي الدلالي، يمكن أن يكون الإدخال معرفا رقميا أو قيمة سلسلة ليتطابق مع حقل البحث ذي الصلة، ومع ذلك، فإن أي مرجع معرف يتعلق بميدياويكي الدلالي وليس بصفحة أو معرف مراجعة ميدياويكي.", "smw-admin-iddispose-title": "تصرف", "smw-admin-iddispose-docu": "تجدر الإشارة إلى أن عملية التخلص غير مقيدة وستقوم بإزالة الكيان من محرك التخزين بجميع مراجعه في الجداول المعلقة، إذا تم تأكيدها، الرجاء تنفيذ هذه المهمة '''بحذر''' وفقط بعد الاطلاع على [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal التوثيق].", "smw-admin-iddispose-done": "تمت إزالة المعرف \"$1\" من الواجهة الخلفية للتخزين.", "smw-admin-iddispose-references": "المعرف \"$1\" {{PLURAL:$2|لا يحتوي على مرجع نشط|يحتوي على مرجع نشط واحد على الأقل}}:", "smw-admin-iddispose-references-multiple": "قائمة المطابقات مع سجل مرجع نشط واحد على الأقل.", "smw-admin-iddispose-no-references": "لم يتمكن البحث من مطابقة \"$1\" إلى إدخال جدول.", "smw-admin-idlookup-input": "بحث:", "smw-admin-objectid": "معرف:", "smw-admin-tab-general": "نظرة عامة", "smw-admin-tab-notices": "إشعارات انتقاص", "smw-admin-tab-maintenance": "الصيانة", "smw-admin-tab-supplement": "وظائف إضافية", "smw-admin-tab-registry": "سجل", "smw-admin-tab-alerts": "الإشعارات", "smw-admin-alerts-tab-deprecationnotices": "إشعارات الانتقاص", "smw-admin-alerts-tab-maintenancealerts": "إخطارات الصيانة", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "كيانات غير محدثة", "smw-admin-maintenancealerts-invalidentities-alert-title": "عنوان غير صالح", "smw-admin-deprecation-notice-section": "ميدياويكي الدلالي", "smw-admin-configutation-tab-settings": "إعدادات", "smw-admin-configutation-tab-namespaces": "النطاقات", "smw-admin-maintenance-tab-tasks": "مهام", "smw-admin-maintenance-tab-scripts": "سكريبتات الصيانة", "smw-admin-maintenance-no-description": "بدون وصف.", "smw-admin-maintenance-script-section-title": "قائمة سكريبتات الصيانة المتاحة", "smw-admin-maintenance-script-section-intro": "تتطلب سكريبتات الصيانة التالية إداريا والوصول إلى سطر الأوامر لتتمكن من تنفيذ السكريبتات المدرجة.", "smw-admin-maintenance-script-description-dumprdf": "تصدير RDF الثلاثي الحالي.", "smw-admin-maintenance-script-description-rebuildconceptcache": "يتم استخدام هذا السكريبت في إدارة مخابئ المفاهيم لميدياويكي الدلالي حيث يمكن إنشاء، وإزالة ، وتحديث ذاكرات مختارة.", "smw-admin-maintenance-script-description-rebuilddata": "يعيد إنشاء جميع البيانات الدلالية في قاعدة البيانات، عن طريق ركوب الدراجات من خلال جميع الصفحات التي قد تحتوي على بيانات دلالية.", "smw-admin-maintenance-script-description-rebuildelasticindex": "يعيد إنشاء فهرس إلاستيك سيرش (للتركيبات التي تستخدم <code>ElasticStore</code> فقط)، عن طريق ركوب الدراجات من خلال جميع الكيانات التي لديها بيانات دلالية.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "ابحث عن الكيانات المفقودة في إلاستيك سيرش (فقط للتركيبات التي تستخدم <code>ElasticStore</code>) وجدولة مهام التحديث المناسبة.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "إعادة بناء فهرس البحث النصي الكامل <code>SQLStore</code> (للتثبيتات التي تم تمكين الإعداد فيها).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "يعيد بناء إحصائيات الاستخدام لجميع كيانات الخواص.", "smw-admin-maintenance-script-description-removeduplicateentities": "يزيل الكيانات المكررة الموجودة في الجداول المحددة التي لا تحتوي على مراجع نشطة.", "smw-admin-maintenance-script-description-setupstore": "يقوم بإعداد خلفية التخزين المحددة في <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "يحدث الحقل <code>smw_sort</code> في <code>SQLStore</code> (وفقا لإعداد [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "يعب<PERSON> حقل <code>smw_hash</code> للصفوف التي تفتقد إلى القيمة.", "smw-admin-maintenance-script-description-purgeentitycache": "إزالة إدخالات ذاكرة التخزين المؤقت للكيانات المعروفة والبيانات المرتبطة بها.", "smw-admin-maintenance-script-description-updatequerydependencies": "تحديث الاستعلامات وتبعيات الاستعلام (راجع الإعداد [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-section-update": "تحديث السكربتات", "smw-admin-maintenance-script-section-rebuild": "إعادة بناء السكربتات", "smw-livepreview-loading": "جارٍ التحميل...", "smw-sp-searchbyproperty-description": "توفر هذه الصفحة [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces واجهة تصفح] بسيطة للعثور على كيانات تم وصفها بواسطة خاصية وقيمة مسماة، تتضمن واجهات البحث الأخرى المتاحة [[Special:PageProperty|صفحة بحث الخصائص]]، و[[Special:Ask|اطلب منشئ استعلام]].", "smw-sp-searchbyproperty-resultlist-header": "قائمة النتائج", "smw-sp-searchbyproperty-nonvaluequery": "قائمة القيم التي لديها الخاصية المعينة \"$1\".", "smw-sp-searchbyproperty-valuequery": "قائمة الصفحات التي تحتوي على الخاصية \"$1\" مع القيمة \"$2\" المشروحة.", "smw-datavalue-number-textnotallowed": "\"$1\" لا يمكن أن تُسنَد إلى نوع العدد المعلن بقيمة $2.", "smw-datavalue-number-nullnotallowed": "\"$1\" عاد مع \"NULL\" غير المسموح به كرقم.", "smw-editpage-annotation-enabled": "تدعم هذه الصفحة التعليقات التوضيحية الدلالية في النص (على سبيل المثال <nowiki>\"[[Is specified as::World Heritage Site]]\"</nowiki>) لإنشاء محتوى منظم وقابل للاستعلام من قبل ميدياويكي الدلالي، للحصول على وصف شامل حول كيفية استخدام التعليقات التوضيحية أو وظيفة محلل #ask; يُرجَى إلقاء نظرة على صفحات المساعدة [https://www.semantic-mediawiki.org/wiki/Help:Getting_started الخطوات الأولى] أو [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation في النص annotation] أو [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries استعلامات مضمنة].", "smw-editpage-annotation-disabled": "لم يتم تمكين هذه الصفحة للتعليقات التوضيحية في النص الدلالي بسبب قيود النطاق، يمكن العثور على التفاصيل حول كيفية تمكين النطاق في صفحة المساعدة الخاصة ب[https://www.semantic-mediawiki.org/wiki/Help:Configuration الإعدادات].", "smw-editpage-property-annotation-enabled": "يمكن تمديد هذه الخاصية باستخدام شروح دلالية لتحديد نوع البيانات (على سبيل المثال <nowiki>\"[[Has type::Page]]\"</nowiki>) أو غيرها من الإعلانات الداعمة (على سبيل المثال <nowiki>\"[[Subproperty of::dc:date]]\"</nowiki>)،  للحصول على وصف حول كيفية زيادة هذه الصفحة; راجع صفحة المساعدة [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration declaration of a property] or the [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes قائمة أنواع البيانات المتوفرة].", "smw-editpage-property-annotation-disabled": "لا يمكن تمديد هذه الخاصية بتعليق توضيحي لنوع البيانات (على سبيل المثال <nowiki>\"[[Has type::Page]]\"</nowiki>) كما هو محدد بالفعل (انظر  صفحة المساعدة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties خصائص خاصة] لمزيد من المعلومات).", "smw-editpage-concept-annotation-enabled": "يمكن تمديد هذا المفهوم باستخدام محلل دالة #concept، للحصول على وصف حول كيفية استخدام #concept; راجع صفحة المساعدة [https://www.semantic-mediawiki.org/wiki/Help:Concepts مفهوم].", "smw-search-syntax-support": "مدخلات البحث يدعم استخدام [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search بناء جملة الاستعلام] الدلالي للمساعدة في مطابقة النتائج باستخدام ميدياويكي الدلالي.", "smw-search-input-assistance": "يتم تمكين [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance مساعد المدخلات] أيضا لتسهيل الاختيار المسبق للخصائص والتصنيفات المتاحة.", "smw-search-help-intro": "سيشير إدخال <code><nowiki>[[ ... ]]</nowiki></code> iإلى معالج الإدخال لاستخدام واجهة بحث الخلفية لميدياكي الدلالي، تجدر الإشارة إلى أن الجمع بين <code><nowiki>[[ ... ]]</nowiki></code> مع بحث نصي غير منظم مثل <code><nowiki>[[ ... ]] أو لوريم إيبسوم</nowiki></code> غير مدعوم.", "smw-search-help-structured": "عمليات بحث منظمة:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> ([https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context كسياق تمت تصفيته])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (مع [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context سياق استعلام])", "smw-search-help-proximity": "عمليات البحث عن قرب (خاصية غير معروفة، متوفرة '''فقط''' للجهات الخلفية التي توفر تكامل بحث عن نص كامل):\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code> (ابحث في كل الوثائق عن \"lorem\" و\"ipsum\" التي تمت فهرستها)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (طابق \"لوريم إيبسوم\" كعبارة)", "smw-search-help-ask": "ستوضح الروابط التالية كيفية استخدام صيغة <code>#ask</code> .\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages تحديد الصفحات] يصف كيفية تحديد الصفحات وبناء الشروط\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators عوامل البحث] تسرد عوامل البحث المتاحة بما في ذلك تلك الخاصة باستعلامات النطاق والبدلات", "smw-search-input": "المدخلات والبحث", "smw-search-help-input-assistance": "يتم توفير [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance مساعدة الإدخال] لحقل الإدخال ويتطلب استخدام إحدى البادئات التالية:\n\n*<code>p:</code> لتمكين اقتراحات الخاصية (مثل <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> لتمكين اقتراحات التصنيفات\n\n*<code>con:</code> لتمكين اقتراحات المفهوم", "smw-search-syntax": "بناء الجملة", "smw-search-profile": "موسع", "smw-search-profile-tooltip": "وظائف البحث فيما يتعلق بميدياويكي الدلالي", "smw-search-profile-sort-best": "أفضل مطابقة", "smw-search-profile-sort-recent": "الأحدث", "smw-search-profile-sort-title": "العنوان", "smw-search-profile-extended-help-intro": "[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile الملف الشخصي الموسع] في خاص:بحث توفر الوصول إلى وظائف البحث الخاصة بميدياويكي الدلالي والواجهة الخلفية لاستعلامه المدعوم.", "smw-search-profile-extended-help-sort": "يحد<PERSON> تفضيل الفرز لعرض النتيجة به:", "smw-search-profile-extended-help-sort-title": "* \"العنوان\" باستخدام عنوان الصفحة (أو عنوان العرض) كمعايير فرز", "smw-search-profile-extended-help-sort-recent": "* ستعرض \"الأحدث\" أحدث الكيانات المعدلة أولا (سيتم منع الكيانات الفرعية لأن هذه الكيانات لا يتم تعليقها على [[Property:Modification date|تاريخ التعديل]])", "smw-search-profile-extended-help-sort-best": "*\"أفضل تطابق\" سيتم ترتيب الكيانات حسب [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy الصلة] استنادا إلى النتائج التي توفرها الواجهة الخلفية", "smw-search-profile-extended-help-form": "يتم توفير النماذج (إذا تمت صيانتها) لتتطابق مع حالات استخدام محددة عن طريق عرض حقول خصائص وقيم مختلفة لتضييق نطاق عملية الإدخال وتسهيل عملية متابعة طلب البحث للمستخدمين. (راجع $1)", "smw-search-profile-extended-help-namespace": "سيتم إخفاء صندوق اختيار النطاق بمجرد تحديد نموذج ولكن يمكن جعله مرئيا بمساعدة الزر \"إظهار/إخفاء\".", "smw-search-profile-extended-help-search-syntax": "يدعم حقل إدخال البحث استخدام الصيغة <code>#ask</code> لتعريف سياق بحث ميدياويكي دلالي معين، تشمل التعبيرات المفيدة ما يلي:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> للعثور على أي شيء يحتوي على \"...\" ويكون مفيدا بشكل خاص عندما يكون سياق البحث أو الخصائص المعنية غير معروفة (على سبيل المثال <code>in:(lorem && ipsum)</code> تعادل <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> للعثور على أي شيء يحتوي على \"...\" بنفس الترتيب بالضبط", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> لمطابقة أي كيان مع خاصية \"...\" (على سبيل المثال <code>has:(Foo && Bar)</code> تعادل <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> لعدم تطابق أي كيان يتضمن \"...\"", "smw-search-profile-extended-help-search-syntax-prefix": "* تكون البادئات المخصصة الإضافية متاحة ومحددة مثل: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* يتم حجز بعض التعبيرات مثل: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''بعض العمليات المسردة ليست مفيدة إلا فيما يتعلق بفهرس النص الكامل الممكَّن أو ElasticStore.''", "smw-search-profile-extended-help-query": "تم استخدام <code><nowiki>$1</nowiki></code> كاستعلام.", "smw-search-profile-extended-help-query-link": "لمزيد من التفاصيل؛ يُرجَى استخدام $1.", "smw-search-profile-extended-help-find-forms": "النماذج المتوفرة", "smw-search-profile-extended-section-sort": "الترتيب حسب", "smw-search-profile-extended-section-form": "نماذج", "smw-search-profile-extended-section-search-syntax": "مدخلة البحث", "smw-search-profile-extended-section-namespace": "النطاق", "smw-search-profile-extended-section-query": "الاستعلام", "smw-search-profile-link-caption-query": "منشئ الاستعلام", "smw-search-show": "<PERSON><PERSON><PERSON>", "smw-search-hide": "إخفاء", "log-name-smw": "سجل ميدياويكي الدلالي", "log-show-hide-smw": "$1 سجل ميدياويكي الدلالي", "logeventslist-smw-log": "سجل ميدياويكي الدلالي", "log-description-smw": "أنشطة ل[https://www.semantic-mediawiki.org/wiki/Help:Logging enabled أنواع الأحداث] التي تم الإبلاغ عنها من قبل ميدياويكي الدلالي ومكوناته.", "logentry-smw-maintenance": "أحداث الصيانة المتعلقة المنبعثة من ميدياويكي الدلالي", "smw-datavalue-import-unknown-namespace": "نطاق الاستيراد \"$1\" غير معروف; يُرجَى التأكد من أن تفاصيل استيراد OWL متاحة عبر [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "<PERSON>ير قادر على العثور على مسار النطاق \"$1\"  في [[MediaWiki:Smw import $1|$1 استيراد]].", "smw-datavalue-import-missing-type": "لم يتم العثور على تعريف نوع ل\"$1\" في [[MediaWiki:Smw import $2|$2 استيراد]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1 استيراد]]", "smw-datavalue-import-invalid-value": "\"$1\" ليس تنسيقا صالحا ويُتوقَّع أن يتألف من \"النطاق\":\"معرف\" (على سبيل المثال \"foaf:اسم\").", "smw-datavalue-import-invalid-format": "من المتوقع تقسيم السلسلة \"$1\" إلى أربعة أجزاء ولكن لم يتم فهم الصيغة.", "smw-property-predefined-impo": "\"$1\" هي خاصية محددة مسبقا تصف علاقة [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary المفردات المستوردة] ويتم توفيرها من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-type": "\"$1\" هي خاصية محددة مسبقا تصف[[Special:Types|نوع بيانات]] الخاصية ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-sobj": "\"$1\" عبارة عن خاصية محددة مسبقا تمثل [https://www.semantic-mediawiki.org/wiki/Help:Container بناء] حاوية ويتم توفيرها من قبل\n [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-long-sobj": "تسمح الحاوية بتجميع تخصيصات قيمة خواص مماثلة لتخصيصات صفحة ويكي عادية ولكن داخل حيز كيان مختلف بينما تكون مرتبطة بموضوع التضمين.", "smw-property-predefined-errp": "\"$1\" عبارة عن خاصية محددة مسبقا تتتبع أخطاء الإدخال في التعليقات التوضيحية للقيمة غير المنتظمة ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-long-errp": "في معظم الحالات هو سبب عدم تطابق نوع أو [[Property:Allows value|قيمة]] تقييد.", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] عبارة عن خاصية محددة مسبقا يمكنها تحديد قائمة بالقيم المسموح بها لتقييد تخصيصات القيمة لخاصية ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value \"$1\"] عبارة عن خاصية محددة مسبقا يمكنها تحديد مرجع لقائمة تحمل القيم المسموح بها لتقييد تخصيصات القيمة لخاصية ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-datavalue-property-restricted-annotation-use": "الخاصية \"$1\" لها مجال تطبيق مقيد ولا يمكن استخدامها كموقع تعليق توضيحي من قبل المستخدم.", "smw-datavalue-property-restricted-declarative-use": "الخاصية \"$1\" هي خاصية إعلانية ولا يمكن استخدامها إلا على صفحة خاصية أو تصنيف.", "smw-datavalue-property-create-restriction": "الخاصية \"$1\" غير موجودة، ويفتقد المستخدم الإذن \"$2\" (راجع [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode وضع السلطة]) لإنشاء أو إضافة تعليقات توضيحية إلى القيم مع خاصية غير معتمدة.", "smw-datavalue-property-invalid-character": "\"$1\" تحتوي على الحرف \"$2\" مدرجا كجزء من تسمية الخاصية; وبالتالي تم تصنيفها على أنها غير صالحة.", "smw-datavalue-property-invalid-chain": "لا يُسمَح باستخدام \"$1\" كسلسلة خاصية أثناء عملية التعليق التوضيحي.", "smw-datavalue-restricted-use": "تم وضع علامة على داتافالو \"$1\" للاستخدام المقيد.", "smw-datavalue-invalid-number": "\"$1\" لا يمكن تفسيره على أنه رقم.", "smw-query-condition-circular": "تم اكتشاف حالة دائرية محتملة في \"$1\".", "smw-query-condition-empty": "وصف الاستعلام يحتوي على شرط فارغ.", "smw-types-list": "قائمة أنواع البيانات", "smw-types-default": "\"$1\" نوع بيانات مدمج.", "smw-types-help": "يمكن العثور على مزيد من المعلومات والأمثلة في [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 صفحة المساعدة هذه].", "smw-type-anu": "\"$1\" هو بديل نوع البيانات [[Special:Types/URL|مسار]] ويُستخدَم في الغالب لإعلان تصدير \"owl:AnnotationProperty\".", "smw-type-boo": "\"$1\" نوع بيانات أساسي لوصف قيمة صحيحة/خاطئة.", "smw-type-cod": "\"$1\" بديل نوع البيانات [[Special:Types/Text|نص]] ليتم استخدامه للنصوص التقنية ذات الطول التعسفي، مثل قوائم رموز المصدر.", "smw-type-geo": "\"$1\" هو نوع بيانات يصف المواقع الجغرافية ويتطلب تمديد [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"الخرائط\"] لتوفير وظيفة موسعة.", "smw-type-tel": "\"$1\" هو نوع بيانات خاص لوصف أرقام الهاتف الدولية وفقا لـRFC 3966.", "smw-type-txt": "\"$1\" نوع بيانات أساسي لوصف سلاسل الطول التعسفي.", "smw-type-dat": "\"$1\" نوع بيانات أساسي لتمثيل النقاط في الوقت بصيغة موحدة.", "smw-type-ema": "\"$1\" هو نوع بيانات خاص لتمثيل بريد إلكتروني.", "smw-type-tem": "\"$1\" هو نوع بيانات رقمي خاص لتمثيل درجة حرارة.", "smw-type-qty": "\"$1\" هو نوع بيانات لوصف الكميات ذات التمثيل الرقمي ووحدة القياس.", "smw-type-rec": "\"$1\" هو نوع بيانات حاوية يحدد قائمة بالخصائص المكتوبة بترتيب ثابت.", "smw-type-extra-tem": "يتضمن مخطط التحويل وحدات مدعومة مثل كلفن وسيلزيوس وفهرنهايت ورانكين.", "smw-type-tab-properties": "الخصائص", "smw-type-tab-types": "الأنواع", "smw-type-tab-type-ids": "اكتب المعرفات", "smw-type-tab-errors": "أخطاء", "smw-type-primitive": "أساسي", "smw-type-contextual": "قريني", "smw-type-compound": "مر<PERSON><PERSON>", "smw-type-container": "حاوية", "smw-type-no-group": "غير مصنف", "smw-special-pageproperty-description": "توفر هذه الصفحة واجهة تصفح للعثور على جميع قيم خاصية وصفحة معينة، تتضمن واجهات البحث الأخرى المتاحة [[Special:SearchByProperty|بحث الخاصية]]، و[[Special:Ask|اطلب باني استعلام]].", "smw-property-predefined-errc": "\"$1\" عبارة عن خاصية محددة مسبقا يوفرها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] وتمثل الأخطاء التي ظهرت فيما يتعلق بتعليقات أو معالجة مدخلات قيمة غير صحيحة.", "smw-property-predefined-long-errc": "يتم جمع الأخطاء في [https://www.semantic-mediawiki.org/wiki/Help:Container حاوية] التي قد تتضمن مرجعا إلى الخاصية التي تسببت في التناقض.", "smw-property-predefined-errt": "\"$1\" عبارة عن خاصية محددة مسبقا تحتوي على وصف نصي لخطأ ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-subobject-parser-invalid-naming-scheme": "يحتوي الكائن الفرعي المعرف من قبل المستخدم على نظام تسمية غير صالح، يتم تخصيص تدوين النقطة ($1) المستخدم في الأحرف الخمسة الأولى للإضافات، يمكنك تعيين [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier معرف مسمى].", "smw-datavalue-record-invalid-property-declaration": "يحتوي تعريف السجل على الخاصية \"$1\" التي يتم الإعلان عنها في حد ذاتها كنوع سجل وذلك غير مسموح به.", "smw-property-predefined-mdat": "\"$1\" خاصية محددة مسبقا تتوافق مع تاريخ آخر تعديل للموضوع ويتم توفيرها من خلال [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-cdat": "\"$1\" خاصية محددة مسبقا تتوافق مع تاريخ التنقيح الأول للموضوع ويتم توفيرها من خلال [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-newp": "\"$1\"  خاصية محددة مسبقا تشير إلى ما إذا كان الموضوع جديدا أم لا، ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-ledt": "\"$1\" خاصية محددة مسبقا تحتوي على اسم الصفحة للمستخدم الذي أنشأ آخر مراجعة ويتم توفيره من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-mime": "\"$1\" خاصية محددة مسبقا تصف نوع MIME للملف المرفوع ويتم توفيره بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-media": "\"$1\"  خاصية محددة مسبقا تصف نوع وسائط الملف المرفوع ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-askfo": "\"$1\" خاصية محددة مسبقا تحمل اسم صيغة النتيجة المستخدم في الاستعلام ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-askst": "\"$1\" هي خاصية محددة مسبقا تصف شروط الاستعلام كسلسلة ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-askdu": "\"$1\" خاصية محددة مسبقا تحتوي على قيمة زمنية (بالثواني) كانت مطلوبة لإكمال تنفيذ الاستعلام ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-asksc": "\"$1\"  خاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] التي تحدد مصادر الاستعلام البديلة (مثل البعيد، المتحد).", "smw-property-predefined-askco": "\"$1\" خاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لوصف حالة الاستعلام أو مكوناته.", "smw-property-predefined-long-askco": "يمثل الرقم أو الأرقام المعينة حالة مقننة داخلية يتم شرحها في [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler صفحة المساعدة].", "smw-property-predefined-prec": "\"$1\" خاصية محددة مسبقا تصف [https://www.semantic-mediawiki.org/wiki/Help:Display_precision دقة العرض] (بالأرقام العشرية) لأنواع البيانات الرقمية.", "smw-property-predefined-attch-link": "\"$1\" هي خاصية محددة مسبقا تجمع وصلات الملفات والملفات المضمنة الموجودة في الصفحة ويتم توفيرها من خلال [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-format-schema": "ويتم توفيرها من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-types-extra-geo-not-available": " لم يتم الكشف عن [https://www.semantic-mediawiki.org/wiki/Extension:Maps تمديد \"الخرائط\"] وبالتالي يقتصر \"$1\" على قدرته على العمل.", "smw-datavalue-monolingual-dataitem-missing": "عنصر متوقع لبناء قيمة مجمع أحادي اللغة مفقود.", "smw-datavalue-languagecode-missing": "بالنسبة للتعليق التوضيحي \"$1\"، لم يتمكن المحلل من تحديد رمز اللغة (أي \"foo@en\").", "smw-datavalue-languagecode-invalid": "لم يتم التعرف على \"$1\" كرمز لغة مدعوم.", "smw-property-predefined-lcode": "\"$1\" عبارة عن خاصية محددة مسبقا تمثل رمز لغة BCP47 مهيأ ويتم توفيره بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-type-mlt-rec": "\"$1\" هو [https://www.semantic-mediawiki.org/wiki/Help:Container حاوية]  بيانات يربط قيمة نصية [[Property:Language code|رمز لغة]] خاص.", "smw-types-extra-mlt-lcode": "نوع البيانات {{PLURAL:$2|يتطلب|لا يتطلب }} رمز لغة (أي أن {{PLURAL:$2|a value الشرح بدون رمز لغة غير مقبول|الشرح بدون رمز لغة غير مقبول}}).", "smw-property-predefined-text": "\"$1\" خاصية محددة مسبقا تمثل نص الطول التعسفي ويتم توفيرها من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-pdesc": "\"$1\" خاصية محددة مسبقا تسمح بوصف خاصية في سياق لغة ويتم توفيرها من قبل [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-list": "\"$1\" خاصية محددة مسبقا لتحديد قائمة الخصائص المستخدمة خاصية من نوع [[Special:Types/Record|سجل]] ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties  ميدياويكي الدلالي].", "smw-limitreport-intext-parsertime": "[SMW] وقت تحليل الشرح في النص", "smw-limitreport-intext-postproctime": "[SMW] بعد وقت المعالجة", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|ثانية واحدة|ثانية}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|ثانية واحدة|ثانية}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] وقت تحديث المستودع (في تطهير الصفحة)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|ثانية واحدة|ثانية}}", "smw_allows_pattern": "من المتوقع أن تحتوي هذه الصفحة على قائمة بالمراجع (متبوعة [https://ar.wikipedia.org/wiki/تعبير نمطي بتعبيرات نمطية]) يتم توفيرها بواسطة خاصية  [[Property:Allows pattern|يسمح بنمط]] لتعديل هذه الصفحة; مطلوب إدخال حق <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "تم تصنيف \"$1\" على أنه غير صالح بواسطة التعبير النمطي \"$2\".", "smw-datavalue-allows-pattern-reference-unknown": "لا يمكن مطابقة مرجع النمط \"$1\" بإدخال في [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "لم يكن مرجع قائمة \"$1\" متطابقا مع [[MediaWiki:Smw allows list $1]] page.", "smw-datavalue-allows-value-list-missing-marker": "محتوى قائمة \"$1\" يفتقد العناصر التي تحتوي على علامة قائمة *.", "smw-datavalue-feature-not-supported": "الميزة \"$1\" غير مدعومة أو تم تعطيلها في موقع الويكي هذا.", "smw-property-predefined-pvap": "\"$1\" خاصية محددة مسبقا يمكن أن تحدد [[MediaWiki:Smw allows pattern|مرجع نمط\n]] لتطبيق [https://en.wikipedia.org/wiki/تعبير_نمطي تعبير نمطي] مطابقة ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-dtitle": "\"$1\" خاصية محددة مسبقا يمكنها تعيين عنوان عرض مميز إلى كيان ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-pvuc": "\"$1\" خاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لتقييد تخصيصات القيمة لكل مثيل لتكون فريدة (أو واحدة على الأكثر).", "smw-property-predefined-long-pvuc": "يتحدد الت<PERSON><PERSON>د عندما لا تكون القيمتان متساويتين في تمثيلهما الحرفي، وسيُصنَّف أي انتهاك لهذا القيد على أنه خطأ.", "smw-datavalue-constraint-uniqueness-violation": "الخاصية \"$1\" تسمح فقط بتخصيصات القيمة الفريدة و\"$2\" تم التعليق عليها بالفعل في الموضوع \"$3\".", "smw-datavalue-constraint-uniqueness-violation-isknown": "تسمح الخاصية \"$1\" فقط بالتعليقات التوضيحية للقيمة الفريدة، حيث يحتوي \"$2\" على قيمة معينة، \"$3\" ينتهك قيود التفرد.", "smw-datavalue-constraint-violation-non-negative-integer": "تحتوي الخاصية \"$1\" على قيد \"عدد صحيح غير سالب\" والقيمة \"$2\" تنتهك هذا المطلب.", "smw-datavalue-constraint-violation-must-exists": "تحتوي الخاصية\"$1\" على القيد <code>must_exists</code> والقيمة  ''$2'' تنتهك هذا المطلب.", "smw-datavalue-constraint-violation-single-value": "تحتوي الخاصية \"[[Property:$1|$1]]\" على قيد <code>single_value</code> والقيمة \"$2\" تنتهك هذا المطلب.", "smw-constraint-violation-class-mandatory-properties-constraint": "التصنيف \"[[:$1]]\"  له قيد <code>mandatory_properties</code> تم تعيينه معين ويتطلب الخصائص الإلزامية التالية: $2", "smw-property-predefined-boo": "\"$1\" هو [[Special:Types/Boolean|نوع]] وخاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لتمثيل القيم المنطقية.", "smw-property-predefined-num": "\"$1\" هو [[Special:Types/Boolean|نوع]] وخاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لتمثيل القيم الرقمية.", "smw-property-predefined-dat": "\"$1\" هو [[Special:Types/Boolean|نوع]] وخاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لتمثيل قيم التاريخ.", "smw-property-predefined-uri": "\"$1\" هو [[Special:Types/Boolean|نوع]] وخاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لتمثيل قيم URI/URL.", "smw-property-predefined-qty": "\"$1\" هو [[Special:Types/Boolean|نوع]] وخاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لتمثيل القيم الكمية.", "smw-datavalue-time-invalid-offset-zone-usage": "يحتوي \"$1\" على معرف إزاحة ومنطقة غير معتمد.", "smw-datavalue-time-invalid-values": "تحتوي القيمة \"$1\" على معلومات غير قابلة للتفسير في شكل \"$2\".", "smw-datavalue-time-invalid-date-components-common": "يحتوي \"$1\" على بعض المعلومات غير القابلة للتفسير.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\" يحتوي على شرطة خارجية أو حروف أخرى غير صالحة لتفسير التاريخ.", "smw-datavalue-time-invalid-date-components-empty": "يحتوي \"$1\" على بعض المكونات الفارغة.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" يحتوي على أكثر من ثلاثة مكونات مطلوبة لتفسير التاريخ.", "smw-datavalue-time-invalid-date-components-sequence": "يحتوي \"$1\" على تسلسل لا يمكن تفسيره مقابل مصفوفة مطابقة متاحة لمكونات التاريخ.", "smw-datavalue-time-invalid-ampm": "يحتوي \"$1\" على \"$2\" كعنصر ساعة غير صالح لعقد مدته 12 ساعة.", "smw-datavalue-time-invalid-jd": "يتعذر تفسير قيمة إدخال \"$1\" كمعرف صالح (التاريخ اليولياني) مع الإبلاغ عن \"$2\".", "smw-datavalue-time-invalid-prehistoric": "يتعذر تفسير قيمة إدخال \"ما قبل التاريخ\" بقيمة $1، على سبيل المثال، بعد تحديد أكثر من سنة أو نموذج تقويم قد تعود نتائج غير متوقعة في سياق ما قبل التاريخ.", "smw-datavalue-time-invalid": "يتعذر تفسير قيمة الإدخال \"$1\" كمكون تاريخ أو وقت صالح مع الإبلاغ عن \"$2\".", "smw-datavalue-external-formatter-uri-missing-placeholder": "يف<PERSON><PERSON><PERSON> URI المنسق العنصر النائب ''$1''.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\" مسار غير صالح.", "smw-datavalue-external-identifier-formatter-missing": "الخاصية تفتقد تعيين [[Property:External formatter uri|\"URI المنسق الخارجي\"]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "يتوقع المعرف الخارجي \"$1\" استبدال حقل متعدد لكن القيمة الحالية \"$2\" تفتقد إلى وسيط قيمة واحد على الأقل لتتوافق مع المتطلبات.", "smw-datavalue-keyword-maximum-length": "تجاوزت الكلمة الرئيسية الحد الأقصى لطول {{PLURAL:$1|حرف|حروف}}..", "smw-property-predefined-eid": "\"$1\" هو [[Special:Types/Boolean|نوع]] وخاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لتمثيل المعرفات الخارجية.", "smw-property-predefined-peid": "\"$1\" خاصية محددة مسبقا تحدد معرفا خارجيا ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-pefu": "\"$1\" خاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] للتحديد مورد خارجي به عنصر نائب.", "smw-property-predefined-long-pefu": "من المتوقع أن يحتوي URI على عنصر نائب سيتم تعديله باستخدام قيمة [[Special:Types/External identifier|معرف خارجي]] لتكوين مرجع مرجعي صالح.", "smw-type-eid": "\"$1\" هو بديل  نوع البيانات [[Special:Types/Text|نص]] لوصف الموارد الخارجية (على أساس URI) ويتطلب خصائص معينة للإعلان عن [[Property:External formatter uri|URI المنسق الخارجي]].", "smw-property-predefined-keyw": "\"$1\" خاصية و[[Special:Types/Keyword|نوع]] محددة مسبقا مقدمة من [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] تقوم بتطبيع النص ولها قيود طول الحرف.", "smw-type-keyw": "\"$1\" هو بديل نوع البيانات [[Special:Types/Text|النص]] الذي يحتوي على طول حرف مقيد مع تمثيل محتوى عادي.", "smw-datavalue-stripmarker-parse-error": "القيمة المعطاة \"$1\" تحتوي على [https://en.wikipedia.org/wiki/Help:Strip_markers علامات الشريط]  وبالتالي فإنه لا يمكن تحليلها بما فيه الكفاية.", "smw-datavalue-parse-error": "القيمة المعطاة \"$1\" لم يتم فهمها.", "smw-datavalue-propertylist-invalid-property-key": "تحتوي قائمة الخاصية \"$1\" على مفتاح خاصية غير صالح \"$2\".", "smw-datavalue-type-invalid-typeuri": "تعذر تحويل نوع \"$1\" إلى تمثيل URI صالح.", "smw-datavalue-wikipage-missing-fragment-context": "لا يمكن استخدام قيمة إدخال صفحة الويكي \"$1\" بدون صفحة سياق.", "smw-datavalue-wikipage-invalid-title": "تحتوي قيمة إدخال نوع الصفحة \"$1\" على أحرف غير صالحة أو غير مكتملة، وبالتالي يمكن أن تسبب نتائج غير متوقعة أثناء عملية استعلام أو تعليق توضيحي.", "smw-datavalue-wikipage-property-invalid-title": "الخاصية \"$1\" (كنوع الصفحة) بقيمة الإدخال \"$2\" تحتوي على أحرف غير صالحة أو غير مكتملة وبالتالي يمكن أن تسبب نتائج غير متوقعة أثناء الاستعلام أو عملية التعليق التوضيحي.", "smw-datavalue-wikipage-empty": "قيمة إدخال صفحة الويكي فارغة (على سبيل المثال <code>[[SomeProperty::]]، [[]]</code>) وبالتالي لا يمكن استخدامها كاسم أو كجزء من شرط الاستعلام.", "smw-type-ref-rec": "\"$1\" هو نوع [https://www.semantic-mediawiki.org/wiki/Container الحاوية] الذي يسمح بتسجيل معلومات إضافية (مثل بيانات المصدر) حول تعيين قيمة.", "smw-datavalue-reference-invalid-fields-definition": "يتوقع نوع [[Special:Types/Reference|المرجع]] قائمة بالخصائص التي سيتم الإعلان عنها باستخدام خاصية [mediawiki.org/wiki/Help:Special_property_Has_fields لديه حقول].", "smw-parser-invalid-json-format": "عاد محلل JSON بـ\"$1\".", "smw-property-preferred-label-language-combination-exists": "لا يمكن استخدام \"$1\" كعلامة مفضلة لأن اللغة \"$2\" تم تعيينها بالفعل للتسمية \"$3\".", "smw-clipboard-copy-link": "نسخ الرابط إلى الحافظة", "smw-property-userdefined-fixedtable": "تم تكوين \"$1\" [https://www.semantic-mediawiki.org/wiki/Fixed_properties كخاصية ثابتة] وأي تعديل في [https://www.semantic-mediawiki.org/wiki/Type_declaration إعلان نوعها] يتطلب إما تشغيل <code>setupStore.php</code> أو إكمال المهمة الخاصة [[Special:SemanticMediaWiki|\"\"تثبيت وترقية قاعدة البيانات\"]].", "smw-data-lookup": "جاري جلب البيانات...", "smw-data-lookup-with-wait": "تتم معالجة الطلب وقد يستغرق بعض الوقت.", "smw-no-data-available": "لا تتوفر بيانات.", "smw-property-req-violation-missing-fields": "الخاصية \"$1\" تفقد إعلان [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] لنوع \"$2\" هذا.", "smw-property-req-violation-multiple-fields": "تحتوي الخاصية \" $1 \" على إعلانات متعددة (وبالتالي متنافسة) [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code> ]، ومن المتوقع إعلان واحد فقط لهذا النوع \" $2 \".", "smw-property-req-violation-missing-formatter-uri": "تفتقد الخاصية \"$1\" تفاصيل التصريح للنوع المشروح عن طريق عدم تحديد الخاصية <code>URI المنسق الخارجي</code>.", "smw-property-req-violation-predefined-type": "الخاصية \"$1\" كخاصية معرفة مسبقا تحتوي على إعلان \"من نوع $2\" غير متوافق مع النوع الافتراضي لهذه الخاصية.", "smw-property-req-violation-import-type": "تم الكشف عن إعلان نوع غير متوافق مع نوع معرف مسبقا من المفردات \"$1\" المستوردة، بشكل عام، ليس من الضروري الإعلان عن نوع لأن المعلومات يتم استرجاعها من تعريف الاستيراد.", "smw-property-req-violation-change-propagation-locked-error": "تم تغيير الخاصية \"$1\" وتتطلب إعادة تقييم الكيانات المعينة باستخدام عملية [https://www.semantic-mediawiki.org/wiki/Change_propagation انتشار التغيير]، تمت حماية صفحة الخاصية حتى يتم استكمال تحديث المواصفات الأساسية لمنع انقطاع وسيط أو مواصفات متناقضة، قد تستغرق هذه العملية بعض الوقت قبل أن يتم إلغاء حماية الصفحة لأنها تعتمد على حجم وتواتر جدولة [https://www.mediawiki.org/wiki/Manual:Job_queue طابور العمل].", "smw-property-req-violation-change-propagation-locked-warning": "تم تغيير الخاصية \"$1\" وتتطلب إعادة تقييم الكيانات المعينة باستخدام عملية [https://www.semantic-mediawiki.org/wiki/Change_propagation انتشار التغيير]، تمت حماية صفحة الخاصية حتى يتم استكمال تحديث المواصفات الأساسية لمنع انقطاع وسيط أو مواصفات متناقضة، قد تستغرق هذه العملية بعض الوقت قبل أن يتم إلغاء حماية الصفحة لأنها تعتمد على حجم وتواتر جدولة [https://www.mediawiki.org/wiki/Manual:Job_queue طابور العمل] ويُقترَح تأجيل التغييرات إلى الخاصية لمنع انقطاع وسيط أو مواصفات متناقضة.", "smw-property-req-violation-change-propagation-pending": "تحديثات [https://www.semantic-mediawiki.org/wiki/Change_propagation انتشار التغيير] معلقة ($1 [https://www.mediawiki.org/wiki/Manual:Job_queue {{PLURAL:$1|وظيفة مقدرة|وظائة مقدرة}}]) ويُوصَى بالانتظار مع التعديلات على الخاصية حتى يتم الانتهاء من العملية لمنع انقطاع وسيط أو مواصفات متناقضة.", "smw-property-req-violation-missing-maps-extension": "تعذر على ميدياويكي الدلالي اكتشاف ملحق [https://www.semantic-mediawiki.org/wiki/Extension:Maps \"خرائط\"] الذي يعد متطلبا أساسيا للنوع المحدد ونتيجة لذلك يحد من وظائف (أي غير قادر على تخزين أو معالجة البيانات الجغرافية) هذه الخاصية.", "smw-property-req-violation-type": "تحتوي الخاصية على مواصفات نوع منافسة قد تؤدي إلى تعليقات توضيحية ذات قيمة غير صالحة لذلك فمن المتوقع قيام أحد المستخدمين بتعيين نوع واحد مناسب.", "smw-property-req-error-list": "تحتوي الخاصية على الأخطاء أو التحذيرات التالية:", "smw-property-req-violation-parent-type": "تحتوي الخاصية \"$1\" والخاصية الأصلية المعينة \"$2\" على تعليقات توضيحية مختلفة في النوع.", "smw-property-req-violation-forced-removal-annotated-type": "تم تمكين فرض [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance الإرتباط الإجباري من نوع الأصل]، لا يطابق نوع التعليق التوضيحي لخاصية \"$1\" مع نوع الخاصية الأصلي \"$2\"، تم تغييره ليعكس هذا الشرط؛ يُوصَى بتعديل تعريف النوع في الصفحة بحيث تتم إزالة رسالة الخطأ والتنفيذ الإلزامي لهذه الخاصية.", "smw-change-propagation-protection": "تمت حماية هذه الصفحة لمنع تعديل البيانات العرضي أثناء تشغيل تحديث [https://www.semantic-mediawiki.org/wiki/Change_propagation انتشار التغيير]، قد تستغرق العملية بعض الوقت قبل أن يتم إلغاء حماية الصفحة لأنها تعتمد على حجم وتواتر جدولة [https://www.mediawiki.org/wiki/Manual:Job_queue طابور العمل].", "smw-category-change-propagation-locked-error": "تم تغيير التصنيف \"$1\" ويتطلب إعادة تقييم الكيانات المعينة باستخدام عملية [https://www.semantic-mediawiki.org/wiki/Change_propagation انتشار التغيير]، تمت حماية صفحة التصنيف حتى يتم استكمال تحديث المواصفات الأساسية لمنع انقطاع وسيط أو مواصفات متناقضة، قد تستغرق هذه العملية بعض الوقت قبل أن يتم إلغاء حماية الصفحة لأنها تعتمد على حجم وتواتر جدولة [https://www.mediawiki.org/wiki/Manual:Job_queue طابور العمل].", "smw-category-change-propagation-locked-warning": "تم تغيير التصنيف \"$1\" ويتطلب إعادة تقييم الكيانات المعينة باستخدام عملية [https://www.semantic-mediawiki.org/wiki/Change_propagation انتشار التغيير]، تمت حماية صفحة التصنيف حتى يتم استكمال تحديث المواصفات الأساسية لمنع انقطاع وسيط أو مواصفات متناقضة، قد تستغرق هذه العملية بعض الوقت قبل أن يتم إلغاء حماية الصفحة لأنها تعتمد على حجم وتواتر جدولة [https://www.mediawiki.org/wiki/Manual:Job_queue طابور العمل] ويُقترَح تأجيل التغييرات إلى التصنيف لمنع انقطاع وسيط أو مواصفات متناقضة.", "smw-category-change-propagation-pending": "تحديثات [https://www.semantic-mediawiki.org/wiki/Change_propagation انتشار التغيير] معلقة ($1 [https://www.mediawiki.org/wiki/Manual:Job_queue {{PLURAL:$1|وظيفة مقدرة|وظائة مقدرة}}]) ويُوصَى بالانتظار مع التعديلات على التصنيف حتى يتم الانتهاء من العملية لمنع انقطاع وسيط أو مواصفات متناقضة.", "smw-category-invalid-value-assignment": "\"$1\" غير متعرف عليها كتصنيف أو شرح قيمة صحيح.", "protect-level-smw-pageedit": "السماح للمستخدمين فقط بإذن تعديل الصفحة (ميدياويكي الدلالي)", "smw-create-protection": "يقتصر إنشاء الخاصية \"$1\" على المستخدمين الذين لديهم الصلاحية المناسبة \"$2\" (أو [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups مجموعة المستخدمين]) أثناء تمكين [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode وضع السلطة].", "smw-create-protection-exists": "تقتصر التغييرات على الخاصية \"$1\" على المستخدمين الذين لديهم الصلاحية المناسبة \"$2\" (أو [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups مجموعة المستخدمين]) أثناء تمكين [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode وضع السلطة].", "smw-edit-protection": "هذه الصفحة [[Property:Is edit protected|محمية]] لمنع تعديل البيانات العرضي ولا يمكن تعديلها إلا بواسطة المستخدمين الذين لديهم الصلاحية المناسبة \"$1\" (أو [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups مجموعة المستخدمين]).", "smw-edit-protection-disabled": "تم تعطيل حماية التعديل ولذلك لا يمكن استخدام \"$1\" لحماية صفحات الكيان من التعديل غير المصرح به.", "smw-edit-protection-auto-update": "قام ميدياويكي الدلالي بتحديث حالة الحماية وفقا لخاصية \"محمية من التعديل\".", "smw-edit-protection-enabled": "محمية من التعديل (ميدياويكي الدلالي)", "smw-patternedit-protection": "هذه الصفحة محمية ولا يمكن تعديلها إلا بواسطة المستخدمين ذوي <code>smw-patternedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions الصلاحية] المناسبة.", "smw-property-predefined-edip": "\"$1\"  خاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] للإشارة إلى ما إذا كان التحرير محمي أم لا", "smw-property-predefined-long-edip": "في حين أن أي مستخدم مؤهل لإضافة هذه الخاصية إلى موضوع ما، يمكن فقط للمستخدم الذي لديه صلاحية مخصصة تعديل أو إلغاء الحماية لكيان بعد إضافته.", "smw-query-reference-link-label": "مرجع الاستعلام", "smw-format-datatable-emptytable": "لا تتوفر بيانات في الجدول", "smw-format-datatable-info": "إظهار _START_ ل_END_ من _TOTAL_ إدخالات_TOTAL_", "smw-format-datatable-infoempty": "عرض 0 إلى 0 من 0 إدخالات", "smw-format-datatable-infofiltered": "(تمت تصفيتها من _MAX_ إجمالي الإدخالات)", "smw-format-datatable-infothousands": "،", "smw-format-datatable-lengthmenu": "إظهار إدخالات _MENU_", "smw-format-datatable-loadingrecords": "جارٍ التحميل...", "smw-format-datatable-processing": "تجري المعالجة...", "smw-format-datatable-search": "بحث:", "smw-format-datatable-zerorecords": "لم يتم العثور على سجلات مطابقة", "smw-format-datatable-first": "الأول", "smw-format-datatable-last": "الأخير", "smw-format-datatable-next": "التالي", "smw-format-datatable-previous": "السابق", "smw-format-datatable-sortascending": ": تفعيل لفرز العمود تصاعديا", "smw-format-datatable-sortdescending": ": تفعيل لفرز العمود تنازليا", "smw-format-datatable-toolbar-export": "تصدير", "smw-format-list-other-fields-open": " )", "smw-format-list-other-fields-close": " )", "smw-category-invalid-redirect-target": "يحتوي التصنيف \"$1\" على هدف تحويلة غير صالح إلى نطاق غير التصنيف.", "smw-parser-function-expensive-execution-limit": "وصلت وظيفة المحلل إلى الحد الأقصى لعمليات التنفيذ المكلفة (انظر وسيط التكوين \n [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "ميدياويكي الدلالي يقوم بتحديث الصفحة الحالية على شرط معالجة بعض طلبات البحث المطلوبة.", "apihelp-smwinfo-summary": "وحدة API لاسترداد المعلومات حول إحصاءات ميدياويكي الدلالي والمعلومات الوصفية الأخرى.", "apihelp-ask-summary": "وحدة API لاستعلام ميدياويكي الدلالي باستخدام لغة الطلب.", "apihelp-askargs-summary": "وحدة API لاستعلام ميدياويكي الدلالي باستخدام لغة الطلب كقائمة من الشروط والمطبوعات والوسائط.", "apihelp-browsebyproperty-summary": "وحدة API لاسترداد المعلومات حول خاصية أو قائمة من الخصائص.", "apihelp-browsebysubject-summary": "وحدة API لاسترداد المعلومات حول موضوع.", "apihelp-smwtask-summary": "وحدة API لتنفيذ المهام ذات الصلة بميدياويكي الدلالي", "apihelp-smwbrowse-summary": "وحدة API لدعم أنشطة التصفح لأنواع الكيانات المختلفة في ميدياويكي.", "apihelp-ask-parameter-api-version": "تنسيق الإخراج:\n;2: تنسيق متوافق مع الإصدارات السابقة يستخدم {} لقائمة النتائج.\n;3:التنسيق التجريبي الذي يستخدم [] كقائمة نتائج.", "apihelp-smwtask-param-task": "يحدد نوع المهمة", "smw-api-invalid-parameters": "وسائط غير صالحة، \"$1\"", "smw-parser-recursion-level-exceeded": "تم تجاوز مستوى التكرارات $1 أثناء عملية التحليل. يُقترح التحقق من صحة بنية القالب، أو إذا لزم الأمر تعديل معلمة التكوين <code>$maxRecursionDepth</code> .", "smw-property-page-list-count": "إظهار $1 {{PLURAL:$1|صفحة|صفحات}} باستخدام هذه الخاصية.", "smw-property-page-list-search-count": "إظهار $1 {{PLURAL:$1|صفحة|صفحات}} باستخدام هذه الخاصية بمطابقة قيمة $2.", "smw-property-reserved-category": "تصنيف", "smw-category": "التصنيف", "smw-datavalue-uri-invalid-scheme": "لم يتم إدراج \"$1\" كمخطط URI صالح.", "smw-datavalue-uri-invalid-authority-path-component": "\"$1\" تم التعرف عليه كمحتوي على مكون سلطة أو مسار غير صحيح \"$2\".", "smw-browse-property-group-title": "مجموعة الخصائص", "smw-browse-property-group-label": "تسمية مجموعة الخصائص", "smw-browse-property-group-description": "وصف مجموعة الخصائص", "smw-property-predefined-ppgr": "\"$1\" خاصية محددة مسبقا تحدد الكيانات (بشكل رئيسي التصنيفات) التي يتم استخدامها كمثال تجميع للخصائص ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-filter": "مر<PERSON>ح", "smw-section-expand": "وسع هذا القسم", "smw-section-collapse": "اطو هذا القسم", "smw-ask-format-help-link": "صيغة [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "مساعدة", "smw-cheat-sheet": "صحيفة الغش", "smw-personal-jobqueue-watchlist": "قائمة مراقبة قائمة انتظار الوظائف", "smw-personal-jobqueue-watchlist-explain": "تشير الأرقام إلى تقدير إدخالات قائمة انتظار العمل في انتظار التنفيذ.", "smw-property-predefined-label-skey": "م<PERSON><PERSON><PERSON><PERSON> الفرز", "smw-processing": "تجري المعالجة...", "smw-loading": "جارٍ التحميل...", "smw-fetching": "جارٍ الإحضار...", "smw-preparing": "جارٍ التجهيز...", "smw-expand": "وسع", "smw-collapse": "طي", "smw-copy": "نسخ", "smw-copy-clipboard-title": "ين<PERSON><PERSON> المحتوى إلى الحافظة", "smw-jsonview-expand-title": "يوسع عرض JSON", "smw-jsonview-collapse-title": "يطوي عرض JSON", "smw-jsonview-search-label": "بحث:", "smw-redirect-target-unresolvable": "الهدف غير قابل للحل بسبب \"$1\"", "smw-types-title": "النوع: $1", "smw-schema-namespace-editcontentmodel-disallowed": "غير مسموح بتغيير نموذج محتوى [https://www.semantic-mediawiki.org/wiki/Help:Schema صفحة مخطط].", "smw-schema-namespace-edit-protection": "هذه الصفحة محمية ولا يمكن تحريرها إلا بواسطة المستخدمين الذين لديهم  [https://www.semantic-mediawiki.org/wiki/Help:Permissions صلاحية] <code>smw-schemaedit</code> المناسبة.", "smw-schema-error-title": "{{PLURAL:$1|خطأ|أخطاء}} في التحقق", "smw-schema-error-schema": "عثر مخطط التحقق '''$1''' على التناقضات التالية:", "smw-schema-error-validation-file-inaccessible": "يتعذر الوصول إلى ملف التحقق من الصحة \"$1\".", "smw-schema-error-violation": "[\"$1\"، \"$2\"]", "smw-schema-error-type-missing": "يفت<PERSON><PERSON> المحتوى إلى نوع لكي يتم التعرف عليه واستخدامه في [https://www.semantic-mediawiki.org/wiki/Help:Schema نطاق المخطط].", "smw-schema-error-type-unknown": "النوع \"$1\" غير مسجل وبالتالي لا تستطيع استخدامه للمحتوى في [https://www.semantic-mediawiki.org/wiki/Help:Schema نطاق المخطط/مد].", "smw-schema-error-json": "خطأ JSON: \"$1\"", "smw-schema-error-input": "لقد وجد التحقق من صحة المدخلات المشكلات التالية، تجب معالجتها قبل حفظ المحتوى، قد تقدم صفحة [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling مساعدة المخطط] بعض النصائح حول كيفية إزالة التناقضات أو حل المشكلات المتعلقة بتنسيق الإدخال.", "smw-schema-error-input-schema": "لقد وجد التحقق من الصحة '''$1''' التناقضات التالية وتجب معالجتها قبل حفظ المحتوى، قد تقدم صفحة [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling مساعدة المخطط] بعض النصائح حول كيفية إزالة التناقضات أو حل المشكلات المتعلقة بتنسيق الإدخال.", "smw-schema-error-title-prefix": "يتطلب هذا النوع من المخطط أن يبدأ عنوان المخطط ببادئة \"$1\".", "smw-schema-validation-error": "النوع \"$1\" غير مسجل ولا يمكن استخدامه للمحتوى في نطاق [https://www.semantic-mediawiki.org/wiki/Help:Schema المخطط/مد].", "smw-schema-validation-schema-title": "مخطط JSON", "smw-schema-summary-title": "الملخص", "smw-schema-title": "مخطط", "smw-schema-usage": "الاستخدام", "smw-schema-type": "النوع", "smw-schema-type-description": "اكتب الوصف", "smw-schema-description": "وصف المخطط", "smw-schema-description-link-format-schema": "نوع المخطط هذا يدعم تعريف الخصائص الخاصة بإنشاء وصلات حساسة للسياق فيما يتعلق بخاصية [[Property:Formatter schema|مخطط المنسق]].", "smw-schema-description-search-form-schema": "يدعم نوع المخطط هذا تعريف نماذج المدخلات والخصائص لملف تعريف [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch extended search] حيث يحتوي على إرشادات حول كيفية إنشاء حقول الإدخال وتحديد النطاقات الافتراضية، أو بيان تعبيرات بادئة لطلب بحث.", "smw-schema-description-property-profile-schema": "يدعم نوع المخطط هذا تعريف ملف شخصي لإعلان خصائص الخاصية المعينة وقيمها التوضيحية.", "smw-schema-description-property-group-schema": "يدعم نوع المخطط هذا تعريف [https://www.semantic-mediawiki.org/wiki/Help:Property_group مجموعات الخواص] للمساعدة في بناء واجهة [https://www.semantic-mediawiki.org/wiki/Help:Special:Browse التصفح].", "smw-schema-description-property-constraint-schema": "يدعم نوع المخطط هذا تعريف قواعد القيد لمثيل خاصية وكذلك تلك القيم المعينة لها.", "smw-schema-description-class-constraint-schema": "يدعم نوع المخطط هذا تعريف قواعد القيد لمثيل فئة (تُعرَف أيضًا باسم التصنيف).", "smw-schema-tag": "{{PLURAL:$1|وسم|وسوم}}", "smw-property-predefined-constraint-schema": "\"$1\" هي خاصية محددة مسبقا تحدد مخطط القيد ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-schema-desc": "\"$1\" خاصية محددة مسبقا تقوم بتخزين وصف المخطط ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-schema-def": "\"$1\" خاصية محددة مسبقا تقوم بتخزين محتويات المخطط ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-schema-tag": "\"$1\" خاصية محددة مسبقا يقدمها [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي] لتحديد مجموعة من المخططات.", "smw-property-predefined-long-schema-tag": "تصنيف يح<PERSON><PERSON> مخططات ذات محتوى أو خصائص مشابهة.", "smw-property-predefined-schema-type": "\"$1\"  خاصية محددة مسبقا تصف نوعا للتمييز بين مجموعة من المخططات ويتم توفيرها بواسطة [https://www.semantic-mediawiki.org/wiki/Help:Special_properties ميدياويكي الدلالي].", "smw-property-predefined-long-schema-type": "يوفر كل [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type نوع] تفسيره الخاص لعناصر بناء الجملة وقواعد التطبيق ويمكن التعبير عنه بمساعدة [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation مخطط التحقق].", "smw-ask-title-keyword-type": "بحث بكلمة مفتاحية", "smw-ask-message-keyword-type": "تطابق هذا البحث مع الشرط <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "يتعذر الاتصال بالهدف \"$1\" البعيد.", "smw-remote-source-disabled": "لقد عطل المصدر '''$1''' دعم الطلب عن بعد!", "smw-remote-source-unmatched-id": "لا يطابق المصدر '''$1''' إصدارا من ميدياويكي الدلالي يمكنه دعم طلب بعيد.", "smw-remote-request-note": "يتم جلب النتيجة من المصدر البعيد '''$1''' ومن المحتمل أن يحتوي المحتوى المُنشأ على معلومات غير متوفرة ضمن الويكي الحالي.", "smw-remote-request-note-cached": "يتم '''التخزين المؤقت''' للنتيجة من المصدر البعيد '''$1''' ومن المحتمل أن يحتوي المحتوى المُنشأ على معلومات غير متوفرة ضمن الويكي الحالي.", "smw-parameter-missing": "الوسيط \"$1\" مفقود.", "smw-property-tab-usage": "الاستخدام", "smw-property-tab-redirects": "مرادفات", "smw-property-tab-subproperties": "خصائص فرعية", "smw-property-tab-errors": "مهام غير مناسبة", "smw-property-tab-constraint-schema": "م<PERSON><PERSON><PERSON> القيد", "smw-property-tab-constraint-schema-title": "مخط<PERSON> القيد المجمع", "smw-property-tab-specification": "... المزيد", "smw-concept-tab-list": "قائمة", "smw-concept-tab-errors": "أخطاء", "smw-ask-tab-result": "النتيجة", "smw-ask-tab-extra": "إضافات", "smw-ask-tab-debug": "تصحيح", "smw-ask-tab-code": "كود", "smw-install-incomplete-tasks-title": "مهام الإدارة غير المكتملة", "smw-install-incomplete-intro": "هناك $2 غير مكتمل أو [[Special:PendingTaskList|معلق]] {{PLURAL:$2| مهمة | مهام}} لإنهاء {{PLURAL:$1| تثبيت | ترقية}} من [https://www.semantic-mediawiki.org Semantic MediaWiki]. يمكن للمسؤول أو المستخدم الذي يتمتع بحقوق كافية إكمال {{PLURAL:$2| انها | هذه}}. يجب القيام بذلك قبل إضافة بيانات جديدة لتجنب التناقضات.", "smw-updateentitycountmap-incomplete": "الحقل <code>smw_countmap</code> أضيف مؤخرا في إصدارة حديثة ويتطلب تشغيل السكربت <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> script is لتتمكن وظائف النظام من النفاذ لمحتوى هذا الحقل.", "smw-install-incomplete-populate-hash-field": "تم تخطي حقل <code>smw_hash</code> أثناء الإعداد. يجب تنفيذ البرنامج النصي <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> .", "smw-install-incomplete-elasticstore-indexrebuild": "تم اختيار <code>المتجر المرن</code> [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore كمتجر افتراضي]، ومع ذلك لم يتمكن الملحق من العثور على أي سجل بتنفيذ السكريبت [https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]، يُرجَى تشغيل السكريبت حسب التعليمات.", "smw-pendingtasks-setup-tasks": "المهام", "smw-filter-count": "<PERSON><PERSON><PERSON> المرشح", "smw-es-replication-check": "فحص النسخ المتماثل (إلاستيك سيرش)", "smw-es-replication-error": "قضية تكرار", "smw-es-replication-file-ingest-error": "مسألة تناول الملف", "smw-es-replication-error-missing-id": "مراقبة التكرار وجد أن المقالة \"$1\" (ID: $2) مفقودة من قاعدة بيانات Elasticsearch.", "smw-es-replication-error-divergent-date": "مراقبة التكرار وجدت أن <b>تاريخ تعديل</b> المقالة \"$1\" (ID: $2) تظهر تعارضا.", "smw-es-replication-error-divergent-date-detail": "تاريخ التعديل المشار إليه\n*إلاستيك سيرش: $1\n*قاعدة البيانات: $2", "smw-es-replication-error-divergent-revision": "لقد وجدت مراقبة النسخ المتماثل أنه بالنسبة للمقالة \"$1\" (المعرف: $2)، تظهر <b>المراجعة المرتبطة</b> تباينا.", "smw-es-replication-error-divergent-revision-detail": "*إلاستيك سيرش: $1\n*قاعدة البيانات: $2", "smw-es-replication-error-maintenance-mode": "محرك النسخ المتماثل والبحث في [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>وضع الصيانة</b>] إجراء تغييرات على الكيانات والصفحات <b>غير</b> مرئي على الفور ولذلك التكرار لا يظهر في نتائج الاستعلام.", "smw-es-replication-error-no-connection": "مراقبة النسخ المتماثل غير قادرة على إجراء أية اختبارات لأنه لا يمكن تأسيس اتصال إلى كتلة إلاستيك سيرش.", "smw-es-replication-error-bad-request-exception": "طرح معالج اتصال إلاستيك سيرش استثناء طلب سيئ (\"400 conflict http error\") يشير إلى مشكلة مستمرة أثناء طلبات النسخ المتماثل والبحث.", "smw-es-replication-error-suggestions": "يوصى بتعديل أو إزالة كاش الصفحة لإزالة التعارض. لو استمرت المشكلة فتحقق من عنقود Elasticsearch نفسه (allocator, exceptions, disk space etc.).", "smw-es-replication-error-suggestions-maintenance-mode": "يُقترَح الاتصال بإداري الويكي للتحقق مما إذا كانت [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild إعادة بناء الفهرس] قيد التقدم حاليا أو <code>refresh_interval</code> لم يتم ضبطه على القيمة الافتراضية المتوقعة.", "smw-es-replication-error-suggestions-no-connection": "يُقترَح الاتصال بإداري الويكي والإبلاغ عن مشكلة \"عدم الاتصال\".", "smw-es-replication-error-suggestions-exception": "يُرجَى التحقق من السجلات للحصول على معلومات حول حالة إلاستيك سيرش ومؤشراته ومشاكل التكوين المحتملة.", "smw-es-replication-error-file-ingest-missing-file-attachment": "وجد رصد النسخ المتماثل أن \"$1\" ينقصه تعليق [[Property:File attachment|ملف مرفق]]  يشير إلى أن معالج استيعاب المحتوى لم يبدأ أو لم ينته.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "يُرجَى التأكد من جدولة وتنفيذ مهمة [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion استيعاب الملف] قبل إتاحة شرح وفهرس الملف.", "smw-report": "أ<PERSON><PERSON><PERSON>", "smw-legend": "عنوان تفسيري", "smw-datavalue-constraint-schema-category-invalid-type": "المخطط \"$1\" المشروح غير صالح لتصنيف؛ فهو يتطلب نوع \"$2\".", "smw-datavalue-constraint-schema-property-invalid-type": "المخطط \"$1\" المشروح غير صالح لخاصية؛ فهو يتطلب نوع \"$2\".", "smw-entity-examiner-associated-revision-mismatch": "مراجعة", "smw-entity-examiner-deferred-fake": "مزيف", "smw-indicator-revision-mismatch": "مراجعة", "smw-facetedsearch-intro-tab-explore": "استكشف", "smw-facetedsearch-intro-tab-search": "ب<PERSON><PERSON>", "smw-facetedsearch-explore-intro": "حد<PERSON> المجموعة وابدأ التصفح.", "smw-facetedsearch-profile-options": "خيارات الملف الشخصي", "smw-facetedsearch-size-options": "خيارات الصفحة", "smw-facetedsearch-order-options": "خيارات الطلب", "smw-facetedsearch-format-options": "خيارات العرض", "smw-facetedsearch-format-table": "جدول", "smw-facetedsearch-input-filter-placeholder": "ترشيح الطلبات حسب...", "smw-facetedsearch-no-filters": "لا فلترة", "smw-facetedsearch-no-filter-range": "لا يوجد نطاق تصفية.", "smw-search-placeholder": "بحث...", "smw-listingcontinuesabbrev": "(تابع)", "smw-showingresults": "معروض بالأسفل {{PLURAL:$1|<strong>1</strong> نتيجة}} بدءا من رقم <strong>$2</strong>."}