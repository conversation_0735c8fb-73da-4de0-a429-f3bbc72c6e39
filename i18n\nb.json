{"@metadata": {"authors": ["<PERSON><PERSON><PERSON>", "EdoAug", "Emilbk", "Event", "<PERSON>", "<PERSON><PERSON><PERSON>", "Je<PERSON><PERSON>", "<PERSON>", "Kghbln", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Nemo bis", "Nghtwlkr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SuperPotato", "아라"]}, "smw-desc": "<PERSON><PERSON><PERSON><PERSON> wikien din mer tilg<PERSON>g – for maskiner ''og'' mennes<PERSON> ([https://www.semantic-mediawiki.org/wiki/Help:User_manual online documentation])", "smw-error": "<PERSON><PERSON>", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] ble installert og slått på, men mangler en [https://www.semantic-mediawiki.org/wiki/Help:Upgrade oppgraderingsnøkkel].", "smw-upgrade-release": "Utgave", "smw-upgrade-progress": "<PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-progress-explain": "Et estimat for når oppgraderingen blir fullført er vanskelig å komme med fordi det avhenger av størrelsen til datalageret og den tilgjengelige maskinkraften, og kan ta lengre tid å fullføre for større wikier.\n\nKontakt den lokale administratoren din for mer informasjon om framgangen.", "smw-upgrade-progress-create-tables": "<PERSON><PERSON><PERSON><PERSON> (eller oppdaterer) tabeller og indekser …", "smw-upgrade-progress-post-creation": "<PERSON><PERSON><PERSON><PERSON> oppgaver etter opprettelse …", "smw-upgrade-progress-table-optimization": "<PERSON><PERSON><PERSON><PERSON> tabell<PERSON><PERSON> …", "smw-upgrade-progress-supplement-jobs": "<PERSON><PERSON> til supplementære jobber …", "smw-upgrade-error-title": "Feil » Semantic MediaWiki", "smw-upgrade-error-why-title": "Hvorfor ser jeg denne siden?", "smw-upgrade-error-why-explain": "Semantic MediaWikis interne databasestruktur har blitt endret og krever noen justeringer for å fungere optimalt. Det kan være flere årsaker, inkludert:\n* Ekstra fiksede egenskaper (som krever ekstra tabelloppsett) ble lagt til\n* En oppgradering innholder noen endringer i tabeller eller indekser som gjør det nødvendig å gripe inn før data kan aksesseres\n* Endringer i lagrings- eller spørringsmotoren", "smw-upgrade-error-how-title": "<PERSON><PERSON><PERSON> fi<PERSON>er jeg denne feilen?", "smw-upgrade-error-how-explain-admin": "En administrator (eller noen med administratorrettigheter) må kjøre enten MediaWikis [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] eller Semantic MediaWikis [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php].", "smw-upgrade-error-how-explain-links": "Du kan også sjekke følgende sider for mer hjelp:\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation Installasjonsinstruksjoner]\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting Hjelpesiden for problemløsing]", "smw-extensionload-error-why-title": "Hvorfor ser jeg denne siden?", "smw-extensionload-error-why-explain": "Utvidelsen ble <b>ikke</b> lastet med <code>enableSemantics</code> og ble i stedet slått på på andre måter, som ved å bruke <code>wfLoadExtension( 'SemanticMediaWiki' )</code> direkte.", "smw-extensionload-error-how-title": "<PERSON><PERSON><PERSON> fi<PERSON>er jeg denne feilen?", "smw-extensionload-error-how-explain": "For å slå på utvidelsen og unngå problemer med navneromserklæringer og ventende konfigurasjoner må man bruke <code>enableSemantics</code> som vil sørge for at påkrevde variabler er satt før utvidelsen lastes via <code>ExtensionRegistry</code>.\n\nTa en titt på hjelpesiden [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] for mer hjelp.", "smw-upgrade-maintenance-title": "Vedlikehold » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "Hvorfor ser jeg denne siden?", "smw-upgrade-maintenance-note": "Det foregår en [https://www.semantic-mediawiki.org/wiki/Help:Upgrade oppdatering] av utvidelsen [https://www.semantic-mediawiki.org/ Semantic MediaWiki] sammen med dens datalager, og vi ber deg om tålmodighet mens vedlikeholdet pågår før wikien kan gjøres tilgjengelig igjen.", "smw-upgrade-maintenance-explain": "Utvidelsen prøver å minimere påvirkningen og nedetiden ved å utsette de fleste vedlikeholdsoppgavene til etter <code>update.php</code>, men noen databaserelaterte endringer må fullføres først for å unngå inkonsistens i dataene. Det kan inkludere:\n* Endring av tabellstrukturer som å legge til eller endre eksisterende felter\n* Endring eller tillegg i tabellindekser\n* Kjøring av tabelloptimalisering (når det er slått på)", "smw-semantics-not-enabled": "Semantic MediaWiki-funksjonalitet har ikke blitt slått på på denne wikien.", "smw_viewasrdf": "RDF-kilde", "smw_finallistconjunct": " og", "smw-factbox-head": "… mer om «$1»", "smw-factbox-facts": "<PERSON><PERSON><PERSON>", "smw-factbox-facts-help": "Viser påstander og fakta som har blitt opprettet av en bruker", "smw-factbox-attachments": "<PERSON><PERSON><PERSON><PERSON>", "smw-factbox-attachments-value-unknown": "I/T", "smw-factbox-attachments-is-local": "Er lokal", "smw-factbox-attachments-help": "Viser tilgjengelige vedlegg", "smw-factbox-facts-derived": "<PERSON><PERSON><PERSON><PERSON> fakta", "smw-factbox-facts-derived-help": "Viser fakta som har blitt avledet fra regler eller ved hjelp av andre deduksjonsm<PERSON>der", "smw_isspecprop": "Denne egenskapen er en spesialegenskap på denne wikien.", "smw-concept-cache-header": "Bruk av mellomlager", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count Konseptmellomlageret] inneholder {{PLURAL:$1|'''en''' enhet|'''$1''' enheter}} ($2).", "smw-concept-no-cache": "Mellomlager ikke tilgjengelig.", "smw_concept_description": "Beskrivelse av konseptet «$1»", "smw_no_concept_namespace": "Konsepter kan kun defineres på sider i Konsept:-navnerommet.", "smw_multiple_concepts": "Hver konseptside kan kun ha én konseptdefinisjon.", "smw_concept_cache_miss": "Konseptet \"$1\" kan ikke brukes for øyeblikket da wikiens konfigurasjon krever at den blir satt opp frakoplet.\nHvis problemet ikke forsvinner etter en periode, spør sidens administratorer om å gjøre konseptet tilgjengelig.", "smw_noinvannot": "Inverse egenskaper kan ikke tildeles verdier.", "version-semantic": "Semantiske utvidelser", "smw_baduri": "<PERSON><PERSON><PERSON>, URI-er på formen «$1» er ikke tillatt.", "smw_printername_count": "Antall resultater", "smw_printername_csv": "CSV-eksport", "smw_printername_dsv": "DSV-eksport", "smw_printername_debug": "<PERSON><PERSON><PERSON><PERSON><PERSON>ø<PERSON> (for eksperter)", "smw_printername_embedded": "Innkapselert sideinnhold", "smw_printername_json": "JSON-eksport", "smw_printername_list": "Liste", "smw_printername_plainlist": "<PERSON><PERSON>e", "smw_printername_ol": "Nummerert liste", "smw_printername_ul": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON> tabell", "smw_printername_template": "Mal", "smw_printername_templatefile": "Malfil", "smw_printername_rdf": "RDF-eksport", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-limit": "Maksimalt antall resultater som skal returneres", "smw-paramdesc-offset": "Forskyvningen av det første resultatet", "smw-paramdesc-headers": "Viser overskriftene/egenskapsnavnene", "smw-paramdesc-mainlabel": "Hovedsidenavnets etikett", "smw-paramdesc-link": "Vis verdier som lenker", "smw-paramdesc-intro": "Tekst som skal vises før spørringsresultat, dersom det er noen", "smw-paramdesc-outro": "Te<PERSON>t som skal vises etter spørringsresultat, dersom det er noen", "smw-paramdesc-default": "Tekst som skal vises dersom det ikke er noen resultat for spørringen", "smw-paramdesc-sep": "Skilletegn mellom resultater", "smw-paramdesc-propsep": "Skilletegnet mellom egenskaper i en resultatoppføring", "smw-paramdesc-valuesep": "Skilletegnet mellom verdier for egenskapen til et resultat", "smw-paramdesc-showsep": "Vis skilletegn i starten av CSV-filen (\"sep=<verdi>\")", "smw-paramdesc-distribution": "Istedenfor å vise hver av verdiene vises antall verdier", "smw-paramdesc-distributionsort": "Sorter verdifordelingen mot forekomstantallet", "smw-paramdesc-distributionlimit": "Begrens verdifordelingen til antallet av visse verdier.", "smw-paramdesc-aggregation": "Angi hva aggregeringen skal relateres til", "smw-paramdesc-template": "Navnet på malen som skal vise frem utskriftene", "smw-paramdesc-columns": "<PERSON><PERSON><PERSON> kolo<PERSON> for å vise resultater", "smw-paramdesc-userparam": "En verdi sendt til hvert malkall, hvis en mal brukes", "smw-paramdesc-class": "En ekstra CSS-klasse som skal settes for lista", "smw-paramdesc-introtemplate": "Navnet på en mal som skal vises før spørringsresultatene, hvis det er noen", "smw-paramdesc-outrotemplate": "Navnet på en mal som skal vises etter spørringsresultatene, hvis det er noen", "smw-paramdesc-embedformat": "HTML-elementet brukt for å definere overskrifter", "smw-paramdesc-embedonly": "<PERSON><PERSON><PERSON> vis overskrifter", "smw-paramdesc-table-class": "En CSS-klasse til som kan brukes for tabellen", "smw-paramdesc-table-transpose": "Vis tabelloverskrifter vertikalt og resultater horisontalt", "smw-paramdesc-prefix": "Kontroller visning av navnerom i utskrifter", "smw-paramdesc-rdfsyntax": "RDF-syntaksen som blir brukt", "smw-paramdesc-csv-sep": "<PERSON><PERSON>", "smw-paramdesc-csv-valuesep": "Angir et verdiskilletegn", "smw-paramdesc-csv-merge": "Slå sammen rader og kolonner med identisk subjektidentifikator (første kolonne)", "smw-paramdesc-csv-bom": "Legg til en BOM (tegn for å varsle endianhet) på toppen av resultatfila", "smw-paramdesc-dsv-separator": "<PERSON><PERSON><PERSON><PERSON><PERSON> som blir brukt", "smw-paramdesc-dsv-filename": "Navnet på DSV-filen", "smw-paramdesc-filename": "Navnet på utdatafilen", "smw-smwdoc-description": "Viser en tabell over alle parametre som kan brukes for det angitte resultatformatet sammen med standardverdier og beskrivelser.", "smw-smwdoc-default-no-parameter-list": "Dette resultatformatet gir ikke formatspesifikke parametre.", "smw-smwdoc-par-format": "Resultatformatet for å vise parameterdokumentasjon.", "smw-smwdoc-par-parameters": "Hvilke parametre som skal vises. \"specific\" for dem som er lagt til av formatet, \"base\" for dem som er tilgjengelig i alle formater og \"all\" for begge.", "smw-paramdesc-sort": "Egenskap som spørringen skal sorteres etter", "smw-paramdesc-order": "Sorteringsrekkefølge for spørringen", "smw-paramdesc-searchlabel": "Tekst for å fortsette søket", "smw-paramdesc-named_args": "Nav<PERSON><PERSON>ne for malen", "smw-paramdesc-template-arguments": "<PERSON>t opp hvordan navngitte argumenter overføres til malen", "smw-paramdesc-import-annotation": "Noen flere annoterte data skal kopieres under parsing av et subjekt", "smw-paramdesc-export": "Eksportvalg", "smw-paramdesc-prettyprint": "En ryddig-utskrift som bruker ekstra innrykk og linjeskift", "smw-paramdesc-json-unescape": "Utdata skal inneholde rene \"\\\"-tegn og multibyte Unicode-tegn", "smw-paramdesc-json-type": "Serialiseringstype", "smw-paramdesc-source": "Alternativ spørringskilde", "smw-paramdesc-jsonsyntax": "JSON-syntaks som brukes", "smw-printername-feed": "RSS- og Atom-strøm", "smw-paramdesc-feedtype": "Strømtype", "smw-paramdesc-feedtitle": "<PERSON><PERSON><PERSON> som brukes som tittel på <PERSON>en", "smw-paramdesc-feeddescription": "‎<PERSON><PERSON><PERSON> som brukes som beskrivelse på matingen", "smw-paramdesc-feedpagecontent": "Sideinnholdet som vises av matingen", "smw-label-feed-description": "$1 $2 mating", "smw-paramdesc-mimetype": "Medietypen (MIME-typen) til resultatfila", "smw_iq_disabled": "Semantiske spørringer er slått av på denne wikien.", "smw_iq_moreresults": "… flere resultater", "smw_parseerror": "<PERSON><PERSON><PERSON><PERSON> verdi ble ikke for<PERSON>.", "smw_decseparator": ",", "smw_kiloseparator": "&nbsp;", "smw_notitle": "«$1» kan ikke brukes som sidenavn på denne wikien.", "smw_noproperty": "\"$1\" kan ikke brukes som en egenskap i denne wikien.", "smw_wrong_namespace": "Kun sider i navnerommet «$1» er tillatte her.", "smw_manytypes": "Mer enn én type definert for egenskapen.", "smw_emptystring": "<PERSON><PERSON> tekststrenger godtas ikke.", "smw_notinenum": "«$1» er ikke i listen ($2) over [[Property:Allows value|tillatte verdier]] for egenskapen «$3».", "smw-datavalue-constraint-error-allows-value-list": "«$1» er ikke i lista ($2) over [[Property:Allows value|tillatte verdier]] for egenskapen «$3».", "smw-datavalue-constraint-error-allows-value-range": "«$1» er ikke i intervallet til «$2» satt av begrensingen for [[Property:Allows value|tillatte verdier]] for egenskapen «$3».", "smw-constraint-error": "Begrensningsproblem", "smw-constraint-error-suggestions": "Sjekk de opplistede begrensningsbruddene og egenskapene sammen med deres annoterte verdier for å sjekke at alle begrensningskrav imøtekommes.", "smw-constraint-error-limit": "Listen vil inneholde maksimalt $1 {{PLURAL:$1|begrensning|begrensninger}}.", "smw_noboolean": "«$1» gjenkjennes ikke som en Boolean-verdi (true/false).", "smw_true_words": "true,t,yes,y,ja,j,sant,s", "smw_false_words": "false,f,no,n,nei,usant,u", "smw_nofloat": "«$1» er ikke et tall.", "smw_infinite": "Tall så store som «$1» støttes ikke.", "smw_unitnotallowed": "\"$1\" er ikke definert som en lovlig måleen<PERSON>t for denne egenskapen.", "smw_nounitsdeclared": "Ingen måleenhet ble definert for denne egenskapen.", "smw_novalues": "Ingen verdier angitt.", "smw_nodatetime": "<PERSON><PERSON><PERSON> «$1» ble ikke forst<PERSON>.", "smw_toomanyclosing": "«$1» opptrer for mange ganger i spørringen.", "smw_noclosingbrackets": "Bruken av «<nowiki>[[</nowiki>» i spørringen din ble ikke stengt av «]]».", "smw_misplacedsymbol": "Symbolet «$1» ble brukt på et sted hvor det ikke er nyttig.", "smw_unexpectedpart": "«$1»-delen av spørringen var uforståelig.\nResultatene kan være uventede.", "smw_emptysubquery": "En underspørring har ingen gyldige betingelser.", "smw_misplacedsubquery": "En underspørring ble brukt på et sted hvor underspørringer ikke tillates.", "smw_valuesubquery": "Undersp<PERSON>rringer støttes ikke for verdier av egenskapen «$1».", "smw_badqueryatom": "En del («<nowiki>[[…]]</nowiki>») av spørringen ble ikke forstått.", "smw_propvalueproblem": "Verdien av egenskapen «$1» ble ikke forstått.", "smw_noqueryfeature": "<PERSON><PERSON> spørringsfunksjoner ble ikke støttet i denne wikien, og deler av spørringen ble hoppet over ($1).", "smw_noconjunctions": "Konjunksjoner i spørringer støttes ikke i denne wikien, og deler av spørringen ble hoppet over ($1).", "smw_nodisjunctions": "Disjunksjoner i spørringer støttes ikke på denne wikien, og deler av spøringen ble utelatt ($1).", "smw_querytoolarge": "Følgende {{PLURAL:$2|spørringsbetingelse|$2 spørringsbetingelser}} kunne ikke vurderes på grunn av wikiens restriksjoner på spørringsstørrelse eller dybde: <code>$1</code>.\n\nSpørringsbetingelsene <code>$1</code> kunne ikke tas hensyn til på grunn av wikiens begrensninger i spørringsstørrelse eller dybde.", "smw_notemplategiven": "Oppgi en verdi for parameteret «mal» for at dette spørringsformatet skal fungere.", "smw_db_sparqlqueryproblem": "Sp<PERSON>rringen ga ikke noe resultat fra SPARQL-databasen. Denne feilen kan være midlertidig eller indikere en feil i database-programvaren.", "smw_db_sparqlqueryincomplete": "Det viste seg å være for vanskelig å gi et svar på spørringen og denne ble derfor avbrutt. Noen resultater kan mangle. <PERSON><PERSON> mulig, bruk en enklere spørring.", "smw_type_header": "Egenskaper av typen «$1»", "smw_typearticlecount": "Viser {{PLURAL:$1|én egenskap|$1 egenskaper}} som bruker denne typen.", "smw_attribute_header": "Sider som bruker egenskapen «$1»", "smw_attributearticlecount": "Viser {{PLURAL:$1|én side|$1 sider}} som bruker denne e<PERSON>pen.", "smw-propertylist-subproperty-header": "Underegenskaper", "smw-propertylist-redirect-header": "Synonymer", "smw-propertylist-error-header": "Sider med ugyldige tilordninger", "smw-propertylist-count": "Viser $1 {{PLURAL:$1|relatert element|relaterte elementer}}.", "smw-propertylist-count-with-restricted-note": "Viser $1 {{PLURAL:$1|relatert element|relaterte elementer}} (flere er tilgjengelige, men visningen er begrenset til \"$2\").", "smw-propertylist-count-more-available": "Viser $1 {{PLURAL:$1|relatert element|relaterte elementer}} (flere er tilgjengelige).", "specialpages-group-smw_group-maintenance": "Vedlikehold", "specialpages-group-smw_group-properties-concepts-types": "<PERSON><PERSON><PERSON><PERSON>, konsepter og typer", "specialpages-group-smw_group-search": "Bla gjennom og søk", "exportrdf": "Eksporter sider til RDF", "smw_exportrdf_docu": "Denne siden lar deg skaffe data fra en side i RDF-format.\nSkriv inn titler i tekstboksten nedenfor for å eksportere sider, én tittel per linje.", "smw_exportrdf_recursive": "Eksporter alle relaterte sider rekursivt.\nMerk at resultatet kan være stort.", "smw_exportrdf_backlinks": "Eksporter også alle sider som refererer til de eksporterte sidene.\nLager en RDF som kan gås gjennom.", "smw_exportrdf_lastdate": "<PERSON>kke eksporter sider som ikke ble endret siden gitte tidspunkt.", "smw_exportrdf_submit": "Eksporter", "uriresolver": "URI-løser", "properties": "Egenskaper", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "Følgende egenskaper brukes på wikien.", "smw_property_template": "$1 av type $2 ($3 {{PLURAL:$3|bruker}})", "smw_propertylackspage": "Alle egenskaper burde beskrives av en side.", "smw_propertylackstype": "Ingen type spesifisert for denne egens<PERSON> (bruker foreløpig typen $1).", "smw_propertyhardlyused": "<PERSON>ne egenskapen brukes nesten ikke på wikien.", "smw-property-name-invalid": "Egenskap $1 ikke ikke brukes (ugyldig egenskapsnavn).", "smw-property-name-reserved": "«$1» ble listet opp som et reservert navn og bør ikke brukes som en egenskap. Følgende [https://www.semantic-mediawiki.org/wiki/Help:Property_naming hjelpeside] kan inneholde informasjon om hvorfor dette navnet ble reservert.", "smw-sp-property-searchform": "Vis egenskaper som inneholder:", "smw-sp-property-searchform-inputinfo": "Det skilles mellom store og små bokstaver i inndata, og når disse brukes for filtrering, vil bare egenskaper vises som passer med betingelsen.", "smw-special-property-searchform": "Vis egenskaper som inneholder:", "smw-special-property-searchform-inputinfo": "Det skilles mellom store og små bokstaver i inndata, og når disse brukes for filtrering, vil bare egenskaper vises som oppfyller betingelsen.", "smw-special-property-searchform-options": "<PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "Ingen", "smw-special-wantedproperties-filter-unapproved": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved-desc": "Filtervalg brukt i forbindelse med autoritetsmodus.", "concepts": "Begreper", "smw-special-concept-docu": "Et [https://www.semantic-mediawiki.org/wiki/Help:Concepts konsept] kan betraktes som en \"dynamisk kategori\", dvs. som en samling sider som ikke opprettes manuelt, men som settes sammen av Semantic MediaWiki fra en beskrivelse av en gitt spørring.", "smw-special-concept-header": "Liste av konsepter", "smw-special-concept-count": "Følgende {{PLURAL:$1|konsept|$1 konsepter}} {{PLURAL:$1|}} vises.", "smw-special-concept-empty": "Ingen konsepter ble funnet.", "unusedproperties": "Ubrukte egenskaper", "smw-unusedproperties-docu": "<PERSON><PERSON> siden lister opp [https://www.semantic-mediawiki.org/wiki/Unused_properties ubenyttede egenskaper] som er opprettet uten at andre sider bruker dem. For en annen visning, se spesialsidene [[Special:Properties|alle]] eller [[Special:WantedProperties|ønskede egenskaper]].", "smw-unusedproperty-template": "$1 av type $2", "wantedproperties": "Ønskede egenskaper", "smw-wantedproperties-docu": "<PERSON><PERSON> siden lister opp [https://www.semantic-mediawiki.org/wiki/Wanted_properties ønskede egenskaper] som brukes i wikien, men har ennå ingen side som beskriver dem. For en annan visning, se spesialsidene [[Special:Properties|alle]] eller  [[Special:UnusedProperties|ubrukte egenskaper]].", "smw-wantedproperty-template": "$1 (brukt {{PLURAL:$2|én gang|$2 ganger}})", "smw-special-wantedproperties-docu": "<PERSON><PERSON> siden lister opp [https://www.semantic-mediawiki.org/wiki/Wanted_properties ønskede egenskaper] som brukes i wikien, men som ikke har noen side som beskriver dem. For en annan visning, se spesialsidene [[Special:Properties|alle]] eller  [[Special:UnusedProperties|ubrukte egenskaper]].", "smw-special-wantedproperties-template": "$1 (er brukt {{PLURAL:$2|én gang|$2 ganger}})", "smw_purge": "Gjenoppfrisk", "smw-purge-update-dependencies": "Semantic MediaWiki gjenoppfrisker den gjeldende siden på grunn av utdaterte avhengigheter den fant som krever en oppdateringer.", "smw-purge-failed": "Semantic MediaWiki prøvde å gjenoppfriske siden, men lyktes ikke", "types": "Typer", "smw_types_docu": "Liste over [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tilgjengelige datatyper] der hver [https://www.semantic-mediawiki.org/wiki/Help:Datatype type] representerer et unikt sett av attributter for å beskrive en verdi med tanke på lagring og visningskarakteristikker som arves av en gitt egenskap.", "smw-special-types-no-such-type": "«$1» er ukjent eller har ikke blitt angitt som en gyldig datatype.", "smw-statistics": "Semantisk statistikk", "smw-statistics-cached": "Semantisk statistikk (mellomlagret)", "smw-statistics-entities-total": "<PERSON><PERSON><PERSON><PERSON><PERSON> (totalt)", "smw-statistics-entities-total-info": "Et estimert antall rader for entiteter. Det inkluderer egenskaper, konsepter og annen registrert objektrepresentasjon som krever in ID-tildeling.", "smw-statistics-property-instance": "Egenskap {{PLURAL:$1|verdi|verdier}} (totalt)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Egenskap|Egenskaper}}]] (totalt)", "smw-statistics-property-total-info": "Totalt antall registrerte egenskaper.", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Egenskap|Egenskaper}} (totalt)", "smw-statistics-property-used": "{{PLURAL:$1|Egenskap|Egenskaper}} (bruk med minst én verdi)", "smw-statistics-property-page": "{{PLURAL:$1|Egenskap|Egenskaper}} (som har en egen side)", "smw-statistics-property-page-info": "Antall egenskaper som har en dedikert side og beskrivelse.", "smw-statistics-property-type": "{{PLURAL:$1|Egenskap|Egenskaper}} (som er tildelt en datatype)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>}}]] (innbygd, totalt)", "smw-statistics-query-format": "<code>$1</code>-format", "smw-statistics-query-size": "<PERSON><PERSON><PERSON><PERSON> på spørringen", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Konsept|Konsepter}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Konsept|Konsepter}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Underobjekt|Underobjekter}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Underobjekt|Underobjekter}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Datatype|Datatyper}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Egenskapsverdi|Egenskapsverdier}} ([[Special:ProcessingErrorList|upassende {{PLURAL:$1|annotering|annoteringer}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Egenskapsverdi|Egenskapsverdier}} (upassende {{PLURAL:$1|annotering|annoteringer}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Utdatert enhet|Utdaterte enheter}}]", "smw-statistics-delete-count-info": "En<PERSON>ter som har blitt merket for fjerning bør ryddes vekk regelmessig med de gitte vedlikeholdsskriptene.", "smw_uri_doc": "URI-løseren implementerer [$1 finning av W3C TAG-er på «httpRange-14»].\n<PERSON> sø<PERSON> for at en RDF-representasjon (for maskiner) eller ei wikiside (for mennesker) leveres avhengig av forespørselen.", "ask": "Semantisk søk", "smw-ask-help": "Denne seksjonen inneholder noen lenker for å forklare hvordan syntaksen <code>#ask</code> brukes.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Å velge sider] beskriver hvordan man velger sider og bygger vilk<PERSON>\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Search_operators Søkeoperatorer] lister opp tilgjengelige søkeoperatorer inkludert de for intervall- og jokertegnspørringer\n\n*[https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Å vise informasjon] gir et overblikk over bruken av printout-påstander og formateringsvalg", "smw_ask_sortby": "Sorter etter kolonne (valgfritt)", "smw_ask_ascorder": "Stigende", "smw_ask_descorder": "Synkende", "smw-ask-order-rand": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Finn resultater", "smw_ask_editquery": "<PERSON><PERSON> spø<PERSON>", "smw_add_sortcondition": "[Legg til sorteringsbetingelse]", "smw-ask-sort-add-action": "Legg til sorteringsbetingelse", "smw_ask_hidequery": "<PERSON><PERSON><PERSON><PERSON>ø<PERSON> (kompakt visning)", "smw_ask_help": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_queryhead": "Betingelse", "smw_ask_printhead": "Utvalg for utskrift", "smw_ask_printdesc": "(legg til et egenskapsnavn per linje)", "smw_ask_format_as": "Formater som:", "smw_ask_defaultformat": "standard", "smw_ask_otheroptions": "<PERSON>", "smw-ask-otheroptions-info": "Dette avsnittet innholder valg som påvirker utdata. Parameterbeskrivelser vises når musepekeren føres over dem.", "smw-ask-otheroptions-collapsed-info": "Vennligst bruk pluss-ikonet for å vise alle valgmuligheter", "smw_ask_show_embed": "<PERSON>is inn<PERSON> kode", "smw_ask_hide_embed": "Skjul innkapslet kode", "smw_ask_embed_instr": "For å bygge inn denne spørringen i en wikiside, bruk koden nedenfor.", "smw-ask-delete": "<PERSON><PERSON><PERSON>", "smw-ask-sorting": "Sortering", "smw-ask-options": "Valg<PERSON><PERSON><PERSON><PERSON>", "smw-ask-options-sort": "Sorteringsmuligheter", "smw-ask-format-options": "Format og alternativer", "smw-ask-parameters": "Parametre", "smw-ask-search": "<PERSON><PERSON><PERSON>", "smw-ask-debug": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-debug-desc": "Genererer feilsøkingsinformasjon for spørring", "smw-ask-no-cache": "Slå av spørringsmellomlager", "smw-ask-no-cache-desc": "Resultater uten spø<PERSON>lager", "smw-ask-result": "Resultat", "smw-ask-empty": "<PERSON><PERSON><PERSON> alle oppføringer", "smw-ask-download-link-desc": "Last ned spørringsresultatene i formatet $1", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Hjelp med det valgte formatet: $1", "smw-ask-condition-change-info": "Vilkå<PERSON> ble endret og søkemotoren må kjøre spørringen på nytt for å produsere resultatene som matcher de nye vilkårene.", "smw-ask-input-assistance": "Inndatahjelp", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Inndatahjelp] gis for feltene printout, sort og condition. Condition-feltet kreves for å bruke én av følgende prefiks:", "smw-ask-condition-input-assistance-property": "<code>p:</code> for å hente egenskapsforslag (f.eks. <code>[[p:<PERSON> …</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> for å hente kategoriforslag", "smw-ask-condition-input-assistance-concept": "<code>con:</code> for å hente konseptforslag", "smw-ask-format-change-info": "Formatet ble endret og spørringen må kjøres på nytt for å matche nye parametre og visualisere alternativer.", "smw-ask-format-export-info": "Det valgte formatet er et eksportformat uten visuell representasjon. <PERSON><PERSON><PERSON> obs på at resultatene kommer som nedlastinger.", "smw-ask-query-search-info": "<PERSON><PERSON><PERSON><PERSON><PERSON> <code><nowiki>$1</nowiki></code> ble besvart av {{PLURAL:$3|1=<code>$2</code> (fra mellomlager)|<code>$2</code> (fra mellomlager)|<code>$2</code>}} på $4 {{PLURAL:$4|sekund|sekunder}}.", "smw-ask-extra-query-log": "Spørringslogg", "smw-ask-extra-other": "<PERSON><PERSON>", "searchbyproperty": "<PERSON><PERSON><PERSON> etter e<PERSON>", "processingerrorlist": "Liste over prosesseringsfeil", "constrainterrorlist": "Begrensningsfeilliste", "propertylabelsimilarity": "Likhetsrapport for egenskapsetikett", "missingredirectannotations": "Mangler omdirigeringsannotasjoner", "smw-processingerrorlist-intro": "F<PERSON>lgende liste gir en oversikt over [https://www.semantic-mediawiki.org/wiki/Processing_errors prosesseringsfeil] som oppsto i forbindelse med [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Det er anbefalt å sjekke lista regelmessig og rette ugyldige verdiannoteringer.", "smw-constrainterrorlist-intro": "Følgende liste gir en oversikt over [https://www.semantic-mediawiki.org/wiki/Constraint_errors begrensningsfeil] som oppsto i tilknytning til [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Det anbefales å overvåke denne listen på jevnlig basis og rette ugyldige verdiannoteringer.", "smw-missingredirects-intro": "Følgende seksjon lister opp sider som mangler [https://www.semantic-mediawiki.org/wiki/Redirects omdirigering]-annotasjoner i Semantic MediaWiki (ved å sammenligne med informasjon lagret i MediaWiki) og gjenopprette disse annotasjonene manuelt, [https://www.semantic-mediawiki.org/wiki/Help:Purge gjenoppfriske] siden eller kjøre <code>rebuildData.php</code> (med alternativet <code>--redirects</code>).", "smw-missingredirects-list": "Sider som mangler annoteringer", "smw-missingredirects-list-intro": "Viser {{PLURAL:$1|éi side|$1 sider}} som mangler omdirigeringsannotasjoner.", "smw-missingredirects-noresult": "Ingen manglende omdirigeringsannotasjoner funnet.", "smw_sbv_docu": "<PERSON><PERSON><PERSON> etter alle sider som har en gitt egenskap og verdi.", "smw_sbv_novalue": "Skriv inn en gyldig verdi for egenskapen, eller vis alle egenskapsverdier for «$1».", "smw_sbv_displayresultfuzzy": "En liste over alle sider som har egenskapen «$1» med verdien «$2».\nSiden det bare ble noen få resultater, vises også nære verdier.", "smw_sbv_property": "Egenskap:", "smw_sbv_value": "Verdi:", "smw_sbv_submit": "Finn resultater", "browse": "Se gjennom wikien", "smw_browselink": "Bla gjennom egenskaper", "smw_browse_article": "Skriv inn navnet på siden du vil starte å bla fra.", "smw_browse_go": "Gå", "smw_browse_show_incoming": "Vis innkommende egenskaper", "smw_browse_hide_incoming": "Skjul innkommende egenskaper", "smw_browse_no_outgoing": "<PERSON>ne siden har ingen egens<PERSON>.", "smw_browse_no_incoming": "Ingen egenskaper lenker til denne siden.", "smw-browse-from-backend": "Informasjon hentes nå fra tjenermaskinen.", "smw-browse-intro": "Denne siden viser detaljer om et element eller  objektype, så vær vennlig å angi navnet på et objekt som skal sjekkes.", "smw-browse-invalid-subject": "Elementvalideringen ga en \"$1\"-feil.", "smw-browse-api-subject-serialization-invalid": "Elementet har et ugyldig serialiseringsformat.", "smw-browse-js-disabled": "Det er mistanke om at JavaScript er deaktivert eller utilgjengelig av annen årsak, og vi anbefaler derfor å bruke en nettleser hvor dette støttes. Andre valgmuligheter diskuteres på innstillingssiden for [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>].", "smw-browse-show-group": "<PERSON>is grupper", "smw-browse-hide-group": "Skjul grupper", "smw-noscript": "<PERSON><PERSON> siden eller handlingen trenger JavaScript for å virke. Aktiver JavaScript i nettleseren din eller bruk en nettleser der dette virker. Ta en titt på hjelpesiden [https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript] for mer hjelp.", "smw_inverse_label_default": "$1 av", "smw_inverse_label_property": "Invers egenskapsmerkelapp", "pageproperty": "Sideegenskapssøk", "pendingtasklist": "Liste over ventende oppgaver", "smw_pp_docu": "Enten en side og en egenskap, eller bare en egenskap for å hente alle tildelte verdier.", "smw_pp_from": "Fra side:", "smw_pp_type": "Egenskap:", "smw_pp_submit": "Finn resultater", "smw-prev": "de {{PLURAL:$1|$1}} forrige", "smw-next": "de {{PLURAL:$1|$1}} neste", "smw_result_prev": "<PERSON><PERSON><PERSON>", "smw_result_next": "Neste", "smw_result_results": "Resultater", "smw_result_noresults": "<PERSON><PERSON><PERSON>, ingen resultater funnet", "smwadmin": "Kontrollpanel for Semantic MediaWiki", "smw-admin-statistics-job-title": "Jobbstatistikk", "smw-admin-statistics-job-docu": "Jobb-statistikken viser informasjon om planlagte Semantic MediaWiki-jobber som enda ikke har blitt kjørt. Antall jobber kan være unøyaktig angitt eller inneholde mislykkede forsøk, se [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue manual] for mer informasjon.", "smw-admin-statistics-querycache-title": "<PERSON><PERSON><PERSON><PERSON>smellomlager", "smw-admin-statistics-querycache-disabled": "[https://www.semantic-mediawiki.org/wiki/QueryCache QueryCache] har ikke blitt aktivisert på denne wikien, så derfor er ingen statistikk tilgjengelig.", "smw-admin-statistics-querycache-legend": "Mellomlager-statistikken skal inneholde foreløpige kumulative og avledede data som inneholder: \n* «misses» som er det samlede antall forsøk på å hente data fra mellomlagret med uoppnåelige svar, som fremtvinger direkte-henting fra kodelageret (database, triple-store etc.)\n* «deletes» som er det totale antall mellomlager-evakueringer (enten gjennom gjenoppfrisking eller spørringsavhengighet) \n* «hits» som er det totale antall mellomlager-hentinger fra enten fra enten innbygde (spørringer kalt fra en wikiside) eller ikke-innbygde (dersom aktivisert, etterspurt av sider som Special:Ask eller API)\n* «medianRetrievalResponseTime» er en orienteringsverdi for median responstid (i sekunder)  for mellomlagrede og ikke-mellomlagrede hentingsforespørsler over tidsrommet til samlingsprosessen\n* «noCache» angir antall ikke-prøv<PERSON> forespørsler (limit=0-spørringer, «no-cache»-alternativ etc.) for å hente resultater fra mellomlagret", "smw-admin-statistics-section-explain": "Seksjonen gir ekstra statistikk for administratorer.", "smw-admin-statistics-semanticdata-overview": "Oversikt", "smw-admin-permission-missing": "Tilgangen til denne siden har blitt blokkert på grunn av manglende tillatelser, se [https://www.semantic-mediawiki.org/wiki/Help:Permissions tillatelser]-hjelpesiden for detaljer om nødvendige innstillinger.", "smw-admin-setupsuccess": "Lagringsmotoren har blitt initialisert.", "smw_smwadmin_return": "Gå tilbake til $1", "smw_smwadmin_updatestarted": "En ny oppdateringsprosess for oppfriskning av de semantiske dataene ble startet.\nAlle lagrede data vil bli bygd på nytt eller reparert der det trengs.\nDu kan følge med på fremgangen til oppdateringen på denne spesialsiden.", "smw_smwadmin_updatenotstarted": "Det kjører allerede en oppdateringsprosess.\nStarter derfor ikke enda en.", "smw_smwadmin_updatestopped": "Alle eksisterende oppdateringsprosesser har blitt stoppet.", "smw_smwadmin_updatenotstopped": "For å stoppe den kjørende oppdateringsprosessen må du markere avkrysningsboksen for å vise at du er helt sikker.", "smw-admin-docu": "Denne spesialsiden hjelper deg under innstallasjon, oppgradering og vedlikehold av <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a> og tilbyr flere administrative funksjoner og oppgaver i tillegg til statistikk. Husk å ta sikkerhetskopier av viktige data før du starter disse administrasjonsfunksjonene.", "smw-admin-environment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-db": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-db-preparation": "Tabellinitialiseringen er underveis og det kan ta noe tid før resultater vises avhengig av størrelse og tabelloptimalisering.", "smw-admin-dbdocu": "Semantic MediaWiki krever sin egen databasestruktur (og er uavhengig av MediaWiki og påvirker derfor ikke resten av MediaWiki-installasjonen) forå  lagre semantiske data.\nDenne initialiseringsfunksjonen kan kjøres flere ganger uten å gjøre noen skade, men den trenger kun å kjøres en gang i forbindelse med installering eller oppgradering.", "smw-admin-permissionswarn": "Dersom operasjonen mislykkes med SQL-feil, har databasebrukeren som wikien din anvender sannsynligvis manglende rettigheter (sjekk LocalSettings.php).\nEnten må denne brukeren gis tilstrekkelige rettigheter til å opprette og slette tabeller, eller en må midlertidig skrive inn innloggingsinformasjonen til database-roten i LocalSettings.php, eller en må bruke vedlikeholdsskriptet <code>setupStore.php</code> som har rettighetene til LocalSettings.php.", "smw-admin-dbbutton": "Initialiser eller oppgrader tabeller", "smw-admin-announce": "Kunngjør din wiki", "smw-admin-announce-text": "Dersom wikien er offentlig, kan du registrere den på <a href=\"https://wikiapiary.com\">WikiApiary</a>, wikien som holder oversikt over wikier.", "smw-admin-deprecation-notice-title": "Meldinger om funksjonalitet som vil bli fjernet", "smw-admin-deprecation-notice-docu": "Det følgende avsnittet inneholder innstillinger som har blitt satt som utgående eller utgått, men har blitt oppdaget å være aktive på denne wikien. Det må påregnes at i en fremtidig versjon vil støtte for disse innstillingsmulighetene være fjernet.", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> er utgående og blir fjernet i $2", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> vil fjerne (el<PERSON>) følgende {{PLURAL:$2|alternativ|alternativer}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> er foreldet og blir fjernet i $2", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ble erstattet av <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ble erstattet av <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|alternativ|alternativer}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> erstattes av <code>$2</code>", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ble fjernet i $2", "smw-admin-deprecation-notice-title-notice": "Foreldede innstillinger", "smw-admin-deprecation-notice-title-notice-explanation": "<b>Foreldede innstillinger</b> viser innstillinger som har blitt oppdaget brukt på denne wikien og som kommer til å fjernes eller endres i en framtidig utgave.", "smw-admin-deprecation-notice-title-replacement": "Erstattede eller omdøpte innstillinger", "smw-admin-deprecation-notice-title-replacement-explanation": "<b><PERSON><PERSON><PERSON><PERSON> eller omdøpte innstillinger</b> inneholder innstillinger som er blitt omdøpt eller endret på annen måte, og det anbefales snarest å oppdatere navn og format.", "smw-admin-deprecation-notice-title-removal": "Fjernede innstillinger", "smw-admin-deprecation-notice-title-removal-explanation": "<b>Fjernede innstillinger</b> identisiferer innstillinger som ble fjernet i en tidligere utgave, men bruk av dem på denne wikien pågår fortsatt.", "smw-admin-deprecation-notice-section-legend": "Tegnforklaring", "smw-smwadmin-refresh-title": "Datareparering og -oppdatering", "smw_smwadmin_datarefresh": "Datagjenoppbygging", "smw_smwadmin_datarefreshdocu": "Det er mulig å gjenopprette alle Semantic MediaWiki-data basert på det nåværende innholdet på wikien.\nDette kan være nyttig for å reparere ødelagte data eller for å oppfriske data hvis det interne formatet har blitt endret på grunn av programvareoppdateringer.\nOppdateringen blir utført side for side og vil ikke bli fullført umiddelbart.\nDet følgende viser om en oppdatering er i gang og lar deg starte eller stoppe oppdateringer (ved mindre denne muligheten er gjort utilgjengelig av sidens administratorer).", "smw_smwadmin_datarefreshprogress": "<strong>En oppdatering er allerede i gang.</strong>\nDet er normalt at oppdateringen går tregt fremover siden den bare oppdaterer data i små biter hver gang en bruker besøker wikien.\nFor å fullføre denne oppdateringen raskere kan du starte MediaWiki-vedlikeholdsskriptet <code>runJobs.php</code> (bruk valget <code>--maxjobs 1000</code> for å avgrense antallet oppdateringer som gjøres sammenhengende).\nAnslått fremdrift for gjeldende oppdatering:", "smw_smwadmin_datarefreshbutton": "Planlegg gjenoppbygging av data", "smw_smwadmin_datarefreshstop": "Stopp den<PERSON>", "smw_smwadmin_datarefreshstopconfirm": "Ja, jeg er {{GENDER:$1|sikker}}.", "smw-admin-job-scheduler-note": "Oppgaver (som er slått på) i denne seksjonen utføres via jobbkøen for å unngå vranglåssituasjoner under kjøringen. [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue Jobbkøen] er ansvarlig for prosesseringen og det er kritisk at vedlikeholdsskriptet <code>runJobs.php</code> har riktig kapasitet (se også konfigurasjonsparameteren <code>$wgRunJobsAsync</code>).", "smw-admin-outdateddisposal-title": "Forkasting av utdaterte elementer", "smw-admin-outdateddisposal-intro": "<PERSON>en aktiviteter (som en endring av en egenskap sin type, fjerning av wiki-sider, eller verdikorreksjoner) resulterer i [https://www.semantic-mediawiki.org/wiki/Outdated_entities utdaterte elementer], og det anbefales å fjerne disse med jevne mellomrom for å frigjøre databaseplass.", "smw-admin-outdateddisposal-active": "En ryddejobb av utdaterte entiteter er planlagt.", "smw-admin-outdateddisposal-button": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> r<PERSON>", "smw-admin-feature-disabled": "<PERSON>ne funksjonen har blitt slått av på denne wikien. Se den <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">tilknyttede</a> hjelpesiden for detaljer.", "smw-admin-propertystatistics-title": "Ombygging av egenskapsstatistikk", "smw-admin-propertystatistics-intro": "Omb<PERSON>gger hele statistikken for bruken av egenskaper og derigjennom oppdaterer og korrigerer [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count bruksantallet] av egenskaper.", "smw-admin-propertystatistics-active": "En ombyggingsjobb for egenskapsstatistikk har blitt planlagt.", "smw-admin-propertystatistics-button": "Sett opp ombygging av statistikken", "smw-admin-fulltext-title": "Ombygging av fulltekstsøk", "smw-admin-fulltext-intro": "G<PERSON><PERSON><PERSON>bygger søkeindeksen fra egenskapstabeller med en aktivisert [https://www.semantic-mediawiki.org/wiki/Full-text fulltekstsøkbasert] datatype.", "smw-admin-fulltext-active": "En fulltekstsøkbasert gjenoppbyggingsjobb har blitt planlagt.", "smw-admin-fulltext-button": "Sett opp en fulltekstbasert gjenoppbygging", "smw-admin-support": "<PERSON><PERSON> støtte", "smw-admin-supportdocu": "Diverse ressurser er tilgjengelige for å hjelpe deg om du skulle få problemer:", "smw-admin-installfile": "<PERSON><PERSON> du får problemer med installeringen, studer retningslinjene i <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">INSTALL-filen</a> og <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">installeringssiden</a>.", "smw-admin-smwhomepage": "Den komplette brukerdokumentasjonen til Semantic MediaWiki finnes på <b><a href=\"https://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Feil kan rapporteres til <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">saksflytverktøyet</a>. <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">Feilrapportsiden</a> gir retningslinjer om hvordan en skriver en effektiv feilrapport.", "smw-admin-questions": "<PERSON><PERSON> du har ytterligere spø<PERSON><PERSON><PERSON><PERSON> eller forslag, bli med i diskusjonen på <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">Semantic MediaWikis brukerforum</a>.", "smw-admin-other-functions": "<PERSON>", "smw-admin-statistics-extra": "Statistikkfunksjoner", "smw-admin-statistics": "Statistikk", "smw-admin-supplementary-section-title": "Ekstra funksjoner", "smw-admin-supplementary-section-subtitle": "Støttede kjernefunksjoner", "smw-admin-supplementary-section-intro": "Dette avsnittet har tilleggsfunksjoner utover det som behøves for vedlikehold, og det er mulig at noen funksjoner som angis i [https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions dokumentasjonen] er begrenset eller utilgjengelig og derfor ikke kan brukes på denne wikien.", "smw-admin-supplementary-settings-title": "Konfigurasjon og innstillinger", "smw-admin-supplementary-settings-intro": "<u>$1</u> viser parametere som definerer oppførselen til Semantic MediaWiki", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "Driftsstatistikk", "smw-admin-supplementary-operational-statistics-short-title": "operasjonsstatistikk", "smw-admin-supplementary-operational-statistics-intro": "Viser et utvidet sett av <u>$1</u>", "smw-admin-supplementary-idlookup-title": "Elementoppslag og forkasting", "smw-admin-supplementary-idlookup-short-title": "entitetsoppslag og forkasting", "smw-admin-supplementary-idlookup-intro": "<PERSON><PERSON><PERSON> en enkel <u>$1</u>-funksjon", "smw-admin-supplementary-duplookup-title": "Dupliser elementoppslag", "smw-admin-supplementary-duplookup-intro": "<u>$1</u> for å finne entiteter som er kategorisert som duplikater for den valgte tabellmatrisen", "smw-admin-supplementary-duplookup-docu": "<PERSON>ne siden angir elementer fra de valgte tabellene som har blitt kategorisert som [https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities duplikater]. Duplikatelementer bør, om no<PERSON><PERSON>e, bare opptre i sjeldne tilfeller, ev. forårsaket av en avsluttet prosess under en databaseoppdatering eller en mislykket tilbakerulling fra sikkerhetskopi.", "smw-admin-supplementary-operational-statistics-cache-title": "Mellomlagerstatistikk", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> viser et utvalgt sett av mellomlagerstatistikk", "smw-admin-supplementary-operational-table-statistics-title": "Tabellstatistikk", "smw-admin-supplementary-operational-table-statistics-short-title": "tabellstatistikk", "smw-admin-supplementary-operational-table-statistics-intro": "Genererer <u>$1</u> for et valgt sett av tabeller", "smw-admin-supplementary-operational-table-statistics-explain": "Denne seksjonen inneholder den vasgte tabellstatistikken for å hjelpe administratorer og andre datakuratorer med å gjøre informerte valg om statusen til bakstykket og lagringsmotoren.", "smw-admin-supplementary-operational-table-statistics-legend": "Tegnforklaringen beskriver noen av nøklene som brukes for tabellstatistikken og inkluderer:", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> totalt antall rader i en tabell", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "* <code>last_id</code> den siste ID-en som er i bruk for tiden\n* <code>duplicate_count</code> antall duplikater funnet i id_table (se også [[Special:SemanticMediaWiki/duplicate-lookup|Oppslag på duplikatentiteter]]) \n* <code>rows.rev_count</code> antall rader som har en revision_id tildelt som indikerer en direkte wikisidelenke \n* <code>rows.smw_namespace_group_by_count</code> antall aggregerte rader for navnerom brukt i tabellen\n* <code>rows.smw_proptable_hash.query_match_count</code>antall spørringsunderobjekter med en tilsvarende tabellreferanse\n* <code>rows.smw_proptable_hash.query_null_count</code> antall spørringsunderobjekter uten en tabellreferanse (ulenket, flytende referanse)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "* <code>unique_terms_occurrence_in_percent</code> prosentandel begreper som er unike (en lav prosent indikerer at repetetive begreper er i tabellinnholdet og indeksen)\n* <code>rows.terms_occurrence.single_occurrence_total_count</code> antall begreper som kun dukker opp én gang\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> antall begreper som dukker opp mer enn én gang", "smw-admin-supplementary-elastic-version-info": "Versjon", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> viser detaljer om innstillings- og indeksstatistikken", "smw-admin-supplementary-elastic-docu": "<PERSON>ne siden inneholder informasjon om innstillings-, mapping-, helse- og indeksstatistikk relatert til en ElasticSearch-klynge som er koblet til Semantic MediaWiki og dens [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "<PERSON><PERSON><PERSON><PERSON> funksjoner", "smw-admin-supplementary-elastic-settings-title": "Innstillinger (indekser)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> brukes av ElasticSearch for å behandle Semantic MediaWiki-indekser", "smw-admin-supplementary-elastic-mappings-title": "<PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> for å liste opp indekser og feltmappinger", "smw-admin-supplementary-elastic-mappings-docu": "<PERSON>ne siden inneholder feltmappingdetaljer brukt av den gjeldende indeksen. Det anbefales å overvåke mappingene i samband med <code>index.mapping.total_fields.limit</code> (angir maksimalt antall felter i en indeks som er tillatt).", "smw-admin-supplementary-elastic-mappings-docu-extra": "<code>property_fields</code> refererer til antall indekserte kjernefelter mens <code>nested_fields</code> refererer til et akkumulertantall ekstra felter tildelt et kjernefelt for å støtte spesifikke strukturerte søkemønstre.", "smw-admin-supplementary-elastic-mappings-summary": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-mappings-fields": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-nodes-title": "Noder", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> viser nodestatistikk", "smw-admin-supplementary-elastic-indices-title": "Indekser", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> gir en oversikt over tilgjengelige indekser og deres statistikk", "smw-admin-supplementary-elastic-statistics-title": "Statistikk", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> viser indeksnivåstatistikk", "smw-admin-supplementary-elastic-statistics-docu": "<PERSON>ne sider gir en innsikt i indeksstatistikk for forskjellige operasjoner som skjer på indeksnivå, de returnerte statistikkene aggregeres med primære og totale aggregeringer. [https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html Hjelpesiden] inneholder detaljerte beskrivelser over tilgjengelige indeksstatistikker.", "smw-admin-supplementary-elastic-status-replication": "Replikasjonsstatus", "smw-admin-supplementary-elastic-status-last-active-replication": "Siste aktive replikasjon: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Oppdateringsintervall: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Etterslep på gjenopprettelsesjobber: $1 (estimat)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Etterslep på ingest (fil): $1 (estimat)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replikasjon låst: $1 (gjenoppbygging pågår)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Replikasjonsovervåkning (aktiv): $1", "smw-admin-supplementary-elastic-replication-header-title": "Replikasjonsstatus", "smw-admin-supplementary-elastic-replication-function-title": "Replikasjon", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> viser informasjon om mislykkede replikasjoner", "smw-admin-supplementary-elastic-replication-docu": "Denne siden gir informasjon om [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring replikasjonsstatusen] til entiteter som har blitt rapportert å ha problemer med ElasticSearch-klyngen. Det anbefales å gå gjennom oppføringer og gjenoppfriske innholdet for å bekrefte at det var et midlertidig problem.", "smw-admin-supplementary-elastic-replication-files-docu": "Det burde merkes at for fillisten må [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion file ingest]-jobben kjøres først og må fullføre prosesseringen.", "smw-admin-supplementary-elastic-replication-files": "Filer", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON>", "smw-admin-supplementary-elastic-endpoints": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-config": "Kon<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-no-connection": "Wikien er for tiden '''ikke i stand''' til å etablere en tilkobling til ElasticSearch-k<PERSON><PERSON>, kontakt wikiens administrator for å undersøke problemet siden det vanskeliggjør systemets muligheter til indeksering og spørring.", "smw-list-count": "Listen inneholder $1 {{PLURAL:$1|oppføring|oppføringer}}.", "smw-property-label-uniqueness": "Etiketten «$1» hadde treff på minst en egenskaprepresentasjon til. Vær vennlig å studere [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness hjelpesiden] om hvordan du kan løse denne saken.", "smw-property-label-similarity-title": "Likhetsrapport for egenskapsetikett", "smw-property-label-similarity-intro": "<u>$1</u> regner ut likheter innenfor eksisterende egenskapsetiketter", "smw-property-label-similarity-threshold": "Terskelverdi:", "smw-property-label-similarity-type": "Vis type-ID", "smw-property-label-similarity-noresult": "Ingen resultater ble funnet for de valgte mulighetene.", "smw-property-label-similarity-docu": "Denne siden sammenlikner og rapporterer [https://www.semantic-mediawiki.org/wiki/Property_similarity syntaks-samsvar] (i motsetning til semantikk-samsvar) mellom to egenskapsetiketter som kan bidra til å filtrere bort feilskrevne eller ekvivalente egenskaper som representerer det samme begrepet (se spesialsiden  [[Special:Properties|Properties]] for å oppklare begrep og bruk av rapporterte egenskaper). Terskelverdien kan justeres i den hensikt å utvide eller innskrenke samsvarsavstanden. <code>[[Property:$1|$1]]</code> brukes for å unnta egenskaper fra analysen.", "smw-admin-operational-statistics": "<PERSON>ne siden inneholder driftsstatistikk samlet fra Semantic MediaWiki eller relaterte funksjoner. En utvidet liste av wikispesifikk statistikk kan finnes [[Special:Statistics|<b>her</b>]].", "smw_adminlinks_datastructure": "Datastruktur", "smw_adminlinks_displayingdata": "Datavisning", "smw_adminlinks_inlinequerieshelp": "<PERSON><PERSON><PERSON><PERSON> for innebygde spørringer", "smw-page-indicator-usage-count": "Estimert [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count bruksantall]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|<PERSON><PERSON><PERSON>|System}}-opprettet egenskap", "smw-property-indicator-last-count-update": "Estimert bruksantall\nSist oppdatert: $1", "smw-concept-indicator-cache-update": "Mellomlageropptelling\nSist oppdatert: $1", "smw-createproperty-isproperty": "Det er en egenskap av type $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Den tillatte verdien|De tillatte verdiene}} for denne egenskapen er:", "smw-paramdesc-category-delim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-paramdesc-category-template": "En mal til bruk for å formatere elementene", "smw-paramdesc-category-userparam": "En parameter som gis til malen", "smw-info-par-message": "Melding å vise frem.", "smw-info-par-icon": "Ikon for å vise frem enten \"info\" eller \"advarsel\".", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Generelle valg", "prefs-extended-search-options": "Utvidet søk", "prefs-ask-options": "Semantisk søk", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] (og relaterte utvidelser) for å muliggjøre individuelle tilpasninger for en gruppe utvalgte funksjoner. En liste over individuelle innstillinger med deres beskrivelse og karakteristikker er tilgjengelig på følgende [https://www.semantic-mediawiki.org/wiki/Help:User_preferences hjelpeside].", "smw-prefs-ask-options-tooltip-display": "Vis parameterteksten som hinttekst for [[Special:Ask|spørringsbyggeren]].", "smw-prefs-ask-options-compact-view-basic": "Slå på grunnleggende kompakt visning", "smw-prefs-help-ask-options-compact-view-basic": "<PERSON><PERSON> denne er slått på vises et redisert sett av lenker i den kompakte visningen til Special:Ask.", "smw-prefs-general-options-time-correction": "Aktiver tidskorreksjonen for spesialsider ved å bruke den lokale [[Special:Preferences#mw-prefsection-rendering|klokkeslettforskyvnings]]-preferansen.", "smw-prefs-general-options-jobqueue-watchlist": "<PERSON><PERSON>overvåkningslisten i min personlige meny", "smw-prefs-help-general-options-jobqueue-watchlist": "<PERSON><PERSON> denne er slått på vises en [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist liste] over ventende utvalgte jobber sammen med deres estimerte køstørrelser.", "smw-prefs-general-options-disable-editpage-info": "Slå av introduksjonsteksten på redigeringssiden", "smw-prefs-general-options-disable-search-info": "Slå av syntaksstøtteinformasjonen på standardsøkesiden", "smw-prefs-general-options-suggester-textinput": "Slå på inndataassistanse for semantiske entiteter", "smw-prefs-help-general-options-suggester-textinput": "<PERSON><PERSON> denne er slått på kan man bruke [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance inndataassistanse] for å finne egenskaper, konsepter og kategorier fra en inndatakontekst.", "smw-prefs-general-options-show-entity-issue-panel": "Vis entitetsproblempanelet", "smw-prefs-help-general-options-show-entity-issue-panel": "<PERSON><PERSON> denne er slått på kjøres integritetssjekker på hver side og viser [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel entitetsproblempanelet].", "smw-ui-tooltip-title-property": "Egenskap", "smw-ui-tooltip-title-quantity": "Størrelseskonvertering", "smw-ui-tooltip-title-info": "Informasjon", "smw-ui-tooltip-title-service": "Tjenestelenker", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-error": "<PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-note": "Merknad", "smw-ui-tooltip-title-legend": "Tegnforklaring", "smw-ui-tooltip-title-reference": "Referanse", "smw_unknowntype": "Typen «$1» for denne egenskapen er ugyldig", "smw-concept-cache-text": "Konseptet består av totalt $1 {{PLURAL:$1|side|sider}}, og ble sist oppdatert $3, $2.", "smw_concept_header": "Sider av konseptet «$1»", "smw_conceptarticlecount": "Viser nedenfor {{PLURAL:$1|én side|$1 sider}}.", "smw-qp-empty-data": "Forespurte data kan ikke vises på grunn av utilstrekkelige søkekriterier.", "right-smw-admin": "Tilgang til administrasjonsoppgaver (Semantic MediaWiki)", "right-smw-patternedit": "Redigeringstilgang for å vedlikeholde tillatte regulære uttrykk og mønstre (Semantic MediaWiki)", "right-smw-pageedit": "Rediger tilgang til <code>beskyttede</code> annoteringssider (Semantic MediaWiki)", "right-smw-schemaedit": "Redigere [https://www.semantic-mediawiki.org/wiki/Help:<PERSON><PERSON><PERSON> s<PERSON>] (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "<PERSON>å tilgang til jobbkøens [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist overvåkningslistefunksjoner] (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "<PERSON>å tilgang til informasjon om en entitetsassosiert revisjonsfeilmatching (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "Vise [https://www.semantic-mediawiki.org/wiki/Help:Edit_help redigeringshjelp] (Semantic MediaWiki)", "restriction-level-smw-pageedit": "beskyttet (kun berettigede brukere)", "action-smw-patternedit": "rediger regular expressions brukt av Semantic MediaWiki", "action-smw-pageedit": "rediger sider annotert med <code>Er sperret mot redigering</code> (Semantic MediaWiki)", "group-smwadministrator": "Administratorer (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrator (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:<PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator": "<PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|kurator (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smweditor": "Redaktører (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|redaktør (Semantic MediaWiki)}}", "grouppage-smweditor": "{{ns:project}}:<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantic MediaWiki)", "action-smw-admin": "gå til administrasjonsoppgaver for Semantic MediaWiki", "action-smw-ruleedit": "redigere regelsider (Semantic MediaWiki)", "smw-property-namespace-disabled": "Egenskapen [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks navnerom] er slått av, så det er ikke mulig å erklære typekarakteristikk eller egenskapsspesifikke karakteristikker for denne egenskapen.", "smw-property-predefined-default": "«$1» er en forhåndsdefinert egenskap av typen $2.", "smw-property-predefined-common": "<PERSON>ne egenskapen er forhåndsinstallert (også kjent som [https://www.semantic-mediawiki.org/wiki/Help:Special_properties spesialegenskap]) og kommer med ekstra administrative privilegier, men kan ellers brukes på samme måte som vanlige [https://www.semantic-mediawiki.org/wiki/Property brukerdefinerte egenskaper].", "smw-property-predefined-ask": "«$1» er en forhåndsdefinert egenskap som representerer metainformasjon (i form av [https://www.semantic-mediawiki.org/wiki/Subobject underobjekt]) om individuelle spørringer og fremskaffes av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "«$1» er en forhåndsdefinert egenskap som samler antall betingelser brukt i en spørring og fremskaffes av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "«$1» er en forhåndsdefinert egenskap som informerer om dybden i en spørring og fremskaffes av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "Det er en numerisk verdi beregnet på grunnlag av nøsting via en underspørring, egenskapskjeder og tilgjengelige  beskrivelseselementer med utførelsen av en spørring som er begrenset av <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgQMaxDepth $smwgQMaxDepth]</code>-konfigurasjonsparameteren.", "smw-property-predefined-askpa": "«$1» er en forhåndsdefinert egenskap som beskriver parametre som påvirker resultatet fra en spørring fremskaffet av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "Den er en del av en samling egenskaper som angir en [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler spørreprofil].", "smw-sp-properties-docu": "<PERSON>ne siden viser [https://www.semantic-mediawiki.org/wiki/Property egenskapene] og hvordan de er brukt på denne wikien. For en oppdatert statistikk anbefales det at [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics statistikken] sitt vedlikeholdsskript kjøres regelmessig. For en variert visning, se  [[Special:UnusedProperties|ubrukte]] eller [[Special:WantedProperties|ønskede egenskaper]] på spesialsidene.", "smw-sp-properties-cache-info": "Dataene i listen er blitt hentet fra [https://www.semantic-mediawiki.org/wiki/Caching et mellomlager], og ble sist oppdatert $1.", "smw-sp-properties-header-label": "Liste av egenskaper", "smw-admin-settings-docu": "Viser en liste av alle standard og lokaliserte innstillinger som er relevante for omgivelsene til Semantic MediaWiki. For detaljer om individuelle innstillinger, vennligst slå opp på [https://www.semantic-mediawiki.org/wiki/Help:Configuration konfigurerings]-hjelpesiden.", "smw-sp-admin-settings-button": "Generer innstillingslisten", "smw-admin-idlookup-title": "Oppslag", "smw-admin-idlookup-docu": "Dette ansnittet viser tekniske detaljer om individuelle elementer (wikiside, underobjekt, egenskap, etc.) i Semantic MediaWiki. Brukeren oppgir en numerisk ID eller en stringverdi som sammenliknes med det valgte feltet. Merk at slike ID-referanser ikke må forveksles med MediaWiki-sider eller redigerings-ID-er.", "smw-admin-iddispose-title": "Forkasting", "smw-admin-iddispose-docu": "<PERSON> bør merkes at forkastingsoperasjonen er ubegrenset og vil fjerne entiteten fra lagringsmotoren sammen med alle dens referanser i ventende tabeller, hvis det bekreftes. Utfør denne oppgaven med '''forsiktighet''' og kun etter at du har gått gjennom [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal dokumentasjonen].", "smw-admin-iddispose-done": "ID-en «$1» ble fjernet fra lagringsbakstykket.", "smw-admin-iddispose-references": "ID-en «$1» har {{PLURAL:$2|ingen aktive referanser|minst én aktiv referanse}}:", "smw-admin-iddispose-references-multiple": "Liste over treff med minst én aktiv referanseoppføring.", "smw-admin-iddispose-no-references": "Søket kunne ikke matche «$1» mot en tabelloppføring.", "smw-admin-idlookup-input": "Søk:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Oversikt", "smw-admin-tab-notices": "Foreldingsnotiser", "smw-admin-tab-maintenance": "Vedlikehold", "smw-admin-tab-supplement": "Supp<PERSON><PERSON><PERSON>", "smw-admin-tab-registry": "Register", "smw-admin-tab-alerts": "<PERSON><PERSON><PERSON>", "smw-admin-alerts-tab-deprecationnotices": "Foreldelsesnotiser", "smw-admin-alerts-tab-maintenancealerts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-alerts-section-intro": "Denne seksjonen viser varsler og notiser som gjelder innstillinger, handlinger og andre aktiviteter som er klassifisert som å trenge oppmerksomhet fra en administrator eller en bruker med de rette rettighetene.", "smw-admin-maintenancealerts-section-intro": "Følgende varsler og notiser bør løses for å forbedre systemet og vedlikeholdet.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Tabelloptimalisering", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Systemet har funnet at den siste [https://www.semantic-mediawiki.org/wiki/Table_optimization tabelloptimaliseringen] ble kjørt for $2 {{PLURAL:$2|dag|dager}} siden (oppføring fra $1), som overstiger vedlikeholdsterskelen på $3 {{PLURAL:$3|dag|dager}}. Som nevnt i dokumentasjonen vil spørringsplanleggeren kunne gjøre bedre avgjørelser om spørringer hvis dette kjøres regelmessig.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Utdaterte entiteter", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Systemet har talt $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities {{PLURAL:$1|utdatert entitet|utdaterte entiteter}}] og nådd et kritisk nivå for vedlikehold ved å overstige terskelen på $2. Det anbefales å kjøre vedlikeholdsskriptet [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>].", "smw-admin-maintenancealerts-invalidentities-alert-title": "Ugyldige entiteter", "smw-admin-maintenancealerts-invalidentities-alert": "Systemet matchet $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|entitet|entiteter}}] til et [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace ikke-vedlikeholdt navnerom], og det anbefales å kjøre vedlikeholdsskriptet [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code>disposeOutdatedEntities.php</code>] eller [https://www.semantic-mediawiki.org/wiki/rebuildData.php <code>rebuildData.php</code>].", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Innstillinger", "smw-admin-configutation-tab-namespaces": "Navnerom", "smw-admin-configutation-tab-schematypes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-maintenance-tab-tasks": "Oppgaver", "smw-admin-maintenance-tab-scripts": "Vedlikeholdsskript", "smw-admin-maintenance-no-description": "Ingen beskrivelse.", "smw-admin-maintenance-script-section-title": "Liste over tilgjengelige vedlikeholdsskript", "smw-admin-maintenance-script-section-intro": "Følgende vedlikeholdsskript krever en administrator og tilgang til kommandolinjen for å kunne utføre de opplistede skriptene.", "smw-admin-maintenance-script-description-dumprdf": "RDF-eksport av eksisterende tripler.", "smw-admin-maintenance-script-description-rebuildconceptcache": "<PERSON><PERSON> skriptet brukes for å behandle konseptmellomlagre for Semantic MediaWiki der det kan opprette, fjerne og oppdatere valgte mellomlagre.", "smw-admin-maintenance-script-description-rebuilddata": "Gjenskaper alle semantiske data i databasen ved å gå gjennom alle sidene som kan ha semantiske data.", "smw-admin-maintenance-script-description-rebuildelasticindex": "<PERSON><PERSON><PERSON>bygger ElasticSearch-indeksen (kun for installasjoner som bruker <code>ElasticStore</code>) ved å gå gjennom alle entiteter som har semantiske data.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Finn manglende entiteter i ElasticSearch (kun for installasjoner som bruker <code>ElasticStore</code>) og planlegg de relevante oppdateringsjobbene.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Gjenbygger <code>SQLStore</code>s fulltekstsøkindeks (for installasjoner hvor innstillingen er slått på).", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Gjenbygger bruksstatistikken for alle egenskapsentiteter.", "smw-admin-maintenance-script-description-removeduplicateentities": "Fjerner duplikatentiteter funnet i valgte tabeller som ikke har noen aktive referanser.", "smw-admin-maintenance-script-description-setupstore": "Setter opp lagrings- og spørringsbakstykket som definert i <code>LocalSettings.php</code>.", "smw-admin-maintenance-script-description-updateentitycollation": "Oppdaterer feltet <code>smw_sort</code> i <code>SQLStore</code> (i samsvar med innstillingen [https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation]).", "smw-admin-maintenance-script-description-populatehashfield": "Befolker feltet <code>smw_hash</code> for rader som mangler verdien.", "smw-admin-maintenance-script-description-purgeentitycache": "Gjenoppfrisk mellomlageroppføringer for kjente entiteter og deres assosierte data.", "smw-admin-maintenance-script-description-updatequerydependencies": "<PERSON><PERSON><PERSON><PERSON> spø<PERSON> og spørringsavhengigheter (se innstillingen [https://www.semantic-mediawiki.org/wiki/Help:$smwgEnabledQueryDependencyLinksStore $smwgEnabledQueryDependencyLinksStore]).", "smw-admin-maintenance-script-description-disposeoutdatedentities": "Bli kvitt utdaterte entiteter og spørringslenker.", "smw-admin-maintenance-script-description-runimport": "Befolk og importer auto-oppdaget innhold fra [https://www.semantic-mediawiki.org/wiki/Help:$smwgImportFileDirs $smwgImportFileDirs].", "smw-admin-maintenance-script-section-update": "Oppdateringsskript", "smw-admin-maintenance-script-section-rebuild": "Gjenoppbyggingsskript", "smw-livepreview-loading": "Laster …", "smw-sp-searchbyproperty-description": "Denne siden gir et enkelt [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces gjennomblaingsgrensesnitt] for å finne entiteter beskrevet av en egenskap og en navngitt verdi. Andre tilgjengelige søkegrensesnitt inkluderer [[Special:PageProperty|sideegenskapssøket]] og [[Special:Ask|ask-spørringsbyggeren]].", "smw-sp-searchbyproperty-resultlist-header": "Liste over resultater", "smw-sp-searchbyproperty-nonvaluequery": "En liste over verdier som har egenskapen «$1» tildelt.", "smw-sp-searchbyproperty-valuequery": "En liste over sider som har egenskapen «$1» med verdien «$2» annotert.", "smw-datavalue-number-textnotallowed": "«$1» kan ikke tildeles en erklært nummertype med verdi $2.", "smw-datavalue-number-nullnotallowed": "«$1» returnerte med «NULL» som ikke er tillatt som tall.", "smw-editpage-annotation-enabled": "<PERSON><PERSON> siden støtter semantiske annoteringer i teksten (f.eks. <nowiki>«[[Is specified as::World Heritage Site]]»</nowiki>) for å bygge strukturerte og spørbart innhold gitt av Semantic MediaWiki. For en utdypende beskrivelse av hvordan man bruker annoteringer eller parserfunksjonen #ask, ta en titt på hjelpesidene [https://www.semantic-mediawiki.org/wiki/Help:Getting_started kom i ang], [https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation annoteringer i tekst] eller [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries inline-spørringer].", "smw-editpage-annotation-disabled": "Denne siden er ikke slått på for semantiske annoteringer i teksten på grunn av navneromsbegrensninger. Detaljer om hvordan navnerommet kan slås på kan du finne på hjelpesiden for [https://www.semantic-mediawiki.org/wiki/Help:Configuration konfigurasjon].", "smw-editpage-property-annotation-enabled": "Denne egenskapen kan utvides med semantiske annoteringer for å angi en datatype (f.eks. <nowiki>«[[Has type::Page]]»</nowiki>) eller andre støttende erklæringer (f.eks. <nowiki>«[[Subproperty of::dc:date]]»</nowiki). For en beskrivelse av hvordan man kan tilpasse denne siden, se hjelpesidene [https://www.semantic-mediawiki.org/wiki/Help:Property_declaration erklæring av en egenskap] eller [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes liste over tilgjengelige datatyper].", "smw-editpage-property-annotation-disabled": "Denne egenskapen kan utvides med en datatypeerklæring (f.eks. <nowiki>«[[Has type::Page]]»</nowiki>) som den allerede er forhåndsdefinert (se hjelpesiden [https://www.semantic-mediawiki.org/wiki/Help:Special_properties spesielle egenskaper] for mer informasjon).", "smw-editpage-concept-annotation-enabled": "Dette konspeten kan utvides med parserfunksjonen #concept. For en beskrivelse av hvordan man bruker #concept, se hjelpesiden [https://www.semantic-mediawiki.org/wiki/Help:Concepts konsept].", "smw-search-syntax-support": "Søkeinndataene støtter bruken av semantisk [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search spørringssyntaks] for å hjelpe til å matche resultater med Semantic MediaWiki.", "smw-search-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Inndataassistenten] er også slått på for å forenkle forhåndsutvalget av tilgjengelige egenskaper og kategorier.", "smw-search-help-intro": "Inndata med <code><nowiki>[[ ... ]]</nowiki></code> signaliserer til inndataprosessoren at den skal bruke Semantic MediaWikis søkebakstykke. Merk at å kombinere <code><nowiki>[[ ... ]]</nowiki></code> med ustrukturert tekstsøk som <code><nowiki>[[ ... ]] OR Lorem ipsum</nowiki></code> ikke støttes.", "smw-search-help-structured": "Strukturerte søk:\n\n*<code><nowiki>[[Category:Lorem ipsum]]</nowiki></code>, <code><nowiki>[[Has number::123]]</nowiki></code> (som [https://www.semantic-mediawiki.org/wiki/Help:Search#Filter_context filtrert kontekst])\n\n*<code><nowiki>[[Has text::~*lorem*]]</nowiki></code> (med en [https://www.semantic-mediawiki.org/wiki/Help:Search#Query_context spørringskontekst])", "smw-search-help-proximity": "Nærhetssøk (en egenskap er ukjent, '''kun''' tilgjengelig for de bakstykkene som tilbyr fulltekst-søkeintegrasjon):\n\n*<code><nowiki>[[in:lorem ipsum]]</nowiki></code> (søk i alle dokumenter etter \"lorem\" og \"ipsum\" som har blitt indeksert)\n\n* <code><nowiki>[[phrase:lorem ipsum]]</nowiki></code> (match \"lorem ipsum\" som frase)", "smw-search-help-ask": "<PERSON><PERSON><PERSON><PERSON> lenker forklarer hvordan man bruker syntaksen <code>#ask</code>.\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Å velge sider] beskriver hvordan man velger sider og bygger vilkår\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Søkeoperatorer] lister opp tilgjengelige søkeoperatorer inkludert de for intervall og jokerspørringer", "smw-search-input": "Inndata og søk", "smw-search-help-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Inndataassistanse] tilbys for inndatafeltet og krever bruk av én av følgende prefikser:\n\n*<code>p:</code> for å slå på egenskapsforslag (f.eks. <code><nowiki>[[p:Has ...</nowiki></code>)\n\n*<code>c:</code> for å slå på kategoriforslag\n\n*<code>con:</code> for å slå på konseptforslag", "smw-search-syntax": "Syntaks", "smw-search-profile": "Utvidet", "smw-search-profile-tooltip": "Søkefunksjoner tilknyttet Semantic MediaWiki", "smw-search-profile-sort-best": "<PERSON><PERSON> treff", "smw-search-profile-sort-recent": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-sort-title": "<PERSON><PERSON><PERSON>", "smw-search-profile-extended-help-intro": "Special:Search med [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile utvidet profil] gir tilgang til søkefunksjoner som er spesifikke for Semantic MediaWiki og støttes av spørringsbakstykket.", "smw-search-profile-extended-help-sort": "Angir en sorteringspreferanse resultatet skal vises med:", "smw-search-profile-extended-help-sort-title": "* «Title» bruker sidetittelen (eller visningstittelen) som sorteringskriterium", "smw-search-profile-extended-help-sort-recent": "* «Most recent» viser de sist endrede entitetene først (underobjektentiteter vises ikke siden slike entiteter ikke annoteres med en [[Property:Modification date|endringsdato]])", "smw-search-profile-extended-help-sort-best": "* «Best match» serterer entiteter etter [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy relevans] basert på poeng som gis av bakstykket", "smw-search-profile-extended-help-form": "<PERSON><PERSON><PERSON><PERSON><PERSON> gis (hvis de vedlikeholdes) for å matche spesifikke bruksområder ved å vise forskjellige egenskaps- og verdifelter for å innskrenke inndataprosessen og gjøre det enklere for brukere å fortsette med en søkeforespørsel. (se $1)", "smw-search-profile-extended-help-namespace": "Navneromsboksen skjules så snart et skjema velges, men kan gjøres synlig igjen med hjelp av «vis/skjul»-knappen.", "smw-search-profile-extended-help-search-syntax": "Søkeinndatafeltet støtter bruken av <code>#ask</code>-syntaks for å definere en Semantic MediaWiki-spesifikk søkekontekst. Nyttige uttrykk inkluderer:", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code> for å finne alt som inneholder «...» og er spesielt nyttig når søkekonteksten eller egenskapene som er involvert er ukjente (f.eks. er <code>in:(lorem && ipsum)</code> likeverdig med <code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code>).", "smw-search-profile-extended-help-search-syntax-simplified-phrase": "* <code>phrase:</code> for å finne alt som inneholder «…» i nøyaktig samme rekkefølge", "smw-search-profile-extended-help-search-syntax-simplified-has": "* <code>has:</code> for å matche alle entiteter med egenskapen «…» (f.eks. er <code>has:(Foo && Bar)</code> likeverdig med <code><nowiki>[[Foo::+]] && [[Bar::+]]</nowiki></code>)", "smw-search-profile-extended-help-search-syntax-simplified-not": "* <code>not:</code> for å matche entiteter som ikke inkluderer «…»", "smw-search-profile-extended-help-search-syntax-prefix": "* Ekstra egendefinerte prefikser er tilgjengelige og definert som det: $1", "smw-search-profile-extended-help-search-syntax-reserved": "* <PERSON><PERSON> u<PERSON> er <PERSON>, som: <nowiki>$1</nowiki>", "smw-search-profile-extended-help-search-syntax-note": "''Noen av de opplistede operasjonene er kun nyttige i tilknytning til en aktivert fulltekstindeks eller ElasticStore.''", "smw-search-profile-extended-help-query": "Brukte <code><nowiki>$1</nowiki></code> som spørring.", "smw-search-profile-extended-help-query-link": "For flere detaljer, bruk $1.", "smw-search-profile-extended-help-find-forms": "tilgjengelige skjemaer", "smw-search-profile-extended-section-sort": "Sorter etter", "smw-search-profile-extended-section-form": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-extended-section-search-syntax": "Søkeinndata", "smw-search-profile-extended-section-namespace": "Navnerom", "smw-search-profile-extended-section-query": "<PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-link-caption-query": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-show": "Vis", "smw-search-hide": "Skjul", "log-name-smw": "Semantic MediaWiki-logg", "log-show-hide-smw": "$1 Semantic MediaWiki-logg", "logeventslist-smw-log": "Semantic MediaWiki-logg", "log-description-smw": "Aktiviteter for [https://www.semantic-mediawiki.org/wiki/Help:Logging påslåtte hendelsestyper] som har blitt rapportert av Semantic MediaWiki og dens komponenter.", "logentry-smw-maintenance": "Vedlikeholdsrelaterte hendelser utsendt av semantic MediaWiki", "smw-datavalue-import-unknown-namespace": "Importnavnerommet «$1» er ukjent. Sjekk at OWL-importdetaljer er tilgjengelige via [[MediaWiki:Smw import $1]]", "smw-datavalue-import-missing-namespace-uri": "Kunne ikke finne navneroms-URI-en «$1» i [[MediaWiki:Smw import $1|$1-import]].", "smw-datavalue-import-missing-type": "Ingen typedefinisjon ble funnet for «$1» i [[MediaWiki:Smw import $2|$2-import]].", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1-importering]]", "smw-datavalue-import-invalid-value": "«$1» er ikke et gyldig format og forventes å bestå av «navnerom»:«identifikator» (f.eks. «foaf:name»).", "smw-datavalue-import-invalid-format": "Forventet at strengen «$1» skulle være delt i fire deler, men formatet ble ikke forstått.", "smw-property-predefined-impo": "«$1» er en forhåndsdefinert egenskap som beskriver en relasjon til et [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary importert ordforråd] og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "«$1» er en forhåndsdefinert egenskap som beskriver [[Special:Types|datatypen]] til en egenskap og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-sobj": "«$1» er en forhåndsdefinert egenskap som representerer en [https://www.semantic-mediawiki.org/wiki/Help:Container konteinerkonstruksjon] og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-sobj": "Konteineren gjør det mulig å akkumulere egenskap–verdi-tildelinger på samme måte som en vanlig wikiside, men innen et annet entitetsrom mens det lenkes til det innbyggende subjektet.", "smw-property-predefined-errp": "«$1» er en forhåndsdefinert egenskap som sporer inndatafeil for uvanlige verdiannoteringer og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-errp": "I de fleste tilfeller forårsakes det av typeulikhet eller en [[Property:Allows value|verdibegrensning]].", "smw-property-predefined-pval": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value «$1»] er en forhåndsdefinert egenskap som kan definere en liste over tillatte verdier for å begrense verditildelinger for en egenskap og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvali": "[https://www.semantic-mediawiki.org/wiki/Help:Special_property_Allows_value_list «$1»] er en forhåndsdefinert egenskap som kan angi en referanse til en liste som inneholder tillatte verdier for å begrense verditildelinger for en egenskap og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-restricted-annotation-use": "Egenskapen «$1» har et begrenset bruksområde og kan ikke brukes som annoteringsegenskap av en bruker.", "smw-datavalue-property-restricted-declarative-use": "Egenskapen «$1» er en erklærende egenskap og kan kun brukes som egenskaps- eller kategoriside.", "smw-datavalue-property-create-restriction": "Egenskapen «$1» finnes ikke og brukeren har ikke tillatelsen «$2» (se [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode autoritetsmodus]) til å opprette eller annotere verdier med en ikke-godkjent egenskap.", "smw-datavalue-property-invalid-character": "«$1» inneholder et listet tegn «$2» som del av egenskapsetiketten og har derfor blitt klassifisert som ugyldig.", "smw-datavalue-property-invalid-chain": "Å bruke «$1» som egenskapskjede er ikke tillatt under annoteringsprosessen.", "smw-datavalue-restricted-use": "Dataverdien «$1» har blitt merket for begrenset bruk.", "smw-datavalue-invalid-number": "«$1» kan ikke tolkes som tall.", "smw-query-condition-circular": "En mulig sirkelrefernse har blitt oppdaget i «$1».", "smw-query-condition-empty": "Spørringsbeskrivelsen har et tomt vilkår.", "smw-types-list": "Liste over datatyper", "smw-types-default": "«$1» er ikke en innebygd datatype.", "smw-types-help": "Videre informasjon og eksempler kan finnes på denne [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 hjelpesiden].", "smw-type-anu": "«$1» er en variant av datatypen [[Special:Types/URL|URL]] og brukes mest for eksporterklæringen «owl:AnnotationProperty».", "smw-type-boo": "«$1» er en grunnleggende datatype for å beskrive en verdi som er sann/usann.", "smw-type-cod": "«$1» er en variant av datatypen [[Special:Types/Text|Text]] som brukes for tekniske tekster av vilkårlig lengde, som kildekodelister.", "smw-type-geo": "«$1» er en datatype som beskriver geografiske beliggenhet og krever utvidelsen [https://www.semantic-mediawiki.org/wiki/Extension:Maps «Maps»] for å tilby utvidet funksjonalitet.", "smw-type-tel": "«$1» er en spesiell datatype for å beskrive internasjonale telefonnummer i samsvar med RFC 3966.", "smw-type-txt": "«$1» er en grunnleggende datatype for å beskrive strenger av vilkårlig lengde.", "smw-type-dat": "«$1» er en grunnleggende datatype for å representere tidspunkter i et enhetlig format.", "smw-type-ema": "«$1» er en spesiell datatype for å representere e-post.", "smw-type-tem": "«$1» er en spesiell numerisk datatype for å representere en temperatur.", "smw-type-qty": "«$1» er en datatype for å beskrive kvantiteter med en numerisk representasjon og en måleenhet.", "smw-type-rec": "«$1» er en konteinerdatatype som angir en liste over typede egenskaper i en bestemt rekkefølge.", "smw-type-extra-tem": "Konverteringsskjemaet inkluderer støttede enheter som kelvin, celsius, fahrenheit og rankine.", "smw-type-tab-properties": "Egenskaper", "smw-type-tab-types": "Typer", "smw-type-tab-type-ids": "Type-ID-er", "smw-type-tab-errors": "<PERSON><PERSON>", "smw-type-primitive": "Grunnleggende", "smw-type-contextual": "Kontekstuell", "smw-type-compound": "Sam<PERSON><PERSON><PERSON>", "smw-type-container": "<PERSON><PERSON><PERSON>", "smw-type-no-group": "Uklassifisert", "smw-special-pageproperty-description": "<PERSON>ne siden gir et gjennomblaingsgrensesnitt for å finne alle verdier med en egenskap og en gitt side. <PERSON> tilgjengelige søkegrensesnitt inkluder [[Special:SearchByProperty|egenskapssøk]] og [[Special:Ask|ask-spørringsbyggeren]].", "smw-property-predefined-errc": "«$1» er en forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] og representerer feil som dukket opp i tilknytning til dårlig verdiannotering eller inndataprosessering.", "smw-property-predefined-long-errc": "<PERSON><PERSON> samles i en [https://www.semantic-mediawiki.org/wiki/Help:Container konte<PERSON>] som kan inkludere en referanse til egenskapen som forårsaket uoverensstemmelsen.", "smw-property-predefined-errt": "«$1» er en forhåndsdefinert egenskap som inneholder tekstbeskrivelsen til en feil og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-subobject-parser-invalid-naming-scheme": "Et brukerdefinert underobjekt inneholdt en ugyldig navneformel. Punktnotasjonen ($1) som er brukt i de første fem tegnene er reservert for utvidelser. Du kan sette en [https://www.semantic-mediawiki.org/wiki/Help:Adding_subobjects#Named_identifier navngitt identifikator].", "smw-datavalue-record-invalid-property-declaration": "Opptegnelsesdefinisjonen inneholder egenskapen «$1» som selv er erklært som en opptegnelsestype, og det er ikke tillatt.", "smw-property-predefined-mdat": "«$1» er en forhåndsdefinert egenskap som samsvarer med datoen et subjekt sist ble endret, og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cdat": "«$1» er en forhåndsdefinert egenskap som samsvarer med datoen til subjektets første versjon, og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-newp": "«$1» er en forhåndsdefinert egenskap som indikerer hvorvidt et subjekt er nytt eller ikke, og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-ledt": "«$1» er en forhåndsdefinert egenskap som inneholder sidenavnet til brukeren som opprettet den siste revisjonen og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-mime": "«$1» er en forhåndsdefinert egenskap som beskriver MIME-typen til en opplastet fil og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-media": "«$1» er en forhåndsdefinert egenskap som beskriver medietypen til en opplastet fil og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askfo": "«$1» er en forhåndsdefinert egenskap som inneholder navnet til resultatformatet brukt i en spørring og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askst": "«$1» er en forhåndsdefinert egenskap som beskriver forholdene til spørringen som en streng og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askdu": "«$1» er en forhåndsdefinert egenskap som inneholder en tidsverdi (i sekunder) som var påkrevd for å fullføre spørringskjøringen og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksc": "«$1» er en forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] som identifiserer alternative spørringskilder.", "smw-property-predefined-askco": "«$1» er en forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] som beskriver statusen til en spørring eller dens bestanddeler.", "smw-property-predefined-long-askco": "Antallet brukere tildelt representerer en intern kodet status som forklares på [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler hjelpesiden].", "smw-property-predefined-prec": "«$1» er en forhåndsdefinert egenskap som beskriver en [https://www.semantic-mediawiki.org/wiki/Help:Display_precision visningspresisjon] (i desimaltall) for numeriske datatyper.", "smw-property-predefined-attch-link": "«$1» er en forhåndsdefinert egenskap som samler innbygde fil- og bildelenker funnet på en side og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-inst": "«$1» er en intern forhåndsdefinert egenskap som lagrer kategoriinformasjon uavhengig av MediaWiki og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-unit": "«$1» er en deklarativ forhåndsdefinert egenskap for å definere visningsenheter for numerisktypede egenskaper og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-unit": "En kommaatskilt liste gjør det mulig å beskrive enheter eller formater som skal brukes i visningen.", "smw-property-predefined-conv": "«$1» er en deklarativ forhåndsdefinert egenskap for å definere konversjonsfaktoren for en fysisk enhet og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-serv": "«$1» er en deklarativ forhåndsdefinert egenskap for å legge tel tjenestelenker til en egenskap og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-redi": "«$1» er en intern forhåndsdefinert egenskap for å spore omdirigeringer og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subp": "«$1» er en deklarativ forhåndsdefinert egenskap for å definere at en egenskap er en [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subproperty_of underegenskap av] en annen og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-subc": "«$1» er en forhåndsdefinert egenskap for å definere at en kategori er en [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Subcategory_of underkategori av] en annen og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-conc": "«$1» er en intern forhåndsdefinert egenskap for å definere et tilknyttet konsept og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-err-type": "«$1» er en forhåndsdefinert egenskap for å identifisere en gruppe av eller klasse av [https://www.semantic-mediawiki.org/wiki/Help:Processing_errors prosesseringsfeil] og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-skey": "«$1» er en intern forhåndsdefinert egenskap for å holde en sorteringsreferanse og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pplb": "«$1» er en deklarativ forhåndsdefinert egenskap for å angi en [https://www.semantic-mediawiki.org/wiki/Help:Preferred_property_label foretrukket egenskapsetikett] og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-chgpro": "«$1» er en forhåndsdefinert egenskap for å holde [https://www.semantic-mediawiki.org/wiki/Help:Change_propagation endringspropageringsinformasjon] og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-link": "og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-format-schema": "og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-profile-schema": "og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans": "og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-source": "og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-trans-group": "og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-cont-len": "«$1» er en forhåndsdefinert egenskap for å lagre lengdeinformasjon, og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-len": "Den brukes i tilknytning til [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (og [https://www.semantic-mediawiki.org/Attachment_processor vedleggsprosessoren]) for å samle og lagre lengdeinformasjon hentet fra en inntatt fil (hvis angitt).", "smw-property-predefined-cont-lang": "«$1» er en forhåndsdefinert egenskap for å lagre språkinformasjon og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-lang": "Den brukes i tilknytning til [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (og [https://www.semantic-mediawiki.org/Attachment_processor vedleggsprosessoren]) for å samle og lagre språkinformasjon hentet fra en inntatt fil (hvis angitt).", "smw-property-predefined-cont-title": "«$1» er en forhåndsdefinert egenskap for å lagre tittelinformasjon og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-title": "Den brukes i tilknytning til [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (og [https://www.semantic-mediawiki.org/Attachment_processor vedleggsprosessoren]) for å samle og lagre tittelinformasjon hentet fra en inntatt fil (hvis angitt).", "smw-property-predefined-cont-author": "«$1» er en forhåndsdefinert egenskap for å lagre forfatterinformasjon og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-author": "Den brukes i tilknytning til [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (og [https://www.semantic-mediawiki.org/Attachment_processor vedleggsprosessoren]) for å samle og lagre forfatterinformasjon hentet fra en inntatt fil (hvis angitt).", "smw-property-predefined-cont-date": "«$1» er en forhåndsdefinert egenskap for å lagre datoinformasjon og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-date": "Den brukes i tilknytning til [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (og [https://www.semantic-mediawiki.org/Attachment_processor vedleggsprosessoren]) for å samle og lagre datoinformasjon hentet fra en inntatt fil (hvis angitt).", "smw-property-predefined-cont-type": "«$1» er en forhåndsdefinert egenskap for å lagre filtypeinformasjon og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-type": "Den brukes i tilknytning til [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (og [https://www.semantic-mediawiki.org/Attachment_processor vedleggsprosessoren]) for å samle og lagre typeinformasjon hentet fra en inntatt fil (hvis angitt).", "smw-property-predefined-cont-keyw": "«$1» er en forhåndsdefinert egenskap for å representere nøkkelord og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-cont-keyw": "Den brukes i tilknytning til [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (og [https://www.semantic-mediawiki.org/Attachment_processor vedleggsprosessoren]) for å samle og lagre nøkkelord hentet fra en inntatt fil (hvis angitt).", "smw-property-predefined-file-attch": "«$1» er en forhåndsdefinert egenskap for å representere en konteiner som lagrer vedleggsinformasjon og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-file-attch": "Den brukes i tilknytning til [https://www.semantic-mediawiki.org/ElasticStore ElasticStore] (og [https://www.semantic-mediawiki.org/Attachment_processor vedleggsprosessoren]) for å samle all innholdsspesifikk informasjon hentet fra en inntatt fil (hvis angitt).", "smw-types-extra-geo-not-available": "[https://www.semantic-mediawiki.org/wiki/Extension:Maps Utvidelsen «Maps»] ble ikke oppdaget, og «$1» har derfor begrensede muligheter.", "smw-datavalue-monolingual-dataitem-missing": "Et forventet element for å bygge en enspråklig sammensatt verdi mangler.", "smw-datavalue-languagecode-missing": "For annoteringen «$1» kunne ikke parseren bestemme en språkkode (f.eks. «foo@en»).", "smw-datavalue-languagecode-invalid": "«$1» ble ikke gjenkjent som en støttet språkkode.", "smw-property-predefined-lcode": "«$1» er en forhåndsdefinert egenskap som representerer en BCP47-formatert språkkode og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-type-mlt-rec": "«$1» er en [https://www.semantic-mediawiki.org/wiki/Help:Container konteinerdatatype] som assosierer en tekstverdi med en spesifikk [[Property:Language code|språkkode]].", "smw-types-extra-mlt-lcode": "Datatypen {{PLURAL:$2|krever|krever ikke}} en språkkode (f.eks. {{PLURAL:$2|en verdiannotering uten en språkkode godtas ikke|en verdiannotering uten språkkode godkjennes}}).", "smw-property-predefined-text": "«$1» er en forhåndsdefinert egenskap som representerer tekst av vilkårlig lengde og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pdesc": "«$1» er en forhåndsdefinert egenskap som gjør det mulig å beskrive en egenskap i en språkkontekst og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-list": "«$1» er en forhåndsdefinert egenskap for å definere en liste over egenskaper brukt med en egenskap av typen [[Special:Types/Record|opptegnelse]] og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-limitreport-intext-parsertime": "[SMW] Pa<PERSON><PERSON><PERSON> for tekstannotering", "smw-limitreport-intext-postproctime": "[SMW] etterprosesseringstid", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] Tid for lagringsoppdatering (ved sidegjenoppfrisking)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw_allows_pattern": "Denne siden forventes å inneholde en liste over referanser (etterfulgt av [https://en.wikipedia.org/wiki/Regular_expression regulære uttrykk]) forå gjøres tilgjengelig med egenskapen [[Property:Allows pattern|Allows pattern]]. For å redigere denne siden kreves rettigheten <code>smw-patternedit</code>.", "smw-datavalue-allows-pattern-mismatch": "«$1» ble klassifisert som ugyldig av det regulære uttrykket «$2».", "smw-datavalue-allows-pattern-reference-unknown": "Mønsterreferansen «$1» kunne ikke matches med en oppføring i [[MediaWiki:Smw allows pattern]].", "smw-datavalue-allows-value-list-unknown": "Listereferansen «$1» kunne ikke matches mot en side i [[MediaWiki:Smw allows list $1]].", "smw-datavalue-allows-value-list-missing-marker": "Listeinnholdet «$1» mangler elementer med en listemarkør *.", "smw-datavalue-feature-not-supported": "Funksjonen «$1» støttes ikke eller er slått av på denne wikien.", "smw-property-predefined-pvap": "«$1» er en forhåndsdefinert egenskap som kan angi et [[MediaWiki:Smw allows pattern|mønsterreferanse]] ved å bruke [https://en.wikipedia.org/wiki/Regular_expression regulært uttrykk] og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-dtitle": "«$1» er en forhåndsdefinert egenskap som kan tildele en distinktiv visningstittel til en entitet og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pvuc": "«$1» er en forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å begrense verditildelinger for hver instans til å være unik (eller maksimalt én).", "smw-property-predefined-long-pvuc": "Unikhet etableres når to verdier ikke er likeverdige i den bokstavelige representasjonen, og ethvert brudd på den begrensningen blir kategorisert som en feil.", "smw-datavalue-constraint-uniqueness-violation": "Egenskapen «$1» tillater kun unike verditildelinger og «$2» var allerede annotert i subjektet «$3».", "smw-datavalue-constraint-uniqueness-violation-isknown": "Egenskapen «$1» tillater kun unike verdiannoteringer, ''$2'' inneholder allerede en tildelt verdi. «$3» bryter med unikhetsbegrensningen.", "smw-datavalue-constraint-violation-non-negative-integer": "Egenskapen «$1» har en begrensning for «ikke-negativt heltall» og verdien ''$2'' bryter med den begrensningen.", "smw-datavalue-constraint-violation-must-exists": "Egenskapen «$1» har en begrensning <code>must_exists</code> og verdien ''$2'' bryter med den begrensningen.", "smw-datavalue-constraint-violation-single-value": "Egenskapen «[[Property:$1|$1]]» har en begrensning <code>single_value</code> og verdien «$2» bryter med den begrensnigen.", "smw-constraint-violation-uniqueness": "En <code>unique_value_constraint</code>-begrensning er tildelt egenskapen «[[Property:$1|$1]]» som kun tillater unike verdier, og verdien ''$2'' finnes allerede for subjektet «$3».", "smw-constraint-violation-uniqueness-isknown": "En <code>unique_value_constraint</code>-begrensning er tildelt egenskapen «[[Property:$1|$1]]», og derfor tillates kun unike verdiannoteringer. ''$2'' finnes allede som en annotert verdi med «$3», som bryter med unikhetsbegrensningen for det gjeldende subjektet.", "smw-constraint-violation-non-negative-integer": "En <code>non_negative_integer</code>-begrensning er tildelt egenskapen «[[Property:$1|$1]]» og verdien ''$2'' bryter med denne begrensningen.", "smw-constraint-violation-must-exists": "En <code>must_exists</code>-begrensning er tildelt egenskapen «[[Property:$1|$1]]», og verdien ''$2'' bryter med denne begrensningen.", "smw-constraint-violation-single-value": "En <code>single_value</code>-begrensning er tildelt egenskapen «[[Property:$1|$1]]», og verdien ''$2'' bryter med denne begrensningen.", "smw-constraint-violation-class-shape-constraint-missing-property": "En <code>shape_constraint</code> er tildelt kategorien «[[:$1|$1]]» med en <code>property</code>-<PERSON><PERSON><PERSON><PERSON>, men den påkrevde egenskapen «$2» mangler.", "smw-constraint-violation-class-shape-constraint-wrong-type": "En <code>shape_constraint</code> er tildelt kategorien «[[:$1|$1]]» med en <code>property_type</code>-nøk<PERSON>; egenskapen «$2» matcher ikke typen «$3».", "smw-constraint-violation-class-shape-constraint-invalid-max-cardinality": "En <code>shape_constraint</code> er tildelt kategorien «[[:$1|$1]]» med en <code>max_cardinality</code>-nøkkel; egenskapen «$2> matcher ikke kardinaliteten til «$3».", "smw-constraint-violation-class-shape-constraint-invalid-min-length": "En <code>shape_constraint</code> er tildelt kategorien «[[:$1|$1]]» med en <code>min_textlength</code>-nøkkel; egenskapen «$2» matcher ikke lengdekrave<PERSON> til «$3».", "smw-constraint-violation-class-mandatory-properties-constraint": "En <code>mandatory_properties</code>-begrensning er tildelt kategorien «[[:$1|$1]]» og krever følgende obligatoriske egenskaper: $2", "smw-constraint-violation-allowed-namespace-no-match": "En <code>allowed_namespaces</code>-begrensning er tildelt egenskapen «[[Property:$1|$1]]», og «$2» bryter med navneromskravet; kun de følgende «$3» navnerommene er tillatt.", "smw-constraint-violation-allowed-namespaces-requires-page-type": "Begrensningen <code>allowed_namespaces</code> krever en sidetype.", "smw-constraint-schema-category-invalid-type": "Det annoterte skjemaet «$1» er ugyldig for en kategori, det krever typen «$2».", "smw-constraint-schema-property-invalid-type": "Det annoterte skjemaet «$1» er ugyldig for en egenskap, det krever typen «$2».", "smw-constraint-error-allows-value-list": "«$1» er ikke i lista ($2) over [[Property:Allows value|tillatte verdier]] for egenskapen «$3».", "smw-constraint-error-allows-value-range": "«$1» er ikke i intervallet «$2» angitt av [[Property:Allows value|tillatte verdier]] for egenskapen «$3».", "smw-property-predefined-boo": "«$1» er en [[Special:Types/Boolean|type]] og forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å representere boolske verdier.", "smw-property-predefined-num": "«$1» er en [[Special:Types/Number|type]] og forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å representere numeriske verdier.", "smw-property-predefined-dat": "«$1» er en [[Special:Types/Date|type]] og forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å representere datoverdier.", "smw-property-predefined-uri": "«$1» er en [[Special:Types/URL|type]] og forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å representere URI-/URL-verdier.", "smw-property-predefined-qty": "«$1» er en [[Special:Types/Quantity|type]] og forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å representere kvantitetsverdier.", "smw-datavalue-time-invalid-offset-zone-usage": "«$1» inneholder en forskyvnings- og soneidentifikator som ikke støttes.", "smw-datavalue-time-invalid-values": "Verdien «$1» inneholder informasjon som ikke kan tolkes med formen «$2».", "smw-datavalue-time-invalid-date-components-common": "«$1» inneholder informasjon som ikke kan tolkes.", "smw-datavalue-time-invalid-date-components-dash": "«$1» inneholder en type tankestrek eller andre tegn som er ugyldige for datotolking.", "smw-datavalue-time-invalid-date-components-empty": "«$1» inneholder tomme komponenter.", "smw-datavalue-time-invalid-date-components-three": "«$1» inneholder mer enn tre komponenter som kreves for datotolking.", "smw-datavalue-time-invalid-date-components-sequence": "«$1» inneholder en sekvens som ikke kunne tolkes mot en tilgjengelig matchingsmatrise over datokompo<PERSON><PERSON>.", "smw-datavalue-time-invalid-ampm": "«$1» inneholder «$2» som timeelement som er ugyldig for 12-timerskonvensjonen.", "smw-datavalue-time-invalid-jd": "Kunne ikke tolke inndataverdien «$1» som gyldig JD-tall (juliansk dag) med «$2» rapportert.", "smw-datavalue-time-invalid-prehistoric": "Kunne ikke tolke forhistorisk inndataverdi «$1». For <PERSON><PERSON><PERSON><PERSON>, har man angitt flere år, eller kalendermodellen returnerer uventede resultater i en forhistorisk kontekst.", "smw-datavalue-time-invalid": "Kunne ikke tolke inndataverdien «$1» som gyldig dato- eller tidskomponent med «$2» rapportert.", "smw-datavalue-external-formatter-uri-missing-placeholder": "Formaterings-URI-en mangler plassholderen «$1».", "smw-datavalue-external-formatter-invalid-uri": "«$1» er en ugyldig URL.", "smw-datavalue-external-identifier-formatter-missing": "Egenskapen mangler en tildeling for [[Property:External formatter uri|«External formatter URI»]].", "smw-datavalue-external-identifier-multi-substitute-parameters-missing": "Den eksterne identifikatoren «$1» forventer flerfeltserstatning, men den nåværende verdien «$2» mangler minst én verdiparameter for å imøtekomme kravet.", "smw-datavalue-keyword-maximum-length": "Nøkkelordet oversteg maksimallengden på {{PLURAL:$1|ett|$1}} tegn.", "smw-property-predefined-eid": "«$1» er en [[Special:Types/External edintefier|type]] og forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å representere eksterne identifikatorer.", "smw-property-predefined-peid": "«$1» er en forhåndsdefinert egenskap som spesifiserer en ekstern identifikator og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-pefu": "«$1» er en forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å angi en ekstern ressurs med en plassholder.", "smw-property-predefined-long-pefu": "URI-en forventes å inneholde en plassholder som justeres med en [[Special:Types/External identifier|ekstern identifikator]] for å danne en gyldig ressursreferanse.", "smw-type-eid": "«$1» er en variant av datatypen [[Special:Types/Text|tekst]] for å beskrive eksterne ressurser (URI-basert) og krever at tildelte egenskaper erklærer en [[Property:External formatter uri|External formatter URI]].", "smw-property-predefined-keyw": "«$1» er en forhåndsdefinert egenskap og [[Special:Types/Keyword|type]] gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] som normaliserer teksten og har begrenset tegnlengde.", "smw-type-keyw": "«$1» er en variant av datatypen [[Special:Types/Text|tekst]] som har begrensed tegnlengde med en normalisert innholdsrepresentasjon.", "smw-datavalue-stripmarker-parse-error": "Den gitte verdien «$1» ineholder [https://en.wikipedia.org/wiki/Help:Strip_markers «strip markers»] og kan derfor ikke tolkes tilstrek<PERSON>.", "smw-datavalue-parse-error": "Den gitte verdien «$1» ble ikke forstått.", "smw-datavalue-propertylist-invalid-property-key": "Egenskapslisten «$1» inneholdt en ugyldig egenskapsnøkkel «$2».", "smw-datavalue-type-invalid-typeuri": "Typen «$1» kunne ikke gjøres om til en gyldig URI-representasjon.", "smw-datavalue-wikipage-missing-fragment-context": "Wikisideinndataverdien «$1» kan ikke brukes uten en kontekstside.", "smw-datavalue-wikipage-invalid-title": "Sidetypens inndataverdi «$1» inneholdt ugyldige tegn eller er ufullstendig og kan derfor forårsake uventede resultater i en spørrings- eller annoteringsprosess.", "smw-datavalue-wikipage-property-invalid-title": "Egenskapen «$1» (som sidetype) med inndataverdien «$2» inneholder ugyldige tegn eller er ukompatibel og kan derfor forårsake uventede resultater i en spørrings- eller annoteringsprosess.", "smw-datavalue-wikipage-empty": "Wikisidens inndataverdi er tom (f.eks. <code>[[SomeProperty::]], [[]]</code>) og kan derfor ikke brukes som navn på eller del av et spørringsvilkår.", "smw-type-ref-rec": "«$1» er en [https://www.semantic-mediawiki.org/wiki/Container konteinertype] som gjør det mulig å tegne opp ekstra informasjon om en verditildeling.", "smw-datavalue-reference-invalid-fields-definition": "Typen [[Special:Types/Reference|referanse]] forventer en liste over egenskaper som erklæres med egenskapen [https://www.semantic-mediawiki.org/wiki/Help:Special_properties_Has_fields Has fields].", "smw-parser-invalid-json-format": "JSON-parseren returnerte med «$1».", "smw-property-preferred-label-language-combination-exists": "«$1» kan ikke brukes som foretrukket etikett fordi språket «$2» allerede har etiketten «$3» tildelt.", "smw-clipboard-copy-link": "<PERSON><PERSON>r lenke til utklippstavlen", "smw-property-userdefined-fixedtable": "«$1» ble konfigurert som [https://www.semantic-mediawiki.org/wiki/Fixed_properties «fixed property»] og alle endringer av dens [https://www.semantic-mediawiki.org/wiki/Type_declaration typeerklæring] krever enten kjøring av <code>setupStore.php</code> eller fullføring av oppgaven [[Special:SemanticMediaWiki|«databaseinstallering og oppgradering»]].", "smw-data-lookup": "Henter data ...", "smw-data-lookup-with-wait": "Forespørselen prosesseres og kan ta en liten stund.", "smw-no-data-available": "Ingen data tilgjengelige.", "smw-property-req-violation-missing-fields": "Egenskapen «$1» mangler en påkrevd erklæring for [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>] for denne «$2»-typen.", "smw-property-req-violation-multiple-fields": "Egenskapen «$1» inneholder flere (og derfor konkurrerende) [https://www.semantic-mediawiki.org/wiki/Help:Special_property_Has_fields <code>Has fields</code>]-erk<PERSON><PERSON><PERSON><PERSON>, kun én forventes for denne «$2»-typen.", "smw-property-req-violation-missing-formatter-uri": "Egenskapen «$1» mangler erklæringsdetaljer for den annoterte typen ved ikke å definere egenskapen <code>External formatter URI</code>.", "smw-property-req-violation-predefined-type": "Egenskapen «$1» som inneholder som forhåndsdefinert egenskap typeerklæringen «$2» som er inkompatibel med standardtypen til denne egenskapen.", "smw-property-req-violation-import-type": "En typeerklæring ble funnet som ikke er kompatibel med den forhåndsdefinerte typen til det importerte ordforrådet «$1». Generelt sett er det ikke nødvendig å erklære en type fordi informasjonen hentes fra den importerte definisjonen.", "smw-property-req-violation-change-propagation-locked-error": "Egenskapen «$1» ble endret og krever at tildelte entiteter reevalueres med bruk av prosessen [https://www.semantic-mediawiki.org/wiki/Change_propagation «change propagation»]. Egenskapssiden har blitt låst fram til den primære spesifikasjonsoppdateringen er fullført for å forhindre forstyrrelser i mellomtiden eller motstridende spesifikasjoner. Prosessen kan ta en liten stund før siden låses opp siden det avhenger av størrelsen og frekvensen til [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue jobbkøen].", "smw-property-req-violation-change-propagation-locked-warning": "Egenskapen «$1» ble endret og krever at tildelte entiteter reevalueres med prosessen [https://www.semantic-mediawiki.org/wiki/Change_propagation «change propagation»]. Oppdateringen kan ta en liten stund siden den avhenger av størrelsen og frekvensen til [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue jobbkøen], og det foreslås å utsette endringer av egenskapen for å forhindre forstyrrelser i mellomtiden eller motstridende spesifikasjoner.", "smw-property-req-violation-change-propagation-pending": "Oppdateringer med [https://www.semantic-mediawiki.org/wiki/Change_propagation «change propagation»] venter ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|jobb|jobber}}] estimert) og det anbefales å vente med endringer i egenskapen til prosessen er fullført for å forhindre forstyrrelser i mellomtiden eller motstridende spesifikasjoner.", "smw-property-req-violation-missing-maps-extension": "Semantic MediaWiki oppdaget ikke utvidelsen [https://www.semantic-mediawiki.org/wiki/Extension:Maps «Maps»] som er en avhengighet, og som følge av dette begrenses funksjonaliteten (f.eks. muligheten til å lagre og prosessere geografiske data) til denne egenskapen.", "smw-property-req-violation-type": "Egenskapen inneholder motstridende typespesifikasjoner som kan resultere i ugyldige verdiannoteringer, derfor forventes det at brukeren tildeler én passende type.", "smw-property-req-error-list": "Egenskapen inneholder følgende feil eller advarsler:", "smw-property-req-violation-parent-type": "Egenskapen «$1» og den tildelte foreldreegenskapen «$2» har forskjellige typeannoteringer.", "smw-property-req-violation-forced-removal-annotated-type": "Håndhevelsen av [https://www.semantic-mediawiki.org/wiki/Help:Mandatory_parent_type_inheritance obligatorisk foreldretypearv] har blitt slått på, annoteringstypen for egenskapen «$1» matcher ikke typen til foreldreegenskapen «$2» og har blitt endret for å reflektere det kravet. Det anbefales å tilpasse typedefinisjonene på sider så feilbeskjeden og obligatorisk håndhevelse fjernes for denne egenskapen.", "smw-change-propagation-protection": "<PERSON>ne siden er låst for å forhindre dataendring mens en [https://www.semantic-mediawiki.org/wiki/Change_propagation «change propagation»]-oppdatering kjøres. Prosessen kan ta en liten stund før siden låses opp, siden det avhenger av størrelsen og frekvensen til [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue jobbkøen].", "smw-category-change-propagation-locked-error": "Kategorien «$1» ble endret og krever at tildelte entiteter reevalueres med bruk av prosessen [https://www.semantic-mediawiki.org/wiki/Change_propagation «change propagation»]. Kategorisiden har blitt låst fram til den primære spesifikasjonsoppdateringen er fullført for å forhindre forstyrrelser i mellomtiden eller motstridende spesifikasjoner. Prosessen kan ta en liten stund før siden låses opp siden det avhenger av størrelsen og frekvensen til [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue jobbkøen].", "smw-category-change-propagation-locked-warning": "Kategorien «$1» ble endret og krever at tildelte entiteter reevalueres med bruk av prosessen [https://www.semantic-mediawiki.org/wiki/Change_propagation «change propagation»]. Oppdateringen kan ta en liten stund fordi den avhenger av størrelsen og frekvensen til [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue jobbkøen], og det anbefales at du venter med å gjøre endringer i kategorien for å forhindre forstyrrelser i mellomtiden eller motstridende spesifikasjoner.", "smw-category-change-propagation-pending": "Oppdateringer med [https://www.semantic-mediawiki.org/wiki/Change_propagation «change propagation»] venter ($1 [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue {{PLURAL:$1|jobb|jobber}}] estimert) og det anbefales å vente med endringer i kategorien til prosessen er fullført for å forhindre forstyrrelser i mellomtiden eller motstridende spesifikasjoner.", "smw-category-invalid-value-assignment": "«$1» gjenkjennes ikke som en gyldig kategori- el<PERSON> verdiannoteringe.", "protect-level-smw-pageedit": "Tillat kun brukere med sideredigeringstillatelse (Semantic MediaWiki)", "smw-create-protection": "Opprettelse av egenskapen «$1» er begrenset til brukere med den rette rettigheten (eller [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups brukergruppa]) «$2» mens [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode autoritetsmodus] er slått på.", "smw-create-protection-exists": "<PERSON><PERSON><PERSON> i egenskapen «$1» begrenses til brukere med rettigheten  (eller [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups brukergruppa]) «$2» mens [https://www.semantic-mediawiki.org/wiki/Help:Authority_mode autoritetsmodus] er slått på.", "smw-edit-protection": "<PERSON>ne siden er [[Property:Is edit protected|beskyttet]] for å forhindre utilsikted dataendring og kan kun redigeres av brukere med den riktige rettigheten eller [https://www.semantic-mediawiki.org/wiki/Help:User_rights_and_user_groups brukergruppa] «$1».", "smw-edit-protection-disabled": "Redigeringsbeskyttelsen er slått av og «$1» kan derfor ikke brukes for å beskytte entitetssider mot uautorisert redigering.", "smw-edit-protection-auto-update": "Semantic MediaWiki har oppdatert beskyttelsesstatusen i samsvar med egenskapen «Is edit protected».", "smw-edit-protection-enabled": "Redigeringsbeskyttet (Semantic MediaWiki)", "smw-patternedit-protection": "<PERSON>ne siden er beskyttet og kan kun redigeres av brukere med den rette [https://www.semantic-mediawiki.org/wiki/Help:Permissions tillatelsen] <code>smw-patternedit</code>.", "smw-property-predefined-edip": "«$1» er en forhåndsdefinert egenskap gitt av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å indikere hvorvidt redigering er beskyttet eller ikke.", "smw-property-predefined-long-edip": "Alle brukere er kvalifisert til å legge til denne egenskapen til et subjekt, men kun brukere med en spesiell tillatelse kan redigere eller fjerne beskyttelsen av en entitet etter at den har blitt lagt til.", "smw-query-reference-link-label": "Spørringsreferanse", "smw-format-datatable-emptytable": "Ingen data tilgjengelige i tabellen", "smw-format-datatable-info": "Viser _START_ til _END_ av _TOTAL_ oppføringer", "smw-format-datatable-infoempty": "Viser 0 til 0 av 0 oppføringer", "smw-format-datatable-infofiltered": "(filtrert fra totalt _MAX_  oppføringer)", "smw-format-datatable-lengthmenu": "Vis _MENU_ oppføringer", "smw-format-datatable-loadingrecords": "Laster …", "smw-format-datatable-processing": "Prosesserer …", "smw-format-datatable-search": "Søk:", "smw-format-datatable-zerorecords": "Ingen matchende opptegnelser funnet", "smw-format-datatable-first": "<PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-last": "<PERSON><PERSON>", "smw-format-datatable-next": "Neste", "smw-format-datatable-previous": "<PERSON><PERSON><PERSON>", "smw-format-datatable-sortascending": ": aktiver for å sortere kolonnen stigende", "smw-format-datatable-sortdescending": ": aktiver for å sortere kolonnen synkende", "smw-format-datatable-toolbar-export": "Eksporter", "smw-format-list-other-fields-open": "(", "smw-category-invalid-redirect-target": "Kategorien «$1» inneholder et ugyldig omdirigeringsmål til et navnerom som ikke er kategori.", "smw-parser-function-expensive-execution-limit": "Parserfunksjonen har nådd grensa for dyre kjøringer (se konfigurasjonsparameteren [https://www.semantic-mediawiki.org/wiki/Help:$smwgQExpensiveExecutionLimit <code>$smwgQExpensiveExecutionLimit</code>]).", "smw-postproc-queryref": "Semantic MediaWiki oppdaterer den gjeldende siden på grunn av etterprosessering av en spørring.", "apihelp-smwinfo-summary": "API-modul for å hente informasjon om Semantic MediaWiki-statistikk og annen metainformasjon.", "apihelp-ask-summary": "API-modul for å kjøre spørringer mot Semantic MediaWiki med ask-språket.", "apihelp-askargs-summary": "API-modul for å kjøre spørringer mot Semantic MediaWiki med ask-språket som en liste over forutsetninger, printouts og parametre.", "apihelp-browsebyproperty-summary": "API-modul for å hente informasjon om en egenskap eller en liste over egenskaper.", "apihelp-browsebysubject-summary": "API-modul for å hente informasjon om et subjekt.", "apihelp-smwtask-summary": "API-modul for å utføre Semantic MediaWiki-relaterte oppgaver (kun for intern bruk, ikke offentlig bruk).", "apihelp-smwbrowse-summary": "API-modul for å støtte leseaktiviteter for forskjellige entitetstyper i Semantic MediaWiki.", "apihelp-ask-parameter-api-version": "Resultatformat:\n;2:Bakoverkompatibelt format som bruker {} for resultatlista.\n;3:Eksperimentelt format som bruker [] for resultatlista.", "apihelp-smwtask-param-task": "Definerer oppgavetypen", "apihelp-smwtask-param-params": "JSON-kodede parametere som matcher valgte oppgavetypekrav", "smw-apihelp-smwtask-example-update": "Eksempel på å kjøre en oppdateringsoppgave for et visst subjekt:", "smw-api-invalid-parameters": "Ugyldige parametere, «$1»", "smw-parser-recursion-level-exceeded": "Nivået for $1 rekursjoner ble overskredet under en tolkingsprosess. Det anbefales å validere malstrukturen, eller hvis det er nødvendig justere konfigurasjonsparameteren <code>$maxRecursionDepth</code>.", "smw-property-page-list-count": "Viser $1 {{PLURAL:$1|side|sider}} som bruker denne e<PERSON>.", "smw-property-page-list-search-count": "Viser $1 {{PLURAL:$1|side|sider}} som bruker denne egenskapen med treff på verdien «$2».", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter Søkefilteret] gjør det mulig å bruke [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions spørringsuttrykk] som <code>~</code> eller <code>!</code>. Den valgte [https://www.semantic-mediawiki.org/wiki/Query_engine spørringsmotoren] kan også støtte matching uten hensyn til store/små bokstaver eller andre korte uttrykk, som:\n\n* <code>in:</code> resultatet bør inkludere begrepet, f.eks. «<code>in:Foo</code>»\n\n* <code>not:</code> resultatet bør ikke inkludere begrepet, f.eks. «<code>not:Bar</code>»", "smw-property-reserved-category": "<PERSON><PERSON><PERSON>", "smw-category": "<PERSON><PERSON><PERSON>", "smw-datavalue-uri-invalid-scheme": "«$1» har ikke blitt listet opp som et gyldig URI-format.", "smw-datavalue-uri-invalid-authority-path-component": "«$1» har lbitt identifisert til å inneholde en ugyldig autoritets- eller stikomponent «$2».", "smw-browse-property-group-title": "Egenskapsgruppe", "smw-browse-property-group-label": "Etikett for egenskapsgruppe", "smw-browse-property-group-description": "Beskrivelse for egenskapsgruppe", "smw-property-predefined-ppgr": "«$1» er en forhåndsdefinert egenskap som identifiserer entiteter (primært kategorier) som brukes som grupperingsinstanser for egenskaper og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-filter": "Filter", "smw-section-expand": "<PERSON><PERSON><PERSON>", "smw-section-collapse": "<PERSON><PERSON><PERSON> sammen seks<PERSON>en", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]-format", "smw-help": "<PERSON><PERSON><PERSON><PERSON>", "smw-cheat-sheet": "<PERSON><PERSON><PERSON><PERSON>", "smw-personal-jobqueue-watchlist": "Jobbjø-overvåkningsliste", "smw-personal-jobqueue-watchlist-explain": "Tallet indikerer et estimat for jobbkøoppføringer som venter på kjøring.", "smw-property-predefined-label-skey": "So<PERSON>ingsnøkkel", "smw-processing": "Prosesserer …", "smw-loading": "Laster …", "smw-fetching": "<PERSON><PERSON> …", "smw-preparing": "Forbereder …", "smw-expand": "<PERSON><PERSON><PERSON>", "smw-collapse": "S<PERSON>å sammen", "smw-copy": "<PERSON><PERSON><PERSON>", "smw-copy-clipboard-title": "Ko<PERSON>rer innholdet til utklippstavlen", "smw-jsonview-expand-title": "Utvider JSON-visningen", "smw-jsonview-collapse-title": "Slår sammen JSON-visningen", "smw-jsonview-search-label": "Søk:", "smw-redirect-target-unresolvable": "Målet kan ikke løses fordi «$1»", "smw-types-title": "Type: $1", "smw-schema-namespace-editcontentmodel-disallowed": "Endring av innholdsmodellen til en [https://www.semantic-mediawiki.org/wiki/Help:<PERSON><PERSON>a skjemas<PERSON>] er ikke tillatt.", "smw-schema-namespace-edit-protection": "Siden er beskyttet og kan kun redigeres av brukere med [https://www.semantic-mediawiki.org/wiki/Help:Permissions tillatelsen] <code>smw-schemaedit</code>.", "smw-schema-namespace-edit-protection-by-import-performer": "Denne siden ble importert av en opplistet [https://www.semantic-mediawiki.org/wiki/Import_performer importeringsutfører], hvilket betyr at endring av innholdet på denne siden er begrenset til de opplistede brukerne.", "smw-schema-error-title": "{{PLURAL:$1|Valideringsfeil}}", "smw-schema-error-schema": "Valideringsskjemaet '''$1''' fant følgende inkonsekvenser:", "smw-schema-error-miscellaneous": "Udefinert feil ($1)", "smw-schema-error-validation-json-validator-inaccessible": "JSON-validatoren «<b>$1</b>» er ikke tilg<PERSON> (eller installert), hvilket er grunnen til at fila «$2» ikke kan under<PERSON><PERSON><PERSON>, som hindrer den gjeldende siden fra å bli lagret eller endret.", "smw-schema-error-validation-file-inaccessible": "Valideringsfila «$1» er utilgjengelig.", "smw-schema-error-violation": "[«$1», «$2»]", "smw-schema-error-type-missing": "Konspetet mangler en type for at den skal kunne gjenkjennes og brukes i [https://www.semantic-mediawiki.org/wiki/Help:Schema skjemanavnerommet].", "smw-schema-error-type-unknown": "Typen «$1» er ikke registrert og kan ikke brukes for innhold i [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema]-navnerommet.", "smw-schema-error-json": "JSON-feil: «$1»", "smw-schema-error-input": "Inndatavalideringen fant følgende feil, de må fikses før innholdet kan lagres. [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling Skjemahjelpesiden] kan gi noen råd om hvordan man fjerner inkonsekvenser eller løser feil med skjemaets inndata.", "smw-schema-error-input-schema": "Valideringsskjemaet '''$1''' fant følgende inkonsekvenser, og de må fikses før innholdet kan lagres. [https://www.semantic-mediawiki.org/wiki/Help:Schema/Error_handling Skjemahjelpesiden] kan gi noen råd for hvordan man kan fikse disse feilene.", "smw-schema-error-title-prefix": "Denne skjematypen krever at tittelen til skjemaet begynner med prefikset «$1».", "smw-schema-validation-error": "Typen «$1» er ikke registrert og kan ikke brukes for innhold i [https://www.semantic-mediawiki.org/wiki/Help:Schema smw/schema]-navnerommet.", "smw-schema-validation-schema-title": "JSON-skjema", "smw-schema-summary-title": "<PERSON><PERSON><PERSON><PERSON>", "smw-schema-title": "Sk<PERSON><PERSON>", "smw-schema-usage": "Bruk", "smw-schema-type": "Skjematype", "smw-schema-type-description": "Typebeskrivelse", "smw-schema-description": "Skjemabeskrivelse", "smw-schema-description-link-format-schema": "Denne skjematypen støtter definisjonen av karakteristikker for å opprette kontekstsensitive lenker i tilknytning til en [[Property:Formatter schema|formatskjema]]-tildelt egenskap.", "smw-schema-description-search-form-schema": "Denne skjematypen støtter definisjonen av inndataformer og -karakteristikker for den [https://www.semantic-mediawiki.org/wiki/Help:SMWSearch utvidede søkeprofilen] der den inneholder instruksjoner for hvordan man genererer inndatafelt, definerer standard navnerom eller erklærer prefiksuttrykk for en søkeforespørsel.", "smw-schema-description-property-profile-schema": "Denne skjematypen støtter definisjonen av en profil for å erklære karakteristikker for den tildelte egenskapen og dens annoteringsverdier.", "smw-schema-description-property-group-schema": "Denne skjematypen støtter definisjonen av [https://www.semantic-mediawiki.org/wiki/Help:Property_group egenskapsgrupper] for å hjelpe strukteren til [https://www.semantic-mediawiki.org/wiki/Help:Special:<PERSON><PERSON><PERSON> les<PERSON>grensesnitte<PERSON>].", "smw-schema-description-property-constraint-schema": "<PERSON><PERSON> støtter definisjonen av begrensningsregler for en egenskapsinstans samt de verdiene som er tildelt den.", "smw-schema-description-class-constraint-schema": "Denne skjematypen støtter definisjonen av begrensningsregler for en klasseinstans (også kjent som en kategori).", "smw-schema-tag": "{{PLURAL:$1|Tagg|Tagger}}", "smw-property-predefined-constraint-schema": "«$1» er en forhåndsdefinert egenskap som definererer et begrensningsskjema og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-desc": "«$1» er en forhåndsdefinert egenskap som lagrer en skjemabeskrivelse og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-def": "«$1» er en forhåndsdefinert egenskap som lagrer skjemainnhold og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-schema-tag": "«$1» er en forhåndsdefinert egenskap av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] for å identifisere en samling skjemaer.", "smw-property-predefined-long-schema-tag": "En etikett som identifiserer skjemaene til lignende innhold eller karakteristikk.", "smw-property-predefined-schema-type": "«$1» er en forhåndsdefinert egenskap som beskriver en type for å skjelne en gruppe skjemaer og gis av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-schema-type": "Hver [https://www.semantic-mediawiki.org/wiki/Help:Schema/Type type] gir sin egen tolkning av syntakselementene og applikasjonsreglene og kan uttrykkes med hjelp av et [https://www.semantic-mediawiki.org/wiki/Help:Schema#validation valideringsskjema].", "smw-ask-title-keyword-type": "Nøkkelordsøk", "smw-ask-message-keyword-type": "Dette søket matcher vilkåret <code><nowiki>$1</nowiki></code>.", "smw-remote-source-unavailable": "Kunne ikke koble til eksternt mål «$1».", "smw-remote-source-disabled": "<PERSON><PERSON><PERSON> «$1» har slått av støtte for eksterne forespørsler!", "smw-remote-source-unmatched-id": "Kilden '''$1''' matcher ikke en versjon av Semantic MediaWiki som kan støtte en ekstern forespørsel.", "smw-remote-request-note": "Resultatet er hentet fra den eksterne kilden '''$1''', og det er sannsynlig at generert innhold inneholder informasjon som ikke er tilgjengelig fra den nåværende wikien.", "smw-remote-request-note-cached": "Resultatet er '''mellomlagret''' fra den eksterne kilden '''$1''', og det er sannsynlig at det genererte innholdet inneholder informasjon som ikke er tilgjengelig i den nåværende wikien.", "smw-parameter-missing": "Parameteren «$1» mangler.", "smw-property-tab-usage": "Bruk", "smw-property-tab-profile-schema": "Profilskjema", "smw-property-tab-redirects": "Synonymer", "smw-property-tab-subproperties": "Underegenskaper", "smw-property-tab-errors": "Feilaktige tildelinger", "smw-property-tab-constraint-schema": "Begrensningsskjema", "smw-property-tab-constraint-schema-title": "Kompilert begrensningsskjema", "smw-property-tab-specification": "… mer", "smw-concept-tab-list": "Liste", "smw-concept-tab-errors": "<PERSON><PERSON>", "smw-ask-tab-result": "Resultat", "smw-ask-tab-extra": "Ekstra", "smw-ask-tab-debug": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-tab-code": "<PERSON><PERSON>", "smw-install-incomplete-tasks-title": "Ufullstendige administrasjonsoppgaver", "smw-install-incomplete-intro": "Det er $2 {{PLURAL:$2|ufullstendig|ufullstendige}} eller [[Special:PendingTaskList|ventende]] {{PLURAL:$2|oppgave|oppgaver}} for å fullføre {{PLURAL:$1|installasjonen|oppgraderingen}} av [https://www.semantic-mediawiki.org Semantic MediaWiki]. En administrator eller bruker med nødvendige rettigheter kan fullføre {{PLURAL:$2|den|dem}}. Dette bør gjøres før nye data legges til for å unngå unøyaktigheter.", "smw-install-incomplete-intro-note": "<PERSON>ne beskjeden forsvinner når alle relevante oppgaver har blitt løst.", "smw-pendingtasks-intro-empty": "Ingen oppgaver har blitt klassifisert som ventende, ufullstendige eller ufullførte i forbindelse med Semantic MediaWiki.", "smw-pendingtasks-intro": "Denne siden gir informasjon om oppgaver som har blitt klassifisert som ventende, ufullstendige eller utestående i forbindelse med Semantic MediaWiki.", "smw-pendingtasks-setup-no-tasks-intro": "Installasjonen (eller oppgrade<PERSON>) har blitt fullført, det er ingen ventende eller utestående oppgaver.", "smw-pendingtasks-tab-setup": "<PERSON><PERSON><PERSON>", "smw-updateentitycollation-incomplete": "Innstillingen <code>[https://www.semantic-mediawiki.org/wiki/Help:$smwgEntityCollation $smwgEntityCollation] ble nylig endret, og krever at skriptet <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCollation.php updateEntityCollation.php]</code> kj<PERSON><PERSON> slik at entitetene er oppdatert og inneholder riktig sorteringsfeltverdi.", "smw-updateentitycountmap-incomplete": "Feltet <code>smw_countmap</code> ble lagt til i en ny utgivelse og krever at skriptet <code>[https://www.semantic-mediawiki.org/wiki/Help:updateEntityCountMap.php updateEntityCountMap.php]</code> k<PERSON><PERSON><PERSON> slik at funksjoner kan få tilgang til innholdet i dette feltet.", "smw-populatehashfield-incomplete": "Befolkningen av feltet <code>smw_hash</code> ble hoppet over under oppsettet. Skriptet <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> må kjøres.", "smw-install-incomplete-populate-hash-field": "Befolkningen av feltet <code>smw_hash</code> ble hoppet over under oppsettet. Skriptet <code>[https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php]</code> må kjøres.", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code> har blitt valgt som [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore standardlager], men utvidelsen kunne ikke finne noen opptegnelser på at skriptet <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> ble kjørt. Kjør skriptet som instruert.", "smw-elastic-rebuildelasticindex-run-incomplete": "<code>ElasticStore</code> har blitt valgt som [https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore standardlager], men utvidelsen kunne ikke finne noen opptegnelser på at skriptet <code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> ble kjørt. Kjør skriptet som instruert.", "smw-pendingtasks-setup-intro": "{{PLURAL:$1|Installeringen|Oppgraderingen}} av <b>Semantic MediaWiki</b> har klassifisert følgende oppgaver som [https://www.semantic-mediawiki.org/wiki/Help:Upgrade/Incomplete_upgrade ufullstendige], og en administrator (eller en annen bruker med rette rettigheter) forventes å løse disse oppgavene før brukere kan fortsette å opprette eller endre innhold.", "smw-pendingtasks-setup-tasks": "Oppgaver", "smw-filter-count": "Antall filtre", "smw-es-replication-check": "Replikasjonssjekk (ElasticSearch)", "smw-es-replication-error": "Replikasjonsproblem i Elasticsearch", "smw-es-replication-file-ingest-error": "Problem med filhenting", "smw-es-replication-maintenance-mode": "Elasticsearch-vedlikehold", "smw-es-replication-error-missing-id": "Replikasjonsovervåkningen har funnet at artikkelen «$1» (ID: $2) mangler i ElasticSearchs bakstykke.", "smw-es-replication-error-divergent-date": "Replikasjonsovervåkningen har funnet ut at for artikkelen «$1» (ID: $2) er det inkonsekvenser i <b>endringsdatoen</b>.", "smw-es-replication-error-divergent-date-short": "Følgende datoinformasjon ble brukt for sammenligning:", "smw-es-replication-error-divergent-date-detail": "Referert endringsdato:\n* ElasticSearch: $1\n* Database: $2", "smw-es-replication-error-divergent-revision": "Replikasjonsovervåkningen har funnet ut at den <b>tilknyttede revisjonen</b> for artikkelen «$1» (ID: $2) viser inkonsekvens.", "smw-es-replication-error-divergent-revision-short": "Følgende assosierte revisjonsdata ble brukt for sammenligning:", "smw-es-replication-error-divergent-revision-detail": "Referert til<PERSON>ttet sideversjon:\n* ElasticSearch: $1\n* Database: $2", "smw-es-replication-error-maintenance-mode": "Replikasjonen til Elasticsearch re for tiden begrenset fordi den drives i [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Maintenance_mode <b>vedlikeholdsmodus</b>]; endringer i entiteter og sider blir <b>ikke</b> umid<PERSON><PERSON><PERSON> synlig, og spørringsresultater kan inneholde utdatert informasjon.", "smw-es-replication-error-no-connection": "Replikasjonsovervåkningen kan ikke utføre noen sjekker fordi den ikke kan etablere en tilkobling til ElasticSearch-klyngen.", "smw-es-replication-error-bad-request-exception": "ElasticSearch-tilkoblingshåndtereren har avgitt et unntak for ugyldig forespørsel («400 conflict http error») som indikerer et fortsettelsesproblem under replikasjons- og søkeforespørsler.", "smw-es-replication-error-other-exception": "Elasticsearch-tilkoblingsbehandleren har returnert et unntak: «$1».", "smw-es-replication-error-suggestions": "Det anbefales å redigere eller gjenoppfriske mellomlageret til siden for å fjerne inkonsekvensen. Hvis problemer fortsetter, sjekk selve ElasticSearch-klyngen.", "smw-es-replication-error-suggestions-maintenance-mode": "Det anbefales å kontakte wikiens administrator for å sjekke hvorvidt en [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Index_rebuild indeksgjenoppbygging] er på gang eller om <code>refresh_interval</code> ikke har blitt satt til den forventede standardverdien.", "smw-es-replication-error-suggestions-no-connection": "Det foreslås å kontakte wikiens administrator og rapportere feilen for «ingen tilkobling»…", "smw-es-replication-error-suggestions-exception": "<PERSON><PERSON><PERSON><PERSON> loggene for informasjon om statusen til ElasticSearch, dens indekser og mulige feilkonfigurasjoner.", "smw-es-replication-error-file-ingest-missing-file-attachment": "Replikasjonsovervåkningen har funnet ut at «$1» mangler en [[Property:File attachment|File attachment]]-annotering, som indikererer at filhentingsprosessoren ikke har begynt eller ikke har fullført.", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "Sjekk at [https://www.semantic-mediawiki.org/wiki/Help:ElasticSearch/File_ingestion file ingest]-jobben er planlagt og fullført før annoteringen og filindeksen er gjort tilgjengelig.", "smw-report": "Rapport", "smw-legend": "Tegnforklaring", "smw-datavalue-constraint-schema-category-invalid-type": "Det annoterte skjemaet «$1» er ugyldig for en kategori, det krever typen «$2».", "smw-datavalue-constraint-schema-property-invalid-type": "Det annoterte skjemaet «$1» er ugyldig for en egenskap, det krever typen «$2».", "smw-entity-examiner-check": "Kj<PERSON>rer {{PLURAL:$1|en undersøker|undersøkere}} i bakgrunnen", "smw-entity-examiner-indicator": "Entitetsproblempanel", "smw-entity-examiner-deferred-check-awaiting-response": "<PERSON><PERSON><PERSON><PERSON><PERSON> «$1» venter på svar fra bakstykket.", "smw-entity-examiner-deferred-elastic-replication": "Elastic", "smw-entity-examiner-deferred-constraint-error": "Begrensning", "smw-entity-examiner-associated-revision-mismatch": "Versjon", "smw-entity-examiner-deferred-fake": "Falsk", "smw-entity-examiner-indicator-suggestions": "Som del av en entitetsundersøkelse ble følgende {{PLURAL:$1|problem|problemer}} funnet, og det foreslås at du går gjennom {{PLURAL:$1|det|dem}} og tar riktig grep.", "smw-indicator-constraint-violation": "{{PLURAL:$1|Begrensning|Begrensninger}}", "smw-indicator-revision-mismatch": "Versjon", "smw-indicator-revision-mismatch-error": "Sjekken for [https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner assosiert versjon] fant manglende samsvar mellom versjonen som refereres til i MediaWiki og den som er assosiert med Semantic MediaWiki for denne entiteten.", "smw-indicator-revision-mismatch-comment": "Manglende samsvar indi<PERSON>er vanli<PERSON>vis at en prosess forstyrret lagringsoperasjonen i Semantic MediaWiki. Det anbefales å gå gjennom tjenerloggene og se etter unntak eller andre feil.", "smw-listingcontinuesabbrev": "forts.", "smw-showingresults": "Nedenfor vises opptil {{PLURAL:$1|'''ett''' resultat|'''$1''' resultater}} fra og med nummer <b>$2</b>."}