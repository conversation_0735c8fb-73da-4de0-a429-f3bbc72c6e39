{"@metadata": {"authors": ["AdiJapan", "<PERSON>", "<PERSON>", "<PERSON>", "Firilacroco", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Minisarm", "NGC 54", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WebSourceContentRO"]}, "smw-desc": "Wikiul dumneavoastră devine mai accesibil — pentru ma<PERSON> ''și'' oameni ([https://www.semantic-mediawiki.org/wiki/Help:User_manual documentație on-line])", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] a fost instalat și activat, dar lips<PERSON><PERSON><PERSON> o [https://www.semantic-mediawiki.org/wiki/Help:Upgrade cheie de actualizare] corespunzătoare.", "smw-upgrade-error-why-title": "De ce văd această pagină?", "smw-upgrade-error-why-explain": "Structura internă a bazei de date interne a MediaWiki se modifică și necesită unele ajustări pentru a fi pe deplin funcțională. Pot exista mai multe motive, printre care:\n* Au fost adăugate proprietăți fixe suplimentare (necesită configurarea suplimentară a tabelului)\n* Un upgrade conține unele modificări la tabele sau indicii care fac obligatorie interceptarea înainte de a accesa datele", "smw-upgrade-error-how-title": "Cum pot remedia această eroare?", "smw_viewasrdf": "Flux RDF", "smw_finallistconjunct": "și", "smw_isspecprop": "Această proprietate este una specială pe acest wiki.", "smw_concept_description": "Descrierea conceptului „$1”", "version-semantic": "Extensii semantice", "smw_printername_count": "Numără rezultatele", "smw_printername_csv": "Export CSV", "smw_printername_dsv": "Export DSV", "smw_printername_debug": "Interogare de depanare (pentru experți)", "smw_printername_embedded": "Conținut de pagini încorporate", "smw_printername_json": "Export JSON", "smw_printername_list": "Listă", "smw_printername_ol": "Listă numerotată", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_template": "Format", "smw_printername_rdf": "Export RDF", "smw_printername_category": "Categorie", "validator-type-class-SMWParamSource": "text", "smw_iq_disabled": "Interogările semantice au fost dezactivate pe acest wiki.", "smw_iq_moreresults": "… mai multe rezultate", "smw_parseerror": "Valoarea oferită nu este înțeleasă.", "smw_notitle": "„$1” nu poate fi folosit ca nume de pagină în acest wiki.", "smw_wrong_namespace": "Numai paginile din spațiul de nume \"$1\" sunt permise aici.", "smw_emptystring": "<PERSON><PERSON><PERSON><PERSON> vide nu sunt acceptate.", "smw_notinenum": "„$1” nu este în lista de [[Property:Allows value|valori permise]] ($2) pentru proprietatea „$3”.", "smw_noboolean": "„$1” nu este recunoscut ca valoare booleană (adevărat/fals).", "smw_nofloat": "„$1” nu este număr.", "smw_infinite": "Numerele mari precum „$1” nu sunt suportate.", "smw_nodatetime": "Data „$1” nu a fost înțeleasă.", "smw_unexpectedpart": "Partea „$1” a interogării nu a fost înțeleasă.\nRezultatele pot fi neașteptate.", "smw_propvalueproblem": "Valoarea proprietății „$1” nu a fost înțeleasă.", "smw_type_header": "Proprietăți de tipul „$1”", "smw_attribute_header": "Pagini care folosesc proprietatea „$1”", "exportrdf": "Exportare pagini în RDF", "smw_exportrdf_submit": "Export", "uriresolver": "Rezolvator URI", "properties": "Propriet<PERSON><PERSON><PERSON>", "smw-categories": "Categorii", "smw_properties_docu": "Următoarele proprietăți sunt folosite în wiki.", "smw_property_template": "$1 de tipul $2 ($3 {{PLURAL:$3|utilizare|utilizări|de utilizări}})", "smw_propertylackstype": "Nici un tip nu a fost specificat pentru această proprietate (presupunem deocamdată tipul $1).", "smw_propertyhardlyused": "Această proprietate este puțin folosită în wiki!", "unusedproperties": "Proprietăți neutilizate", "smw-unusedproperties-docu": "Această pagină listează [https://www.semantic-mediawiki.org/wiki/Unused_properties proprietățile nefolosite] care nu sunt declarate deși nicio altă pagină nu le folosește. Pentru o imagine diferențiată, vedeți paginile speciale pentru [[Special:Properties|toate proprietățile]] sau [[Special:WantedProperties|proprietățile dorite]]", "smw-unusedproperty-template": "$1 de tipul $2", "wantedproperties": "Proprietăți dorite", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|utilizare|utiliz<PERSON><PERSON>}})", "smw_purge": "Re<PERSON><PERSON><PERSON><PERSON><PERSON>", "types": "Tipuri", "ask": "Căutare semantică", "smw_ask_sortby": "So<PERSON><PERSON> după coloană (opțional)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_descorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "Găsește rezultate", "smw_ask_editquery": "Modifică interogarea", "smw_add_sortcondition": "[Adaugă o condiție de sortare]", "smw_ask_hidequery": "Ascunde interogarea (afișare compactă)", "smw_ask_help": "<PERSON><PERSON><PERSON> despre inter<PERSON>re", "smw_ask_queryhead": "Condiție", "smw_ask_printhead": "Selecție de imprimare", "smw_ask_printdesc": "(ad<PERSON><PERSON><PERSON><PERSON> câte un nume de proprietate pe fiecare rând)", "smw_ask_format_as": "Formatare ca:", "smw_ask_defaultformat": "implicit", "smw_ask_otheroptions": "Alte opțiuni", "smw-ask-otheroptions-collapsed-info": "Utilizați pictograma „plus” pentru a vedea toate opțiunile disponibile", "smw_ask_show_embed": "Arată codul de încorporat", "smw_ask_hide_embed": "Ascunde codul de încorporat", "smw_ask_embed_instr": "Pentru a încorpora această interogare într-o pagină wiki, utilizați codul de mai jos.", "smw-ask-delete": "Șterge", "smw-ask-sorting": "Sortare", "searchbyproperty": "Căutare după proprietate", "smw_sbv_docu": "<PERSON><PERSON><PERSON> toate paginile care au anumite proprietăți și valori.", "smw_sbv_property": "Proprietate:", "smw_sbv_value": "Valoare:", "smw_sbv_submit": "Găsește rezultate", "browse": "Răsfoire wiki", "smw_browselink": "Răsfoire proprietăți", "smw_browse_article": "Introduceți numele paginii de la care să porniți navigarea.", "smw_browse_go": "<PERSON><PERSON><PERSON>", "smw_browse_show_incoming": "Afișează proprietățile care trimit aici", "smw_browse_hide_incoming": "Ascunde proprietățile care trimit aici", "smw_browse_no_outgoing": "Această pagină nu are proprietăți.", "smw_browse_no_incoming": "Nici o proprietate nu trimite la această pagină.", "smw_inverse_label_default": "$1 din", "smw_pp_from": "De la pagina:", "smw_pp_type": "Proprietate:", "smw_pp_submit": "Găsește rezultate", "smw_result_prev": "Precedent", "smw_result_next": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_results": "Rezultate", "smw_result_noresults": "<PERSON>e pare rău, niciun rezultat.", "smwadmin": "Dashboard (Semantic MediaWiki)", "smw_smwadmin_return": "Înapoi la $1", "smw_smwadmin_datarefreshbutton": "Planifică reconstrucția datelor", "smw_smwadmin_datarefreshstop": "Oprește această actualizare", "smw_smwadmin_datarefreshstopconfirm": "<PERSON>, sunt {{GENDER:$1|sigur|sigură}}.", "smw-admin-support": "Obținere de ajutor", "smw-admin-supplementary-elastic-mappings-summary": "<PERSON><PERSON><PERSON>", "smw_adminlinks_datastructure": "Structura datelor", "smw_adminlinks_displayingdata": "Afișarea datelor", "smw-createproperty-isproperty": "Aceasta este o proprietate de tipul $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Valoarea admisă pentru această proprietate este|Valorile admise pentru această proprietate sunt}}:", "smw-info-par-message": "<PERSON><PERSON>.", "smw-ui-tooltip-title-property": "Proprietate", "smw-ui-tooltip-title-quantity": "Conversie de unități", "smw-ui-tooltip-title-info": "Informații", "smw-ui-tooltip-title-warning": "Avertizare", "smw-ui-tooltip-title-parameter": "Parametru", "smw-ui-tooltip-title-event": "Eveniment", "smw-ui-tooltip-title-note": "Notă", "smw_unknowntype": "Tipul „$1” al acestei proprietăți nu este valid", "smw_concept_header": "Pagini ale conceptului „$1”", "smw-qp-empty-data": "Imposibil de afișat datele solicitate ca urmare a unor criterii de selecție insuficiente.", "group-smweditor": "Editori (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|editor (Semantic MediaWiki)|editoare (Semantic MediaWiki)|editor (Semantic MediaWiki)}}", "smw-livepreview-loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "log-name-smw": "Jurnal Semantic MediaWiki", "log-show-hide-smw": "$1 Jurnal Semantic MediaWiki", "logeventslist-smw-log": "Jurnal Semantic MediaWiki", "smw-property-tab-usage": "<PERSON><PERSON><PERSON><PERSON>", "smw-property-tab-redirects": "Sinonime", "smw-property-tab-subproperties": "Subproprietăți", "smw-concept-tab-list": "Listă", "smw-concept-tab-errors": "<PERSON><PERSON><PERSON>", "smw-install-incomplete-intro": "Instalarea (sau actualizarea) aplicației <b>Semantic MediaWiki</b> nu a fost finalizată și un administrator ar trebui să execute următoarele activități pentru a preveni inconsecvențele de date înainte ca utilizatorii să continue să creeze sau să modifice conținut.", "smw-install-incomplete-populate-hash-field": "Populația de câmp <code>smw_hash</code> a fost omisă în timpul configur<PERSON><PERSON>i, este necesar să fie executat scriptul [https://www.semantic-mediawiki.org/wiki/Help:populateHashField.php populateHashField.php].", "smw-listingcontinuesabbrev": "cont.", "smw-showingresults": "Mai jos {{PLURAL:$1|apare '''1''' rezultat|apar '''$1''' rezultate|apar '''$1''' de rezultate}} începând cu nr. <b>$2</b>."}