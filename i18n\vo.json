{"@metadata": {"authors": ["Mal<PERSON>aya", "Smeira"]}, "smw_finallistconjunct": ", e", "smw_isspecprop": "<PERSON><PERSON><PERSON> at binon patöf patik in vük at.", "smw_concept_description": "Bepenam <PERSON>: „$1“.", "smw_no_concept_namespace": "Suemods kanons pamiede<PERSON>ön te su pads nemadaspada: <PERSON><PERSON><PERSON>.", "smw_multiple_concepts": "Suemodapad alik dalon labön suemodimiedeti te bali.", "smw_baduri": "Els URI ön fom: „$1“ no padälons.", "smw_iq_moreresults": "... seks pluik", "smw_parseerror": "<PERSON><PERSON><PERSON> pegivöl no pesuemon.", "smw_notitle": "„$1“ no dalon page<PERSON><PERSON><PERSON> as pad<PERSON>m in vük at.", "smw_wrong_namespace": "Te pades se nemaspad: „$1“ padälons ad binön is.", "smw_manytypes": "Sots plu bals pemiedetons pro patöf.", "smw_emptystring": "Vödems vagik no pelasumons.", "smw_notinenum": "„$1“ no komon in lised völadas mögik ($2) patöfa at.", "smw_true_words": "veratik,v,si,s", "smw_false_words": "dobik,d,no,n", "smw_nofloat": "„$1“ no binon num.", "smw_infinite": "Nums so gretik äs „$1“ no kanons pagebön.", "smw_nodatetime": "Dät: „$1“ no pesuemon.", "smw_emptysubquery": "Donaseivid semik no labon stipi lonöföl.", "smw_misplacedsubquery": "Donaseivid semik pägebon uto, kö donaseivids no padälons.", "smw_badqueryatom": "Dil semik: „<nowiki>[[…]]</nowiki>“ seivida no pesuemon.", "smw_propvalueproblem": "<PERSON><PERSON><PERSON>: „$1“ no pesuemon.", "smw_notemplategiven": "Gevolös v<PERSON> paramete: „samafomot“, dat seividafomät at ojäfidon.", "smw_type_header": "<PERSON><PERSON><PERSON> sota: „$1“", "smw_typearticlecount": "{{PLURAL:$1|Patöf $1 pajonon, kel labon|Patöfs $1 pajonons, kels labons}} soti at.", "smw_attribute_header": "<PERSON><PERSON>ö<PERSON>: „$1“ g<PERSON><PERSON><PERSON>", "smw_attributearticlecount": "{{PLURAL:$1|Pad $1 pajonon, kel gebon|Pads $1 pajonons, kels gebons}} pat<PERSON>fi at.", "exportrdf": "<PERSON><PERSON><PERSON><PERSON><PERSON> padis at RDF", "properties": "<PERSON><PERSON><PERSON>", "smw-categories": "Klads", "smw_properties_docu": "<PERSON><PERSON><PERSON> sökö<PERSON> pagebons in vük.", "smw_property_template": "$1 sota: $2 ($3)", "smw_propertylackspage": "Pat<PERSON><PERSON> valik sötons pabepenön su pad semik!", "smw_propertyhardlyused": "<PERSON><PERSON><PERSON> at pagebon vemo selediko in vük!", "unusedproperties": "Pat<PERSON><PERSON> no pegeböls", "smw-unusedproperties-docu": "<PERSON><PERSON><PERSON><PERSON>, do pad votik nonik gebon onis.", "smw-unusedproperty-template": "$1 sota: $2", "wantedproperties": "<PERSON><PERSON><PERSON>", "smw-wantedproperties-docu": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> pagebons in vük ab no nog labons bepenamapadis oksik.", "smw-wantedproperty-template": "$1 (gebs $2)", "types": "Sots", "smw_ask_submit": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_editquery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_add_sortcondition": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]", "smw_ask_hidequery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>", "smw_ask_queryhead": "<PERSON><PERSON><PERSON>", "searchbyproperty": "<PERSON><PERSON><PERSON><PERSON> ma <PERSON>f", "smw_sbv_docu": "Sukön padis valik patö<PERSON> e völadi semi<PERSON> labö<PERSON>.", "smw_sbv_novalue": "Penolös völadi lonö<PERSON>l patö<PERSON>, u logolös patöfavöladis valik tefü „$1“.", "smw_sbv_displayresultfuzzy": "Lised padas valik labü völad: „$1“ patöfa: „$1“.\nBi petuvons pads te aniks, i völads nilöfik palisedons.", "smw_sbv_property": "<PERSON><PERSON><PERSON>", "smw_sbv_value": "Völad:", "smw_sbv_submit": "<PERSON><PERSON><PERSON><PERSON>", "browse": "<PERSON><PERSON><PERSON><PERSON>", "smw_browselink": "<PERSON><PERSON><PERSON><PERSON>", "smw_browse_article": "<PERSON><PERSON><PERSON><PERSON> nemi pada, de kel padam oprimon.", "smw_browse_go": "<PERSON><PERSON><PERSON><PERSON>", "smw_browse_show_incoming": "<PERSON><PERSON><PERSON><PERSON> is<PERSON>", "smw_browse_hide_incoming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is<PERSON>", "smw_browse_no_outgoing": "Pad at no labon patöfis.", "smw_browse_no_incoming": "<PERSON><PERSON><PERSON> nonik peyü<PERSON> lü pad at.", "smw_inverse_label_default": "$1 de", "pageproperty": "Suk <PERSON>ö<PERSON>s", "smw_pp_from": "De pad", "smw_pp_type": "<PERSON><PERSON><PERSON>", "smw_pp_submit": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_prev": "Büik", "smw_result_next": "Sök<PERSON><PERSON>", "smw_result_results": "Seks.", "smw_result_noresults": "Seks nonik.", "smw_smwadmin_return": "Geik<PERSON><PERSON> lü pad: $1", "smw_concept_header": "Pads suemoda: „$1“.", "smw_conceptarticlecount": "{{PLURAL:$1|Pajonon pad $1, kel duton|Pajonons pads $1, kels dutons}} lü suemod at.", "smw-livepreview-loading": "Pa<PERSON>od<PERSON>…", "smw-listingcontinuesabbrev": "(fov.)", "smw-showingresults": "Pajonons dono jü {{PLURAL:$1|sukasek '''1'''|sukaseks '''$1'''}}, primölo me nüm #'''$2'''."}