{"@metadata": {"authors": ["<PERSON><PERSON>", "<PERSON>ef", "ArlandGa", "Bagas <PERSON>", "<PERSON><PERSON>", "<PERSON>ud I.F<PERSON>", "Farras", "Hidayatsrf", "Ilham151096", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Leapofod", "Naval Scene", "Presidenvolksraad", "<PERSON><PERSON><PERSON><PERSON>", "Rachmat<PERSON>", "Rachmat04", "<PERSON><PERSON><PERSON>", "<PERSON>", "පසිඳු කාවින්ද", "아라"]}, "smw-desc": "Membuat wiki Anda lebih mudah diakses - oleh mesin ''dan'' manusia ([https://www.semantic-mediawiki.org/wiki/Help:User_manual dokumentasi daring])", "smw-error": "Galat", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] telah dipasang dan diaktifkan tetapi tidak punya [https://www.semantic-mediawiki.org/wiki/Help:Upgrade kunci peningkatan] yang se<PERSON><PERSON>.", "smw-upgrade-release": "Rilis", "smw-upgrade-progress": "<PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-error-why-title": "<PERSON><PERSON><PERSON> saya melihat halaman ini?", "smw-upgrade-error-how-title": "<PERSON>gai<PERSON> saya memperbaiki galat ini?", "smw-extensionload-error-why-title": "<PERSON><PERSON><PERSON> saya melihat halaman ini?", "smw-extensionload-error-how-title": "<PERSON>gai<PERSON> saya memperbaiki galat ini?", "smw-upgrade-maintenance-why-title": "<PERSON><PERSON><PERSON> saya melihat halaman ini?", "smw_viewasrdf": "Umpan RDF", "smw_finallistconjunct": ", dan", "smw-factbox-facts": "<PERSON><PERSON><PERSON>", "smw_isspecprop": "<PERSON><PERSON>t ini adalah sifat khusus di wiki ini.", "smw_concept_description": "Deskripsi konsep \"$1\"", "smw_no_concept_namespace": "<PERSON>nsep hanya dapat didefinisikan pada halaman dalam ruang nama Konsep:.", "smw_multiple_concepts": "<PERSON><PERSON><PERSON> halaman konsep hanya dapat memiliki satu konsep definisi.", "smw_concept_cache_miss": "Konsep \"$1\" tidak dapat digunakan saat ini karena konfigurasi wiki membutuhkannya untuk dihitung secara luring.\n<PERSON>ka masalah ini tidak hilang setelah beberapa waktu, mintalah pengelola situs untuk membuat konsep ini tersedia.", "smw_noinvannot": "<PERSON><PERSON> tidak dapat diberikan pada properti inversi.", "version-semantic": "Ekstensi semantik", "smw_baduri": "URI berbentuk \"$1\" tidak diperbolehkan.", "smw_printername_count": "<PERSON><PERSON>", "smw_printername_csv": "Expor CSV", "smw_printername_dsv": "Ekspor DSV", "smw_printername_debug": "Debug query (untuk ahli)", "smw_printername_embedded": "Sertakan isi halaman", "smw_printername_json": "Ekspor JSON", "smw_printername_list": "<PERSON><PERSON><PERSON>", "smw_printername_plainlist": "Daftar polos", "smw_printername_ol": "<PERSON><PERSON><PERSON> berno<PERSON>", "smw_printername_ul": "<PERSON><PERSON><PERSON> be<PERSON>oin", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON> lebar", "smw_printername_template": "Templat", "smw_printername_templatefile": "<PERSON><PERSON><PERSON> templat", "smw_printername_rdf": "Ekspor RDF", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "teks", "smw-paramdesc-limit": "<PERSON><PERSON><PERSON> maksimum hasil yang di<PERSON>ikan", "smw-paramdesc-headers": "<PERSON><PERSON><PERSON><PERSON> nama kepala/properti", "smw-paramdesc-mainlabel": "Label untuk nama halaman utama", "smw-paramdesc-link": "<PERSON><PERSON><PERSON><PERSON> nilai sebagai pranala", "smw-paramdesc-intro": "<PERSON><PERSON><PERSON> yang ditampilkan sebelum hasil kueri, jika ada", "smw-paramdesc-outro": "<PERSON><PERSON><PERSON> untuk ditampi<PERSON>an setelah hasil kueri, jika ada", "smw-paramdesc-default": "<PERSON><PERSON><PERSON> untuk ditampilkan jika tidak ada hasil kueri", "smw-paramdesc-sep": "<PERSON><PERSON><PERSON><PERSON> di antara nilai-nilai", "smw-paramdesc-propsep": "<PERSON><PERSON><PERSON><PERSON> di antara atribut-atribut entri hasil", "smw-paramdesc-valuesep": "<PERSON><PERSON><PERSON><PERSON> di antara nilai-nilai atribut hasil", "smw-paramdesc-showsep": "<PERSON><PERSON><PERSON><PERSON> pemisah di atas berkas CSV (\"sep=<nilai>\")", "smw-paramdesc-template": "Nama templat yang digunakan untuk menampilkan cetakan", "smw-paramdesc-columns": "<PERSON><PERSON><PERSON> kolom untuk menampilkan hasil", "smw-paramdesc-userparam": "<PERSON><PERSON>ah nilai dimasukkan ke setiap pencarian templat, bila sebuah templat digunakan", "smw-paramdesc-introtemplate": "<PERSON>a templat yang ditampilkan sebelum hasil pencarian, bila ada", "smw-paramdesc-outrotemplate": "<PERSON>a templat yang ditampilkan setelah hasil pencarian, bila ada", "smw-paramdesc-embedformat": "Tag HTML yang digunakan untuk menentukan judul", "smw-paramdesc-embedonly": "<PERSON><PERSON> tamp<PERSON> judul", "smw-paramdesc-rdfsyntax": "Sintaks RDF untuk digunakan", "smw-paramdesc-csv-sep": "Menentukan pemisah kolom", "smw-paramdesc-csv-valuesep": "<PERSON><PERSON><PERSON><PERSON> pemisah nilai", "smw-paramdesc-dsv-separator": "<PERSON><PERSON><PERSON><PERSON> yang dipakai", "smw-paramdesc-dsv-filename": "<PERSON><PERSON> be<PERSON>s DSV", "smw-paramdesc-searchlabel": "Teks untuk melanjutkan pencarian", "smw_iq_disabled": "Query semantik telah dinonaktifkan di wiki ini.", "smw_iq_moreresults": "… hasil lebih lan<PERSON>t", "smw_parseerror": "<PERSON><PERSON> yang diberikan tidak dipahami.", "smw_notitle": "\"$1\" tidak dapat digunakan sebagai nama halaman di wiki ini.", "smw_noproperty": "\"$1\" tidak dapat digunakan sebagai nama properti di wiki ini.", "smw_wrong_namespace": "<PERSON><PERSON> halaman pada ruang nama \"$1\" yang di<PERSON>inkan di sini.", "smw_manytypes": "Lebih dari satu tipe yang ditetapkan untuk properti.", "smw_emptystring": "Untaian kosong tidak diterima.", "smw_notinenum": "\"$1\" tidak ada dalam daftar ($2) dari [[Property:Allows value|nilai <PERSON>]] untuk atribut \"$3\".", "smw_noboolean": "\"$1\" tidak dikenali sebagai suatu ni<PERSON> (true/false)", "smw_true_words": "benar,t,ya,y", "smw_false_words": "salah,f,tidak,n", "smw_nofloat": "\"$1\" bukan angka.", "smw_infinite": "Angka sebesar \"$1\" tidak didukung.", "smw_unitnotallowed": "\"$1\" bukanlah satuan ukuran yang sah untuk properti ini.", "smw_nounitsdeclared": "Tidak ada satuan pengukuran yang dinyatakan untuk properti ini.", "smw_novalues": "Tidak ada nilai yang disebutkan.", "smw_nodatetime": "Tanggal \"$1\" tidak dipahami.", "smw_toomanyclosing": "Tampaknya ada terlalu banyak penyebutan \"$1\" pada query.", "smw_noclosingbrackets": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> \"<nowiki>[[</nowiki>\" pada query Anda tidak ditutup dengan pasangan \"]]\".", "smw_misplacedsymbol": "Simbol \"$1\" digunakan pada tempat yang mubazir.", "smw_unexpectedpart": "Bagian \"$1\" dari query tidak dipahami.\nHasilnya mungkin tidak seperti yang diharapkan.", "smw_emptysubquery": "Beberapa subquery tidak memiliki kondisi yang valid.", "smw_misplacedsubquery": "Beberapa subquery digunakan pada tempat yang tidak mengizinkan subquery.", "smw_valuesubquery": "Subquery tidak didukung untuk nilai dari properti \"$1\".", "smw_badqueryatom": "<PERSON><PERSON><PERSON> <PERSON><PERSON> \"<nowiki>[[…]]</nowiki>\" pada query tidak dipahami.", "smw_propvalueproblem": "<PERSON><PERSON> \"$1\" tidak dipahami.", "smw_noqueryfeature": "Beberapa fitur query belum didukung di wiki ini dan sebagian query diabaikan ($1).", "smw_noconjunctions": "Konjungsi dalam query tidak didukung dalam wiki ini dan sebagian dari query diabaikan ($1).", "smw_nodisjunctions": "Disjungsi dalam query tidak didukung dalam wiki ini dan sebagian dari query diabaikan ($1).", "smw_querytoolarge": "{{PLURAL:$2|<PERSON><PERSON><PERSON>}} kueri berikut tidak dapat diproses karena pembatas wiki terhadap ukuran atau kedalaman kueri: $1.", "smw_notemplategiven": "Berikan suatu nilai bagi parameter \"template\" agar format query ini dapat bekerja.", "smw_db_sparqlqueryproblem": "<PERSON><PERSON> kueri tidak dapat diambil dari basis data SPARQL. Galat ini mungkin sementara atau menandakan adanya bug dalam perangkat lunak basis data.", "smw_type_header": "Properti bertipe \"$1\"", "smw_typearticlecount": "Menampilkan $1 {{PLURAL:$1|properti|properti}} yang menggunakan tipe ini.", "smw_attribute_header": "Halaman yang menggunakan properti \"$1\"", "smw_attributearticlecount": "Menampilkan $1 {{PLURAL:$1|halaman|halaman}} yang menggunakan properti ini.", "smw-propertylist-subproperty-header": "Subatribut", "smw-propertylist-redirect-header": "Sinonim", "specialpages-group-smw_group-maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exportrdf": "Ekspor halaman ke RDF", "smw_exportrdf_docu": "Halaman ini mengizinkan Anda untuk mendapatkan data dari suatu halaman dalam format RDF.\nUntuk mengekspor halaman, masukkan judul di kotak teks di bawah, satu judul per baris.", "smw_exportrdf_recursive": "Ekspor semua halaman yang terkait secara rekursif.\nPerhatikan bahwa hasilnya bisa besar!", "smw_exportrdf_backlinks": "Ekspor juga semua halaman yang merujuk ke halaman yang diekspor.\nMenghasilkan RDF yang dapat ditelurusi.", "smw_exportrdf_lastdate": "Jangan mengekspor halaman yang tidak berubah sejak waktu yang diberikan.", "smw_exportrdf_submit": "Ekspor", "uriresolver": "Pengurai URI", "properties": "Atribut", "smw-categories": "<PERSON><PERSON><PERSON> kategori", "smw_properties_docu": "Properti berikut digunakan dalam wiki ini.", "smw_property_template": "$1 dari tipe $2 ($3 {{PLURAL:$3|digunakan|digunakan}})", "smw_propertylackspage": "<PERSON><PERSON><PERSON> properti harus dideskripsikan dengan suatu halaman!", "smw_propertylackstype": "Tidak ada tipe yang dispesifikasikan untuk properti ini (untu saat ini diasumsikan bertipe $1)", "smw_propertyhardlyused": "Properti ini hampir tak digunakan di wiki ini!", "smw-property-name-invalid": "Atribut $1 tidak bisa digunakan (nama atribut tidak sah).", "smw-sp-property-searchform": "Menampilkan atribut yang berisi:", "smw-special-property-searchform": "Menampilkan atribut yang berisi:", "smw-special-property-searchform-options": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-label": "Penyaring:", "smw-special-wantedproperties-filter-none": "Tidak ada", "smw-special-wantedproperties-filter-unapproved": "Tidak disetujui", "concepts": "<PERSON><PERSON><PERSON>", "smw-special-concept-header": "Daftar konsep", "smw-special-concept-empty": "Tidak ada konsep yang di<PERSON>n", "unusedproperties": "Properti yang tak digunakan", "smw-unusedproperties-docu": "Halaman ini berisi daftar [https://www.semantic-mediawiki.org/wiki/Unused_properties atribut tidak terpakai] yang dideklarasikan padahal tidak ada halaman lain yang menggunakannya. Untuk daftar sejenis, lihat halaman istimewa [[Special:Properties|semua atribut]] atau [[Special:WantedProperties|yang diinginkan saja]].", "smw-unusedproperty-template": "$1 bertipe $2", "wantedproperties": "Properti yang <PERSON>", "smw-wantedproperties-docu": "Halaman ini berisi daftar [https://www.semantic-mediawiki.org/wiki/Wanted_properties atribut yang diinginkan] yang dideklarasikan padahal tidak ada halaman lain yang menggunakannya. Untuk daftar sejenis, lihat halaman istimewa [[Special:Properties|semua atribut]] atau [[Special:UnusedProperties|yang tidak digunakan saja]].", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|penggunaan|penggunaan}})", "smw-special-wantedproperties-docu": "Halaman ini berisi daftar [https://www.semantic-mediawiki.org/wiki/Wanted_properties atribut yang diinginkan] yang dideklarasikan padahal tidak ada halaman lain yang menggunakannya. Untuk daftar sejenis, lihat halaman istimewa [[Special:Properties|semua atribut]] atau [[Special:UnusedProperties|yang tidak digunakan saja]].", "smw-special-wantedproperties-template": "$1 ($2 {{PLURAL:$2|penggunaan}})", "smw_purge": "Segarkan", "types": "Tipe", "smw_types_docu": "Daftar [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tipe data yang tersedia] dengan setiap [https://www.semantic-mediawiki.org/wiki/Help:Datatype tipe] merepresentasikan sehimpunan atribut unik untuk menjelaskan nilai penyimpanan dan karakteristik tampilan yang diturunkan dari atribut yang ditetapkan.", "smw_uri_doc": "Pengurai URI menerapkan [$1 W3C pencarian TAG pada httpRange-14].\nIni memastikan bahwa manusia tidak berubah menjadi situs web.", "ask": "<PERSON><PERSON><PERSON> se<PERSON>", "smw_ask_sortby": "<PERSON><PERSON><PERSON> kolo<PERSON> (pilihan)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON>", "smw_ask_descorder": "<PERSON><PERSON><PERSON>", "smw_ask_submit": "<PERSON><PERSON><PERSON> hasil", "smw_ask_editquery": "<PERSON><PERSON> kueri", "smw_add_sortcondition": "[Tambahkan kondisi pengurutan]", "smw_ask_hidequery": "Sembunyikan kueri (tampilan padat)", "smw_ask_help": "<PERSON><PERSON><PERSON> kueri", "smw_ask_queryhead": "<PERSON><PERSON><PERSON>", "smw_ask_printhead": "<PERSON><PERSON><PERSON><PERSON> untuk di<PERSON>an", "smw_ask_printdesc": "(tambahkan satu nama properti per baris)", "smw_ask_format_as": "Format sebagai:", "smw_ask_defaultformat": "baku", "smw_ask_otheroptions": "<PERSON><PERSON><PERSON> lain", "smw_ask_show_embed": "<PERSON><PERSON><PERSON><PERSON> kode sertaan", "smw_ask_hide_embed": "Sembunyikan kode sertaan", "smw_ask_embed_instr": "Untuk menyertakan query ini secara tersisip dalam suatu halaman wiki gunakan kode di bawah.", "searchbyproperty": "<PERSON><PERSON><PERSON><PERSON>", "smw_sbv_docu": "<PERSON>i semua halaman yang memiliki properti dan nilai yang diberikan.", "smw_sbv_novalue": "<PERSON><PERSON>kkan nilai yang valid untuk properti, atau lihat semua nilai properti untuk \"$1\".", "smw_sbv_displayresultfuzzy": "<PERSON><PERSON><PERSON> semua halaman yang memiliki properti \"$1\" dengan nilai \"$2\".\n<PERSON><PERSON> hanya ada sedikit hasil, nilai yang mendekati juga ditampilkan.", "smw_sbv_property": "Properti:", "smw_sbv_value": "<PERSON><PERSON>:", "smw_sbv_submit": "<PERSON><PERSON><PERSON> hasil", "browse": "Jelajahi wiki", "smw_browselink": "<PERSON><PERSON><PERSON><PERSON>", "smw_browse_article": "<PERSON><PERSON><PERSON><PERSON> nama halaman untuk memulai penel<PERSON>n", "smw_browse_go": "<PERSON><PERSON> ke", "smw_browse_show_incoming": "<PERSON><PERSON><PERSON><PERSON> properti yang bertaut ke sini", "smw_browse_hide_incoming": "Sembunyikan properti yang bertaut ke sini", "smw_browse_no_outgoing": "Halaman ini tidak memiliki properti.", "smw_browse_no_incoming": "Tidak ada properti yang bertaut ke halaman ini.", "smw_inverse_label_default": "$1 dari", "smw_inverse_label_property": "Label properti inversi", "pageproperty": "<PERSON><PERSON><PERSON> <PERSON> ha<PERSON>an", "smw_pp_docu": "Ma<PERSON>kkan satu halaman dan satu atribut, atau hanya satu atribut untuk mengambil semua nilai yang ditetapkan.", "smw_pp_from": "<PERSON><PERSON>:", "smw_pp_type": "Properti:", "smw_pp_submit": "<PERSON><PERSON><PERSON> hasil", "smw-prev": "{{PLURAL:$1|$1}} sebelumnya", "smw-next": "{{PLURAL:$1|$1}} selanjutnya", "smw_result_prev": "Sebelumnya", "smw_result_next": "Selanjutnya", "smw_result_results": "<PERSON><PERSON>", "smw_result_noresults": "Tidak ada hasilnya.", "smwadmin": "<PERSON>bor <PERSON> MediaWiki", "smw-admin-statistics-job-title": "Statistik pekerjaan", "smw-admin-setupsuccess": "<PERSON><PERSON> pen<PERSON>n telah disia<PERSON>.", "smw_smwadmin_return": "Kembali ke $1", "smw_smwadmin_updatestarted": "Proses pemutakhiran baru untuk menyegarkan data semantik dimulai.\nSemua data yang tersimpan akan dibangun kembali atau diperbaiki ketika diperlukan.\nAnda dapat mengikuti kemajuan pembaruan pada halaman khusus ini.", "smw_smwadmin_updatenotstarted": "Sudah ada suatu proses pembaruan yang sedang berjalan.\n<PERSON><PERSON> membuat proses lain.", "smw_smwadmin_updatestopped": "<PERSON><PERSON><PERSON> proses pembaruan yang ada telah di<PERSON>.", "smw_smwadmin_updatenotstopped": "Untuk menghentikan proses pembaruan yang sedang be<PERSON>, <PERSON><PERSON> harus memilih kotak centang untuk menunjukkan bahwa Anda benar-benar yakin.", "smw-admin-docu": "<PERSON><PERSON> is<PERSON>wa ini akan membantu <PERSON>a se<PERSON> instalasi, p<PERSON><PERSON><PERSON><PERSON><PERSON>, pem<PERSON><PERSON><PERSON>, dan pengg<PERSON> <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a> dan juga memberikan fungsi dan peker<PERSON>an adminstratif tambahan beserta statistik.\nJangan lupa untuk mencadangkan data Anda yang berharga sebelum melaksanakan fungsi administratif.", "smw-admin-environment": "Lingkungan perangkat lunak", "smw-admin-db": "Persiapan basis data", "smw-admin-db-preparation": "Inisialisasi tabel sedang berjalan dan mungkin perlu beberapa waktu sebelum hasilnya ditampilkan tergantung ukuran dan pengoptimalan tabel yang ada.", "smw-admin-dbdocu": "Semantic MediaWiki memerlukan struktur basis datanya sendiri (dan tidak bergantung pada MediaWiki sehingga tidak mempengaruhi bagian instalasi MediaWiki lainnya) untuk menyimpan data semantik.\nFungsi penyiapan ini bisa dieksekusi beberapa kali tanpa menyeba<PERSON>kan bahasa, tetapi hanya perlu dilakukan sekali ketika instalasi atau memutakhirkan.", "smw-admin-permissionswarn": "Jika operasi gagal karena k<PERSON>alahan SQL, pengguna basis data yang digunakan oleh wiki Anda (cek LocalSettings.php Anda) mungkin tidak mempunyai hak akses yang cukup.\nAnda dapat memberikan hak akses tambahan untuk membuat dan menghapus tabel, memasukkan sementara pengguna root basis data Anda di LocalSettings.php, atau menggunakan skrip pemeliharaan <code>setupStore.php</code> yang dapat menggunakan hak akses seorang pengurus.", "smw-admin-dbbutton": "Inisiasi atau tingkatkan tabel", "smw-admin-announce": "Umumkan wiki Anda", "smw-admin-announce-text": "Jika wiki Anda bersifat publik, <PERSON>a bisa mendaftarkannya ke <a href=\"https://wikiapiary.com\">WikiApiary</a>, wiki pelacak wiki.", "smw_smwadmin_datarefresh": "Perbaikan dan peningkatan data", "smw_smwadmin_datarefreshdocu": "Dimungkinkan untuk memulihkan semua data Semantic MediaWiki berdasarkan isi yang ada sekarang di wiki ini.\nHal ini berguna untuk memperbaiki data yang rusak atau menyegarkan data jika format internal telah berubah karena suatu peningkatan perangkat lunak.\nPembaruan dilaksanakan halaman per halaman dan tidak akan langsung selesai.\nTampilan berikut menunjukkan jika suatu pembaruan sedang berlangsung dan memungkinkan Anda untuk memulai atau menghentikan proses (kecuali jika fitur ini dimatikan oleh pengelola situs).", "smw_smwadmin_datarefreshprogress": "<strong>Suatu pembaruan sedang dalam proses.</strong>\n<PERSON><PERSON> wajar jika kemajuan pembaruan berjalan lambat karena ia hanya menyegarkan data sedikit demi sedikit setiap kali seorang pengguna mengakses wiki ini.\nUntuk menyelesaikan pembaruan ini dengan lebih cepat, <PERSON><PERSON> <PERSON>pat menjalankan skrip pemeliharaan MediaWiki <code>runJobs.php</code> (gunakan opsi <code>--maxjobs 1000</code> untuk membatasi jumlah pembaruan yang dilakukan dalam satu saat).\nPerkiraan kemajuan pembaruan saat ini:", "smw_smwadmin_datarefreshbutton": "Jadwalkan pembangunan ulang data", "smw_smwadmin_datarefreshstop": "Hentikan pembaruan ini", "smw_smwadmin_datarefreshstopconfirm": "Ya, saya {{GENDER:$1|yakin}}", "smw-admin-support": "<PERSON>i dukungan", "smw-admin-supportdocu": "Berbagai sumber disediakan untuk membantu Anda jika ada masalah:", "smw-admin-installfile": "<PERSON><PERSON>a menghadapi masalah dengan instalasi Anda, mula<PERSON>h dengan mengecek pedoman di <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">berkas INSTALL</a> dan <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">halaman instalasi</a>.", "smw-admin-smwhomepage": "Dokumentasi pengguna lengkap untuk Semantic MediaWiki tersedia di <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Bug dapat dilaporkan ke <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">pelacak isu</a>, halaman <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">melaporkan bug</a> menyediakan beberapa bantuan tentang cara menulis laporan isu yang efektif.", "smw-admin-questions": "<PERSON><PERSON> Anda memiliki pertanyaan atau saran lain, be<PERSON><PERSON><PERSON><PERSON> dengan diskusi di <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">milis pengg<PERSON></a> Semantic MediaWiki.", "smw-admin-other-functions": "<PERSON><PERSON><PERSON> lain", "smw-admin-statistics-extra": "Fungsi statistik", "smw-admin-statistics": "Statistik", "smw-admin-supplementary-section-title": "<PERSON><PERSON><PERSON> p<PERSON>", "smw_adminlinks_datastructure": "Struktur data", "smw_adminlinks_displayingdata": "Menampilkan data", "smw_adminlinks_inlinequerieshelp": "<PERSON><PERSON><PERSON> kueri si<PERSON>p", "smw-createproperty-isproperty": "Ini adalah properti bertipe $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|<PERSON><PERSON>|<PERSON><PERSON>}} yang di<PERSON>inkan untuk properti ini adalah:", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ MediaWiki Semantik] dan ekstensi-ekstensi terkait menyediakan preferensi untuk sekelompok fitur dan fungsi terpilih. Daftar pengaturan individu beserta deskripsi dan karakteristiknya tersedia di [http://semantic-mediawiki.org/wiki/Help:User_preferences halaman bantuan] berikut.", "smw-prefs-general-options-show-entity-issue-panel": "Tampilkan panel ma<PERSON><PERSON> entitas", "smw-prefs-help-general-options-show-entity-issue-panel": "<PERSON><PERSON>, jalankan pemeriksaan integritas pada setiap halaman dan tampilkan [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel Entity issue panel].", "smw-ui-tooltip-title-quantity": "satuan konversi", "smw-ui-tooltip-title-info": "Informasi", "smw-ui-tooltip-title-warning": "Peringatan", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-note": "Catatan", "smw_unknowntype": "Tipe \"$1\" yang ditetapkan untuk properti ini tidak sah.", "smw_concept_header": "Halaman berkonsep \"$1\"", "smw_conceptarticlecount": "Menampilkan $1 {{PLURAL:$1|halaman}} berikut.", "smw-livepreview-loading": "Mengunggah...", "smw-datavalue-number-nullnotallowed": "\"$1\" kembali dengan sebuah \"NULL\" yang tidak mengizinkan sebagai nomor.", "smw-processing": "Memproses...", "smw-loading": "Memuat...", "smw-preparing": "Menyiapkan...", "smw-facetedsearch-profile-label-default": "<PERSON><PERSON> b<PERSON>", "smw-facetedsearch-no-filters": "Tanpa filter.", "smw-search-placeholder": "Cari...", "smw-listingcontinuesabbrev": "samb.", "smw-showingresults": "Di bawah ini ditampilkan hingga {{PLURAL:$1|<strong>1</strong> hasil|<strong>$1</strong> hasil}}, dimulai dari #<strong>$2</strong>."}