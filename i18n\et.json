{"@metadata": {"authors": ["Avjoska", "Pikne", "<PERSON>"]}, "smw_viewasrdf": "RDF-voog", "smw_finallistconjunct": " ja", "smw_isspecprop": "See omadus on selles vikis eriomadus.", "smw_concept_description": "Mõiste \"$1\" kirjeldus", "version-semantic": "Semantilised lisad", "smw_printername_list": "Lo<PERSON>", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_template": "Mall", "smw_printername_category": "Kategooria", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-link": "Näita väärtuseid linkidena", "smw-paramdesc-embedonly": "<PERSON><PERSON>ita ilma peal<PERSON>a", "smw-paramdesc-feedtype": "Vootüüp", "smw-label-feed-description": "$2-voog: $1", "smw_iq_disabled": "Semantilised p<PERSON><PERSON><PERSON> on siin vikis keel<PERSON>.", "smw_iq_moreresults": "... roh<PERSON>m tule<PERSON>i", "smw_parseerror": "Sisestatud väärtusest ei saadud aru.", "smw_notitle": "Väärtust \"$1\" ei saa siin vikis lehek<PERSON><PERSON><PERSON> peal<PERSON><PERSON><PERSON> ka<PERSON>a.", "smw_emptystring": "<PERSON><PERSON><PERSON><PERSON>d sõned ei sobi.", "smw_true_words": "t<PERSON><PERSON>,t,jah,j", "smw_false_words": "vä<PERSON>r,v,ei,e", "smw_nofloat": "\"$1\" pole number.", "smw-categories": "Kategooriad", "smw_purge": "Värskenda", "smw_types_docu": "[https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes Saada olevate andmetüüpid<PERSON>] loend, kus igale [https://www.semantic-mediawiki.org/wiki/Help:Datatype tüübile] vastab kordumatu komplekt atribuute. Omistatud omadus pärib mõne komplekti, mis kirjeldab väärtuse talletus- ja kuvakarakteristikuid.", "smw-special-types-no-such-type": "\"$1\" on tundmatu või seda pole määratud kehtivaks andmetüübiks.", "smw-statistics": "Semantilised arvandmed", "smw-statistics-property-instance": "Omaduse {{PLURAL:$1|väärtus|väärtusi}} (kokku)", "smw-ask-delete": "<PERSON><PERSON><PERSON>", "smw_sbv_submit": "<PERSON><PERSON><PERSON> tule<PERSON>i", "browse": "<PERSON><PERSON>", "smw_browse_go": "Mine", "smw_pp_type": "Omadus:", "smw_result_prev": "Eelmised", "smw_result_next": "Järgmised", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, olen {{GENDER:$1|kindel}}.", "smw-createproperty-isproperty": "<PERSON><PERSON> o<PERSON>use tüüp on $1.", "smw-ui-tooltip-title-property": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-warning": "<PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-event": "Sünd<PERSON>", "smw_unknowntype": "<PERSON><PERSON> omaduse tüüp \"$1\" on vigane.", "smw-property-predefined-default": "\"$1\" on eelmääratletud omadus tüübiga $2.", "smw-livepreview-loading": "Laadimine...", "smw-listingcontinuesabbrev": "jätk", "smw-showingresults": "Allpool näidatakse '''{{PLURAL:$1|ühte|$1}}''' tulemust alates '''$2'''. tulemusest."}