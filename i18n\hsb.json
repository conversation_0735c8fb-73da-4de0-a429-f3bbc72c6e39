{"@metadata": {"authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "아라"]}, "smw-desc": "Twój wiki přistupniši činić - za mašin<PERSON> ''a'' lud<PERSON>i ([https://www.semantic-mediawiki.org/wiki/Help:User_manual dokumentacija online])", "smw-error": "Zmylk", "smw-upgrade-release": "Wozjewjenje", "smw-upgrade-progress": "Postup aktualizacije", "smw-upgrade-error-why-title": "Čehodla widźu tutu zdźělenku?", "smw-upgrade-error-how-title": "<PERSON><PERSON> so tutón problem roz<PERSON><PERSON>ć?", "smw-extensionload-error-why-title": "<PERSON>ehodla so tuta strona pokazuje?", "smw-extensionload-error-how-title": "<PERSON><PERSON> so tutón problem roz<PERSON><PERSON>ć?", "smw-upgrade-maintenance-why-title": "<PERSON>eh<PERSON><PERSON> widźu tutu stronu?", "smw_viewasrdf": "RDF-kanal", "smw_finallistconjunct": "a", "smw-factbox-head": "… wjace datow wo stronje „$1“", "smw-factbox-facts": "Fak<PERSON>", "smw-factbox-facts-help": "Pokazuje wuprajenja a fakty, kiž su so wot wužiwarjow wutworili", "smw-factbox-attachments": "Přiwěš<PERSON>", "smw-factbox-attachments-value-unknown": "njeznaty", "smw-factbox-attachments-is-local": "Lokalny", "smw-factbox-facts-derived": "Wotwodźene fakty", "smw_isspecprop": "<PERSON><PERSON> ka<PERSON> je specialna kajk<PERSON> w tutym wikiju.", "smw_concept_description": "Wopisanje koncepta \"$1\"", "smw_no_concept_namespace": "Koncepty hodźa so jeno<PERSON> w mjenowym rumje ''Koncept:'' defino<PERSON><PERSON>.", "smw_multiple_concepts": "Kóžda konceptowa strona móže jenož jednu konceptowu definiciju měć.", "smw_concept_cache_miss": "Koncept \"$1\" njeda so tuchwilu w<PERSON><PERSON><PERSON><PERSON><PERSON>, do<PERSON>ž wikijowa konfiguracija jón za předźěłowanje offline trjeba.\n<PERSON><PERSON> so problem po wěstym času n<PERSON>, w<PERSON><PERSON><PERSON><PERSON> so sydłoweho administratora, zo by wón tutón koncept k dispoziciji stajił.", "smw_noinvannot": "Hódnoty njedadźa so nawopačnym kajkosćam připokazać.", "version-semantic": "Semantiske rozšěrjenja", "smw_baduri": "URI formy \"$1\" njej<PERSON> dowolene.", "smw_printername_count": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> li<PERSON>", "smw_printername_csv": "Eksport CSV", "smw_printername_dsv": "DSV-eksport", "smw_printername_debug": "Naprašowanje za zmylkami přepytać (za ekspertow)", "smw_printername_embedded": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "smw_printername_json": "Eksport JSON", "smw_printername_list": "Lisćina", "smw_printername_ol": "Naličen<PERSON>", "smw_printername_ul": "Nalistowanje", "smw_printername_table": "<PERSON><PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON><PERSON><PERSON> tabela", "smw_printername_template": "Př<PERSON>ł<PERSON><PERSON>", "smw_printername_rdf": "RDF-eksport", "smw_printername_category": "Kategorija", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-limit": "Maksimalna ličba wuslědkow, kotraž ma so wr<PERSON><PERSON><PERSON>", "smw-paramdesc-offset": "Pozicija prěnjeho wuslědka", "smw-paramdesc-headers": "Mjena hłowow abo atributow zwobraznić", "smw-paramdesc-mainlabel": "Pomjenowanje, kot<PERSON>ž ma so hłownej stronje dać", "smw-paramdesc-link": "Hódnoty jako wotka<PERSON> p<PERSON>", "smw-paramdesc-intro": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> ma so před naprašowanskimi wuslě<PERSON><PERSON><PERSON>, jeli tajke su", "smw-paramdesc-outro": "<PERSON><PERSON><PERSON>, kot<PERSON><PERSON> ma so za naprašowanskimi wuslědka<PERSON> zwo<PERSON>z<PERSON>ć, jeli tajke su", "smw-paramdesc-default": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> ma <PERSON><PERSON>, jeli žane naprašowanske wuslědki njejsu", "smw-paramdesc-sep": "Dźělatko za hódnoty", "smw-paramdesc-showsep": "Dźělatko horjeka w CSV-dataji pokazać (\"sep=<value>\")", "smw-paramdesc-distribution": "<PERSON><PERSON><PERSON> toho zo so wš<PERSON> hódnoty zwobraznjeja, jich wustupowanja ličić a te pokazać.", "smw-paramdesc-distributionsort": "Rozdźělenje hódnotow po ličbje wustupowanjow sortěrować.", "smw-paramdesc-distributionlimit": "Rozdźělenje hódnotow na wěste hódnoty wobmjezować.", "smw-paramdesc-template": "<PERSON><PERSON><PERSON>, z kotrejž maja so wućišće zwobraznić", "smw-paramdesc-columns": "Ličba špaltow, w kotrych<PERSON> maja so wuslěd<PERSON> zwobraznić (standard je $1)", "smw-paramdesc-userparam": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> so za kóždu namołwu předłohi přepodawa, jeli předłoha so wužiwa.", "smw-paramdesc-introtemplate": "<PERSON><PERSON><PERSON>, kot<PERSON><PERSON> ma so před naprašowanskimi wuslěd<PERSON><PERSON>, jeli tajke su", "smw-paramdesc-outrotemplate": "<PERSON><PERSON><PERSON>, kot<PERSON><PERSON> ma so za naprašowanskimi wuslěd<PERSON><PERSON> z<PERSON>ć, jeli tajke su", "smw-paramdesc-embedformat": "HTML-<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> so wu<PERSON><PERSON><PERSON><PERSON>, zo bychu nadpisma definowali", "smw-paramdesc-embedonly": "Žane nadpisma zwobraznić", "smw-paramdesc-table-class": "Přidatna CSS-klasa za tabelu", "smw-paramdesc-rdfsyntax": "RDF-syntaksa, kotraž ma so wužiwać", "smw-paramdesc-csv-sep": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kot<PERSON>ž ma so wužywać", "smw-paramdesc-dsv-separator": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kot<PERSON>ž ma so wužywać", "smw-paramdesc-dsv-filename": "Mjeno za DSV-dataju", "smw-paramdesc-filename": "Mjeno za wudawansku dataju", "smw-smwdoc-description": "Pokazuje tabelu w<PERSON><PERSON><PERSON> parametrow, kot<PERSON><PERSON> dadźa so za podaty wuslědkowy format hromadźe ze standardnymi hódnotami a wopisanjemi wužiwać.", "smw-smwdoc-par-format": "Wuslědkowy format, za kotrehož parametry dokumentacija ma so zwobraznić.", "smw-smwdoc-par-parameters": "Parametry, kotrež maja so pokazać: \"specific\" za tute, kotre<PERSON> so přez format přidawaja, \"base\" za tute, kotrež su we wšěch formatach k dispoziciji a \"all\" za wobaj.", "smw-paramdesc-sort": "<PERSON><PERSON><PERSON><PERSON>, po kotre<PERSON><PERSON> naprašowanje ma so sortěrowa<PERSON>", "smw-paramdesc-order": "Sortěrowanski porjad za naprašowanje", "smw-paramdesc-searchlabel": "Tekst za dalše pytanske wuslědki", "smw-paramdesc-named_args": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kot<PERSON>ž maja so předłoze přepodać", "smw-paramdesc-export": "Eksportowe nastajenje", "smw-paramdesc-prettyprint": "Njeformatowane wudaće, kotrež přidatne zasunjenja a nowe linki zwobraznja", "smw-paramdesc-source": "Alternatiwne naprašowanske žórło", "smw-paramdesc-jsonsyntax": "JSON-syntaksa, kot<PERSON>ž ma so wužiwać", "smw-printername-feed": "RSS- a Atom-kanal", "smw-paramdesc-feedtype": "Kanalowy typ", "smw-paramdesc-feedtitle": "<PERSON><PERSON><PERSON>, kot<PERSON><PERSON> ma so jako titul kanala wužiwać", "smw-paramdesc-feeddescription": "<PERSON><PERSON><PERSON>, kot<PERSON><PERSON> ma so jako wopisanje kanala wužiwać", "smw-paramdesc-feedpagecontent": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>, k<PERSON><PERSON><PERSON> ma so z komentarom zwobraznić", "smw-label-feed-description": "$2-kanal: $1", "smw_iq_disabled": "Semantiske naprašowanja su w tutym wikiju znjemóžnjene.", "smw_iq_moreresults": "... da<PERSON><PERSON><PERSON> w<PERSON>", "smw_parseerror": "Podata hódnota njebu zrozumjena.", "smw_notitle": "\"$1\" njeda so jako mjeno strony w tutym wikiju wuž<PERSON>.", "smw_noproperty": "\"$1\" njeda so jako <PERSON> w tutym wikiju wuž<PERSON><PERSON>.", "smw_wrong_namespace": "<PERSON><PERSON><PERSON> strony w mjenowym rumje \"$1\" su tu dowolene.", "smw_manytypes": "<PERSON><PERSON> bu wjace hač jeden typ definowany.", "smw_emptystring": "Prózdne znamješkowe slědy so njeakceptuja.", "smw_notinenum": "\"$1\" w lisć<PERSON>je móžnych hódnotow ($2) za tutu kajkosć njeje.", "smw_noboolean": "\"$1\" płaćiwa hódnota typa boolean (wěrny/njewěrny) njeje.", "smw_true_words": "wě<PERSON><PERSON>, haj, true", "smw_false_words": "wop<PERSON>, nje<PERSON>ě<PERSON>y, n<PERSON>, false", "smw_nofloat": "\"$1\" ličba njeje.", "smw_infinite": "<PERSON><PERSON><PERSON>, kot<PERSON><PERSON> su tak wulke kaž \"$1\", so njepodpěruja.", "smw_unitnotallowed": "\"$1\" njeje jako płaćiwa měrjenska jednotka za tutón atribut postajił.", "smw_nounitsdeclared": "<PERSON><PERSON> měrjenske jednotki njejsu so za tutu kajk<PERSON> podali.", "smw_novalues": "<PERSON><PERSON> h<PERSON> podate.", "smw_nodatetime": "Datum \"$1\" njebu zrozumjeny.", "smw_toomanyclosing": "<PERSON><PERSON> so, zo \"$1\" w tutym naprašowanju přehusto wustupuje.", "smw_noclosingbrackets": "Wustupowanje pora róžkatych spinkow \"<nowiki>[[</nowiki>\" w twojim naprašowanju njeje přez wotpowědny \"]]\" wuk<PERSON><PERSON><PERSON>.", "smw_misplacedsymbol": "Symbol \"$1\" so na městnje wužiwa, hd<PERSON><PERSON>ž wužitny njeje.", "smw_unexpectedpart": "<PERSON><PERSON><PERSON><PERSON> \"$1\" naprašowanja njebu zrozumjeny.\nWuslědki snano kaž wočakowane njejsu.", "smw_emptysubquery": "Někajke podnaprašowanje nima płaćiwe wuměnjenje.", "smw_misplacedsubquery": "Někajke podnaprašowanje so na městnje wužiwa, hd<PERSON><PERSON>ž podnaprašowanja njejsu dowolene.", "smw_valuesubquery": "Podnaprašowanja so za hódnoty kajkosće \"$1\" njepodpěruja.", "smw_badqueryatom": "<PERSON><PERSON><PERSON><PERSON> \"<nowiki>[[…]]</nowiki>\" naprašowanja njebu zrozumjeny.", "smw_propvalueproblem": "Hódnota kajkosće \"$1\" njebu zrozumjena.", "smw_noqueryfeature": "Wotprašowanska funkcija njepodpěruje so w tutym wikiju a dźě<PERSON> wotprašowanja je so wotstronił ($1).", "smw_noconjunctions": "A-zwjazanja we wotprašowanajch njepodpěruja so w tutym wikiju a dźěl wotprašowanja je so wotstronił ($1).", "smw_nodisjunctions": "ABO-zwjazanja we wotprašowanjach njepodpěruja so w tutym wikiju a dźěl wotprašowanja je so wotstronił ($1).", "smw_querytoolarge": "Slědowace naprašowanske wuměnjenja njedachu so dla wikijowych wobmjezowanjow za wulkosć abo hłubokosć naprašowanja wobkedźbować: $1.", "smw_notemplategiven": "Podaj hódnotu za parameter \"template\", zo by tutón naprašowanski format fungował.", "smw_db_sparqlqueryproblem": "Naprašowanski wuslědk njeda so z datoweje banki SPARQL wotwołać. Tutón zmylk móhł nachwilny być abo programowy zmylk w datowej bance być.", "smw_db_sparqlqueryincomplete": "Wotmołwjenje na naprašowanje je so jako přećežko wukopało a je so přetorhnyło. Někotre wuslědki móhli falować. <PERSON><PERSON> je mó<PERSON>, spytaj jednoriše naprašowanje město toho wužiwać.", "smw_type_header": "<PERSON><PERSON><PERSON><PERSON><PERSON> typa \"$1\"", "smw_typearticlecount": "{{PLURAL:$1|Pokazuje so kajk<PERSON><PERSON>, kot<PERSON><PERSON> tutón typ wužiwa|Pokazujetej so $1 kajkos<PERSON><PERSON>, kotrej<PERSON> tutón typ wužiwatej|Pokazuja so $1 kajkosće, kotre<PERSON> tutón typ wužiwaja|Pokazuje so $1 kajkosćow, kotrež tutón typ wužiwa}}.", "smw_attribute_header": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> \"$1\" wužiwaja", "smw_attributearticlecount": "{{PLURAL:$1|Pokazuje so $1 strona, kotra<PERSON> tutu kajkosć wužiwa|Pokazujetej $1 stronje, kotrejž tutu kajkosć wužiwatej|Pokazuja so $1 strony, kotrež tutu kajksć wužiwaja|Pokazuje so $1 stronow, kotrež tutu kajkosć wužiwa}}.", "exportrdf": "Do RDF eksportować", "smw_exportrdf_docu": "Tuta strona ći zmóžnja daty ze strony we formaće RDF wotwołać. Zo by stron<PERSON>, zapodaj titule w slědowacym kašćiku, jedyn titul na linku.", "smw_exportrdf_recursive": "Eksportuj wšě piwuzne strony.\n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zo wuslědk móhł wulki być!", "smw_exportrdf_backlinks": "Eksportuj tež wš<PERSON> strony, kot<PERSON>ž so na eksportowane strony poćahuja.\nPłodźi přepytujomny RDF.", "smw_exportrdf_lastdate": "Njeeksport<PERSON><PERSON> s<PERSON>, k<PERSON><PERSON><PERSON> nje<PERSON> so wot podateho časoweho dypka změnili.", "smw_exportrdf_submit": "Eksportować", "uriresolver": "Rezolwer URI", "properties": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-categories": "Kategorije", "smw_properties_docu": "Slědo<PERSON><PERSON> kajk<PERSON>e so we wikiju wužiwaja.", "smw_property_template": "$1 typa $2 ($3 {{PLURAL:$3|wu<PERSON><PERSON><PERSON>|wu<PERSON>ići|wužića|wu<PERSON><PERSON>ow}})", "smw_propertylackspage": "<PERSON><PERSON><PERSON> ka<PERSON> měli so přez stronu wop<PERSON>!", "smw_propertylackstype": "Za tutu kajkosć njeje so žadyn typ podał (mjeztym so typ $1 předpokładuje).", "smw_propertyhardlyused": "<PERSON><PERSON> so we wikiju lědma wužiwa!", "smw-property-name-invalid": "Kajkosć $1 njeda so wu<PERSON><PERSON><PERSON><PERSON> (njepłaćiwe kajkostne mjeno)", "smw-sp-property-searchform": "<PERSON><PERSON><PERSON><PERSON><PERSON> zwobraznić, kotrež wobsahuja:", "smw-sp-property-searchform-inputinfo": "Wudaće dźiwa na wulkopisanje. Za filtrowanje so j<PERSON><PERSON> ka<PERSON> zwobraznjeja, kot<PERSON>ž wuměnjenju wotpowěduja.", "concepts": "Koncepty", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Concepts Koncept] hod<PERSON><PERSON> so jako \"dynamiska kategorija\" wob<PERSON><PERSON><PERSON>, t.r. jako z<PERSON><PERSON><PERSON> stronow, kot<PERSON><PERSON> so manuelnje n<PERSON>, ale so přez Semantic mediaWiki z wopisanja dateho naprašowanja wobličuja.", "smw-special-concept-header": "Lisćina konceptow", "smw-special-concept-count": "{{PLURAL:$1|Slědowacy koncept|Slědowacej $1 konceptaj|Slědowace $1 koncepty|Slědowacych $1 konceptow}} {{PLURAL:$1|eksistuje|eksistu<PERSON><PERSON>|eksistuja|eksistuje}}.", "smw-special-concept-empty": "N<PERSON>je so žadyn koncept namakał.", "unusedproperties": "Njewužiwan<PERSON> ka<PERSON>", "smw-unusedproperties-docu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kajk<PERSON> e<PERSON>, ha<PERSON><PERSON><PERSON><PERSON> druha strona je wužiwa.", "smw-unusedproperty-template": "$1 typa $2", "wantedproperties": "<PERSON><PERSON><PERSON><PERSON>", "smw-wantedproperties-docu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kajk<PERSON> so we wikiju wužiwaja, ale nimaja stron<PERSON>, kot<PERSON><PERSON> je wopisuje.", "smw-wantedproperty-template": "$1 ({{PLURAL:$2|j<PERSON><PERSON> wu<PERSON>|dw<PERSON>jce wužitej|$2 razy wužite|$2 razow wužite}})", "smw_purge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "types": "<PERSON><PERSON>", "smw_types_docu": "Deleka je lisćina w<PERSON><PERSON><PERSON> datowych typow, kot<PERSON><PERSON> so kajkosćam př<PERSON>.", "smw-special-types-no-such-type": "<PERSON><PERSON><PERSON> da<PERSON>wy typ njeeksistu<PERSON>", "smw-statistics": "Semantiska statistika", "smw-statistics-property-instance": "{{PLURAL:$1|Kajkostna hódnota|Kajkostnej hódnoće|Kajkostne hódnoty}} (dohromady)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>}}]] (dohromady)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>}} (dohromady)", "smw-statistics-property-page": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>}} (ze stronu zregistrowane)", "smw-statistics-property-type": "{{PLURAL:$1|<PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON><PERSON>}} (datowemu typej připokazane)", "smw-statistics-query-inline": "{{PLURAL:$1|Naprašowanje|Naprašowani|Naprašowanja}}", "smw-statistics-query-size": "<PERSON><PERSON><PERSON><PERSON>", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Koncept|Konceptaj|Koncepty}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Koncept|Konceptaj|Koncepty}}]]", "smw-statistics-subobject-count": "{{PLURAL:$1|Podobjekt|Podobjektaj|Podobjekty}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|<PERSON><PERSON><PERSON> typ|<PERSON>towej typaj|<PERSON><PERSON><PERSON> typy}}]]", "smw_uri_doc": "Rozpušćak URI implementuje [$1 W3C TAG finding on httpRange-14].\nStara so wo to, zo so ludźo z websydłami njestanu.", "ask": "Semantiske pytanje", "smw_ask_sortby": "Po špalće <PERSON> (opcionalny)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_descorder": "Spadowacy", "smw_ask_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_editquery": "Naprašowan<PERSON> wobdźě<PERSON>", "smw_add_sortcondition": "[Sortěrowanske wuměnje<PERSON><PERSON>]", "smw_ask_hidequery": "Na<PERSON>š<PERSON><PERSON><PERSON> s<PERSON>", "smw_ask_help": "Pomoc za naprašowanja", "smw_ask_queryhead": "Wuměnjenje", "smw_ask_printhead": "Př<PERSON><PERSON><PERSON> daty, kot<PERSON>ž maja so zwobraznić", "smw_ask_printdesc": "(přidaj jedne atributowe mjeno na linku)", "smw_ask_format_as": "Formatowany jako:", "smw_ask_defaultformat": "standard", "smw_ask_otheroptions": "<PERSON><PERSON><PERSON> op<PERSON>", "smw-ask-otheroptions-info": "Tutón wotrězk wob<PERSON>huje opcije, kot<PERSON><PERSON> měnjeja ćišćerske wudaće. Parametrowe wopisanje dadźa so wob<PERSON><PERSON><PERSON>, jeli so kursor myš<PERSON> nad nich znošuje.", "smw-ask-otheroptions-collapsed-info": "<PERSON><PERSON><PERSON> wu<PERSON><PERSON><PERSON> symbol (+), zo by sej wš<PERSON> k dispoziciji stejace opcije wobhladał", "smw_ask_show_embed": "Zasadźeny kod pokazać", "smw_ask_hide_embed": "Zasadźeny kod schować", "smw_ask_embed_instr": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> kod, zo by tute wotprašowanje do wikijoweje strony zasadźił.", "smw-ask-delete": "<PERSON><PERSON><PERSON><PERSON>", "smw-ask-sorting": "Sortěrowanje", "smw-ask-format-selection-help": "Pomoc wo wubranym form<PERSON>: $1", "searchbyproperty": "Po atribuće pytać", "smw_sbv_docu": "<PERSON><PERSON><PERSON> s<PERSON>, kot<PERSON><PERSON> maja wěstu kajk<PERSON> a hódnotu.", "smw_sbv_novalue": "Zapodaj płaćiwu hódnotu za kajkosć abo wobhladaj sej wšě hódnoty kajkosće za \"$1\".", "smw_sbv_displayresultfuzzy": "Lisć<PERSON> w<PERSON><PERSON><PERSON> stronow, kot<PERSON><PERSON> maja kajk<PERSON> \"$1\" z hódnotu \"$2\".\n<PERSON><PERSON><PERSON> je j<PERSON><PERSON> mało wuslědkow, so tež podobne hódnoty nalistuja.", "smw_sbv_property": "<PERSON><PERSON><PERSON><PERSON>:", "smw_sbv_value": "Hódnota:", "smw_sbv_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "browse": "Wiki přepytać", "smw_browselink": "<PERSON><PERSON><PERSON><PERSON><PERSON>ř<PERSON>", "smw_browse_article": "Zapodaj m<PERSON> s<PERSON>, wot kotre<PERSON>ž ma so pytanje započeć.", "smw_browse_go": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_browse_show_incoming": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>, kot<PERSON><PERSON> sem wotkazuja", "smw_browse_hide_incoming": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kot<PERSON><PERSON> sem wotkazuja", "smw_browse_no_outgoing": "<PERSON><PERSON> strona ni<PERSON> ka<PERSON>.", "smw_browse_no_incoming": "<PERSON><PERSON> ka<PERSON> k tutej stronje njewotkazuja.", "smw_inverse_label_default": "$1 z", "smw_inverse_label_property": "Pomjenowanje nawopačneje kajk<PERSON>će", "pageproperty": "<PERSON>yt<PERSON><PERSON> strony", "smw_pp_docu": "Pytaj wšě hódnoty kajkosće na datej stronje.\nZapodaj stronu kaž tež kajkosć.", "smw_pp_from": "<PERSON>e strony", "smw_pp_type": "<PERSON><PERSON><PERSON><PERSON>:", "smw_pp_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_result_prev": "Předchadny", "smw_result_next": "Přichodny", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "<PERSON><PERSON>.", "smwadmin": "Administraciske funkcije za Semantic MediaWiki", "smw-admin-setupsuccess": "Składowanska jednotka je so wuspěšnje nastajiła.", "smw_smwadmin_return": "Wróćo k $1", "smw_smwadmin_updatestarted": "Nowy proces aktualizacije za wobnowjenje semantiskich datow bu startowany.\n<PERSON><PERSON><PERSON> skła<PERSON>e daty budu so znowa tworić abo porjed<PERSON>, hd<PERSON><PERSON><PERSON> je to trěbne.\n<PERSON><PERSON><PERSON><PERSON>š proces aktualizacije na tutej specialnej stronje slědować.\n\nWróćo k $1.", "smw_smwadmin_updatenotstarted": "Proces aktualizacije hižo běži.\nNowy so njezapočnje.\n\nWróćo k $1.", "smw_smwadmin_updatestopped": "<PERSON>šě eksistowace aktualizaciske procesy su so zastajili.\n\nWróćo k $1.", "smw_smwadmin_updatenotstopped": "Zo by běžny aktualizowanski proces zastajił, d<PERSON><PERSON>š kontrolny kašćik aktiwizować, zo by poda<PERSON>, zo sy sej woprawdźe wěsty.\n\nWróćo k $1.", "smw-admin-docu": "Tuta specialna strona ći za instalaciju a akutalizaciju <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a> pomha.\nNjezabudź hódnotne da<PERSON>, prje<PERSON> ha<PERSON> administratiwne funkcije wuwjedźeš.", "smw-admin-db": "Instalacija a aktualizacija datoweje banki", "smw-admin-dbdocu": "Semantic MediaWiki trjeba někotre rozšěrjenja na datowej bance MediaWiki, zo by semantiske daty składował.\nSlědowaca funkcija zawěsćuje, zo twoja datowa banka je prawje zarjadowana.\n<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> so w tutym kroku činja, njewobwliwuja zbytk datowje banki MediaWiki a dadźa so lochko cof<PERSON>, jeli po<PERSON>.\nTuta zarjadowanska funkcija da so wjacore razy wuw<PERSON>, b<PERSON><PERSON><PERSON><PERSON> zo so škoda zawinuje, je wšak j<PERSON><PERSON> jedyn raz při instalaciji abo aktualizaciji trěbna.", "smw-admin-permissionswarn": "Jeli operacija so ze zmylkami SQL njeradźi, najskerje wužiwar datoweje banki, kotrehož twój wiki wužiwa (hlej twoju dataju LocalSettings.php), dosahace prawa nima.\nZaruč tute přidatne wužiwarske prawa za wutworjenje a wušmórnjenje tabelow, zapodaj nachwilu přizjewjenje twojeho administratora datoweje banki do dataje LocalSettings.php abo wužij wothladowanski skript <code>setupStore.php</code>, kotryž móže wužiwarske daty administratora wužiwać.", "smw-admin-dbbutton": "Tabele inicializować abo aktualizować", "smw-admin-announce": "Twój wiki připowědźić", "smw_smwadmin_datarefresh": "Porjedźenje a aktualizacija datow", "smw_smwadmin_datarefreshdocu": "Je móžno wšě daty ze Semantic MediaWiki na zakładźe aktualneho wobsaha wikija wobnowić. To móže wužitne być, zo bychu so wobškodźene daty porjedźili abo daty aktualizowali, jeli interny format je so aktualizacije softwary dla změnił.\nAktualizacija so stronu po stronu přewjedźe a njebudźe so hnydom kónčić.\nNaslědne pokazuje, jeli aktualizacija so wotměwa a ći dowoluje aktualizacije startować abo zastajić (chibazo tuta funkcija bu wot administratora sydła znjemóžnjena).", "smw_smwadmin_datarefreshprogress": "<strong>Aktualizacija so hižo wotm<PERSON>wa.</strong>\nJe normaln<PERSON>, zo so aktualizacija jeno<PERSON> pomału wotm<PERSON>, do<PERSON><PERSON> daty jenož w małych porcijach kóždy raz, <PERSON><PERSON><PERSON> wuž<PERSON>war ma přistup na wiki, aktualizuje.\nZo by tutu aktualizaciju spěš<PERSON>, m<PERSON><PERSON><PERSON>š wothladowanski skript MediaWiki <code>runJobs.php</code> zaw<PERSON><PERSON><PERSON> (wužij opciju <code>--maxjobs 1000</code>, zo by li<PERSON><PERSON> aktualizacijow, kot<PERSON><PERSON> so z jednym wotmachom přewjedu, wobmjezował).\nTrochowany staw aktualneje aktualizacije:", "smw_smwadmin_datarefreshbutton": "Aktualizaciju datow započeć", "smw_smwadmin_datarefreshstop": "<PERSON>tu aktualiza<PERSON><PERSON>", "smw_smwadmin_datarefreshstopconfirm": "<PERSON>j, sym {{GENDER:$1|wěsty|wěsta}}.", "smw-admin-support": "Podp<PERSON><PERSON>", "smw-admin-supportdocu": "Wšelake resursy móhli ći w problemowych padach pomhać:", "smw-admin-installfile": "<PERSON><PERSON> su <PERSON>y z twojej instalaciju, wobhl<PERSON>j sej směrnicy w <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">dataji INSTALL</a>.", "smw-admin-smwhomepage": "Kompletna wužiwarska dokumentacija za Semantic MediaWiki je na <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Zmylki móžeš w systemje <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "smw-admin-questions": "<PERSON><PERSON> <PERSON> p<PERSON> abo namjety, wob<PERSON><PERSON><PERSON><PERSON> so na diskusiji na <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">wužiwarskim forumje Semantic MediaWiki</a>.", "smw-admin-supplementary-elastic-replication-files": "<PERSON>je", "smw-admin-supplementary-elastic-replication-pages": "Strony", "smw_adminlinks_datastructure": "Struktura datow", "smw_adminlinks_displayingdata": "Zwobraznje<PERSON><PERSON> da<PERSON>w", "smw_adminlinks_inlinequerieshelp": "Pomoc za rjadowe wotprašowanja", "smw-createproperty-isproperty": "To je ka<PERSON><PERSON><PERSON> typa $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Dowolena hódnota za tutu kajkosć je|Dowolenej hódnoće za tutu kajkosć stej|Dowolene hódnoty za tutu kajkosć su|Dowolene hódnoty za tutu kajkosć su}}:", "smw-paramdesc-category-delim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-paramdesc-category-template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, z kotrejž zapiski maja so formatować", "smw-paramdesc-category-userparam": "Parmeter, kot<PERSON><PERSON> ma so předłoze přepodać", "smw-info-par-message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kot<PERSON>ž ma so zwobraznić.", "smw-info-par-icon": "Symbol, kotryž ma so pokazać, pak \"info\" pak \"warnowanje\".", "prefs-smw": "Semantic MediaWiki", "prefs-ask-options": "Semantiske pytanje", "smw-prefs-intro-text": "Slědowace nastajenja so wot [https://www.semantic-mediawiki.org/ Semantic MediaWiki]  (abo podobnych rozšěrjenjow) k dispoziciji steja, zo bychu indiwiduelne přiměrjenje za wubrane funkcije zmóžnili. Za dalše informacije hlej tutón [https://www.semantic-mediawiki.org/wiki/Help:User_preferences wotrězk pomocy]", "smw-prefs-ask-options-tooltip-display": "Parametrowy tekst jako spěšne info zwobraznić", "smw-ui-tooltip-title-property": "<PERSON><PERSON><PERSON><PERSON>", "smw-ui-tooltip-title-quantity": "Mnóstwo", "smw-ui-tooltip-title-info": "Informacije", "smw-ui-tooltip-title-service": "Słužbne wotkazy", "smw-ui-tooltip-title-warning": "Warnowanje", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "Podawk", "smw-ui-tooltip-title-note": "Přispomnjenka", "smw-ui-tooltip-title-legend": "<PERSON>a", "smw_unknowntype": "Typ „$1“ tuteje kajkos<PERSON>e je njepłaćiwy", "smw-concept-cache-text": "Koncept ma dohromady $1 {{PLURAL:$1|stronu|stronje|strony|stronow}} a je so $2 posledni raz zaktualizował.", "smw_concept_header": "Strony koncepta \"$1\"", "smw_conceptarticlecount": "Deleka {{PLURAL:$1|so $1 strona pokazuje|so $1 stronje pokazujetej|so $1 strony pokazuja|so $1 stronow pokazuje}}.", "right-smw-admin": "Administraciske nadawki (Semantic MediaWiki)", "group-smwadministrator": "SMW-<PERSON><PERSON><PERSON>", "group-smwadministrator-member": "{{GENDER:$1|SMW-administrator|SMW-administratorka}}", "grouppage-smwadministrator": "{{ns:project}}:SMW-administratorojo", "action-smw-admin": "administraciske nadawki Semantic MediaWiki", "smw-sp-properties-cache-info": "Nalistowane daty pochadźeja z [https://www.semantic-mediawiki.org/wiki/Caching pufrowaka] a su so $1 posledni raz zaktualozowali.", "smw-sp-properties-header-label": "<PERSON><PERSON><PERSON><PERSON>", "smw-sp-admin-settings-button": "Lisćinu nastajenjo<PERSON> w<PERSON><PERSON><PERSON>", "smw-admin-objectid": "ID:", "smw-livepreview-loading": "<PERSON><PERSON>", "smw-listingcontinuesabbrev": "(pokročowanje)", "smw-showingresults": "Deleka so hač {{PLURAL:$1|'''1''' wuslědk pokazuje|'''$1''' wuslědkaj pokazujetej|'''$1''' wuslědki pokazuja|'''$1''' wuslědkow pokazuje}}, započinajo z #'''$2'''."}