{"@metadata": {"authors": ["<PERSON><PERSON>", "Bengtsson96", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Flrn", "Freked", "Gabbe.g", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Josve05a", "<PERSON><PERSON><PERSON>", "<PERSON>", "Lokal Profil", "M.M.S.", "<PERSON><PERSON><PERSON>", "MagnusA", "Martinwiss", "McDut<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nemo bis", "Per", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sabelöga", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Umeaboy", "WikiPhoenix", "아라"]}, "smw-desc": "Gör din wiki mer tillgäng<PERSON>g – för både maskiner och människor ([https://www.semantic-mediawiki.org/wiki/Help:User_manual dokumentation online])", "smw-error": "<PERSON><PERSON>", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] installerade och aktiverades men saknar en lämplig [https://www.semantic-mediawiki.org/wiki/Help:Upgrade uppgraderingsnyckel].", "smw-upgrade-release": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-upgrade-progress": "Framsteg", "smw-upgrade-error-title": "Fel » Semantic MediaWiki", "smw-upgrade-error-why-title": "<PERSON><PERSON><PERSON><PERSON>r ser jag denna sida?", "smw-upgrade-error-how-title": "Hur kan jag å<PERSON><PERSON> felet?", "smw-extensionload-error-why-title": "<PERSON><PERSON><PERSON><PERSON>r ser jag denna sida?", "smw-extensionload-error-how-title": "Hur kan jag å<PERSON><PERSON> felet?", "smw-upgrade-maintenance-why-title": "<PERSON><PERSON><PERSON><PERSON>r ser jag denna sida?", "smw-semantics-not-enabled": "Semantic MediaWiki-funktionalitet är inte aktiverad på denna wiki.", "smw_viewasrdf": "RDF-matning", "smw_finallistconjunct": "och", "smw-factbox-head": "... mer om \"$1\"", "smw-factbox-facts": "<PERSON><PERSON><PERSON>", "smw-factbox-facts-help": "<PERSON><PERSON> påståenden och fakta som en användare har skapat", "smw-factbox-attachments": "Bilagor", "smw-factbox-attachments-value-unknown": "N/A", "smw-factbox-attachments-is-local": "Är lokal", "smw-factbox-attachments-help": "Visa tillgängliga bilagor", "smw-factbox-facts-derived": "<PERSON><PERSON><PERSON><PERSON> fakta", "smw-factbox-facts-derived-help": "<PERSON>r fakta som har av<PERSON>s från regler eller med hjälp av andra resonemangsverktyg.", "smw_isspecprop": "Den här egenskapen är en specialegenskap på den här wikin.", "smw-concept-cache-header": "Cacheanvändning", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count Konceptcachen] innehåller {{PLURAL:$1|'''en''' entitet|'''$1''' entiteter}} ($2).", "smw-concept-no-cache": "Ingen tillgänglig cache.", "smw_concept_description": "Beskrivning av konceptet ”$1”", "smw_no_concept_namespace": "Koncept kan endast defineras på sidor i namnrymden Concept:", "smw_multiple_concepts": "<PERSON>arje konceptsida kan endast ha en konceptdefinition.", "smw_concept_cache_miss": "Konceptet \"$1\" kan inte användas för tillf<PERSON>, eftersom wiki-konfigurationen kräver att det beräknas off-line.\nOm problemet inte försvinner efter någon tid, så be din administratör att göra konceptet tillgängligt.", "smw_noinvannot": "Värden kan inte tilldelas inverterade egenskaper.", "version-semantic": "Semantiska tillägg", "smw_baduri": "<PERSON><PERSON><PERSON>, URI:er på formen \"$1\" är inte tillåtna.", "smw_printername_count": "<PERSON><PERSON>", "smw_printername_csv": "CSV", "smw_printername_dsv": "DSV", "smw_printername_debug": "<PERSON><PERSON><PERSON> (för experter)", "smw_printername_embedded": "Bädda in sidinnehåll", "smw_printername_json": "JSON", "smw_printername_list": "Lista", "smw_printername_plainlist": "<PERSON><PERSON><PERSON> lista", "smw_printername_ol": "<PERSON><PERSON><PERSON>d lista", "smw_printername_ul": "Punktlista", "smw_printername_table": "<PERSON><PERSON>", "smw_printername_broadtable": "<PERSON><PERSON> tabell", "smw_printername_template": "Mall", "smw_printername_templatefile": "Mallfil", "smw_printername_rdf": "RDF", "smw_printername_category": "<PERSON><PERSON><PERSON>", "validator-type-class-SMWParamSource": "text", "smw-paramdesc-limit": "Maximalt antal resultat att visa", "smw-paramdesc-offset": "Vilket resultat i ordningen ska visas som det första?", "smw-paramdesc-headers": "Visa rubriker/egenskapsnamn", "smw-paramdesc-mainlabel": "Etiketten som ska ges till huvudsidans namn", "smw-paramdesc-link": "Visa värden som länkar", "smw-paramdesc-intro": "Text som ska visas före resultatet (om det finns något)", "smw-paramdesc-outro": "Text som ska visas efter resultatet (om det finns något)", "smw-paramdesc-default": "Text som ska visas om det inte finns något resultat", "smw-paramdesc-sep": "Skiljetecken mellan resultaten", "smw-paramdesc-showsep": "Visa skiljetecken högst upp i CSV-filen (\"sep=<value>\")", "smw-paramdesc-distribution": "Räkna och visa antalet förekomster istället för att visa alla värden.", "smw-paramdesc-distributionsort": "Sortera värdena efter hur ofta de förekommer.", "smw-paramdesc-distributionlimit": "Begränsa värdefördelningen till vissa värdens antal", "smw-paramdesc-template": "Namnet på den mall som ska skriva ut resultatet", "smw-paramdesc-columns": "Antalet kolumner som ska visa resultat", "smw-paramdesc-userparam": "Ett värde som skickas till varje mall, om mallar används", "smw-paramdesc-introtemplate": "Mall som ska ska visas före resultatet, om det finns något", "smw-paramdesc-outrotemplate": "Mall som ska visas efter resultatet, som det finns något", "smw-paramdesc-embedformat": "HTML-element för rubriker", "smw-paramdesc-embedonly": "Visa inga rubriker", "smw-paramdesc-table-class": "Ytterligare CSS-klass som ska användas för tabellen", "smw-paramdesc-table-transpose": "Visa tabellrubriker vertikalt och resultat horisontellt", "smw-paramdesc-rdfsyntax": "RDF-syntax som ska användas", "smw-paramdesc-csv-sep": "Anger en kolumnseparator", "smw-paramdesc-csv-valuesep": "Anger en värdeseparator", "smw-paramdesc-dsv-separator": "Skiljetecken att använda", "smw-paramdesc-dsv-filename": "Namn på DSV-filen", "smw-paramdesc-filename": "<PERSON><PERSON> på filen", "smw-smwdoc-description": "<PERSON>r en tabell med alla parametrar som kan användas för det angivna resultatformatet, samt standardvärden och beskrivningar.", "smw-smwdoc-par-format": "Resultatformatet som parameter-dokumentation ska visas för.", "smw-smwdoc-par-parameters": "Vilka parametrar som ska visas. Välj ”specific” för sådana som hör till det här formatet, ”base” för sådana som är tillgängliga för alla format, och ”all” för samtliga parametrar.", "smw-paramdesc-sort": "Egenskap som resultatet ska sorteras efter", "smw-paramdesc-order": "Hur ska resultaten sorteras?", "smw-paramdesc-searchlabel": "Text för att visa ytterligare resultat", "smw-paramdesc-named_args": "Namnet på de argument som ska ges till mallen", "smw-paramdesc-export": "Export-alternativ", "smw-paramdesc-prettyprint": "”Pretty-print”-version med indrag och radbrytningar", "smw-paramdesc-source": "Alternativ frågekälla", "smw-paramdesc-jsonsyntax": "JSON-syntax som ska användas", "smw-printername-feed": "RSS- och Atom-feed", "smw-paramdesc-feedtype": "Feed-typ", "smw-paramdesc-feedtitle": "Texten som ska användas som rubrik för feeden", "smw-paramdesc-feeddescription": "Texten som ska användas som beskrivning av feeden", "smw-paramdesc-feedpagecontent": "<PERSON><PERSON><PERSON><PERSON>ll som ska visas tillsammans med feeden", "smw-label-feed-description": "$1 $2-feed", "smw-paramdesc-mimetype": "Mediatypen (MIMI-typen) för utmatningsfilen", "smw_iq_disabled": "Beklagar. Semantiska sökning har slagits av på den här wikin.", "smw_iq_moreresults": "… ytterligare resultat", "smw_parseerror": "Det angivna värdet förstods inte.", "smw_notitle": "\"$1\" kan inte användas som sidnamn på den här wikin.", "smw_noproperty": "”$1” kan inte användas som namn på en egenskap på den här wikin.", "smw_wrong_namespace": "Endast sidor i namnrymden \"$1\" till<PERSON><PERSON> här.", "smw_manytypes": "<PERSON>r än en typ definierad för e<PERSON>.", "smw_emptystring": "<PERSON><PERSON> str<PERSON>nger accepteras inte.", "smw_notinenum": "\"$1\" är inte i listan ($2) över [[Property:Allows value|tillåtna värden]] för egenskapen \"$3\".", "smw-datavalue-constraint-error-allows-value-list": "\"$1\" finns inte i listan ($2) över [[Property:Allows value|tillåtna värden]] för egenskapen \"$3\".", "smw-datavalue-constraint-error-allows-value-range": "\"$1\" är inte inom intervallet för \"$2\" som specificeras av begränsningen [[Property:Allows value|tillåtna värden]] för egenskapen \"$3\".", "smw_noboolean": "\"$1\" är inte ett giltigt booleskt värde (sant/falskt).", "smw_true_words": "sant,s,ja,j", "smw_false_words": "falskt,f,nej,n", "smw_nofloat": "\"$1\" är inte ett tal.", "smw_infinite": "Tal så stora som \"$1\" stödjs inte.", "smw_unitnotallowed": "”$1” är ingen giltigt måttenhet för den här egenskapen.", "smw_nounitsdeclared": "Inga måttenheter har definierats för den här egenskapen.", "smw_novalues": "Inga värden angivna.", "smw_nodatetime": "Datumet \"$1\" förstods inte.", "smw_toomanyclosing": "\"$1\" uppträder för många gånger i efterfrågningen.", "smw_noclosingbrackets": "Användningen av \"<nowiki>[[</nowiki>\" i din efterfrågning stängdes inte av \"]]\".", "smw_misplacedsymbol": "Symbolen \"$1\" användes på en plats där den inte är användbar.", "smw_unexpectedpart": "Delen \"$1\" av efterfrågningen förstods inte.\nVissa resultat kan bli oväntade.", "smw_emptysubquery": "Någon underfråga har inget giltigt villkor.", "smw_misplacedsubquery": "Någon underfråga användes på ett ställe där inga underf<PERSON><PERSON><PERSON>.", "smw_valuesubquery": "Underfr<PERSON>gor stöds inte för värden på egenskapen “$1”.", "smw_badqueryatom": "Någon del “<nowiki>[[…]]</nowiki>” av frågan förstods inte.", "smw_propvalueproblem": "Värdet på egenskap “$1” förstods inte.", "smw_noqueryfeature": "För någon frågefunktion saknades det stöd i denna wikin och delar av frågan hoppades över ($1).", "smw_noconjunctions": "F<PERSON>r konjunktioner i frågor saknas det stöd i denna wikin och delar av frågan hoppades över ($1).", "smw_nodisjunctions": "<PERSON><PERSON><PERSON> disjunktioner i frågor saknas det stöd i denna wikin och delar av frågan hoppades över ($1).", "smw_querytoolarge": "Följande {{PLURAL:$2|frågevillkor|$2 frågevillkor}} kunde inte tas hänsyn till på grund av wikins begränsningar i frågestorlek eller -djup: <code>$1</code>.", "smw_notemplategiven": "<PERSON><PERSON><PERSON> att den här frågan ska fungera behöver du ange ett värde på parametern \"template\".", "smw_db_sparqlqueryproblem": "Svar<PERSON> på frågan kunde inte fås från SPARQL-databasen. Det här felet kan vara tillfälligt eller också så kan det bero på ett permanent fel med databasen.", "smw_db_sparqlqueryincomplete": "Den här frågan visade sig vara för komplex, och har arbetet med att besvara den har avbrutits. En del resultat kan saknas. Försök om möjligt med en enklare fråga.", "smw_type_header": "Egenskaper av typen “$1”", "smw_typearticlecount": "Visar $1 {{PLURAL:$1|egenskap|egenskaper}} som använder den här typen.", "smw_attribute_header": "Sidor som använder egenskapen \"$1\"", "smw_attributearticlecount": "Visar $1 {{PLURAL:$1|sida|sidor}} som använder den här egenskapen.", "smw-propertylist-subproperty-header": "Underegenskaper", "smw-propertylist-redirect-header": "Synonymer", "smw-propertylist-count": "Visar $1 {{PLURAL:$1|relaterad entitet|relaterade entiteter}}.", "smw-propertylist-count-with-restricted-note": "Visar $1 {{PLURAL:$1|relaterad entitet|relaterade entiteter}} (fler finns tillgängliga men antalet som visas begränsas till \"$2\").", "smw-propertylist-count-more-available": "Visar $1 {{PLURAL:$1|relaterad entitet|relaterade entiteter}} (fler finns tillgängliga).", "specialpages-group-smw_group-maintenance": "<PERSON><PERSON><PERSON><PERSON>", "specialpages-group-smw_group-search": "Bläddra och sök", "exportrdf": "Exportera sidor till RDF", "smw_exportrdf_docu": "Den här sidan låter dig hämta data från en sida i RDF-format.\nSkriv sidtitlar i textrutan härunder för att exportera sidor (en titel per rad).", "smw_exportrdf_recursive": "Exportera alla relaterade sidor rekursivt.\nObservera att resultatet kan bli stort!", "smw_exportrdf_backlinks": "Exportera också alla sidor som refererar till de exporterade sidorna.\nSkapar en RDF som kan gås igenom.", "smw_exportrdf_lastdate": "Exportera inte sidor som inte ändrats efter den uppgivna tidpunkten.", "smw_exportrdf_submit": "Exportera", "uriresolver": "URI-lösare", "properties": "Egenskaper", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "Följande egenskaper används i wikin", "smw_property_template": "$1 av typen $2 ($3 {{PLURAL:$3| gång| gånger}})", "smw_propertylackspage": "Alla egenskaper ska beskrivas av en sida!", "smw_propertylackstype": "Ingen typ specificerades för denna e<PERSON> (antar typ $1 tills vidare).", "smw_propertyhardlyused": "Denna egenskap används knappt i wikin!", "smw-property-name-invalid": "Egenskapen $1 kan inte användas (ogiltigt egenskapsnamn).", "smw-property-name-reserved": "\"$1\" har listats som ett reservat namn och bör inte användas som en egenskap. Följande [https://www.semantic-mediawiki.org/wiki/Help:Property_naming hjälpsida] kan innehålla information om varför detta namn reserverades.", "smw-sp-property-searchform": "Visa egenskaper som innehåller:", "smw-sp-property-searchform-inputinfo": "Indata är skiftlägeskänsligt och, när den används för filtrering, visas endast egenskaper som matchar villkoret.", "smw-special-property-searchform": "Visa egenskaper som innehåller:", "smw-special-property-searchform-inputinfo": "Indata är skiftlägeskänsligt och, när den används för filtrering, visas endast egenskaper som matchar villkoret.", "smw-special-property-searchform-options": "Alternativ", "smw-special-wantedproperties-filter-label": "Filter:", "smw-special-wantedproperties-filter-none": "Ingen", "smw-special-wantedproperties-filter-unapproved": "Inte godkänd", "smw-special-wantedproperties-filter-unapproved-desc": "Filtreringsalternativ som används i anslutning med auktoriseringsläget.", "concepts": "<PERSON><PERSON><PERSON><PERSON>", "smw-special-concept-docu": "<PERSON><PERSON> [https://www.semantic-mediawiki.org/wiki/Help:Concepts koncept] är ett slags ”dynamisk katagori”, det vill säga en samling sidor som skapas automatiskt utifrån en semantisk sökning", "smw-special-concept-header": "Lista över begrepp", "smw-special-concept-count": "Följande {{PLURAL:$1|begrepp|$1 begrepp}} {{PLURAL:$1|är}} listade.", "smw-special-concept-empty": "Inget begrepp hittades.", "unusedproperties": "Oanvända egenskaper", "smw-unusedproperties-docu": "Denna sida listar [https://www.semantic-mediawiki.org/wiki/Unused_properties oanvända egenskaper] som finns fastän ingen annan sida använder dem. For en annan vy, se <PERSON>idorna [[Special:Properties|alla]] eller [[Special:WantedProperties|önskade egenskaper]].", "smw-unusedproperty-template": "$1 av typen $2", "wantedproperties": "Önskade egenskaper", "smw-wantedproperties-docu": "Denna sida listar [https://www.semantic-mediawiki.org/wiki/Wanted_properties önskade egenskaper] som används i wikin men har ännu inte en sida som beskriver dem. För en annan vy, se <PERSON>idorna [[Special:Properties|alla]] eller  [[Special:UnusedProperties|oanvända egenskaper]].", "smw-wantedproperty-template": "$1 (använd $2 {{PLURAL:$2|gång|gånger}})", "smw-special-wantedproperties-docu": "Denna sida listar [https://www.semantic-mediawiki.org/wiki/Wanted_properties önskade egenskaper] som används i wikin men har ännu inte en sida som beskriver dem. För en annan vy, se <PERSON>idorna [[Special:Properties|alla]] eller  [[Special:UnusedProperties|oanvända egenskaper]].", "smw-special-wantedproperties-template": "$1 (används $2 {{PLURAL:$2|gång|gånger}})", "smw_purge": "Uppdatera", "smw-purge-failed": "Uppdatering misslyckades", "types": "Typer", "smw_types_docu": "Lista över [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes tillgängliga datatyper] där varje [https://www.semantic-mediawiki.org/wiki/Help:Datatype typ] representerar en unik uppsättning attribut för att beskriva ett värde i termer av hur det lagras och visas, som ärvs av en tilldelad egenskap.", "smw-special-types-no-such-type": "Den angivna datatypen finns inte", "smw-statistics": "Semantisk statistik", "smw-statistics-property-instance": "Egenskaps{{PLURAL:$1|värde|värden}} (totalt)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Egenskap|Egenskaper}}]] (totalt)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|Egenskap|Egenskaper}} (totalt)", "smw-statistics-property-used": "{{PLURAL:$1|Egenskap|Egenskaper}} (används med minst ett värde)", "smw-statistics-property-page": "{{PLURAL:$1|Egenskap|Egenskaper}} (som har en egen sida)", "smw-statistics-property-type": "{{PLURAL:$1|Egenskap|Egenskaper}} (som har tilldelats en datatyp)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Fråga|Frågor}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Förfråga|Förfrågningar}}]]", "smw-statistics-query-size": "Frågestorlek", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|Koncept}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Koncept}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|Underobjekt}}]]", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|Underobjekt}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|Datatyp|Datatyper}}]]", "smw-statistics-error-count": "{{PLURAL:$1|Egenskapsvärde|Egenskapsvärden}} ([[Special:ProcessingErrorList|{{PLURAL:$1|felaktig annotering|felaktiga annoteringar}}]])", "smw-statistics-error-count-legacy": "{{PLURAL:$1|Egenskapsvärde|Egenskapsvärden}} ({{PLURAL:$1|felaktig kommentar|felaktiga kommentarer}})", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities {{PLURAL:$1|Föråldrad entitet|Föråldrade entiteter}}]", "smw_uri_doc": "The URI resolver implements the [$1 W3C TAG finding on httpRange-14].\nIt takes care that humans do not turn into websites.", "ask": "Semantisk sökning", "smw_ask_sortby": "Sortera efter kolumn (valfritt)", "smw_ask_ascorder": "Stigande", "smw_ask_descorder": "<PERSON><PERSON><PERSON>", "smw-ask-order-rand": "Slumpartad", "smw_ask_submit": "<PERSON>ö<PERSON>", "smw_ask_editquery": "<PERSON><PERSON><PERSON> fråga", "smw_add_sortcondition": "[Lägg till sorteringsvillkor]", "smw-ask-sort-add-action": "Lägg till sorteringsvillkor", "smw_ask_hidequery": "D<PERSON><PERSON><PERSON> f<PERSON> (kompakt vy)", "smw_ask_help": "Frågehjälp", "smw_ask_queryhead": "Villkor", "smw_ask_printhead": "Urval för u<PERSON>", "smw_ask_printdesc": "(lägg till varje egenskap på en egen rad)", "smw_ask_format_as": "Formatera som:", "smw_ask_defaultformat": "standard", "smw_ask_otheroptions": "Andra alternativ", "smw-ask-otheroptions-info": "Den här delen innehåller alternativ som påverkar ”printout statements”. Beskrivningar av parametrarna visas när du för muspekaren över dem.", "smw-ask-otheroptions-collapsed-info": "Använd plus-tecknet för att visa alla tillgänliga alternativ.", "smw_ask_show_embed": "Visa inbäddad kod", "smw_ask_hide_embed": "Göm inbäddad kod", "smw_ask_embed_instr": "<PERSON>v<PERSON>nd koden nedan för att lägga in den här frågan i en wiki-sida.", "smw-ask-delete": "<PERSON> bort", "smw-ask-sorting": "Sortering", "smw-ask-options": "Alternativ", "smw-ask-options-sort": "Sorteringsalternativ", "smw-ask-format-options": "Format och alternativ", "smw-ask-parameters": "Parametrar", "smw-ask-search": "<PERSON>ö<PERSON>", "smw-ask-debug": "Felsökning", "smw-ask-debug-desc": "Skapar felsökningsinformation för fråga", "smw-ask-no-cache": "Inaktivera frågecache", "smw-ask-no-cache-desc": "Resultat utan frågecache", "smw-ask-result": "Resultat", "smw-ask-empty": "<PERSON><PERSON> bort alla poster", "smw-ask-download-link-desc": "Ladda ned frågeresultat i formatet $1", "smw-ask-format": "Format", "smw-ask-format-selection-help": "Hjälp med det valda formatet $1.", "smw-ask-condition-change-info": "Villkoret förändrades och sökmotorn behöver köra frågan igen för att producera resultat som matchar de nya kraven.", "smw-ask-input-assistance": "Inmatningassistans", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance Inmatnings assistans] tillhandahålls för utskrifter, sortering och villkorsfält. Villkorsfältet kräver att en av följande prefix används:", "smw-ask-condition-input-assistance-property": "<code>p:</code> för att hämta egenskapsförslag (t.ex. <code>[[p:<PERSON>r …</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> för att hämta kategoriförslag", "smw-ask-condition-input-assistance-concept": "<code>con:</code> för att hämta konceptsökningar", "smw-ask-query-search-info": "Fr<PERSON>gan <code><nowiki>$1</nowiki></code> besvarades av {{PLURAL:$3|1=<code>$2</code> (från cachen)|<code>$2</code> (från cachen)|<code>$2</code>}} i $4 {{PLURAL:$4|sekund|sekunder}}.", "searchbyproperty": "Sök<PERSON> per egenskap", "processingerrorlist": "Lista över bearbetningsfel", "smw-processingerrorlist-intro": "Följande lista tillhandahåller en översikt över bearbetningsfelen som uppstod i anslutning med [https://www.semantic-mediawiki.org/ Semantic MediaWiki]. Det är rekommenderat att övervaka denna lista då och då och korrigera annoteringar om ogiltiga värden.", "smw_sbv_docu": "<PERSON><PERSON><PERSON> efter alla sidor som har en given egenskap och värde.", "smw_sbv_novalue": "Skriv in ett giltigt värde för egenskapen, eller visa alla egenskapsvärden för ”$1”.", "smw_sbv_displayresultfuzzy": "En lista över alla sidor som har egenskapen ”$1” med värdet ”$2”.\nEftersom det bara finns ett fåtal resultat, visas även snarlika värden.", "smw_sbv_property": "Egenskap:", "smw_sbv_value": "Värde:", "smw_sbv_submit": "<PERSON>ö<PERSON>", "browse": "Bläddra igenom wikin", "smw_browselink": "Bläddra genom egenskaper", "smw_browse_article": "Skriv namnet på sidan du vill börja bläddra från.", "smw_browse_go": "Visa", "smw_browse_show_incoming": "Visa inkommande egenskaper", "smw_browse_hide_incoming": "<PERSON><PERSON><PERSON><PERSON>", "smw_browse_no_outgoing": "<PERSON>na sida har inga e<PERSON>.", "smw_browse_no_incoming": "Inga egenskaper länkar till den här sidan.", "smw-browse-show-group": "Visa grupper", "smw-browse-hide-group": "<PERSON><PERSON><PERSON><PERSON>", "smw_inverse_label_default": "$1 av", "smw_inverse_label_property": "Benämning på inverterad egenskap", "pageproperty": "Sidegenskapssökning", "smw_pp_docu": "<PERSON>e antingen en sida och egenskap, eller bara en egenskap för att hämta alla tilldelade värden.", "smw_pp_from": "<PERSON><PERSON><PERSON>:", "smw_pp_type": "Egenskap:", "smw_pp_submit": "Hitta resultat", "smw_result_prev": "Föregående", "smw_result_next": "<PERSON><PERSON><PERSON>", "smw_result_results": "Resultat", "smw_result_noresults": "<PERSON><PERSON><PERSON>, inga resultat.", "smwadmin": "Administrativa och underhållsorienterande funktioner", "smw-admin-statistics-job-title": "Jobbstatistik", "smw-admin-statistics-job-docu": "Jobbstatistiken visar information om schemalagda Semantic MediaWiki-jobb som inte ännu har utförts. Antalet jobb kan vara något felaktiga eller innehålla misslyckade försök, l<PERSON><PERSON> [https://www.mediawiki.org/wiki/Manual:Job_queue manualen] för mer information.", "smw-admin-statistics-semanticdata-overview": "Översikt", "smw-admin-setupsuccess": "Lagringsmotorn har satts upp.", "smw_smwadmin_return": "Gå tillbaka till $1", "smw_smwadmin_updatestarted": "Nu har en ny process för att uppdatera semantiska data påbörjats.\nAlla semantiska data kommer att återuppbyggas och repareras där så behövs.\nDu kan följa processen på den här specialsidan.", "smw_smwadmin_updatenotstarted": "Process för att uppdatera pågår.\nStartar ingen ny process.", "smw_smwadmin_updatestopped": "Alla uppdateringsprocesser har avslutats.", "smw_smwadmin_updatenotstopped": "<PERSON><PERSON><PERSON> att avsluta uppdaterings-processen måste du aktivera kryss-rutan.", "smw-admin-docu": "Denna specialsida hjälper dig under installation, uppgradering, underhåll och användning av <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> och tillhandahåller också ytterligare administrativa funktioner och uppgifter, såväl som statistik.\nKom ihåg att säkerhetskopiera värdefull data innan du kör administrativa funktioner!", "smw-admin-environment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-db": "Inställning av databas", "smw-admin-dbdocu": "Semantic's MediaWiki kräver sin egen databas-struktur (och är oberoende från MediaWiki vilket inte påverkar resten av MediaWiki-installationen) för att kunna lagra semantic-datan.\nDet här installationsfunktionen kan exekveras flerfaldiga gånger utan att det gör någon skada, men det behövs endast en gång i installation eller uppgradering.", "smw-admin-permissionswarn": "Om operationen misslyckas och visar SQL-fel så har din användare för databasen (kolla inställningarna i filen \"LocalSettings.php\") förmodligen inte tillräckliga rättigheter.\nDet finns tre sätt att åtgärda detta: Ge rättigheter till användaren att skapa och radera tabeller, ändra temporärt användaren till ''root'' i \"LocalSettings.php\", eller använd underhålls-scriptet <code>setupStore.php</code>, vilken kan använda rättigheterna för en administratör.", "smw-admin-dbbutton": "Initialisera eller uppgradera tabeller", "smw-admin-announce": "<PERSON><PERSON><PERSON> att din wiki finns", "smw-admin-announce-text": "Om din wiki är offentligt kan du registrera den på <a href=\"https://wikiapiary.com\">WikiApiary</a>, wikin som listar upp wikier.", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> kommer ta bort eller ersätta följande {{PLURAL:$2|val}}:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> rekommenderas inte och kommer tas bort i $2", "smw-admin-deprecation-notice-config-replacement-other": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> ersattes av <code>$2</code>", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|val}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code> ersätts av <code>$2</code>", "smw-admin-deprecation-notice-title-notice": "Kommande ändringar", "smw-admin-deprecation-notice-title-replacement": "Ersatta eller omdöpta inställningar", "smw-admin-deprecation-notice-title-replacement-explanation": "Följande avsnitt innehåller inställningar som har döpts om eller annars förändrats och rekommenderas att uppdatera deras namn eller format omedelbart.", "smw-admin-deprecation-notice-title-removal": "Borttagna inställningar", "smw-admin-deprecation-notice-title-removal-explanation": "Inställningarna som listas har tagits bort i en tidigare utgåva men användning har upptäckts på denna wiki.", "smw-admin-deprecation-notice-section-legend": "Teckenförklaring", "smw-smwadmin-refresh-title": "Datareparation och -uppdatering", "smw_smwadmin_datarefresh": "Datauppbyggning", "smw_smwadmin_datarefreshdocu": "Det är möjligt att återställa all Semantic MediaWiki-data baserat på det aktuella innehållet för din wiki.\nDetta kan vara användbart för att reparera eller fräscha upp data om det interna formatet har ändrats på grund av programuppdateringar.\nUppdateringen utförs sida för sida och blir inte klar omedelbart.\nDet följande visar om en uppdatering pågår och tillåter dig att starta eller stoppa uppdateringar (såvida inte denna finess har stängts av av administratören).", "smw_smwadmin_datarefreshprogress": "<strong>En uppdatering pågår redan.</strong>\nDet är normalt att uppdateringen fortskrider långsamt, eftersom den endast uppdaterar data i små bitar åt gången varje gång en användare använder wikin.\nFör att avsluta uppdateringen snabbare, kan du köra MediaWiki-scriptet <code>runJobs.php</code> (använd valet <code>--maxjobs 1000</code>  för att begränsa antalet uppdateringar per körning).\nUppskattning av hur långt uppdateringen har kommit:", "smw_smwadmin_datarefreshbutton": "Schemalägg återuppbyggnad av data", "smw_smwadmin_datarefreshstop": "Stoppa denna uppdatering", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, jag är {{GENDER:$1|s<PERSON><PERSON>}}.", "smw-admin-support": "Få support", "smw-admin-supportdocu": "Diverse resurser tillhandahålls för kan hjälpa dig, om du får problem:", "smw-admin-installfile": "Om du har problem med din installation, bö<PERSON><PERSON> med att läsa instruktionerna i <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">installationsfilen</a> och <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">installationssidan</a>.", "smw-admin-smwhomepage": "Den kompletta användardokumentationen till Semantic MediaWiki finns på <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "<PERSON><PERSON> kan rapporteras på <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">ärendehanteraren</a>, sidan för att <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">rapportera buggar</a> tillhandahåller lite hjälp om hur man skriver en effektiv ärenderapport.", "smw-admin-questions": "Om du har ytterligare fr<PERSON><PERSON> fö<PERSON>, diskutera dem på <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">Semantic MediaWiki användarsändlista</a>!", "smw-admin-other-functions": "<PERSON><PERSON>", "smw-admin-statistics-extra": "Ytterligare statistik", "smw-admin-statistics": "Statistik", "smw-admin-supplementary-section-title": "Extra funktioner", "smw-admin-supplementary-section-subtitle": "Kärnfunktioner som stöds", "smw-admin-supplementary-section-intro": "Vissa av de listades funktionerna i detta avsnitt kan vara begränsade och är därför otillgängliga på denna wiki.", "smw-admin-supplementary-settings-title": "Konfiguration och inställningar", "smw-admin-supplementary-operational-statistics-title": "Driftstatistik", "smw-admin-supplementary-operational-statistics-intro": "<u>$1</u> visar en utvidgad samling statistik", "smw-admin-supplementary-duplookup-title": "<PERSON><PERSON><PERSON><PERSON> entiteter", "smw-admin-supplementary-operational-statistics-cache-title": "Cachestatistik", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u> visar cacherelaterad statistik", "smw-admin-supplementary-operational-table-statistics-title": "Tabellstatistik", "smw-admin-supplementary-operational-table-statistics-legend-general": "* <code>total_row_count</code> totala antalet rader i en tabell", "smw-admin-supplementary-operational-table-statistics-legend-id-table": "\n* <code>last_id</code> senaste ID som används för närva<PERSON>\n* <code>duplicate_count</code> antal dubbletter som finns i id_table (se också [[Special:SemanticMediaWiki/duplicate-lookup|Duplicate entities lookup]]) \n* <code>rows.rev_count</code> antal rader som har ett revisions_id tilldelat som indikerar en direkt wikipage-länk\n* <code>rows.smw_namespace_group_by_count</code> antal aggregerade rader för namnområden som används i tabellen\n* <code>rows.smw_proptable_hash.query_match_count</code> antal frågeobjekt med en motsvarande tabellreferens\n* <code>rows.smw_proptable_hash.query_null_count</code> antal frågeobjekt utan tabellreferens (länkad, flytande referens)", "smw-admin-supplementary-operational-table-statistics-legend-blob-table": "\n* <code>unique_terms_occurrence_in_percent</code> procent av termer som är unika (en låg procentandel anger att repetitiva termer upptar tabellinnehållet och indexet) \n* <code>rows.terms_occurrence.single_occurrence_total_count</code> antal termer som bara visas en gång\n* <code>rows.terms_occurrence.multi_occurrence_total_count</code> antal termer som visas mer än en gång", "smw-admin-supplementary-elastic-version-info": "Version", "smw-admin-supplementary-elastic-section-subtitle": "Elasticsearch", "smw-admin-supplementary-elastic-intro": "<u>$1</u> visar detaljer om inställningar och indexstatistik", "smw-admin-supplementary-elastic-docu": "Denna sida innehåller information om inställningar, mappningar, hälsa och indexstatistik relaterad till ett Elasticsearch-kluster som är kopplat till Semantic MediaWiki och dess [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>ElasticStore</code>].", "smw-admin-supplementary-elastic-functions": "<PERSON><PERSON><PERSON> som stöds", "smw-admin-supplementary-elastic-settings-title": "Inställningar (index)", "smw-admin-supplementary-elastic-settings-intro": "<u>$1</u> används av Elasticsearch för att hantera Semantic MediaWiki-index", "smw-admin-supplementary-elastic-mappings-title": "Kartläggningar", "smw-admin-supplementary-elastic-mappings-intro": "<u>$1</u> för att lista index och fältmappningar", "smw-admin-supplementary-elastic-mappings-docu": "Denna sida innehåller fältmappningsinformation som används av det aktuella indexet. Det rekommenderas att övervaka kartläggningarna i samband med <code>index.mapping.total_fields.limit</code> (anger det maximala antalet fält i ett index som är tillåtet).", "smw-admin-supplementary-elastic-mappings-docu-extra": "Koden <code>property_fields</code> refererar till antalet indexerade kärnfält medan <code> nested_fields </code> refererar till ett ackumulerat antal ytterligare fält som tilldelats ett kärnfält för att stödja specifika strukturerade sökmönster.", "smw-admin-supplementary-elastic-mappings-summary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-supplementary-elastic-mappings-fields": "Fältmappningar", "smw-admin-supplementary-elastic-nodes-title": "Noder", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u> visar nodstatistik", "smw-admin-supplementary-elastic-indices-title": "Index", "smw-admin-supplementary-elastic-indices-intro": "<u>$1</u> ger en översikt över tillgängliga index och deras statistik", "smw-admin-supplementary-elastic-statistics-title": "Statistik", "smw-admin-supplementary-elastic-statistics-intro": "<u>$1</u> visar statistik på indexnivå", "smw-admin-supplementary-elastic-statistics-docu": "Denna sida ger en inblick i indexstatistik för olika operationer som sker på indexnivå, den returnerade statistiken aggregeras med primärsatser och totala aggregeringar. [Https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-stats.html hjälpsida] innehåller en detaljerad beskrivning av tillgänglig indexstatistik.", "smw-admin-supplementary-elastic-status-replication": "Replikationsstatus", "smw-admin-supplementary-elastic-status-last-active-replication": "Senaste aktiva replikering: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "Uppdateringsintervall: $1", "smw-admin-supplementary-elastic-status-recovery-job-count": "Återställningsjobb: $1 (uppskattning)", "smw-admin-supplementary-elastic-status-file-ingest-job-count": "Ingång (fil) jobb eftersläpning: $1 (uppskattning)", "smw-admin-supplementary-elastic-status-rebuild-lock": "Replikering låst: $1 (ombyggnad pågår)", "smw-admin-supplementary-elastic-status-replication-monitoring": "Replikeringsövervakning (aktiv): $1", "smw-admin-supplementary-elastic-replication-header-title": "Replikationsstatus", "smw-admin-supplementary-elastic-replication-function-title": "Replikation", "smw-admin-supplementary-elastic-replication-intro": "<u>$1</u> visar information om misslyckade repliker", "smw-admin-supplementary-elastic-replication-docu": "Denna sida ger information om [https://www.semantic-mediawiki.org/wiki/Help:Replication_monitoring replikeringsstatus] för enheter som rapporterades ha problem med Elasticsearch-klustret. Det rekommenderas att granska listade enheter och rensa innehållet för att bekräfta att det var ett tillfälligt problem.", "smw-admin-supplementary-elastic-replication-files-docu": "Det bör noteras att för listan över filer måste jobbet [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion file ingest] köras först och måste slutföra behandlingen.", "smw-admin-supplementary-elastic-replication-files": "Filer", "smw-admin-supplementary-elastic-replication-pages": "<PERSON><PERSON>", "smw-admin-supplementary-elastic-endpoints": "Slutpunkter", "smw-admin-supplementary-elastic-config": "Kon<PERSON>gu<PERSON><PERSON>", "smw-admin-supplementary-elastic-no-connection": "Wikin är för nä<PERSON>e ''oförmögen'' att upprätta en anslutning till Elasticsearch -klustret. Vänligen kontakta wiki -administratören för att undersöka problemet eftersom det gör att index och sökförmåga för systemet inte fungerar.", "smw-list-count": "Listan innehåller $1 {{PLURAL:$1|element}}.", "smw-property-label-uniqueness": "E<PERSON><PERSON><PERSON> \"$1\" matchades med minst en annan fastighetsrepresentation. Se [https://www.semantic-mediawiki.org/wiki/Help:Property_uniqueness-hjälpsidan] om hur du löser detta problem.", "smw-property-label-similarity-title": "Fastighetsetikett likhetsrapport", "smw-property-label-similarity-intro": "<u>$1</u> beräknar liknelser för befintliga egenskapsetiketter", "smw-property-label-similarity-threshold": "Gränsvärde:", "smw-property-label-similarity-type": "Visa typ-ID", "smw-property-label-similarity-noresult": "Inga resultat hittades för de valda alternativen.", "smw-property-label-similarity-docu": "<PERSON>na sida jämför [https://www.semantic-mediawiki.org/wiki/Property_similarity distans] (för att inte förväxla med en semantisk eller lexikal likhet) mellan fastighetsetiketter och rapporterar dem om de överskrider tröskeln. Rapporten kan hjälpa till att filtrera felstavade eller motsvarande egenskaper som representerar samma koncept (se specialsidan [[Special:Properties|properties]] för att klargöra koncept och användning av rapporterade egenskaper). Tröskeln kan justeras för att vidga eller begränsa avståndet som används för den ungefärliga matchningen. <code>[[Property:$1|$1]]</code> används för att undanta egenskaper från analysen.", "smw-admin-operational-statistics": "Denna sida innehåller driftsstatistik samlad i eller från Semantic MediaWiki-relaterade funktioner. En utökad lista med wiki-specifik statistik kan hittas [[Special:Statistics|<b>here</b>]].", "smw_adminlinks_datastructure": "Datastruktur", "smw_adminlinks_displayingdata": "Datavisning", "smw_adminlinks_inlinequerieshelp": "Hjälp för in<PERSON>ä<PERSON> frågor", "smw-page-indicator-usage-count": "Uppskattat [https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count användningsantal]: {{PLURAL:$2|'''$2'''}}", "smw-property-indicator-type-info": "{{PLURAL:$1|Användardefinierad|Systemdefinierad}} egenskap", "smw-property-indicator-last-count-update": "Uppskattat användningsantal\nSenast uppdaterad: $1", "smw-concept-indicator-cache-update": "Cachemängd\nSenast uppdaterad: $1", "smw-createproperty-isproperty": "Det här är en egenskap av typen $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Det tillåtna värdet|De tillåtna värdena}} för den här egenskapen är:", "smw-paramdesc-category-delim": "<PERSON><PERSON><PERSON>eck<PERSON>", "smw-paramdesc-category-template": "Mall att formatera resultatet med", "smw-paramdesc-category-userparam": "Parameter till mallen", "smw-info-par-message": "Meddelande att skriva ut.", "smw-info-par-icon": "Ikon att visa, antingen \"info\" eller \"warning\".", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "Allmänna alternativ", "prefs-extended-search-options": "Utökad sökning", "prefs-ask-options": "Semantisk sökning", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] och tillh<PERSON>rande tillägg ger individuella preferenser för en grupp valda funktioner och funktioner. En lista över enskilda inställningar med deras beskrivning och egenskaper finns på följande [https://www.semantic-mediawiki.org/wiki/Help:User_preferences help page].", "smw-prefs-ask-options-tooltip-display": "Visa parametertext som ett infoverktygstips på #ask [[Special:Ask|query builder]] specialsida.", "smw-prefs-ask-options-compact-view-basic": "Aktivera grundläggande kompaktvy", "smw-prefs-help-ask-options-compact-view-basic": "Om det är aktiverat, visas en minskad uppsättning länkar i Special:Ask kompaktvyn.", "smw-prefs-general-options-time-correction": "Aktivera tidskorrigering för speciella sidor med hjälp av den lokala [[Special:Preferences#mw-prefsection-rendering|time offset]] preferens", "smw-prefs-general-options-jobqueue-watchlist": "Visa bevakningslistan för jobbkön i min personliga bar", "smw-prefs-help-general-options-jobqueue-watchlist": "Om den är aktiverad visas en [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist lista över] väntande utvalda jobb tillsammans med deras beräknade kö-stor<PERSON>.", "smw-prefs-general-options-disable-editpage-info": "Inaktivera introduktionstexten på redigeringssidan.", "smw-prefs-general-options-disable-search-info": "Stäng av syntaxstödsinformation på standardsöksidan", "smw-prefs-general-options-suggester-textinput": "Aktivera ingångshjälp för semantiska enheter", "smw-prefs-help-general-options-suggester-textinput": "Om den är aktiverad kan du använda en [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance input assistance] för att hitta egenskaper, begrepp och kategorier från ett input-sammanhang.", "smw-ui-tooltip-title-property": "Egenskap", "smw-ui-tooltip-title-quantity": "Enhetskonvertering", "smw-ui-tooltip-title-info": "Information", "smw-ui-tooltip-title-service": "Service-länkar", "smw-ui-tooltip-title-warning": "Varning", "smw-ui-tooltip-title-error": "<PERSON><PERSON>", "smw-ui-tooltip-title-parameter": "Parameter", "smw-ui-tooltip-title-event": "Händelse", "smw-ui-tooltip-title-note": "Anteckning", "smw-ui-tooltip-title-legend": "F<PERSON>rk<PERSON>ing", "smw-ui-tooltip-title-reference": "Re<PERSON><PERSON>", "smw_unknowntype": "Typen \"$1\" för denna e<PERSON> är ogiltig", "smw-concept-cache-text": "Det här konceptet har totalt {{PLURAL:$1|en sida|$1 sidor}}, och uppdaterades senast $3, $2.", "smw_concept_header": "<PERSON><PERSON> som hör till konceptet \"$1\"", "smw_conceptarticlecount": "Visar {{PLURAL:$1|en sida|$1 sidor}}", "smw-qp-empty-data": "Önskad data kunde inte visas på grund av att vissa urvalskriterier är otillräckliga.", "right-smw-admin": "Tillgång till administrativa uppgifter (Semantisk MediaWiki)", "right-smw-patternedit": "<PERSON><PERSON><PERSON>komst för att behålla tillåtna reguljära uttryck och mönster (Semantic MediaWiki)", "right-smw-pageedit": "Redigeringsåtkomst för sidor med annoteringen <code><PERSON>r redigeringsskyddad</code> (Semantic MediaWiki)", "right-smw-schemaedit": "Redigera [https://www.semantic-mediawiki.org/wiki/Help:Schema schemasidor] (Semantic MediaWiki)", "right-smw-viewjobqueuewatchlist": "Åtkomst till jobbkön [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist watchlist] funktion (Semantic MediaWiki)", "right-smw-viewentityassociatedrevisionmismatch": "Åtkomstinformation om en enhetsassocierad revisionsfel (Semantic MediaWiki)", "right-smw-vieweditpageinfo": "Visa [https://www.semantic-mediawiki.org/wiki/Help:Edit_help redigera hjälp] (Semantic MediaWiki)", "restriction-level-smw-pageedit": "skyddad (endast berättigade användare)", "action-smw-patternedit": "redigera regulj<PERSON>ra uttryck som Semantic MediaWiki använder", "action-smw-pageedit": "redigera sidor annoterade med <code>Is edit protected</code> (Semantic MediaWiki)", "group-smwadministrator": "Administratörer (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administratör (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:Administratörer (Semantic MediaWiki)", "group-smwcurator": "<PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|kurator (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:<PERSON><PERSON><PERSON> (Semantic MediaWiki)", "group-smweditor": "Redaktörer (Semantic MediaWiki)", "group-smweditor-member": "{{GENDER:$1|editor (Semantic MediaWiki)}}", "grouppage-smweditor": "{{ns:project}}:Editors (Semantic MediaWiki)", "action-smw-admin": "komma åt Semantisk MediaWikis administrationsåtgärder", "action-smw-ruleedit": "redigera regelsidor (Semantic MediaWiki)", "smw-property-namespace-disabled": "Fastigheten [https://www.semantic-mediawiki.org/wiki/Help:$smwgNamespacesWithSemanticLinks namespace] är inaktiverad, det är inte möjligt att försöka deklarera en typ eller andra egenskapsspecifika egenskaper för den här egenskapen.", "smw-property-predefined-default": "\"$1\" är en fördefinierad egenskap av typ $2.", "smw-property-predefined-common": "Den här egenskapen är förutplacerad (även känd som [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property]) och har ytterligare administratörsrättigheter men kan användas precis som alla andra [https:/ /www.semantic-mediawiki.org/wiki/Property användardefinierad egenskap].", "smw-property-predefined-ask": "\"$1\" är en fördefinierad egenskap som representerar metainformation (i form av ett [https://www.semantic-mediawiki.org/wiki/Subobject underobjektet]) om individuella förfrågningar och tillhandahålls av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-asksi": "\"$1\" är en fördefinierad egenskap som samlar antalet villkor som används i en fråga och tillhandahålls av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-askde": "\"$ 1\" är en fördefinierad egenskap som informerar om djupet i en fråga och tillhandahålls av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askde": "Det är ett numeriskt värde beräknat på grundval av underfrå<PERSON><PERSON> häckning, fastighetskedjor och tillgängliga beskrivningselement där utförandet av en fråga begränsas av <code> [https://www.semantic-mediawiki.org/wiki/Help: $ smwgQMaxDepth $ smwgQMaxDepth] </code> konfigurationsparameter.", "smw-property-predefined-askpa": "\"$1\" är en fördefinierad egenskap som beskriver parametrar som påverkar ett sökresultat och tillhandahålls av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-long-askpa": "Det är en del av en samling egenskaper som anger en [https://www.semantic-mediawiki.org/wiki/Help:Query_profiler frågeprofil].", "smw-sp-properties-docu": "<PERSON>na sida visar  [https://www.semantic-mediawiki.org/wiki/Property egenskaper] och deras användningsantal finns tillgängliga för denna wiki. För uppdaterad räknarstatistik rekommenderas att [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics fastighetsstatistik] underhållskript körs regelbundet. För en differentierad vy, se [[Special:UnusedProperties|unused]] eller [[Special:WantedProperties|wanted properties]] specialsidor.", "smw-sp-properties-cache-info": "Listad data har hämtats från [https://www.semantic-mediawiki.org/wiki/Caching cache], och uppdaterades sist $1.", "smw-sp-properties-header-label": "Lista med egenskaper", "smw-admin-settings-docu": "Visar en lista över alla standard- och lokaliserade inställningar som är relevanta för den semantiska MediaWiki-miljön. För detaljer om individuella inställningar, rådfråga hjälpsidan för [https://www.semantic-mediawiki.org/wiki/Help:Configuration konfiguration].", "smw-sp-admin-settings-button": "Generera lista med inställningar", "smw-admin-idlookup-title": "<PERSON><PERSON> upp", "smw-admin-idlookup-docu": "Det här avsnittet visar tekniska detaljer om en enskild enhet (wiki-sida, delobjekt, egendom, etc.) i Semantic MediaWiki. Inmatningen kan vara ett numeriskt ID eller ett strängvärde som matchar det relevanta sökfältet, men alla ID -referenser avser Semantic MediaWiki och inte MediaWikis sida eller revisions-ID.", "smw-admin-iddispose-title": "Förfogande", "smw-admin-iddispose-docu": "Det bör noteras att bortskaffningsoperationen är obegränsad och kommer att ta bort enheten från lagringsmotorn tillsammans med alla dess referenser i väntande tabeller, om den bekräftas. Utför den här uppgiften med '' '' försiktighet '' 'och endast efter att [https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal dokumentation] har konsulterats.", "smw-admin-iddispose-done": "ID \"$1\" togs bort från lagringsbackend.", "smw-admin-iddispose-references": "ID \"$1\" har {{PLURAL:$2|ingen|minst en}} aktiv referens till ID \"$1\".", "smw-admin-iddispose-references-multiple": "Lista över matcher med minst en aktiv referenspost.", "smw-admin-iddispose-no-references": "Sökningen kunde inte matcha \"$1\" till en tabellpost.", "smw-admin-idlookup-input": "Sök:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "Översikt", "smw-admin-tab-notices": "Meddelanden om avskrivning", "smw-admin-tab-maintenance": "<PERSON><PERSON><PERSON><PERSON>", "smw-admin-tab-supplement": "<PERSON><PERSON><PERSON><PERSON><PERSON> funk<PERSON>", "smw-admin-tab-registry": "Register", "smw-admin-tab-alerts": "Varningar", "smw-admin-alerts-tab-deprecationnotices": "Meddelanden om avskrivning", "smw-admin-alerts-tab-maintenancealerts": "Underhållsaviseringar", "smw-admin-alerts-section-intro": "Det här avsnittet visar varningar och meddelanden relaterade till inställningar, funktioner och andra aktiviteter som har klassificerats för att kräva uppmärksamhet från en administratör eller användare med tilldelade rättigheter.", "smw-admin-maintenancealerts-section-intro": "Följande varningar och meddelanden bör lösas och även om det inte är nödvändigt förväntas det bidra till att förbättra system- och driftsunderhåll.", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "Tabelloptimering", "smw-admin-maintenancealerts-lastoptimizationrun-alert": "Systemet har funnit att den senaste [https://www.semantic-mediawiki.org/wiki/Table_optimization-tabelloptimeringen] kördes för $2 dagar sedan (rekord från $1) som överstiger $3 dagars underhållströskel. Som nämnts i dokumentationen kommer körningsoptimeringar att göra det möjligt för frågeplaneraren att fatta bättre beslut om frågor, därför rekommenderas det att köra tabelloptimeringen regelbundet.", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "Föråldrade enheter", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert": "Systemet har räknat $1 [https://www.semantic-mediawiki.org/wiki/Outdated_entities föråldrade enheter] och nått en kritisk nivå av obevakat underhåll genom att överskrida tröskeln på $2. Det rekommenderas att köra [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code> disposeOutdatedEntities.php </code>] underhållskript.", "smw-admin-maintenancealerts-invalidentities-alert-title": "Ogiltiga enheter", "smw-admin-maintenancealerts-invalidentities-alert": "Systemet matchade $1 [https://www.semantic-mediawiki.org/wiki/Invalid_entities {{PLURAL:$1|entity|entities}}] till en [https://www.semantic-mediawiki.org/wiki/Unmaintained_namespace unmaintained namespace] och det rekommenderas att köra [https://www.semantic-mediawiki.org/wiki/disposeOutdatedEntities.php <code> disposeOutdatedEntities.php </code>] eller [https: //www.semantic-mediawiki.org/wiki/rebuildData.php <code> rebuildData.php </code>] underhållsskript.", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "Inställningar", "smw-admin-configutation-tab-namespaces": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-configutation-tab-schematypes": "Schematyper", "smw-admin-maintenance-tab-tasks": "Uppgifter", "smw-admin-maintenance-tab-scripts": "Underhållsskript", "smw-admin-maintenance-no-description": "Ingen beskrivning.", "smw-admin-maintenance-script-section-title": "Lista över tillgängliga underhållsskript", "smw-admin-maintenance-script-section-intro": "Följande underhållsskript kräver en administratör och åtkomst till kommandoraden för att kunna utföra listade skript.", "smw-admin-maintenance-script-description-dumprdf": "RDF-export av befintliga tripplar.", "smw-admin-maintenance-script-description-rebuildconceptcache": "Detta skript används för att hantera konceptcacher för Semantic MediaWiki där det kan skapa, ta bort och uppdatera utvalda cacher.", "smw-admin-maintenance-script-description-rebuilddata": "Återskapar alla semantiska data i databasen genom att bläddra igenom alla sidor som kan ha semantiska data.", "smw-admin-maintenance-script-description-rebuildelasticindex": "Återskapar Elasticsearch-indexet (endast för installationer som använder <code> ElasticStore </code>) genom att bläddra igenom alla enheter som har semantisk data.", "smw-admin-maintenance-script-description-rebuildelasticmissingdocuments": "Hitta saknade enheter i Elasticsearch (endast för installationer som använder <code> ElasticStore </code>) och schemalägg lämpliga uppdateringsjobb.", "smw-admin-maintenance-script-description-rebuildfulltextsearchtable": "Återställer <code> SQLStore </code> söktextindex (för installationer där inställningen har aktiverats.", "smw-admin-maintenance-script-description-rebuildpropertystatistics": "Återskapar användningsstatistiken för alla egendomsenheter.", "smw-admin-maintenance-script-description-removeduplicateentities": "Tar bort dubbletter som finns i utvalda tabeller som inte har några aktiva referenser.", "smw-livepreview-loading": "<PERSON><PERSON><PERSON>…", "smw-sp-searchbyproperty-description": "Denna sida erbjuder ett enkelt [https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces navigeringsgränssnitt] för att hitta entiteter som beskrivs av en egenskap och ett namngivet värde. Andra tillgängliga sökgränssnitt inkluderar [[Special:PageProperty|sidegenskapssök]] och [[Special:Ask|ask-förfrågningsbyggare]].", "smw-sp-searchbyproperty-resultlist-header": "Lista med resultat", "smw-sp-searchbyproperty-nonvaluequery": "En lista över värden som har tilldelats egenskapen \"$1\".", "smw-sp-searchbyproperty-valuequery": "En lista över sidor som har egenskapen \"$1\" med värdet \"$2\" annoterat.", "smw-datavalue-number-nullnotallowed": "\"$1\" returnerades med \"NULL\" som inte tillåts som en siffra.", "smw-search-input": "Inmatning och sökning", "smw-search-syntax": "Syntax", "smw-search-profile": "Utökad", "smw-search-profile-sort-best": "<PERSON><PERSON><PERSON> träff", "smw-search-profile-sort-recent": "Senaste", "smw-search-profile-sort-title": "Titel", "smw-search-profile-extended-help-query": "Länk till: $1", "smw-search-profile-extended-help-query-link": "(<PERSON><PERSON><PERSON> $1).", "smw-search-profile-extended-help-find-forms": "tillgängliga formulär", "smw-search-profile-extended-section-sort": "Sortera efter", "smw-search-profile-extended-section-form": "Formulär", "smw-search-profile-extended-section-search-syntax": "Sökinmatning", "smw-search-profile-extended-section-namespace": "Namnrymd", "smw-search-profile-extended-section-query": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-search-profile-link-caption-query": "se", "smw-search-show": "Visa", "smw-search-hide": "<PERSON><PERSON><PERSON><PERSON>", "log-name-smw": "Semantic MediaWiki-logg", "log-show-hide-smw": "$1 Semantic MediaWiki-logg", "logeventslist-smw-log": "Semantic MediaWiki-logg", "log-description-smw": "Aktiviteter för [https://www.semantic-mediawiki.org/wiki/Help:Logging aktiverade händelstyper] som har rapporterats av Semantic MediaWiki och dess underkomponenter.", "logentry-smw-maintenance": "Underhållsrelaterade händelser som utelämnas av Semantic MediaWiki", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1 importering]]", "smw-datavalue-import-invalid-value": "\"$1\" är inte ett giltig format och förväntas vara \"namnrymd\":\"identifierare\" (d.v.s \"foaf:namn\").", "smw-datavalue-import-invalid-format": "Förväntade att strängen \"$1\" skulle delas upp i fyra delar men formatet uppfattades inte.", "smw-property-predefined-impo": "\"$1\" är en fördefinierade egenskap som beskriver ett förhållande till en [https://www.semantic-mediawiki.org/wiki/Help:Import_vocabulary importerad ordlista] och tillhandahålls av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-property-predefined-type": "\"$1\" är en fördefinierad egenskap som beskriver [[Special:Types|datatypen]] av en egenskap och tillhandahålls av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki].", "smw-datavalue-property-invalid-character": "\"$1\" innehåller tecknet \"$2\" som del av en egenskapsetikett och har klassificerats som  ogiltig.", "smw-datavalue-property-invalid-chain": "\"$1\" tillåts inte användas som en egenskapskedja under kommentarsprocessen.", "smw-datavalue-restricted-use": "Datavärdet \"$1\" har märkts för begränsad användning.", "smw-datavalue-invalid-number": "\"$1\" kan inte tolkas som ett nummer.", "smw-types-list": "Lista över datatyper", "smw-types-default": "\"$1\" är en inbyggd datatyp.", "smw-type-boo": "\"$1\" är en primitiv datatyp för att beskriva ett värde som kan vara sant/falskt.", "smw-type-tab-properties": "Egenskaper", "smw-type-tab-types": "Typer", "smw-type-tab-errors": "<PERSON><PERSON>", "smw-type-primitive": "Grundläggande", "smw-type-container": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-types-extra-mlt-lcode": "Datatypen {{PLURAL:$2|kräver inte}} en språkkod (t.ex. {{PLURAL:$2|ett värde utan en språkkod accepteras inte}}).", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw-limitreport-intext-postproctime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|sekund|sekunder}}", "smw_allows_pattern": "Den här sidan är förväntad att innehålla en lista av referenser (följt av [https://en.wikipedia.org/wiki/Regular_expression regular expressions]) och att bli tillgänglig genom att använda [[Property:Allows pattern|Allows pattern]] egenskap.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\" innehåller några tomma komponenter.", "smw-datavalue-time-invalid-date-components-three": "\"$1\" innehåller mer än tre komponenter.", "smw-datavalue-time-invalid-ampm": "\"$1\" innehåller \"$2\" som timmeselement som är ogiltigt för en 12-timmarskonvention.", "smw-datavalue-external-formatter-invalid-uri": " \"$1\" är en ogiltig URL.", "smw-property-preferred-label-language-combination-exists": "\"$1\" kan inte användas som en föredragen etikett eftersom språket \"$2\" är redan tilldelat till etiketten \"$3\".", "smw-clipboard-copy-link": "Kopiera länk till urklipp", "smw-data-lookup": "Hämtar data...", "smw-no-data-available": "Ingen tillgänglig data.", "smw-property-req-error-list": "Egenskapen innehåller följande fel eller varningar:", "protect-level-smw-pageedit": "Tillåt endast användare med sidredigeringsbehörighet (Semantic MediaWiki)", "smw-edit-protection-auto-update": "Semantic MediaWiki har uppdaterat skyddsstatusen enligt egenskapen `Är redigeringsskyddad`.", "smw-edit-protection-enabled": "Redigeringsskyddad (Semantic MediaWiki)", "smw-patternedit-protection": "Denna sida skyddas och kan endast redigeras av användare med den lämpliga [https://www.semantic-mediawiki.org/wiki/Help:Permissions behörigheten <code>smw-patternedit</code>].", "smw-property-predefined-edip": "\"$1\" är en fördefinierad egenskap tillhandahållen av [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] för att indikera om redigering är skyddad eller inte.", "smw-format-datatable-emptytable": "Ingen data tillgänglig i tabellen", "smw-format-datatable-info": "Visar _START_ till _END_ av _TOTAL_ poster", "smw-format-datatable-infoempty": "Visar 0 till 0 av 0 poster", "smw-format-datatable-infofiltered": "(filt<PERSON><PERSON> frå<PERSON> _MAX_ totala poster)", "smw-format-datatable-infothousands": "&nbsp;", "smw-format-datatable-lengthmenu": "Visar _MENU_ poster", "smw-format-datatable-loadingrecords": "<PERSON><PERSON><PERSON> in...", "smw-format-datatable-processing": "Bearbetar...", "smw-format-datatable-search": "Sök:", "smw-format-datatable-zerorecords": "Inga matchande poster hittades", "smw-format-datatable-first": "<PERSON><PERSON><PERSON><PERSON>", "smw-format-datatable-last": "Sista", "smw-format-datatable-next": "<PERSON><PERSON><PERSON>", "smw-format-datatable-previous": "Föregående", "smw-format-datatable-sortascending": ": aktivera för att sortera kolumnen stigande", "smw-format-datatable-sortdescending": ": aktivera för att sortera kolumnen fallande", "smw-format-datatable-toolbar-export": "Exportera", "smw-category-invalid-redirect-target": "<PERSON><PERSON><PERSON> \"$1\" innehåller en ogiltigt omdirigeringsmål till en namnrymd som inte är en kategori.", "smw-postproc-queryref": "<PERSON><PERSON> markerades för att uppdateras p.g.a. behov av efterbehandling.", "smw-api-invalid-parameters": "Ogiltiga parametrar, \"$1\"", "smw-property-page-list-count": "Visar $1 {{PLURAL:$1|sida|sidor}} som använder den här egenskapen.", "smw-property-page-list-search-count": "Visar $1 {{PLURAL:$1|sida|sidor}} som använder den här egenskapen med det matchande värdet \"$2\".", "smw-property-reserved-category": "<PERSON><PERSON><PERSON>", "smw-category": "<PERSON><PERSON><PERSON>", "smw-browse-property-group-title": "Egenskapsgrupp", "smw-browse-property-group-label": "Etikett för egenskapsgrupp", "smw-browse-property-group-description": "Beskrivning för egenskapsgrupp", "smw-filter": "Filter", "smw-section-expand": "Expandera avsnittet", "smw-section-collapse": "Stäng avsnittet", "smw-ask-format-help-link": "Formatet [https://www.semantic-mediawiki.org/wiki/Help:$1_format $1]", "smw-help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-cheat-sheet": "<PERSON><PERSON><PERSON>", "smw-personal-jobqueue-watchlist": "Bevakningslista (jobbkö)", "smw-property-predefined-label-skey": "Sorteringsnyckel", "smw-processing": "Bearbetar...", "smw-jsonview-search-label": "Sök:", "smw-types-title": "Typ: $1", "smw-schema-error-title": "Validerings{{PLURAL:$1|fel}}", "smw-schema-validation-schema-title": "JSON-schema", "smw-schema-summary-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-schema-title": "<PERSON><PERSON><PERSON>", "smw-schema-usage": "Anv<PERSON><PERSON><PERSON>", "smw-schema-type": "Schematyp", "smw-schema-tag": "{{PLURAL:$1|<PERSON><PERSON>rke|Märken}}", "smw-ask-title-keyword-type": "Nyckelordssökning", "smw-ask-message-keyword-type": "Denna sökning matchar villkoret <code><nowiki>$1</nowiki></code>.", "smw-parameter-missing": "Parametern \"$1\" saknas.", "smw-property-tab-usage": "Anv<PERSON><PERSON><PERSON>", "smw-property-tab-redirects": "Synonymer", "smw-property-tab-subproperties": "Underegenskaper", "smw-property-tab-errors": "Felak<PERSON>ga <PERSON>del<PERSON>", "smw-concept-tab-list": "Lista", "smw-concept-tab-errors": "<PERSON><PERSON>", "smw-ask-tab-result": "Resultat", "smw-ask-tab-extra": "Extra", "smw-ask-tab-debug": "Felsökning", "smw-ask-tab-code": "Kod", "smw-pendingtasks-setup-tasks": "Uppgifter", "smw-report": "Rapportera", "smw-legend": "Teckenförklaring", "smw-listingcontinuesabbrev": "forts.", "smw-showingresults": "Nedan visas upp till {{PLURAL:$1|<strong>1</strong> resultat|<strong>$1</strong> resultat}} fr<PERSON>n och med nummer <strong>$2</strong>."}