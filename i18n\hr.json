{"@metadata": {"authors": ["Bugoslav", "<PERSON><PERSON><PERSON>", "Ex13", "Kghbln", "<PERSON>, the Mystic", "<PERSON><PERSON>", "Sociologist", "SpeedyGonsales", "<PERSON><PERSON><PERSON>", "아라"]}, "smw-desc": "Čini Vaš wiki dostupnijim - za strojeve ''i'' ljude ([https://www.semantic-mediawiki.org/wiki/Help:User_manual dokumentacija])", "smw-error": "Pogreška", "smw-upgrade-release": "Verzija", "smw-upgrade-progress": "Napredak", "smw-upgrade-progress-create-tables": "Stvaranje (ili ažuriranje) tablica i indeksa ...", "smw-upgrade-progress-post-creation": "Pokretanje zadaća nakon kreiranja ...", "smw-upgrade-progress-table-optimization": "Pokretanje optimizacija tablica ...", "smw-upgrade-progress-supplement-jobs": "Dodavanje naknadnih poslova ...", "smw-upgrade-error-title": "Pogreška » Semantic MediaWiki", "smw-upgrade-error-why-title": "Za<PERSON>to mi se prikazuje ova stranica?", "smw-upgrade-error-how-title": "<PERSON><PERSON> ovu pogreš<PERSON>?", "smw-extensionload-error-how-title": "<PERSON><PERSON> ovu pogreš<PERSON>?", "smw-semantics-not-enabled": "Funkcionalnost Semantičkoga MediaWikija nije omogućena na ovoj wiki.", "smw_viewasrdf": "RDF feed", "smw_finallistconjunct": ", i", "smw_isspecprop": "Ovo svojstvo je posebno svojstvo na ovom wikiju.", "smw-concept-cache-header": "Upotreba predmemorije", "smw_concept_description": "Opis koncepta \"$1\"", "smw_no_concept_namespace": "Koncepti mogu biti definirani jedino u imenskom prostoru Concept: .", "smw_multiple_concepts": "Stranica koncepta smije imati samo jednu definiciju koncepta.", "smw_concept_cache_miss": "Koncept \"$1\" se trenutačno ne može se koristiti jer je wiki podešen tako da se koncept mora proračunati i izraditi off-line. Ako problem ne nestane nakon nekog vremena, zamolite administratora da ovaj koncept učini dostupnim.", "smw_noinvannot": "Vrijednosti se ne mogu dodijeliti inverznim svojstva.", "version-semantic": "Semantičke ekstenzije", "smw_baduri": "URI oblika \"$1\" nije dozvoljen.", "smw_printername_count": "<PERSON><PERSON>ji rezultate", "smw_printername_csv": "Izvoz u CSV", "smw_printername_debug": "Debugi<PERSON><PERSON> (za stručnjake)", "smw_printername_embedded": "Umetni sadržaj stranice", "smw_printername_json": "Izvoz u JSON", "smw_printername_list": "<PERSON><PERSON>", "smw_printername_ol": "<PERSON><PERSON><PERSON><PERSON> popis", "smw_printername_ul": "Popis s grafi<PERSON><PERSON>", "smw_printername_table": "Tablica", "smw_printername_broadtable": "Široka tablica", "smw_printername_template": "Predložak", "smw_printername_category": "Kategorija", "validator-type-class-SMWParamSource": "tekst", "smw-paramdesc-limit": "Maksimalni broj rezultata", "smw-paramdesc-headers": "Prikaz zaglavlja/imena svo<PERSON>", "smw-paramdesc-mainlabel": "Oznaka imenu glavne stranice", "smw-paramdesc-link": "Prikaži vrijednosti kao poveznice", "smw-paramdesc-intro": "Tekst za prikazati prije rezultata upita, ukoliko ih ima", "smw-paramdesc-outro": "Tekst za prikazati nakon rezultata upita, ukoliko ih ima", "smw-paramdesc-default": "Tekst koji će se prikazati ako nema rezultata upita", "smw-paramdesc-sep": "Znak za razdvajanje vrijednosti", "smw-paramdesc-template": "Naziv predloška kojim će se prikazati ispis", "smw-paramdesc-columns": "Broj stupaca u kojima se prikazuje rezultat (default je $1)", "smw-paramdesc-userparam": "Vrijednost koja se prenosi u svaki poziv predloška, ako se predložak koristi", "smw-paramdesc-introtemplate": "<PERSON><PERSON> pre<PERSON> koji će se prikazati prije rezultata upita, ako ih ima", "smw-paramdesc-outrotemplate": "<PERSON><PERSON> pre<PERSON> koji će se prikazati nakon rezultata upita, ako ih ima", "smw-paramdesc-embedformat": "HTML tag koji definira zaglavlja", "smw-paramdesc-embedonly": "Ne prikazuj zaglavlja", "smw-paramdesc-searchlabel": "Tekst poveznice na rezultate", "smw_iq_disabled": "Semantički upiti su isključeni na ovom wikiju.", "smw_iq_moreresults": "… da<PERSON><PERSON><PERSON> re<PERSON>", "smw_parseerror": "Dana vrijednost nije razumljiva.", "smw_notitle": "\"$1\" ne može biti ime stranice na ovom wikiju.", "smw_wrong_namespace": "U ovom su slučaju dopuštene samo stranice iz imenskoga prostora \"$1\".", "smw_manytypes": "Za svojstvo je definirano više od jednog tipa.", "smw_emptystring": "Prazni se nizovi ne prihvaćaju.", "smw_notinenum": "\"$1\" nije na popisu dozvoljenih vrijednosti ($2) za ovo svojstvo.", "smw_noboolean": "\"$1\" nije prepoznat kao booleovska vrijednost (\"true/false\" odn. istina/laž).", "smw_true_words": "true,t,yes,y,is<PERSON>,i,da,d", "smw_false_words": "false,f,no,n,laž,l,ne", "smw_nofloat": "\"$1\" nije broj.", "smw_infinite": "Veliki brojevi poput \"$1\" nisu podržani.", "smw_novalues": "Nisu naznačene vrijednosti.", "smw_nodatetime": "Datum \"$1\" nije prepoznat.", "smw_toomanyclosing": "<PERSON><PERSON> se da postoji previše pojava \"$1\" u upitu.", "smw_noclosingbrackets": "Jedna od \"<nowiki>[[</nowiki>\" u Vašem upitu nije zatvorena odgovarajućom \"]]\".", "smw_misplacedsymbol": "Simbol \"$1\" se javlja na mjestu gdje nije upotrebljiv.", "smw_unexpectedpart": "<PERSON><PERSON> <PERSON>ita \"$1\" nije razumljiv.\nRezultati možda neće biti odgovarajući.", "smw_emptysubquery": "Neki podupiti nemaju valjani uvjet.", "smw_misplacedsubquery": "Podupit je postavljen na nedopuštenom mjestu.", "smw_valuesubquery": "Podupiti ne mogu biti vrijednosti svojstva \"$1\".", "smw_badqueryatom": "<PERSON><PERSON> od di<PERSON>ita \"<nowiki>[[…]]</nowiki>\" nije razumljiv.", "smw_propvalueproblem": "Vrijednost svojstva \"$1\" nije razumljiva.", "smw_noqueryfeature": "Ovaj wiki ne podržava neku od osobina upita, pa je dio upita odbačen ($1).", "smw_noconjunctions": "Ovaj wiki ne podržava veznike u upitima, pa je dio upita odbačen ($1).", "smw_nodisjunctions": "Ovaj wiki ne podržava razdvajanja u upitima, pa je dio upita odbačen ($1).", "smw_querytoolarge": "Zbog ograničenja veličine ili dubine upita na ovom wikiju, sljedeći uvjeti upita nisu uzeti u obzir: $1.", "smw_notemplategiven": "Navedite vrijednost parametra \"template\" (\"predložak\") za ovaj format upita.", "smw_type_header": "Svojstva tipa \"$1\".", "smw_typearticlecount": "{{PLURAL:$1|Prikazano je $1 svojstvo koje koristi|Prikazana su $1 svojstva koja koriste|Prikazano je $1 svojstava koja koriste}} ovaj tip.", "smw_attribute_header": "Stranice koje koriste svojstvo \"$1\".", "smw_attributearticlecount": "Prikazano {{PLURAL:$1|$1 stranica koja koristi|$1 stranice koje koriste|$1 stranica koje koriste}} ovo svojstvo.", "specialpages-group-smw_group": "Semantički MediaWiki", "exportrdf": "Izvezi stranice u RDF", "smw_exportrdf_docu": "Na ovoj stranici možete dobiti podatke o nekoj stranici u RDF formatu.\nDa biste izvezli stranice unesite njihove naslove u prozor za uređivanje ispod, jedan naslov po retku.", "smw_exportrdf_recursive": "Rekurzivno izvezi sve srodne stranice.\nImajte na umu da bi rezultat mogao biti velik!", "smw_exportrdf_backlinks": "Također izvezi sve stranice koje se referenciraju na izvezene stranice.\nGenerira RDF za pregled i pretraživanje.", "smw_exportrdf_lastdate": "Ne izvozi stranice koje nisu izmijenjene od navedenog datuma.", "smw_exportrdf_submit": "Izvezi", "uriresolver": "URI razrješitelj", "properties": "Svojstva", "smw-categories": "Kategorije stranica", "smw_properties_docu": "U wikiju se koriste sljedeća svojstva.", "smw_property_template": "$1 tipa $2 ($3)", "smw_propertylackspage": "Svako svojstvo mora biti opisano na svojoj stranici!", "smw_propertylackstype": "Za ovo svojstvo nije naveden tip (od sad na dalje pretpostavljam tip $1).", "smw_propertyhardlyused": "Ovo se svojstvo gotovo ne koristi u wikiji!", "concepts": "Pojmovnici", "unusedproperties": "Nekorištena svojstva", "smw-unusedproperties-docu": "Sljedeća svojstva postoje, no ne koristi ih ni jedna stranica.", "smw-unusedproperty-template": "$1 tipa $2", "wantedproperties": "Tražena svojstva", "smw-wantedproperties-docu": "Sljedeća svojstva se koriste u wikiju, no još nemaju svoje stranice koje bi ih opisale.", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|korištenje|korištenja}})", "smw_purge": "Osvježi", "types": "Tipovi", "smw_types_docu": "Slijedi popis svih tipova podataka koji mogu biti dodijeljeni svojstvima.\nSvaki tip podatka ima stranicu koja može pružiti dodatne informacije.", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Pojmovnik|Pojmovnika|Pojmovnici}}]]", "smw_uri_doc": "URI razrješitelj implementira [$1 W3C traženje TAGova na httpRange-14]\nkoje brine da se ljudi ne pretvore u web stranice.", "ask": "Semantička tražilica", "smw_ask_sortby": "Razvrstavanje po stupcu (opcionalno)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_descorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_ask_submit": "<PERSON><PERSON><PERSON> rezultate", "smw_ask_editquery": "<PERSON><PERSON><PERSON> upit", "smw_add_sortcondition": "[Dodaj uvjet za razvrstavanje]", "smw_ask_hidequery": "<PERSON><PERSON><PERSON><PERSON> upit", "smw_ask_help": "<PERSON><PERSON>ć za upite", "smw_ask_queryhead": "Upit", "smw_ask_printhead": "Dodatni podaci za prikazati", "smw_ask_printdesc": "(dodaj jedno svojstvo po retku)", "smw_ask_format_as": "Formatiraj kao:", "smw_ask_defaultformat": "prvotno", "smw_ask_otheroptions": "Ostale opcije", "smw_ask_show_embed": "Prikaži kôd za umetanje", "smw_ask_hide_embed": "<PERSON><PERSON><PERSON><PERSON> kôd za umetanje", "smw_ask_embed_instr": "Da biste umetnuli ovaj upit u stranicu koristite kôd dolje.", "searchbyproperty": "Traži po svojstvu", "processingerrorlist": "Popis pogrešaka pri obradi", "propertylabelsimilarity": "Izvješće o sličnosti naljepnice svojstva", "smw_sbv_docu": "Traži sve stranice koje imaju zadano svojstvo i vrijednost.", "smw_sbv_novalue": "Unesite valjanu vrijednost za svojstvo ili pogledajte sve vrijednosti za \"$1\".", "smw_sbv_displayresultfuzzy": "Popis svih stranica koje imaju svojstvo \"$1\" vrijednosti \"$2\".\n<PERSON><PERSON><PERSON><PERSON> da je rezultata malo, takođ<PERSON> su prikazane i bliske vrijednosti.", "smw_sbv_property": "Svojstvo:", "smw_sbv_value": "Vrijednost:", "smw_sbv_submit": "<PERSON><PERSON><PERSON> rezultate", "browse": "Pretražuj wiki", "smw_browselink": "Pregledaj svojstva", "smw_browse_article": "Unesite ime stranice od koje ćete početi pregledavanje.", "smw_browse_go": "<PERSON><PERSON><PERSON>", "smw_browse_show_incoming": "prikaži svojstva koja povezuju ovamo", "smw_browse_hide_incoming": "sakrij svojstva koja povezuju ovamo", "smw_browse_no_outgoing": "Ova stranica nema svojstva.", "smw_browse_no_incoming": "Nijedno svojstvo ne povezuje na ovu stranicu.", "smw_inverse_label_default": "$1 od", "smw_inverse_label_property": "Oznaka inverznog svojstva", "pageproperty": "Pretraživanje svojstava stranica", "pendingtasklist": "Popis zadataka na čekanju", "smw_pp_docu": "Pretraži sve vrijednosti nekog svojstva na zadanoj stranici.\nUnesite stranicu i svojstvo.", "smw_pp_from": "Sa stranice", "smw_pp_type": "Svojstvo", "smw_pp_submit": "<PERSON><PERSON><PERSON> rezultate", "smw_result_prev": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_next": "Sljedeći", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "<PERSON><PERSON> rezultata.", "smwadmin": "Administratorske funkcije za Semantic MediaWiki", "smw-admin-setupsuccess": "Spremište je uspješno postavljeno.", "smw_smwadmin_return": "Vrati se na $1", "smw_smwadmin_updatestarted": "Pokrenut je novi proces osvježavanja semantičkih podataka.\nSvi spremljeni podaci će biti ponovno izgrađeni ili popravljeni ako je to potrebno.\nNapredak osvježavanja možete pratiti na ovoj posebnoj stranici.", "smw_smwadmin_updatenotstarted": "Već se izvršava proces osvježavanja.\nNe pokrećem novi.", "smw_smwadmin_updatestopped": "Svi postojeći procesi osvježavanja su zaustavljeni.", "smw_smwadmin_updatenotstopped": "Da biste zaustavili proces osvježavan<PERSON>, označite u potvrdnom okviru da ste zaista sigurni.", "smw-admin-docu": "Ova posebna stranica pomaže prilikom instalacije i nadogradnje <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a>.\nNe zaboravite napraviti sigurnosnu kopiju važnih podataka prije izvršavanja administrativnih funkcija.", "smw-admin-db": "Instalacija i nadgradnja baze podataka", "smw-admin-dbdocu": "Semantic MediaWiki zahtijeva određena proširenja MediaWiki baze podataka za čuvanje semantičkih podataka.\nFunkcija ispod osigurava da je vaša baza pravilno postavljena.\nOvaj korak ne utječe na ostatak MediaWiki baze podataka i lako se može poništiti ukoliko to želite.\nOva se funkcija može izvršiti više puta bez ikakvih štetnih posljedica, no potrebna je samo kod instalacije ili nadgradnje.", "smw-admin-permissionswarn": "Ako operacija ne uspije i vrati SQL grešku, baza podataka koju koristi Vaš wiki (provjerite svoj LocalSettings.php) vjerojatno nema dovoljna prava.\nUčinite jedno od sljedećeg: dodijelite ovom korisniku dodatna prava za stvaranje i brisanje tablica, privremeno unesite administratorsko korisničko ime i lozinku Vaše baze podataka u LocalSettings.php, ili pokrenite skriptu za održavanje <code>setupStore.php</code> koja će koristiti prava iz AdminSettings.php.", "smw-admin-dbbutton": "Inicijaliziraj ili nadogradi tablice", "smw-admin-announce": "Najavite svoj wiki", "smw-admin-deprecation-notice-title-notice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw-admin-deprecation-notice-section-legend": "<PERSON>a", "smw_smwadmin_datarefresh": "Popravljanje i nadgradnja podataka", "smw_smwadmin_datarefreshdocu": "Moguće je povratiti sve semantičke podatke Semantic MediaWiki na osnovi trenutnog tekstualnog wikija, što možete koristiti za popravak pokvarenih podataka ili osvježivanje podataka u slučaju internih promjena softvera.\nOsvježavanje se izvršava stranicu po stranicu i neće biti gotovo odmah.\nPrikaz dolje sadrži popis svih osvježavanja u tijeku i omogućuje Vam da zaustavite ili pokrenete osvježavanje (osim ako to nije onemogućio administrator).", "smw_smwadmin_datarefreshprogress": "<strong>Osvježavanje je već u tijeku.</strong>\nNormalno je da osvježavanje napreduje sporo jer se podaci osvježavaju u malim obrocima svaki put kad korisnik pristupi wikiju.\nDa bi ovo osvježavanje završilo čim prije, možete pokrenuti MediaWiki skriptu za održavanje <code>runJobs.php</code> (koristite opciju <code>--maxjobs 1000</code> kako biste ograničili broj zasebnih osvježavanja po jednoj seriji).\nProcjena napretka osvježavanja:", "smw_smwadmin_datarefreshbutton": "Pokreni osvježavanje podataka", "smw_smwadmin_datarefreshstop": "Zaustavi ovo osvježavanje", "smw_smwadmin_datarefreshstopconfirm": "<PERSON>, {{GENDER:$1|siguran|sigurna}} sam.", "smw-admin-support": "Zatražite pomoć", "smw-admin-supportdocu": "U slučaju problema poslužite se sljedećim izvorima:", "smw-admin-installfile": "U slučaju problema s Vašom instalacijom pogledajte naputke u datoteci <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">INSTALL</a>.", "smw-admin-smwhomepage": "Potpuna korisnička dokumentacija za Semantic MediaWiki nalazi se na <a href=\"http://semantic-mediawiki.org\"><b>semantic-mediawiki.org</b></a>.", "smw-admin-bugsreport": "<PERSON><PERSON><PERSON><PERSON> (bugove) možete prijaviti na <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a>.", "smw-admin-questions": "Ukoliko imate pitanja ili prijedl<PERSON>e, prid<PERSON>žite se raspravi na <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">korisničkom forumu Semantic MediaWiki</a>.", "smw-admin-supplementary-settings-title": "Konfiguracija i postavke", "smw-admin-supplementary-elastic-version-info": "Inačica", "smw-admin-supplementary-elastic-config": "Konfiguracije", "smw_adminlinks_datastructure": "Struktura podataka", "smw_adminlinks_displayingdata": "Prikaz podataka", "smw_adminlinks_inlinequerieshelp": "Po<PERSON>ć za umetnute upite", "prefs-smw": "Semantički MediaWiki", "prefs-general-options": "<PERSON><PERSON><PERSON> m<PERSON>", "prefs-ask-options": "Semantička tražilica", "smw-ui-tooltip-title-info": "Obavijest", "smw_unknowntype": "Tip ovog svojstva je neispravan", "smw_concept_header": "<PERSON><PERSON><PERSON> koncepta \"$1\"", "smw_conceptarticlecount": "Prikazano $1 {{PLURAL:$1|stranica koja pripada|stranice koje pripadaju|stranica koje pripadaju}} tom konceptu.", "right-smw-vieweditpageinfo": "Pregled [https://www.semantic-mediawiki.org/wiki/Help:Edit_help uređivanja pomoći] (Semantički MediaWiki)", "group-smwadministrator": "Administratori (Semantički MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|administrator (Semantički MediaWiki)|administratorica (Semantički MediaWiki)}}", "group-smwcurator": "Pomnjici (Semantički MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|pomnjik (Semantički MediaWiki)|pomnjikinja (Semantički MediaWiki)}}", "group-smweditor": "Uređivači/ce (Semantički MediaWiki)", "group-smweditor-member": "{{GENDER:$1|<PERSON><PERSON><PERSON><PERSON><PERSON> (Semantički MediaWiki)|u<PERSON>đ<PERSON>č<PERSON> (Semantički MediaWiki)}}", "grouppage-smweditor": "{{ns:project}}:Uređivači/ce (Semantički MediaWiki)", "smw-admin-tab-alerts": "Obavijesti", "smw-livepreview-loading": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "log-name-smw": "Evidencija Semantičkoga MediaWikija", "log-show-hide-smw": "$1 evidenciju Semantičkoga MediaWikija", "logeventslist-smw-log": "Evidencija Semantičkoga MediaWikija", "smw-type-tab-errors": "Pog<PERSON><PERSON><PERSON>", "smw-type-primitive": "<PERSON><PERSON><PERSON><PERSON>", "smw-install-incomplete-intro-note": "Ova će se poruka prestati prikazivati nakon što svi značajni zadaci budu riješeni.", "smw-pendingtasks-setup-tasks": "<PERSON><PERSON><PERSON>", "smw-listingcontinuesabbrev": "nast.", "smw-showingresults": "Do<PERSON><PERSON> {{PLURAL:$1|je prikazan '''$1''' rezultat|su prikazana '''$1''' rezultata|je prikazano '''$1''' rezultata}}, po<PERSON><PERSON><PERSON><PERSON> od '''$2'''."}