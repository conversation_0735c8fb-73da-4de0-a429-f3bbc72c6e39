name: <PERSON><PERSON>

on:
  push:
    paths:
      - .github/workflows/tesa.yml
      - Tesa/**
      - composer.json

jobs:
  phpunit:
    name: "PHPUnit PHP ${{ matrix.php }}"

    strategy:
      matrix:
        php: [ 8.1, 8.2, 8.3, 8.4 ]

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: mbstring, intl
          tools: composer, cs2pr
          coverage: pcov

      - name: Cache Composer cache
        uses: actions/cache@v4
        with:
          path: ~/.composer/cache
          key: composer-cache-php${{ matrix.php }}

      - name: Composer install
        run: composer install --no-progress --no-interaction --prefer-dist --optimize-autoloader

      - name: Add PHPUnit
        run: composer require phpunit/phpunit:^9

      - name: PHPUnit
        run: vendor/bin/phpunit -c Tesa/phpunit.xml.dist
