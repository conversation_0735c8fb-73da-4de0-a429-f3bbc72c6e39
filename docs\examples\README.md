
## Examples and code snippets

* [register.core.datatype.md](register.core.datatype.md) Shows how to register a new dataType/dataValue
* [register.custom.datatype.md](register.custom.datatype.md) Shows how to register a new dataType/dataValue using the the `SMW::DataType::initTypes` hook
* [approve.update.md](approve.update.md) Shows how to alter the data representation in Semantic MediaWiki with the help of selected hooks in connection with the `ApprovedRevs` extension
* [hook.pagecontentsavecomplete.md](hook.pagecontentsavecomplete.md) Creating subobjects using the `PageContentSaveComplete` hook (see [#2974](https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues/2974))
* [hook.store.afterqueryresultlookupcomplete.md](hook.store.afterqueryresultlookupcomplete.md) Extending the query result