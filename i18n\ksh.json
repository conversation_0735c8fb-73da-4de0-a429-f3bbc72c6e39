{"@metadata": {"authors": ["Kghbln", "<PERSON><PERSON><PERSON>", "아라"]}, "smw-desc": "<PERSON><PERSON>ät Ding Wikki bäßer ze bruche - för Minsche un Maschihne. ([https://www.semantic-mediawiki.org/wiki/Help:User_manual Handbohch])", "smw_viewasrdf": "RDF Kannaal", "smw_finallistconjunct": ", un", "smw_isspecprop": "Di Eijeschaff es en schpezjälle Eijeschaff en heh dämm Wikki.", "smw_concept_description": "Beschrievong för et Kunzäp: „$1“", "smw_no_concept_namespace": "<PERSON><PERSON><PERSON><PERSON><PERSON> kam_mer bloß em {{ns:concept}}-Appachtemang faßlääje.", "smw_multiple_concepts": "Op jeede {{ns:concept}}-<PERSON>gg kann bloß ei Kunzäp faßjelaat wääde.", "smw_concept_cache_miss": "<PERSON><PERSON> „$1“ kann för der Momang noch nit jebruch wääde.\nDem Wiki sing Kumfijuraz<PERSON><PERSON> fordert, dat dat offline ußjeräshent weed.\nWann dat nit noh en jeweße Zick nit fun sellfs jedonn es, un hee di\nAanforderung dorschjeiht, dann froch Dinge Wiki-<PERSON><PERSON><PERSON> donoh, noh dämm Kunzäp.", "smw_noinvannot": "<PERSON><PERSON><PERSON> met ömjedriehte Reschtung künne kei Wääte verjovve wääde.", "version-semantic": "Projrammzohsäz vum „Semantesch MehdijaWikki“", "smw_baduri": "URIs met dä<PERSON> Opbou „$1“ sin nit zojelohße.", "smw_csv_link": "<i lang=\"en\">CSV</i>", "smw_dsv_link": "<i lang=\"en\">DSV</i>-<PERSON><PERSON><PERSON>", "smw_json_link": "<i lang=\"en\">JSON</i>", "smw_rdf_link": "<i lang=\"en\">RDF</i>", "smw_printername_count": "<PERSON><PERSON><PERSON>, wat erus kütt", "smw_printername_csv": "Expoot em <i lang=\"en\">CSV</i>-Fommaat", "smw_printername_dsv": "Äxpoot als en <i lang=\"en\">DSV</i>-<PERSON><PERSON><PERSON>", "smw_printername_debug": "En dä <PERSON>ch noh F<PERSON>hler<PERSON> sö<PERSON>ke (joot för de Expächte)", "smw_printername_embedded": "Sigge-Enhallt enföhje", "smw_printername_json": "Expoot em <i lang=\"en\">JSON</i>-Fommaat", "smw_printername_list": "<PERSON><PERSON>", "smw_printername_ol": "<PERSON>z<PERSON><PERSON>", "smw_printername_ul": "<PERSON><PERSON> met Einzelheite opföhre", "smw_printername_table": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_broadtable": "En breed Tabäll", "smw_printername_template": "<PERSON><PERSON><PERSON><PERSON>", "smw_printername_rdf": "Äxpoot als en <i lang=\"en\">RDF</i>-<PERSON><PERSON><PERSON>", "smw-paramdesc-limit": "De hühßte Aanzahl vun Äjeebnesse för zeröck ze jävve", "smw-paramdesc-headers": "Donn der Name vun de Övveschrefte un Eijeschaffte aanzeije", "smw-paramdesc-mainlabel": "Wi dä <code>mainlabel</code> Parrameeter för inlain Affroore beschrevve weed", "smw-paramdesc-link": "<PERSON><PERSON> Wääte als Lengks aanzeije", "smw-paramdesc-intro": "Wat för ene Täx aanjezeijsch wääde sull, für dämm, wat jefonge woodt", "smw-paramdesc-outro": "Wat för ene Täx aanjezeijsch wääde sull, noh dämm, wat jefonge woodt", "smw-paramdesc-default": "Wat för ene Täx aanjezeijsch wääde sull, wann nix jefonge woodt", "smw-paramdesc-sep": "Wat zwesche de Wääte schtonn sull", "smw-paramdesc-template": "<PERSON><PERSON> <PERSON> vun en<PERSON>, <PERSON><PERSON><PERSON> <PERSON> met a<PERSON><PERSON><PERSON><PERSON>", "smw-paramdesc-columns": "<PERSON> A<PERSON>hl Schpallde för a<PERSON>, wad eruß kütt — der Schtandatt wöhr $1", "smw-paramdesc-userparam": "<PERSON><PERSON>, dä aan jehde <PERSON> wigger_j<PERSON><PERSON><PERSON> weed, wann övverhoub en Schablohn jebruch weed", "smw-paramdesc-introtemplate": "Dä <PERSON> vun en Schabloon, di aanjezei<PERSON><PERSON> wääde sull, für dämm, wat jefonge woodt", "smw-paramdesc-outrotemplate": "<PERSON><PERSON> Nah<PERSON> vun en <PERSON>, di aanjeze<PERSON><PERSON><PERSON> wähde sull, noh dämm, wat jefonge wohdt", "smw-paramdesc-embedformat": "De <i lang=\"en\">html</i>-Be<PERSON><PERSON><PERSON><PERSON> för de <PERSON>v<PERSON>chreffte ze makeere", "smw-paramdesc-embedonly": "<PERSON><PERSON> aanzei<PERSON>", "smw-paramdesc-rdfsyntax": "De Zoot Opbou (Syntax) vun dä <i lang=\"en\">RDF</i>-<PERSON><PERSON><PERSON>", "smw-paramdesc-csv-sep": "<PERSON>t Z<PERSON> för <PERSON> ze tränne", "smw-paramdesc-dsv-separator": "<PERSON>t Z<PERSON> för <PERSON> ze tränne", "smw-paramdesc-dsv-filename": "<PERSON><PERSON> <PERSON> för di <i lang=\"en\">DSV</i>-<PERSON><PERSON><PERSON>", "smw-paramdesc-searchlabel": "Dä Täx för em Lengk för wat eruß kütt", "smw_iq_disabled": "Semantesche Frore sem em Wiki em Momang afjeschaldt.", "smw_iq_moreresults": "…&nbsp;mih vun däm, wat jefonge woodt", "smw_parseerror": "Dä aanjejovve Wäät kunnte mer nit vershtonn.", "smw_decseparator": ",", "smw_kiloseparator": "&nbsp;", "smw_notitle": "„$1“ kam_mer en hee däm Wiki nit als ene Siggename bruche.", "smw_wrong_namespace": "<PERSON><PERSON>ß Sigge uß däm Appachtemang „$1“ sin hee zohjelo<PERSON>ße.", "smw_manytypes": "<PERSON> Eijeschaff es en mih wi ein <PERSON> dren.", "smw_emptystring": "De jonn kei Reihe uß Boochstabe un Zeishe, woh nit ens ei dofun dren shteiht. <PERSON><PERSON> nit leddesch lohße.", "smw_notinenum": "„$1“ es nit bei dä zohjeloße Wääte för di Eijeschaff, dat wöhre: $2", "smw_noboolean": "„$1“ es keine vun dä Wääte för „wohr“ un „verkee<PERSON>“.", "smw_true_words": "wohr,wor,woo,woh,wo,w,j,jo,joh,true,t,yes,y", "smw_false_words": "verkiehrt,verkihrt,verkirt,verkeht,verkeet,verket,vokiehrt,vokihrt,vokirt,vokeht,vokeet,voket,ferkiehrt,ferkihrt,ferkirt,ferkeht,ferkeet,ferket,fokiehrt,fokihrt,fokirt,fokeht,fokeet,foket,fallsch,falsch,näh,nää,nä,false,f,no,n", "smw_nofloat": "„$1“ es kei Zahl.", "smw_infinite": "Zahle esu jruuß udder esu lang wi „$1“ dom_mer hee nit ongershtöze.", "smw_unitnotallowed": "„$1“ es nit als en jölteje Mohßeinheit för heh di Eijeschaff enjedraare.", "smw_novalues": "Et sinn_er kein Wääte aanjejovvwe.", "smw_nodatetime": "<PERSON><PERSON> „$1“ künne mer nit vershtonn.", "smw_toomanyclosing": "En dä Frooch es zoh öff „$1“ enthallde.", "smw_noclosingbrackets": "En <code><nowiki>[[</nowiki></code> en Dinge Fr<PERSON>ch woh nit zoh<PERSON>, un hät kei <code>]]</code> wat drop paßße deiht.", "smw_misplacedsymbol": "<PERSON><PERSON> „$1“ wohr aan ene <PERSON>, woh et nix nöz.", "smw_unexpectedpart": "Dä Aandeil „$1“ en dä Frooch kunnte mer nit vershtonn. Wat eruß kütt, künnt jet angersch sin, wi jedaach.", "smw_emptysubquery": "En Ongerfrooch hät kein reschtejje <PERSON>.", "smw_misplacedsubquery": "En Ongerfrooch douch aan ene <PERSON>htäll op, woh kei Ongerfroore sin dörve.", "smw_valuesubquery": "En Ongerfrooch noh de Wääte fun dä Eijeschaff „$1“ künne mer nit.", "smw_badqueryatom": "<PERSON><PERSON> Aandeil „<nowiki>[[…]]</nowiki>“ en dä <PERSON>f<PERSON>ch es nit ze vershtonn.", "smw_propvalueproblem": "<PERSON><PERSON> E<PERSON>f „$1“ ier Wäät es nit ze vershtonn.", "smw_noqueryfeature": "Ene Deijl vun dä <PERSON>frooch iere Eijeschaffte sin hee en dämm Wiki nit\nmüj<PERSON>ch, un dä Aandeil ($1) es dröm us dä <PERSON>ch eruß jenumme woode.", "smw_noconjunctions": "<PERSON>n<PERSON>x<PERSON><PERSON><PERSON> (e „un“ en Aanfroore) sin hee en dämm Wiki nit müj<PERSON>ch, un dä A<PERSON>eil ($1) es dröm us dä <PERSON>ch eruß jenumme woode.", "smw_nodisjunctions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (e „of“ un „odder“ en Aanfroore) sin hee en dämm Wiki nit müjje<PERSON>ch, un dä Aandeil ($1) es dröm us dä <PERSON>ch eruß jenumme woode.", "smw_querytoolarge": "De Bedengunge hee noh kunnte mer nit mieh beärbei<PERSON>, weil dem Wiki sing Jrenze för der Ömfang udder de Aanzahl Verschachtelunge vun Datebangk_Froore övverschredde wohre: $1.", "smw_notemplategiven": "Jiv ene Wäät för dä <PERSON>rrammeeter „Schablohn“ aan, domet di <PERSON>at Fr<PERSON>ch jonn kann.", "smw_db_sparqlqueryproblem": "De Antwood op di Aanfrooch kunnt nit vun dä Daatebangk för <i lang=\"en\">SPARQL</i> jehollt wääde.\nHeh dä Fähler künnt wider vörbei jonn, udder ene Fähler em Programm för de Daatebangk opzeije.", "smw_type_header": "Eijeschaffte vun dä Aat „$1“", "smw_typearticlecount": "<PERSON><PERSON> {{PLURAL:$1|Ein Eijeschaff|$1 Eijeschaffte|kei <PERSON>}} met dämm <PERSON>ntüp aan:", "smw_attribute_header": "<PERSON><PERSON><PERSON> met <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> „$1“", "smw_attributearticlecount": "{{PLURAL:$1|Hee kütt ein Sigg|Hee kumme $1 Sigge|Et jitt kei Sigge}} met dä E<PERSON>jeschaff dren{{PLURAL:$1|:|:|.}}", "specialpages-group-smw_group": "<PERSON><PERSON><PERSON><PERSON>", "exportrdf": "Sigge em Fommaat RDF expoteere", "smw_exportrdf_docu": "<PERSON><PERSON> <PERSON>gg mää<PERSON> et mü<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> vun en Sigg em <i lang=\"en\">RDF</i>-Fommaat ze krijje. Öm <PERSON>gge ze Expoteere, doon dänne ier Titte<PERSON>, j<PERSON>er_ein en en Reih fö sesch, onge en dat Täx-Feld erin schri<PERSON>.", "smw_exportrdf_recursive": "Donn all de Sigge expotteere, di domet zosamme hange, un di domet och, un esu wigger. <PERSON><PERSON><PERSON><PERSON>, dat künnt reschtesch vill wääde!", "smw_exportrdf_backlinks": "Donn och all de Sigge expoteere, di op expoteete Sigge verwiese donn, un maach en <i lang=\"en\">RDF</i> <PERSON><PERSON><PERSON>, woh me dren bläddere udder mem <PERSON> draan jonn kann.", "smw_exportrdf_lastdate": "<PERSON>n kein <PERSON> ä<PERSON>pot<PERSON>hr<PERSON>, di noh däm aanjejovve <PERSON>um un dä aanjejovve Uhrzigg nimmih verändert woodte.", "smw_exportrdf_submit": "<PERSON><PERSON>ß <PERSON>!", "uriresolver": "Oplööser för <i lang=\"en\">URIs</i>", "properties": "Eijeschaffte", "smw-categories": "Saachjroppe", "smw_properties_docu": "<PERSON><PERSON> di Eijeschaffte wähde em Wikki jebruch:", "smw_property_template": "$1 fun dä Zoot $2 ({{PLURAL:$3|ei|$3|kei}} mol)", "smw_propertylackspage": "Alle Eijeschaffte sullte op en Sigg beschrevve sin!", "smw_propertylackstype": "<PERSON><PERSON><PERSON> es bes jetz kein oot Dahte aanjejovve, mer nämme för der Momang ens „$1“.", "smw_propertyhardlyused": "<PERSON> Ei<PERSON><PERSON>f wehd em W<PERSON> koum jebruch!", "unusedproperties": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, di nit jebruch wähde", "smw-unusedproperties-docu": "<PERSON><PERSON> di Eijeschaffte jidd_et un han <PERSON>, äv<PERSON> de Eijeschaffte sällver wähde em <PERSON> nörjenswoh jebruch.", "smw-unusedproperty-template": "$1 vun de Aat $2", "wantedproperties": "Nit aanje<PERSON> Eijeschaffte", "smw-wantedproperties-docu": "De Eijeschaffte heh noh wäde em <PERSON><PERSON><PERSON>, ävver se han noch kei <PERSON>, di se beschrievee un äklihjere deiht.", "smw-wantedproperty-template": "$1 ({{PLURAL:$2|eimol|$2 mol|nit}} jebruch)", "smw_purge": "Nöü Aanzeije!", "types": "Date-T<PERSON><PERSON>", "smw_types_docu": "<PERSON><PERSON> kütt en [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes Leß] met all dä <PERSON><PERSON><PERSON>_Zoo<PERSON>, di för <PERSON>fte verjovve wähde künnte.", "smw-statistics": "Schtatißtike övver de semanntesche Dahte", "smw-statistics-property-instance": "{{PLURAL:$1|Wä<PERSON>|Wääte |Wääte}} för <PERSON> (ensjesamp)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|Eijeschaff|Eijeschaffte |Eijeschaffte}}]] (ensjesamp)", "smw-statistics-property-page": "[[Special:Properties|{{PLURAL:$1|Eijeschaff|Eijeschaffte |Eijeschaffte}}]] (di obb en Sigg ennjedrahe sin)", "smw-statistics-property-type": "[[Special:Properties|{{PLURAL:$1|Eijeschaff|Eijeschaffte |Eijeschaffte}}]] (di ene beschtemmpte Zoot vun Dahte han)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|Affrohch|A<PERSON><PERSON><PERSON>e|<PERSON><PERSON>}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|Affrohch|A<PERSON><PERSON>hre| A<PERSON><PERSON>hre}}]]", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|Ko<PERSON>äpp|Konzäppte|Konzäppte }}]]", "smw-statistics-subobject-count": "[[Special:Properties|{{PLURAL:$1|Ongerobjäk|Ongerobjäkte|Ongerobjäkte }}]]", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|<PERSON><PERSON>e|<PERSON><PERSON>|<PERSON><PERSON>}}]]", "smw-statistics-error-count": "{{PLURAL:$1|<PERSON><PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON>|Wäät}} vun ene E<PERSON>f (met [[Property:Has improper value for|kappodde  {{PLURAL:$1|Aanmärkong|Aanmärkonge| Aanmärkonge}}]])", "smw_uri_doc": "Dä <i lang=\"en\">URI resolver</i> hät dä <PERSON>ach <i lang=\"en\">[$1 W3C TAG finding on httpRange-14]</i> opjenumme\n(-: un sorresch esu doför, dat uß Minsche kei Websigge wääde :-)", "ask": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_sortby": "<PERSON><PERSON> de Spallde zotteere (moß ävver nit)", "smw_ask_ascorder": "<PERSON><PERSON><PERSON> eröm z<PERSON>", "smw_ask_descorder": "Röckwääts zotteere", "smw_ask_submit": "<PERSON><PERSON>ß <PERSON>!", "smw_ask_editquery": "<PERSON><PERSON><PERSON>", "smw_add_sortcondition": "[Zoteere dobeidonn]", "smw_ask_hidequery": "Opdrach nit aanzeije", "smw_ask_help": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_queryhead": "<PERSON><PERSON><PERSON>", "smw_ask_printhead": "Zosä<PERSON><PERSON><PERSON><PERSON> för aanzezeije", "smw_ask_printdesc": "(<PERSON>n j<PERSON>e <PERSON> op ein Reih för sesch enjävve)", "smw_ask_format_as": "Fommatteere als:", "smw_ask_defaultformat": "Schtandatt-Fommaat", "smw_ask_otheroptions": "<PERSON><PERSON>", "smw_ask_show_embed": "Donn dä Wiki_Kood zom ennfööje aanzeije", "smw_ask_hide_embed": "Donn dä Wiki_Kood zom Ennfööje vershteishe", "smw_ask_embed_instr": "<PERSON><PERSON> di Fr<PERSON>ch en en Sigg em W<PERSON><PERSON> en<PERSON>, donn dä <PERSON><PERSON>_<PERSON><PERSON> heh dronger en di Sigg enföhje.", "smw-ask-query-search-info": "Op Aanfrohch <code><nowiki>$1</nowiki></code> hät de Dahtebangk („$2“) en {{PLURAL:$3|ener Sekonde|$3 Sekonde|nix aan <PERSON>}} jeantwoot.", "searchbyproperty": "Ö<PERSON>ver de Eijeschaffte söke", "smw_sbv_docu": "Donn noh all dä <PERSON>e s<PERSON>, di en beschtemmpte Eijesch<PERSON> han, un medd_ennem beschtemmpte Wäät för se.", "smw_sbv_novalue": "Donn ene jöltij<PERSON> Wäät för di Eijeschavv aanjävve, udder donn Der all de müjjelesche Wääte för „$1“ aanlohre.", "smw_sbv_displayresultfuzzy": "<PERSON><PERSON> met alle <PERSON><PERSON> met d<PERSON> <PERSON><PERSON><PERSON><PERSON> „$1“, di doh dä <PERSON> „$2“ hät.<br />\n(Weil nur winnisch dovun do sin, dom_mer de ähnlesche Wääte metzeije)", "smw_sbv_property": "Eijeschaff:", "smw_sbv_value": "Wäät:", "smw_sbv_submit": "<PERSON><PERSON>ß <PERSON>!", "browse": "Em Wiki bläddere", "smw_browselink": "Eijeschaffte aanzeije", "smw_browse_article": "<PERSON>ß esu joot, un jif di Övverschreff vun dä <PERSON> aan, wo <PERSON> met däm Bläddere aanfange wells.", "smw_browse_go": "<PERSON><PERSON>ß <PERSON>!", "smw_browse_more": "&nbsp;…", "smw_browse_show_incoming": "donn de <PERSON>schaffte aanzeije, di ene Lengk noh hee han", "smw_browse_hide_incoming": "donn de <PERSON>sch<PERSON><PERSON>e vershteishe, di ene Lengk noh hee han", "smw_browse_no_outgoing": "Di Sigg heh hät kei Eijeschaffte.", "smw_browse_no_incoming": "Mer han kein Eijeschaffte em Wiki, di ene Lengk noh heh han.", "smw_inverse_label_default": "$1 vun", "smw_inverse_label_property": "Dä <PERSON> för <PERSON>, wann dä ier Reschtung ömjedrieht weed", "pageproperty": "Söke noh Eijeschaffte vun <PERSON>e", "smw_pp_docu": "Söhk noh all dä <PERSON>e, di en beschtemmpte Eijeschaff en dä aanjejovve Sigg han. Donn sowohl en Sigg aanjävve als och en Eijeschaff.", "smw_pp_from": "<PERSON><PERSON>", "smw_pp_type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_pp_submit": "<PERSON><PERSON>ß <PERSON>!", "smw_result_prev": "Vörij<PERSON>", "smw_result_next": "<PERSON><PERSON><PERSON>", "smw_result_results": "<PERSON><PERSON> j<PERSON>", "smw_result_noresults": "<PERSON> jefonge.", "smwadmin": "Verwalldung fum semantesche MehdijaWikki", "smw-admin-setupsuccess": "Dä Schpeischerplaz es opjesaz för et semantesch MehdijaWikki.", "smw_smwadmin_return": "Jangk retuhr noh $1", "smw_smwadmin_updatestarted": "Ene Projrammlouf för de seman<PERSON>che Date neu opzeboue es em Jang. All de jeshpeisherte Date wäde neu zosamme jebout un woh nüdesch repareet. Mer kann bei däm Louf hee op dä Söndersigg metlo<PERSON>.", "smw_smwadmin_updatenotstarted": "Et es ald e Projramm för dat op der neuste Shtand ze bränge aam Loufe.\nMer donn jetz nit noch ein a<PERSON>htüße!", "smw_smwadmin_updatestopped": "Alle Projramme för et op der neuste Shtand Bränge, sin jetz aanjehallde woode.", "smw_smwadmin_updatenotstopped": "Öm dat Projramm anzehallde, wat ald am Loufe es, moß De dat H<PERSON>che en dat K<PERSON>ßje maache, öm ze zei<PERSON>, dat De <PERSON> janz sesher bes.", "smw-admin-docu": "Hee di {{int:specialpage}} hellef beim En<PERSON>tallerhre un beim op der neue Schtand bränge vum <a href=\"http://semantic-mediawiki.org\">Semantesche MehdijaWikki</a>. Opjepaß: <PERSON><PERSON>, ih dat De di <PERSON>ßjohne aanschmiiße deihß!", "smw-admin-db": "Datebangk inshtalleere un op der neuste Shatnd bränge", "smw-admin-dbdocu": "Semantesch MehdijaWikki bruch eije zosätzlejje Plaz en de Dahtebangk vum MehdijaWikki, öm sing semantesche Dahte ongerzebränge. De Fonkßjuhne heh dronger sorje <PERSON>, dat de Dahtebangk em Wik<PERSON> öhndles<PERSON> för et Semantesch MediaWiki opjesaz es. Wat heh verändert weet, dat hät keine Ennfloß op dä Rääß vun dä Dahtebangk vum MehdijaWikki, un mer kann et och eijfach widder retuhr maache, wann mer well. Di Fonkßjuhn zom Opsäze kam_mer esu öff loufe lohße, wi mer well, dat schahdt nix, ävver et es bloß eijmohl nühdesch bemm Ennreeschte, udder bem Ömschteije obb en neujere Projrammväsjohn.", "smw-admin-permissionswarn": "<PERSON><PERSON> <PERSON> met en<PERSON> em\n<i lang=\"en\" xml:lang=\"en\" dir=\"ltr\" title=\"Structured Query Language - „bedehnt Dahebangke met Bezösch dren“\">SQL</i> don<PERSON><PERSON><PERSON> j<PERSON>, dann künnd et sin, dat dä Nahme för op de Dahtebangk zohzejriife un sesch draan aanzemällde en de <code lang=\"en\" xml:lang=\"en\" dir=\"ltr\">LocalSettings.php</code> för Ding Wikki nit jenohch Rääschte en dä Datebangk hät. Änntwehder jiß De dämm dat Rääsch, Tabälle aanzelähje udder fott ze maache, udder donn för der Momang Dinge eije Aanmälldong udder di vum  Datebank-Köbes en dä <code lang=\"en\" xml:lang=\"en\" dir=\"ltr\">LocalSettings.php</code> en<PERSON><PERSON>, udder nemm dat Waadungsprojramm <code lang=\"en\" xml:lang=\"en\" dir=\"ltr\">setupStore.php</code> wat sesch op de Aanjabe en <code lang=\"en\" xml:lang=\"en\" dir=\"ltr\">LocalSettings.php</code> beträke kann, un Opjepaß: Donn dat dernoh wider retuhr maache!", "smw-admin-dbbutton": "Tabelle aanlääje udder op der neuste Shatnd bränge", "smw-admin-announce": "Donn Ding Wiki annongßeere un bikannt maache", "smw_smwadmin_datarefresh": "Date repareere un op der neuste Shtand bränge", "smw_smwadmin_datarefreshdocu": "Et es müj<PERSON><PERSON><PERSON>, all Date vun Semantesch MehdijaWikki uß dä nommahle Dahte em Wikki widder neu zesamme ze krijje. Dat kann joot sin, öm kapodde Date ze repareere, udder de Date op der aktoälle Schtand ze bränge, nohdämm sesh jet draan jeändert hät, dorjen Änderong aan de Projramme. De Aanpaßong weed Sigg för Sigg jedonn un weed dröm nit paaftisch fähdesch wehde. <PERSON><PERSON><PERSON> kanns De <PERSON>, ov en Aanpaßong em Jang es, un Do kanns ein aanfange udder aanhallde, ußer wann ene Wiki_Köbes di Müjjeleschkeit ußjeschalldt hät.", "smw_smwadmin_datarefreshprogress": "<strong>En XXXXXXX es ald ungerwähß.</strong> Es es nomahl för ene XXXXXXXXX dat dat langsam föran jeiht, weil dobei de Date nur en klein Häppsche jeändert wääde, un nur dann, wann eine dat wiki bruch. Öm flöcker fähdesch ze wäde, kanns de dat Waadungs-Projramm <code>runJobs.php</code> vun MediaWiki aanschmiiße. Nemm dä Parrameeter <code>--maxjobs 1000</code> öm dä XXXXXXXXXX en jedem Rötsch faß_ze_lääje. Mer sen onjefähr esu wick:", "smw_smwadmin_datarefreshbutton": "<PERSON><PERSON><PERSON><PERSON> met de <PERSON> op der neue Shtand ze brenge", "smw_smwadmin_datarefreshstop": "Donn dat op der neue Schtand Brenge ophühre", "smw_smwadmin_datarefreshstopconfirm": "<PERSON><PERSON>, esch ben mer sescher, esch well dat han.", "smw-admin-support": "Hölp kri<PERSON>", "smw-admin-supportdocu": "Bei Probleme kriss De velleich Hölp övver en Aanzahl vun Müjjeleschkeite:", "smw-admin-installfile": "<PERSON><PERSON> met <PERSON><PERSON><PERSON>, dann donn met de Reeschlinnije en dä Dattei <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">INSTALL</a> an<PERSON><PERSON>, donoh ze söke, wat De donn kanns.", "smw-admin-smwhomepage": "De kumplätte Dokku för et <i lang=\"en\">Semantic Mediawiki</i> es op <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b> ze fenge.", "smw-admin-bugsreport": "<PERSON><PERSON><PERSON> kanns <PERSON> öv<PERSON> <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\" lang=\"en\">GitHub</a> melde.", "smw-admin-questions": "<PERSON><PERSON> De noch Froore häß odder Vörschlääsch maache odder klaafe wells, jangk op et <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\"><i lang=\"en\">Semantic MediaWiki</i> Metmaacher Forum</a> un donn doh metschwaade.", "smw_adminlinks_datastructure": "<PERSON>chtrok<PERSON>", "smw_adminlinks_displayingdata": "<PERSON><PERSON> a<PERSON>", "smw_adminlinks_inlinequerieshelp": "<PERSON><PERSON><PERSON><PERSON> övver de em Wiki sing Sigge enjeboute Froore", "smw-createproperty-isproperty": "Dat ess_en Eijeschaff vun dä Zoot $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Dä einzijje müjjelesche Wäät för di Eijeschaff es:|De müjjelesche Wääte för di Eijeschaff sin:|Mer han kein müjjelesche Wääte för di Eijeschaff.}}", "smw-ui-tooltip-title-quantity": "De Moßeinheijd <PERSON>", "smw_unknowntype": "<PERSON>t „$1“ fö<PERSON> de <PERSON><PERSON>f dom_mer nit ongers<PERSON>.", "smw_concept_header": "Sigge vun däm Kumzäp „$1“", "smw_conceptarticlecount": "{{PLURAL:$1|Ein Sigg jehööt|$1 Sigge jehüüre|Et jehüüere kein Sigge}} zoh dämm Kumzäp{{PLURAL:$1|:|:|.}}", "smw-property-predefined-default": "„$1“ es en Eijeschaff, di vum Wikkiprojramm ald faßjelahd es.", "smw-property-predefined-asksi": "„$1“ es en Eijeschaff, di vum Wikkiprojramm ald faßjelahd es, woh de Nommer vun dä Bedengonge en ene Affrohch faßjehallde es.", "smw-livepreview-loading": "Ben <PERSON>&nbsp;…", "smw-editpage-property-annotation-disabled": "Heh di Eijeschaff löht sesch nit med ene Zoot vun de Dahte verbenge, weil di ald vum Wikki uß faßjelaat es. Alsu „<code lang=\"en\" xml:lang=\"en\" dir=\"ltr\"><nowiki>[[Has type::Page]]</nowiki></code>“ jeiht heh nit. Op dä <PERSON> met [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Hölp övver besöndere Eijeschaffte] fengk mer mih Enfommazjuhne.", "smw-datavalue-import-missing-type": "Mer han de <PERSON> Da<PERSON>e vun dä Eijeschaff „$1“ nidd em [[MediaWiki:Smw import $2|Empoot vum $2]] jefonge.", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|Empoot vum $1]]", "smw-property-predefined-errp": "„$1“ es en Eijeschaff, di vum Wikkiprojramm ald faßjelaad es un jebruch weed, öm kappode Wääte för Eijeschaffte faßzehallde, di wahschein<PERSON>ch vun Beschängkonge vun de Zoot udder Beschängkonge vun [[Property:Allows value|de zohjelohße Wääte]] kumme.", "smw-property-predefined-pval": "[https://semantic-mediawiki.org/wiki/Help:Special_property_Allows_value\"$1\"] es en Eijeschaf<PERSON>, di vum Wikkiprojramm ald faßjelaad es un di en Leß met zohjelohße Wääte, f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ö<PERSON> de Zohdeijlong vun Wääte för en Einjeschaff ze beschrängke.", "smw-datavalue-restricted-use": "<PERSON><PERSON>äd „$1“ wood för ene beschrängkte Jebruch makehrt.", "smw-datavalue-invalid-number": "„$1“ km_mer nit als en Zahl verschtonn.", "smw-query-condition-circular": "<PERSON><PERSON><PERSON><PERSON><PERSON>, dat en Bedengong en „$1“ em Kreis erm jeiht.", "smw-types-list": "Leß vun de <PERSON>e_Zoote", "smw-types-default": "„$1“ es ene pä Projramm ald faßjelahte un ennjeboute Dahte_Zoot.", "smw-types-help": "<PERSON>h Aanjahbe un Beijschpelle fengk mer op dä [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 Sigg met Hölp] derzoh.", "smw-type-anu": "„$1“ ess_en beschtemmpte Zoot vun [[Special:Types/URL|\n<i lang=\"en\" xml:lang=\"en\" dir=\"ltr\" title=\"Uniform Ressource Locator\">URL</i> ]] dä dermihts för ene Äxpocht noh <i lang=\"en\" xml:lang=\"en\" dir=\"ltr\">owl:AnnotationProperty</i> ze beschriehve jebruch weed.", "smw-type-boo": "„$1“ ess_en eijfache Dahte_Zoot, di <PERSON><PERSON>/<PERSON><PERSON><PERSON>, udder <PERSON>/Verkiehrt affbelde kann.", "smw-type-cod": "„$1“ ess_en affjewandelte Dahte_Zoot vum [[Special:Types/Text|Täx]], öm täschnesche Täx_Krohm med esu vell Zeische henernander wi nühdesch daazeschtälle, zem Beijschpell Projrammleßte.", "smw-type-geo": "„$1“ ess_en Dahte_Zoot, di Pläz op de Ääd beschriev un et Zohsazprojramm för [https://www.semantic-mediawiki.org/wiki/Semantic_Maps semantesch Lanndkaate] nühdesch hät.", "smw-type-tel": "„$1“ ess_en Dahte_Zoot, beschtemmp derför, engenazjonahle Tellefohnnommere em Fommaht vum <i lang=\"en\" xml:lang=\"en\" dir=\"ltr\">RFC 3966</i> daazeschtälle.", "smw-type-txt": "„$1“ ess_en eijfache Dahte_Zoot, becht<PERSON><PERSON>för, esu vell Zeische hengerenander daazeschtälle, wi nühdesch.", "smw-type-dat": "„$1“ ess_en Dahte_Zoot för <PERSON>_Pongkte en enem eijheijtlejje Fommaht daazeschtälle.", "smw-limitreport-intext-parsertime": "[SMW] De Zigg di der Paaser för et Annotehre jebruch hät", "smw-limitreport-intext-parsertime-value": "{{PLURAL:$1|eijn <PERSON>|$1 Sekonde|keijn <PERSON>d}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] De Duur för et Ändere fum Zwescheschpeijscher beim Fottschmiiße", "smw-limitreport-pagepurge-storeupdatetime-value": "{{PLURAL:$1|eijn <PERSON>|$1 Sekond|keijn Sekond}}", "smw-listingcontinuesabbrev": "… (wigger)", "smw-showingresults": "Onge {{PLURAL:$1|weed <strong>eine</strong>|wääde bes <strong>$1</strong>|weed <strong>keine</strong>}} vun de jefonge <PERSON><PERSON> j<PERSON>, vun de <PERSON> <strong>$2</strong> av."}