{"settings": {"number_of_shards": 2, "number_of_replicas": 2, "refresh_interval": "1s", "index.mapping.total_fields.limit": "9000", "index.max_result_window": "50000", "analysis": {"filter": {"uri_stopwords_filter": {"type": "stop", "stopwords": ["http", "https", "ftp", "www"]}}, "char_filter": {"wiki_char_filter": {"type": "mapping", "mappings": ["[ => ", "] => ", "/ => "]}}, "analyzer": {"uri_lowercase_with_stopwords": {"type": "custom", "tokenizer": "lowercase", "filter": ["uri_stopwords_filter"]}, "exact_text_lowercase": {"type": "custom", "tokenizer": "keyword", "filter": ["lowercase", "asciifolding"]}}, "normalizer": {"standard_sort_normalizer": {"type": "custom", "char_filter": ["wiki_char_filter"]}, "char_normalizer": {"type": "custom", "char_filter": ["wiki_char_filter"]}, "lowercase_normalizer": {"type": "custom", "char_filter": [], "filter": ["lowercase"]}, "case_insensitive_sort_normalizer": {"type": "custom", "char_filter": ["wiki_char_filter"], "filter": ["lowercase", "asciifolding"]}}}}, "mappings": {"dynamic_templates": [{"text_fields": {"path_match": "P:*.txtField", "match_mapping_type": "*", "mapping": {"type": "text", "copy_to": "text_copy", "fields": {"sort": {"type": "keyword", "normalizer": "standard_sort_normalizer", "index": false, "ignore_above": 256}, "keyword": {"type": "keyword", "ignore_above": 2000}}}}}, {"uri_fields": {"path_match": "P:*.<PERSON><PERSON><PERSON><PERSON>", "match_mapping_type": "*", "mapping": {"type": "text", "copy_to": "text_copy", "fields": {"sort": {"type": "keyword", "normalizer": "standard_sort_normalizer", "index": false, "ignore_above": 256}, "keyword": {"type": "keyword", "ignore_above": 2000}, "lowercase": {"type": "text", "analyzer": "uri_lowercase_with_stopwords"}}}}}, {"page_fields_text": {"path_match": "P:*.wpg<PERSON><PERSON>", "match_mapping_type": "*", "mapping": {"type": "text", "copy_to": "text_copy", "fields": {"sort": {"type": "keyword", "normalizer": "standard_sort_normalizer", "index": false, "ignore_above": 256}, "keyword": {"type": "keyword", "ignore_above": 500}, "lowercase": {"type": "keyword", "normalizer": "lowercase_normalizer"}}}}}, {"page_fields_identifier": {"path_match": "P:*.wpgID", "match_mapping_type": "*", "mapping": {"type": "long"}}}, {"numeric_fields": {"path_match": "P:*.num<PERSON><PERSON>", "match_mapping_type": "*", "mapping": {"type": "double", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, {"date_fields": {"path_match": "P:*.da<PERSON><PERSON><PERSON>", "match_mapping_type": "*", "mapping": {"type": "double", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, {"date_fields_raw": {"path_match": "P:*.dat_raw", "match_mapping_type": "*", "mapping": {"type": "keyword"}}}, {"geo_fields": {"path_match": "P:*.geo<PERSON><PERSON>", "match_mapping_type": "string", "mapping": {"type": "keyword", "fields": {"point": {"type": "geo_point"}}}}}, {"boolean_fields": {"path_match": "P:*.b<PERSON><PERSON><PERSON>", "match_mapping_type": "boolean", "mapping": {"type": "boolean"}}}], "properties": {"noop": {"type": "integer"}, "text_copy": {"type": "text"}, "text_raw": {"type": "text"}, "subject.title": {"type": "text", "fields": {"sort": {"type": "keyword", "normalizer": "standard_sort_normalizer", "index": false}, "keyword": {"type": "keyword", "ignore_above": 256}}}, "subject.interwiki": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "subject.subobject": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "subject.sortkey": {"type": "text", "copy_to": "text_copy", "fields": {"sort": {"type": "keyword", "normalizer": "standard_sort_normalizer", "index": false}, "keyword": {"type": "keyword", "ignore_above": 256}, "lowercase": {"type": "keyword", "normalizer": "lowercase_normalizer"}}}, "subject.rev_id": {"type": "integer"}}}}