name: build

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  workflow_dispatch:

jobs:
  build:

    runs-on: ubuntu-latest
    continue-on-error: ${{ matrix.experimental }}

    strategy:
      matrix:
        include:
          - mediawiki_version: '1.43'
            php_version: 8.1
            database_type: mysql
            database_image: "mariadb:11.2"
            coverage: true
            experimental: false
          - mediawiki_version: '1.43'
            php_version: 8.2
            database_type: mysql
            database_image: "mariadb:11.2"
            coverage: false
            experimental: false
          - mediawiki_version: '1.43'
            php_version: 8.3
            database_type: mysql
            database_image: "mariadb:11.2"
            coverage: false
            experimental: true
          - mediawiki_version: '1.44'
            php_version: 8.3
            database_type: mysql
            database_image: "mariadb:11.2"
            coverage: false
            experimental: true
          - mediawiki_version: '1.44'
            php_version: 8.3
            database_type: mysql
            database_image: "mariadb:11.8"
            coverage: false
            experimental: true

    env:
      MW_VERSION: ${{ matrix.mediawiki_version }}
      PHP_VERSION: ${{ matrix.php_version }}
      DB_TYPE: ${{ matrix.database_type }}
      DB_IMAGE: ${{ matrix.database_image }}
      PHP_EXTENSIONS: pcntl

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
                   
      - name: Update submodules
        run: git submodule update --init --remote

      - name: Run tests
        run: make ci
        if: matrix.coverage == false

      - name: Run unit tests with coverage
        run: make ci-coverage COMPOSER_PARAMS="-- --testsuite=semantic-mediawiki-unit"
        if: matrix.coverage == true

      - name: Run tests with coverage
        run: make ci-coverage COMPOSER_PARAMS="-- --testsuite semantic-mediawiki-check,semantic-mediawiki-data-model,semantic-mediawiki-integration,semantic-mediawiki-import,semantic-mediawiki-structure"
        if: matrix.coverage == true

      - name: Upload code coverage
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: coverage/php/coverage.xml
        if: matrix.coverage == true
