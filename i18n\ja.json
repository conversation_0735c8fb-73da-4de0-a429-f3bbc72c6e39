{"@metadata": {"authors": ["48<PERSON><PERSON>", "Aefgh39622", "Ant176", "Aotake", "<PERSON>", "Foomin10", "Fryed-peach", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Kokage si", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Likibp", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Müller857", "Naohiro19", "Nemo bis", "Ochaochaocha3", "Omotecho", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> ja", "Ridmevo", "Rootflag", "Rxy", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "SkyDaisy9", "Suchichi02", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Wat", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Yusuke1109", "Zero 1 k", "沈澄心", "背番号4のエース", "青子守歌", "아라"]}, "smw-desc": "ウィキを機械や人間にとってよりアクセスしやすいものにする ([https://www.semantic-mediawiki.org/wiki/Help:User_manual オンラインドキュメント])", "smw-error": "エラー", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki]はインストールが済んで有効に設定しましたが、適切な [https://www.semantic-mediawiki.org/wiki/Help:Upgrade 更新キー]が見当たりません。", "smw-upgrade-release": "リリース", "smw-upgrade-progress": "進捗", "smw-upgrade-progress-explain": "更新の完了時期について、データリポジトリのサイズならびに使える機器に左右されるため、予測の算出は難しく、大規模ウィキでは所要時間が長くかかる可能性があります。\n\nローカルの管理者にご連絡の上、プロセスの詳細をご確認ください。", "smw-upgrade-progress-create-tables": "テーブルと索引の作成 (もしくは更新中) です...", "smw-upgrade-progress-post-creation": "作成後のタスクを実行中です...", "smw-upgrade-progress-table-optimization": "テーブルの最適化を実行中です...", "smw-upgrade-progress-supplement-jobs": "補充ジョブを追加中です...", "smw-upgrade-error-title": "エラー » Semantic MediaWiki", "smw-upgrade-error-why-title": "このページが表示される理由は？", "smw-upgrade-error-why-explain": "Semantic MediaWiki の内部データベース構造は変更され、十分に機能するための調整が必要です。理由として複数あげられます。\n* 修正済みプロパティを追加したため (テーブルに追加設定が必要)\n* 更新によりテーブルもしくは索引の変更が発生しデータへのアクセス前に処理の中断を余儀なくされたため\n* ストレージもしくはクエリエンジンに変更があったため", "smw-upgrade-error-how-title": "このエラーの修復方法は？", "smw-upgrade-error-how-explain-admin": "管理者 (もしくは管理者権限の保有者) に連絡しし MediaWiki の [https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Update.php update.php] もしくは Semantic MediaWiki の [https://www.semantic-mediawiki.org/wiki/Help:SetupStore.php setupStore.php] 管理用スクリプトの実行を依頼してください。", "smw-upgrade-error-how-explain-links": "また手助けに関する詳細情報は以下のページを参照してください。\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation インストール]の手順\n* [https://www.semantic-mediawiki.org/wiki/Help:Installation/Troubleshooting トラブル解決]用のヘルプページ", "smw-extensionload-error-why-title": "このページが表示される理由は？", "smw-extensionload-error-how-explain": "拡張機能を有効にし、名前空間の宣言や未設定の構成による問題を回避するには、<code>enableSemantics</code> を使用して必要な変数を設定してから、<code>ExtensionRegistry</code> を通じて拡張機能を読み込む必要があります。\n\n詳細は [https://www.semantic-mediawiki.org/wiki/Help:EnableSemantics enableSemantics] のヘルプページを参照してください。", "smw-upgrade-maintenance-title": "メンテナンス » Semantic MediaWiki", "smw-upgrade-maintenance-why-title": "このページが表示される理由は？", "smw-upgrade-maintenance-explain": "この拡張機能は、影響とダウンタイムを最小限に抑えるため、メンテナンスタスクの大半を <code>update.php</code> の後に遅延させるようにしていますが、いくつかのデータベース関連の変更については、データの不整合を避けるために先に完了させる必要があります。これには以下が含まれることがあります:\n* テーブル構造の変更 (既存のフィールドの変更や新規フィールドの追加など)\n* テーブル索引の変更または追加\n* テーブル最適化の実行 (有効に設定されている場合)", "smw-semantics-not-enabled": "この wiki では Semantic MediaWiki の機能が有効化されていませんでした。", "smw_viewasrdf": "RDF フィード", "smw_finallistconjunct": ",", "smw-factbox-head": "...「$1」についてさらに詳しく", "smw-factbox-facts": "ファクト", "smw-factbox-facts-help": "特定の利用者が作成した文とファクトを表示", "smw-factbox-attachments": "添付ファイル", "smw-factbox-attachments-value-unknown": "利用不可", "smw-factbox-attachments-is-local": "ローカル", "smw-factbox-attachments-help": "利用可能な添付ファイルを表示", "smw_isspecprop": "このプロパティはこのウィキ内の特別なプロパティです。", "smw-concept-cache-header": "キャッシュの使用", "smw-concept-cache-count": "[https://www.semantic-mediawiki.org/wiki/Help:Concept_cache_count コンセプトのキャッシュ]には {{PLURAL:$1|エンティティが'''1件''' |エンティティが'''$1'''件}}あります ($2)。", "smw-concept-no-cache": "利用可能なキャッシュはありません。", "smw_concept_description": "コンセプト「$1」の説明", "smw_no_concept_namespace": "コンセプトを定義できるのは、Concept: 名前空間内のみです。", "smw_multiple_concepts": "各コンセプトページが持てるコンセプト定義は 1 つのみです。", "smw_concept_cache_miss": "コンセプト「$1」は、ウィキの設定によりオフラインでの処理が必要なため、現時点では利用できません。しばらく経っても問題が解消されない場合は、このコンセプトを利用可能にするようにサイト管理者にお問い合わせください。", "smw_noinvannot": "逆プロパティには値を割り当てられません。", "version-semantic": "意味的拡張機能", "smw_baduri": "「$1」の形式の URI は許可されていません。", "smw_printername_count": "集計結果", "smw_printername_csv": "CSV 形式で書き出し", "smw_printername_dsv": "DSV 形式で書き出し", "smw_printername_debug": "クエリをデバッグ (上級者向け)", "smw_printername_embedded": "ページの内容を埋め込む", "smw_printername_json": "JSON 形式で書き出し", "smw_printername_list": "一覧", "smw_printername_plainlist": "平文の一覧", "smw_printername_ol": "番号付き箇条書き", "smw_printername_ul": "箇条書き", "smw_printername_table": "表", "smw_printername_broadtable": "幅広の表", "smw_printername_template": "テンプレート", "smw_printername_templatefile": "テンプレートファイル", "smw_printername_rdf": "RDF 形式で書き出し", "smw_printername_category": "カテゴリ", "validator-type-class-SMWParamSource": "テキスト", "smw-paramdesc-limit": "返す結果の最大数", "smw-paramdesc-offset": "最初の結果のオフセット", "smw-paramdesc-headers": "ヘッダー名やプロパティ名を表示", "smw-paramdesc-mainlabel": "メインページ名に付けるラベル", "smw-paramdesc-link": "値をリンクとして表示", "smw-paramdesc-intro": "問い合わせ結果がある場合、その前に表示する文字列", "smw-paramdesc-outro": "問い合わせ結果がある場合、その後に表示する文字列", "smw-paramdesc-default": "問い合わせ結果がない場合に表示する文字列", "smw-paramdesc-sep": "結果の区切り文字", "smw-paramdesc-valuesep": "結果の属性値の区切り文字", "smw-paramdesc-showsep": "CSV ファイルの先頭に区切り文字を表示する (\"sep=<値>\")", "smw-paramdesc-distribution": "すべての値を表示する代わりに、出現数を数えて表示します。", "smw-paramdesc-distributionsort": "出現数によって値の分布を整列します。", "smw-paramdesc-template": "印刷出力とともに表示するテンプレートの名前", "smw-paramdesc-columns": "結果を表示する列の数", "smw-paramdesc-userparam": "テンプレートが使用されている場合に、各テンプレート呼出しに渡される値", "smw-paramdesc-introtemplate": "問い合わせ結果がある場合、その前に表示するテンプレートの名前", "smw-paramdesc-outrotemplate": "問い合わせ結果がある場合、その後に表示するテンプレートの名前", "smw-paramdesc-embedformat": "見出しの定義に使用する HTML タグ", "smw-paramdesc-embedonly": "見出しを表示しない", "smw-paramdesc-table-class": "表に設定する追加 CSS クラス", "smw-paramdesc-table-transpose": "表の見出しを縦に、結果を横に表示する", "smw-paramdesc-prefix": "印刷出力における名前空間名の表示を制御", "smw-paramdesc-rdfsyntax": "使用する RDF 構文", "smw-paramdesc-csv-sep": "使用する区切り文字の指定", "smw-paramdesc-csv-valuesep": "使用する区切り文字の指定", "smw-paramdesc-csv-bom": "出力するファイルの先頭にBOM (エンディアンネス識別文字) をつける", "smw-paramdesc-dsv-separator": "使用する区切り文字", "smw-paramdesc-dsv-filename": "DSV ファイルの名前", "smw-paramdesc-filename": "出力ファイルの名前", "smw-paramdesc-sort": "クエリの並べ替えに使用するプロパティ", "smw-paramdesc-order": "クエリの並べ替えでの並び順", "smw-paramdesc-searchlabel": "検索結果の続きへのリンク文字列", "smw-paramdesc-named_args": "テンプレートに渡した引数に名前を付ける", "smw-paramdesc-export": "書き出しオプション", "smw-paramdesc-jsonsyntax": "JSON 構文を使用します", "smw-printername-feed": "RSS / Atom フィード", "smw-paramdesc-feedtype": "フィードの種類", "smw-paramdesc-feedtitle": "フィードの表題として使用する文字列", "smw-paramdesc-feeddescription": "フィードの説明として使用する文字列", "smw-paramdesc-feedpagecontent": "フィードにページの本文も含める", "smw-label-feed-description": "$1 の $2 フィード", "smw_iq_disabled": "このウィキでは、意味的クエリは無効になっています。", "smw_iq_moreresults": "…その他の結果", "smw_parseerror": "指定した値を理解できませんでした。", "smw_notitle": "「$1」はこのウィキではページ名として使用できません。", "smw_noproperty": "「$1」はこのウィキではプロパティ名として使用できません。", "smw_wrong_namespace": "ここでは、「$1」名前空間のページのみが許可されています。", "smw_manytypes": "プロパティに対して複数の型を定義しました。", "smw_emptystring": "空文字列は受け入れられません。", "smw_notinenum": "「$1」は、「$3」プロパティに対して[[Property:Allows value|許可された値]]の一覧 ($2) の中に存在しません。", "smw-datavalue-constraint-error-allows-value-list": "\"$1\"は、\"$3\"で [[Property:Allows value|許可された値]] ($2) にありません。", "smw-constraint-error-suggestions": "一覧の違反項目とプロパティをそれぞれの注釈付きの値と比べて制限要項に合致するか確認してください。", "smw_noboolean": "「$1」は真偽値 (true/false) として認識されません。", "smw_true_words": "真,true,t,yes,y", "smw_false_words": "偽,false,f,no,n", "smw_nofloat": "「$1」は数値ではありません。", "smw_infinite": "「$1」を超える数値には対応していません。", "smw_unitnotallowed": "「$1」はこのプロパティの測定値の有効な単位として宣言されていません。", "smw_nounitsdeclared": "このプロパティの測定の単位は何も宣言されませんでした。", "smw_novalues": "値が指定されていません。", "smw_nodatetime": "日付「$1」を理解できませんでした。", "smw_toomanyclosing": "クエリ内の「$1」の数が多すぎるようです。", "smw_noclosingbrackets": "クエリ内の「<nowiki>[[</nowiki>」に、対応する「]]」で閉じられていないものがありました。", "smw_misplacedsymbol": "記号「$1」が、無意味な場所で使用されました。", "smw_unexpectedpart": "クエリの一部「$1」を理解できませんでした。\n結果は予期しないものになる可能性があります。", "smw_emptysubquery": "有効な条件がないサブクエリがあります。", "smw_misplacedsubquery": "許可されていない場所で使用されているサブクエリがあります。", "smw_valuesubquery": "プロパティ「$1」の値は、サブクエリに対応していません。", "smw_badqueryatom": "クエリの一部「<nowiki>[[…]]</nowiki>」を理解できませんでした。", "smw_propvalueproblem": "プロパティ「$1」の値を理解できませんでした。", "smw_noqueryfeature": "このウィキが対応していないクエリ機能があるため、クエリの一部を破棄しました ($1)。", "smw_noconjunctions": "このウィキはクエリの連言に対応していないため、クエリの一部を破棄しました ($1)。", "smw_nodisjunctions": "このウィキはクエリの選言に対応していないため、クエリの一部を破棄しました ($1)。", "smw_querytoolarge": "クエリの長さまたは深さに関するこのウィキでの制限のため、以下の{{PLURAL:$2|クエリ条件}}は考慮されませんでした: <code>$1</code>", "smw_notemplategiven": "このクエリ形式を動作させるには、引数「テンプレート」の値を指定してください。", "smw_db_sparqlqueryproblem": "クエリの結果を SPARQL データベースから取得できませんでした。このエラーは一時的なものである可能性、またはデータベース ソフトウェアのバグである可能性があります。", "smw_db_sparqlqueryincomplete": "クエリへの応答が非常に難しいと判断されたため、中止されました。いくつかの結果が失われている可能性があります。可能ならば、より単純なクエリを試してください。", "smw_type_header": "型「$1」のプロパティ", "smw_typearticlecount": "この型を使用している $1 {{PLURAL:$1|プロパティ}}を表示しています。", "smw_attribute_header": "プロパティ「$1」を使用しているページ", "smw_attributearticlecount": "このプロパティを使用している $1 {{PLURAL:$1|件のページ}}を表示しています。", "smw-propertylist-redirect-header": "同義語", "smw-propertylist-count": "関連する $1 {{PLURAL:$1|件の要素}}を表示しています。", "smw-propertylist-count-more-available": "関連する $1 {{PLURAL:$1|件の要素}}を表示しています（さらに利用可能）。", "specialpages-group-smw_group": "Semantic MediaWiki", "specialpages-group-smw_group-maintenance": "メンテナンス", "specialpages-group-smw_group-search": "閲覧と検索", "exportrdf": "ページを RDF に書き出し", "smw_exportrdf_docu": "このページを使用すると、ページからデータを RDF 形式で取得できます。ページを書き出すには、下のテキストボックスに、ページ名を一行に一つずつ入力してください。", "smw_exportrdf_recursive": "すべての関連ページを再帰的に書き出します。結果が大きくなる場合があるため注意してください。", "smw_exportrdf_backlinks": "書き出し対象のページを参照するページもすべて書き出す。\n閲覧できるRDFを生成する。", "smw_exportrdf_lastdate": "指定した時点以降に変更がされていないページを書き出さない。", "smw_exportrdf_submit": "書き出し", "uriresolver": "URI リゾルバー", "properties": "プロパティ", "smw-categories": "カテゴリ", "smw_properties_docu": "このウィキでは以下のプロパティが使用されています。", "smw_property_template": "$1、$2 型 ($3 {{PLURAL:$3|回使用}})", "smw_property_template_notype": "$1 ($2 件)", "smw_propertylackspage": "すべてのプロパティについて、各プロパティのページで解説する必要があります!", "smw_propertylackstype": "このプロパティには型が指定されていません (現時点では型 $1 と想定します)。", "smw_propertyhardlyused": "このプロパティはウィキ内でほとんど使用されていません!", "smw-property-name-invalid": "プロパティ $1 は使用できません (無効なプロパティ名)。", "smw-sp-property-searchform": "以下を含むプロパティを表示:", "smw-sp-property-searchform-inputinfo": "入力した内容は大文字・小文字が区別され、絞り込みに使用されます。条件に該当したプロパティのみが表示されます。", "smw-special-property-searchform-inputinfo": "入力した内容は大文字・小文字が区別され、絞り込みに使用されたときは、条件に該当したプロパティのみ表示されます。", "smw-special-property-searchform-options": "設定", "smw-special-wantedproperties-filter-label": "絞り込み:", "smw-special-wantedproperties-filter-none": "なし", "smw-special-wantedproperties-filter-unapproved": "未承認", "concepts": "コンセプト", "smw-special-concept-header": "コンセプト一覧", "smw-special-concept-count": "以下に{{PLURAL:$1|コンセプト}}を列挙します。", "smw-special-concept-empty": "コンセプトが見つかりませんでした。", "unusedproperties": "使われていないプロパティ", "smw-unusedproperties-docu": "このページの[https://www.semantic-mediawiki.org/wiki/Unused_properties 未使用のプロパティ]は存在しますが、他のページで使用されていません。特別ページで[[Special:Properties|全件]]もしくは[[Special:WantedProperties|要望で作成されたプロパティの一覧]]が確認できます。", "smw-unusedproperty-template": "$1: 型 $2", "wantedproperties": "望まれているプロパティ", "smw-wantedproperties-docu": "このページの[https://www.semantic-mediawiki.org/wiki/Wanted_properties 要望で作成されたプロパティ]は存在しますが、説明ページがありません。特別ページで[[Special:Properties|全件]]もしくは[[Special:UnusedProperties|未使用のプロパティを抽出した]]一覧が確認できます。", "smw-wantedproperty-template": "$1 ($2{{PLURAL:$2|件}})", "smw-special-wantedproperties-docu": "このページの[https://www.semantic-mediawiki.org/wiki/Wanted_properties 要望で作成されたプロパティ]は存在しますが、説明ページがありません。特別ページで[[Special:Properties|全件]]もしくは[[Special:UnusedProperties|未使用のプロパティを抽出した一覧]]が確認できます。", "smw_purge": "更新", "smw-purge-failed": "Semantic MediaWiki はページのリフレッシュを試みましたが失敗しました", "types": "型", "smw_types_docu": "[https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes 利用可能なデータ型]の一覧です。各々の[https://www.semantic-mediawiki.org/wiki/Help:Datatype 型]は、保管および、割り当てられたプロパティから遺伝する性質の表示の点で、値を特徴付ける属性の一意な集合に相当します。", "smw-special-types-no-such-type": "\"$1\" は適切なデータ型ではありません。", "smw-statistics": "意味的統計", "smw-statistics-property-instance": "プロパティ{{PLURAL:$1|値}} (合計)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|プロパティ}}]] (合計)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|プロパティ}} (合計)", "smw-statistics-property-used": "{{PLURAL:$1|プロパティ}}（少なくとも1つの値で使用）", "smw-statistics-property-page": "{{PLURAL:$1|プロパティ}}(ページに登録)", "smw-statistics-property-type": "{{PLURAL:$1|プロパティ}}(データ型に割り当てられています)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|クエリ}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|クエリ}}]] (総数、埋め込みされたもの)", "smw-statistics-query-size": "クエリのサイズ", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|コンセプト}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|コンセプト}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|下位オブジェクト}}]]", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|データ型}}]]", "smw-statistics-error-count": "{{PLURAL:$1|プロパティ値}} ([[Special:ProcessingErrorList|{{PLURAL:$1|不適切な注記}}]])", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities 期限切れ{{PLURAL:$1|エンティティ}}]", "smw_uri_doc": "URI リゾルバーは [$1 W3C TAG での httpRange-14 に関する議論成果]を実装しています。\nそれにより（機械向けの）RDF表現または（人間向けの）ウィキページがリクエストに応じて配信されることを保証します。", "ask": "意味的検索", "smw-ask-help": "この節には <code>#ask</code> 構文の使い方を説明するリンクが含まれます。\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Selecting_pages Selecting pages]  - ページを選択し条件を作成する方法について\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Search_operators Search operators] - 範囲クエリやワイルドカードなどの使用可能な検索演算子について\n\n* [https://www.semantic-mediawiki.org/wiki/Help:Displaying_information Displaying information] - 印刷用ステートメントとフォーマットオプションの使用方法の概要", "smw_ask_sortby": "列ごとに並べ替え (任意選択)", "smw_ask_ascorder": "昇順", "smw_ask_descorder": "降順", "smw-ask-order-rand": "おまかせ表示", "smw_ask_submit": "結果を取得", "smw_ask_editquery": "クエリを編集", "smw_add_sortcondition": "[並べ替え条件の追加]", "smw-ask-sort-add-action": "並べ替え条件の追加", "smw_ask_hidequery": "クエリを非表示 (折りたたむ)", "smw_ask_help": "クエリのヘルプ", "smw_ask_queryhead": "条件", "smw_ask_printhead": "表示内容の選択", "smw_ask_printdesc": "(プロパティ名を各行に 1 つ追加してください)", "smw_ask_format_as": "書式:", "smw_ask_defaultformat": "既定", "smw_ask_otheroptions": "その他のオプション", "smw-ask-otheroptions-collapsed-info": "利用できるオプションをすべて表示するには + アイコンを使用してください", "smw_ask_show_embed": "埋め込み用コードを表示", "smw_ask_hide_embed": "埋め込み用コードを非表示", "smw_ask_embed_instr": "このクエリをウィキページにインラインで埋め込むには、下記のコードを使用します。", "smw-ask-delete": "削除", "smw-ask-sorting": "並べ替え", "smw-ask-options": "オプション", "smw-ask-options-sort": "並べ替えオプション", "smw-ask-format-options": "フォーマットとオプション", "smw-ask-parameters": "パラメーター", "smw-ask-search": "検索", "smw-ask-debug": "デバッグ", "smw-ask-result": "結果", "smw-ask-empty": "すべての入力のクリア", "smw-ask-download-link-desc": "クエリ結果を $1 形式でダウンロードする", "smw-ask-format": "フォーマット", "smw-ask-format-selection-help": "選択したフォーマットに関するヘルプ: $1", "smw-ask-query-search-info": "問い合わせ <code><nowiki>$1</nowiki></code> に対して {{PLURAL:$3|1=<code>$2</code>（キャッシュ）|<code>$2</code>（キャッシュ）|<code>$2</code>}} より $4 {{PLURAL:$4|秒}}で応答が返りました。", "searchbyproperty": "プロパティによる検索", "processingerrorlist": "処理エラーの一覧", "propertylabelsimilarity": "プロパティラベル類似性レポート", "smw-processingerrorlist-intro": "以下の一覧は[https://www.semantic-mediawiki.org/ Semantic MediaWiki] に関連して発生した[https://www.semantic-mediawiki.org/wiki/Processing_errors 処理エラー]の概要を提供します。この一覧は定期的に監視し、無効な値の注記を訂正することをお勧めします。", "smw_sbv_docu": "指定したプロパティと値を持つすべてのページを検索します。", "smw_sbv_novalue": "そのプロパティに対して有効な値を入力するか、「$1」のすべてのプロパティ値を確認してください。", "smw_sbv_displayresultfuzzy": "プロパティ「$1」の値が「$2」であるすべてのページの一覧。\n結果がごく少数だったため、近い値も表示しています。", "smw_sbv_property": "プロパティ:", "smw_sbv_value": "値:", "smw_sbv_submit": "結果を取得", "browse": "ウィキの閲覧", "smw_browselink": "プロパティを閲覧", "smw_browse_article": "閲覧を開始するページの名前を入力してください。", "smw_browse_go": "表示", "smw_browse_show_incoming": "リンク元のプロパティを表示", "smw_browse_hide_incoming": "リンク元のプロパティを隠す", "smw_browse_no_outgoing": "このページにはプロパティはありません。", "smw_browse_no_incoming": "このページにリンクしているプロパティはありません。", "smw-browse-intro": "このページは主題もしくは実態のインスタンスの詳細を述べており、検査対象のオブジェクト名を入力してください。", "smw-browse-show-group": "グループを表示する", "smw-browse-hide-group": "グループを隠す", "smw-noscript": "このページもしくは操作の実行には JavaScript が必要です。要求された機能を提供できるように、ブラウザーで JavaScript を有効にするか、 JavaScript がサポートされているブラウザーを使用してください。詳細情報は、[https://www.semantic-mediawiki.org/wiki/Help:Noscript noscript] ヘルプページをご参照ください。", "smw_inverse_label_default": "$1である", "smw_inverse_label_property": "逆プロパティのラベル", "pageproperty": "ページのプロパティの検索", "smw_pp_docu": "指定したプロパティの値をすべて検索するには、ページとプロパティ、もしくはプロパティだけ入力します。", "smw_pp_from": "対象ページ", "smw_pp_type": "プロパティ", "smw_pp_submit": "結果を取得", "smw-prev": "前の {{PLURAL:$1|$1件}}", "smw-next": "次の {{PLURAL:$1|$1件}}", "smw_result_prev": "前へ", "smw_result_next": "次へ", "smw_result_results": "結果", "smw_result_noresults": "該当結果はありません。", "smwadmin": "Semantic MediaWiki のダッシュボード", "smw-admin-statistics-job-title": "ジョブ統計", "smw-admin-statistics-job-docu": "ジョブ統計は、まだ実行されていないスケジュールされた Semantic MeidaWiki ジョブについての情報を表示します。ジョブ数はわずかに不正確である可能性があり、また失敗した試行が含まれます。さらなる詳細については、[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue マニュアル]を参照してください。", "smw-admin-statistics-querycache-title": "クエリキャッシュ", "smw-admin-statistics-querycache-disabled": "このウィキでは[https://www.semantic-mediawiki.org/wiki/QueryCache クエリキャッシュ]が有効化されていないため、統計は利用できません。", "smw-admin-statistics-querycache-legend": "キャッシュ統計には暫定的な累積データと以下の派生データが含まれます：\n* 「misses」は、達成不可能という応答だった、キャッシュからのデータ検索の合計試行回数です。この応答により、直接のリポジトリ（DB、triple-store など）検索が強いられます。\n* 「deletes」は、キャッシュ追い出し操作の総量です（パージあるいはクエリの依存関係によります）\n* 「hits」には、埋め込まれた（ウィキページ内から呼ばれたクエリ）ソースあるいは埋め込まれていない（有効化されていれば、特別:問い合わせのようなページまたは API からリクエストされた）ソースからのキャッシュ検索量が含まれます\n* 「medianRetrievalResponseTime」は、収集プロセスの期間にわたる、キャッシュされた検索リクエストおよびキャッシュされていない検索リクエストの応答時間（秒単位）の中央値です\n* 「noCache」は、結果をキャッシュから検索しようとしないリクエスト（limit=0 クエリ、'no-cache' オプションなど）の量を示します", "smw-admin-statistics-semanticdata-overview": "概要", "smw-admin-permission-missing": "このページへのアクセスは権限がないためブロックされました。[https://www.semantic-mediawiki.org/wiki/Help:Permissions 権限]ヘルプページを参照して必要な設定の詳細をご確認ください。", "smw-admin-setupsuccess": "ストレージエンジンが構築されました。", "smw_smwadmin_return": "$1 に戻る", "smw_smwadmin_updatestarted": "意味的データを最新の状態にするための新しい更新プロセスを開始しました。\n格納済みのデータはすべて必要に応じて再構築または修復されます。\nこの特別ページで更新の状況を追うことができます。", "smw_smwadmin_updatenotstarted": "既に実行中の更新プロセスがあります。\n新たに作成しないでください。", "smw_smwadmin_updatestopped": "既存のすべての更新プロセスは停止されました。", "smw_smwadmin_updatenotstopped": "実行中の更新プロセスを停止するには、本当に理解していることを示すためにチェックボックスを選択する必要があります。", "smw-admin-docu": "この特別ページは <a href=\"https://www.semantic-mediawiki.org\">Semantic MediaWiki</a> のインストール、アップグレード、保守および使用を支援します。また、統計と同様にさらなる管理者向け機能およびタスクを提供します。\n管理機能を実行する前に、重要なデータをバックアップするようにしてください。", "smw-admin-environment": "ソフトウェア環境", "smw-admin-db": "データベース設定", "smw-admin-dbdocu": "Semantic MediaWiki は、意味的データを格納するため、独自のデータベース構造を必要とします（Semantic MediaWiki は MediaWiki から独立しており、MediaWiki の他の要素には影響しません）。この設定機能を複数回実行しても何の害もありませんが、インストール時もしくは更新時に一度だけ実行すれば十分です。", "smw-admin-permissionswarn": "SQL エラーを出して処理が失敗する場合、おそらくあなたのウィキのデータベース ユーザー (\"LocalSettings.php\" を確認してください) に必要な権限がありません。\n\"LocalSettings.php\" に一時的にデータベースの root のログイン情報を記入してこのユーザーにテーブルを作成および削除するための追加権限を与えるか、<code>setupStore.php</code> の認証情報を使用できるメンテナンススクリプト <code>setupStore.php</code> を使用してください。", "smw-admin-dbbutton": "テーブルを初期化またはアップグレード", "smw-admin-announce": "ウィキの発表", "smw-admin-announce-text": "ウィキが公開されている場合、ウィキを監視するウィキである <a href=\"https://wikiapiary.com\">WikiApiary</a> に登録できます。", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code> は非推奨となり、$2 で削除される予定です。", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1<code> は <code>$2<code> に置き換えられます", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> は $2 で削除されました", "smw-admin-deprecation-notice-title-replacement": "代替または名前が変更された設定", "smw-admin-deprecation-notice-title-removal": "削除された設定", "smw-admin-deprecation-notice-title-removal-explanation": "<b>削除された設定</b>以前のリリースで削除されたが、このウィキでまだ使用していることを検出した識別された設定項目", "smw-smwadmin-refresh-title": "データの修復と更新", "smw_smwadmin_datarefresh": "データの再構築", "smw_smwadmin_datarefreshdocu": "ウィキの現在の内容に基づいて、Semantic MediaWiki の全データを復旧できます。\nこの機能は、破損したデータを修復する場合や、ソフトウェアのアップグレードによって内部形式が変わった際にデータをリフレッシュする場合などに有用です。\n更新は1ページずつ実行され、直ちには完了しません。\n以下に更新が進行中かどうかを表示します。更新を開始または停止できます（サイト管理者が機能を無効にしていない場合のみ）。", "smw_smwadmin_datarefreshprogress": "<strong>更新が既に進行中です。</strong>\n利用者がウィキにアクセスするたびに少量の塊のデータを更新するのみであるため、更新がゆっくりとしか進まないのは正常な動作です。\nこの更新をより早く終えるには、MediaWiki のメンテナンス スクリプト <code>runJobs.php</code> を実行してください (1つのバッチで行われる更新の数を制限するには <code>--maxjobs 1000</code> オプションを使用してください)。\n現在の更新の推定進捗:", "smw_smwadmin_datarefreshbutton": "データの再構築をスケジュールする", "smw_smwadmin_datarefreshstop": "この更新を停止する", "smw_smwadmin_datarefreshstopconfirm": "はい、{{GENDER:$1|もちろん}}です。", "smw-admin-job-scheduler-note": "この節のタスク (有効なもの) は、実行中にデッドロック状態となることを避けるため、ジョブキューを使用して実行されます。[https://www.mediawiki.org/wiki/Special:MyLanguage/Manual:Job_queue ジョブキュー]が処理を担当しているため、<code>runJobs.php</code> メンテナンススクリプトが適切な処理能力をもっていることが重要です (設定パラメーター <code>$wgRunJobsAsync</code> も参照してください)。", "smw-admin-outdateddisposal-title": "期限切れエンティティの破棄", "smw-admin-outdateddisposal-intro": "アクティビティによっては（プロパティの型の変更、ウィキページの除去、エラー値の修正）、[https://www.semantic-mediawiki.org/wiki/Outdated_entities 期限切れエンティティ]が発生します。関連するテーブル空間を解放するため、それらを定期的に除去することをお勧めします。", "smw-admin-outdateddisposal-active": "期限切れエンティティ破棄ジョブはスケジュールされています。", "smw-admin-outdateddisposal-button": "破棄をスケジュールする", "smw-admin-feature-disabled": "このウィキでは当該の機能は無効にされているため、<a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">設定</a>のヘルプページを参照するかシステム管理者にお問い合わせください。", "smw-admin-propertystatistics-title": "プロパティ統計の再構築", "smw-admin-propertystatistics-intro": "プロパティ使用統計をすべて再構築し、プロパティ[https://www.semantic-mediawiki.org/wiki/Help:Property_usage_count 使用数]を更新、修正します。", "smw-admin-propertystatistics-active": "プロパティ統計再構築ジョブはスケジュールされています。", "smw-admin-propertystatistics-button": "統計の再構築をスケジュールする", "smw-admin-fulltext-title": "全文検索データの再構築", "smw-admin-fulltext-button": "全文検索データを再構築する", "smw-admin-support": "支援を得る", "smw-admin-supportdocu": "問題が発生したときに助けとなるよう、次のようなさまざまなリソースが提供されます。", "smw-admin-installfile": "インストール中に問題が発生した場合の対策は、はじめに<a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">INSTALL ファイル</a>と、<a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">インストールページ\n</a>にある指針を確認します。", "smw-admin-smwhomepage": "Semantic MediaWiki 利用者向け説明文書の全文は <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b> にあります。", "smw-admin-bugsreport": "バグは <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">問題追跡ツール</a>、<a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">バグの報告</a>ページで報告できます。このページには、効果的な課題レポートの作成方法に関するガイダンスがあります。", "smw-admin-questions": "さらに質問や提案がある場合は、Semantic MediaWikiの<a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">利用者メーリングリスト</a>での議論に参加してください。", "smw-admin-other-functions": "その他の機能", "smw-admin-statistics": "統計", "smw-admin-supplementary-section-title": "追加機能", "smw-admin-supplementary-section-intro": "このセクションは、メンテナンスの範囲を超えた追加機能を提供します。記載されている機能（[https://www.semantic-mediawiki.org/wiki/Help:Special:SemanticMediaWiki/Supplementary_functions ドキュメント]を参照）の中には制限されているものや利用できないものもあり、したがってこのウィキではアクセスできない可能性があります。", "smw-admin-supplementary-settings-title": "設定", "smw-admin-supplementary-settings-intro": "<u>$1</u>は Semantic MediaWiki の動作を定義するパラメーターを示しています", "smw-admin-main-title": "Semantic MediaWiki » $1", "smw-admin-supplementary-operational-statistics-title": "運用統計", "smw-admin-supplementary-operational-statistics-short-title": "運用統計", "smw-admin-supplementary-operational-statistics-intro": "広範な<u>$1</u>のセットを表示します", "smw-admin-supplementary-idlookup-title": "エンティティの検索および破棄", "smw-admin-supplementary-idlookup-short-title": "エンティティの検索および破棄", "smw-admin-supplementary-idlookup-intro": "単純な<u>$1</u>機能をサポートします", "smw-admin-supplementary-duplookup-title": "重複エンティティ検索", "smw-admin-supplementary-duplookup-intro": "選択されたテーブル行列で重複と分類されたエンティティを検索するには、<u>$1</u>を実行します", "smw-admin-supplementary-duplookup-docu": "このページは、選択されたテーブルから[https://www.semantic-mediawiki.org/wiki/Help:Duplicate_entities 重複]と分類されたエントリを一覧表示します。重複エントリは（仮にあったとしても）稀にしか発生しません。潜在的な原因は、更新の中止またはロールバックトランザクションの失敗です。", "smw-admin-supplementary-operational-statistics-cache-title": "キャッシュ統計", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u>は、キャッシュ関連統計の選択されたセットを表示します", "smw-admin-supplementary-elastic-functions": "対応している関数", "smw-admin-supplementary-elastic-settings-title": "設定 (インデックス)", "smw-admin-supplementary-elastic-mappings-summary": "要約", "smw-admin-supplementary-elastic-nodes-title": "ノード", "smw-admin-supplementary-elastic-statistics-title": "統計", "smw-admin-supplementary-elastic-status-last-active-replication": "最後に同期した時刻: $1", "smw-admin-supplementary-elastic-status-refresh-interval": "更新間隔: $1", "smw-admin-supplementary-elastic-replication-files": "ファイル", "smw-admin-supplementary-elastic-replication-pages": "ページ", "smw-admin-supplementary-elastic-no-connection": "ウィキから現在、Elasticsearch クラスタへの接続が確立'''できない'''状態のため、ウィキの管理者に連絡してシステムの索引とクエリ機能が無効の問題の精査を依頼してください、", "smw-list-count": "一覧には$1{{PLURAL:$1|個}}のエントリが含まれています。", "smw-property-label-similarity-title": "プロパティラベル類似性レポート", "smw-property-label-similarity-threshold": "閾値:", "smw-admin-operational-statistics": "このページには、Semantic MediaWiki の関連機能内で収集された、あるいはそれらから収集された運用統計が含まれています。ウィキ固有の統計の広範な一覧は[[Special:Statistics|<b>ここ</b>]]に記載されています。", "smw_adminlinks_datastructure": "データ構造", "smw_adminlinks_displayingdata": "データの表示", "smw_adminlinks_inlinequerieshelp": "インラインクエリのヘルプ", "smw-property-indicator-type-info": "{{PLURAL:$1|利用者|システム}}が定義したプロパティ", "smw-createproperty-isproperty": "これは型 $1 のプロパティです。", "smw-createproperty-allowedvals": "このプロパティが取れる{{PLURAL:$1|値}}:", "smw-paramdesc-category-delim": "区切り文字", "smw-paramdesc-category-template": "項目の整形に使用するテンプレート", "smw-paramdesc-category-userparam": "テンプレートに渡すパラメーター", "smw-info-par-message": "表示するメッセージです。", "smw-info-par-icon": "表示するアイコン (「info」と「warning」のいずれか) です。", "prefs-smw": "Semantic MediaWiki", "prefs-general-options": "全般オプション", "prefs-extended-search-options": "拡張検索", "prefs-ask-options": "意味的検索", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ Semantic MediaWiki] および関連する拡張機能により、一部の選択された機能と関数に個別の個人設定ができます。 個別の特徴と解説および設定については、以下の[https://www.semantic-mediawiki.org/wiki/Help:User_preferences ヘルプ]を参照してください。", "smw-prefs-ask-options-tooltip-display": "特別ページ #ask [[Special:Ask|クエリビルダー]]でパラメーター文字列を情報ツールチップとして表示", "smw-prefs-general-options-time-correction": "地域の[[Special:Preferences#mw-prefsection-rendering|時差]]を使った特別ページ上の時刻補正を有効にする", "smw-prefs-general-options-disable-editpage-info": "編集ページ上の導入テキストを無効化する", "smw-prefs-general-options-disable-search-info": "標準の検索ページでの構文サポート情報を無効にする", "smw-prefs-general-options-suggester-textinput": "Semantic MediaWikiの要素の入力補助を有効にする", "smw-prefs-help-general-options-suggester-textinput": "有効にすると、入力したテキストからプロパティ、コンセプト、カテゴリの候補を表示する[https\n://www.semantic-mediawiki.org/wiki/Help:Input_assistance 入力補助機能]を使用できるようになります。", "smw-prefs-general-options-show-entity-issue-panel": "エンティティ問題パネルを表示", "smw-prefs-help-general-options-show-entity-issue-panel": "有効にすると、各ページにおいて整合性確認を実行し[https\n://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel エンティティ問題パネル]を表示します。", "smw-ui-tooltip-title-property": "プロパティ", "smw-ui-tooltip-title-quantity": "単位の換算", "smw-ui-tooltip-title-info": "情報", "smw-ui-tooltip-title-service": "サービスのリンク", "smw-ui-tooltip-title-warning": "警告", "smw-ui-tooltip-title-error": "エラー", "smw-ui-tooltip-title-parameter": "パラメーター", "smw-ui-tooltip-title-event": "イベント", "smw-ui-tooltip-title-note": "注記", "smw-ui-tooltip-title-legend": "凡例", "smw_unknowntype": "このプロパティの型「$1」は無効です", "smw-concept-cache-text": "このコンセプトについては合計 $1 {{PLURAL:$1|件のページ}}があり、最終更新は $2 $3です。", "smw_concept_header": "コンセプト「$1」のページ", "smw_conceptarticlecount": "以下に $1 {{PLURAL:$1|件のページ}}を表示しています。", "right-smw-admin": "管理作業にアクセス (Semantic MediaWiki)", "right-smw-patternedit": "許可された正規表現およびパターンの維持のための編集アクセス (Semantic MediaWiki)", "right-smw-pageedit": "<code>Is edit protected</code> が付与されたページに対する編集アクセス (Semantic MediaWiki)", "action-smw-pageedit": "<code>Is edit protected</code> が付与されたページの編集 (Semantic MediaWiki)", "group-smwadministrator": "管理者 (Semantic MediaWiki)", "group-smwadministrator-member": "{{GENDER:$1|管理者 (Semantic MediaWiki)}}", "grouppage-smwadministrator": "{{ns:project}}:管理者 (Semantic MediaWiki)", "group-smwcurator": "キュレーター (Semantic MediaWiki)", "group-smwcurator-member": "{{GENDER:$1|キュレーター (Semantic MediaWiki)}}", "grouppage-smwcurator": "{{ns:project}}:キュレーター (Semantic MediaWiki)", "action-smw-admin": "Semantic MediaWiki 管理作業へのアクセス", "smw-property-predefined-default": "「$1」は型 $2 の定義済みプロパティです。", "smw-property-predefined-common": "このプロパティは事前に設置されています ([https://www.semantic-mediawiki.org/wiki/Help:Special_properties 特別プロパティ]として知られています)。このプロパティには追加の管理権限が付属していますが、他の[https://www.semantic-mediawiki.org/wiki/Property 利用者定義プロパティ]と同様に使用できます。", "smw-sp-properties-docu": "このページはこのウィキにおける[https://www.semantic-mediawiki.org/wiki/Property プロパティ]およびその使用回数の一覧を表示します。最新の回数統計を表示するために、[https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics プロパティ統計]メンテナンススクリプトを定期的に実行することをお勧めします。異なる表示を行うには、[[Special:UnusedProperties|使われていないプロパティ]]あるいは[[Special:WantedProperties|望まれているプロパティ]]特別ページを参照してください。", "smw-sp-properties-header-label": "プロパティ一覧", "smw-admin-settings-docu": "Semantic MediaWiki 環境に関連するすべての既定とローカルの設定の一覧を表示します。個別の設定の詳細については、[https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration] ヘルプページを参照してください。", "smw-sp-admin-settings-button": "設定の一覧を生成", "smw-admin-idlookup-title": "検索", "smw-admin-idlookup-docu": "この節では、Semantic MediaWikiにおける個々のエンティティ（ウィキぺージ、サブオブジェクト、プロパティ等）に関する詳細な技術情報を表示します。該当する検索フィールドに一致する数値IDまたは文字列を入力しますが、どのID参照もSemantic MediaWikiが対象であり、MediaWiki上のページやIDではないことに注意してください。", "smw-admin-iddispose-title": "破棄", "smw-admin-iddispose-docu": "破棄操作は制限されておらず、さらに確認された場合はストレージエンジンからエンティティを削除するばかりかペンディングテーブルのレファレンスもすべて消去します。このタスクは必ず[https://www.semantic-mediawiki.org/wiki/Help:Object_ID_disposal ドキュメント]を参照してから'''注意して'''実行してください。", "smw-admin-iddispose-done": "ID「$1」はストレージバックエンドから除去されました。", "smw-admin-iddispose-no-references": "検索しましたが「$1」に対するテーブルのエントリが一致しませんでした。", "smw-admin-idlookup-input": "検索:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "概要", "smw-admin-tab-maintenance": "メンテナンス", "smw-admin-tab-supplement": "追加機能", "smw-admin-deprecation-notice-section": "Semantic MediaWiki", "smw-admin-configutation-tab-settings": "設定", "smw-admin-configutation-tab-namespaces": "名前空間", "smw-admin-maintenance-tab-tasks": "タスク", "smw-admin-maintenance-tab-scripts": "メンテナンススクリプト", "smw-admin-maintenance-no-description": "説明はありません。", "smw-livepreview-loading": "読み込み中...", "smw-sp-searchbyproperty-description": "このページは、プロパティと名前を付けられた値によって記述されたエントリを見つけるための簡素な[https://www.semantic-mediawiki.org/wiki/Help:Browsing_interfaces 閲覧インターフェース]を提供します。[[Special:PageProperty|ページのプロパティの検索]]や[[Special:Ask|問い合わせクエリビルダー]]といった他の検索インターフェースも利用可能です。", "smw-sp-searchbyproperty-resultlist-header": "結果の一覧", "smw-sp-searchbyproperty-nonvaluequery": "プロパティ「$1」が割り当てられている値の一覧です。", "smw-sp-searchbyproperty-valuequery": "値「$2」が付けられたプロパティ「$1」のあるページの一覧です。", "smw-editpage-annotation-enabled": "このページは意味的テキスト内注記（例：<nowiki>[[Is specified as::World Heritage Site]]</nowiki>）をサポートしています。これは構造化され問い合わせ可能なコンテンツを構築するためのもので、Semantic MediaWiki が提供しています。注記や #ask パーサー関数についての完全な説明は [https://www.semantic-mediawiki.org/wiki/Help:Getting_started getting started]、[https://www.semantic-mediawiki.org/wiki/Help:In-text_annotation in-text annotation]、または [https://www.semantic-mediawiki.org/wiki/Help:Inline_queries inline queries] ヘルプページをご覧ください。", "smw-editpage-annotation-disabled": "名前空間の制限により、このページでは意味的テキスト内注記を行えません。この名前空間において有効化する方法についての詳細は [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration] ヘルプページに記載されています。", "smw-search-syntax": "構文", "smw-search-profile-sort-recent": "最新", "smw-search-profile-extended-help-search-syntax-simplified-in": "* <code>in:</code>は、「...」を含む項目を全て検出し、検索に用いる文脈もしくはプロパティが不明の場合に特に便利です (例：<code>in:(lorem && ipsum)</code> と入力すると<code><nowiki>[[~~*lorem*]] && [[~~*ipsum*]]</nowiki></code> と記述したのと同じ検索を実行できる。)", "smw-search-profile-extended-help-query-link": "詳細情報は、$1をご使用ください。", "smw-search-profile-extended-help-find-forms": "利用可能なフォーム", "smw-search-profile-extended-section-sort": "並び順", "smw-search-profile-extended-section-form": "フォーム", "smw-search-profile-extended-section-namespace": "名前空間", "smw-search-profile-extended-section-query": "<PERSON><PERSON>リ", "smw-search-show": "表示", "smw-search-hide": "非表示", "log-name-smw": "Semantic MediaWiki ログ", "log-show-hide-smw": "Semantic MediaWiki 記録を$1", "logeventslist-smw-log": "Semantic MediaWiki ログ", "log-description-smw": "Semantic MediaWiki とその構成要素によって報告された、[https://www.semantic-mediawiki.org/wiki/Help:Logging 有効なイベントの種類]についての活動です。", "smw-datavalue-import-unknown-namespace": "インポート名前空間「$1」が不明です。OWL インポートの詳細を [[MediaWiki:Smw import $1]]経由で取得できるかどうか確認してください。", "smw-datavalue-import-missing-namespace-uri": "[[MediaWiki:Smw import $1|$1 インポート]]を検索しましたが「$1」名前空間URIは見つかりませんでした。", "smw-datavalue-import-invalid-value": "「$1」は有効な書式ではありません。\"名前空間\":\"識別子\"（例：「foaf:name」）から成ることが要求されています。", "smw-property-predefined-type": "「$1」は、プロパティの[[Special:Types|データ型]]を記述する定義済みプロパティです。これは [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] によって提供されています。", "smw-property-predefined-sobj": "「$1」は[https://www.semantic-mediawiki.org/wiki/Help:Container コンテナ]構造を表す定義済みのプロパティであり、[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] によって提供されています。", "smw-property-predefined-long-sobj": "コンテナはプロパティ・値の割り当てを蓄積することを可能にします。これは通常のページと同様ですが、埋め込んでいるテーマとリンクされ、異なるエンティティ空間の中で行われます。", "smw-datavalue-invalid-number": "「$1」は数値として解釈できません。", "smw-query-condition-circular": "「$1」において循環条件の可能性が検出されました。", "smw-types-list": "データ型の一覧", "smw-types-default": "「$1」は組み込みのデータ型です。", "smw-types-help": "追加の情報と例は、この[https://www.semantic-mediawiki.org/wiki/Help:Type_$1 ヘルプページ]で閲覧できます。", "smw-type-anu": "「$1」は [[Special:Types/URL|URL]] データ型の異形です。主に「owl:AnnotationProperty」の出力定義に使われます。", "smw-type-boo": "「$1」は真偽値を記述するための基本データ型です。", "smw-type-cod": "「$1」はソースコードリストのような任意の長さの技術的テキストに使われる [[Special:Types/Text|Text]] データ型の異形です。", "smw-type-geo": "「$1」は地理的な場所を記述するデータ型です。このデータ型は、拡張された機能を提供するために[https://www.semantic-mediawiki.org/wiki/Extension:Maps 「Maps」]拡張機能を必要とします。", "smw-type-tel": "「$1」は RFC 3966 に従った国際電話番号を記述するための特別なデータ型です。", "smw-type-txt": "「$1」は任意の長さの文字列を記述するための基本データ型です。", "smw-type-dat": "「$1」は統一された書式で時点を表すための基本データ型です。", "smw-type-ema": "「$1」はメールアドレスを表現する特別なデータ型です。", "smw-type-tem": "「$1」は温度を表現する特別なデータ型です。", "smw-type-qty": "「$1」は数値表現と測定単位を使用して数量を記述するためのデータ型です。", "smw-type-rec": "「$1」は型のあるプロパティを固定の順序で持つ一覧のコンテナ データ型です。", "smw-type-tab-properties": "プロパティ", "smw-type-tab-types": "型", "smw-type-tab-errors": "エラー", "smw-type-primitive": "基本型", "smw-type-contextual": "文脈型", "smw-type-compound": "複合型", "smw-type-container": "コンテナ型", "smw-type-no-group": "未分類", "smw-property-predefined-errt": "「$1」という定義済みのプロパティにはエラーの説明文が含まれ、[https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]から取得します。", "smw-property-predefined-mime": "「$1」は、アップロードされたファイルの MIME タイプを記述する定義済みプロパティです。これは [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] によって提供されています。", "smw-property-predefined-askst": "「$1」は、文字列としてクエリの条件を記述する定義済みプロパティです。これは [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki] によって提供されています。", "smw-datavalue-languagecode-missing": "「$1」注釈に対して、パーサは言語コード (例えば「foo@en」形式) を判断できませんでした。", "smw-type-mlt-rec": "「$1」は指定したテキスト値を[[Property:Language code|言語コード]]に関連付ける[https://www.semantic-mediawiki.org/wiki/Help:Container コンテナ] データ型です。", "smw-limitreport-intext-parsertime": "[SMW] テキスト内注記パーサー時間", "smw-limitreport-intext-parsertime-value": "$1{{PLURAL:$1|秒}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1{{PLURAL:$1|秒}}", "smw-property-predefined-long-pvuc": "2つの値が文による表現が等価ではない場合に重み付けをし、もしその制約に違反するとエラーに分類します。", "smw-constraint-violation-uniqueness": "<code>unique_value_constraint</code>制約が \"[[Property:$1|$1]]\" プロパティに割り当てられ、一意の値の割り当てのみを許可し、'' $2 '' 値の注釈は既に \" $3 \" サブジェクトで注釈が付けられています。", "smw-constraint-violation-uniqueness-isknown": "\"[[Property:$1|$1]]\" プロパティに<code>unique_value_constraint</code>制約が割り当てられた際に一意の値の注釈のみが許可されます。'' $2 '' には、現在のサブジェクトの一意性制約に違反する \" $3 \" の注釈付きの値が含まれています。", "smw-datavalue-time-invalid-jd": "入力値「$1」に対して JD (Julian day＝ユリウス日 ) 「$2」が返されたため解釈ができませんでした。", "smw-datavalue-time-invalid": "入力値「$1」の解釈を試みましたが「」Unable to interpret the \"$1\" input value as valid date or time component with \"$2\" being reported.", "smw-datavalue-external-formatter-invalid-uri": "「$1」は無効な URL です。", "smw-type-eid": "「$1」は外部リソース（URI ベース）を記述する [[Special:Types/Text|Text]] データ型の一種です。[[Property:External formatter uri|External formatter URI]] を記述する定義済みプロパティに使われます。", "smw-type-keyw": "「$1」は文字数が制限された正規化コンテンツ表現のための [[Special:Types/Text|Text]] データ型の一種です。", "smw-datavalue-stripmarker-parse-error": " \"$1  \"には [https://ja.wikipedia.org/wiki/Help:Strip_markers strip markers] が含まれており解析できません。", "smw-datavalue-propertylist-invalid-property-key": "プロパティリスト「$1」は無効なプロパティキー「$2」を含んでいました。", "smw-type-ref-rec": "「$1」は値の割り当てに関する追加の情報（例：出典データ）を記録することを可能にする [https://www.semantic-mediawiki.org/wiki/Container コンテナ] データ型です。", "smw-data-lookup-with-wait": "要求は処理中です。しばらく時間がかかることがあります。", "smw-no-data-available": "利用できるデータがありません。", "smw-property-req-violation-missing-maps-extension": "Semantic MediaWiki は不可欠な[https://www.semantic-mediawiki.org/wiki/Extension:Maps 「地図」Maps]拡張機能の検出に失敗し、結果としてこのプロパティの機能性に制限をかけました (つまり地理的データの保存もしくは処理が不可能。)", "smw-edit-protection": "このページは意図しないデータの変更を防止するため[[Property:Is edit protected|保護]]されており、適切な編集権限（「$1」）を持っているか適切な利用者グループに所属している利用者のみ編集できます。", "smw-edit-protection-auto-update": "Semantic MediaWiki は「Is edit protected」プロパティに従い保護状態を更新しました。", "smw-format-datatable-emptytable": "テーブルにデータがありません", "smw-format-datatable-info": "_TOTAL_ 件中 _START_ から _END_ まで表示", "smw-format-datatable-infoempty": "0 件中 0 から 0 まで表示", "smw-format-datatable-infofiltered": "（全 _MAX_ 件より抽出）", "smw-format-datatable-lengthmenu": "_MENU_ 件表示", "smw-format-datatable-loadingrecords": "読み込み中...", "smw-format-datatable-processing": "処理中...", "smw-format-datatable-search": "検索:", "smw-format-datatable-zerorecords": "一致するレコードがありません", "smw-format-datatable-first": "先頭", "smw-format-datatable-last": "最終", "smw-format-datatable-next": "次", "smw-format-datatable-previous": "前", "smw-format-datatable-sortascending": ": 列を昇順に並べ替えるにはアクティブにする", "smw-format-datatable-sortdescending": ": 列を降順に並べ替えるにはアクティブにする", "smw-format-datatable-toolbar-export": "エクスポート", "smw-api-invalid-parameters": "無効なパラメーター:「$1」", "smw-property-page-list-count": "このプロパティを使用している $1 {{PLURAL:$1|件のページ}}を表示しています。", "smw-property-page-list-search-count": "Showing $1 {{PLURAL:$1|page|pages}} using this property with a \"$2\" value match.\nこのプロパティを使用していて値が「$2」に一致した $1 {{PLURAL:$1|件のページ}}を表示しています。", "smw-property-page-filter-note": "[https://www.semantic-mediawiki.org/wiki/Help:Property_page/Filter search filter] では <code>~</code> や <code>!</code> などの [https://www.semantic-mediawiki.org/wiki/Help:Query_expressions query expressions] を使用できます。選択した [https://www.semantic-mediawiki.org/wiki/Query_engine query engine] では、大文字と小文字を区別しない検索や次のような短い式もサポートされているかもしれません:\n\n* <code>in:</code> - この文字列を結果に含む（例：'<code>in:Foo</code>'）\n\n* <code>not:</code> - この文字列を結果に含まない（例：'<code>not:Bar</code>'）", "smw-property-reserved-category": "カテゴリ", "smw-category": "カテゴリ", "smw-section-expand": "すべての節を展開", "smw-section-collapse": "節を折りたたむ", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1] 形式", "smw-help": "ヘルプ", "smw-cheat-sheet": "早見表", "smw-property-predefined-label-skey": "ソートキー", "smw-processing": "処理中...", "smw-loading": "読み込み中...", "smw-expand": "展開する", "smw-collapse": "折り畳む", "smw-copy": "コピー", "smw-schema-error-title": "検証 {{PLURAL:$1|エラー|エラー}}", "smw-ask-title-keyword-type": "キーワード検索", "smw-remote-source-unavailable": "リモートのターゲット「$1」に接続できませんでした。", "smw-parameter-missing": "パラメーター \"$1\" がありません。", "smw-property-tab-usage": "使用", "smw-property-tab-redirects": "同義語", "smw-property-tab-subproperties": "下位プロパティ", "smw-concept-tab-errors": "エラー", "smw-ask-tab-result": "結果", "smw-ask-tab-debug": "デバッグ", "smw-ask-tab-code": "コード", "smw-pendingtasks-tab-setup": "セットアップ", "smw-install-incomplete-elasticstore-indexrebuild": "<code>ElasticStore</code> を[https://www.semantic-mediawiki.org/wiki/Help:smwgDefaultStore 既定のストア]に選択しましたが、拡張機能は記録を検知できないため<code>[https://www.semantic-mediawiki.org/wiki/Help:rebuildElasticIndex.php rebuildElasticIndex.php]</code> スクリプトが発動、スクリプトを指示どおりに走らせてください。", "smw-es-replication-check": "複製を検査 (Elasticsearch)", "smw-es-replication-error-no-connection": "複製の監視において Elasticsearch への接続に失敗したため検査はまったく実施できませんでした。", "smw-es-replication-error-suggestions": "ページを編集して矛盾を除去する推奨されます。問題がそれでも残る場合は Elasticsearch クラスタ自体の確認をお願いします (アロケータ、例外状況、ディスク空間その他。)", "smw-es-replication-error-suggestions-exception": "ログを調べて Elasticsearch の状態、その索引、設定の錯誤の問題が発生していないか情報を確認してください。", "smw-es-replication-error-file-ingest-missing-file-attachment-suggestions": "注釈とファイル索引より以前に[https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/File_ingestion ファイル取得]ジョブの予定が確立していて実行されるかどうか確認してください。", "smw-report": "レポート", "smw-legend": "凡例", "smw-entity-examiner-indicator": "エンティティ問題パネル", "smw-indicator-revision-mismatch-error": "[https://www.semantic-mediawiki.org/wiki/Help:Associated_revision/Examiner 関連する改版]検査により、このエントリに対して MediaWiki が引用する改版と Semantic MediaWiki に紐付けされたものに不一致が見つかりました。", "smw-listingcontinuesabbrev": "の続き", "smw-showingresults": "<strong>$2</strong> 件目以降の最大 {{PLURAL:$1|<strong>$1</strong> 件の結果}}を表示しています。"}