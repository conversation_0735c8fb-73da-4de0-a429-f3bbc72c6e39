https://www.w3.org/TR/owl2-syntax/ | [https://www.w3.org/TR/owl2-syntax/ Web Ontology Language (OWL)]
 AllDifferent|Category
 allValuesFrom|Type:Page
 AnnotationProperty|Category
 backwardCompatibleWith|Type:Page
 cardinality|Type:Number
 Class|Category
 comment|Type:Page
 complementOf|Type:Page
 DataRange|Category
 DatatypeProperty|Category
 DeprecatedClass|Category
 DeprecatedProperty|Category
 differentFrom|Type:Page
 disjointWith|Type:Page
 distinctMembers|Type:Page
 equivalentClass|Type:Page
 equivalentProperty|Type:Page
 FunctionalProperty|Category
 hasValue|Type:Page
 imports|Type:Page
 incompatibleWith|Type:Page
 intersectionOf|Type:Page
 InverseFunctionalProperty|Category
 inverseOf|Type:Page
 label|Type:Page
 maxCardinality|Type:Number
 Members|Type:Page
 minCardinality|Type:Number
 Nothing|Category
 ObjectProperty|Category
 oneOf|Type:Page
 onProperty|Type:Page
 Ontology|Category
 OntologyProperty|Category
 owl|Type:Page
 priorVersion|Type:Page
 Restriction|Category
 sameAs|Type:Page
 seeAlso|Type:Page
 someValuesFrom|Type:Page
 SymmetricProperty|Category
 Thing|Category
 TransitiveProperty|Category
 unionOf|Type:Page
 versionInfo|Type:Page

[[Category:Imported vocabulary]]
