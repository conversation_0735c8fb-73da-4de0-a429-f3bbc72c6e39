{"@metadata": {"authors": ["*Youngjin", "<PERSON><PERSON><PERSON><PERSON>", "Aster15", "Bluehill", "Codenstory", "<PERSON><PERSON><PERSON>", "HDNua", "Hwangjy9", "Jerrykim306", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Kghbln", "Kwj2772", "McDut<PERSON><PERSON>", "MemphisA5", "Nemo bis", "Nuevo Paso", "Priviet", "<PERSON><PERSON>", "SeoJeongHo", "Suleiman the Magnificent Television", "Tensama0415", "<PERSON><PERSON><PERSON><PERSON>", "밥풀떼기", "아라"]}, "smw-desc": "위키를 기계와 사람이 더 접근하기 쉽게 합니다 ([https://www.semantic-mediawiki.org/wiki/Help:User_manual 온라인 설명서])", "smw-error": "오류", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ 시맨틱 미디어위키]가 설치되었고 활성화되었으나 적절한 [https://www.semantic-mediawiki.org/wiki/Help:Upgrade 업그레이드 키]가 없습니다.", "smw-upgrade-release": "배포판", "smw-upgrade-progress": "진행도", "smw-upgrade-progress-create-tables": "테이블과 인덱스를 생성(또는 업데이트)하는 중...", "smw-upgrade-progress-table-optimization": "테이블 최적화를 수행 중...", "smw-upgrade-error-title": "오류 » 시맨틱 미디어위키", "smw-upgrade-error-why-title": "이 페이지가 왜 표시됩니까?", "smw-upgrade-error-how-title": "이 오류를 어떻게 수정합니까?", "smw-extensionload-error-why-title": "이 페이지가 왜 표시됩니까?", "smw-extensionload-error-how-title": "이 오류를 어떻게 수정합니까?", "smw-upgrade-maintenance-title": "유지보수 » 시맨틱 미디어위키", "smw-upgrade-maintenance-why-title": "이 페이지가 왜 표시됩니까?", "smw-semantics-not-enabled": "시맨틱 미디어위키 기능은 이 위키에서 사용할 수 없습니다.", "smw_viewasrdf": "RDF 피드", "smw_finallistconjunct": ", 그리고", "smw-factbox-head": "... \"$1\"에 대하여 더 알아보기", "smw-factbox-facts": "상식", "smw-factbox-attachments": "첨부", "smw-factbox-attachments-value-unknown": "N/A", "smw-factbox-attachments-is-local": "로컬 여부", "smw-factbox-attachments-help": "사용 가능한 첨부 파일을 표시합니다", "smw_isspecprop": "이 속성은 이 위키에 특수한 속성입니다.", "smw-concept-cache-header": "캐시 사용률", "smw-concept-no-cache": "이용 가능한 캐시가 없습니다.", "smw_concept_description": "\"$1\" 개념의 설명", "smw_no_concept_namespace": "개념은 Concept: 이름공간의 문서에만 지정할 수 있습니다.", "smw_multiple_concepts": "각 개념 문서는 개념 정의 하나만 할 수 있습니다.", "smw_concept_cache_miss": "\"$1\" 개념은 위키 설정은 오프라인에서 처리해야 하기 때문에 현재는 사용할 수 없습니다.\n문제가 잠시 후 사라지지 않으면 이 개념을 사용할 수 있도록 사이트 관리자에게 문의하세요.", "smw_noinvannot": "값은 역 속성에 할당할 수 없습니다.", "version-semantic": "시맨틱 확장 기능", "smw_baduri": "\"$1\" 양식의 URI는 허용하지 않습니다.", "smw_printername_count": "개수 결과", "smw_printername_csv": "CSV 내보내기", "smw_printername_dsv": "DSV 내보내기", "smw_printername_debug": "(전문가용) 쿼리 디버그", "smw_printername_embedded": "문서 내용 포함", "smw_printername_json": "JSON 내보내기", "smw_printername_list": "목록", "smw_printername_plainlist": "단순 목록", "smw_printername_ol": "번호를 매긴 목록", "smw_printername_ul": "글머리 기호 목록", "smw_printername_table": "표", "smw_printername_broadtable": "넓은 표", "smw_printername_template": "틀", "smw_printername_templatefile": "틀 파일", "smw_printername_rdf": "PDF 내보내기", "smw_printername_category": "분류", "validator-type-class-SMWParamSource": "텍스트", "smw-paramdesc-limit": "반환할 결과의 최대 수", "smw-paramdesc-offset": "첫 결과의 오프셋", "smw-paramdesc-headers": "머리글/속성 이름 표시", "smw-paramdesc-mainlabel": "대문 이름으로 주어지는 레이블", "smw-paramdesc-link": "링크로 값 보이기", "smw-paramdesc-intro": "질문에 답이 있으면 쿼리 결과 앞에 표시할 텍스트", "smw-paramdesc-outro": "질문에 답이 있으면 쿼리 결과 뒤에 표시할 텍스트", "smw-paramdesc-default": "질문에 답이 없으면 표시할 텍스트", "smw-paramdesc-sep": "결과 간 구분자", "smw-paramdesc-propsep": "결과 엔트리 속성 간 구분자", "smw-paramdesc-valuesep": "결과 속성 값 간 구분자", "smw-paramdesc-showsep": "CSV 파일의 위에 구분자를 보이기 (\"sep=<값>\")", "smw-paramdesc-distribution": "모든 값을 표시하는 대신 값의 발생을 세서 보여줍니다.", "smw-paramdesc-distributionsort": "발생 횟수로 값 분포를 정렬합니다.", "smw-paramdesc-distributionlimit": "일부 값만의 횟수로 값 분포를 제한합니다.", "smw-paramdesc-template": "인쇄 출력과 함께 표시할 틀의 이름", "smw-paramdesc-columns": "결과를 표시할 열의 수", "smw-paramdesc-userparam": "틀이 사용되는 경우 각 틀 호출에 전달되는 값입니다", "smw-paramdesc-class": "리스트에 설정할 추가적인 CSS 클래스", "smw-paramdesc-introtemplate": "질문에 답이 있으면 쿼리 결과 앞에 표시할 틀의 이름", "smw-paramdesc-outrotemplate": "질문에 답이 있으면 쿼리 결과 뒤에 표시할 틀의 이름", "smw-paramdesc-embedformat": "머리글을 정의하는 데 사용하는 HTML 태그", "smw-paramdesc-embedonly": "머리글을 표시하지 않음", "smw-paramdesc-table-class": "표에 설정할 추가적인 CSS 클래스", "smw-paramdesc-table-transpose": "표 머리말은 수직으로, 결과는 수평으로 표시합니다", "smw-paramdesc-rdfsyntax": "사용할 RDF 구문", "smw-paramdesc-csv-sep": "컬럼 구분자를 지정합니다", "smw-paramdesc-csv-valuesep": "값 구분자를 지정합니다", "smw-paramdesc-dsv-separator": "사용할 구분자", "smw-paramdesc-dsv-filename": "DSV 파일의 이름", "smw-paramdesc-filename": "출력 파일의 이름", "smw-smwdoc-description": "기본 값과 설명과 함께 지정한 결과 형식에 사용할 수 있는 모든 변수의 표를 보여줍니다.", "smw-smwdoc-default-no-parameter-list": "이 결과 포맷은 포맷에 특화된 변수를 제공하고 있지 않습니다.", "smw-smwdoc-par-format": "변수 설명문서를 표시할 결과 형식입니다.", "smw-smwdoc-par-parameters": "보여줄 어떤 변수입니다. 형식에 추가한 변수에 대해서는 \"specific\", 모든 형식을 사용할 수 있는 변수에 대해서는 \"base\", 둘 다는 \"all\"입니다.", "smw-paramdesc-sort": "쿼리를 정렬하는 속성", "smw-paramdesc-order": "쿼리 정렬의 순서", "smw-paramdesc-searchlabel": "검색을 계속하기에 대한 텍스트", "smw-paramdesc-named_args": "틀에 전달한 인수의 이름을 지정", "smw-paramdesc-export": "내보내기 옵션", "smw-paramdesc-prettyprint": "추가적인 들여 쓰기와 줄 바꿈을 보여주는 예쁜 인쇄 출력", "smw-paramdesc-json-type": "직렬화 유형", "smw-paramdesc-source": "대체 쿼리 소스", "smw-paramdesc-jsonsyntax": "사용할 JSON 구문", "smw-printername-feed": "RSS 및 아톰 피드", "smw-paramdesc-feedtype": "피드 유형", "smw-paramdesc-feedtitle": "피드의 제목으로 사용할 텍스트", "smw-paramdesc-feeddescription": "피드의 설명으로 사용할 텍스트", "smw-paramdesc-feedpagecontent": "피드에 보여줄 문서 내용", "smw-label-feed-description": "$1 $2 피드", "smw-paramdesc-mimetype": "출력 파일의 미디어 타입(MIME 타입)", "smw_iq_disabled": "시맨틱 쿼리는 이 위키에 비활성화되었습니다.", "smw_iq_moreresults": "… 다른 결과", "smw_parseerror": "지정한 값은 이해할 수 없습니다.", "smw_notitle": "\"$1\"은(는) 이 위키에서 문서 이름으로 사용할 수 없습니다.", "smw_noproperty": "\"$1\"은(는) 이 위키에서 속성 이름으로 사용할 수 없습니다.", "smw_wrong_namespace": "\"$1\" 이름공간의 문서만 여기에 허용합니다.", "smw_manytypes": "속성에 정의한 하나 이상의 유형입니다.", "smw_emptystring": "빈 문자열은 허용하지 않습니다.", "smw_notinenum": "\"$1\"은(는) \"$3\" 속성의 [[Property:Allows value|허용된 값]] 목록($2)에 없습니다.", "smw-datavalue-constraint-error-allows-value-list": "\"$1\"은(는) \"$3\" 속성의 [[Property:Allows value|허용된 값]] 목록($2)에 없습니다.", "smw-datavalue-constraint-error-allows-value-range": "\"$1\"은(는) \"$3\" 속성의 [[Property:Allows value|허용값]] 제한을 통해 지정된 \"$2\"의 범위에 속하지 않습니다.", "smw-constraint-error-limit": "목록은 최대 $1개의 위반을 포함합니다.", "smw_noboolean": "\"$1\"은(는) 불리언(참/거짓) 값으로 인식하지 않습니다.", "smw_true_words": "true,t,yes,y", "smw_false_words": "false,f,no,n", "smw_nofloat": "\"$1\"은(는) 숫자가 아닙니다.", "smw_infinite": "\"$1\" 크기와 같은 숫자는 지원하지 않습니다.", "smw_unitnotallowed": "\"$1\"은(는) 이 속성에 대한 측정의 올바른 단위로 선언되어 있지 않습니다.", "smw_nounitsdeclared": "이 속성에 대해 선언한 측정의 단위가 없습니다.", "smw_novalues": "지정한 값이 없습니다.", "smw_nodatetime": "\"$1\" 날짜는 이해할 수 없습니다.", "smw_toomanyclosing": "쿼리에 \"$1\"의 사건이 너무 많은 것 같습니다.", "smw_noclosingbrackets": "쿼리에 \"<nowiki>[[</nowiki>\"에 해당하는 \"]]\"를 닫지 않았습니다.", "smw_misplacedsymbol": "\"$1\" 기호는 유용하지 않은 곳에 사용했습니다.", "smw_unexpectedpart": "쿼리의 \"$1\" 부분은 이해할 수 없습니다.\n결과는 예상하지 않을 수 있습니다.", "smw_emptysubquery": "일부 하위 쿼리는 올바른 조건이 없습니다.", "smw_misplacedsubquery": "일부 하위 쿼리는 하위 쿼리를 허용하지 않은 곳에 사용했습니다.", "smw_valuesubquery": "하위 쿼리는 \"$1\" 속성의 값에 지원하지 않습니다.", "smw_badqueryatom": "쿼리의 일부 \"<nowiki>[[…]]</nowiki>\" 부분은 이해할 수 없습니다.", "smw_propvalueproblem": "\"$1\" 속성의 값은 이해할 수 없습니다.", "smw_noqueryfeature": "일부 쿼리 기능은 이 위키에서 지원하지 않아 쿼리의 부분을 없앴습니다. ($1)", "smw_noconjunctions": "쿼리의 접속사는 이 위키에서 지원하지 않아 쿼리의 부분을 없앴습니다. ($1)", "smw_nodisjunctions": "쿼리의 분리사는 이 위키에서 지원하지 않아 쿼리의 부분을 없앴습니다. ($1)", "smw_querytoolarge": "쿼리의 크기나 깊이에 대한 위키의 제한으로 인해 다음의 {{PLURAL:$2|쿼리 조건은|쿼리 조건 $2개는}} 고려하지 못했습니다: <code>$1</code>.", "smw_notemplategiven": "작동할 이 쿼리 형식에 \"틀\" 변수에 대한 값을 제공하세요.", "smw_db_sparqlqueryproblem": "쿼리 결과는 SPARQL 데이터베이스에서 가져올 수 없습니다. 이 오류는 일시적이거나 데이터베이스 소프트웨어의 버그일 수 있습니다.", "smw_db_sparqlqueryincomplete": "쿼리 응답이 너무 어려운 것으로 밝혀져 중단되었습니다. 일부 결과는 사라졌을 수 있습니다. 가능하면 간단한 쿼리를 대신 사용하세요.", "smw_type_header": "\"$1\" 유형의 속성", "smw_typearticlecount": "이 유형을 사용하여 {{PLURAL:$1|속성}} $1개를 보여줍니다.", "smw_attribute_header": "\"$1\" 유형을 사용한 문서", "smw_attributearticlecount": "이 속성을 사용하는 {{PLURAL:$1|문서}} $1개를 보여줍니다.", "smw-propertylist-subproperty-header": "하위 속성", "smw-propertylist-redirect-header": "동의어", "smw-propertylist-count": "$1 관련 {{PLURAL:$1|엔티티}}를 표시합니다.", "smw-propertylist-count-with-restricted-note": "$1 관련 {{PLURAL:$1|엔티티}}를 표시합니다. (더 많은 엔티티의 사용이 가능하나 표시는 \"$2\"에 국한됩니다)", "smw-propertylist-count-more-available": "$1 관련 {{PLURAL:$1|엔티티}}를 표시합니다. (더 많은 엔티티를 볼 수 있습니다)", "specialpages-group-smw_group": "시맨틱 미디어위키", "specialpages-group-smw_group-maintenance": "유지보수", "specialpages-group-smw_group-search": "찾아보기 및 검색", "exportrdf": "RDF로 문서 내보내기", "smw_exportrdf_docu": "이 문서는 RDF 형식의 문서에서 데이터를 얻을 수 있습니다.\n문서를 내보내려면 아래 텍스트 상자에 줄마다 제목 하나를 입력하세요.", "smw_exportrdf_recursive": "모든 관련된 문서를 재귀적으로 내보냅니다.\n결과가 커질 수 있음을 참고하세요!", "smw_exportrdf_backlinks": "또한 내보낸 문서를 참고하는 모든 문서를 내보냅니다.\n찾아볼 수 있는 RDF를 생성합니다.", "smw_exportrdf_lastdate": "지정한 시간 이후에 바뀌지 않은 문서는 내보내지 않습니다.", "smw_exportrdf_submit": "내보내기", "uriresolver": "URI해결", "properties": "속성 목록", "smw-categories": "분류 목록", "smw_properties_docu": "다음 속성은 위키에서 사용합니다.", "smw_property_template": "$2 유형의 $1 ($3개 {{PLURAL:$3|사용}})", "smw_propertylackspage": "모든 속성은 문서에서 설명해야 합니다.", "smw_propertylackstype": "이 속성에 지정한 유형이 없습니다. (지금은 $1 유형으로 가정합니다)", "smw_propertyhardlyused": "이 속성은 거의 위키 안에 사용하지 않습니다!", "smw-property-name-invalid": "$1 속성은 사용할 수 없습니다. (잘못된 속성 이름)", "smw-property-name-reserved": "\"$1\"은(는) 예약어로 등재되었으므로 속성으로 사용해서는 안 됩니다. 다음의 [https://www.semantic-mediawiki.org/wiki/Help:Property_naming 도움말 문서]에는 이 이름이 예약된 이유에 관한 정보가 포함되어 있을 수 있습니다.", "smw-sp-property-searchform": "다음을 포함하는 속성 표시:", "smw-sp-property-searchform-inputinfo": "입력은 대소문자를 구분하여 필터에 사용되며, 조건과 일치하는 속성만 보입니다.", "smw-special-property-searchform": "다음을 포함하는 속성 표시:", "smw-special-property-searchform-inputinfo": "입력은 대소문자를 구분하여 필터에 사용되며, 조건과 일치하는 속성만 보입니다.", "smw-special-property-searchform-options": "옵션", "smw-special-wantedproperties-filter-label": "필터:", "smw-special-wantedproperties-filter-none": "없음", "smw-special-wantedproperties-filter-unapproved": "미승인", "smw-special-wantedproperties-filter-unapproved-desc": "전거 모드와 관련되어 사용되는 필터 옵션입니다.", "concepts": "개념", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Concepts 개념]은 \"동적 분류\", 즉 수동으로 만들어지지 않지만 주어진 쿼리의 설명에서 시맨틱 미디어위키가 계산한 문서의 모음집으로 볼 수 있습니다.", "smw-special-concept-header": "개념 목록", "smw-special-concept-count": "다음 {{PLURAL:$1|개념|개념 $1개}}{{PLURAL:$1|은|는}} 나열되어 있습니다.", "smw-special-concept-empty": "개념을 찾을 수 없습니다.", "unusedproperties": "사용하지 않는 속성 목록", "smw-unusedproperties-docu": "이 문서는 선언은 되었으나 다른 문서에서 사용하지 않는 [https://www.semantic-mediawiki.org/wiki/Unused_properties 미사용 속성]을 나열합니다. 차별화된 뷰에 대해서는 [[Special:Properties|전체]] 또는 [[Special:WantedProperties|필요 속성]] 특수 문서를 참고하십시오.", "smw-unusedproperty-template": "$2 유형의 $1", "wantedproperties": "필요한 속성 목록", "smw-wantedproperties-docu": "이 문서는 위키에는 쓰이지만 설명하는 문서가 없는 [https://www.semantic-mediawiki.org/wiki/Wanted_properties 필요 속성]을 나열합니다. 차별화된 뷰에 대해서는 [[Special:Properties|전체]] 또는 [[Special:UnusedProperties|미사용 속성]] 특수 문서를 참고하십시오.", "smw-wantedproperty-template": "$1 ($2개 {{PLURAL:$2|사용}})", "smw-special-wantedproperties-template": "$1 ($2개 {{PLURAL:$2|사용}})", "smw_purge": "새로 고침", "smw-purge-failed": "시맨틱 미디어위키가 페이지 새로 고침을 시도하였으나 실패했습니다", "types": "유형 목록", "smw_types_docu": "할당된 속성에 상속되는 저장 및 표시 기능에 따라 값을 기술하기 위해 각각의 [https://www.semantic-mediawiki.org/wiki/Help:Datatype 자료형]에 고유한 속성 집합을 표현한 [https://www.semantic-mediawiki.org/wiki/Help:List_of_datatypes 사용 가능한 자료형] 목록입니다.", "smw-special-types-no-such-type": "\"$1\"은(는) 알 수 없거나 지정된 자료형으로 지정되어 있지 않습니다.", "smw-statistics": "시맨틱 통계", "smw-statistics-cached": "시맨틱 통계 (캐시됨)", "smw-statistics-entities-total": "엔티티 (전체)", "smw-statistics-property-instance": "(총) 속성 {{PLURAL:$1|값}}", "smw-statistics-property-total": "(총) [[Special:Properties|{{PLURAL:$1|속성}}]]", "smw-statistics-property-total-legacy": "(총) {{PLURAL:$1|속성}}", "smw-statistics-property-used": "{{PLURAL:$1|Property|Properties}} (적어도 하나의 값과 함께 사용됨)", "smw-statistics-property-page": "(문서에 등록된) {{PLURAL:$1|속성}}", "smw-statistics-property-type": "(데이터유형으로 할당된) {{PLURAL:$1|속성}}", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|쿼리|쿼리}}", "smw-statistics-query-inline": "[[Property:Has query| {{PLURAL:$1| Query|Queries}}]] (내장형, 전체)", "smw-statistics-query-format": "<code>$1</code> 포맷", "smw-statistics-query-size": "쿼리 크기", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|개념}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|개념}}]]", "smw-statistics-subobject-count": "[[Property:Has subobject|{{PLURAL:$1|하위개체|하위개체}}]]", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|데이터유형}}]]", "smw-statistics-delete-count": "[https://www.semantic-mediawiki.org/wiki/Help:Outdated_entities 오래됨 {{PLURAL:$1|entity|entities}}]", "smw_uri_doc": "URI 해결기는 [$1 W3C TAG finding on httpRange-14]를 구현합니다.\n요청에 따라 RDF 표현(기계용) 또는 위키 페이지(사람용)가 전달되도록 합니다.", "ask": "시맨틱 검색", "smw_ask_sortby": "열 순서로 정렬 (선택 사항)", "smw_ask_ascorder": "오름차순", "smw_ask_descorder": "내림차순", "smw-ask-order-rand": "무작위 순서로", "smw_ask_submit": "결과 찾기", "smw_ask_editquery": "쿼리 편집", "smw_add_sortcondition": "[정렬 조건 추가]", "smw-ask-sort-add-action": "정렬 조건 추가", "smw_ask_hidequery": "쿼리 숨기기 (간략히 보기)", "smw_ask_help": "쿼리 도움말", "smw_ask_queryhead": "조건", "smw_ask_printhead": "출력 선택", "smw_ask_printdesc": "(줄마다 속성 이름 하나를 추가하세요)", "smw_ask_format_as": "서식:", "smw_ask_defaultformat": "기본값", "smw_ask_otheroptions": "다른 옵션", "smw-ask-otheroptions-info": "이 문단은 출력 문을 바꾸는 옵션이 있습니다. 변수 설명은 변수 위로 가리켜 볼 수 있습니다.", "smw-ask-otheroptions-collapsed-info": "사용 가능한 모든 옵션을 보려면 더하기 아이콘을 사용하세요", "smw_ask_show_embed": "포함한 코드 보이기", "smw_ask_hide_embed": "포함한 코드 숨기기", "smw_ask_embed_instr": "이 쿼리를 위키 문서에 인라인으로 포함하려면 다음 코드를 사용하세요.", "smw-ask-delete": "제거", "smw-ask-sorting": "정렬", "smw-ask-options": "옵션", "smw-ask-options-sort": "정렬 옵션", "smw-ask-format-options": "형식 및 옵션", "smw-ask-parameters": "변수", "smw-ask-search": "검색", "smw-ask-debug": "디버그", "smw-ask-debug-desc": "쿼리 디버그 정보를 생성합니다", "smw-ask-no-cache": "쿼리 캐시 비활성화", "smw-ask-no-cache-desc": "쿼리 캐시가 없는 결과", "smw-ask-result": "결과", "smw-ask-empty": "모든 항목 비우기", "smw-ask-download-link-desc": "조회된 결과를 $1 형식으로 다운로드합니다", "smw-ask-format": "형식", "smw-ask-format-selection-help": "선택된 형식의 도움말: $1", "smw-ask-condition-change-info": "조건이 변경되었으므로 검색 엔진은 새로운 요구사항을 충족하는 결과를 출력하기 위해 쿼리를 다시 실행해야 합니다.", "smw-ask-input-assistance": "입력 보조", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 입력 지원]은 출력, 정렬, 조건 필드를 위해 제공됩니다. 이 조건 필드는 다음의 접두사 중 하나를 사용해야 합니다:", "smw-ask-condition-input-assistance-property": "속성 제안을 가져오려면 <code>p:</code> (예: <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "분류 제안을 가져오려면 <code>c:</code>", "smw-ask-condition-input-assistance-concept": "개념 제안을 가져오려면 <code>con:</code>", "smw-ask-format-change-info": "포맷이 수정되었으므로 새로운 변수와 시각화 옵션을 일치시키려면 쿼리를 다시 실행해야 합니다.", "smw-ask-format-export-info": "선택된 형식은 시각 표현이 없는 내보내기 형식이므로 결과는 다운로드로만 제공됩니다.", "smw-ask-extra-query-log": "쿼리 로그", "smw-ask-extra-other": "기타", "searchbyproperty": "속성으로 검색", "processingerrorlist": "오류 목록 처리중", "constrainterrorlist": "제약조건 오류 목록", "propertylabelsimilarity": "속성 레이블 유사성 보고서", "smw-missingredirects-list": "주석이 없는 문서", "smw-missingredirects-list-intro": "넘겨주기 주석이 없는 $1개 {{PLURAL:$1|문서}}를 표시합니다.", "smw-missingredirects-noresult": "누락된 넘겨주기 주석이 없습니다.", "smw_sbv_docu": "주어진 속성과 값을 가지고 모든 문서를 검색합니다.", "smw_sbv_novalue": "속성에 대한 올바른 값을 입력하거나 \"$1\"에 대한 모든 속성 값을 보세요.", "smw_sbv_displayresultfuzzy": "\"$2\" 값의 \"$1\" 속성을 가진 모든 문서의 목록입니다.\n결과가 얼마 안 되기 때문에 주변의 값을 표시합니다.", "smw_sbv_property": "속성:", "smw_sbv_value": "값:", "smw_sbv_submit": "결과 찾기", "browse": "위키 찾아보기", "smw_browselink": "속성 찾아보기", "smw_browse_article": "찾아보기를 시작하려면 문서의 이름을 입력하세요.", "smw_browse_go": "보기", "smw_browse_show_incoming": "들어오는 속성 표시", "smw_browse_hide_incoming": "들어오는 속성 숨기기", "smw_browse_no_outgoing": "이 문서에는 속성이 없습니다.", "smw_browse_no_incoming": "이 문서에 링크한 속성이 없습니다.", "smw-browse-from-backend": "정보는 현재 백엔드에서 검색되고 있습니다.", "smw-browse-api-subject-serialization-invalid": "이 주제에는 유효하지 않은 직렬화 포맷이 있습니다.", "smw-browse-js-disabled": "자바스크립트가 비활성화되어 있거나 사용할 수 없는 것으로 의심됩니다. 지원하는 브라우저를 사용할 것을 권고합니다. 다른 옵션은 [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>] 구성 변수 문서에서 논의됩니다.", "smw-browse-show-group": "그룹 표시", "smw-browse-hide-group": "그룹 숨기기", "smw_inverse_label_default": "$1입니다", "smw_inverse_label_property": "역 속성 레이블", "pageproperty": "문서 속성 검색", "pendingtasklist": "보류 중인 작업 목록", "facetedsearch": "패싯 검색", "smw_pp_docu": "문서와 속성을 입력하십시오. 할당된 모든 값을 가져오려면 속성만 입력하십시오.", "smw_pp_from": "문서에서:", "smw_pp_type": "속성:", "smw_pp_submit": "결과 찾기", "smw-prev": "이전 {{PLURAL:$1|$1개}}", "smw-next": "다음 {{PLURAL:$1|$1개}}", "smw_result_prev": "이전", "smw_result_next": "다음", "smw_result_results": "결과", "smw_result_noresults": "결과가 없습니다.", "smwadmin": "시맨틱 미디어위키 대시보드", "smw-admin-statistics-job-title": "작업 통계", "smw-admin-statistics-querycache-title": "쿼리 캐시", "smw-admin-statistics-semanticdata-overview": "개요", "smw-admin-setupsuccess": "저장소 엔진을 설정했습니다.", "smw_smwadmin_return": "$1 문서로 돌아갑니다", "smw_smwadmin_updatestarted": "시맨틱 데이터의 최신화를 위한 새 업데이트 과정이 시작되었습니다.\n저장된 모든 데이터는 필요에 따라 다시 구성되거나 복구됩니다.\n이 특수 문서에 대한 업데이트의 진행을 따를 수 있습니다.", "smw_smwadmin_updatenotstarted": "이미 실행 중인 업데이트 프로세스가 있습니다.\n새로 만들지 않습니다.", "smw_smwadmin_updatestopped": "기존의 모든 업데이트 과정이 중단되었습니다.", "smw_smwadmin_updatenotstopped": "실행하고 있는 업데이트 과정을 중지하려면 정말 확실한지 나타내는 확인란을 활성화해야 합니다.", "smw-admin-docu": "이 특수 문서는 <a href=\"https://www.semantic-mediawiki.org\">시맨틱 미디어위키</a>의 설치, 업그레이드, 정비, 사용 중에 도움을 주며 추가적인 관리 기능과 작업, 통계 또한 제공합니다. 관리 기능을 실행하기 전에 중요한 데이터는 백업해 주십시오.", "smw-admin-environment": "소프트웨어 환경", "smw-admin-db": "데이터베이스 설정", "smw-admin-db-preparation": "테이블 초기화가 진행 중이며 크기 및 잠재적인 테이블 최적화가 대기 중이므로 결과가 표시되는데 시간이 걸릴 수 있습니다.", "smw-admin-dbdocu": "시맨틱 미디어위키는 시멘틱 데이터를 저장하기 위해 자체 데이터베이스 구조가 필요합니다 (미디어위키와 독립적이므로 나머지 미디어위키 설치에 영향을 미치지 않음).\n이 설정 기능은 어떠한 피해 없이 여러 번 실행될 수 있지만, 설치나 업그레이드에 한 번만 필요합니다.", "smw-admin-permissionswarn": "SQL 오류로 작업이 실패했다, 위키가 고용한 데이터베이스 사용자(LocalSettings.php 파일을 확인하세요)는 아마도 충분한 권한이 없을 것입니다.\n테이블을 만들고 삭제할 추가 권한을 이 사용자에게 부여하거나, 일시적으로 LocalSettings.php에 데이터베이스 루트의 로그인을 입력하거나, 관리자의 자격 증명을 사용할 수 있는 <code>setupStore.php</code> 유지 관리 스크립트를 사용하세요.", "smw-admin-dbbutton": "표를 초기화하거나 업그레이드", "smw-admin-announce": "내 위키 발표", "smw-admin-deprecation-notice-title": "구식 알림", "smw-admin-deprecation-notice-config-notice": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>은(는) 구식이므로 $2에서 제거되었습니다", "smw-admin-deprecation-notice-config-notice-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>은(는) \n다음의 {{PLURAL:$2|옵션}}을 제거(또는 대체)할 것입니다:", "smw-admin-deprecation-notice-config-notice-option-list": "<code>$1</code>은(는) 사용되지 않으며 $2에 제거됩니다", "smw-admin-deprecation-notice-config-replacement": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>은(는) <code>[https://www.semantic-mediawiki.org/wiki/Help:$2 $2]</code>으(로) 변경되었습니다", "smw-admin-deprecation-notice-config-replacement-option": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code> {{PLURAL:$2|옵션}}:", "smw-admin-deprecation-notice-config-replacement-option-list": "<code>$1</code>은(는) <code>$2</code>(으)로 대체되고 있습니다", "smw-admin-deprecation-notice-config-removal": "<code>[https://www.semantic-mediawiki.org/wiki/Help:$1 $1]</code>이(가) 제거되었습니다 ($2)", "smw-admin-deprecation-notice-title-notice": "더 이상 사용되지 않는 설정", "smw-admin-deprecation-notice-title-notice-explanation": "<b>더 이상 사용하지 않는 설정</b>는 향후 릴리스에서 제거되거나 변경될 예정인 설정을 이 위키에서 사용되는 것으로 감지되어 표시합니다.", "smw-admin-deprecation-notice-title-replacement": "치환되거나 이름이 변경된 설정", "smw-admin-deprecation-notice-title-replacement-explanation": "<b>치환되거나 이름이 변경된 설정</b>에는 이름이 바뀌거나 다른 방식으로 수정된 설정이 포함되어 있으므로 해당 이름이나 형식을 업데이트하는 것이 좋습니다.", "smw-admin-deprecation-notice-title-removal": "제거된 설정", "smw-admin-deprecation-notice-title-removal-explanation": "<b>제거된 설정</b>는 이전 릴리스에서 제거되었지만 이 위키에서 사용되는 것으로 감지된 설정을 식별합니다.", "smw-admin-deprecation-notice-section-legend": "범례", "smw-smwadmin-refresh-title": "데이터 복구 및 업데이트", "smw_smwadmin_datarefresh": "데이터 재생성", "smw_smwadmin_datarefreshdocu": "위키의 현재 내용에 따라 모든 시맨틱 미디어위키 데이터를 복구할 수 있습니다.\n안쪽 형식이 어떤 소프트웨어 업그레이드로 인해 바뀌었으면 깨진 데이터를 복구하거나 데이터를 새로 고치는 데 유용할 수 있습니다.\n업데이트는 문서 단위로 실행되고 즉시 완료되지 않습니다.\n다음은 업데이트가 진행하고 있으면 보여주고 (이 기능이 사이트 관리자가 비활성화하지 않는 한) 업데이트를 시작하거나 멈출 수 있습니다.", "smw_smwadmin_datarefreshprogress": "<strong>업데이트가 이미 진행 중입니다.</strong>\n사용자가 위키에 접근할 때마다 작은 덩어리의 새로 고침 데이터 진행만 있기 때문에 업데이트 진행이 느린 것은 정상입니다.\n더 빠르게 이 업데이트를 마치려면 <code>runJobs.php</code> 미디어위키 유지 보수 스크립트를 호출할 수 있습니다. (일괄 하나에서 수행하는 업데이트의 수를 제한하려면 <code>--maxjobs 1000</code> 옵션을 사용하세요)\n현재 업데이트의 예상된 진행:", "smw_smwadmin_datarefreshbutton": "데이터 다시 빌드 예약", "smw_smwadmin_datarefreshstop": "이 업데이트 중지", "smw_smwadmin_datarefreshstopconfirm": "예, {{GENDER:$1|확실합니다}}.", "smw-admin-outdateddisposal-active": "만기된 항목의 제거 작업이 예약되었습니다.", "smw-admin-outdateddisposal-button": "제거 예약", "smw-admin-feature-disabled": "이 기능은 이 위키에서 비활성화되어 있으므로 <a href=\"https://www.semantic-mediawiki.org/wiki/Help:$smwgAdminFeatures\">설정</a> 도움말 페이지를 참조하거나 시스템 관리자에게 문의하십시오.", "smw-admin-propertystatistics-active": "속성 통계 다시 빌드 작업이 예약되었습니다.", "smw-admin-fulltext-title": "전문 검색 재구성", "smw-admin-fulltext-active": "전문 검색 다시 빌드 작업이 예약되었습니다.", "smw-admin-support": "지원 얻기", "smw-admin-supportdocu": "문제가 있을 때 도움이 될 만한 다양한 자료가 제공됩니다:", "smw-admin-installfile": "설치에 문제가 있는 경우 <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md\">설치 파일</a>과 <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Installation\">설치 페이지</a>의 지침을 확인하세요.", "smw-admin-smwhomepage": "시맨틱 미디어위키의 완전한 사용자 설명서는 <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b>에 있습니다.", "smw-admin-bugsreport": "버그는 <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">이슈 트래커</a>에 신고할 수 있으며, <a href=\"https://www.semantic-mediawiki.org/wiki/Help:Reporting_bugs\">버그 신고하기</a> 페이지에서 효과적인 이슈 보고서 작성 방법에 대한 몇 가지 지침을 확인할 수 있습니다.", "smw-admin-questions": "추가 질문이나 제안 사항이 있으면 시맨틱 미디어위키 <a href=\"https://sourceforge.net/p/semediawiki/mailman/semediawiki-user/\">사용자 메일링 리스트</a>에서 토론에 참여하세요.", "smw-admin-other-functions": "다른 기능", "smw-admin-statistics-extra": "통계 함수", "smw-admin-statistics": "통계", "smw-admin-supplementary-section-title": "추가 기능", "smw-admin-supplementary-section-subtitle": "지원되는 핵심 기능", "smw-admin-supplementary-settings-title": "구성 및 설정", "smw-admin-supplementary-settings-intro": "<u>$1</u>은 시맨틱 미디어위키의 동작을 정의하는 매개변수를 보여줍니다.", "smw-admin-main-title": "시맨틱 미디어위키 » $1", "smw-admin-supplementary-operational-statistics-title": "조작 통계", "smw-admin-supplementary-operational-statistics-intro": "<u>$1</u>의 확장된 집합을 표시합니다", "smw-admin-supplementary-idlookup-title": "엔티티 검색 및 처리", "smw-admin-supplementary-idlookup-short-title": "엔티티 검색 및 제거", "smw-admin-supplementary-idlookup-intro": "단순한 <u>$1</u> 기능을 지원합니다", "smw-admin-supplementary-duplookup-title": "중복 엔티티 확인", "smw-admin-supplementary-duplookup-docu": "이 문서는 [https://www.semantic-mediawiki.org/wiki/Help:Entity_table 개체 표]에서 중복된 것으로 분류된 개체들의 목록입니다. 중복 개체들은 (존재한다면) 데이터베이스 업데이트 중에 프로세스가 종료되거나 롤백 행동이 성공스럽지 못했을 때와 같이 매우 드문 경우에만 발생할 수 있습니다.", "smw-admin-supplementary-operational-statistics-cache-title": "캐시 통계", "smw-admin-supplementary-operational-statistics-cache-intro": "<u>$1</u>은(는) 캐시 관련 통계를 표시합니다", "smw-admin-supplementary-operational-table-statistics-title": "테이블 통계", "smw-admin-supplementary-operational-table-statistics-short-title": "테이블 통계", "smw-admin-supplementary-elastic-title": "일래스틱서치", "smw-admin-supplementary-elastic-version-info": "버전", "smw-admin-supplementary-elastic-section-subtitle": "일래스틱서치", "smw-admin-supplementary-elastic-docu": "이 문서는 시맨틱 미디어위키와 [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore <code>일래스틱스토어</code>]에 연결된 일래스틱서치 클러스터 관련 설정, 매핑, 상태, 색인 통계에 관한 정보를 포함합니다.", "smw-admin-supplementary-elastic-functions": "지원되는 기능", "smw-admin-supplementary-elastic-settings-title": "설정 (인덱스)", "smw-admin-supplementary-elastic-mappings-title": "매핑", "smw-admin-supplementary-elastic-mappings-summary": "요약", "smw-admin-supplementary-elastic-mappings-fields": "필드 매핑", "smw-admin-supplementary-elastic-nodes-title": "노드", "smw-admin-supplementary-elastic-nodes-intro": "<u>$1</u>은(는) 노드 통계를 표시합니다", "smw-admin-supplementary-elastic-indices-title": "색인", "smw-admin-supplementary-elastic-statistics-title": "통계", "smw-admin-supplementary-elastic-status-replication": "레플리케이션 상태", "smw-admin-supplementary-elastic-status-refresh-interval": "새로 고침 주기: $1", "smw-admin-supplementary-elastic-replication-function-title": "복제", "smw-admin-supplementary-elastic-replication-files": "파일", "smw-admin-supplementary-elastic-replication-pages": "페이지", "smw-admin-supplementary-elastic-endpoints": "엔드포인트", "smw-admin-supplementary-elastic-config": "구성", "smw-list-count": "이 목록은 {{PLURAL:$1|항목}} $1개를 갖고 있습니다.", "smw-property-label-similarity-title": "속성 레이블 유사성 보고서", "smw-property-label-similarity-intro": "<u>$1</u> 문서는 기존 속성 레이블의 유사성을 계산합니다", "smw-property-label-similarity-threshold": "한계치:", "smw-property-label-similarity-type": "표시 유형 ID", "smw-property-label-similarity-noresult": "선택된 옵션에서는 아무 결과도 발견되지 않았습니다.", "smw_adminlinks_datastructure": "데이터 구조", "smw_adminlinks_displayingdata": "데이터 표시", "smw_adminlinks_inlinequerieshelp": "인라인 쿼리 도움말", "smw-property-indicator-type-info": "{{PLURAL:$1|사용자|시스템}} 정의된 속성", "smw-concept-indicator-cache-update": "캐시 카운트\n마지막 업데이트: $1", "smw-createproperty-isproperty": "$1 유형의 속성입니다.", "smw-createproperty-allowedvals": "{{PLURAL:$1|이 속성에 대해 허용하는 값}}:", "smw-paramdesc-category-delim": "구분자", "smw-paramdesc-category-template": "항목의 서식을 사용할 틀", "smw-paramdesc-category-userparam": "틀을 전달할 변수", "smw-info-par-message": "표시할 메시지입니다.", "smw-info-par-icon": "\"info\"나 \"warning\" 중 하나를 보여주는 아이콘입니다.", "prefs-smw": "시맨틱 미디어위키", "prefs-general-options": "일반 옵션", "prefs-extended-search-options": "확장된 검색", "prefs-ask-options": "시맨틱 검색", "smw-prefs-intro-text": "[https://www.semantic-mediawiki.org/ 시맨틱 미디어위키] 및 관련 확장 기능은 선별된 기능과 함수 그룹의 개별 환경 설정을 제공합니다. 설명과 특징을 포함한 개별 설정 목록은 다음 [https://www.semantic-mediawiki.org/wiki/Help:User_preferences 도움말 문서]에서 볼 수 있습니다.", "smw-prefs-ask-options-tooltip-display": "정보 말풍선으로 변수 텍스트 표시", "smw-prefs-general-options-time-correction": "선호하는 지역 [[Special:Preferences#mw-prefsection-rendering|시간 오프셋]]을 사용하여 특수 문서의 시간 조정을 사용합니다", "smw-prefs-general-options-jobqueue-watchlist": "작업 대기열 주시문서 목록을 내 개인 표시줄에 표시합나다", "smw-prefs-help-general-options-jobqueue-watchlist": "활성화하면 보류 중인 선택된 작업의 [https://www.semantic-mediawiki.org/wiki/Help:Job_queue_watchlist 목록]을 쿼리 크기 추정치와 함께 표시합니다.", "smw-prefs-general-options-disable-editpage-info": "편집 문서의 도입문을 사용하지 않음", "smw-prefs-general-options-disable-search-info": "표준 검색 문서에서 문법 지원 정보를 비활성화합니다", "smw-prefs-general-options-suggester-textinput": "시맨틱 엔티티를 위한 입력 지원을 사용합니다", "smw-prefs-help-general-options-suggester-textinput": "활성화하면 입력 문맥에서 속성, 개념, 분류를 찾기 위해 [https://www.semantic-mediawiki.org/wiki/Help:Input_assistance 입력 지원]을 사용할 수 있게 합니다.", "smw-prefs-general-options-show-entity-issue-panel": "엔티티 이슈 패널 표시", "smw-prefs-help-general-options-show-entity-issue-panel": "활성화하면 각 페이지의 무결성 검사를 수행하고 [https://www.semantic-mediawiki.org/wiki/Help:Entity_issue_panel 엔티티 이슈 패널]을 표시합니다.", "smw-ui-tooltip-title-property": "속성", "smw-ui-tooltip-title-quantity": "단위 변환", "smw-ui-tooltip-title-info": "정보", "smw-ui-tooltip-title-service": "서비스 링크", "smw-ui-tooltip-title-warning": "경고", "smw-ui-tooltip-title-error": "오류", "smw-ui-tooltip-title-parameter": "변수", "smw-ui-tooltip-title-event": "사건", "smw-ui-tooltip-title-note": "참고", "smw-ui-tooltip-title-legend": "범례", "smw-ui-tooltip-title-reference": "각주", "smw_unknowntype": "이 속성의 \"$1\" 형식이 잘못되었습니다", "smw-concept-cache-text": "개념은 총 {{PLURAL:$1|문서}} $1개가 있으며, $2 $3에 마지막으로 업데이트했습니다.", "smw_concept_header": "\"$1\" 개념의 문서", "smw_conceptarticlecount": "아래에 {{PLURAL:$1|문서}} $1개를 보여줍니다.", "smw-qp-empty-data": "일부 선택 기준이 충분하지 않아 요청된 데이터를 표시할 수 없습니다.", "right-smw-admin": "관리 작업에 접근 (시맨틱 미디어위키)", "right-smw-patternedit": "허용된 정규 표현식과 패턴을 정비할 편집 권한 (시맨틱 미디어위키)", "restriction-level-smw-pageedit": "보호됨 (허용된 유저만)", "action-smw-patternedit": "시맨틱 미디어위키에 쓰이는 정규 표현식 편집", "group-smwadministrator": "관리자(시맨틱 미디어위키)", "group-smwadministrator-member": "{{GENDER:$1|관리자 (시맨틱 미디어위키)}}", "grouppage-smwadministrator": "{{ns:project}}:관리자 (시맨틱 미디어위키)", "group-smwcurator": "큐레이터 (시맨틱 미디어위키)", "group-smwcurator-member": "{{GENDER:$1|큐레이터 (시맨틱 미디어위키)}}", "grouppage-smwcurator": "{{ns:project}}:큐레이터 (시맨틱 미디어위키)", "group-smweditor": "편집자 (시맨틱 미디어위키)", "action-smw-admin": "시맨틱 미디어위키 관리 작업에 접근", "action-smw-ruleedit": "규칙 문서 편집 (시맨틱 미디어위키)", "smw-property-predefined-default": "\"$1\" 항목은 $2 유형의 미리 정의된 속성입니다.", "smw-property-predefined-ask": "\"$1\"은(는) 개개의 쿼리에 대한 ([https://www.semantic-mediawiki.org/wiki/Subobject 하위 객체]의 형태로) 메타 정보를 표현하는 미리 정의된 속성이며 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties 시맨틱 미디어위키]를 통해 제공됩니다.", "smw-property-predefined-asksi": "\"$1\"은(는) 쿼리에서 사용되는 조건의 수를 모으는 미리 정의된 속성이며 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties Semantic MediaWiki]를 통해 제공됩니다.", "smw-sp-properties-docu": "이 페이지는 이 위키를 위해 사용할 수 있는 [https://www.semantic-mediawiki.org/wiki/Property 속성]과 그것의 사용 횟수를 나열합니다. 최신 횟수 통계에 대해 그것은 [https://www.semantic-mediawiki.org/wiki/rebuildPropertyStatistics 속성 통계] 유지 관리 스크립트가 정기적으로 실행되는 것을 권장합니다. 차별화된 보기에 대해서는 [[Special:UnusedProperties|사용하지 않는 속성 목록]]이나 [[Special:WantedProperties|필요한 속성 목록]] 특수 문서를 보세요.", "smw-sp-properties-cache-info": "나열된 데이터는 [https://www.semantic-mediawiki.org/wiki/Caching 캐시]에서 검색되었고, $1에 마지막으로 업데이트했습니다.", "smw-sp-properties-header-label": "속성 목록", "smw-admin-settings-docu": "시맨틱 미디어위키 환경과 관련된 모든 기본값과 지역화된 설정의 목록을 보여줍니다. 개별 설정에 대한 자세한 내용은 [https://www.semantic-mediawiki.org/wiki/Help:Configuration 구성] 도움말 문서를 참조하세요.", "smw-sp-admin-settings-button": "설정 목록 생성", "smw-admin-idlookup-title": "조회", "smw-admin-idlookup-docu": "시맨틱 미디어위키에 개별 개체(위키문서, 하위개체 등)를 나타내는 안쪽 개체 Id에 대한 자세한 정보를 보여줍니다.", "smw-admin-iddispose-title": "제거", "smw-admin-iddispose-done": "\"$1\" ID가 스토리지 백엔드에서 제거되었습니다.", "smw-admin-iddispose-no-references": "\"$1\" ID는 찾을 수 없거나 임의의 참조를 포함합니다.", "smw-admin-idlookup-input": "검색:", "smw-admin-objectid": "ID:", "smw-admin-tab-general": "개요", "smw-admin-tab-notices": "구식 알림", "smw-admin-tab-maintenance": "유지보수", "smw-admin-tab-supplement": "추가 기능", "smw-admin-tab-alerts": "알림", "smw-admin-alerts-tab-deprecationnotices": "구식 알림", "smw-admin-alerts-tab-maintenancealerts": "유지 관리 알림", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "표 최적화", "smw-admin-maintenancealerts-outdatedentitiesmaxcount-alert-title": "오래된 엔티티", "smw-admin-maintenancealerts-invalidentities-alert-title": "잘못된 엔티티", "smw-admin-deprecation-notice-section": "시맨틱 미디어위키", "smw-admin-configutation-tab-settings": "설정", "smw-admin-configutation-tab-namespaces": "이름공간", "smw-admin-configutation-tab-schematypes": "스키마 유형", "smw-admin-maintenance-tab-tasks": "작업", "smw-admin-maintenance-tab-scripts": "유지 관리 스크립트", "smw-admin-maintenance-no-description": "설명이 없습니다.", "smw-admin-maintenance-script-section-title": "사용 가능한 유지 관리 스크립트 목록", "smw-admin-maintenance-script-section-update": "스크립트 업데이트", "smw-admin-maintenance-script-section-rebuild": "스크립트 다시 빌드", "smw-livepreview-loading": "불러오는 중...", "smw-sp-searchbyproperty-resultlist-header": "결과 목록", "smw-sp-searchbyproperty-nonvaluequery": "\"$1\" 속성이 할당된 값의 목록입니다.", "smw-datavalue-number-nullnotallowed": "\"$1\"가 숫자를 허용하지 않는 \"NULL\"과 함께 반환되었습니다.", "smw-search-syntax-support": "검색 입력은 시맨틱 미디어위키를 사용한 결과 일치를 도와주는 [https://www.semantic-mediawiki.org/wiki/Help:Semantic_search 쿼리 문법]의 사용을 지원합니다.", "smw-search-input": "입력과 검색", "smw-search-syntax": "구문", "smw-search-profile": "확장", "smw-search-profile-tooltip": "시맨틱 위키와 연계된 검색 기능", "smw-search-profile-sort-best": "최적의 일치", "smw-search-profile-sort-recent": "가장 최근", "smw-search-profile-sort-title": "제목", "smw-search-profile-extended-help-intro": "[https://www.semantic-mediawiki.org/wiki/Help:SMWSearch/Extended_profile 확장 프로파일]은 특수:검색 문서 내의 시맨틱 미디어위키 확장 기능에 특화된 검색 기능에 접근할 수 있는 권한을 제공하며 다음을 포함합니다:", "smw-search-profile-extended-help-sort": "결과 표시를 위한 정렬 환경 설정을 지정합니다:", "smw-search-profile-extended-help-sort-title": "* \"제목\"은 정렬 기준으로 문서 제목 문서 제목(또는 표시 제목)을 사용합니다", "smw-search-profile-extended-help-sort-best": "* \"최적의 일치\"는 백엔드가 제공하는 점수에 기반한 [https://www.semantic-mediawiki.org/wiki/Help:ElasticStore/Relevancy 관련성]에 따른 엔티티를 정렬합니다", "smw-search-profile-extended-help-namespace": "폼을 선택하자마자 이름공간 선택 상자는 숨겨지지만 \"표시/숨김\" 버튼을 이용하여 보이게 할 수 있습니다.", "smw-search-profile-extended-help-query": "<code><nowiki>$1</nowiki></code>가 쿼리로 사용되었습니다.", "smw-search-profile-extended-help-query-link": "더 자세한 사항의 경우, $1를 사용하십시오.", "smw-search-profile-extended-help-find-forms": "사용 가능한 폼", "smw-search-profile-extended-section-sort": "정렬 기준", "smw-search-profile-extended-section-form": "폼", "smw-search-profile-extended-section-search-syntax": "검색 입력", "smw-search-profile-extended-section-namespace": "이름공간", "smw-search-profile-extended-section-query": "쿼리", "smw-search-profile-link-caption-query": "쿼리 빌더", "smw-search-show": "보기", "smw-search-hide": "숨기기", "log-name-smw": "시맨틱 미디어위키 로그", "log-show-hide-smw": "$1 시맨틱 미디어위키 로그", "logeventslist-smw-log": "시맨틱 미디어위키 기록", "logentry-smw-maintenance": "유지보수 관련 이벤트가 시맨틱 미디어위키에 의해 발행되었습니다", "smw-datavalue-import-unknown-namespace": "\"$1\" 가져오기 이름공간을 알 수 없습니다. [[MediaWiki:Smw import $1]]를 통해 자세한 OWL 가져오기 정보를 이용할 수 있는지 확인해 주십시오.", "smw-datavalue-import-missing-namespace-uri": "[[MediaWiki:Smw import $1|$1 가져오기]]에서 \"$1\" 이름공간 URI를 찾을 수 없습니다.", "smw-datavalue-import-missing-type": "[[MediaWiki:Smw import $2|$2 가져오기]]에서 \"$1\"에 대한 유형 정의를 찾을 수 없습니다.", "smw-datavalue-import-link": "[[MediaWiki:Smw import $1|$1 가져오기]]", "smw-datavalue-import-invalid-value": "\"$1\"는 유효한 포맷이 아니며 \"이름공간\":\"식별자\"로의 구성을 예측합니다. (예: \"foaf:name\")", "smw-datavalue-import-invalid-format": "\"$1\" 문자열이 4개의 부분으로 나뉠 것으로 예측되었으나 포맷을 인식하지 못했습니다.", "smw-datavalue-property-create-restriction": "\"$1\" 속성은 존재하지 않으며 사용자는 승인되지 않은 속성이 있는 값을 만들거나 주석을 추가할 \"$2\" 권한이 없습니다. ([https://www.semantic-mediawiki.org/wiki/Help:Authority_mode 인가\n 방식] 참고)", "smw-datavalue-property-invalid-character": "\"$1\" 속성은 \"$2\" 문자를 속성 레이블의 일부로서 포함하고 있으므로 유효하지 않은 것으로 분류됩니다.", "smw-datavalue-restricted-use": "데이터 값 $1은(는) 이용이 제한되어 있습니다.", "smw-datavalue-invalid-number": "\"$1\"는 숫자로 해석되지 않습니다.", "smw-query-condition-circular": "\"$1\" 안에서 잠재적인 순환 조건이 발견되었습니다.", "smw-query-condition-empty": "쿼리 설명에 조건이 비어있습니다.", "smw-types-list": "자료형 목록", "smw-types-default": "\"$1\"은 내장 자료형입니다.", "smw-types-help": "더 많은 정보와 예제는 이 [https://www.semantic-mediawiki.org/wiki/Help:Type_$1 도움말 문서]에서 볼 수 있습니다.", "smw-type-ema": "\"$1\"은(는) 이메일을 표현하기 위한 특수 자료형입니다.", "smw-type-tem": "\"$1\"은(는) 온도를 표현하기 위한 특수 수치 자료형입니다.", "smw-type-qty": "\"$1\"은(는) 수치 표현과 온도 단위가 있는 수량자를 기술하기 위한 자료형입니다.", "smw-type-extra-tem": "변환 스키마에는 켈빈, 섭씨, 화씨, 란씨등의 지원 단위를 포함합니다.", "smw-type-tab-properties": "속성", "smw-type-tab-types": "유형", "smw-type-tab-type-ids": "유형 ID", "smw-type-tab-errors": "오류", "smw-type-primitive": "기본", "smw-type-contextual": "문맥형", "smw-type-compound": "복합형", "smw-type-container": "컨테이너", "smw-type-no-group": "미분류", "smw-special-pageproperty-description": "이 문서는 속성과 주어진 문서의 모든 값을 찾기 위한 둘러보기 인터페이스를 제공합니다. 그 밖의 이용 가능한 검색 인터페이스에는 [[Special:SearchByProperty|속성 검색]], [[Special:Ask|문의 쿼리 빌더]]가 있습니다.", "smw-datavalue-record-invalid-property-declaration": "레코드 정의에는 그 자체가 레코드 타입으로 선언된, 허용되지 않는 \"$1\" 프로퍼티를 포함하고 있습니다.", "smw-datavalue-languagecode-invalid": "\"$1\"는 지원되는 언어 코드로 인식되지 않습니다.", "smw-types-extra-mlt-lcode": "자료형에는 언어 코드가 {{PLURAL:$2|필요합니다|필요하지 않습니다}} (예: {{PLURAL:$2|언어 코드가 없는 값 애너테이션은 받아들이지 않습니다|언어 코드가 없는 값 애너테이션은 받아들입니다}})", "smw-limitreport-intext-parsertime": "[SMW] 텍스트 내 주석 파서 시간", "smw-limitreport-intext-postproctime": "[SMW] 후처리 시간", "smw-limitreport-intext-parsertime-value": "$1초", "smw-limitreport-intext-postproctime-value": "$1{{PLURAL:$1|초}}", "smw-limitreport-pagepurge-storeupdatetime": "[SMW] 스토어 업데이트 시간 (문서를 새로 고칠 때)", "smw-limitreport-pagepurge-storeupdatetime-value": "$1초", "smw-datavalue-allows-pattern-mismatch": "\"$1\"은(는) \"$2\" 정규 표현식에 의해 유효하지 않은 것으로 분류되었습니다.", "smw-datavalue-allows-pattern-reference-unknown": "\"$1\" 패턴 참조는 [[MediaWiki:Smw allows pattern]]의 엔트리와 일치되지 못했습니다.", "smw-datavalue-allows-value-list-unknown": "\"$1\" 목록 참조는 [[MediaWiki:Smw allows list $1]] 문서와 일치되지 못했습니다.", "smw-datavalue-allows-value-list-missing-marker": "\"$1\" 목록 내용에 * 목록 표시가 있는 항목이 없습니다.", "smw-datavalue-feature-not-supported": "\"$1\" 기능은 이 위키에서 지원되지 않거나 비활성화되었습니다.", "smw-datavalue-time-invalid-offset-zone-usage": "\"$1\"에는 지원하지 않는 오프셋과 구역 식별자가 포함되어 있습니다.", "smw-datavalue-time-invalid-values": "\"$1\" 값에는 \"$2\" 형태의 해석할 수 없는 정보가 포함되어 있습니다.", "smw-datavalue-time-invalid-date-components-common": "\"$1\"에는 일부 해석할 수 없는 정보가 포함되어 있습니다.", "smw-datavalue-time-invalid-date-components-dash": "\"$1\"에는 데이터 해석에 유효하지 않은 외인성 대시나 기타 문자들이 포함되어 있습니다.", "smw-datavalue-time-invalid-date-components-empty": "\"$1\"에는 일부 비어있는 구성 요소가 포함되어 있습니다.", "smw-datavalue-time-invalid-date-components-three": "\"$1\"에는 데이터 해석에 필요한 세 개 이상의 구성 요소가 포함되어 있습니다.", "smw-datavalue-time-invalid-ampm": "\"$1\"에는 12시간 변환에 유효하지 않은 시간 요소로서 \"$2\"이(가) 포함되어 있습니다.", "smw-datavalue-time-invalid-jd": "보고된 \"$2\" 값을 가지고 \"$1\" 입력값을 유효한 JD(율리우스일) 숫자로 해석할 수 없습니다.", "smw-datavalue-external-formatter-invalid-uri": "\"$1\"은(는) 유효하지 않은 URL입니다.", "smw-datavalue-keyword-maximum-length": "키워드가 $1의 최대 길이를 초과했습니다.", "smw-datavalue-parse-error": "지정한 \"$1\" 값을 인식하지 못했습니다.", "smw-datavalue-propertylist-invalid-property-key": "\"$1\" 속성 목록은 유효하지 않은 \"$2\" 속성 키를 포함합니다.", "smw-datavalue-type-invalid-typeuri": "\"$1\" 유형은 유효한 URI 표현으로 변환될 수 없습니다.", "smw-datavalue-wikipage-empty": "위키페이지 입력값이 비어있으므로(예: <code>[[SomeProperty::]], [[]]</code>) 이름이나 쿼리 조건의 일부로 사용할 수 없습니다.", "smw-parser-invalid-json-format": "JSON 파서가 \"$1\"로 반환했습니다.", "smw-property-preferred-label-language-combination-exists": "\"$2\" 언어가 이미 \"$3\" 레이블에 할당되어 있기 때문에 \"$1\"을(를) 선호하는 레이블로 사용할 수 없습니다.", "smw-clipboard-copy-link": "클립보드에 링크 복사하기", "smw-data-lookup": "데이터를 가져오는 중...", "smw-data-lookup-with-wait": "이 요청은 처리 중이며 시간이 잠시 소요될 수 있습니다.", "smw-no-data-available": "이용 가능한 데이터 없음.", "smw-property-req-error-list": "속성에 다음의 오류 또는 경고가 포함되어 있습니다:", "protect-level-smw-pageedit": "문서 편집 권한이 있는 사용자만 허용 (시맨틱 미디어위키)", "smw-edit-protection-auto-update": "시맨틱 미디어위키는 \"ls edit protected\" 속성에 따라 보호 상태를 업데이트하였습니다.", "smw-edit-protection-enabled": "편집 보호됨 (시맨틱 미디어위키)", "smw-patternedit-protection": "이 문서는 보호되어 있으므로 적절한 <code>smw-patternedit</code> [https://www.semantic-mediawiki.org/wiki/Help:Permissions 권한]이 있는 사용자만 편집할 수 있습니다.", "smw-property-predefined-edip": "\"$1\" 속성은 편집이 보호되어 있는지 아닌지의 여부를 지시하기 위해 [https://www.semantic-mediawiki.org/wiki/Help:Special_properties 시맨틱 미디어위키]가 제공하는 미리 정의된 속성입니다.", "smw-query-reference-link-label": "쿼리 참조", "smw-format-datatable-emptytable": "테이블에 사용할 수 있는 데이터가 없습니다", "smw-format-datatable-info": "_TOTAL_개의 항목 중 _START_부터 _END_까지 표시 중", "smw-format-datatable-infoempty": "0개의 항목 중 0부터 0까지 표시 중", "smw-format-datatable-infofiltered": "(_MAX_개의 전체 항목에서 필터됨)", "smw-format-datatable-lengthmenu": "_MENU_ 엔트리 보기", "smw-format-datatable-loadingrecords": "불러오는 중...", "smw-format-datatable-processing": "처리 중...", "smw-format-datatable-search": "검색:", "smw-format-datatable-zerorecords": "일치하는 레코드가 없습니다", "smw-format-datatable-first": "처음", "smw-format-datatable-last": "마지막", "smw-format-datatable-next": "다음", "smw-format-datatable-previous": "이전", "smw-format-datatable-toolbar-export": "내보내기", "smw-category-invalid-redirect-target": "\"$1\" 분류는 분류가 아닌 이름공간에 대한 잘못된 넘겨주기 대상을 포함하고 있습니다.", "smw-postproc-queryref": "일부 쿼리 후처리가 필요한 상황에서 시맨틱 미디어위키는 현재 페이지를 새로 고칩니다.", "apihelp-browsebyproperty-summary": "속성이나 속성 목록에 관한 정보를 반환하기 위한 API 모듈입니다.", "apihelp-smwtask-summary": "시맨틱 미디어위키 관련 작업을 실행하기 위한 API 모듈입니다.", "apihelp-smwbrowse-summary": "시맨틱 미디어위키의 각기 다른 엔티티 유형의 탐색 활동을 지원하기 위한 API 모듈입니다.", "apihelp-ask-parameter-api-version": "출력 형식:\n;2:결과 목록을 위해 {}를 사용하는 하위 호환 포맷\n;3:결과 목록을 위해 {}를 사용하는 실험적인 포맷.", "apihelp-smwtask-param-task": "작업 유형을 정의합니다", "smw-api-invalid-parameters": "\"$1\" 변수가 유효하지 않습니다", "smw-property-page-list-count": "이 속성을 사용하는 {{PLURAL:$1|문서}} $1개를 보여줍니다.", "smw-property-page-list-search-count": "\"$2\" 값이 일치하고 이 속성을 사용하는 {{PLURAL:$1|문서}} $1개를 보여줍니다.", "smw-property-reserved-category": "분류", "smw-category": "분류", "smw-datavalue-uri-invalid-scheme": "\"$1\"은(는) 유효한 URI 스킴으로 등재되지 않았습니다.", "smw-browse-property-group-title": "속성 그룹", "smw-browse-property-group-label": "속성 그룹 레이블", "smw-browse-property-group-description": "속성 그룹 설명", "smw-filter": "필터", "smw-section-expand": "문단 펼치기", "smw-section-collapse": "문단 접기", "smw-ask-format-help-link": "[https://www.semantic-mediawiki.org/wiki/Help:$1_format $1] 형식", "smw-help": "도움말", "smw-personal-jobqueue-watchlist": "작업 대기열 주시문서 목록", "smw-property-predefined-label-skey": "정렬키", "smw-processing": "처리 중...", "smw-loading": "불러오는 중...", "smw-fetching": "가져오는 중...", "smw-preparing": "준비 중...", "smw-expand": "펼치기", "smw-collapse": "접기", "smw-copy": "복사", "smw-copy-clipboard-title": "내용을 클립보드에 복사합니다", "smw-jsonview-expand-title": "JSON 뷰를 확장합니다", "smw-jsonview-collapse-title": "JSON 뷰를 접습니다", "smw-jsonview-search-label": "검색:", "smw-types-title": "유형: $1", "smw-schema-namespace-editcontentmodel-disallowed": "[https://www.semantic-mediawiki.org/wiki/Help:Schema 스키마 문서]의 콘텐츠 모델을 변경할 권한이 없습니다.", "smw-schema-error-title": "유효성 검사 {{PLURAL:$1|오류}}", "smw-schema-error-miscellaneous": "기타 오류 ($1)", "smw-schema-error-validation-file-inaccessible": "유효성 확인 파일 \"$1\"에 접근할 수 없습니다.", "smw-schema-error-json": "JSON 오류: \"$1\"", "smw-schema-validation-schema-title": "JSON 스키마", "smw-schema-summary-title": "요약", "smw-schema-title": "스키마", "smw-schema-type": "스키마 종류", "smw-schema-tag": "{{PLURAL:$1|태그}}", "smw-ask-title-keyword-type": "키워드 검색", "smw-ask-message-keyword-type": "이 검색은 <code><nowiki>$1</nowiki></code> 조건과 일치합니다.", "smw-remote-source-unavailable": "원격 \"$1\" 대상으로 연결할 수 없습니다.", "smw-remote-source-disabled": "'''$1''' 원본은 원격 요청 지원을 비활성화하였습니다!", "smw-parameter-missing": "\"$1\" 변수가 없습니다.", "smw-property-tab-usage": "사용", "smw-property-tab-redirects": "동의어", "smw-property-tab-subproperties": "하위 속성", "smw-property-tab-specification": "... 기타", "smw-concept-tab-list": "목록", "smw-concept-tab-errors": "오류", "smw-ask-tab-result": "결과", "smw-ask-tab-extra": "기타", "smw-ask-tab-debug": "디버그", "smw-ask-tab-code": "코드", "smw-pendingtasks-tab-setup": "설정", "smw-pendingtasks-setup-tasks": "작업", "smw-es-replication-error-divergent-date-short": "다음의 날짜 정보가 비교를 위해 사용되었습니다:", "smw-es-replication-error-divergent-revision-short": "다음의 관련 판 데이터는 비교를 위해 사용되었습니다:", "smw-report": "보고서", "smw-legend": "범례", "smw-entity-examiner-associated-revision-mismatch": "판", "smw-facetedsearch-intro-tab-search": "검색", "smw-facetedsearch-input-filter-placeholder": "필터...", "smw-facetedsearch-no-filters": "필터가 없습니다.", "smw-facetedsearch-no-filter-range": "필터 범위가 없습니다.", "smw-search-placeholder": "검색...", "smw-listingcontinuesabbrev": "(계속)", "smw-showingresults": "'''$2'''번 부터의 {{PLURAL:$1|결과 '''1'''개|결과 '''$1'''개}}입니다."}