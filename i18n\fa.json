{"@metadata": {"authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Armin1392", "AzorAhai", "<PERSON><PERSON><PERSON><PERSON>", "DEXi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Jaideraf", "Jeeputer", "<PERSON><PERSON><PERSON>", "McDut<PERSON><PERSON>", "Mjbmr", "<PERSON>", "<PERSON><PERSON>", "Obzord", "<PERSON><PERSON><PERSON><PERSON>", "Physicsch", "Reza1615", "Sunfyre", "ZxxZxxZ"]}, "smw-desc": "ویکی خودتان را برای ماشین‌ها ''و'' انسان‌ها بیشتر قابل دسترس کنید ([https://www.semantic-mediawiki.org/wiki/Help:User_manual مستندات برخط])", "smw-error": "خطا", "smw-upgrade-error": "[https://www.semantic-mediawiki.org/ مدیاویکی معنایی] نصب و فعال شده است اما یک [https://www.semantic-mediawiki.org/wiki/Help:Upgrade کلید به‌روزرسانی] مناسب فراموش شده است.", "smw-upgrade-release": "انتشار", "smw-upgrade-progress": "پیشرفت", "smw-upgrade-progress-create-tables": "ایجاد (یا به‌روزرسانی) جدول‌ها و شاخص‌ها ...", "smw-upgrade-progress-post-creation": "درحال اجرای وظایف ایجادشده ارسالی ...", "smw-upgrade-progress-table-optimization": "درحال اجرای بهینه‌سازی‌های جدول ...", "smw-upgrade-error-title": "خطا » مدیاویکی معنایی", "smw-upgrade-error-how-title": "چگونه این خطا را درست کنیم؟", "smw-extensionload-error-how-title": "چگونه این خطا را درست کنم؟", "smw-upgrade-maintenance-title": "نگهداری » مدیاویکی معنایی", "smw_viewasrdf": "خوراک RDF", "smw_finallistconjunct": "، و", "smw-factbox-head": "... بیشتر دربارۀ \"$1\"", "smw-factbox-attachments": "پیوست‌ها", "smw_isspecprop": "این ویژگی یک ویژگی ویژه در این ویکی است.", "smw-concept-cache-header": "به‌کارگیری حافظۀ نهان", "smw-concept-no-cache": "هیچ حافظهٔ نهانی در دسترس نیست.", "smw_concept_description": "توضیحات مفهوم \"$1\"", "smw_no_concept_namespace": "مفاهیم فقط می‌توانند روی صفحه‌هایی با فضای نام Concept: تعریف می‌شوند.", "smw_multiple_concepts": "هر صفحۀ مفهوم می‌تواند فقط یک تعریف مفهوم داشته باشد.", "smw_concept_cache_miss": "مفهوم \"$1\" نمی‌تواند در حال حاضر استفاده شود، زیرا تنظیمات ویکی نیاز به محاسبه آن به‌صورت برون‌خط دارد.\nاگر این مشکل پس از مدتی از بین نرفت، از مدیر سایت بخواهید تا این مفهوم را در دسترس قرار دهد.", "smw_noinvannot": "مقادیر نمی‌توانند برای ویژگی‌های معکوس گماشته شوند.", "version-semantic": "افزونه‌های معنایی", "smw_baduri": "URIهای به شکل \"$1\" مجاز نیستند.", "smw_printername_count": "شمارش نتایج", "smw_printername_csv": "برون‌بری CSV", "smw_printername_dsv": "برون‌بری DSV", "smw_printername_debug": "پرسمان اشکال‌یابی (برای برون‌بران)", "smw_printername_embedded": "محتویات جازساز شدۀ صفحه", "smw_printername_json": "برون‌بری JSON", "smw_printername_list": "فهرست", "smw_printername_ol": "فهرست شماره‌‌گذاری شده", "smw_printername_ul": "فهرست گلوله‌‌گذاری شده", "smw_printername_table": "جدول", "smw_printername_broadtable": "جدول گسترده", "smw_printername_template": "الگو", "smw_printername_templatefile": "پروندۀ الگو", "smw_printername_rdf": "برون‌بری RDF", "smw_printername_category": "رده", "validator-type-class-SMWParamSource": "متن", "smw-paramdesc-limit": "حداکثر تعداد نتایج برای بر گرداندن", "smw-paramdesc-offset": "جابجاسازی اولین نتیجه", "smw-paramdesc-headers": "نمایش نام‌های سرایندها/ویژگی", "smw-paramdesc-mainlabel": "برچسبی برای دادن نام صفحهٔ اصلی", "smw-paramdesc-link": "نمایش مقادیر به‌صورت پیوندها", "smw-paramdesc-intro": "متنی برای نمایش قبل از نتایج پرسمان، اگر چیزی وجود داشته باشد", "smw-paramdesc-outro": "متنی برای نمایش پس از نتایج پرسمان، اگر چیزی وجود داشته باشد", "smw-paramdesc-default": "متن برای نمایش اگر هیچ نتیجهٔ پرسمانی وجود نداشته باشد", "smw-paramdesc-sep": "جداکنندۀ بین نتایج", "smw-paramdesc-showsep": "نمایش جداکننده (\"sep=<value>\") در بالای پرونده CSV", "smw-paramdesc-distribution": "به جای نمایش‌دادن همه مقادیر، رخدادها را شمارش کن، و آنها را نمایش بده.", "smw-paramdesc-distributionsort": "مرتب‌سازی توزیع مقدار به‌وسیلۀ شمارش رخداد.", "smw-paramdesc-distributionlimit": "محدودکردن توزیع مقدار فقط به شماری از بعضی مقادیر", "smw-paramdesc-template": "نام یک الگو با آنی که چاپ‌ها را نمایش می‌دهد", "smw-paramdesc-columns": "تعداد ستون‌هایی که در آن نتایج نمایش داده می‌شوند", "smw-paramdesc-userparam": "یک مقداری که به فراخوانی هر الگو گذارنده می‌شود، اگر الگو استفاده شده باشد", "smw-paramdesc-introtemplate": "نام الگو برای نمایش قبل از نتایج پرسمان، اگر چیزی وجود داشته باشد", "smw-paramdesc-outrotemplate": "نام الگو برای نمایش پس از نتایج پرسمان، اگر چیزی وجود داشته باشد", "smw-paramdesc-embedformat": "برچسب HTML برای تعریف سرایندها استفاده شده است", "smw-paramdesc-embedonly": "نمایش بدون سرایندها", "smw-paramdesc-table-class": "یک کلاس CSS اضافی برای جدول تنظیم شود", "smw-paramdesc-table-transpose": "نمایش سرایندهای جدول به طور عمودی و نتایج به طور افقی", "smw-paramdesc-rdfsyntax": "نحو RDF باید استفاده شود", "smw-paramdesc-csv-sep": "یک جداکنندهٔ ستونی مشخص شود", "smw-paramdesc-csv-valuesep": "یک جداکنندهٔ مقدار مشخص شود", "smw-paramdesc-dsv-separator": "جداکننده برای استفاده", "smw-paramdesc-dsv-filename": "نامی برای پروندۀ DSV", "smw-paramdesc-filename": "نامی برای پروندۀ خروجی", "smw-smwdoc-description": "یک جدول از همهٔ پارامترهایی که می‌تواند برای قالب نتیجه مشخص شده استفاده شود را به همراه مقدارها و توضیح‌های پیش‌فرض نشان می‌دهد", "smw-smwdoc-par-format": "قالب نتیجه برای نمایش مستندات پارامتر.", "smw-smwdoc-par-parameters": "پارامترهایی برای نمایش دادن. \"مشخص\" برای آن افزوده شده‌ها توسط فرمت، \"اساس\" برای آن متغیرها در همه فرمت‌ها، و \"همه\" برای هر دو.", "smw-paramdesc-sort": "ویژگی‌ای که پرسمان را بر پایه آن مرتب شود", "smw-paramdesc-order": "ترتیب مرتب‌سازی پرسمان", "smw-paramdesc-searchlabel": "متنی برای ادامه‌دادن جستجو", "smw-paramdesc-named_args": "نام شناسه‌هایی را که به الگو گذارنده شده است", "smw-paramdesc-export": "گزینه برون‌بری", "smw-paramdesc-prettyprint": "خروجی pretty-print که تورفتگی‌های اضافی و خط.ط تازه را نمایش می‌دهد", "smw-paramdesc-json-type": "نوع سریالی‌کردن داده‌ها را مشخص می‌کند", "smw-paramdesc-source": "منبع پرسمان جایگزین", "smw-paramdesc-jsonsyntax": "نحو JSON باید استفاده شود", "smw-printername-feed": "جارزن RSS و Atom", "smw-paramdesc-feedtype": "نوع جارزن", "smw-paramdesc-feedtitle": "متنی را باید به صورت عنوان جارزن استفاده بشود", "smw-paramdesc-feeddescription": "متنی را باید به عنوان توضیح جارزن استفاده بشوند", "smw-paramdesc-feedpagecontent": "محتوای صفحه با مرورگر نمایش داده می‌شود", "smw-label-feed-link": "آراس‌اس", "smw-label-feed-description": "$1 $2 جارزن", "smw_iq_disabled": "پرسمان‌های معنایی برای این ویکی غیر فعال شده است", "smw_iq_moreresults": "... نتایج بیشتر", "smw_parseerror": "مقدار داده شده فهمیده نشده بود.", "smw_decseparator": ".", "smw_kiloseparator": "،", "smw_notitle": "\"$1\" به عنوان نام یک صفحه در این ویکی نمی‌تواند استفاده شود.", "smw_noproperty": "\"$1\" به عنوان نام یک ویژگی در این ویکی نمی‌تواند استفاده شود.", "smw_wrong_namespace": "فقط صفحه‌های در فضای نام \"$1\" اینجا مجاز هستند.", "smw_manytypes": "بیش از یک نوع برای ویژگی تعریف شده است.", "smw_emptystring": "رشته‌های خالی پذیرفته نمی‌شوند.", "smw_notinenum": "\"$1\" در فهرست مقادیر ممکن ($2) برای ویژگی \"$3\" نیست.", "smw_noboolean": "\"$1\" به عنوان یک مقدار بولی (درست/غلط) شناخته نشده است.", "smw_true_words": "درست،د،بله،ب", "smw_false_words": "غلط،غ،نه،ن", "smw_nofloat": "\"$1\" یک عدد نیست.", "smw_infinite": "اعدادی به بزرگی \"$1\" پشتیبانی نمی‌شوند.", "smw_unitnotallowed": "\"$1\" به عنوان یک واحد اندازه‌گیری معتبر برای این ویژگی اعلام نشده است.", "smw_nounitsdeclared": "هیچ واحد اندازه‌گیری برای این ویژگی اعلام نشده‌ بود.", "smw_novalues": "هیچ مقداری مشخص نشده‌ است.", "smw_nodatetime": "تاریخ \"$1\" فهمیده نشد بود.", "smw_toomanyclosing": "به نظر می‌آید که رخدادهای زیادی از \"$1\" در پرسمان باشد.", "smw_noclosingbrackets": "بعضی از به‌کارگیری \"<nowiki>[[</nowiki>\" در پرسمان شما توسط یک \"]]\" متناظر بسته نشده بود.", "smw_misplacedsymbol": "نماد \"$1\" در جایی که مفید نیست استفاده شده بود.", "smw_unexpectedpart": "قسمت \"$1\" از پرسمان را نفهمیده بود.\nنتایج نمی‌تواند مورد انتظار باشد.", "smw_emptysubquery": "بعضی از زیرپرسمان‌ها هیچ شرط معتبری ندارد.", "smw_misplacedsubquery": "بعضی زیرپرسمان‌ها در جایی استفاده شدند که هیچ زیرپرسمانی مجاز نیست.", "smw_valuesubquery": "زیرپرسمان‌ها برای مقادیر ویژگی \"$1\" پشتیبانی نمی‌شوند.", "smw_badqueryatom": "بعضی از قسمت‌های \"<nowiki>[[...]]</nowiki>\" از پرسمان فهمیده نشدند.", "smw_propvalueproblem": "مقدار ویژگی \" $1 \" فهمیده نشده بود.", "smw_noqueryfeature": "بعضی از ویژگی‌های پرسمان در این ویکی پشتیبانی نمی‌شود و قسمتی از پرسمان ($1) از قلم انداخته شد.", "smw_noconjunctions": "اتصالات در پرس‌وجو‌ها در این ویکی پشتیبانی نمی‌شود و بخشی از پرس‌وجو کاهش یافت ($1).", "smw_nodisjunctions": "عدم اتصالات در  پرس‌وجو‌ها در این ویکی پشتیبانی نمی‌شوند و بخشی از پرس‌وجو کاهش یافت ($1).", "smw_querytoolarge": "شرایط پرس‌وجو زیر به علت محدودیت‌های ویکی در اندازه پرس‌وجو یا عمق نتوانست در نظر گرفته شود: $1.", "smw_notemplategiven": "ارائه یک مقدار برای پارامتر \"الگو\" برای این فرمت پرس‌وجو برای کار کردن", "smw_db_sparqlqueryproblem": "نتیجه پرس‌وجو نتوانست از پایگاه اطلاعاتی اس‌پی‌ای‌آر‌کیو‌ال اخذ شود. ممکن است این خطا موقت باشد یا مشکلی در نرم‌افزار پایگاه اطلاعاتی نشان می‌دهد.", "smw_db_sparqlqueryincomplete": "پاسخ به پرس‌و‌جو بیش از حد دشوار است و شکست خورد. برخی از نتایج می‌توانند از دست بروند. در صورت امکان سعی کنید به جای آن از پرس‌وجو ساده استفاده کنید.", "smw_type_header": "ویژگی‌های نوع \"$1\"", "smw_typearticlecount": "نمایش $1 {{PLURAL:$1|ویژگی|ویژگی}} با استفاده از این نوع.", "smw_attribute_header": "صفحه‌های استفاده‌کنندۀ ویژگی \"$1\"", "smw_attributearticlecount": "درحال نمایش $1 {{PLURAL:$1|صفحه|صفحه‌هایی}} از این ویژگی استفاده کردن.", "smw-propertylist-subproperty-header": "زیرویژگی‌ها", "smw-propertylist-redirect-header": "مترادف‌ها", "specialpages-group-smw_group": "مدیاویکی معنایی", "specialpages-group-smw_group-properties-concepts-types": "ویژگی‌ها، مفهوم‌ها و نوع‌ها", "specialpages-group-smw_group-search": "مرور و جستجو", "exportrdf": "برون‌بری صفحه‌ها به RDF", "smw_exportrdf_docu": "این صفحه به شما برای به دست آوردن داده‌ها از صفحه در فرمت آر‌دی‌اف اجازه می‌دهد.\nبرای برون‌بری صفحات، عناوین را در جعبه متن زیر وارد کنید، یک عنوان در هر خط.", "smw_exportrdf_recursive": "تمام صفحه‌های مرتبط را به صورت بازگشتی برون‌بری کن.\nتوجه کنید که نتیجه می‌تواند بزرگ باشد!", "smw_exportrdf_backlinks": "همچنین تمام صفحه‌هایی را که به صفحه‌های برون‌بری شده ارجاع دارند، برون‌بری کن.\nآردی‌اف قابل مرور تولید می‌کند.", "smw_exportrdf_lastdate": "صفحه‌هایی را که از زمان داده شده تغییری نکرده‌اند، برون‌بری نکن.", "smw_exportrdf_submit": "برون‌بری", "uriresolver": "حل‌ کنندهٔ یوآرآی‌آر", "properties": "ویژگی‌ها", "smw-categories": "رده‌ها", "smw_properties_docu": "ویژگی‌های زیر در این ویکی استفاده شدند.", "smw_property_template": "$1 از نوع $2 ($3 بار {{PLURAL:$3|استفاده شده}})", "smw_propertylackspage": "تمام ویژگی‌ها باید توسط یک صفحه توصیف شوند.", "smw_propertylackstype": "هیچ نوعی برای این ویژگی مشخص نشده است (با فرض نوع $1 برای اکنون).", "smw_propertyhardlyused": "این ویژگی به ندرت در این ویکی استفاده شده است.", "smw-property-name-invalid": "ویژگی $1 نمی‌تواند استفاده شود (نام ویژگی نامعتبر).", "smw-sp-property-searchform": "نمایش ویژگی‌هایی که در بردارد:", "smw-sp-property-searchform-inputinfo": "این ورودی حساس است و هنگامی که برای فیلتر کردن استفاده می‌شود، فقط خواصی که با شرایط مطابقت دارند نمایش داده می‌شوند.", "smw-special-property-searchform": "نمایش ویزگی‌هایی که شامل:", "smw-special-property-searchform-inputinfo": "این ورودی حساس است و هنگامی که برای فیلتر کردن استفاده می‌شود، فقط خواصی که با شرایط مطابقت دارند نمایش داده می‌شوند.", "smw-special-property-searchform-options": "گزینه‌ها", "smw-special-wantedproperties-filter-label": "پالایه:", "smw-special-wantedproperties-filter-none": "<PERSON><PERSON><PERSON>", "smw-special-wantedproperties-filter-unapproved": "تأییدنشده", "concepts": "مفهوم‌ها", "smw-special-concept-docu": "[https://www.semantic-mediawiki.org/wiki/Help:Concepts concept] می‌تواند  به عنوان \"دسته پویا\" مشاهده شوند، یعنی به عنوان مجموعه‌ای از صفحاتی که به طور دستی ایجاد نمی‌شوند، اما آنهایی که توسط مدیاویکی معنایی از یک توصیف داده شده پرس‌وجو، محاسبه می‌شوند.", "smw-special-concept-header": "فهرست مفاهیم", "smw-special-concept-count": "{{PLURAL:$1|مفهوم}} در زیر فهرست {{PLURAL:$1|شده است}}.", "smw-special-concept-empty": "هیچ مفهومی یافت نشد.", "unusedproperties": "ویژگی‌های استفاده‌نشده", "smw-unusedproperties-docu": "خصوصیت‌های زیر با وجود این‌که هیچ صفحه‌ای از آن‌ها استفاده نمی‌کند، وجود دارند.", "smw-unusedproperty-template": "$1 از نوع $2", "wantedproperties": "ویژگی‌های خواسته‌شده", "smw-wantedproperties-docu": "خصوصیت‌های زیر در این ویکی استفاده شده‌اند ولی هنوز صفحه‌ای برای توصیف آن‌ها وجود ندارد.", "smw-wantedproperty-template": "$1 ($2 بار {{PLURAL:$2|استفاده‌ شده}})", "smw-special-wantedproperties-template": "$1 ($2 بار {{PLURAL:$2|استفاده شده}})", "smw_purge": "تازه‌سازی", "smw-purge-failed": "مدیاویکی معنایی برای پاک‌سازی این صفحه تلاش کرد اما شکست خورد", "types": "نوع‌ها", "smw_types_docu": "در زیر فهرستی از تمام انواع داده است که می‌تواند به ویژگی‌ها اختصاص داده شده باشد.", "smw-special-types-no-such-type": "\"$1\" ناشناخته است یا به عنوان نوع داده معتبر تعیین نشده است.", "smw-statistics": "آمارهای معنایی", "smw-statistics-entities-total": "مدخل‌ها (کل)", "smw-statistics-property-instance": "ویژگی {{PLURAL:$1|مقدار|مقادیر}} (کل)", "smw-statistics-property-total": "[[Special:Properties|{{PLURAL:$1|ویژگی|ویژگی‌ها}}]] (کل)", "smw-statistics-property-total-legacy": "{{PLURAL:$1|ویژگی|ویژگی‌ها}} (کل)", "smw-statistics-property-page": "{{PLURAL:$1|ویژگی|ویژگی‌ها}} (همراه یک صفحه ثبت شده)", "smw-statistics-property-type": "{{PLURAL:$1|ویژگی|ویژگی‌ها}} (به نوع اطلاعات اختصاص داده شده)", "smw-statistics-query-inline-legacy": "{{PLURAL:$1|پرسمان|پرسمان‌ها}}", "smw-statistics-query-inline": "[[Property:Has query|{{PLURAL:$1|پرسمان|پرسمان‌ها}}]] (جاساز‌شده, کل)", "smw-statistics-query-size": "اندازۀ پرسمان", "smw-statistics-concept-count-legacy": "{{PLURAL:$1|مفهوم|مفهوم‌ها}}", "smw-statistics-concept-count": "[[Special:Concepts|{{PLURAL:$1|مفهوم|مفهوم‌ها}}]]", "smw-statistics-subobject-count": "{{PLURAL:$1|موضوع|موضوع‌ها}}", "smw-statistics-subobject-count-legacy": "{{PLURAL:$1|موضوع|موضوع‌ها}}", "smw-statistics-datatype-count": "[[Special:Types|{{PLURAL:$1|نوع داده|نوع‌های داده}}]]", "smw_uri_doc": "برطرف‌کننده یوآر‌ال [$1 W3C TAG finding on httpRange-14] را پیاده می‌کند.\nاز انسان‌هایی که به سمت وب‌سایت‌ها نمی‌روند مراقبت می‌کند.", "ask": "جستجوی معنایی", "smw_ask_sortby": "مرتب‌سازی با ستون (اختیاری)", "smw_ask_ascorder": "صعودی", "smw_ask_descorder": "نزولی", "smw-ask-order-rand": "تصادفی", "smw_ask_submit": "نتایج را جستجو کن", "smw_ask_editquery": "ویرایش پرسمان", "smw_add_sortcondition": "[افزودن شرط مرتب‌سازی]", "smw-ask-sort-add-action": "افزودن شرط مرتب‌سازی", "smw_ask_hidequery": "نهفتن پرسمان (نمای متراکم)", "smw_ask_help": "راهنمای پرسمان گرفتن", "smw_ask_queryhead": "شرط", "smw_ask_printhead": "نسخهٔ چاپی انتخابی", "smw_ask_printdesc": "(افزودن یک نام ویژگی در هر خط)", "smw_ask_format_as": "فرمت به عنوان:", "smw_ask_defaultformat": "پیش‌<PERSON><PERSON>ض", "smw_ask_otheroptions": "گزینه‌های دیگر", "smw-ask-otheroptions-info": "این بخش شامل گزینه‌هایی است که اظهارات چاپی را تغییر می‌دهد. توصیفات پارامتر می‌تواند توسط توقف در بالای آن‌ها مشاهده شوند.", "smw-ask-otheroptions-collapsed-info": "لطفاً از نماد به علاوه برای مشاهده همه گزینه‌های در دسترس استفاده کنید", "smw_ask_show_embed": "نمایش کد جاسازی", "smw_ask_hide_embed": "پنهان‌سازی کد جاسازی", "smw_ask_embed_instr": "برای جاسازی این سوال خطی در یک صفحه ویکی از کد زیر استفاده کنید.", "smw-ask-delete": "<PERSON><PERSON><PERSON>", "smw-ask-sorting": "مرتب‌<PERSON><PERSON><PERSON>ی", "smw-ask-options": "گزینه‌ها", "smw-ask-options-sort": "گزینه‌های مرتب‌سازی", "smw-ask-format-options": "قالب و گزینه‌ها", "smw-ask-parameters": "پارامترها", "smw-ask-search": "جستجو", "smw-ask-debug": "اشکال‌زدایی کردن", "smw-ask-debug-desc": "اطلاعات عمومی خطایابی پرسمان", "smw-ask-no-cache": "غیرفعال‌سازی حافظهٔ نهان پرسمان", "smw-ask-no-cache-desc": "نتایج بدون حافظهٔ نهان پرسمان", "smw-ask-result": "نتیجه", "smw-ask-empty": "پاک‌کردن همهٔ مدخل‌ها", "smw-ask-download-link-desc": "بارگیری نتایج مورد پرسمان در قالب $1", "smw-ask-format": "قالب", "smw-ask-format-selection-help": "راهنمایی برای فرمت انتخاب شده: $1", "smw-ask-input-assistance": "راهنمای ورودی", "smw-ask-condition-input-assistance": "[https://www.semantic-mediawiki.org/wiki/Help:Input_assistance  راهنمای ورودی] برای چاپ، مرتب‌سازی و فیلد شرط ارائه می‌شود. فیلد شرط نیاز به یکی از پیشوند‌های زیر دارد:", "smw-ask-condition-input-assistance-property": "<code>p:</code> برای واکشی پیشنهادهای ویژگی (مثل <code>[[p:Has ...</code>)", "smw-ask-condition-input-assistance-category": "<code>c:</code> برای واکشی پیشنهادهای رده", "smw-ask-condition-input-assistance-concept": "<code>con:</code> برای واکشی پیشنهادهای مفهوم", "smw-ask-extra-query-log": "سیاهۀ پرسمان", "smw-ask-extra-other": "سایر", "searchbyproperty": "جستجو بر اساس ویژگی", "processingerrorlist": "پردازش فهرست خطا", "propertylabelsimilarity": "گزارش تشابه برچسب ویژگی", "smw_sbv_docu": "جستجو برای همه صفحاتی که یک ویژگی داده شده و مقدار دارند.", "smw_sbv_novalue": "یک مقدار معتبر برای ویژگی وارد کید، یا همه مقادیر ویژگی را برای \"$1\" مشاهده کنید.", "smw_sbv_displayresultfuzzy": "فهرست همه صفحاتی که ویژگی \"$1\" با مقدار \"$2\" دارند.\nاز آنجایی که آنها فقط نتایج کمی دارند، همچنین تقریباً مقادیر نمایش داده می‌شوند.", "smw_sbv_property": "ویژگی:", "smw_sbv_value": "مقدار:", "smw_sbv_submit": "پیدا کردن نتایج", "browse": "مرور ویکی", "smw_browselink": "مرور ویژگی‌ها", "smw_browse_article": "نام صفحه‌‌ای را وارد کنید تا مرورکردن را از آن شروع کنید", "smw_browse_go": "برو", "smw_browse_show_incoming": "نمایش خصوصیات دارای ورودی به اینجا", "smw_browse_hide_incoming": "نهفتن خصوصیات دارای ورودی به اینجا", "smw_browse_no_outgoing": "این صفحه هیچ ویژگی ندارد", "smw_browse_no_incoming": "هیچ خصوصیتی به این صفحه پیوند ندارد", "smw-browse-from-backend": "اطلاعات در حال بازیابی از زیرساخت است", "smw-browse-invalid-subject": "اعتبارسنجی موضوع با خطای \"$1\" برگشت خورد.", "smw-browse-api-subject-serialization-invalid": "این موضوع یک قالب سریالی‌کردن نامعتبر دارد.", "smw-browse-js-disabled": "این گمان می‌رود که قابلیت جاوا‌اسکریپت غیر فعال است و یا وجود ندارد. توصیهٔ ما این است که از مرورگری استفاده کنید که آن را پشتیبانی می‌کند. سایر گزینه‌ّا در صفحهٔ تنظیمات پارامتر [https://www.semantic-mediawiki.org/wiki/Help:$smwgBrowseByApi <code>$smwgBrowseByApi</code>] بحث شده‌اند.", "smw-browse-show-group": "نمایش گروه‌ها", "smw-browse-hide-group": "نهفتن گروه‌ها", "smw_inverse_label_default": "$1 از", "smw_inverse_label_property": "برچسب ویژگی برعکس", "pageproperty": "جستجوی ویژگی صفحه", "smw_pp_docu": "جستجو برای همه مقادیر خاصیت در صفحه داده شده.\nصفحه و خاصیت، هر دو را وارد کنید.", "smw_pp_from": "از صفحه:", "smw_pp_type": "ویژگی:", "smw_pp_submit": "ویژگی", "smw-prev": "{{PLURAL:$1|$1}}تای قبلی", "smw-next": "{{PLURAL:$1|$1}}تای بعدی", "smw_result_prev": "قب<PERSON>ی", "smw_result_next": "بعدی", "smw_result_results": "نتایج", "smw_result_noresults": "بدون نتیجه", "smwadmin": "پیش‌خوان مدیاویکی معنایی", "smw-admin-statistics-job-title": "آ<PERSON><PERSON>ر کار", "smw-admin-statistics-querycache-title": "میان‌گیر پرسمان", "smw-admin-statistics-querycache-disabled": " [https://www.semantic-mediawiki.org/wiki/QueryCache میان‌گیر پرسمان] در این ویکی فعال نشده است، بنابراین هیچ آماری موجود نیست.", "smw-admin-statistics-semanticdata-overview": "مرور کلی", "smw-admin-setupsuccess": "موتور ذخیره‌سازی راه‌اندازی شد.", "smw_smwadmin_return": "بازگشت به $1", "smw_smwadmin_updatestarted": "فرایند روزآمدسازی جدید برای تازه‌سازی داده‌های معنایی آغاز شده بود.\nهمهٔ داده‌های ذخیره‌شده دوباره بازسازی یا در صورت نیاز، تعمیر خواهند‌ شد.\nشما می‌توانید پیشرفت روزآمدسازی را در این صفحهٔ ویژه دنبال کنید.\nبازگشت به $1.", "smw_smwadmin_updatenotstarted": "یک فرایند روزآمدسازی از قبل در‌ حال اجرا است.\nیکی دیگر ایجاد نمی‌شود.", "smw_smwadmin_updatestopped": "همهٔ فرایندهای روزآمدسازی موجود متوقف شده‌اند.", "smw_smwadmin_updatenotstopped": "برای جلوگیری از روند به‌روزرسانی در حال اجرا، شما باید جعبه بررسی را برای نشان دادن اینکه شما واقعاً مطمئن هستید، فعال کنید.", "smw-admin-docu": "این صفحه مخصوص به شما  در طول نصب و ارتقاء <a href=\"https://www.semantic-mediawiki.org\"> مدیاویکی معنایی </a> کمک می‌کند.\nبرای پشتیبان گیری داده‌های ارزشمند قبل از اجرای عملیات اداری، به یاد دیشته باشید.", "smw-admin-environment": "محیط نرم‌افزار", "smw-admin-db": "نصب پایگاه داده", "smw-admin-dbdocu": "مدیاویکی معنایی به برخی از فرمت‌ها به پایگاه اطلاعاتی مدیاویکی برای ذخیره اطلاعات معنایی، نیاز دارد.\nعملکرد زیر تضمین می‌کند که پایگاه اطلاعاتی شما به درستی تنظیم شده است.\nتغییرات ایجاد شده در این مرحله بر روی بقیه پایگاه اطلاعاتب مدیاویکی تاثیر نمی‌گذارد، و اگر مایل باشید می‌تواند آن را به راحتی انجام ندهد.\nاین تنظیم عملکرد می‌تواند چندین بار بدون انجام هر گونه آسیبی اجرا شود، اما تنها یک بار در نصب یا ارتقاء مورد نیاز است.", "smw-admin-permissionswarn": "اگر عملیات با خطاهای اس‌کیو‌ال با شکست مواجه شود، کاربر پایگاه اطلاعاتی استخدام شده توسط ویکی شما ا (LocalSettings.php خود را بررسی کنید) ممکن است مجوزهای کافی را نداشته باشد.\nیا به این کاربر مجوز اضافی برای ایجاد و حذف جداول اعطاء می‌شود، موقتاً ورود اساس پایگاه اطلاعاتی خود را در LocalSettings.php وارد کنید، یا از متن نگهداری <code>setupStore.php</code> استفاده کنید که می‌تواند از اعتبارنامه‌های یک سرپرست استفاده کند.", "smw-admin-dbbutton": "راه‌اندازی یا به‌روزرسانی جدول‌ها", "smw-admin-announce": "وی<PERSON>ی خود را اعلام کنید", "smw-admin-deprecation-notice-title": "اعلان‌های تخریب", "smw-admin-deprecation-notice-title-notice": "تنظیمات منسوخ‌شده", "smw-admin-deprecation-notice-title-replacement": "تنظیمات جایگزین‌شده یا تغییر نام یافته", "smw-admin-deprecation-notice-title-removal": "تنظیمات حذف‌شده", "smw-smwadmin-refresh-title": "تصحیح و روزآمدسازی اطلاعات", "smw_smwadmin_datarefresh": "بازسازی اطلاعات", "smw_smwadmin_datarefreshdocu": "همهٔ داده‌های مدیاویکی معنایی مبتنی بر محتویات فعلی ویکی ممکن است بازیابی شوند.\nاین می‌تواند برای تعمیر داده‌های شکسته یا برای تازه‌سازی داده‌ها مفید باشد اگر فرمت داخلی به علت ارتقای نرم‌افزار، تغییر کرده باشد.\nروزآمدسازی صفحه توسط صفحه اجرا می‌شود و بلافاصله تکمیل نخواهد‌ شد.\nدر زیر، نشان داده می‌شود که آیا روزآمدسازی در حال انجام است و به شما برای شروع کردن با توقف روزآمدسازی اجازه می‌دهد (مگر این‌که این ویژگی توسط مدیر وبگاه غیرفعال شده باشد).", "smw_smwadmin_datarefreshprogress": "<strong>روزآمدسازی در حال حاضر در حال انجام است.</strong>\nطبیعی است که روزآمدسازی فقط به‌آهستگی پیش برود؛ زیرا هر بار که کاربر به ویکی دسترسی می‌یابد، اطلاعات را فقط در تکه‌های کوچک، تازه‌سازی می‌کند.\nبرای سریع‌تر به پایان رساندن این روزآمدسازی، شما می‌‌توانید متن نگهداری مدیاویکی <code>runJobs.php</code> (از گزینه <code>--maxjobs 1000</code> را برای محدود کردن تعداد روزآمدسازی‌های انجام‌شده در هر دسته بخواهید).\nبرآورد پیشرفت فعلی روزآمدسازی:", "smw_smwadmin_datarefreshbutton": "زمان‌بندی بازسازی داده‌ها", "smw_smwadmin_datarefreshstop": "این روزآمدسازی را متوقف کنید", "smw_smwadmin_datarefreshstopconfirm": "بله، من {{GENDER:$1|مطمئن هستم}}.", "smw-admin-propertystatistics-title": "آمارهای ویژگی بازسازی شد", "smw-admin-support": "پشتیبانی شدن", "smw-admin-supportdocu": "منابع گوناگونی که ممکن است به شما در رابطه با مشکلات کمک کند:", "smw-admin-installfile": "اگر شما تجربه مشکلات نصب را داشتید، با بررسی دستورالعمل‌ها در <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">INSTALL file</a> شروع کنید.", "smw-admin-smwhomepage": "مستندات کامل کاربر برای مدیاویکی معنایی در <b><a href=\"https://www.semantic-mediawiki.org\">semantic-mediawiki.org</a></b> است.", "smw-admin-bugsreport": "اشکالات می‌توانند به <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a> گزارش شوند.", "smw-admin-questions": "اگر شما سوالات یا پیشنهادات بیشتری دارید، به گفتگو در<a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">تالار گفتمان کاربران مدیاویکی معنایی</a> بپیوندید.", "smw-admin-other-functions": "تابع‌های دیگر", "smw-admin-statistics-extra": "تابع‌های آماری", "smw-admin-statistics": "آمار", "smw-admin-supplementary-section-title": "کارکردهای تکمیلی", "smw-admin-supplementary-settings-title": "پیکربندی و تنظیمات", "smw-admin-main-title": "مدیاویکی معنایی » $1", "smw-admin-supplementary-operational-statistics-title": "آمارهای عملیاتی", "smw-admin-supplementary-operational-statistics-short-title": "آمارهای عملیاتی", "smw-admin-supplementary-elastic-version-info": "نسخه", "smw-admin-supplementary-elastic-mappings-summary": "خلاصه", "smw-admin-supplementary-elastic-replication-files": "پرونده‌ها", "smw-admin-supplementary-elastic-replication-pages": "صفحه‌ها", "smw-admin-supplementary-elastic-config": "پیکربندی‌ها", "smw-property-label-similarity-threshold": "آستانه:", "smw-property-label-similarity-noresult": "با گزینه‌های انتخاب شده نتیجه‌ای یافت نشد", "smw_adminlinks_datastructure": "ساختار داده‌ها", "smw_adminlinks_displayingdata": "نمایش داده‌ها", "smw_adminlinks_inlinequerieshelp": "کمک سوالات در خط", "smw-createproperty-isproperty": "این یک ویژگی از نوع $1 است.", "smw-createproperty-allowedvals": " {{PLURAL:$1|مقدار مجاز برای این ویژگی|مقادیر مجاز برای این ویژگی}}:", "smw-paramdesc-category-delim": "جداکننده", "smw-paramdesc-category-template": "یک الگو برای فرمت موارد با", "smw-paramdesc-category-userparam": "یک پارامتر برای گذر از جدول", "smw-info-par-message": "پیام برای نمایش.", "smw-info-par-icon": "آیکون برای نشان داد، یا \"اطلاعات\" و یا \"هشدار\".", "prefs-smw": "مدیاویکی معنایی", "prefs-general-options": "اختیارات عمومی", "prefs-ask-options": "جستجوی معنایی", "smw-prefs-intro-text": "گزینه‌های زیر توسط [https://www.semantic-mediawiki.org/ Semantic MediaWiki] (یا فرمت‌های مرتبط) برای فعال کردن سفارشی‌سازی فردی در عملیات انتخاب شده، ارائه شدند. برای کسب اطلاعات بیشتر، لطفاً این[https://www.semantic-mediawiki.org/wiki/Help:User_preferences help section] را مشاهده کنید.", "smw-prefs-ask-options-tooltip-display": "متن پارامتر به صورت یک برچسب اطلاعات نمایش داده شود", "smw-ui-tooltip-title-property": "ویژگی", "smw-ui-tooltip-title-quantity": "تبدیل واحد", "smw-ui-tooltip-title-info": "اطلاعات", "smw-ui-tooltip-title-service": "پیوندها<PERSON> خدمت", "smw-ui-tooltip-title-warning": "هشدار", "smw-ui-tooltip-title-error": "خطا", "smw-ui-tooltip-title-parameter": "پارامتر", "smw-ui-tooltip-title-event": "رویداد", "smw-ui-tooltip-title-note": "یادداشت", "smw-ui-tooltip-title-legend": "افسانه", "smw-ui-tooltip-title-reference": "منبع", "smw_unknowntype": "نوع «$1» برای اين ويژگي نامعتبر است", "smw-concept-cache-text": "این مفهوم، جمعاً $1 صفحه دارد، و زمان آخرین روزآمدسازی، $3، $2 بوده است.", "smw_concept_header": "صفحه‌های مفهوم \"$1\"", "smw_conceptarticlecount": "نمایش زیر $1 {{PLURAL:$1|صفحه|صفحه‌ها}}.", "right-smw-admin": "دسترسی به وظایف مدیریت (مدیاویکی معنایی)", "group-smwadministrator": "مدیر<PERSON> (مدیاویکی معنایی)", "group-smwadministrator-member": "{{GENDER:$1|مدیر (مدیاویکی معنایی)}}", "grouppage-smwadministrator": "{{ns:project}}:مد<PERSON><PERSON><PERSON> (مدیاویکی معنایی)", "action-smw-admin": "دسترسی به وظایف ادارهٔ معنایی مدیاویکی", "smw-property-predefined-default": "\"$1\" یک ویژگی پیش‌تعریف‌شده از نوع $2 است.", "smw-property-predefined-ask": "$1 یک خاصیت از پیش تعریف شده است (همچنین به عنوان [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property] شناخته شده است) که به فراداده [https://www.semantic-mediawiki.org/wiki/Subobject subobject] یک سوال پیوند می‌شود.این خاصیت ساخته شده‌ای است که همراه با امتیازات اجرایی اضافی است اما فقط شبیه هر [https://www.semantic-mediawiki.org/wiki/Property user-defined property] دیگری می‌توانند استفاده شوند.", "smw-property-predefined-asksi": "$1 یک خاصیت از پیش تعریف شده است (همچنین به عنوان [https://www.semantic-mediawiki.org/wiki/Help:Special_properties special property] شناخته شده است) که تعداد شرایط مورد استفاده در یک سوال را جمع می‌کند.این خاصیت ساخته شده‌ای است که همراه با امتیازات اجرایی اضافی است اما فقط شبیه هر [https://www.semantic-mediawiki.org/wiki/Property user-defined property] دیگری می‌توانند استفاده شوند.", "smw-sp-properties-docu": "این صفحه نشان می‌دهد [https://www.semantic-mediawiki.org/wiki/خاصیت خواص] که در دسترس هستند و هنگامی که فیلتر شده، تنها خواص تعریف شده کاربر است که منطبق با شرایط، نمایش داده می‌شود. برای یک نمای متفاوت، صفحه ویژه [[Special:UnusedProperties|unused properties]] یا [[Special:WantedProperties|wanted properties]] را مشاهده کنید.", "smw-sp-properties-cache-info": "داده‌های فهرست شده از [https://www.semantic-mediawiki.org/wiki/Caching حافظۀ نهان] بازیابی شده‌اند، و آخرین به روز شده بودند $1.", "smw-sp-properties-header-label": "فهرست ویژگی‌ها", "smw-admin-settings-docu": "نمایش فهرست همه default و localized settings که به محیط مدیاویکی معنایی مربوط هستند. برای اطلاعات در  تنظیمات فردی، لطفاً به صفحه راهنما [https://www.semantic-mediawiki.org/wiki/Help:Configuration configuration] مراجعه کنید.", "smw-sp-admin-settings-button": "ایجاد فهرست تنظیمات", "smw-admin-idlookup-title": "جستجو", "smw-admin-idlookup-input": "جستجو:", "smw-admin-objectid": "شناسه:", "smw-admin-tab-general": "مرور", "smw-admin-tab-notices": "اعلان‌های تخریب", "smw-admin-tab-maintenance": "نگهداری", "smw-admin-maintenancealerts-lastoptimizationrun-alert-title": "بهینه‌<PERSON><PERSON><PERSON>ی جدول", "smw-admin-deprecation-notice-section": "مدیاویکی معنایی", "smw-admin-configutation-tab-settings": "تنظیمات", "smw-admin-configutation-tab-namespaces": "فضاهای نام", "smw-admin-maintenance-no-description": "بدون توضیحات.", "smw-livepreview-loading": "در حال بارگیری…", "smw-sp-searchbyproperty-resultlist-header": "فهرست نتایج", "smw-search-profile-sort-title": "عنوان", "smw-search-profile-extended-section-query": "پرسمان", "log-name-smw": "سباههٔ مدیاویکی معنایی", "log-show-hide-smw": "$1 سیاههٔ مدیاویکی معنایی", "smw-datavalue-import-invalid-format": "انتظار می‌رفت رشته \"$1\" به چهار قسمت تقسیم شود ولی ساختارش درک نشد.", "smw-types-list": "فهرست انواع داده", "smw-types-default": "\"$1\" یک نوع دادهٔ درونی است", "smw-type-tab-properties": "ویژگی‌ها", "smw-type-tab-errors": "خطاها", "smw-limitreport-intext-parsertime-value": "$1 {{PLURAL:$1|ثانیه|ثانیه}}", "smw-limitreport-pagepurge-storeupdatetime-value": "$1 {{PLURAL:$1|ثانیه|ثانیه}}", "smw-datavalue-external-formatter-invalid-uri": "«$1» نشانی اینترنتی نامعتبر است.", "smw-clipboard-copy-link": "کپی پیوند در کلیپ‌بورد", "smw-data-lookup": "در حال دریافت داده‌ها ...", "smw-no-data-available": "هیچ داده‌ای در دسترس نیست.", "smw-edit-protection-enabled": "ویرایش حفاظت شده است (مدیاویکی معنایی)", "smw-query-reference-link-label": "منبع پرسمان", "smw-format-datatable-emptytable": "هیچ داده‌ای در جدول در دسترس نیست", "smw-format-datatable-info": "نمایش _START_ تا _END_ از _TOTAL_ ورودی", "smw-format-datatable-infoempty": "نمایش ۰ تا ۰ از ۰ ورودی", "smw-format-datatable-infofiltered": "(پالایش‌شده از _MAX_ مجموع ورودی‌ها)", "smw-format-datatable-infothousands": "،", "smw-format-datatable-lengthmenu": "نمایش ورودی‌های _MENU_", "smw-format-datatable-loadingrecords": "در حال بارگیری...", "smw-format-datatable-processing": "در حال پردازش...", "smw-format-datatable-search": "جستجو:", "smw-format-datatable-zerorecords": "هیچ پیشینه مشابهی یافت نشد", "smw-format-datatable-first": "نخستین", "smw-format-datatable-last": "واپسین", "smw-format-datatable-next": "بعدی", "smw-format-datatable-previous": "قب<PERSON>ی", "smw-format-datatable-toolbar-export": "برون‌بری", "smw-category-invalid-redirect-target": "ردهٔ $1 یک تغییر مسیر نامعتبر به فضای نام غیررده‌ای دارد", "apihelp-smwinfo-summary": "ماژول APIها برای بازیابی اطلاعات در مورد آمار مدیاویکی معنایی و سایر فراداده‌ها", "smw-property-reserved-category": "رده", "smw-category": "رده", "smw-browse-property-group-title": "گروه ویژگی", "smw-browse-property-group-label": "برچسب گروه ویژگی", "smw-browse-property-group-description": "توضیحات گروه ویژگی", "smw-filter": "پالایش", "smw-help": "راهنما", "smw-processing": "در حال پردازش ...", "smw-loading": "در حال بارگیری ...", "smw-fetching": "درحال واکشی ...", "smw-expand": "گسترش", "smw-copy": "رونوشت", "smw-jsonview-search-label": "جستجو:", "smw-schema-summary-title": "خلاصه", "smw-schema-title": "طرحواره", "smw-schema-usage": "کا<PERSON><PERSON><PERSON>د", "smw-schema-type": "نوع طرحواره", "smw-schema-description": "توضیحات طرحواره", "smw-ask-title-keyword-type": "جستجوی کلیدواژه", "smw-property-tab-usage": "کا<PERSON><PERSON><PERSON>د", "smw-property-tab-specification": "... بیشتر", "smw-concept-tab-list": "فهرست", "smw-concept-tab-errors": "خطاها", "smw-ask-tab-result": "نتیجه", "smw-ask-tab-code": "<PERSON><PERSON>", "smw-pendingtasks-tab-setup": "را<PERSON>‌<PERSON><PERSON><PERSON><PERSON>ی", "smw-report": "گزارش", "smw-entity-examiner-deferred-elastic-replication": "الاستیک‌سرچ", "smw-entity-examiner-associated-revision-mismatch": "نسخه", "smw-indicator-revision-mismatch": "نسخه", "smw-listingcontinuesabbrev": "ادامه", "smw-showingresults": "نمایش حداکثر {{PLURAL:$1|'''۱''' نتیجه|'''$1''' نتیجه}} در پایین، آغاز از شماره '''$2'''."}