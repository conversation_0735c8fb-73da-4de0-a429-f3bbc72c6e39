{"@metadata": {"authors": ["Als-Chlämens", "Al<PERSON>-Holder", "아라"]}, "smw-desc": "Dyy Wiki zuegängliger mache - fir Maschine ''un'' Mäns<PERSON> ([https://www.semantic-mediawiki.org/wiki/Help:User_manual online-Dokumäntation])", "smw_viewasrdf": "RDF-Feed", "smw_finallistconjunct": " un", "smw_isspecprop": "Die Eigeschaft isch e Spezialeigeschaft in däm Wiki.", "smw_concept_description": "Bschryybig vum Konzäpt „$1“", "smw_no_concept_namespace": "Konzäpt chenne nume im ''Konzä<PERSON>:'' Namensruum aagleit wäre.", "smw_multiple_concepts": "In jedwädere Konzäptsyte cha s nume ei Konzäptdefinition din haa.", "smw_concept_cache_miss": "S Konzäpt „$1“ cha im Momänt nit aagwändet wäre, wel d Wiki-Konfiguration offline grächnet muess wäre.\nWänn s Probläm imfall noch eme Rung nit verschwindet, no bitt Dyy Syteverwalter, des Konzäpt megli z mache.", "smw_noinvannot": "Wärt chenne nit mit umgchehrte Eigeschafte gchännzeichnet wäre", "version-semantic": "Seman<PERSON><PERSON>wy<PERSON>", "smw_baduri": "URI mit dr Form „$1“ sin nit zuelässig.", "smw_printername_count": "Zellerergebnis", "smw_printername_csv": "CSV-Export", "smw_printername_dsv": "DSV-Export", "smw_printername_debug": "Debug-Abfrog (fir Experte)", "smw_printername_embedded": "Inhalt vu yyböute Syte", "smw_printername_json": "JSON-Export", "smw_printername_list": "Lischt", "smw_printername_ol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smw_printername_ul": "Ufgliderig", "smw_printername_table": "Tab<PERSON><PERSON>", "smw_printername_broadtable": "Breiti Tabälle", "smw_printername_template": "Vorlag", "smw_printername_rdf": "RDF-Export", "smw-paramdesc-limit": "Di maximal Aazahl vu <PERSON><PERSON><PERSON><PERSON>, wu zruck gee solle wäre", "smw-paramdesc-headers": "Chopfzyyle bzw. d Nämme vu dr Eigeschafte aazeige", "smw-paramdesc-mainlabel": "S Label vu Hauptsytename", "smw-paramdesc-link": "<PERSON><PERSON><PERSON> as Links aazeige", "smw-paramdesc-intro": "Dr <PERSON>, wu vor dr <PERSON><PERSON><PERSON><PERSON><PERSON> soll aazeigt wäre, wänn s het", "smw-paramdesc-outro": "Dr <PERSON>, wu no dr <PERSON><PERSON>gebnis soll aazeigt wäre, wänn s het", "smw-paramdesc-default": "Dr <PERSON>, wu soll aazeigt wäre, wänn s kei Suechergebnis het", "smw-paramdesc-sep": "S Trännzeiche fir Wärt", "smw-paramdesc-template": "Dr <PERSON> vu dr <PERSON><PERSON>, wu Uusdruck dermit solle aazeigt wäre", "smw-paramdesc-columns": "D Aazahl vu dr <PERSON><PERSON><PERSON> go Ergebnis aazeige (Standard isch $1)", "smw-paramdesc-userparam": "<PERSON>, wu bi jedem Vorlageufruef ibergee wird, wänn e Vorlag brucht wird", "smw-paramdesc-introtemplate": "<PERSON>, wu vor dr Abfrogergebnis aazeigt wird, wänn s git", "smw-paramdesc-outrotemplate": "<PERSON>, wu no dr Abfrogergebnis soll aazeigt wäre, wänn s het", "smw-paramdesc-embedformat": "Dr HTML-Be<PERSON><PERSON>hl zum Iberschrifte definiere", "smw-paramdesc-embedonly": "Kei Iberschrifte aazeige", "smw-paramdesc-rdfsyntax": "D RDF-Syntax, wu brucht wäre soll", "smw-paramdesc-csv-sep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wu sott brucht wäre", "smw-paramdesc-dsv-separator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wu sott brucht wäre", "smw-paramdesc-dsv-filename": "Dr Name fir d DSV-<PERSON><PERSON>", "smw-paramdesc-searchlabel": "<PERSON> Teggst für de Link zue de Ergebniss", "smw_iq_disabled": "<PERSON><PERSON><PERSON><PERSON>afroge sin in däm Wiki zur Zyt nit megli.", "smw_iq_moreresults": "… meh <PERSON>", "smw_parseerror": "<PERSON>, wu <PERSON> hesch, isch nit verstande wore.", "smw_notitle": "„$1“ cha nit as Sytename in däm Wiki bruucht wäre.", "smw_wrong_namespace": "Nume Syten im Namensruum „$1“ sin doo zuelässig.", "smw_manytypes": "In dr Eigesch<PERSON> sin e paar Date<PERSON> zuegwise wore.", "smw_emptystring": "Lääri Zeichefolge wäre nit akzeptiert.", "smw_notinenum": "„$1“ ghert nit zue dr meglige Wärt vu däre Eigeschaft ($2).", "smw_noboolean": "„$1“ isch kei Wohretswärt (wohr/falsch).", "smw_true_words": "wohr,w,jo,j", "smw_false_words": "falsch,f,nei,n", "smw_nofloat": "„$1“ isch kei <PERSON>ahl.", "smw_infinite": "<PERSON> „$1“ isch z lang.", "smw_novalues": "Kei Wärt spezifiziert.", "smw_nodatetime": "S Datum „$1“ isch nit verstande wore.", "smw_toomanyclosing": "In dr Aafrog chemme z vyyl „$1“ vor.", "smw_noclosingbrackets": "In dr <PERSON><PERSON><PERSON> chunnt e „<nowiki>[[</nowiki>“ vor, isch aber nit mit eme „]]“ abgschlosse.", "smw_misplacedsymbol": "S Symbol „$1“ isch an ere <PERSON>ell bruucht wore, wu s nit sinnvoll isch.", "smw_unexpectedpart": "<PERSON> <PERSON><PERSON> „$1“ vu dr <PERSON><PERSON><PERSON> isch nit verstande wore. D Ergebnis sin villicht nit wie s erwartet woren isch.", "smw_emptysubquery": "In ere <PERSON><PERSON><PERSON><PERSON> het s kei <PERSON>.", "smw_misplacedsubquery": "<PERSON> isch an ere <PERSON> bruucht wore, wu keini <PERSON>e derfe vorchu.", "smw_valuesubquery": "Teilaafroge wäre fir Wärt vudr Eigeschaft „$1“ nit unterstitzt.", "smw_badqueryatom": "<PERSON> <PERSON><PERSON> „<nowiki>[[…]]</nowiki>“ vu dr <PERSON><PERSON><PERSON> isch nit verstande wore.", "smw_propvalueproblem": "Dr <PERSON> vu dr Eigeschaft „$1“ isch nit verstande wore.", "smw_noqueryfeature": "<PERSON>-Feature sin im Momänt mit däm Wiki nit megli un dää Teil vu dr <PERSON> isch glescht wore ($1).", "smw_noconjunctions": "UND-Verchnipfige in dr Aafroge wäre in däm Wiki nit unterstitzt un dää Teil vu dr <PERSON>af<PERSON> isch glescht wore ($1).", "smw_nodisjunctions": "ODER-Verchnipfige wäre in däm Wiki nit unterstitzt un e Teil vu dr <PERSON><PERSON><PERSON> isch glescht wore ($1).", "smw_querytoolarge": "Die Aafrogbedingige chenne für Greßi un Tiefi vu Aafroge nit berucksichtigt wäre wäge dr Beschränkige, wu in däm Wiki giltig sin: $1.", "smw_notemplategiven": "Ass die Aafrog cha bearbeitet wäre, muess im Parameter „template“ e Wärt yytrage syy.", "smw_type_header": "Eigeschafte mit em Datetyp „$1“", "smw_typearticlecount": "S {{PLURAL:$1|wird ei Eigeschaft|wäre $1 Eigeschafte}} mit däm Datetyp aazeigt:", "smw_attribute_header": "Syte mit dr Eigeschaft „$1“", "smw_attributearticlecount": "S {{PLURAL:$1|wird ei Syten|wäre $1 Syten}} aazei<PERSON>, wu die Eigeschaft {{PLURAL:$1|bruucht|bruuche}}:", "exportrdf": "Syten as RDF exportiere", "smw_exportrdf_docu": "Doo chenne Informationen iber einzelni Syten im RDF-Format abgruefe wäre. Bitte gib d Name vu dr Syte  <i>zyylewyys</i> yy.", "smw_exportrdf_recursive": "Exportier au alli relevante Syte rekursiv. Dänk dra, ass d Ergebnis chenne arg groß syy!", "smw_exportrdf_backlinks": "Exportier au alli Syte, wu uf exportierti Syte verwyye. Leit e RDF aa, wu lyychter cha dursuecht wäre.", "smw_exportrdf_lastdate": "Exportier k<PERSON><PERSON>, wu ni<PERSON> g<PERSON> wore sin syt em Zytpunk<PERSON>, wu aagee isch.", "smw_exportrdf_submit": "Exportiere", "uriresolver": "URI-Ufleser", "properties": "Eigeschafte", "smw-categories": "<PERSON><PERSON><PERSON>", "smw_properties_docu": "In däm Wiki git s die Eigeschafte:", "smw_property_template": "$1 mit Datetyp $2 ($3)", "smw_propertylackspage": "Alli Eigeschafte sotte uf ere Syte bschribe syy!", "smw_propertylackstype": "Fir die Eigeschaft isch kei Datetyp aagee wore ($1 wird vorerscht as <PERSON><PERSON> aagnuh).", "smw_propertyhardlyused": "Die Eigeschaft wird im Wiki chuum bruucht!", "unusedproperties": "Verwaisti Eigeschafte", "smw-unusedproperties-docu": "Die Eigeschafte git s, trotz ass si nit bruucht wäre.", "smw-unusedproperty-template": "$1 mit Datetyp $2", "wantedproperties": "Gwinschti Eigeschafte", "smw-wantedproperties-docu": "Die Attribut wäre no uf keinere Syte bschribe, trotz ass si scho in däm Wiki bruucht wäre.", "smw-wantedproperty-template": "$1 ($2 {{PLURAL:$2|Vor<PERSON>|Vorchu}})", "smw_purge": "nej lade", "types": "Datetype", "smw_types_docu": "Die Datetype chenne Eigeschafte zuegwise wäre. E jede Datetyp het e eigeni Syte, wu gnaueri Informatione druf chenne yytrage wäre.", "smw_uri_doc": "Dr URI-U<PERSON><PERSON> setzt d Empfählige »[$1 W3C TAG finding on httpRange-14]« um.\nÄr sorgt defir, ass Mänsche nit zue Netzsyte wäre.", "ask": "<PERSON><PERSON><PERSON><PERSON>", "smw_ask_sortby": "Sortiere no Spalte (optional)", "smw_ask_ascorder": "Obsgi", "smw_ask_descorder": "<PERSON><PERSON><PERSON>", "smw_ask_submit": "<PERSON><PERSON><PERSON><PERSON> finde", "smw_ask_editquery": "Aafrog bearbeite", "smw_add_sortcondition": "[Sortieraaw<PERSON><PERSON>ig zuefiege]", "smw_ask_hidequery": "Aafrog uusblände", "smw_ask_help": "<PERSON><PERSON>", "smw_ask_queryhead": "Aafrog", "smw_ask_printhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wu solle aazeigt wäre", "smw_ask_printdesc": "(ei Eigeschaftsname je Zyyle yyfiege)", "smw_ask_format_as": "Formatiert as:", "smw_ask_defaultformat": "Standard", "smw_ask_otheroptions": "<PERSON><PERSON>", "smw_ask_show_embed": "Yyböute Code zeige", "smw_ask_hide_embed": "Yyböute Code verstecke", "smw_ask_embed_instr": "Verwänd dää Code unte go die Abstimmig in e Wikisyte yyböue.", "searchbyproperty": "Iber e Eigeschaft sueche", "smw_sbv_docu": "Diese Spezialsyte findet alli Syte, wu e bstimmte Wärt hän fir d Eigeschaft, wu aagee woren isch.", "smw_sbv_novalue": "Bitte dr gwin<PERSON><PERSON> Wärt yygee oder alli Wärte fir d Eigeschaft $1 aaluege.", "smw_sbv_displayresultfuzzy": "E Lischt vu allene Syte, wu d Eigeschaft „$1“ mit em Wärt „$2“ hän.\nWel nume wenig Ergebnis gfunde wore sin, wäre au ähnligi Wärt ufglischtet.", "smw_sbv_property": "Eigeschaft:", "smw_sbv_value": "Wärt:", "smw_sbv_submit": "<PERSON><PERSON><PERSON><PERSON> finde", "browse": "Wiki browse", "smw_browselink": "Eigeschaften aazeige", "smw_browse_article": "<PERSON>te gib dr <PERSON><PERSON><PERSON> vun ere Syte yy.", "smw_browse_go": "Gang", "smw_browse_show_incoming": "z<PERSON>g <PERSON>, wu do druff verwyyse", "smw_browse_hide_incoming": "versteck Eigeschafte, wu do druff verwyyse", "smw_browse_no_outgoing": "Die Syte het kei Eigeschafte.", "smw_browse_no_incoming": "Kei Eigeschafte verwyyse uf die Syte.", "smw_inverse_label_default": "$1 vu", "smw_inverse_label_property": "Umgchehrti Eigeschaftbezeichnig", "pageproperty": "Eigeschaftswärt vun ere Syte", "smw_pp_docu": "No allene Wärt sueche, wu e bstimmt Attribut fir d <PERSON><PERSON> hän, wu aagee woren isch. Gib e Syte un au ne Attribut yy.", "smw_pp_from": "<PERSON>u dr <PERSON><PERSON>", "smw_pp_type": "Eigeschaft", "smw_pp_submit": "Ergebnis aazeige", "smw_result_prev": "Zrugg", "smw_result_next": "<PERSON><PERSON><PERSON>", "smw_result_results": "<PERSON><PERSON><PERSON><PERSON>", "smw_result_noresults": "Kei Ergebnis gfunde.", "smwadmin": "Admin-Funktione fir Semantic MediaWiki", "smw-admin-setupsuccess": "D Spychereinheit isch erfolgryych yygrichtet wore.", "smw_smwadmin_return": "Zrugg zue $1", "smw_smwadmin_updatestarted": "E neje Update-Prozäss fir d Aktualisierig vu dr seman<PERSON><PERSON> Date isch gstartet wore.\nAlli gspycherete Date wäre nej aagleit oder repariert, wänn netig.\nDu chasch dr <PERSON>sch<PERSON><PERSON> vum Update uf däre Spezialsyte verfolge.", "smw_smwadmin_updatenotstarted": "S lauft scho ne Update-Prozäss.\nS wird kei neje aagfange.", "smw_smwadmin_updatestopped": "<PERSON>i Aktualisierigsprozäss, wu s git, sin a<PERSON><PERSON><PERSON> wore.", "smw_smwadmin_updatenotstopped": "Go Proz<PERSON> a<PERSON>, wu am laufe sin, muesch Du s Kontrollchäschtli aktiviere zum aazeige, ass <PERSON> Dir wirkli sicher bisch.", "smw-admin-docu": "Die Spezialsyte hilft währed dr Installation un em Upgrade vu <a href=\"http://semantic-mediawiki.org\">Semantic MediaWiki</a>.\n<PERSON><PERSON><PERSON> dra, wichtigi Date vor dr Uus<PERSON><PERSON> vu adminischtrative Funktione z sichere.", "smw-admin-db": "Datebankinstallation un -aktualisierig", "smw-admin-dbdocu": "Semantic MediaWiki bruucht e paar Erwyterige an dr MediaWiki-Datebank go di semantische Date z spychere.\nDie Funktion gwährleischtet, ass d Datebank richtig yygrichtet isch.\nD Änderige, wu in däm Schritt gmacht wäre, stere dr Räscht vu dr MediaWiki-Datebank nit un chenne eifach zrugggsetzt wäre, wänn gwinscht.\nDie Setup-Funktion cha e paar Mol uusgfiert wäre ohni Schade z verursache, s isch aber nume eimol netig bi dr  Installation oder em Update.", "smw-admin-permissionswarn": "Wänn d Aktion mit eme SQL-<PERSON><PERSON><PERSON> abb<PERSON>t, ch<PERSON><PERSON> s s<PERSON>y, ass dr <PERSON><PERSON><PERSON><PERSON><PERSON>, wu s Wiki iber en uf d Datenbank zuegryft (lueg d Datei LocalSettings.php), nit d Rächt dezue het.\nGo des Probläm lese  isch s megli, em <PERSON>utzer zuesätzligi Rächt fir s Aalege und Lesche vu Tabälle z gee, dr  Datebank-Adminischtrator zytwyys in d LocalSettings.php yyztrage oder s Wartigsskript <code>setupStore.php</code> z verwände, wu d Benutzerdate us AdminSettings.php cha verwände.", "smw-admin-dbbutton": "Tabällen initialisiere oder aktualisere", "smw-admin-announce": "Dyy Wiki aachinde", "smw_smwadmin_datarefresh": "Datereparatur un -aktualisierig", "smw_smwadmin_datarefreshdocu": "S isch megli alli Datebanke vu Semantic MediaWiki uf dr Grundlag vum aktuälle Inhalt vum Wiki widerhärzstelle.\nDes cha hälfe, ne kaputti Datebank nej z lade oder Date z aktualisiere, wänn wägen eme Softwareupgrade d Datebankstruktur gänderet wore isch.\nS Update wird fir jedi Syte uusgfiert un wird e Wyyli bruuche.\nDoo wird zeigt, eb e Update lauft un Di wird erlaubt e Update z starte oder z stoppe (usser wänn die Funktion vum Sytebetryyber deaktiviert woren isch).", "smw_smwadmin_datarefreshprogress": "<strong>E Update lauft scho.</strong>\nE Update bruucht nromalerwyys lang, wel d Date nume in chleine Prtione aktualisiert wäre, j<PERSON><PERSON> wänn eber uf s Wiki zuegryft.\nGo des Update schnäller z beände, cha mer s MediaWiki-Wartigsskript <code>runJobs.php</code> bruuche (mit em Parameter <code>--maxjobs 1000</code> cha d Aazahl vu dr Updates, wu uf eimol durgfiert wäre, bschränkt wäre).\nGschätzte Fortschritt vum Update, wu grad lauft:", "smw_smwadmin_datarefreshbutton": "<PERSON><PERSON> dr <PERSON><PERSON>ktual<PERSON><PERSON><PERSON> aaf<PERSON>e", "smw_smwadmin_datarefreshstop": "Mit däre Aktualisierig ufhere", "smw_smwadmin_datarefreshstopconfirm": "<PERSON>, ich bi mer sicher.", "smw-admin-support": "Unterstitzig iberchu", "smw-admin-supportdocu": "Verschideni Quälle hälfe Dir villicht bi me Problämfall:", "smw-admin-installfile": "<PERSON>änn s Probläm mit dr Inschtallation git, chennt d <PERSON>i <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/blob/master/docs/INSTALL.md#smw-installation\">INSTALL</a> villicht wyterhälfe.", "smw-admin-smwhomepage": "Di vollständig Benutzerdokumentation vu Semantic MediaWiki findsch uf <b><a href=\"http://semantic-mediawiki.org\">semantic-mediawiki.org</a></b>.", "smw-admin-bugsreport": "Fähler chennte bi <a href=\"https://github.com/SemanticMediaWiki/SemanticMediaWiki/issues\">GitHub</a> berichtet wäre.", "smw-admin-questions": "<PERSON>änn Du meh Frogen oder Vorschleg hesch, mach mit an dr Diskussion uf <a href=\"http://sourceforge.net/mailarchive/forum.php?forum_name=semediawiki-user\">Semantic MediaWiki user forum</a>.", "smw_adminlinks_datastructure": "Datestruktur", "smw_adminlinks_displayingdata": "Date abbilde", "smw_adminlinks_inlinequerieshelp": "Hilf fir Inline-Abfroge", "smw-createproperty-isproperty": "Des isch e Eigeschaft vum Typ $1.", "smw-createproperty-allowedvals": "{{PLURAL:$1|Dr erlaubt Wärt fir die Eigeschaft isch|Di erlaubte Wärt fir die Eigeschaft sin}}:", "smw_unknowntype": "Imn dr <PERSON><PERSON><PERSON><PERSON> isch dr nit bekan<PERSON> „$1“ zuegwise wore.", "smw_concept_header": "Syte vum Konzäpt „$1“", "smw_conceptarticlecount": "S {{PLURAL:$1|wird ei Syten|wäre $1 Syten}} aazei<PERSON>, wu zue däm Konzäpt {{PLURAL:$1|ghert|ghere}}:", "smw-livepreview-loading": "Am Lade …", "smw-listingcontinuesabbrev": "(Forts.)", "smw-showingresults": "Do {{PLURAL:$1|isch '''1''' Erge<PERSON><PERSON>|sin '''$1''' <PERSON><PERSON><PERSON><PERSON><PERSON>}}, s fangt aa mit dr <PERSON><PERSON><PERSON>r '''$2.'''"}